[11:06:25:249] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[11:06:27:982] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$AutoConfiguredMapperScannerRegistrar.registerBeanDefinitions(MybatisAutoConfiguration.java:252) - Searching for mappers annotated with @Mapper
[11:06:27:999] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$AutoConfiguredMapperScannerRegistrar.registerBeanDefinitions(MybatisAutoConfiguration.java:264) - Using auto-configuration base package 'com.taurus.examinationassistant'
[11:06:29:249] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[11:06:29:295] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[11:06:29:296] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[11:06:29:296] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[11:06:29:296] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[11:06:29:296] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[11:06:29:296] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[11:06:29:296] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[11:06:29:296] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[11:06:29:296] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[11:06:29:296] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[11:06:29:297] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[11:06:29:298] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[11:06:29:393] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[11:06:30:229] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[11:06:30:241] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[11:06:31:070] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[11:06:31:097] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[11:06:31:118] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[11:06:31:372] [DEBUG] - io.micrometer.core.util.internal.logging.InternalLoggerFactory.newDefaultFactory(InternalLoggerFactory.java:59) - Using SLF4J as the default logging framework
[11:06:31:470] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[11:06:31:484] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[11:06:31:486] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[11:06:31:487] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[11:06:31:488] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[11:06:31:490] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[11:06:31:491] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[11:06:31:493] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[11:06:31:494] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[11:06:32:221] [DEBUG] - io.undertow.server.session.InMemorySessionManager.registerSessionListener(InMemorySessionManager.java:268) - Registered session listener io.undertow.servlet.core.SessionListenerBridge@57118088
[11:06:32:299] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[11:06:37:518] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[11:06:37:535] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[11:06:37:819] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[11:06:37:830] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PassRaceStageMapper对应的Mapper
[11:06:38:027] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationStartupMapper对应的Mapper
[11:06:38:096] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteWithdrawMapper对应的Mapper
[11:06:38:143] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.DiscountMapper对应的Mapper
[11:06:38:179] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PointRuleMapper对应的Mapper
[11:06:38:218] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CategoryMapper对应的Mapper
[11:06:38:260] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.OssFileAudioMapper对应的Mapper
[11:06:38:276] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarUserProfileMapper对应的Mapper
[11:06:38:296] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongQuestionBookQuestionMapper对应的Mapper
[11:06:38:311] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookSummaryMapper对应的Mapper
[11:06:38:334] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MessagePushSubscriptionMapper对应的Mapper
[11:06:38:353] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UniversalOrderMapper对应的Mapper
[11:06:38:377] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionMapper对应的Mapper
[11:06:38:395] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserExaminationPerformanceReportMapper对应的Mapper
[11:06:38:420] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionRandomMapper对应的Mapper
[11:06:38:434] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.OssFileMapper对应的Mapper
[11:06:38:448] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookFavoriteMapper对应的Mapper
[11:06:38:459] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookExamineeMapper对应的Mapper
[11:06:38:469] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PointUserMapper对应的Mapper
[11:06:38:478] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WxMiniprogramSubscribeMessageOfEteaMapper对应的Mapper
[11:06:38:485] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CustomerRequirementMapper对应的Mapper
[11:06:38:494] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionRandomRangeMapper对应的Mapper
[11:06:38:506] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.TemplateMapper对应的Mapper
[11:06:38:519] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserViewPerformanceWithoutAdMapper对应的Mapper
[11:06:38:537] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionAttachmentMapper对应的Mapper
[11:06:38:545] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationInstanceProcessMapper对应的Mapper
[11:06:38:558] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyFunctionRoleMapper对应的Mapper
[11:06:38:567] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookPraiseMapper对应的Mapper
[11:06:38:575] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookTagMapper对应的Mapper
[11:06:38:583] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SysRoleMapper对应的Mapper
[11:06:38:593] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AppStartNoticeReadUserMapper对应的Mapper
[11:06:38:601] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.GameMapper对应的Mapper
[11:06:38:609] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.NameListDetailMapper对应的Mapper
[11:06:38:615] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserSummaryMapper对应的Mapper
[11:06:38:621] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QiyewxUserAccountLicenseMapper对应的Mapper
[11:06:38:631] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ViewPerformanceWithoutAdTransactionMapper对应的Mapper
[11:06:38:638] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsitePaperInstanceProcessMapper对应的Mapper
[11:06:38:656] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationExamineeSnapshotMapper对应的Mapper
[11:06:38:664] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MessagePlanMapper对应的Mapper
[11:06:38:679] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductOrderRelationCountMapper对应的Mapper
[11:06:38:707] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperMapper对应的Mapper
[11:06:38:722] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationInstanceEventMapper对应的Mapper
[11:06:38:742] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookPracticeSummaryMapper对应的Mapper
[11:06:38:760] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PermissionAuthorizationMapper对应的Mapper
[11:06:38:779] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarUserMilestoneMapper对应的Mapper
[11:06:38:785] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionBankQuestionMapper对应的Mapper
[11:06:38:793] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentSubjectCategoryMapper对应的Mapper
[11:06:38:813] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookMapper对应的Mapper
[11:06:38:821] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UniversalProductOrderRelationMapper对应的Mapper
[11:06:38:834] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductMapper对应的Mapper
[11:06:38:850] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.OrdersMapper对应的Mapper
[11:06:38:865] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserFormMapper对应的Mapper
[11:06:38:879] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteContentAccessMapper对应的Mapper
[11:06:38:890] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentClassificationInfoMapper对应的Mapper
[11:06:38:902] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QiyewxInterfaceLicenseOrderMapper对应的Mapper
[11:06:38:920] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperFavoriteMapper对应的Mapper
[11:06:38:928] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AnnualReportMapper对应的Mapper
[11:06:38:934] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongQuestionBookMapper对应的Mapper
[11:06:38:951] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PracticePushScheduleMapper对应的Mapper
[11:06:38:975] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentSubjectOfUserMapper对应的Mapper
[11:06:38:980] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionExampleDeletedMapper对应的Mapper
[11:06:38:984] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SysLogMapper对应的Mapper
[11:06:38:988] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductOrderRelationMapper对应的Mapper
[11:06:38:992] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SysUserRoleMapper对应的Mapper
[11:06:38:997] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.GameQuestionRandomRuleMapper对应的Mapper
[11:06:39:002] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionSummaryMapper对应的Mapper
[11:06:39:008] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperPraiseMapper对应的Mapper
[11:06:39:014] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationWxGidMapper对应的Mapper
[11:06:39:020] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserReportRelationMapper对应的Mapper
[11:06:39:063] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QrcodeScanEntryMapper对应的Mapper
[11:06:39:085] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SystemMessageMapper对应的Mapper
[11:06:39:090] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationInstanceMapper对应的Mapper
[11:06:39:104] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserDeviceBindingMapper对应的Mapper
[11:06:39:114] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteUserSignatureMapper对应的Mapper
[11:06:39:119] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyRegisterFormFlowRecordMapper对应的Mapper
[11:06:39:127] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductIntroductionMediaMapper对应的Mapper
[11:06:39:140] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserCompanyMapper对应的Mapper
[11:06:39:151] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationNameListMapper对应的Mapper
[11:06:39:171] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationTagMapper对应的Mapper
[11:06:39:183] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationGroupExaminationMapper对应的Mapper
[11:06:39:188] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SmsMessageMapper对应的Mapper
[11:06:39:198] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyGroupUserMapper对应的Mapper
[11:06:39:205] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionFavoriteMapper对应的Mapper
[11:06:39:210] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.FileAccessPermissionMapper对应的Mapper
[11:06:39:214] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteFavoriteMapper对应的Mapper
[11:06:39:220] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserOfYncxMapper对应的Mapper
[11:06:39:228] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PointConsumerationMapper对应的Mapper
[11:06:39:237] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.GameResultMapper对应的Mapper
[11:06:39:249] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MultipleMarkingExaminationInstanceMapper对应的Mapper
[11:06:39:257] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentSubjectMapper对应的Mapper
[11:06:39:265] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyUserGroupMapper对应的Mapper
[11:06:39:274] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongQuestionBookReviewSummaryMapper对应的Mapper
[11:06:39:280] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.TaxIdentityInfoMapper对应的Mapper
[11:06:39:285] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyDepartmentMapper对应的Mapper
[11:06:39:295] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteOrderRelationMapper对应的Mapper
[11:06:39:308] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.NameListMapper对应的Mapper
[11:06:39:315] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperTagMapper对应的Mapper
[11:06:39:327] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserFormFlowRecordMapper对应的Mapper
[11:06:39:347] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarMapper对应的Mapper
[11:06:39:362] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AppGroupUserMapper对应的Mapper
[11:06:39:374] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MessageScheduleMapper对应的Mapper
[11:06:39:386] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.TaxSheetApplicationMapper对应的Mapper
[11:06:39:391] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookPracticeProcessMapper对应的Mapper
[11:06:39:399] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PassRaceResultMapper对应的Mapper
[11:06:39:405] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyJobGradeMapper对应的Mapper
[11:06:39:411] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PassRaceGameMapper对应的Mapper
[11:06:39:414] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PointDetailMapper对应的Mapper
[11:06:39:416] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserExaminationInstanceReportMapper对应的Mapper
[11:06:39:418] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarStageEventMapper对应的Mapper
[11:06:39:424] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AppMapper对应的Mapper
[11:06:39:430] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationExamineeMapper对应的Mapper
[11:06:39:435] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.VersionMapper对应的Mapper
[11:06:39:443] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WeixinAccountMapper对应的Mapper
[11:06:39:452] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QiyewxOrderMapper对应的Mapper
[11:06:39:457] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.InviteRelationMapper对应的Mapper
[11:06:39:469] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MessageTemplateMapper对应的Mapper
[11:06:39:474] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.NanxiangRegisterMapper对应的Mapper
[11:06:39:484] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarUserMapper对应的Mapper
[11:06:39:491] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentSubjectAccessInfoMapper对应的Mapper
[11:06:39:494] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongQuestionBookReviewProcessMapper对应的Mapper
[11:06:39:497] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductIntroductionMapper对应的Mapper
[11:06:39:499] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperQuestionMapper对应的Mapper
[11:06:39:501] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AppStartNoticeMapper对应的Mapper
[11:06:39:503] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookGradeMapper对应的Mapper
[11:06:39:505] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PermissionMapper对应的Mapper
[11:06:39:507] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.FreePracticeOfUserMapper对应的Mapper
[11:06:39:512] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionCompositionChildMapper对应的Mapper
[11:06:39:516] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyMapper对应的Mapper
[11:06:39:520] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongBookMapper对应的Mapper
[11:06:39:526] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MultipleMarkingExaminationMapper对应的Mapper
[11:06:39:534] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationFavoriteMapper对应的Mapper
[11:06:39:537] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookQuestionMapper对应的Mapper
[11:06:39:547] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyRegisterFormMapper对应的Mapper
[11:06:39:550] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentUnlockMapper对应的Mapper
[11:06:39:552] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentProductOfUserMapper对应的Mapper
[11:06:39:555] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ManualServiceRecordMapper对应的Mapper
[11:06:39:558] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookUserRecordMapper对应的Mapper
[11:06:39:562] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteOrderMapper对应的Mapper
[11:06:39:567] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsitePaperInstanceMapper对应的Mapper
[11:06:39:574] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationMapper对应的Mapper
[11:06:39:582] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductCountTransactionMapper对应的Mapper
[11:06:39:585] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserOfProductMapper对应的Mapper
[11:06:39:588] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.HomepageUserFollowedMapper对应的Mapper
[11:06:39:595] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserMapper对应的Mapper
[11:06:39:600] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：1.769秒
[11:06:46:164] [DEBUG] - io.netty.util.internal.logging.InternalLoggerFactory.useSlf4JLoggerFactory(InternalLoggerFactory.java:63) - Using SLF4J as the default logging framework
[11:06:46:171] [DEBUG] - io.netty.util.concurrent.GlobalEventExecutor.<clinit>(GlobalEventExecutor.java:53) - -Dio.netty.globalEventExecutor.quietPeriodSeconds: 1
[11:06:46:183] [DEBUG] - io.netty.util.internal.InternalThreadLocalMap.<clinit>(InternalThreadLocalMap.java:100) - -Dio.netty.threadLocalMap.stringBuilder.initialSize: 1024
[11:06:46:183] [DEBUG] - io.netty.util.internal.InternalThreadLocalMap.<clinit>(InternalThreadLocalMap.java:101) - -Dio.netty.threadLocalMap.stringBuilder.maxSize: 4096
[11:06:46:215] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[11:06:46:220] [DEBUG] - io.netty.channel.MultithreadEventLoopGroup.<clinit>(MultithreadEventLoopGroup.java:44) - -Dio.netty.eventLoopThreads: 16
[11:06:46:257] [DEBUG] - io.netty.util.internal.PlatformDependent0.explicitNoUnsafeCause0(PlatformDependent0.java:515) - -Dio.netty.noUnsafe: false
[11:06:46:259] [DEBUG] - io.netty.util.internal.PlatformDependent0.javaVersion0(PlatformDependent0.java:1026) - Java version: 8
[11:06:46:261] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:140) - sun.misc.Unsafe.theUnsafe: available
[11:06:46:264] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:164) - sun.misc.Unsafe.copyMemory: available
[11:06:46:265] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:196) - sun.misc.Unsafe.storeFence: available
[11:06:46:266] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:239) - java.nio.Buffer.address: available
[11:06:46:267] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:312) - direct buffer constructor: available
[11:06:46:268] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:403) - java.nio.Bits.unaligned: available, true
[11:06:46:268] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:478) - jdk.internal.misc.Unsafe.allocateUninitializedArray(int): unavailable prior to Java9
[11:06:46:268] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:501) - java.nio.DirectByteBuffer.<init>(long, {int,long}): available
[11:06:46:268] [DEBUG] - io.netty.util.internal.PlatformDependent.unsafeUnavailabilityCause0(PlatformDependent.java:1157) - sun.misc.Unsafe: available
[11:06:46:269] [DEBUG] - io.netty.util.internal.PlatformDependent.tmpdir0(PlatformDependent.java:1303) - -Dio.netty.tmpdir: /var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T (java.io.tmpdir)
[11:06:46:270] [DEBUG] - io.netty.util.internal.PlatformDependent.bitMode0(PlatformDependent.java:1382) - -Dio.netty.bitMode: 64 (sun.arch.data.model)
[11:06:46:275] [DEBUG] - io.netty.util.internal.PlatformDependent.isOsx0(PlatformDependent.java:1125) - Platform: MacOS
[11:06:46:276] [DEBUG] - io.netty.util.internal.PlatformDependent.<clinit>(PlatformDependent.java:176) - -Dio.netty.maxDirectMemory: 3817865216 bytes
[11:06:46:276] [DEBUG] - io.netty.util.internal.PlatformDependent.<clinit>(PlatformDependent.java:183) - -Dio.netty.uninitializedArrayAllocationThreshold: -1
[11:06:46:278] [DEBUG] - io.netty.util.internal.CleanerJava6.<clinit>(CleanerJava6.java:92) - java.nio.ByteBuffer.cleaner(): available
[11:06:46:278] [DEBUG] - io.netty.util.internal.PlatformDependent.<clinit>(PlatformDependent.java:203) - -Dio.netty.noPreferDirect: false
[11:06:46:279] [DEBUG] - io.netty.channel.nio.NioEventLoop.<clinit>(NioEventLoop.java:110) - -Dio.netty.noKeySetOptimization: false
[11:06:46:280] [DEBUG] - io.netty.channel.nio.NioEventLoop.<clinit>(NioEventLoop.java:111) - -Dio.netty.selectorAutoRebuildThreshold: 512
[11:06:46:289] [DEBUG] - io.netty.util.internal.PlatformDependent$Mpsc.<clinit>(PlatformDependent.java:1008) - org.jctools-core.MpscChunkedArrayQueue: available
[11:06:46:306] [WARN] - io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:70) - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
[11:06:46:314] [DEBUG] - io.netty.util.NetUtil.<clinit>(NetUtil.java:148) - -Djava.net.preferIPv4Stack: false
[11:06:46:314] [DEBUG] - io.netty.util.NetUtil.<clinit>(NetUtil.java:149) - -Djava.net.preferIPv6Addresses: false
[11:06:46:317] [DEBUG] - io.netty.util.NetUtilInitializations.determineLoopback(NetUtilInitializations.java:145) - Loopback interface: lo0 (lo0, 0:0:0:0:0:0:0:1%lo0)
[11:06:46:318] [DEBUG] - io.netty.util.NetUtil$SoMaxConnAction.run(NetUtil.java:206) - Failed to get SOMAXCONN from sysctl and file /proc/sys/net/core/somaxconn. Default: 128
[11:06:46:336] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:141) - Default ResolvedAddressTypes: IPV4_PREFERRED
[11:06:46:337] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:142) - Localhost address: localhost/127.0.0.1
[11:06:46:337] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:151) - Windows hostname: null
[11:06:46:339] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:164) - Default search domains: []
[11:06:46:340] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:173) - Default UnixResolverOptions{ndots=1, timeout=5, attempts=16}
[11:06:46:349] [DEBUG] - io.netty.resolver.DefaultHostsFileEntriesResolver.<clinit>(DefaultHostsFileEntriesResolver.java:53) - -Dio.netty.hostsFileRefreshInterval: 0
[11:06:46:361] [DEBUG] - io.netty.util.ResourceLeakDetector.<clinit>(ResourceLeakDetector.java:129) - -Dio.netty.leakDetection.level: simple
[11:06:46:361] [DEBUG] - io.netty.util.ResourceLeakDetector.<clinit>(ResourceLeakDetector.java:130) - -Dio.netty.leakDetection.targetRecords: 4
[11:06:46:362] [DEBUG] - io.netty.util.ResourceLeakDetectorFactory$DefaultResourceLeakDetectorFactory.newResourceLeakDetector(ResourceLeakDetectorFactory.java:196) - Loaded default ResourceLeakDetector: io.netty.util.ResourceLeakDetector@d515c7f
[11:06:46:426] [DEBUG] - io.netty.channel.DefaultChannelId.<clinit>(DefaultChannelId.java:79) - -Dio.netty.processId: 67332 (auto-detected)
[11:06:46:435] [DEBUG] - io.netty.channel.DefaultChannelId.<clinit>(DefaultChannelId.java:101) - -Dio.netty.machineId: 76:f7:10:ff:fe:89:1e:3c (auto-detected)
[11:06:46:462] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:157) - -Dio.netty.allocator.numHeapArenas: 16
[11:06:46:463] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:158) - -Dio.netty.allocator.numDirectArenas: 16
[11:06:46:463] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:160) - -Dio.netty.allocator.pageSize: 8192
[11:06:46:464] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:165) - -Dio.netty.allocator.maxOrder: 9
[11:06:46:464] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:169) - -Dio.netty.allocator.chunkSize: 4194304
[11:06:46:465] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:170) - -Dio.netty.allocator.smallCacheSize: 256
[11:06:46:465] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:171) - -Dio.netty.allocator.normalCacheSize: 64
[11:06:46:466] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:172) - -Dio.netty.allocator.maxCachedBufferCapacity: 32768
[11:06:46:466] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:173) - -Dio.netty.allocator.cacheTrimInterval: 8192
[11:06:46:466] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:174) - -Dio.netty.allocator.cacheTrimIntervalMillis: 0
[11:06:46:467] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:175) - -Dio.netty.allocator.useCacheForAllThreads: false
[11:06:46:467] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:176) - -Dio.netty.allocator.maxCachedByteBuffersPerChunk: 1023
[11:06:46:481] [DEBUG] - io.netty.buffer.ByteBufUtil.<clinit>(ByteBufUtil.java:89) - -Dio.netty.allocator.type: pooled
[11:06:46:481] [DEBUG] - io.netty.buffer.ByteBufUtil.<clinit>(ByteBufUtil.java:98) - -Dio.netty.threadLocalDirectBufferSize: 0
[11:06:46:482] [DEBUG] - io.netty.buffer.ByteBufUtil.<clinit>(ByteBufUtil.java:101) - -Dio.netty.maxThreadLocalCharBufferSize: 16384
[11:06:46:489] [DEBUG] - io.netty.bootstrap.ChannelInitializerExtensions.getExtensions(ChannelInitializerExtensions.java:54) - -Dio.netty.bootstrap.extensions: null
[11:06:46:630] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[11:06:46:630] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[11:06:46:634] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[11:06:46:696] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:96) - -Dio.netty.recycler.maxCapacityPerThread: 4096
[11:06:46:698] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:97) - -Dio.netty.recycler.ratio: 8
[11:06:46:698] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:98) - -Dio.netty.recycler.chunkSize: 32
[11:06:46:699] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:99) - -Dio.netty.recycler.blocking: false
[11:06:46:699] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:100) - -Dio.netty.recycler.batchFastThreadLocalOnly: true
[11:06:46:707] [DEBUG] - io.netty.buffer.AbstractByteBuf.<clinit>(AbstractByteBuf.java:63) - -Dio.netty.buffer.checkAccessible: true
[11:06:46:708] [DEBUG] - io.netty.buffer.AbstractByteBuf.<clinit>(AbstractByteBuf.java:64) - -Dio.netty.buffer.checkBounds: true
[11:06:46:708] [DEBUG] - io.netty.util.ResourceLeakDetectorFactory$DefaultResourceLeakDetectorFactory.newResourceLeakDetector(ResourceLeakDetectorFactory.java:196) - Loaded default ResourceLeakDetector: io.netty.util.ResourceLeakDetector@375499e3
[11:06:46:789] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@220483560 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x9f8d6c1f, L:/192.168.8.195:53812 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@60c66040[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[11:06:46:789] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1285697553 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xf58757e2, L:/192.168.8.195:53814 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@5e27470c[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[11:06:46:819] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[11:06:46:820] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[11:06:46:826] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connectPubSub$3(ClientConnectionsEntry.java:234) - new pubsub connection created: RedisPubSubConnection@1352204005 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x0f416b84, L:/192.168.8.195:53813 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@5e5f0a86[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[11:06:46:827] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for 47.111.231.172/47.111.231.172:6379
[11:06:46:922] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@2033839866 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x9774a376, L:/192.168.8.195:53816 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@77fe1fad[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[11:06:46:931] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1650992400 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xddc3f349, L:/192.168.8.195:53815 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@6cafd72d[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[11:06:46:942] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[11:06:46:943] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[11:06:47:033] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1793832696 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xb112183f, L:/192.168.8.195:53817 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@642c45d5[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[11:06:47:035] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@2077369217 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x126490fe, L:/192.168.8.195:53818 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@7515d28b[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[11:06:47:053] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[11:06:47:051] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[11:06:47:130] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@629366290 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xe4c0ba41, L:/192.168.8.195:53820 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@2b44b4d2[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[11:06:47:134] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[11:06:47:135] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@885958070 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xdbe5c6c0, L:/192.168.8.195:53819 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@3a094d5f[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[11:06:47:137] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[11:06:47:206] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1070122405 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xf0a671f9, L:/192.168.8.195:53822 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@310f2c2c[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[11:06:47:210] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1964227607 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x0ce3a826, L:/192.168.8.195:53821 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@7bd44cc5[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[11:06:47:220] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[11:06:47:220] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[11:06:47:309] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1574791456 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x5eeb2fd8, L:/192.168.8.195:53823 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@531a8ceb[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[11:06:47:309] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1199782258 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6f9513db, L:/192.168.8.195:53824 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@4944dff2[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[11:06:47:321] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[11:06:47:320] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[11:06:47:440] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1584179989 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x8c4eacd5, L:/192.168.8.195:53825 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@50ab5ea8[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[11:06:47:440] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@503723865 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7bc0af39, L:/192.168.8.195:53826 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@10c1dae9[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[11:06:47:450] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[11:06:47:451] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[11:06:47:526] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1607754575 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2e90461d, L:/192.168.8.195:53828 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@6b34f0c2[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[11:06:47:528] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[11:06:47:573] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@422937119 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xa8b0268c, L:/192.168.8.195:53827 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@35da8cb9[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[11:06:47:578] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[11:06:47:625] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@11691349 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x28a76109, L:/192.168.8.195:53829 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@1093ebd[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[11:06:47:632] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[11:06:47:671] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1797739160 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd9618e58, L:/192.168.8.195:53834 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@24114fd6[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[11:06:47:680] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[11:06:47:742] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1942062474 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x1b702c2d, L:/192.168.8.195:53835 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@36c31596[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[11:06:47:749] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[11:06:47:770] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1688939589 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x27145ba6, L:/192.168.8.195:53836 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@484ce2f8[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[11:06:47:773] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[11:06:47:832] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1611914632 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x89eb0af9, L:/192.168.8.195:53838 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@25114fee[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[11:06:47:835] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[11:06:47:857] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1518073101 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x937f8727, L:/192.168.8.195:53837 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@207300a4[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[11:06:47:859] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[11:06:47:915] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@796603746 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x616552af, L:/192.168.8.195:53839 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@6a79bee9[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[11:06:47:947] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@2098867796 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xcf26f563, L:/192.168.8.195:53840 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@295ec5bb[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[11:06:47:947] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for 47.111.231.172/47.111.231.172:6379
[11:06:49:510] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 5 thread-local buffer(s) from thread: redisson-netty-2-12
[11:06:49:510] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 5 thread-local buffer(s) from thread: redisson-netty-2-7
[11:06:49:511] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 4 thread-local buffer(s) from thread: redisson-netty-2-25
[11:06:49:508] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 4 thread-local buffer(s) from thread: redisson-netty-2-28
[11:06:49:510] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 4 thread-local buffer(s) from thread: redisson-netty-2-32
[11:06:49:548] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 5 thread-local buffer(s) from thread: redisson-netty-2-8
[11:06:49:510] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 5 thread-local buffer(s) from thread: redisson-netty-2-2
[11:06:49:563] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 5 thread-local buffer(s) from thread: redisson-netty-2-9
[11:06:49:547] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 5 thread-local buffer(s) from thread: redisson-netty-2-21
[11:06:49:510] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 5 thread-local buffer(s) from thread: redisson-netty-2-16
[11:06:49:561] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 4 thread-local buffer(s) from thread: redisson-netty-2-24
[11:06:49:534] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 5 thread-local buffer(s) from thread: redisson-netty-2-17
[11:06:49:547] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 5 thread-local buffer(s) from thread: redisson-netty-2-5
[11:06:49:508] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 5 thread-local buffer(s) from thread: redisson-netty-2-3
[11:06:49:509] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 4 thread-local buffer(s) from thread: redisson-netty-2-29
[11:06:49:546] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 5 thread-local buffer(s) from thread: redisson-netty-2-13
[11:06:49:533] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 4 thread-local buffer(s) from thread: redisson-netty-2-11
[11:06:49:546] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 4 thread-local buffer(s) from thread: redisson-netty-2-1
[11:06:49:503] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 4 thread-local buffer(s) from thread: redisson-netty-2-15
[11:06:49:511] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 5 thread-local buffer(s) from thread: redisson-netty-2-19
[11:06:49:586] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 5 thread-local buffer(s) from thread: redisson-netty-2-4
[11:06:49:598] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-1} closing ...
[11:06:49:618] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-1} closed
