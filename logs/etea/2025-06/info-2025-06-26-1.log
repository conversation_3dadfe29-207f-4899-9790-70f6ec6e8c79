[2025-06-26 15:24:05:689] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "prod-172"
[2025-06-26 15:24:09:558] [INFO] - org.springframework.data.repository.config.RepositoryConfigurationDelegate.multipleStoresDetected(RepositoryConfigurationDelegate.java:262) - Multiple Spring Data modules found, entering strict repository configuration mode
[2025-06-26 15:24:09:561] [INFO] - org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn(RepositoryConfigurationDelegate.java:132) - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2025-06-26 15:24:09:618] [INFO] - org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn(RepositoryConfigurationDelegate.java:201) - Finished Spring Data repository scanning in 40 ms. Found 0 Redis repository interfaces.
[2025-06-26 15:24:10:197] [INFO] - org.springframework.cloud.context.scope.GenericScope.setSerializationId(GenericScope.java:283) - BeanFactory id=0541a14d-3326-3d31-8556-53e99a2a9681
[2025-06-26 15:24:10:548] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[2025-06-26 15:24:10:643] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[2025-06-26 15:24:10:644] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[2025-06-26 15:24:10:644] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[2025-06-26 15:24:10:645] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[2025-06-26 15:24:10:645] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[2025-06-26 15:24:10:646] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[2025-06-26 15:24:10:646] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[2025-06-26 15:24:10:646] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-06-26 15:24:10:646] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-prod-172.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-06-26 15:24:10:647] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-06-26 15:24:10:647] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-06-26 15:24:10:648] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-06-26 15:24:10:722] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[2025-06-26 15:24:11:541] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[2025-06-26 15:24:11:549] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[2025-06-26 15:24:13:022] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[2025-06-26 15:24:13:022] [INFO] - org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.prepareWebApplicationContext(ServletWebServerApplicationContext.java:292) - Root WebApplicationContext: initialization completed in 7214 ms
[2025-06-26 15:24:19:916] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[2025-06-26 15:24:19:958] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-1} closing ...
[2025-06-26 15:24:19:963] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-1} closed
[2025-06-26 15:24:20:023] [INFO] - org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener.logMessage(ConditionEvaluationReportLoggingListener.java:136) - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
[15:24:59:381] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[15:25:02:999] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[15:25:03:075] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[15:25:03:078] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:25:03:080] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:25:03:080] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[15:25:03:080] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[15:25:03:080] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[15:25:03:080] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[15:25:03:080] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:25:03:080] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:25:03:081] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:25:03:082] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:25:03:083] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:25:03:145] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[15:25:03:775] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[15:25:03:781] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[15:25:04:581] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[15:25:04:617] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[15:25:04:985] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[15:25:04:997] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[15:25:04:997] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[15:25:04:998] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[15:25:04:998] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[15:25:04:999] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[15:25:05:000] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[15:25:05:002] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[15:25:05:004] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[15:25:11:215] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[15:25:11:230] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[15:25:11:569] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[15:25:13:416] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：1.833秒
[15:25:19:842] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[15:25:19:930] [WARN] - io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:70) - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
[15:25:20:491] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[15:25:21:734] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[15:25:27:842] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[15:25:28:678] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 57 ms to scan 1 urls, producing 3 keys and 6 values 
[15:25:28:740] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 23 ms to scan 1 urls, producing 4 keys and 9 values 
[15:25:28:778] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 18 ms to scan 1 urls, producing 3 keys and 10 values 
[15:25:28:839] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 43 ms to scan 12 urls, producing 0 keys and 0 values 
[15:25:28:856] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 13 ms to scan 1 urls, producing 1 keys and 5 values 
[15:25:28:873] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
[15:25:28:939] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 59 ms to scan 1 urls, producing 2 keys and 8 values 
[15:25:28:974] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 31 ms to scan 12 urls, producing 0 keys and 0 values 
[15:25:30:505] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[15:25:30:507] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[15:25:30:531] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[15:25:30:559] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[15:25:30:580] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[15:25:30:632] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[15:25:30:837] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[15:25:31:257] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 34.266 seconds (JVM running for 35.504)
[15:25:31:284] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[15:28:14:461] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[15:28:14:502] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[15:28:14:858] [ERROR] - com.alibaba.cloud.nacos.discovery.NacosWatch.stop(NacosWatch.java:178) - namingService unsubscribe failed, properties:NacosDiscoveryProperties{serverAddr='127.0.0.1:8848', username='nacos', password='9945xqyg', endpoint='', namespace='public', watchDelay=30000, logName='', service='exam-main-service', weight=1.0, clusterName='DEFAULT', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={management.endpoints.web.base-path=/activeEgoolanw98sjHpK, preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='*************', networkInterface='', port=8081, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}
com.alibaba.nacos.api.exception.NacosException: Request nacos server failed: 
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:290) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doUnsubscribe(NamingGrpcClientProxy.java:260) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.unsubscribe(NamingGrpcClientProxy.java:241) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.unsubscribe(NamingClientProxyDelegate.java:157) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.NacosNamingService.unsubscribe(NacosNamingService.java:417) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.cloud.nacos.discovery.NacosWatch.stop(NacosWatch.java:174) ~[spring-cloud-starter-alibaba-nacos-discovery-2021.0.4.0.jar:2021.0.4.0]
	at com.alibaba.cloud.nacos.discovery.NacosWatch.stop(NacosWatch.java:107) ~[spring-cloud-starter-alibaba-nacos-discovery-2021.0.4.0.jar:2021.0.4.0]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStop(DefaultLifecycleProcessor.java:235) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.access$300(DefaultLifecycleProcessor.java:54) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.stop(DefaultLifecycleProcessor.java:374) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.stopBeans(DefaultLifecycleProcessor.java:207) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.onClose(DefaultLifecycleProcessor.java:130) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1078) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:94) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:82) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.devtools.restart.Restarter.stop(Restarter.java:309) ~[spring-boot-devtools-2.7.18.jar:2.7.18]
	at org.springframework.boot.devtools.restart.Restarter.lambda$restart$1(Restarter.java:251) ~[spring-boot-devtools-2.7.18.jar:2.7.18]
	at org.springframework.boot.devtools.restart.Restarter$LeakSafeThread.run(Restarter.java:629) [spring-boot-devtools-2.7.18.jar:2.7.18]
Caused by: com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:650) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:630) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:280) ~[nacos-client-2.0.4.jar:?]
	... 26 more
[15:28:15:908] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[15:28:16:246] [ERROR] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:110) - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='127.0.0.1:8848', username='nacos', password='9945xqyg', endpoint='', namespace='public', watchDelay=30000, logName='', service='exam-main-service', weight=1.0, clusterName='DEFAULT', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={management.endpoints.web.base-path=/activeEgoolanw98sjHpK, preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='*************', networkInterface='', port=8081, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}},
com.alibaba.nacos.api.exception.NacosException: Request nacos server failed: 
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:290) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doDeregisterService(NamingGrpcClientProxy.java:153) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterService(NamingGrpcClientProxy.java:139) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.deregisterService(NamingClientProxyDelegate.java:99) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:180) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:170) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:106) ~[spring-cloud-starter-alibaba-nacos-discovery-2021.0.4.0.jar:2021.0.4.0]
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:249) ~[spring-cloud-commons-3.1.7.jar:3.1.7]
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:264) ~[spring-cloud-commons-3.1.7.jar:3.1.7]
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:201) ~[spring-cloud-commons-3.1.7.jar:3.1.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:197) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1086) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:94) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:82) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.devtools.restart.Restarter.stop(Restarter.java:309) ~[spring-boot-devtools-2.7.18.jar:2.7.18]
	at org.springframework.boot.devtools.restart.Restarter.lambda$restart$1(Restarter.java:251) ~[spring-boot-devtools-2.7.18.jar:2.7.18]
	at org.springframework.boot.devtools.restart.Restarter$LeakSafeThread.run(Restarter.java:629) [spring-boot-devtools-2.7.18.jar:2.7.18]
Caused by: com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:650) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:630) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:280) ~[nacos-client-2.0.4.jar:?]
	... 38 more
[15:28:16:255] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[15:28:16:740] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-1} closing ...
[15:28:16:789] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-1} closed
[15:28:19:612] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[15:28:22:983] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[15:28:23:026] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[15:28:23:026] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:28:23:026] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:28:23:026] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[15:28:23:026] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[15:28:23:026] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[15:28:23:026] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[15:28:23:027] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:28:23:027] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:28:23:027] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:28:23:027] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:28:23:028] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:28:23:145] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[15:28:23:336] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[15:28:23:337] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[15:28:23:671] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[15:28:23:680] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[15:28:23:960] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[15:28:23:962] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[15:28:23:962] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[15:28:23:963] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[15:28:23:963] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[15:28:23:964] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[15:28:23:964] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[15:28:23:966] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[15:28:23:967] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[15:28:28:600] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-2} inited
[15:28:28:601] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[15:28:28:723] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[15:28:29:313] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.589秒
[15:28:34:376] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[15:28:34:642] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[15:28:35:700] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[15:28:38:361] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[15:28:38:824] [ERROR] - com.alibaba.cloud.nacos.discovery.NacosWatch.start(NacosWatch.java:136) - namingService subscribe failed, properties:NacosDiscoveryProperties{serverAddr='127.0.0.1:8848', username='nacos', password='9945xqyg', endpoint='', namespace='public', watchDelay=30000, logName='', service='exam-main-service', weight=1.0, clusterName='DEFAULT', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={management.endpoints.web.base-path=/activeEgoolanw98sjHpK, preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='*************', networkInterface='', port=-1, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}
com.alibaba.nacos.api.exception.NacosException: Request nacos server failed: 
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:290) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doSubscribe(NamingGrpcClientProxy.java:230) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.subscribe(NamingGrpcClientProxy.java:215) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.subscribe(NamingClientProxyDelegate.java:147) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.NacosNamingService.subscribe(NacosNamingService.java:393) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.cloud.nacos.discovery.NacosWatch.start(NacosWatch.java:132) ~[spring-cloud-starter-alibaba-nacos-discovery-2021.0.4.0.jar:2021.0.4.0]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:179) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:357) ~[spring-context-5.3.31.jar:5.3.31]
	at java.lang.Iterable.forEach(Iterable.java:75) [?:1.8.0_281]
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:156) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:124) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:946) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:594) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.SpringApplicationBuilder.run(SpringApplicationBuilder.java:164) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.examinationassistant.ExaminationAssistantApplication.main(ExaminationAssistantApplication.java:44) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
Caused by: com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:STARTING
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:650) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:630) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:280) ~[nacos-client-2.0.4.jar:?]
	... 24 more
[15:28:38:911] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[15:28:38:912] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[15:28:38:913] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[15:28:39:262] [ERROR] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:80) - nacos registry, exam-main-service register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='127.0.0.1:8848', username='nacos', password='9945xqyg', endpoint='', namespace='public', watchDelay=30000, logName='', service='exam-main-service', weight=1.0, clusterName='DEFAULT', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={management.endpoints.web.base-path=/activeEgoolanw98sjHpK, preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='*************', networkInterface='', port=8081, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}},
com.alibaba.nacos.api.exception.NacosException: Request nacos server failed: 
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:290) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doRegisterService(NamingGrpcClientProxy.java:129) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.registerService(NamingGrpcClientProxy.java:115) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.registerService(NamingClientProxyDelegate.java:94) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:145) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:74) ~[spring-cloud-starter-alibaba-nacos-discovery-2021.0.4.0.jar:2021.0.4.0]
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.register(AbstractAutoServiceRegistration.java:232) ~[spring-cloud-commons-3.1.7.jar:3.1.7]
	at com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration.register(NacosAutoServiceRegistration.java:78) ~[spring-cloud-starter-alibaba-nacos-discovery-2021.0.4.0.jar:2021.0.4.0]
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.start(AbstractAutoServiceRegistration.java:133) ~[spring-cloud-commons-3.1.7.jar:3.1.7]
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.bind(AbstractAutoServiceRegistration.java:98) ~[spring-cloud-commons-3.1.7.jar:3.1.7]
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:86) ~[spring-cloud-commons-3.1.7.jar:3.1.7]
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:47) ~[spring-cloud-commons-3.1.7.jar:3.1.7]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.WebServerStartStopLifecycle.start(WebServerStartStopLifecycle.java:46) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:179) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:357) ~[spring-context-5.3.31.jar:5.3.31]
	at java.lang.Iterable.forEach(Iterable.java:75) [?:1.8.0_281]
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:156) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:124) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:946) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:594) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.SpringApplicationBuilder.run(SpringApplicationBuilder.java:164) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.examinationassistant.ExaminationAssistantApplication.main(ExaminationAssistantApplication.java:44) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
Caused by: com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:STARTING
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:650) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:630) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:280) ~[nacos-client-2.0.4.jar:?]
	... 36 more
[15:28:39:601] [ERROR] - com.alibaba.cloud.nacos.discovery.NacosWatch.stop(NacosWatch.java:178) - namingService unsubscribe failed, properties:NacosDiscoveryProperties{serverAddr='127.0.0.1:8848', username='nacos', password='9945xqyg', endpoint='', namespace='public', watchDelay=30000, logName='', service='exam-main-service', weight=1.0, clusterName='DEFAULT', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={management.endpoints.web.base-path=/activeEgoolanw98sjHpK, preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='*************', networkInterface='', port=8081, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}
com.alibaba.nacos.api.exception.NacosException: Request nacos server failed: 
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:290) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doUnsubscribe(NamingGrpcClientProxy.java:260) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.unsubscribe(NamingGrpcClientProxy.java:241) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.unsubscribe(NamingClientProxyDelegate.java:157) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.NacosNamingService.unsubscribe(NacosNamingService.java:417) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.cloud.nacos.discovery.NacosWatch.stop(NacosWatch.java:174) [spring-cloud-starter-alibaba-nacos-discovery-2021.0.4.0.jar:2021.0.4.0]
	at com.alibaba.cloud.nacos.discovery.NacosWatch.destroy(NacosWatch.java:204) [spring-cloud-starter-alibaba-nacos-discovery-2021.0.4.0.jar:2021.0.4.0]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:604) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.SpringApplicationBuilder.run(SpringApplicationBuilder.java:164) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.examinationassistant.ExaminationAssistantApplication.main(ExaminationAssistantApplication.java:44) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
Caused by: com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:STARTING
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:650) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:630) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:280) ~[nacos-client-2.0.4.jar:?]
	... 25 more
[15:28:40:126] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-2} closing ...
[15:28:40:130] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-2} closed
[15:28:40:137] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[15:28:40:143] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[15:28:42:353] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[15:28:43:347] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[15:28:43:395] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[15:28:43:396] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:28:43:396] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:28:43:396] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[15:28:43:396] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[15:28:43:396] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[15:28:43:396] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[15:28:43:397] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:28:43:397] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:28:43:397] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:28:43:397] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:28:43:397] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:28:43:431] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[15:28:43:548] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[15:28:43:549] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[15:28:43:711] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[15:28:43:716] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[15:28:43:807] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[15:28:43:810] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[15:28:43:811] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[15:28:43:811] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[15:28:43:811] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[15:28:43:812] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[15:28:43:812] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[15:28:43:812] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[15:28:43:813] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[15:28:47:349] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-3} inited
[15:28:47:350] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[15:28:47:457] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[15:28:47:976] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.519秒
[15:28:52:493] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[15:28:52:700] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[15:28:53:968] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[15:28:55:968] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[15:28:56:400] [ERROR] - com.alibaba.cloud.nacos.discovery.NacosWatch.start(NacosWatch.java:136) - namingService subscribe failed, properties:NacosDiscoveryProperties{serverAddr='127.0.0.1:8848', username='nacos', password='9945xqyg', endpoint='', namespace='public', watchDelay=30000, logName='', service='exam-main-service', weight=1.0, clusterName='DEFAULT', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={management.endpoints.web.base-path=/activeEgoolanw98sjHpK, preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='*************', networkInterface='', port=-1, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}
com.alibaba.nacos.api.exception.NacosException: Request nacos server failed: 
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:290) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doSubscribe(NamingGrpcClientProxy.java:230) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.subscribe(NamingGrpcClientProxy.java:215) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.subscribe(NamingClientProxyDelegate.java:147) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.NacosNamingService.subscribe(NacosNamingService.java:393) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.cloud.nacos.discovery.NacosWatch.start(NacosWatch.java:132) ~[spring-cloud-starter-alibaba-nacos-discovery-2021.0.4.0.jar:2021.0.4.0]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:179) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:357) ~[spring-context-5.3.31.jar:5.3.31]
	at java.lang.Iterable.forEach(Iterable.java:75) [?:1.8.0_281]
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:156) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:124) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:946) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:594) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.SpringApplicationBuilder.run(SpringApplicationBuilder.java:164) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.examinationassistant.ExaminationAssistantApplication.main(ExaminationAssistantApplication.java:44) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
Caused by: com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:STARTING
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:650) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:630) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:280) ~[nacos-client-2.0.4.jar:?]
	... 24 more
[15:28:56:420] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[15:28:56:421] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[15:28:56:425] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[15:28:56:769] [ERROR] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:80) - nacos registry, exam-main-service register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='127.0.0.1:8848', username='nacos', password='9945xqyg', endpoint='', namespace='public', watchDelay=30000, logName='', service='exam-main-service', weight=1.0, clusterName='DEFAULT', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={management.endpoints.web.base-path=/activeEgoolanw98sjHpK, preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='*************', networkInterface='', port=8081, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}},
com.alibaba.nacos.api.exception.NacosException: Request nacos server failed: 
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:290) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doRegisterService(NamingGrpcClientProxy.java:129) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.registerService(NamingGrpcClientProxy.java:115) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.registerService(NamingClientProxyDelegate.java:94) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:145) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:74) ~[spring-cloud-starter-alibaba-nacos-discovery-2021.0.4.0.jar:2021.0.4.0]
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.register(AbstractAutoServiceRegistration.java:232) ~[spring-cloud-commons-3.1.7.jar:3.1.7]
	at com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration.register(NacosAutoServiceRegistration.java:78) ~[spring-cloud-starter-alibaba-nacos-discovery-2021.0.4.0.jar:2021.0.4.0]
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.start(AbstractAutoServiceRegistration.java:133) ~[spring-cloud-commons-3.1.7.jar:3.1.7]
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.bind(AbstractAutoServiceRegistration.java:98) ~[spring-cloud-commons-3.1.7.jar:3.1.7]
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:86) ~[spring-cloud-commons-3.1.7.jar:3.1.7]
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:47) ~[spring-cloud-commons-3.1.7.jar:3.1.7]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.WebServerStartStopLifecycle.start(WebServerStartStopLifecycle.java:46) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:179) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:357) ~[spring-context-5.3.31.jar:5.3.31]
	at java.lang.Iterable.forEach(Iterable.java:75) [?:1.8.0_281]
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:156) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:124) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:946) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:594) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.SpringApplicationBuilder.run(SpringApplicationBuilder.java:164) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.examinationassistant.ExaminationAssistantApplication.main(ExaminationAssistantApplication.java:44) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
Caused by: com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:STARTING
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:650) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:630) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:280) ~[nacos-client-2.0.4.jar:?]
	... 36 more
[15:28:57:095] [ERROR] - com.alibaba.cloud.nacos.discovery.NacosWatch.stop(NacosWatch.java:178) - namingService unsubscribe failed, properties:NacosDiscoveryProperties{serverAddr='127.0.0.1:8848', username='nacos', password='9945xqyg', endpoint='', namespace='public', watchDelay=30000, logName='', service='exam-main-service', weight=1.0, clusterName='DEFAULT', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={management.endpoints.web.base-path=/activeEgoolanw98sjHpK, preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='*************', networkInterface='', port=8081, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}
com.alibaba.nacos.api.exception.NacosException: Request nacos server failed: 
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:290) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doUnsubscribe(NamingGrpcClientProxy.java:260) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.unsubscribe(NamingGrpcClientProxy.java:241) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.unsubscribe(NamingClientProxyDelegate.java:157) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.NacosNamingService.unsubscribe(NacosNamingService.java:417) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.cloud.nacos.discovery.NacosWatch.stop(NacosWatch.java:174) [spring-cloud-starter-alibaba-nacos-discovery-2021.0.4.0.jar:2021.0.4.0]
	at com.alibaba.cloud.nacos.discovery.NacosWatch.destroy(NacosWatch.java:204) [spring-cloud-starter-alibaba-nacos-discovery-2021.0.4.0.jar:2021.0.4.0]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:604) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.SpringApplicationBuilder.run(SpringApplicationBuilder.java:164) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.examinationassistant.ExaminationAssistantApplication.main(ExaminationAssistantApplication.java:44) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
Caused by: com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:STARTING
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:650) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:630) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:280) ~[nacos-client-2.0.4.jar:?]
	... 25 more
[15:28:57:605] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-3} closing ...
[15:28:57:613] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-3} closed
[15:28:57:626] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[15:28:57:633] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[15:47:23:980] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[15:47:27:493] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[15:47:27:562] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[15:47:27:562] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:47:27:563] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:47:27:563] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[15:47:27:563] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[15:47:27:563] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[15:47:27:563] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[15:47:27:563] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:47:27:564] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:47:27:565] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:47:27:565] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:47:27:566] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:47:27:616] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[15:47:28:275] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[15:47:28:284] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[15:47:29:041] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[15:47:29:074] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[15:47:29:437] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[15:47:29:452] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[15:47:29:453] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[15:47:29:453] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[15:47:29:455] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[15:47:29:455] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[15:47:29:456] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[15:47:29:458] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[15:47:29:460] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[15:47:34:391] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[15:47:34:406] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[15:47:34:709] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[15:47:37:193] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：2.47秒
[15:47:45:187] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[15:47:45:278] [WARN] - io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:70) - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
[15:47:46:494] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[15:47:47:750] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[15:47:53:864] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[15:47:54:777] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 51 ms to scan 1 urls, producing 3 keys and 6 values 
[15:47:54:846] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 25 ms to scan 1 urls, producing 4 keys and 9 values 
[15:47:54:892] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 29 ms to scan 1 urls, producing 3 keys and 10 values 
[15:47:54:941] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 38 ms to scan 12 urls, producing 0 keys and 0 values 
[15:47:54:957] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
[15:47:54:971] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
[15:47:54:992] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
[15:47:55:022] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 26 ms to scan 12 urls, producing 0 keys and 0 values 
[15:47:56:360] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[15:47:56:360] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[15:47:56:377] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[15:47:56:398] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[15:47:56:421] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[15:47:56:480] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[15:47:56:618] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[15:47:57:000] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 35.329 seconds (JVM running for 36.575)
[15:47:57:036] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[15:51:36:926] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[15:51:49:457] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:55) - 产品：etea 页面：/pages/personal/admin/exam/index?
[15:51:49:458] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:56) - 请求接口：/examination/getExaminationInViewListOfCreater
[15:51:49:458] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:57) - 请求参数：{"companyId":"0","pageIndex":"1","pageSize":"20","refresh":"true","createrId":"1476420","terminal":"etea","callLocationCode":"/pages/personal/admin/exam/index?"}
[15:51:50:900] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750924310800,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[15:51:51:053] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750924311050,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[15:51:51:275] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750924311261,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[15:51:51:401] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750924311393,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[15:51:51:531] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750924311528,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[15:51:51:712] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750924311694,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[15:51:51:883] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750924311873,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[15:51:51:996] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750924311988,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[15:51:52:109] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750924312104,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[15:51:52:207] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750924312203,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[15:51:52:317] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/etea/examination/20241029/20241029104250b091.jpeg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750924312313,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[15:51:52:466] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/etea/examination/20241029/20241029104250b091.jpeg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750924312462,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[15:51:52:482] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750924312479,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[15:51:52:608] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750924312605,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[15:51:52:727] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750924312725,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[15:51:52:867] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750924312855,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[15:51:53:006] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/etea/examination/20240604/2024060417034913eb.jpeg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750924313001,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7093 bytes)]
[15:51:53:157] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/etea/examination/20240604/2024060417034913eb.jpeg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750924313154,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7093 bytes)]
[15:51:53:169] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750924313166,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7093 bytes)]
[15:51:53:303] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750924313300,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7093 bytes)]
[15:51:53:432] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750924313428,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7093 bytes)]
[15:51:53:562] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750924313559,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7093 bytes)]
[15:52:27:274] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/userForm/getUserFormListByUserId，出现异常:feign.FeignException$ServiceUnavailable；
[15:52:27:275] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"*/*","Referer":"https://servicewechat.com/wxa38e954efdceffbe/devtools/page-frame.html","X-Forwarded-Host":"localhost:8000","User-Agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.06.2503300 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/2","X-Forwarded-Proto":"http","Sec-Fetch-Site":"cross-site","Sec-Fetch-Dest":"empty","Accept-Encoding":"gzip, deflate, br","userId":"1476420","X-Forwarded-Port":"8000","token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJ5aWRhbyIsImV4cCI6MTc1MTUyOTA5OCwiaWF0IjoxNzUwOTI0Mjk4LCJ1c2VySWQiOjE0NzY0MjB9.LbiiqTHg41vMY04m75qoCikMKRsv89_3AEFNML-eT4s","Sec-Fetch-Mode":"cors","host":"*************:8081","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:63074\"","client":"eteamobile","X-Forwarded-For":"0:0:0:0:0:0:0:1","content-type":"application/json;charset=UTF-8","Accept-Language":"zh-CN,zh;q=0.9"}
[15:52:27:276] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"terminal":"etea","userId":"1476420","callLocationCode":"/packageA/pages/formselect/select?"}
[15:52:27:276] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：feign.FeignException$ServiceUnavailable: [503] during [POST] to [http://taurus-formSystem/formDefinition/getFormListByIds] [TaurusFormSystemFeignClient#getFormListByFormIds(JSONArray)]: [Load balancer does not contain an instance for the service taurus-formSystem]，异常信息：[503] during [POST] to [http://taurus-formSystem/formDefinition/getFormListByIds] [TaurusFormSystemFeignClient#getFormListByFormIds(JSONArray)]: [Load balancer does not contain an instance for the service taurus-formSystem]，
[15:53:22:642] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/userForm/getUserFormListByUserId，出现异常:feign.FeignException$ServiceUnavailable；
[15:53:22:645] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"*/*","Referer":"https://servicewechat.com/wxa38e954efdceffbe/devtools/page-frame.html","X-Forwarded-Host":"localhost:8000","User-Agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.06.2503300 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/2","X-Forwarded-Proto":"http","Sec-Fetch-Site":"cross-site","Sec-Fetch-Dest":"empty","Accept-Encoding":"gzip, deflate, br","userId":"1476420","X-Forwarded-Port":"8000","token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJ5aWRhbyIsImV4cCI6MTc1MTUyOTA5OCwiaWF0IjoxNzUwOTI0Mjk4LCJ1c2VySWQiOjE0NzY0MjB9.LbiiqTHg41vMY04m75qoCikMKRsv89_3AEFNML-eT4s","Sec-Fetch-Mode":"cors","host":"*************:8081","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:63074\"","client":"eteamobile","X-Forwarded-For":"0:0:0:0:0:0:0:1","content-type":"application/json;charset=UTF-8","Accept-Language":"zh-CN,zh;q=0.9"}
[15:53:22:646] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"terminal":"etea","userId":"1476420","callLocationCode":"/packageA/pages/formselect/select?"}
[15:53:22:646] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：feign.FeignException$ServiceUnavailable: [503] during [POST] to [http://taurus-formSystem/formDefinition/getFormListByIds] [TaurusFormSystemFeignClient#getFormListByFormIds(JSONArray)]: [Load balancer does not contain an instance for the service taurus-formSystem]，异常信息：[503] during [POST] to [http://taurus-formSystem/formDefinition/getFormListByIds] [TaurusFormSystemFeignClient#getFormListByFormIds(JSONArray)]: [Load balancer does not contain an instance for the service taurus-formSystem]，
[15:53:40:510] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/userForm/getUserFormListByUserId，出现异常:feign.FeignException$ServiceUnavailable；
[15:53:40:511] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"*/*","Referer":"https://servicewechat.com/wxa38e954efdceffbe/devtools/page-frame.html","X-Forwarded-Host":"localhost:8000","User-Agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.06.2503300 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/2","X-Forwarded-Proto":"http","Sec-Fetch-Site":"cross-site","Sec-Fetch-Dest":"empty","Accept-Encoding":"gzip, deflate, br","userId":"1476420","X-Forwarded-Port":"8000","token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJ5aWRhbyIsImV4cCI6MTc1MTUyOTA5OCwiaWF0IjoxNzUwOTI0Mjk4LCJ1c2VySWQiOjE0NzY0MjB9.LbiiqTHg41vMY04m75qoCikMKRsv89_3AEFNML-eT4s","Sec-Fetch-Mode":"cors","host":"*************:8081","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:63074\"","client":"eteamobile","X-Forwarded-For":"0:0:0:0:0:0:0:1","content-type":"application/json;charset=UTF-8","Accept-Language":"zh-CN,zh;q=0.9"}
[15:53:40:512] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"terminal":"etea","userId":"1476420","callLocationCode":"/packageA/pages/formselect/select?"}
[15:53:40:512] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：feign.FeignException$ServiceUnavailable: [503] during [POST] to [http://taurus-formSystem/formDefinition/getFormListByIds] [TaurusFormSystemFeignClient#getFormListByFormIds(JSONArray)]: [Load balancer does not contain an instance for the service taurus-formSystem]，异常信息：[503] during [POST] to [http://taurus-formSystem/formDefinition/getFormListByIds] [TaurusFormSystemFeignClient#getFormListByFormIds(JSONArray)]: [Load balancer does not contain an instance for the service taurus-formSystem]，
[15:54:04:009] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/userForm/getUserFormListByUserId，出现异常:feign.FeignException$ServiceUnavailable；
[15:54:04:010] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"*/*","Referer":"https://servicewechat.com/wxa38e954efdceffbe/devtools/page-frame.html","X-Forwarded-Host":"localhost:8000","User-Agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.06.2503300 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/2","X-Forwarded-Proto":"http","Sec-Fetch-Site":"cross-site","Sec-Fetch-Dest":"empty","Accept-Encoding":"gzip, deflate, br","userId":"1476420","X-Forwarded-Port":"8000","token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJ5aWRhbyIsImV4cCI6MTc1MTUyOTA5OCwiaWF0IjoxNzUwOTI0Mjk4LCJ1c2VySWQiOjE0NzY0MjB9.LbiiqTHg41vMY04m75qoCikMKRsv89_3AEFNML-eT4s","Sec-Fetch-Mode":"cors","host":"*************:8081","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:63074\"","client":"eteamobile","X-Forwarded-For":"0:0:0:0:0:0:0:1","content-type":"application/json;charset=UTF-8","Accept-Language":"zh-CN,zh;q=0.9"}
[15:54:04:011] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"terminal":"etea","userId":"1476420","callLocationCode":"/packageA/pages/formselect/select?"}
[15:54:04:011] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：feign.FeignException$ServiceUnavailable: [503] during [POST] to [http://taurus-formSystem/formDefinition/getFormListByIds] [TaurusFormSystemFeignClient#getFormListByFormIds(JSONArray)]: [Load balancer does not contain an instance for the service taurus-formSystem]，异常信息：[503] during [POST] to [http://taurus-formSystem/formDefinition/getFormListByIds] [TaurusFormSystemFeignClient#getFormListByFormIds(JSONArray)]: [Load balancer does not contain an instance for the service taurus-formSystem]，
[15:56:11:894] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/userForm/getUserFormListByUserId，出现异常:feign.FeignException$InternalServerError；
[15:56:11:898] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"*/*","Referer":"https://servicewechat.com/wxa38e954efdceffbe/devtools/page-frame.html","X-Forwarded-Host":"localhost:8000","User-Agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.06.2503300 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/2","X-Forwarded-Proto":"http","Sec-Fetch-Site":"cross-site","Sec-Fetch-Dest":"empty","Accept-Encoding":"gzip, deflate, br","userId":"1476420","X-Forwarded-Port":"8000","token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJ5aWRhbyIsImV4cCI6MTc1MTUyOTA5OCwiaWF0IjoxNzUwOTI0Mjk4LCJ1c2VySWQiOjE0NzY0MjB9.LbiiqTHg41vMY04m75qoCikMKRsv89_3AEFNML-eT4s","Sec-Fetch-Mode":"cors","host":"*************:8081","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:63074\"","client":"eteamobile","X-Forwarded-For":"0:0:0:0:0:0:0:1","content-type":"application/json;charset=UTF-8","Accept-Language":"zh-CN,zh;q=0.9"}
[15:56:11:900] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"terminal":"etea","userId":"1476420","callLocationCode":"/packageA/pages/formselect/select?"}
[15:56:11:900] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：feign.FeignException$InternalServerError: [500 Internal Server Error] during [POST] to [http://taurus-formSystem/formDefinition/getFormListByIds] [TaurusFormSystemFeignClient#getFormListByFormIds(JSONArray)]: [{"timestamp":1750924571859,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.formSys.mapper.FormDefiniti... (7815 bytes)]，异常信息：[500 Internal Server Error] during [POST] to [http://taurus-formSystem/formDefinition/getFormListByIds] [TaurusFormSystemFeignClient#getFormListByFormIds(JSONArray)]: [{"timestamp":1750924571859,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.formSys.mapper.FormDefiniti... (7815 bytes)]，
[16:02:57:840] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/userForm/getUserFormListByUserId，出现异常:feign.FeignException$InternalServerError；
[16:02:57:842] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"*/*","Referer":"https://servicewechat.com/wxa38e954efdceffbe/devtools/page-frame.html","X-Forwarded-Host":"localhost:8000","User-Agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.06.2503300 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/2","X-Forwarded-Proto":"http","Sec-Fetch-Site":"cross-site","Sec-Fetch-Dest":"empty","Accept-Encoding":"gzip, deflate, br","userId":"1476420","X-Forwarded-Port":"8000","token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJ5aWRhbyIsImV4cCI6MTc1MTUyOTA5OCwiaWF0IjoxNzUwOTI0Mjk4LCJ1c2VySWQiOjE0NzY0MjB9.LbiiqTHg41vMY04m75qoCikMKRsv89_3AEFNML-eT4s","Sec-Fetch-Mode":"cors","host":"*************:8081","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:50462\"","client":"eteamobile","X-Forwarded-For":"0:0:0:0:0:0:0:1","content-type":"application/json;charset=UTF-8","Accept-Language":"zh-CN,zh;q=0.9"}
[16:02:57:842] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"terminal":"etea","userId":"1476420","callLocationCode":"/packageA/pages/formselect/select?"}
[16:02:57:842] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：feign.FeignException$InternalServerError: [500 Internal Server Error] during [POST] to [http://taurus-formSystem/formDefinition/getFormListByIds] [TaurusFormSystemFeignClient#getFormListByFormIds(JSONArray)]: [{"timestamp":1750924977792,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.formSys.mapper.FormDefiniti... (7815 bytes)]，异常信息：[500 Internal Server Error] during [POST] to [http://taurus-formSystem/formDefinition/getFormListByIds] [TaurusFormSystemFeignClient#getFormListByFormIds(JSONArray)]: [{"timestamp":1750924977792,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.formSys.mapper.FormDefiniti... (7815 bytes)]，
[16:48:14:125] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/userForm/getUserFormListByUserId，出现异常:feign.FeignException$InternalServerError；
[16:48:14:127] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"*/*","Referer":"https://servicewechat.com/wxa38e954efdceffbe/devtools/page-frame.html","X-Forwarded-Host":"localhost:8000","User-Agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.06.2503300 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/2","X-Forwarded-Proto":"http","Sec-Fetch-Site":"cross-site","Sec-Fetch-Dest":"empty","Accept-Encoding":"gzip, deflate, br","userId":"1476420","X-Forwarded-Port":"8000","token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJ5aWRhbyIsImV4cCI6MTc1MTUyOTA5OCwiaWF0IjoxNzUwOTI0Mjk4LCJ1c2VySWQiOjE0NzY0MjB9.LbiiqTHg41vMY04m75qoCikMKRsv89_3AEFNML-eT4s","Sec-Fetch-Mode":"cors","host":"*************:8081","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:49669\"","client":"eteamobile","X-Forwarded-For":"0:0:0:0:0:0:0:1","content-type":"application/json;charset=UTF-8","Accept-Language":"zh-CN,zh;q=0.9"}
[16:48:14:128] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"terminal":"etea","userId":"1476420","callLocationCode":"/packageA/pages/formselect/select?"}
[16:48:14:128] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：feign.FeignException$InternalServerError: [500 Internal Server Error] during [POST] to [http://taurus-formSystem/formDefinition/getFormListByIds] [TaurusFormSystemFeignClient#getFormListByFormIds(JSONArray)]: [{"timestamp":1750927694070,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.formSys.mapper.FormDefiniti... (7815 bytes)]，异常信息：[500 Internal Server Error] during [POST] to [http://taurus-formSystem/formDefinition/getFormListByIds] [TaurusFormSystemFeignClient#getFormListByFormIds(JSONArray)]: [{"timestamp":1750927694070,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.formSys.mapper.FormDefiniti... (7815 bytes)]，
[16:56:38:812] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/userForm/getUserFormListByUserId，出现异常:feign.FeignException$InternalServerError；
[16:56:38:816] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"*/*","Referer":"https://servicewechat.com/wxa38e954efdceffbe/devtools/page-frame.html","X-Forwarded-Host":"localhost:8000","User-Agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.06.2503300 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/2","X-Forwarded-Proto":"http","Sec-Fetch-Site":"cross-site","Sec-Fetch-Dest":"empty","Accept-Encoding":"gzip, deflate, br","userId":"1476420","X-Forwarded-Port":"8000","token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJ5aWRhbyIsImV4cCI6MTc1MTUyOTA5OCwiaWF0IjoxNzUwOTI0Mjk4LCJ1c2VySWQiOjE0NzY0MjB9.LbiiqTHg41vMY04m75qoCikMKRsv89_3AEFNML-eT4s","Sec-Fetch-Mode":"cors","host":"*************:8081","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:52302\"","client":"eteamobile","X-Forwarded-For":"0:0:0:0:0:0:0:1","content-type":"application/json;charset=UTF-8","Accept-Language":"zh-CN,zh;q=0.9"}
[16:56:38:816] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"terminal":"etea","userId":"1476420","callLocationCode":"/packageA/pages/formselect/select?"}
[16:56:38:817] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：feign.FeignException$InternalServerError: [500 Internal Server Error] during [POST] to [http://taurus-formSystem/formDefinition/getFormListByIds] [TaurusFormSystemFeignClient#getFormListByFormIds(JSONArray)]: [{"timestamp":1750928198742,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.formSys.mapper.FormDefiniti... (7815 bytes)]，异常信息：[500 Internal Server Error] during [POST] to [http://taurus-formSystem/formDefinition/getFormListByIds] [TaurusFormSystemFeignClient#getFormListByFormIds(JSONArray)]: [{"timestamp":1750928198742,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.formSys.mapper.FormDefiniti... (7815 bytes)]，
[17:27:23:738] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[17:27:27:995] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[17:27:28:091] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[17:27:28:092] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[17:27:28:093] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[17:27:28:093] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[17:27:28:093] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[17:27:28:094] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[17:27:28:094] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[17:27:28:094] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[17:27:28:096] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[17:27:28:097] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[17:27:28:098] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[17:27:28:098] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[17:27:28:178] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[17:27:28:946] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[17:27:28:965] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[17:27:30:298] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[17:27:30:363] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[17:27:30:890] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[17:27:30:903] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[17:27:30:904] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[17:27:30:905] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[17:27:30:906] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[17:27:30:909] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[17:27:30:910] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[17:27:30:911] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[17:27:30:916] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[17:27:39:456] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[17:27:39:475] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[17:27:39:910] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[17:27:42:883] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：2.968秒
[17:27:52:353] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[17:27:52:468] [WARN] - io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:70) - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
[17:27:53:189] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[17:27:54:628] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[17:28:08:092] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[17:28:09:506] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 89 ms to scan 1 urls, producing 3 keys and 6 values 
[17:28:09:685] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 102 ms to scan 1 urls, producing 4 keys and 9 values 
[17:28:09:736] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 43 ms to scan 1 urls, producing 3 keys and 10 values 
[17:28:09:853] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 91 ms to scan 12 urls, producing 0 keys and 0 values 
[17:28:09:877] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 15 ms to scan 1 urls, producing 1 keys and 5 values 
[17:28:09:923] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 23 ms to scan 1 urls, producing 1 keys and 7 values 
[17:28:09:977] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 39 ms to scan 1 urls, producing 2 keys and 8 values 
[17:28:10:057] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 68 ms to scan 12 urls, producing 0 keys and 0 values 
[17:28:16:842] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[17:28:16:843] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[17:28:16:888] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[17:28:16:938] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[17:28:16:974] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[17:28:17:060] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[17:28:17:429] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[17:28:18:499] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 58.13 seconds (JVM running for 59.641)
[17:28:18:697] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[17:30:36:235] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[17:31:04:928] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:55) - 产品：etea 页面：/pages/personal/admin/exam/index?
[17:31:04:930] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:56) - 请求接口：/examination/getExaminationInViewListOfCreater
[17:31:04:931] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:57) - 请求参数：{"companyId":"0","pageIndex":"1","pageSize":"20","refresh":"true","createrId":"1476420","terminal":"etea","callLocationCode":"/pages/personal/admin/exam/index?"}
[17:31:06:590] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750930266499,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[17:31:06:793] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750930266786,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[17:31:06:938] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750930266928,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[17:31:07:117] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750930267109,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[17:31:07:377] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750930267374,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[17:31:07:548] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750930267535,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[17:31:07:677] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750930267672,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[17:31:07:871] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750930267866,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[17:31:08:110] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750930268105,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[17:31:08:329] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750930268324,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[17:31:08:500] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/etea/examination/20241029/20241029104250b091.jpeg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750930268494,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[17:31:08:647] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/etea/examination/20241029/20241029104250b091.jpeg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750930268644,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[17:31:08:661] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750930268658,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[17:31:08:774] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750930268766,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[17:31:08:906] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750930268903,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[17:31:09:045] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750930269040,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[17:31:09:159] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/etea/examination/20240604/2024060417034913eb.jpeg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750930269155,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7093 bytes)]
[17:31:09:393] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/etea/examination/20240604/2024060417034913eb.jpeg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750930269390,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7093 bytes)]
[17:31:09:409] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750930269401,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7093 bytes)]
[17:31:09:782] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750930269778,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7093 bytes)]
[17:31:09:888] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750930269883,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7093 bytes)]
[17:31:10:046] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750930270043,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7093 bytes)]
[17:46:15:791] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[17:46:15:808] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[17:46:15:808] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Destroying Spring FrameworkServlet 'dispatcherServlet'
[17:46:16:157] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[17:46:16:218] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[17:46:16:623] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-1} closing ...
[17:46:16:666] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-1} closed
[17:46:18:840] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[17:46:20:630] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[17:46:20:672] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[17:46:20:673] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[17:46:20:673] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[17:46:20:673] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[17:46:20:673] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[17:46:20:673] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[17:46:20:673] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[17:46:20:673] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[17:46:20:673] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[17:46:20:673] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[17:46:20:673] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[17:46:20:673] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[17:46:20:732] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[17:46:20:870] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[17:46:20:872] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[17:46:21:026] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[17:46:21:031] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[17:46:21:140] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[17:46:21:141] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[17:46:21:141] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[17:46:21:142] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[17:46:21:142] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[17:46:21:143] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[17:46:21:143] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[17:46:21:143] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[17:46:21:144] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[17:46:24:084] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-2} inited
[17:46:24:085] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[17:46:24:090] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.addUnmergedResource(MapperLocationsBuilder.java:216) - 加载未合并Mapper：QuestionMapper.xml
[17:46:24:721] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[17:46:24:865] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[17:46:25:765] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[17:46:29:358] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[17:46:30:003] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[17:46:30:004] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[17:46:30:005] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[17:46:30:064] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[17:46:30:344] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 13.127 seconds (JVM running for 1151.561)
[17:46:30:367] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[17:46:30:490] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[17:46:30:509] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[17:46:30:769] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[17:46:30:818] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[17:46:31:180] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-2} closing ...
[17:46:31:187] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-2} closed
[17:50:49:780] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[17:50:51:192] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[17:50:51:230] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[17:50:51:230] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[17:50:51:231] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[17:50:51:231] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[17:50:51:231] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[17:50:51:231] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[17:50:51:231] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[17:50:51:231] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[17:50:51:231] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[17:50:51:231] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[17:50:51:231] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[17:50:51:231] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[17:50:51:268] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[17:50:51:410] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[17:50:51:411] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[17:50:51:592] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[17:50:51:599] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[17:50:51:734] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[17:50:51:737] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[17:50:51:738] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[17:50:51:739] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[17:50:51:739] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[17:50:51:739] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[17:50:51:740] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[17:50:51:740] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[17:50:51:741] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[17:50:55:249] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-3} inited
[17:50:55:250] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[17:50:55:726] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[17:50:55:883] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[17:50:56:808] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[17:50:59:803] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[17:51:00:420] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[17:51:00:420] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[17:51:00:424] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[17:51:00:462] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[17:51:00:729] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 12.242 seconds (JVM running for 1421.911)
[17:51:00:741] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[17:51:00:831] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[17:51:00:835] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[17:51:01:041] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[17:51:01:067] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[17:51:01:425] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-3} closing ...
[17:51:01:428] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-3} closed
[17:52:34:276] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[17:52:35:203] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[17:52:35:232] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[17:52:35:232] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[17:52:35:232] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[17:52:35:232] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[17:52:35:232] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[17:52:35:232] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[17:52:35:232] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[17:52:35:233] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[17:52:35:233] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[17:52:35:233] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[17:52:35:233] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[17:52:35:233] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[17:52:35:261] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[17:52:35:380] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[17:52:35:381] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[17:52:35:564] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[17:52:35:568] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[17:52:35:717] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[17:52:35:719] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[17:52:35:719] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[17:52:35:720] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[17:52:35:720] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[17:52:35:720] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[17:52:35:721] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[17:52:35:721] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[17:52:35:722] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[17:52:41:167] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-4} inited
[17:52:41:169] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[17:52:41:292] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[17:52:41:833] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.541秒
[17:52:46:973] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[17:52:47:122] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[17:52:48:146] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[17:52:50:332] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[17:52:50:776] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[17:52:50:777] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[17:52:50:778] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[17:52:50:813] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[17:52:51:101] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 17.816 seconds (JVM running for 1532.284)
[17:52:51:114] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[17:53:00:346] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[17:53:05:579] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/userForm/getUserFormListByUserId，出现异常:feign.FeignException$InternalServerError；
[17:53:05:584] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"*/*","Referer":"https://servicewechat.com/wxa38e954efdceffbe/devtools/page-frame.html","X-Forwarded-Host":"localhost:8000","User-Agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.06.2503300 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/1","X-Forwarded-Proto":"http","Sec-Fetch-Site":"cross-site","Sec-Fetch-Dest":"empty","Accept-Encoding":"gzip, deflate, br","userId":"1476420","X-Forwarded-Port":"8000","token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJ5aWRhbyIsImV4cCI6MTc1MTUzNTA0MCwiaWF0IjoxNzUwOTMwMjQwLCJ1c2VySWQiOjE0NzY0MjB9.y9uXmkG9Of6WHNerNZqAtnBxGkGdhK-MR7cNtBEXPRc","Sec-Fetch-Mode":"cors","host":"*************:8081","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:52801\"","client":"eteamobile","X-Forwarded-For":"0:0:0:0:0:0:0:1","content-type":"application/json;charset=UTF-8","Accept-Language":"zh-CN,zh;q=0.9"}
[17:53:05:584] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"terminal":"etea","userId":"1476420","callLocationCode":"/packageA/pages/formselect/select?"}
[17:53:05:584] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：feign.FeignException$InternalServerError: [500 Internal Server Error] during [POST] to [http://taurus-formSystem/formDefinition/getFormListByIds] [TaurusFormSystemFeignClient#getFormListByFormIds(JSONArray)]: [{"timestamp":1750931585500,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.formSys.mapper.FormDefiniti... (7815 bytes)]，异常信息：[500 Internal Server Error] during [POST] to [http://taurus-formSystem/formDefinition/getFormListByIds] [TaurusFormSystemFeignClient#getFormListByFormIds(JSONArray)]: [{"timestamp":1750931585500,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.formSys.mapper.FormDefiniti... (7815 bytes)]，
[21:14:44:983] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[21:14:48:998] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[21:14:49:413] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[21:14:49:417] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[21:14:49:421] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[21:14:49:421] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[21:14:49:421] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[21:14:49:421] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[21:14:49:422] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[21:14:49:422] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[21:14:49:423] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[21:14:49:423] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[21:14:49:423] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[21:14:49:423] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[21:14:49:573] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[21:14:50:416] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[21:14:50:421] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[21:14:51:640] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:14:51:714] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[21:14:52:262] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[21:14:52:277] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[21:14:52:277] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[21:14:52:283] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[21:14:52:284] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[21:14:52:286] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[21:14:52:286] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[21:14:52:289] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[21:14:52:291] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[21:15:00:442] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[21:15:00:457] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[21:15:00:759] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[21:15:02:491] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：1.729秒
[21:15:09:165] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[21:15:09:236] [WARN] - io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:70) - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
[21:15:09:802] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[21:15:11:141] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[21:15:17:254] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[21:15:18:265] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 45 ms to scan 1 urls, producing 3 keys and 6 values 
[21:15:18:343] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 31 ms to scan 1 urls, producing 4 keys and 9 values 
[21:15:18:381] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 26 ms to scan 1 urls, producing 3 keys and 10 values 
[21:15:18:424] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 38 ms to scan 12 urls, producing 0 keys and 0 values 
[21:15:18:435] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
[21:15:18:450] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
[21:15:18:473] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 18 ms to scan 1 urls, producing 2 keys and 8 values 
[21:15:18:507] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 29 ms to scan 12 urls, producing 0 keys and 0 values 
[21:15:19:793] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[21:15:19:793] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[21:15:19:810] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[21:15:19:840] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[21:15:19:863] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[21:15:19:941] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[21:15:20:078] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[21:15:20:437] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 39.055 seconds (JVM running for 40.508)
[21:15:20:480] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[21:15:22:994] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[21:15:29:552] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:55) - 产品：etea 页面：/pages/personal/admin/exam/index?
[21:15:29:556] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:56) - 请求接口：/examination/getExaminationInViewListOfCreater
[21:15:29:558] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:57) - 请求参数：{"companyId":"0","pageIndex":"1","pageSize":"20","refresh":"true","createrId":"1476420","terminal":"etea","callLocationCode":"/pages/personal/admin/exam/index?"}
[21:15:31:500] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750943731341,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[21:15:31:810] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750943731802,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[21:15:32:017] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750943732013,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[21:15:32:177] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750943732169,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[21:15:32:345] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750943732338,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[21:15:32:510] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750943732497,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[21:15:32:645] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750943732641,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[21:15:32:805] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750943732792,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[21:15:32:934] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750943732927,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[21:15:33:100] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750943733097,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[21:15:33:249] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/etea/examination/20241029/20241029104250b091.jpeg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750943733246,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[21:15:33:429] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/etea/examination/20241029/20241029104250b091.jpeg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750943733425,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[21:15:33:446] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750943733444,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[21:15:33:619] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750943733616,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[21:15:33:785] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750943733781,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[21:15:34:034] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750943734025,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7176 bytes)]
[21:15:34:226] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/etea/examination/20240604/2024060417034913eb.jpeg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750943734220,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7093 bytes)]
[21:15:34:413] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/etea/examination/20240604/2024060417034913eb.jpeg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750943734411,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7093 bytes)]
[21:15:34:425] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750943734423,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7093 bytes)]
[21:15:34:624] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750943734620,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7093 bytes)]
[21:15:34:796] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750943734791,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7093 bytes)]
[21:15:34:967] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[500 Internal Server Error] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [{"timestamp":1750943734963,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.oss.mapper.OssRespositoryMa... (7093 bytes)]
[21:17:25:865] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/userForm/getUserFormListByUserId，出现异常:feign.FeignException$InternalServerError；
[21:17:25:871] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"*/*","Referer":"https://servicewechat.com/wxa38e954efdceffbe/devtools/page-frame.html","X-Forwarded-Host":"localhost:8000","User-Agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.06.2503300 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/2","X-Forwarded-Proto":"http","Sec-Fetch-Site":"cross-site","Sec-Fetch-Dest":"empty","Accept-Encoding":"gzip, deflate, br","userId":"1476420","X-Forwarded-Port":"8000","token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJ5aWRhbyIsImV4cCI6MTc1MTU0ODUyNiwiaWF0IjoxNzUwOTQzNzI2LCJ1c2VySWQiOjE0NzY0MjB9.njcRSfW-XA6nBggVqEv-SfU930OaaM4bBOYlayGCE3A","Sec-Fetch-Mode":"cors","host":"*************:8081","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:60046\"","client":"eteamobile","X-Forwarded-For":"0:0:0:0:0:0:0:1","content-type":"application/json;charset=UTF-8","Accept-Language":"zh-CN,zh;q=0.9"}
[21:17:25:874] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"terminal":"etea","userId":"1476420","callLocationCode":"/packageA/pages/formselect/select?"}
[21:17:25:874] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：feign.FeignException$InternalServerError: [500 Internal Server Error] during [POST] to [http://taurus-formSystem/formDefinition/getFormListByIds] [TaurusFormSystemFeignClient#getFormListByFormIds(JSONArray)]: [{"timestamp":1750943845833,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.formSys.mapper.FormDefiniti... (7815 bytes)]，异常信息：[500 Internal Server Error] during [POST] to [http://taurus-formSystem/formDefinition/getFormListByIds] [TaurusFormSystemFeignClient#getFormListByFormIds(JSONArray)]: [{"timestamp":1750943845833,"status":500,"error":"Internal Server Error","trace":"org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.taurus.formSys.mapper.FormDefiniti... (7815 bytes)]，
