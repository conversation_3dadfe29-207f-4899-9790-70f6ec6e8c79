[14:24:12:230] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[14:24:35:504] [WARN] - io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:70) - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
[14:56:48:119] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[14:57:05:528] [WARN] - io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:70) - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
[15:18:38:211] [ERROR] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.translateContactIds(QiyeweixinContactService.java:609) - 调用异步通讯录id转译接口失败: errcode=40007, errmsg=invalid media_id, hint: [1750144718038290338207150], from ip: **************, more info at https://open.work.weixin.qq.com/devtool/query?e=40007
[15:26:22:815] [ERROR] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.translateContactIds(QiyeweixinContactService.java:609) - 调用异步通讯录id转译接口失败: errcode=40007, errmsg=invalid media_id, hint: [1750145102338731679875592], from ip: **************, more info at https://open.work.weixin.qq.com/devtool/query?e=40007
[15:55:01:410] [ERROR] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.translateContactIds(QiyeweixinContactService.java:609) - 调用异步通讯录id转译接口失败: errcode=40007, errmsg=invalid media_id, hint: [1750145386101961349710206], from ip: **************, more info at https://open.work.weixin.qq.com/devtool/query?e=40007
[16:30:17:816] [ERROR] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.translateContactIds(QiyeweixinContactService.java:609) - 调用异步通讯录id转译接口失败: errcode=40007, errmsg=invalid media_id, hint: [1750148786056771463079877], from ip: **************, more info at https://open.work.weixin.qq.com/devtool/query?e=40007
[16:30:19:034] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:33:02:109] [ERROR] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.translateContactIds(QiyeweixinContactService.java:609) - 调用异步通讯录id转译接口失败: errcode=41001, errmsg=access_token missing, hint: [1750149070056773302931279], from ip: **************, more info at https://open.work.weixin.qq.com/devtool/query?e=41001
[16:33:05:009] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:37:24:276] [ERROR] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.translateContactIds(QiyeweixinContactService.java:609) - 调用异步通讯录id转译接口失败: errcode=40007, errmsg=invalid media_id, hint: [1750149280056772295720207], from ip: **************, more info at https://open.work.weixin.qq.com/devtool/query?e=40007
[16:57:09:547] [ERROR] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.translateContactIds(QiyeweixinContactService.java:609) - 调用异步通讯录id转译接口失败: errcode=40007, errmsg=invalid media_id, hint: [1750150629591813256583110], from ip: **************, more info at https://open.work.weixin.qq.com/devtool/query?e=40007
[17:06:16:347] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[17:06:36:405] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[17:10:40:165] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[17:11:27:197] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[17:11:49:670] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[17:12:31:392] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
