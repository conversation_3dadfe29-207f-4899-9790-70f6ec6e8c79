[16:03:14:896] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/pointDetail/exportPointRankOfCompany，出现异常:org.springframework.jdbc.BadSqlGrammarException；
[16:03:14:960] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","X-Forwarded-Host":"localhost:8000","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","X-Forwarded-Proto":"http","Sec-Fetch-Site":"none","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","X-Forwarded-Port":"8000","Sec-Fetch-Mode":"navigate","sec-ch-ua":"\"Microsoft Edge\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","sec-ch-ua-mobile":"?0","Upgrade-Insecure-Requests":"1","sec-ch-ua-platform":"\"macOS\"","host":"*************:8081","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:54454\"","X-Forwarded-For":"0:0:0:0:0:0:0:1","Sec-Fetch-User":"?1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"}
[16:03:14:961] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"companyId":"6040","companyType":"qiyeweixin","userId":"1690046"}
[16:03:14:961] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 't.qkk_qiye_user_id' in 'field list'
### The error may exist in com/taurus/examinationassistant/mapper/PointDetailMapper.java (best guess)
### The error may involve com.taurus.examinationassistant.mapper.PointDetailMapper.getRankOfInterval-Inline
### The error occurred while setting parameters
### SQL: select t.user_id as userId,t.qkk_qiye_user_id as qkkQiyeUserId,sum(if(t.type='S',t.num,0)) as studyPoint,sum(if(t.type='P',t.num,0)) as practicePoint,sum(if(t.type='E',t.num,0)) as examinationPoint,sum(t.num) as totalPoint,t1.name as name,t1.nickname,t1.phone,t1.avatar_url as avatarUrl,t3.depart_name as departmentName,t3.qiyewx_depart_id as qiyewxDepartId,t4.depart_name as parentDepartmentName,t4.qiyewx_depart_id as qiyewxParentDepartId from point_detail t left join user t1 ON t.user_id=t1.id left join user_company t2 ON t.user_id=t2.user_id and t2.company_id=? and t2.enabled=true left join company_department t3 ON t3.id=t2.department_id left join company_department t4 ON t4.id=t3.higher_depart_id where t.company_id=? and t2.user_id is not null   group by t.user_id order by totalPoint desc
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 't.qkk_qiye_user_id' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 't.qkk_qiye_user_id' in 'field list'，异常信息：
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 't.qkk_qiye_user_id' in 'field list'
### The error may exist in com/taurus/examinationassistant/mapper/PointDetailMapper.java (best guess)
### The error may involve com.taurus.examinationassistant.mapper.PointDetailMapper.getRankOfInterval-Inline
### The error occurred while setting parameters
### SQL: select t.user_id as userId,t.qkk_qiye_user_id as qkkQiyeUserId,sum(if(t.type='S',t.num,0)) as studyPoint,sum(if(t.type='P',t.num,0)) as practicePoint,sum(if(t.type='E',t.num,0)) as examinationPoint,sum(t.num) as totalPoint,t1.name as name,t1.nickname,t1.phone,t1.avatar_url as avatarUrl,t3.depart_name as departmentName,t3.qiyewx_depart_id as qiyewxDepartId,t4.depart_name as parentDepartmentName,t4.qiyewx_depart_id as qiyewxParentDepartId from point_detail t left join user t1 ON t.user_id=t1.id left join user_company t2 ON t.user_id=t2.user_id and t2.company_id=? and t2.enabled=true left join company_department t3 ON t3.id=t2.department_id left join company_department t4 ON t4.id=t3.higher_depart_id where t.company_id=? and t2.user_id is not null   group by t.user_id order by totalPoint desc
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 't.qkk_qiye_user_id' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 't.qkk_qiye_user_id' in 'field list'，
[16:05:37:240] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/pointDetail/exportPointRankOfCompany，出现异常:org.springframework.http.converter.HttpMessageNotWritableException；
[16:05:37:251] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","X-Forwarded-Host":"localhost:8000","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","X-Forwarded-Proto":"http","Sec-Fetch-Site":"none","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","X-Forwarded-Port":"8000","Sec-Fetch-Mode":"navigate","sec-ch-ua":"\"Microsoft Edge\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","sec-ch-ua-mobile":"?0","Upgrade-Insecure-Requests":"1","sec-ch-ua-platform":"\"macOS\"","host":"*************:8081","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:54750\"","X-Forwarded-For":"0:0:0:0:0:0:0:1","Sec-Fetch-User":"?1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"}
[16:05:37:253] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"companyId":"6040","companyType":"qiyeweixin","userId":"1690046"}
[16:05:37:253] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：org.springframework.http.converter.HttpMessageNotWritableException: No converter for [class com.taurus.entity.ResponseObject] with preset Content-Type 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'，异常信息：No converter for [class com.taurus.entity.ResponseObject] with preset Content-Type 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'，
[16:05:37:361] [ERROR] - io.undertow.servlet.api.LoggingExceptionHandler.handleThrowable(LoggingExceptionHandler.java:80) - UT005023: Exception handling request to /pointDetail/exportPointRankOfCompany
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is org.springframework.http.converter.HttpMessageNotWritableException: No converter for [class com.taurus.entity.ResponseObject] with preset Content-Type 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:497) ~[jakarta.servlet-api-4.0.4.jar:4.0.4]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:584) ~[jakarta.servlet-api-4.0.4.jar:4.0.4]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.websockets.jsr.JsrWebSocketFilter.doFilter(JsrWebSocketFilter.java:173) ~[undertow-websockets-jsr-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.taurus.examinationassistant.filter.SqlXssFilter.doFilter(SqlXssFilter.java:135) ~[classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SessionRestoringHandler.handleRequest(SessionRestoringHandler.java:119) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1423) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_281]
Caused by: org.springframework.http.converter.HttpMessageNotWritableException: No converter for [class com.taurus.entity.ResponseObject] with preset Content-Type 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:312) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:183) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:135) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.31.jar:5.3.31]
	... 54 more
[16:19:26:479] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/pointDetail/exportPointRankOfCompany，出现异常:java.lang.NullPointerException；
[16:19:26:491] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","X-Forwarded-Host":"localhost:8000","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","X-Forwarded-Proto":"http","Sec-Fetch-Site":"none","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","X-Forwarded-Port":"8000","Sec-Fetch-Mode":"navigate","sec-ch-ua":"\"Microsoft Edge\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","sec-ch-ua-mobile":"?0","Upgrade-Insecure-Requests":"1","sec-ch-ua-platform":"\"macOS\"","host":"*************:8081","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:56781\"","X-Forwarded-For":"0:0:0:0:0:0:0:1","Sec-Fetch-User":"?1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"}
[16:19:26:492] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"companyId":"6040","companyType":"qiyeweixin","userId":"1690046"}
[16:19:26:492] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：java.lang.NullPointerException，异常信息：null，
[17:20:26:851] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[17:20:37:739] [ERROR] - com.taurus.examinationassistant.controller.PointDetailController.exportPointRankOfCompany(PointDetailController.java:172) - userId is empty
[17:20:37:741] [ERROR] - com.taurus.examinationassistant.controller.DownloadProxyController.generateDocument(DownloadProxyController.java:363) - 文件生成失败或为空
[17:22:16:381] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
