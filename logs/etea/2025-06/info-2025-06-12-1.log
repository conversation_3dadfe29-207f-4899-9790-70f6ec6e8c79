[15:06:05:850] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[15:06:09:700] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[15:06:09:784] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[15:06:09:785] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:06:09:785] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:06:09:785] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[15:06:09:785] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[15:06:09:785] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[15:06:09:785] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[15:06:09:785] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:06:09:785] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:06:09:786] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:06:09:786] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:06:09:786] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:06:09:848] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[15:06:10:558] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[15:06:10:590] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[15:06:11:487] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[15:06:11:525] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[15:06:11:915] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[15:06:11:926] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[15:06:11:927] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[15:06:11:929] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[15:06:11:929] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[15:06:11:931] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[15:06:11:931] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[15:06:11:934] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[15:06:11:935] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[15:06:18:952] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[15:06:18:966] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[15:06:19:407] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[15:06:21:470] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：2.06秒
[15:06:29:047] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[15:06:29:117] [WARN] - io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:70) - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
[15:06:29:686] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[15:06:30:862] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[15:06:37:970] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[15:06:38:792] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 49 ms to scan 1 urls, producing 3 keys and 6 values 
[15:06:38:876] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 37 ms to scan 1 urls, producing 4 keys and 9 values 
[15:06:38:921] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 25 ms to scan 1 urls, producing 3 keys and 10 values 
[15:06:38:975] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 47 ms to scan 12 urls, producing 0 keys and 0 values 
[15:06:38:995] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 14 ms to scan 1 urls, producing 1 keys and 5 values 
[15:06:39:010] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
[15:06:39:029] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
[15:06:39:057] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 23 ms to scan 12 urls, producing 0 keys and 0 values 
[15:06:40:443] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[15:06:40:444] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[15:06:40:464] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[15:06:40:488] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[15:06:40:510] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[15:06:40:569] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[15:06:40:703] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[15:06:41:089] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 37.786 seconds (JVM running for 39.312)
[15:06:41:123] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[15:24:53:592] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[15:24:53:615] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[15:24:54:008] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[15:24:54:071] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[15:24:54:509] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-1} closing ...
[15:24:54:591] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-1} closed
[15:24:56:042] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[15:24:57:810] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[15:24:57:867] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[15:24:57:869] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:24:57:869] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:24:57:869] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[15:24:57:869] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[15:24:57:870] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[15:24:57:870] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[15:24:57:870] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:24:57:870] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:24:57:870] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:24:57:871] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:24:57:871] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:24:57:938] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[15:24:58:116] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[15:24:58:116] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[15:24:58:273] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[15:24:58:278] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[15:24:58:370] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[15:24:58:372] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[15:24:58:372] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[15:24:58:373] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[15:24:58:373] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[15:24:58:373] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[15:24:58:374] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[15:24:58:374] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[15:24:58:374] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[15:25:01:542] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-2} inited
[15:25:01:543] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[15:25:01:639] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[15:25:02:135] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.495秒
[15:25:07:232] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[15:25:07:435] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[15:25:08:471] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[15:25:11:111] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[15:25:11:807] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[15:25:11:808] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[15:25:11:809] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[15:25:11:867] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[15:25:12:088] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 17.074 seconds (JVM running for 1150.323)
[15:25:12:110] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[15:37:13:154] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[15:37:13:171] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[15:37:13:541] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[15:37:13:613] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[15:37:13:670] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-2} closing ...
[15:37:13:690] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-2} closed
[15:37:15:294] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[15:37:16:366] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[15:37:16:421] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[15:37:16:422] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:37:16:423] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:37:16:423] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[15:37:16:423] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[15:37:16:423] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[15:37:16:423] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[15:37:16:423] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:37:16:423] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:37:16:423] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:37:16:423] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:37:16:423] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:37:16:461] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[15:37:16:612] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[15:37:16:613] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[15:37:16:749] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[15:37:16:754] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[15:37:16:881] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[15:37:16:885] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[15:37:16:886] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[15:37:16:887] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[15:37:16:887] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[15:37:16:887] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[15:37:16:888] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[15:37:16:889] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[15:37:16:890] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[15:37:19:758] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-3} inited
[15:37:19:759] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[15:37:19:855] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[15:37:20:362] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.505秒
[15:37:25:093] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[15:37:25:292] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[15:37:26:412] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[15:37:28:696] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[15:37:29:216] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[15:37:29:216] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[15:37:29:217] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[15:37:29:250] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[15:37:29:466] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 15.172 seconds (JVM running for 1887.708)
[15:37:29:520] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[15:37:30:300] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[15:37:30:305] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[15:37:30:633] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[15:37:30:740] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[15:37:31:450] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-3} closing ...
[15:37:31:469] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-3} closed
[15:37:32:828] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[15:37:33:642] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[15:37:33:656] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[15:37:33:656] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:37:33:656] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:37:33:656] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[15:37:33:656] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[15:37:33:656] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[15:37:33:657] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[15:37:33:657] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:37:33:657] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:37:33:657] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:37:33:657] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:37:33:657] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:37:33:680] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[15:37:33:770] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[15:37:33:770] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[15:37:33:903] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[15:37:33:908] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[15:37:34:050] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[15:37:34:055] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[15:37:34:055] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[15:37:34:056] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[15:37:34:056] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[15:37:34:056] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[15:37:34:057] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[15:37:34:057] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[15:37:34:057] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[15:37:38:182] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-4} inited
[15:37:38:185] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[15:37:38:279] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[15:37:38:651] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.37秒
[15:37:42:899] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[15:37:43:071] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[15:37:44:648] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[15:37:46:493] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[15:37:47:031] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[15:37:47:032] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[15:37:47:033] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[15:37:47:150] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[15:37:47:457] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 15.49 seconds (JVM running for 1905.7)
[15:37:47:474] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[15:38:28:141] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[15:38:28:153] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[15:38:28:437] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[15:38:28:562] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[15:38:28:936] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-4} closing ...
[15:38:28:952] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-4} closed
[15:38:30:221] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[15:38:31:484] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[15:38:31:505] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[15:38:31:505] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:38:31:505] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:38:31:505] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[15:38:31:505] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[15:38:31:505] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[15:38:31:505] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[15:38:31:506] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:38:31:506] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:38:31:506] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:38:31:506] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:38:31:506] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:38:31:533] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[15:38:31:600] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[15:38:31:600] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[15:38:31:699] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[15:38:31:701] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[15:38:31:766] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[15:38:31:768] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[15:38:31:769] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[15:38:31:769] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[15:38:31:769] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[15:38:31:770] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[15:38:31:770] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[15:38:31:770] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[15:38:31:771] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[15:38:35:192] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-5} inited
[15:38:35:192] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[15:38:35:266] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[15:38:35:664] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.398秒
[15:38:39:760] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[15:38:39:984] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[15:38:40:981] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[15:38:42:410] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[15:38:42:919] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[15:38:42:919] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[15:38:42:920] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[15:38:42:951] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[15:38:43:098] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 13.532 seconds (JVM running for 1961.342)
[15:38:43:100] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[15:45:31:631] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[15:45:31:645] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[15:45:31:862] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[15:45:31:910] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[15:45:32:284] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-5} closing ...
[15:45:32:313] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-5} closed
[15:45:33:945] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[15:45:34:839] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[15:45:34:851] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[15:45:34:851] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:45:34:851] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:45:34:851] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[15:45:34:851] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[15:45:34:851] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[15:45:34:851] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[15:45:34:851] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:45:34:851] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:45:34:851] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:45:34:851] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:45:34:851] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:45:34:872] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[15:45:34:924] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[15:45:34:924] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[15:45:35:008] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[15:45:35:010] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[15:45:35:079] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[15:45:35:080] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[15:45:35:081] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[15:45:35:081] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[15:45:35:081] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[15:45:35:081] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[15:45:35:082] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[15:45:35:082] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[15:45:35:082] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[15:45:38:285] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-6} inited
[15:45:38:286] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[15:45:38:370] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[15:45:38:729] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.359秒
[15:45:43:172] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[15:45:43:338] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[15:45:44:487] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[15:45:46:080] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[15:45:46:611] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[15:45:46:612] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[15:45:46:613] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[15:45:46:662] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[15:45:46:837] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 13.714 seconds (JVM running for 2385.085)
[15:45:46:844] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[15:46:23:488] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[15:46:23:507] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[15:46:23:821] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[15:46:23:928] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[15:46:24:293] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-6} closing ...
[15:46:24:310] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-6} closed
[15:46:25:850] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[15:46:26:872] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[15:46:26:898] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[15:46:26:898] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:46:26:898] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:46:26:898] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[15:46:26:898] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[15:46:26:899] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[15:46:26:899] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[15:46:26:899] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:46:26:899] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:46:26:899] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:46:26:899] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:46:26:899] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:46:26:925] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[15:46:26:986] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[15:46:26:987] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[15:46:27:074] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[15:46:27:077] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[15:46:27:136] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[15:46:27:138] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[15:46:27:139] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[15:46:27:139] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[15:46:27:139] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[15:46:27:139] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[15:46:27:140] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[15:46:27:140] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[15:46:27:140] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[15:46:30:351] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-7} inited
[15:46:30:352] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[15:46:30:462] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[15:46:30:795] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.332秒
[15:46:35:719] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[15:46:35:979] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[15:46:36:873] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[15:46:38:559] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[15:46:39:122] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[15:46:39:122] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[15:46:39:124] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[15:46:39:159] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[15:46:39:403] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 14.166 seconds (JVM running for 2437.652)
[15:46:39:431] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[15:50:25:192] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[15:50:25:213] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[15:50:25:472] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[15:50:25:528] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[15:50:25:905] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-7} closing ...
[15:50:25:937] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-7} closed
[15:50:27:559] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[15:50:28:502] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[15:50:28:515] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[15:50:28:515] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:50:28:515] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:50:28:515] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[15:50:28:515] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[15:50:28:515] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[15:50:28:516] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[15:50:28:516] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:50:28:516] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:50:28:516] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:50:28:516] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:50:28:516] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:50:28:539] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[15:50:28:585] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[15:50:28:585] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[15:50:28:664] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[15:50:28:667] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[15:50:28:720] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[15:50:28:722] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[15:50:28:722] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[15:50:28:722] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[15:50:28:722] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[15:50:28:723] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[15:50:28:723] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[15:50:28:723] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[15:50:28:724] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[15:50:32:067] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-8} inited
[15:50:32:074] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[15:50:32:190] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[15:50:32:599] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.408秒
[15:50:37:038] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[15:50:37:207] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[15:50:38:271] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[15:50:39:617] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[15:50:40:187] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[15:50:40:188] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[15:50:40:189] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[15:50:40:259] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[15:50:40:449] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 13.67 seconds (JVM running for 2678.674)
[15:50:40:461] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[15:51:51:002] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[15:51:51:027] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[15:51:51:423] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[15:51:51:489] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[15:51:51:865] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-8} closing ...
[15:51:51:878] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-8} closed
[15:51:53:804] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[15:51:54:653] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[15:51:54:663] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[15:51:54:663] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:51:54:663] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:51:54:663] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[15:51:54:664] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[15:51:54:664] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[15:51:54:664] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[15:51:54:664] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:51:54:664] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:51:54:664] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:51:54:664] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:51:54:664] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:51:54:685] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[15:51:54:735] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[15:51:54:735] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[15:51:54:817] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[15:51:54:820] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[15:51:54:870] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[15:51:54:872] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[15:51:54:872] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[15:51:54:872] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[15:51:54:873] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[15:51:54:873] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[15:51:54:873] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[15:51:54:874] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[15:51:54:874] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[15:51:58:183] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-9} inited
[15:51:58:183] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[15:51:58:259] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[15:51:58:611] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.351秒
[15:52:04:234] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[15:52:04:482] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[15:52:05:729] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[15:52:07:163] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[15:52:07:759] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[15:52:07:760] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[15:52:07:761] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[15:52:07:813] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[15:52:08:001] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 14.977 seconds (JVM running for 2766.227)
[15:52:08:011] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[15:52:30:898] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[15:52:30:913] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[15:52:31:160] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[15:52:31:214] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[15:52:31:565] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-9} closing ...
[15:52:31:586] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-9} closed
[15:52:33:403] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[15:52:34:438] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[15:52:34:453] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[15:52:34:453] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:52:34:453] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:52:34:453] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[15:52:34:453] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[15:52:34:453] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[15:52:34:453] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[15:52:34:453] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:52:34:453] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:52:34:453] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:52:34:454] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:52:34:454] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:52:34:478] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[15:52:34:537] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[15:52:34:538] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[15:52:34:612] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[15:52:34:614] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[15:52:34:669] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[15:52:34:671] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[15:52:34:672] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[15:52:34:672] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[15:52:34:672] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[15:52:34:673] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[15:52:34:673] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[15:52:34:673] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[15:52:34:674] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[15:52:37:886] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-10} inited
[15:52:37:887] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[15:52:37:966] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[15:52:38:307] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.341秒
[15:52:42:747] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[15:52:43:009] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[15:52:44:048] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[15:52:45:493] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[15:52:46:072] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[15:52:46:072] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[15:52:46:073] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[15:52:46:112] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[15:52:46:332] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 13.801 seconds (JVM running for 2804.557)
[15:52:46:340] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[15:53:14:247] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[15:53:14:266] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[15:53:14:480] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[15:53:14:533] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[15:53:14:891] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-10} closing ...
[15:53:14:920] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-10} closed
[15:53:16:801] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[15:53:17:843] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[15:53:17:857] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[15:53:17:857] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:53:17:857] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:53:17:857] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[15:53:17:857] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[15:53:17:858] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[15:53:17:858] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[15:53:17:858] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:53:17:858] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:53:17:858] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:53:17:858] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:53:17:858] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:53:17:883] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[15:53:17:939] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[15:53:17:940] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[15:53:18:006] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[15:53:18:009] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[15:53:18:062] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[15:53:18:064] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[15:53:18:064] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[15:53:18:064] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[15:53:18:065] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[15:53:18:065] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[15:53:18:065] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[15:53:18:066] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[15:53:18:066] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[15:53:21:463] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-11} inited
[15:53:21:464] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[15:53:21:554] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[15:53:21:930] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.374秒
[15:53:26:668] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[15:53:26:869] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[15:53:28:033] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[15:53:29:577] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[15:53:30:085] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[15:53:30:086] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[15:53:30:087] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[15:53:30:112] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[15:53:30:422] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 14.437 seconds (JVM running for 2848.647)
[15:53:30:426] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[15:56:15:004] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[15:56:15:025] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[15:56:15:218] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[15:56:15:283] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[15:56:15:648] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-11} closing ...
[15:56:15:678] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-11} closed
[15:56:17:620] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[15:56:18:583] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[15:56:18:601] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[15:56:18:601] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:56:18:601] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:56:18:601] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[15:56:18:601] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[15:56:18:601] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[15:56:18:601] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[15:56:18:601] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:56:18:602] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:56:18:602] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:56:18:602] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:56:18:602] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:56:18:630] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[15:56:18:694] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[15:56:18:694] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[15:56:18:784] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[15:56:18:787] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[15:56:18:865] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[15:56:18:867] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[15:56:18:867] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[15:56:18:868] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[15:56:18:868] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[15:56:18:868] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[15:56:18:868] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[15:56:18:869] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[15:56:18:869] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[15:56:21:902] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-12} inited
[15:56:21:904] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[15:56:21:986] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[15:56:22:381] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.394秒
[15:56:27:122] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[15:56:27:367] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[15:56:28:451] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[15:56:30:018] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[15:56:30:573] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[15:56:30:573] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[15:56:30:575] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[15:56:30:605] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[15:56:30:774] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 13.935 seconds (JVM running for 3029.001)
[15:56:30:778] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[15:57:02:000] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[15:57:02:013] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[15:57:02:187] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[15:57:02:237] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[15:57:02:594] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-12} closing ...
[15:57:02:607] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-12} closed
[15:57:04:859] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[15:57:05:897] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[15:57:05:913] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[15:57:05:913] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:57:05:913] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:57:05:913] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[15:57:05:913] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[15:57:05:913] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[15:57:05:913] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[15:57:05:913] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:57:05:913] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:57:05:913] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:57:05:914] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:57:05:914] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:57:05:939] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[15:57:05:995] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[15:57:05:995] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[15:57:06:076] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[15:57:06:079] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[15:57:06:136] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[15:57:06:138] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[15:57:06:138] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[15:57:06:139] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[15:57:06:139] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[15:57:06:140] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[15:57:06:140] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[15:57:06:140] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[15:57:06:141] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[15:57:08:906] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-13} inited
[15:57:08:907] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[15:57:08:984] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[15:57:09:355] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.37秒
[15:57:15:198] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[15:57:15:550] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[15:57:16:467] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[15:57:18:088] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[15:57:18:599] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[15:57:18:599] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[15:57:18:600] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[15:57:18:629] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[15:57:18:772] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 14.863 seconds (JVM running for 3076.998)
[15:57:18:779] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[15:57:19:595] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[15:57:19:625] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[15:57:19:919] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[15:57:20:004] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[15:57:20:667] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-13} closing ...
[15:57:20:687] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-13} closed
[15:57:22:073] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[15:57:23:020] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[15:57:23:040] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[15:57:23:041] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:57:23:041] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:57:23:041] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[15:57:23:041] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[15:57:23:041] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[15:57:23:041] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[15:57:23:041] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:57:23:041] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:57:23:041] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:57:23:041] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:57:23:041] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:57:23:066] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[15:57:23:116] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[15:57:23:117] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[15:57:23:184] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[15:57:23:189] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[15:57:23:277] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[15:57:23:279] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[15:57:23:280] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[15:57:23:280] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[15:57:23:280] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[15:57:23:281] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[15:57:23:281] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[15:57:23:281] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[15:57:23:282] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[15:57:26:377] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-14} inited
[15:57:26:377] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[15:57:26:444] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[15:57:26:836] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.391秒
[15:57:31:983] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[15:57:32:308] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[15:57:33:223] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[15:57:34:982] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[15:57:35:553] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[15:57:35:553] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[15:57:35:554] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[15:57:35:582] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[15:57:35:771] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 14.382 seconds (JVM running for 3093.998)
[15:57:35:776] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[15:58:01:615] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[15:58:01:634] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[15:58:01:876] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[15:58:01:908] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[15:58:02:272] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-14} closing ...
[15:58:02:307] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-14} closed
[15:58:04:337] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[15:58:05:215] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[15:58:05:226] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[15:58:05:226] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:58:05:226] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:58:05:226] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[15:58:05:226] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[15:58:05:226] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[15:58:05:226] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[15:58:05:226] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:58:05:226] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:58:05:226] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:58:05:227] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:58:05:227] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:58:05:249] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[15:58:05:302] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[15:58:05:302] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[15:58:05:381] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[15:58:05:383] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[15:58:05:434] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[15:58:05:440] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[15:58:05:440] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[15:58:05:440] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[15:58:05:441] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[15:58:05:441] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[15:58:05:441] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[15:58:05:442] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[15:58:05:442] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[15:58:09:177] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-15} inited
[15:58:09:180] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[15:58:09:337] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[15:58:09:823] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.485秒
[15:58:14:519] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[15:58:14:832] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[15:58:16:116] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[15:58:17:521] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[15:58:18:112] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[15:58:18:112] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[15:58:18:114] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[15:58:18:156] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[15:58:18:400] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 14.657 seconds (JVM running for 3136.628)
[15:58:18:412] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[15:58:19:028] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[15:58:19:048] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[15:58:19:266] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[15:58:19:277] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[15:58:19:924] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-15} closing ...
[15:58:19:930] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-15} closed
[15:58:21:976] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[15:58:22:803] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[15:58:22:817] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[15:58:22:817] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:58:22:817] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:58:22:817] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[15:58:22:818] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[15:58:22:818] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[15:58:22:818] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[15:58:22:818] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:58:22:818] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:58:22:818] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:58:22:818] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:58:22:818] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:58:22:843] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[15:58:22:894] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[15:58:22:895] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[15:58:22:962] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[15:58:22:964] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[15:58:23:023] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[15:58:23:026] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[15:58:23:026] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[15:58:23:026] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[15:58:23:027] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[15:58:23:027] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[15:58:23:027] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[15:58:23:028] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[15:58:23:029] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[15:58:26:243] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-16} inited
[15:58:26:244] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[15:58:26:334] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[15:58:26:710] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.374秒
[15:58:31:581] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[15:58:31:907] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[15:58:32:869] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[15:58:34:338] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[15:58:34:853] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[15:58:34:854] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[15:58:34:855] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[15:58:34:887] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[15:58:35:065] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 13.813 seconds (JVM running for 3153.293)
[15:58:35:071] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[16:01:37:991] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[16:01:38:019] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[16:01:38:309] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[16:01:38:331] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[16:01:38:701] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-16} closing ...
[16:01:38:713] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-16} closed
[16:01:42:166] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[16:01:43:098] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[16:01:43:113] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[16:01:43:114] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:01:43:114] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:01:43:114] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[16:01:43:114] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[16:01:43:114] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[16:01:43:114] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[16:01:43:114] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:01:43:114] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:01:43:115] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:01:43:115] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:01:43:115] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:01:43:144] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[16:01:43:208] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[16:01:43:208] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[16:01:43:454] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:01:43:457] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[16:01:43:517] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[16:01:43:520] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[16:01:43:520] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[16:01:43:520] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[16:01:43:520] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[16:01:43:521] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[16:01:43:521] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[16:01:43:522] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[16:01:43:522] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[16:01:46:443] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-17} inited
[16:01:46:445] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[16:01:46:554] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[16:01:47:000] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.444秒
[16:01:52:102] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[16:01:52:310] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[16:01:53:388] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[16:01:54:902] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[16:01:55:621] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[16:01:55:622] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[16:01:55:623] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[16:01:55:665] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[16:01:55:836] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 15.042 seconds (JVM running for 3354.066)
[16:01:55:843] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[16:03:13:818] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[16:03:14:896] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/pointDetail/exportPointRankOfCompany，出现异常:org.springframework.jdbc.BadSqlGrammarException；
[16:03:14:960] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","X-Forwarded-Host":"localhost:8000","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","X-Forwarded-Proto":"http","Sec-Fetch-Site":"none","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","X-Forwarded-Port":"8000","Sec-Fetch-Mode":"navigate","sec-ch-ua":"\"Microsoft Edge\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","sec-ch-ua-mobile":"?0","Upgrade-Insecure-Requests":"1","sec-ch-ua-platform":"\"macOS\"","host":"*************:8081","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:54454\"","X-Forwarded-For":"0:0:0:0:0:0:0:1","Sec-Fetch-User":"?1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"}
[16:03:14:961] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"companyId":"6040","companyType":"qiyeweixin","userId":"1690046"}
[16:03:14:961] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 't.qkk_qiye_user_id' in 'field list'
### The error may exist in com/taurus/examinationassistant/mapper/PointDetailMapper.java (best guess)
### The error may involve com.taurus.examinationassistant.mapper.PointDetailMapper.getRankOfInterval-Inline
### The error occurred while setting parameters
### SQL: select t.user_id as userId,t.qkk_qiye_user_id as qkkQiyeUserId,sum(if(t.type='S',t.num,0)) as studyPoint,sum(if(t.type='P',t.num,0)) as practicePoint,sum(if(t.type='E',t.num,0)) as examinationPoint,sum(t.num) as totalPoint,t1.name as name,t1.nickname,t1.phone,t1.avatar_url as avatarUrl,t3.depart_name as departmentName,t3.qiyewx_depart_id as qiyewxDepartId,t4.depart_name as parentDepartmentName,t4.qiyewx_depart_id as qiyewxParentDepartId from point_detail t left join user t1 ON t.user_id=t1.id left join user_company t2 ON t.user_id=t2.user_id and t2.company_id=? and t2.enabled=true left join company_department t3 ON t3.id=t2.department_id left join company_department t4 ON t4.id=t3.higher_depart_id where t.company_id=? and t2.user_id is not null   group by t.user_id order by totalPoint desc
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 't.qkk_qiye_user_id' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 't.qkk_qiye_user_id' in 'field list'，异常信息：
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 't.qkk_qiye_user_id' in 'field list'
### The error may exist in com/taurus/examinationassistant/mapper/PointDetailMapper.java (best guess)
### The error may involve com.taurus.examinationassistant.mapper.PointDetailMapper.getRankOfInterval-Inline
### The error occurred while setting parameters
### SQL: select t.user_id as userId,t.qkk_qiye_user_id as qkkQiyeUserId,sum(if(t.type='S',t.num,0)) as studyPoint,sum(if(t.type='P',t.num,0)) as practicePoint,sum(if(t.type='E',t.num,0)) as examinationPoint,sum(t.num) as totalPoint,t1.name as name,t1.nickname,t1.phone,t1.avatar_url as avatarUrl,t3.depart_name as departmentName,t3.qiyewx_depart_id as qiyewxDepartId,t4.depart_name as parentDepartmentName,t4.qiyewx_depart_id as qiyewxParentDepartId from point_detail t left join user t1 ON t.user_id=t1.id left join user_company t2 ON t.user_id=t2.user_id and t2.company_id=? and t2.enabled=true left join company_department t3 ON t3.id=t2.department_id left join company_department t4 ON t4.id=t3.higher_depart_id where t.company_id=? and t2.user_id is not null   group by t.user_id order by totalPoint desc
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 't.qkk_qiye_user_id' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 't.qkk_qiye_user_id' in 'field list'，
[16:04:57:072] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[16:04:57:088] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[16:04:57:089] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Destroying Spring FrameworkServlet 'dispatcherServlet'
[16:04:57:306] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[16:04:57:348] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[16:04:57:719] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-17} closing ...
[16:04:57:728] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-17} closed
[16:05:00:173] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[16:05:01:456] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[16:05:01:503] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[16:05:01:503] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:05:01:503] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:05:01:504] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[16:05:01:505] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[16:05:01:505] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[16:05:01:505] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[16:05:01:506] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:05:01:506] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:05:01:506] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:05:01:507] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:05:01:507] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:05:01:543] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[16:05:01:646] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[16:05:01:647] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[16:05:01:736] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:05:01:740] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[16:05:01:821] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[16:05:01:823] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[16:05:01:824] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[16:05:01:824] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[16:05:01:824] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[16:05:01:825] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[16:05:01:825] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[16:05:01:826] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[16:05:01:826] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[16:05:04:772] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-18} inited
[16:05:04:773] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[16:05:04:847] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[16:05:05:244] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.395秒
[16:05:09:919] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[16:05:10:196] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[16:05:11:211] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[16:05:12:768] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[16:05:13:318] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[16:05:13:319] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[16:05:13:320] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[16:05:13:362] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[16:05:13:631] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 14.509 seconds (JVM running for 3551.862)
[16:05:13:638] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[16:05:14:289] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[16:05:14:300] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[16:05:14:544] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[16:05:14:568] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[16:05:15:211] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-18} closing ...
[16:05:15:230] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-18} closed
[16:05:17:433] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[16:05:18:287] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[16:05:18:304] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[16:05:18:304] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:05:18:304] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:05:18:304] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[16:05:18:304] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[16:05:18:304] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[16:05:18:304] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[16:05:18:304] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:05:18:304] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:05:18:304] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:05:18:304] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:05:18:304] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:05:18:330] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[16:05:18:387] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[16:05:18:388] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[16:05:18:456] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:05:18:458] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[16:05:18:521] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[16:05:18:523] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[16:05:18:523] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[16:05:18:524] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[16:05:18:524] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[16:05:18:524] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[16:05:18:524] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[16:05:18:525] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[16:05:18:525] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[16:05:21:352] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-19} inited
[16:05:21:353] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[16:05:21:438] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[16:05:21:732] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.293秒
[16:05:26:576] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[16:05:26:818] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[16:05:28:167] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[16:05:29:563] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[16:05:30:084] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[16:05:30:084] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[16:05:30:085] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[16:05:30:129] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[16:05:30:317] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 13.565 seconds (JVM running for 3568.548)
[16:05:30:323] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[16:05:35:048] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[16:05:37:240] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/pointDetail/exportPointRankOfCompany，出现异常:org.springframework.http.converter.HttpMessageNotWritableException；
[16:05:37:251] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","X-Forwarded-Host":"localhost:8000","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","X-Forwarded-Proto":"http","Sec-Fetch-Site":"none","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","X-Forwarded-Port":"8000","Sec-Fetch-Mode":"navigate","sec-ch-ua":"\"Microsoft Edge\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","sec-ch-ua-mobile":"?0","Upgrade-Insecure-Requests":"1","sec-ch-ua-platform":"\"macOS\"","host":"*************:8081","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:54750\"","X-Forwarded-For":"0:0:0:0:0:0:0:1","Sec-Fetch-User":"?1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"}
[16:05:37:253] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"companyId":"6040","companyType":"qiyeweixin","userId":"1690046"}
[16:05:37:253] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：org.springframework.http.converter.HttpMessageNotWritableException: No converter for [class com.taurus.entity.ResponseObject] with preset Content-Type 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'，异常信息：No converter for [class com.taurus.entity.ResponseObject] with preset Content-Type 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'，
[16:05:37:361] [ERROR] - io.undertow.servlet.api.LoggingExceptionHandler.handleThrowable(LoggingExceptionHandler.java:80) - UT005023: Exception handling request to /pointDetail/exportPointRankOfCompany
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is org.springframework.http.converter.HttpMessageNotWritableException: No converter for [class com.taurus.entity.ResponseObject] with preset Content-Type 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:497) ~[jakarta.servlet-api-4.0.4.jar:4.0.4]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:584) ~[jakarta.servlet-api-4.0.4.jar:4.0.4]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.websockets.jsr.JsrWebSocketFilter.doFilter(JsrWebSocketFilter.java:173) ~[undertow-websockets-jsr-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.taurus.examinationassistant.filter.SqlXssFilter.doFilter(SqlXssFilter.java:135) ~[classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SessionRestoringHandler.handleRequest(SessionRestoringHandler.java:119) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1423) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_281]
Caused by: org.springframework.http.converter.HttpMessageNotWritableException: No converter for [class com.taurus.entity.ResponseObject] with preset Content-Type 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:312) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:183) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:135) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.31.jar:5.3.31]
	... 54 more
[16:08:51:886] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[16:08:51:903] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[16:08:51:903] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Destroying Spring FrameworkServlet 'dispatcherServlet'
[16:08:52:157] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[16:08:52:239] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[16:08:52:618] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-19} closing ...
[16:08:52:639] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-19} closed
[16:08:56:327] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[16:08:57:338] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[16:08:57:350] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[16:08:57:351] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:08:57:352] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:08:57:352] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[16:08:57:353] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[16:08:57:353] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[16:08:57:353] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[16:08:57:353] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:08:57:353] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:08:57:353] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:08:57:354] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:08:57:354] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:08:57:379] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[16:08:57:432] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[16:08:57:433] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[16:08:57:524] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:08:57:526] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[16:08:57:591] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[16:08:57:593] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[16:08:57:593] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[16:08:57:593] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[16:08:57:594] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[16:08:57:594] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[16:08:57:594] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[16:08:57:595] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[16:08:57:595] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[16:09:00:768] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-20} inited
[16:09:00:769] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[16:09:00:851] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[16:09:01:287] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.435秒
[16:09:06:115] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[16:09:06:350] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[16:09:07:393] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[16:09:09:036] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[16:09:09:703] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[16:09:09:703] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[16:09:09:704] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[16:09:09:730] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[16:09:09:942] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 15.325 seconds (JVM running for 3788.174)
[16:09:09:946] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[16:09:12:618] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[16:09:12:638] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[16:09:12:844] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[16:09:12:914] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[16:09:13:574] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-20} closing ...
[16:09:13:594] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-20} closed
[16:09:16:193] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[16:09:17:303] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[16:09:17:343] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[16:09:17:343] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:09:17:343] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:09:17:343] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[16:09:17:343] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[16:09:17:343] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[16:09:17:344] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[16:09:17:344] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:09:17:344] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:09:17:344] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:09:17:344] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:09:17:344] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:09:17:374] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[16:09:17:435] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[16:09:17:437] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[16:09:17:532] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:09:17:535] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[16:09:17:622] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[16:09:17:624] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[16:09:17:625] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[16:09:17:625] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[16:09:17:625] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[16:09:17:626] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[16:09:17:626] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[16:09:17:626] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[16:09:17:627] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[16:09:21:116] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-21} inited
[16:09:21:117] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[16:09:21:215] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[16:09:21:589] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.373秒
[16:09:26:560] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[16:09:26:743] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[16:09:29:610] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[16:09:31:661] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[16:09:32:192] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[16:09:32:192] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[16:09:32:194] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[16:09:32:230] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[16:09:32:374] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 16.831 seconds (JVM running for 3810.606)
[16:09:32:379] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[16:09:36:292] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[16:09:36:305] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[16:09:36:598] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[16:09:36:628] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[16:09:37:273] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-21} closing ...
[16:09:37:291] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-21} closed
[16:09:38:849] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[16:09:39:662] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[16:09:39:678] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[16:09:39:678] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:09:39:678] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:09:39:678] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[16:09:39:679] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[16:09:39:679] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[16:09:39:679] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[16:09:39:679] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:09:39:679] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:09:39:679] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:09:39:679] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:09:39:679] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:09:39:706] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[16:09:39:759] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[16:09:39:760] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[16:09:39:837] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:09:39:839] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[16:09:39:909] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[16:09:39:911] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[16:09:39:911] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[16:09:39:911] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[16:09:39:911] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[16:09:39:912] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[16:09:39:912] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[16:09:39:913] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[16:09:39:913] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[16:09:42:981] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-22} inited
[16:09:42:982] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[16:09:43:051] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[16:09:43:462] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.407秒
[16:09:48:860] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[16:09:49:108] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[16:09:50:354] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[16:09:52:117] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[16:09:52:636] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[16:09:52:636] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[16:09:52:639] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[16:09:52:666] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[16:09:52:958] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 14.738 seconds (JVM running for 3831.19)
[16:09:52:961] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[16:09:53:585] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[16:09:53:604] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[16:09:53:883] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[16:09:53:910] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[16:09:54:568] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-22} closing ...
[16:09:54:598] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-22} closed
[16:09:57:042] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[16:09:57:900] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[16:09:57:911] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[16:09:57:911] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:09:57:911] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:09:57:911] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[16:09:57:912] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[16:09:57:912] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[16:09:57:912] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[16:09:57:912] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:09:57:912] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:09:57:912] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:09:57:912] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:09:57:912] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:09:57:935] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[16:09:57:986] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[16:09:57:987] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[16:09:58:048] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:09:58:050] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[16:09:58:107] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[16:09:58:110] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[16:09:58:110] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[16:09:58:110] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[16:09:58:110] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[16:09:58:111] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[16:09:58:111] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[16:09:58:111] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[16:09:58:112] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[16:10:00:863] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-23} inited
[16:10:00:864] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[16:10:00:967] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[16:10:01:343] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.375秒
[16:10:06:453] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[16:10:06:649] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[16:10:07:490] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[16:10:08:864] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[16:10:09:416] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[16:10:09:417] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[16:10:09:418] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[16:10:09:451] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[16:10:09:610] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 13.195 seconds (JVM running for 3847.843)
[16:10:09:618] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[16:10:55:818] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[16:10:55:836] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[16:10:56:043] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[16:10:56:069] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[16:10:56:417] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-23} closing ...
[16:10:56:454] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-23} closed
[16:10:59:501] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[16:11:00:760] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[16:11:00:777] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[16:11:00:778] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:11:00:778] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:11:00:778] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[16:11:00:778] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[16:11:00:778] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[16:11:00:778] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[16:11:00:778] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:11:00:778] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:11:00:778] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:11:00:778] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:11:00:778] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:11:00:805] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[16:11:00:858] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[16:11:00:859] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[16:11:00:941] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:11:00:943] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[16:11:01:004] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[16:11:01:006] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[16:11:01:007] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[16:11:01:007] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[16:11:01:007] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[16:11:01:008] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[16:11:01:008] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[16:11:01:008] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[16:11:01:009] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[16:11:03:982] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-24} inited
[16:11:03:982] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[16:11:04:068] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[16:11:04:460] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.391秒
[16:11:09:785] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[16:11:09:985] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[16:11:10:841] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[16:11:12:407] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[16:11:12:968] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[16:11:12:968] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[16:11:12:969] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[16:11:13:009] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[16:11:13:172] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 14.514 seconds (JVM running for 3911.405)
[16:11:13:181] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[16:11:13:882] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[16:11:13:900] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[16:11:14:147] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[16:11:14:169] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[16:11:14:815] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-24} closing ...
[16:11:14:845] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-24} closed
[16:11:17:459] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[16:11:18:249] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[16:11:18:259] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[16:11:18:259] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:11:18:259] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:11:18:259] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[16:11:18:259] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[16:11:18:260] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[16:11:18:260] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[16:11:18:260] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:11:18:260] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:11:18:260] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:11:18:260] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:11:18:260] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:11:18:284] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[16:11:18:332] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[16:11:18:332] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[16:11:18:394] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:11:18:396] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[16:11:18:453] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[16:11:18:455] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[16:11:18:456] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[16:11:18:456] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[16:11:18:456] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[16:11:18:456] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[16:11:18:457] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[16:11:18:457] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[16:11:18:457] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[16:11:21:222] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-25} inited
[16:11:21:223] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[16:11:21:304] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[16:11:21:600] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.295秒
[16:11:26:904] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[16:11:27:099] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[16:11:28:043] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[16:11:29:619] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[16:11:30:168] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[16:11:30:169] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[16:11:30:171] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[16:11:30:213] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[16:11:30:440] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 13.605 seconds (JVM running for 3928.673)
[16:11:30:453] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[16:11:51:066] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[16:11:51:080] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[16:11:51:344] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[16:11:51:369] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[16:11:51:723] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-25} closing ...
[16:11:51:735] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-25} closed
[16:11:54:699] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[16:11:55:640] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[16:11:55:655] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[16:11:55:655] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:11:55:655] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:11:55:655] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[16:11:55:655] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[16:11:55:655] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[16:11:55:655] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[16:11:55:655] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:11:55:655] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:11:55:655] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:11:55:655] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:11:55:655] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:11:55:681] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[16:11:55:739] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[16:11:55:740] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[16:11:55:810] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:11:55:812] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[16:11:55:865] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[16:11:55:867] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[16:11:55:868] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[16:11:55:868] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[16:11:55:868] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[16:11:55:868] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[16:11:55:869] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[16:11:55:869] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[16:11:55:870] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[16:11:59:147] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-26} inited
[16:11:59:148] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[16:11:59:237] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[16:11:59:740] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.502秒
[16:12:04:623] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[16:12:04:969] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[16:12:06:119] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[16:12:07:642] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[16:12:08:163] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[16:12:08:163] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[16:12:08:165] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[16:12:08:214] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[16:12:08:554] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 14.657 seconds (JVM running for 3966.787)
[16:12:08:565] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[16:12:28:053] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[16:12:28:080] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[16:12:28:357] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[16:12:28:385] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[16:12:28:747] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-26} closing ...
[16:12:28:776] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-26} closed
[16:12:32:082] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[16:12:33:051] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[16:12:33:066] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[16:12:33:066] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:12:33:066] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:12:33:066] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[16:12:33:066] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[16:12:33:066] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[16:12:33:066] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[16:12:33:067] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:12:33:067] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:12:33:067] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:12:33:067] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:12:33:067] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:12:33:093] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[16:12:33:154] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[16:12:33:154] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[16:12:33:235] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:12:33:238] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[16:12:33:308] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[16:12:33:310] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[16:12:33:310] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[16:12:33:311] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[16:12:33:311] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[16:12:33:312] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[16:12:33:312] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[16:12:33:313] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[16:12:33:313] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[16:12:36:447] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-27} inited
[16:12:36:448] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[16:12:36:540] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[16:12:36:902] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.36秒
[16:12:42:186] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[16:12:42:517] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[16:12:43:585] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[16:12:45:171] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[16:12:45:709] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[16:12:45:710] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[16:12:45:711] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[16:12:45:751] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[16:12:45:931] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 14.568 seconds (JVM running for 4004.165)
[16:12:45:940] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[16:13:15:173] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[16:13:15:190] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[16:13:15:361] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[16:13:15:369] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[16:13:15:700] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-27} closing ...
[16:13:15:720] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-27} closed
[16:13:18:620] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[16:13:19:479] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[16:13:19:496] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[16:13:19:497] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:13:19:497] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:13:19:497] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[16:13:19:497] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[16:13:19:497] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[16:13:19:497] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[16:13:19:497] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:13:19:497] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:13:19:498] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:13:19:498] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:13:19:498] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:13:19:524] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[16:13:19:579] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[16:13:19:580] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[16:13:19:752] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:13:19:754] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[16:13:19:811] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[16:13:19:814] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[16:13:19:814] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[16:13:19:814] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[16:13:19:815] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[16:13:19:815] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[16:13:19:815] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[16:13:19:816] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[16:13:19:817] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[16:13:24:078] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-28} inited
[16:13:24:079] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[16:13:24:205] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[16:13:24:632] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.426秒
[16:13:29:782] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[16:13:30:132] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[16:13:31:612] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[16:13:33:431] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[16:13:33:961] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[16:13:33:961] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[16:13:33:963] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[16:13:34:115] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[16:13:34:273] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 16.416 seconds (JVM running for 4052.507)
[16:13:34:280] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[16:13:35:011] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[16:13:35:028] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[16:13:35:205] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[16:13:35:212] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[16:13:35:860] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-28} closing ...
[16:13:35:892] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-28} closed
[16:13:38:824] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[16:13:39:773] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[16:13:39:788] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[16:13:39:788] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:13:39:788] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:13:39:788] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[16:13:39:788] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[16:13:39:788] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[16:13:39:788] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[16:13:39:788] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:13:39:788] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:13:39:789] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:13:39:789] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:13:39:789] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:13:39:815] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[16:13:39:872] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[16:13:39:872] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[16:13:39:952] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:13:39:955] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[16:13:40:014] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[16:13:40:016] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[16:13:40:016] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[16:13:40:016] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[16:13:40:017] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[16:13:40:017] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[16:13:40:017] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[16:13:40:018] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[16:13:40:018] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[16:13:42:769] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-29} inited
[16:13:42:770] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[16:13:42:862] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[16:13:43:227] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.364秒
[16:13:48:150] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[16:13:48:558] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[16:13:49:444] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[16:13:51:010] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[16:13:51:529] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[16:13:51:529] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[16:13:51:530] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[16:13:51:560] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[16:13:51:743] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 13.648 seconds (JVM running for 4069.977)
[16:13:51:749] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[16:17:11:904] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[16:17:11:927] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[16:17:12:241] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[16:17:12:273] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[16:17:12:622] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-29} closing ...
[16:17:12:652] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-29} closed
[16:17:15:401] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[16:17:16:639] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[16:17:16:657] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[16:17:16:657] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:17:16:657] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:17:16:657] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[16:17:16:657] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[16:17:16:657] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[16:17:16:658] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[16:17:16:658] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:17:16:658] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:17:16:658] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:17:16:658] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:17:16:658] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:17:16:684] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[16:17:16:746] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[16:17:16:746] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[16:17:16:848] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:17:16:850] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[16:17:16:914] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[16:17:16:917] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[16:17:16:917] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[16:17:16:917] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[16:17:16:917] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[16:17:16:918] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[16:17:16:918] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[16:17:16:918] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[16:17:16:919] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[16:17:20:357] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-30} inited
[16:17:20:358] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[16:17:20:466] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[16:17:20:825] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.358秒
[16:17:27:819] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[16:17:28:009] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[16:17:30:027] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[16:17:31:805] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[16:17:32:439] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[16:17:32:439] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[16:17:32:440] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[16:17:32:487] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[16:17:32:637] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 18.023 seconds (JVM running for 4290.872)
[16:17:32:644] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[16:18:56:752] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[16:18:56:774] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[16:18:56:938] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[16:18:56:950] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[16:18:57:284] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-30} closing ...
[16:18:57:312] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-30} closed
[16:19:00:254] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[16:19:01:242] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[16:19:01:259] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[16:19:01:259] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:19:01:259] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:19:01:259] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[16:19:01:259] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[16:19:01:259] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[16:19:01:259] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[16:19:01:260] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:19:01:260] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:19:01:260] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:19:01:260] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:19:01:260] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:19:01:286] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[16:19:01:340] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[16:19:01:341] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[16:19:01:421] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:19:01:423] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[16:19:01:482] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[16:19:01:484] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[16:19:01:484] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[16:19:01:485] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[16:19:01:485] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[16:19:01:485] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[16:19:01:485] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[16:19:01:486] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[16:19:01:486] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[16:19:12:141] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-31} inited
[16:19:12:141] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[16:19:12:300] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[16:19:12:718] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.417秒
[16:19:17:861] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[16:19:18:105] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[16:19:20:931] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[16:19:22:387] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[16:19:22:954] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[16:19:22:954] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[16:19:22:956] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[16:19:22:985] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[16:19:23:384] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 24.007 seconds (JVM running for 4401.62)
[16:19:23:394] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[16:19:25:856] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[16:19:26:479] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/pointDetail/exportPointRankOfCompany，出现异常:java.lang.NullPointerException；
[16:19:26:491] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","X-Forwarded-Host":"localhost:8000","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","X-Forwarded-Proto":"http","Sec-Fetch-Site":"none","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","X-Forwarded-Port":"8000","Sec-Fetch-Mode":"navigate","sec-ch-ua":"\"Microsoft Edge\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","sec-ch-ua-mobile":"?0","Upgrade-Insecure-Requests":"1","sec-ch-ua-platform":"\"macOS\"","host":"*************:8081","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:56781\"","X-Forwarded-For":"0:0:0:0:0:0:0:1","Sec-Fetch-User":"?1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"}
[16:19:26:492] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"companyId":"6040","companyType":"qiyeweixin","userId":"1690046"}
[16:19:26:492] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：java.lang.NullPointerException，异常信息：null，
[16:20:53:683] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[16:20:53:699] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[16:20:53:699] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Destroying Spring FrameworkServlet 'dispatcherServlet'
[16:20:53:933] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[16:20:53:948] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[16:20:54:303] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-31} closing ...
[16:20:54:337] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-31} closed
[16:20:58:272] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[16:20:59:213] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[16:20:59:227] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[16:20:59:227] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:20:59:227] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[16:20:59:227] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[16:20:59:227] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[16:20:59:227] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[16:20:59:228] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[16:20:59:228] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:20:59:228] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:20:59:228] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[16:20:59:228] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:20:59:228] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[16:20:59:253] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[16:20:59:310] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[16:20:59:310] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[16:20:59:398] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:20:59:401] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[16:20:59:467] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[16:20:59:469] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[16:20:59:470] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[16:20:59:470] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[16:20:59:470] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[16:20:59:471] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[16:20:59:471] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[16:20:59:472] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[16:20:59:472] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[16:21:02:327] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-32} inited
[16:21:02:328] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[16:21:02:417] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[16:21:02:787] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.369秒
[16:21:07:410] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[16:21:07:607] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[16:21:08:651] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[16:21:10:087] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[16:21:10:671] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[16:21:10:672] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[16:21:10:673] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[16:21:10:704] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[16:21:10:863] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 13.471 seconds (JVM running for 4509.068)
[16:21:10:869] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[16:21:13:999] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[17:20:20:145] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:55) - 产品：qkk 页面：/pages/examindex/index?
[17:20:20:145] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:56) - 请求接口：/examination/getRecommendExaminationsByProduct
[17:20:20:153] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:57) - 请求参数：{"product":"qkk","createrId":"6","terminal":"qkk","callLocationCode":"/pages/examindex/index?"}
[17:20:26:851] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[17:20:37:733] [INFO] - com.taurus.examinationassistant.controller.DownloadProxyController.generateDocument(DownloadProxyController.java:285) - 创建临时文件: /var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T/积分排行榜_62511476139380925621156.xlsx
[17:20:37:739] [ERROR] - com.taurus.examinationassistant.controller.PointDetailController.exportPointRankOfCompany(PointDetailController.java:172) - userId is empty
[17:20:37:741] [ERROR] - com.taurus.examinationassistant.controller.DownloadProxyController.generateDocument(DownloadProxyController.java:363) - 文件生成失败或为空
[17:22:14:102] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:55) - 产品：qkk 页面：/pages/examindex/index?
[17:22:14:103] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:56) - 请求接口：/examination/getRecommendExaminationsByProduct
[17:22:14:104] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:57) - 请求参数：{"product":"qkk","createrId":"6","terminal":"qkk","callLocationCode":"/pages/examindex/index?"}
[17:22:16:381] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[17:22:26:751] [INFO] - com.taurus.examinationassistant.controller.DownloadProxyController.generateDocument(DownloadProxyController.java:285) - 创建临时文件: /var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T/积分排行榜_62517039306607226629804.xlsx
[17:22:26:985] [INFO] - com.taurus.examinationassistant.controller.DownloadProxyController.generateDocument(DownloadProxyController.java:361) - 文件生成成功，大小: 3819 bytes
[17:22:26:985] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinMaterialService.uploadTemporaryMaterial(QiyeweixinMaterialService.java:56) - 开始上传文件，文件名: 积分排行榜_62517039306607226629804.xlsx, 文件大小: 3819 bytes
[17:22:29:023] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.getTranslateResult(QiyeweixinContactService.java:671) - 任务开始: status=1
[17:22:31:545] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinMessagePushService.sendTextCardMessage(QiyeweixinMessagePushService.java:162) - 发送文本卡片消息给用户: DingZhengNan, 标题: 文件通知
[17:22:32:207] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinMessagePushService.sendTextCardMessage(QiyeweixinMessagePushService.java:170) - 发送文本卡片消息响应: {errcode=0, errmsg=ok, msgid=SvpFVDN5j3cfoDnUMZjZAz_5kabs1vQa8w0PwtBYEDaM6PBw-OxcU-WL16FPvcGYjNwNqA50JORu0l-VRpAuVJyKX0eAnrKyk5Ir6rjC5bY}
[17:38:37:300] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[17:38:37:330] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[17:38:37:331] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Destroying Spring FrameworkServlet 'dispatcherServlet'
[17:38:37:649] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[17:38:37:706] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[17:38:38:062] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-32} closing ...
[17:38:38:072] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-32} closed
[17:38:42:940] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[17:38:44:131] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[17:38:44:167] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[17:38:44:167] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[17:38:44:168] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[17:38:44:168] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[17:38:44:168] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[17:38:44:168] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[17:38:44:168] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[17:38:44:168] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[17:38:44:168] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[17:38:44:168] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[17:38:44:168] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[17:38:44:168] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[17:38:44:213] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[17:38:44:637] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[17:38:44:638] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[17:38:44:822] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[17:38:44:825] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[17:38:44:942] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[17:38:44:947] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[17:38:44:948] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[17:38:44:948] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[17:38:44:949] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[17:38:44:949] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[17:38:44:950] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[17:38:44:950] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[17:38:44:951] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[17:38:48:142] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-33} inited
[17:38:48:143] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[17:38:48:234] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[17:38:48:720] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.484秒
[17:38:53:849] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[17:38:54:105] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[17:38:55:041] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[17:38:57:116] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[17:38:57:787] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[17:38:57:788] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[17:38:57:789] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[17:38:57:846] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[17:38:58:021] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 16.134 seconds (JVM running for 9176.36)
[17:38:58:030] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
