[13:07:23:471] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[13:07:26:200] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$AutoConfiguredMapperScannerRegistrar.registerBeanDefinitions(MybatisAutoConfiguration.java:252) - Searching for mappers annotated with @Mapper
[13:07:26:211] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$AutoConfiguredMapperScannerRegistrar.registerBeanDefinitions(MybatisAutoConfiguration.java:264) - Using auto-configuration base package 'com.taurus.examinationassistant'
[13:07:27:500] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[13:07:27:576] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[13:07:27:576] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[13:07:27:576] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[13:07:27:576] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[13:07:27:577] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[13:07:27:577] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[13:07:27:577] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[13:07:27:577] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[13:07:27:578] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[13:07:27:578] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[13:07:27:578] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[13:07:27:578] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[13:07:27:629] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[13:07:28:323] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[13:07:28:328] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[13:07:29:232] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[13:07:29:264] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[13:07:29:277] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[13:07:29:579] [DEBUG] - io.micrometer.core.util.internal.logging.InternalLoggerFactory.newDefaultFactory(InternalLoggerFactory.java:59) - Using SLF4J as the default logging framework
[13:07:29:689] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[13:07:29:706] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[13:07:29:707] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[13:07:29:708] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[13:07:29:710] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[13:07:29:712] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[13:07:29:713] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[13:07:29:714] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[13:07:29:714] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[13:07:30:447] [DEBUG] - io.undertow.server.session.InMemorySessionManager.registerSessionListener(InMemorySessionManager.java:268) - Registered session listener io.undertow.servlet.core.SessionListenerBridge@716d8ab7
[13:07:30:515] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[13:07:35:587] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[13:07:35:604] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[13:07:35:924] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[13:07:35:937] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationInstanceProcessMapper对应的Mapper
[13:07:36:158] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserFormMapper对应的Mapper
[13:07:36:202] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyDepartmentMapper对应的Mapper
[13:07:36:248] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarUserMilestoneMapper对应的Mapper
[13:07:36:292] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionRandomRangeMapper对应的Mapper
[13:07:36:324] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MessageTemplateMapper对应的Mapper
[13:07:36:363] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PermissionAuthorizationMapper对应的Mapper
[13:07:36:384] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.NameListDetailMapper对应的Mapper
[13:07:36:397] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserReportRelationMapper对应的Mapper
[13:07:36:412] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PermissionMapper对应的Mapper
[13:07:36:426] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookTagMapper对应的Mapper
[13:07:36:446] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.NameListMapper对应的Mapper
[13:07:36:469] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperTagMapper对应的Mapper
[13:07:36:485] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UniversalProductOrderRelationMapper对应的Mapper
[13:07:36:503] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookGradeMapper对应的Mapper
[13:07:36:519] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionBankQuestionMapper对应的Mapper
[13:07:36:533] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarStageEventMapper对应的Mapper
[13:07:36:563] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CategoryMapper对应的Mapper
[13:07:36:569] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AppMapper对应的Mapper
[13:07:36:580] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionSummaryMapper对应的Mapper
[13:07:36:598] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserMapper对应的Mapper
[13:07:36:610] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookPraiseMapper对应的Mapper
[13:07:36:635] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyJobGradeMapper对应的Mapper
[13:07:36:644] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarUserMapper对应的Mapper
[13:07:36:664] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.InviteRelationMapper对应的Mapper
[13:07:36:675] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.VersionMapper对应的Mapper
[13:07:36:685] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyMapper对应的Mapper
[13:07:36:703] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyUserGroupMapper对应的Mapper
[13:07:36:716] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PointRuleMapper对应的Mapper
[13:07:36:745] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionRandomMapper对应的Mapper
[13:07:36:776] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationInstanceMapper对应的Mapper
[13:07:36:825] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookPracticeSummaryMapper对应的Mapper
[13:07:36:843] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongQuestionBookReviewSummaryMapper对应的Mapper
[13:07:36:862] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsitePaperInstanceProcessMapper对应的Mapper
[13:07:36:885] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QiyewxInterfaceLicenseOrderMapper对应的Mapper
[13:07:36:895] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.TaxSheetApplicationMapper对应的Mapper
[13:07:36:922] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationMapper对应的Mapper
[13:07:36:962] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserCompanyMapper对应的Mapper
[13:07:36:992] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SysRoleMapper对应的Mapper
[13:07:37:137] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarMapper对应的Mapper
[13:07:37:163] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentSubjectAccessInfoMapper对应的Mapper
[13:07:37:170] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.FreePracticeOfUserMapper对应的Mapper
[13:07:37:179] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperPraiseMapper对应的Mapper
[13:07:37:190] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationGroupExaminationMapper对应的Mapper
[13:07:37:203] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductMapper对应的Mapper
[13:07:37:219] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteOrderMapper对应的Mapper
[13:07:37:236] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentClassificationInfoMapper对应的Mapper
[13:07:37:257] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WxMiniprogramSubscribeMessageOfEteaMapper对应的Mapper
[13:07:37:264] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductCountTransactionMapper对应的Mapper
[13:07:37:273] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ViewPerformanceWithoutAdTransactionMapper对应的Mapper
[13:07:37:296] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MultipleMarkingExaminationInstanceMapper对应的Mapper
[13:07:37:306] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.DiscountMapper对应的Mapper
[13:07:37:313] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ManualServiceRecordMapper对应的Mapper
[13:07:37:326] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteContentAccessMapper对应的Mapper
[13:07:37:335] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyRegisterFormFlowRecordMapper对应的Mapper
[13:07:37:347] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SysLogMapper对应的Mapper
[13:07:37:356] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WeixinAccountMapper对应的Mapper
[13:07:37:360] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AnnualReportMapper对应的Mapper
[13:07:37:367] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductOrderRelationCountMapper对应的Mapper
[13:07:37:375] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.NanxiangRegisterMapper对应的Mapper
[13:07:37:383] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.GameQuestionRandomRuleMapper对应的Mapper
[13:07:37:393] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationExamineeMapper对应的Mapper
[13:07:37:407] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookFavoriteMapper对应的Mapper
[13:07:37:419] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.OssFileMapper对应的Mapper
[13:07:37:431] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookMapper对应的Mapper
[13:07:37:464] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyRegisterFormMapper对应的Mapper
[13:07:37:475] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MultipleMarkingExaminationMapper对应的Mapper
[13:07:37:490] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionAttachmentMapper对应的Mapper
[13:07:37:496] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserOfProductMapper对应的Mapper
[13:07:37:501] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentSubjectMapper对应的Mapper
[13:07:37:510] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserExaminationInstanceReportMapper对应的Mapper
[13:07:37:518] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductIntroductionMediaMapper对应的Mapper
[13:07:37:526] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteWithdrawMapper对应的Mapper
[13:07:37:535] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductOrderRelationMapper对应的Mapper
[13:07:37:543] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationExamineeSnapshotMapper对应的Mapper
[13:07:37:551] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionExampleDeletedMapper对应的Mapper
[13:07:37:560] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserOfYncxMapper对应的Mapper
[13:07:37:573] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AppGroupUserMapper对应的Mapper
[13:07:37:587] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.HomepageUserFollowedMapper对应的Mapper
[13:07:37:601] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentSubjectCategoryMapper对应的Mapper
[13:07:37:612] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperFavoriteMapper对应的Mapper
[13:07:37:622] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SystemMessageMapper对应的Mapper
[13:07:37:630] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteOrderRelationMapper对应的Mapper
[13:07:37:637] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.TemplateMapper对应的Mapper
[13:07:37:648] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongQuestionBookQuestionMapper对应的Mapper
[13:07:37:662] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationInstanceEventMapper对应的Mapper
[13:07:37:678] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookExamineeMapper对应的Mapper
[13:07:37:693] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PassRaceResultMapper对应的Mapper
[13:07:37:706] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionMapper对应的Mapper
[13:07:37:784] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongQuestionBookMapper对应的Mapper
[13:07:37:802] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookPracticeProcessMapper对应的Mapper
[13:07:37:807] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MessagePlanMapper对应的Mapper
[13:07:37:811] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SmsMessageMapper对应的Mapper
[13:07:37:814] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongQuestionBookReviewProcessMapper对应的Mapper
[13:07:37:819] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionFavoriteMapper对应的Mapper
[13:07:37:824] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationWxGidMapper对应的Mapper
[13:07:37:830] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationNameListMapper对应的Mapper
[13:07:37:840] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PassRaceGameMapper对应的Mapper
[13:07:37:846] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserExaminationPerformanceReportMapper对应的Mapper
[13:07:37:851] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.OssFileAudioMapper对应的Mapper
[13:07:37:855] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserFormFlowRecordMapper对应的Mapper
[13:07:37:865] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QiyewxOrderMapper对应的Mapper
[13:07:37:869] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MessagePushSubscriptionMapper对应的Mapper
[13:07:37:874] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QrcodeScanEntryMapper对应的Mapper
[13:07:37:877] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QiyewxUserAccountLicenseMapper对应的Mapper
[13:07:37:882] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UniversalOrderMapper对应的Mapper
[13:07:37:885] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionCompositionChildMapper对应的Mapper
[13:07:37:887] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperMapper对应的Mapper
[13:07:37:893] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PracticePushScheduleMapper对应的Mapper
[13:07:37:909] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationTagMapper对应的Mapper
[13:07:37:913] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookUserRecordMapper对应的Mapper
[13:07:37:917] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AppStartNoticeMapper对应的Mapper
[13:07:37:922] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PointDetailMapper对应的Mapper
[13:07:37:928] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserSummaryMapper对应的Mapper
[13:07:37:932] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyGroupUserMapper对应的Mapper
[13:07:37:935] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.FileAccessPermissionMapper对应的Mapper
[13:07:37:937] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarUserProfileMapper对应的Mapper
[13:07:37:940] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PointConsumerationMapper对应的Mapper
[13:07:37:943] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsitePaperInstanceMapper对应的Mapper
[13:07:37:949] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.TaxIdentityInfoMapper对应的Mapper
[13:07:37:955] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SysUserRoleMapper对应的Mapper
[13:07:37:963] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongBookMapper对应的Mapper
[13:07:37:968] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AppStartNoticeReadUserMapper对应的Mapper
[13:07:37:971] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperQuestionMapper对应的Mapper
[13:07:37:975] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PassRaceStageMapper对应的Mapper
[13:07:37:979] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductIntroductionMapper对应的Mapper
[13:07:37:983] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationStartupMapper对应的Mapper
[13:07:37:987] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PointUserMapper对应的Mapper
[13:07:37:995] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.GameResultMapper对应的Mapper
[13:07:38:010] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteFavoriteMapper对应的Mapper
[13:07:38:015] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteUserSignatureMapper对应的Mapper
[13:07:38:018] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserViewPerformanceWithoutAdMapper对应的Mapper
[13:07:38:020] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentUnlockMapper对应的Mapper
[13:07:38:023] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.OrdersMapper对应的Mapper
[13:07:38:027] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserDeviceBindingMapper对应的Mapper
[13:07:38:030] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentProductOfUserMapper对应的Mapper
[13:07:38:033] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CustomerRequirementMapper对应的Mapper
[13:07:38:037] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookQuestionMapper对应的Mapper
[13:07:38:040] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MessageScheduleMapper对应的Mapper
[13:07:38:043] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationFavoriteMapper对应的Mapper
[13:07:38:046] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookSummaryMapper对应的Mapper
[13:07:38:049] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentSubjectOfUserMapper对应的Mapper
[13:07:38:053] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.GameMapper对应的Mapper
[13:07:38:064] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyFunctionRoleMapper对应的Mapper
[13:07:38:070] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：2.132秒
[13:07:47:742] [DEBUG] - io.netty.util.internal.logging.InternalLoggerFactory.useSlf4JLoggerFactory(InternalLoggerFactory.java:63) - Using SLF4J as the default logging framework
[13:07:47:750] [DEBUG] - io.netty.util.concurrent.GlobalEventExecutor.<clinit>(GlobalEventExecutor.java:53) - -Dio.netty.globalEventExecutor.quietPeriodSeconds: 1
[13:07:47:763] [DEBUG] - io.netty.util.internal.InternalThreadLocalMap.<clinit>(InternalThreadLocalMap.java:100) - -Dio.netty.threadLocalMap.stringBuilder.initialSize: 1024
[13:07:47:764] [DEBUG] - io.netty.util.internal.InternalThreadLocalMap.<clinit>(InternalThreadLocalMap.java:101) - -Dio.netty.threadLocalMap.stringBuilder.maxSize: 4096
[13:07:47:856] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[13:07:47:863] [DEBUG] - io.netty.channel.MultithreadEventLoopGroup.<clinit>(MultithreadEventLoopGroup.java:44) - -Dio.netty.eventLoopThreads: 16
[13:07:47:894] [DEBUG] - io.netty.util.internal.PlatformDependent0.explicitNoUnsafeCause0(PlatformDependent0.java:515) - -Dio.netty.noUnsafe: false
[13:07:47:894] [DEBUG] - io.netty.util.internal.PlatformDependent0.javaVersion0(PlatformDependent0.java:1026) - Java version: 8
[13:07:47:895] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:140) - sun.misc.Unsafe.theUnsafe: available
[13:07:47:896] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:164) - sun.misc.Unsafe.copyMemory: available
[13:07:47:897] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:196) - sun.misc.Unsafe.storeFence: available
[13:07:47:898] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:239) - java.nio.Buffer.address: available
[13:07:47:898] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:312) - direct buffer constructor: available
[13:07:47:900] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:403) - java.nio.Bits.unaligned: available, true
[13:07:47:900] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:478) - jdk.internal.misc.Unsafe.allocateUninitializedArray(int): unavailable prior to Java9
[13:07:47:900] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:501) - java.nio.DirectByteBuffer.<init>(long, {int,long}): available
[13:07:47:901] [DEBUG] - io.netty.util.internal.PlatformDependent.unsafeUnavailabilityCause0(PlatformDependent.java:1157) - sun.misc.Unsafe: available
[13:07:47:902] [DEBUG] - io.netty.util.internal.PlatformDependent.tmpdir0(PlatformDependent.java:1303) - -Dio.netty.tmpdir: /var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T (java.io.tmpdir)
[13:07:47:903] [DEBUG] - io.netty.util.internal.PlatformDependent.bitMode0(PlatformDependent.java:1382) - -Dio.netty.bitMode: 64 (sun.arch.data.model)
[13:07:47:907] [DEBUG] - io.netty.util.internal.PlatformDependent.isOsx0(PlatformDependent.java:1125) - Platform: MacOS
[13:07:47:909] [DEBUG] - io.netty.util.internal.PlatformDependent.<clinit>(PlatformDependent.java:176) - -Dio.netty.maxDirectMemory: 3817865216 bytes
[13:07:47:909] [DEBUG] - io.netty.util.internal.PlatformDependent.<clinit>(PlatformDependent.java:183) - -Dio.netty.uninitializedArrayAllocationThreshold: -1
[13:07:47:910] [DEBUG] - io.netty.util.internal.CleanerJava6.<clinit>(CleanerJava6.java:92) - java.nio.ByteBuffer.cleaner(): available
[13:07:47:911] [DEBUG] - io.netty.util.internal.PlatformDependent.<clinit>(PlatformDependent.java:203) - -Dio.netty.noPreferDirect: false
[13:07:47:912] [DEBUG] - io.netty.channel.nio.NioEventLoop.<clinit>(NioEventLoop.java:110) - -Dio.netty.noKeySetOptimization: false
[13:07:47:912] [DEBUG] - io.netty.channel.nio.NioEventLoop.<clinit>(NioEventLoop.java:111) - -Dio.netty.selectorAutoRebuildThreshold: 512
[13:07:47:925] [DEBUG] - io.netty.util.internal.PlatformDependent$Mpsc.<clinit>(PlatformDependent.java:1008) - org.jctools-core.MpscChunkedArrayQueue: available
[13:07:47:940] [WARN] - io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:70) - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
[13:07:47:949] [DEBUG] - io.netty.util.NetUtil.<clinit>(NetUtil.java:148) - -Djava.net.preferIPv4Stack: false
[13:07:47:949] [DEBUG] - io.netty.util.NetUtil.<clinit>(NetUtil.java:149) - -Djava.net.preferIPv6Addresses: false
[13:07:47:952] [DEBUG] - io.netty.util.NetUtilInitializations.determineLoopback(NetUtilInitializations.java:145) - Loopback interface: lo0 (lo0, 0:0:0:0:0:0:0:1%lo0)
[13:07:47:953] [DEBUG] - io.netty.util.NetUtil$SoMaxConnAction.run(NetUtil.java:206) - Failed to get SOMAXCONN from sysctl and file /proc/sys/net/core/somaxconn. Default: 128
[13:07:47:972] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:141) - Default ResolvedAddressTypes: IPV4_PREFERRED
[13:07:47:973] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:142) - Localhost address: localhost/127.0.0.1
[13:07:47:973] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:151) - Windows hostname: null
[13:07:47:975] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:164) - Default search domains: []
[13:07:47:976] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:173) - Default UnixResolverOptions{ndots=1, timeout=5, attempts=16}
[13:07:47:989] [DEBUG] - io.netty.resolver.DefaultHostsFileEntriesResolver.<clinit>(DefaultHostsFileEntriesResolver.java:53) - -Dio.netty.hostsFileRefreshInterval: 0
[13:07:48:006] [DEBUG] - io.netty.util.ResourceLeakDetector.<clinit>(ResourceLeakDetector.java:129) - -Dio.netty.leakDetection.level: simple
[13:07:48:007] [DEBUG] - io.netty.util.ResourceLeakDetector.<clinit>(ResourceLeakDetector.java:130) - -Dio.netty.leakDetection.targetRecords: 4
[13:07:48:007] [DEBUG] - io.netty.util.ResourceLeakDetectorFactory$DefaultResourceLeakDetectorFactory.newResourceLeakDetector(ResourceLeakDetectorFactory.java:196) - Loaded default ResourceLeakDetector: io.netty.util.ResourceLeakDetector@1f328b9a
[13:07:48:084] [DEBUG] - io.netty.channel.DefaultChannelId.<clinit>(DefaultChannelId.java:79) - -Dio.netty.processId: 80014 (auto-detected)
[13:07:48:106] [DEBUG] - io.netty.channel.DefaultChannelId.<clinit>(DefaultChannelId.java:101) - -Dio.netty.machineId: 76:f7:10:ff:fe:89:1e:3c (auto-detected)
[13:07:48:147] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:157) - -Dio.netty.allocator.numHeapArenas: 16
[13:07:48:148] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:158) - -Dio.netty.allocator.numDirectArenas: 16
[13:07:48:148] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:160) - -Dio.netty.allocator.pageSize: 8192
[13:07:48:148] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:165) - -Dio.netty.allocator.maxOrder: 9
[13:07:48:148] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:169) - -Dio.netty.allocator.chunkSize: 4194304
[13:07:48:149] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:170) - -Dio.netty.allocator.smallCacheSize: 256
[13:07:48:150] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:171) - -Dio.netty.allocator.normalCacheSize: 64
[13:07:48:150] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:172) - -Dio.netty.allocator.maxCachedBufferCapacity: 32768
[13:07:48:150] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:173) - -Dio.netty.allocator.cacheTrimInterval: 8192
[13:07:48:151] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:174) - -Dio.netty.allocator.cacheTrimIntervalMillis: 0
[13:07:48:151] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:175) - -Dio.netty.allocator.useCacheForAllThreads: false
[13:07:48:151] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:176) - -Dio.netty.allocator.maxCachedByteBuffersPerChunk: 1023
[13:07:48:166] [DEBUG] - io.netty.buffer.ByteBufUtil.<clinit>(ByteBufUtil.java:89) - -Dio.netty.allocator.type: pooled
[13:07:48:166] [DEBUG] - io.netty.buffer.ByteBufUtil.<clinit>(ByteBufUtil.java:98) - -Dio.netty.threadLocalDirectBufferSize: 0
[13:07:48:167] [DEBUG] - io.netty.buffer.ByteBufUtil.<clinit>(ByteBufUtil.java:101) - -Dio.netty.maxThreadLocalCharBufferSize: 16384
[13:07:48:179] [DEBUG] - io.netty.bootstrap.ChannelInitializerExtensions.getExtensions(ChannelInitializerExtensions.java:54) - -Dio.netty.bootstrap.extensions: null
[13:07:48:337] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:07:48:337] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:07:48:337] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:07:48:607] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:96) - -Dio.netty.recycler.maxCapacityPerThread: 4096
[13:07:48:619] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:97) - -Dio.netty.recycler.ratio: 8
[13:07:48:627] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:98) - -Dio.netty.recycler.chunkSize: 32
[13:07:48:630] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:99) - -Dio.netty.recycler.blocking: false
[13:07:48:634] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:100) - -Dio.netty.recycler.batchFastThreadLocalOnly: true
[13:07:48:669] [DEBUG] - io.netty.buffer.AbstractByteBuf.<clinit>(AbstractByteBuf.java:63) - -Dio.netty.buffer.checkAccessible: true
[13:07:48:671] [DEBUG] - io.netty.buffer.AbstractByteBuf.<clinit>(AbstractByteBuf.java:64) - -Dio.netty.buffer.checkBounds: true
[13:07:48:671] [DEBUG] - io.netty.util.ResourceLeakDetectorFactory$DefaultResourceLeakDetectorFactory.newResourceLeakDetector(ResourceLeakDetectorFactory.java:196) - Loaded default ResourceLeakDetector: io.netty.util.ResourceLeakDetector@13f285fe
[13:07:48:802] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@543356765 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x1b47e8fd, L:/*************:64692 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@5f2d89cf[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[13:07:48:832] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connectPubSub$3(ClientConnectionsEntry.java:234) - new pubsub connection created: RedisPubSubConnection@1675116867 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xb108cb2a, L:/*************:64691 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@6d1fa348[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[13:07:48:835] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for 47.111.231.172/47.111.231.172:6379
[13:07:48:840] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@708236303 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x78a32070, L:/*************:64690 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@24f12f39[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[13:07:48:844] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:07:48:846] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:07:48:909] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@287487249 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xde275dca, L:/*************:64693 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@1fe55945[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[13:07:48:916] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:07:48:921] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1593373923 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xf247a483, L:/*************:64694 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@503f014c[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[13:07:48:928] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:07:49:020] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1201700932 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x737c5e19, L:/*************:64695 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@496776c0[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[13:07:49:036] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1644559042 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd130ade6, L:/*************:64696 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@6cc218f2[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[13:07:49:131] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:07:49:147] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:07:49:278] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1761677223 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x01a352e9, L:/*************:64701 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@67c6fcf7[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[13:07:49:279] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@413619253 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xe402a442, L:/*************:64702 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@779dd81a[Completed normally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[13:07:49:306] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:07:49:309] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:07:49:418] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@349494030 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x1b9e72e3, L:/*************:64703 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@1a1327e4[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[13:07:49:428] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1407920086 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xab16c563, L:/*************:64704 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@5d2cc3db[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[13:07:49:444] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:07:49:445] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:07:49:540] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@51320082 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x71dc16d9, L:/*************:64705 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@dc8fb03[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[13:07:49:542] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@728669949 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x9df24d66, L:/*************:64706 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@25a965e0[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[13:07:49:557] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:07:49:559] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:07:49:623] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@2105455101 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xa8765202, L:/*************:64707 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@73b94022[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[13:07:49:624] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1606290934 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x5cafa0d3, L:/*************:64708 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@5179e871[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[13:07:49:635] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:07:49:647] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:07:49:733] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@556982211 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x22a144dd, L:/*************:64709 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@5f8c655[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[13:07:49:747] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:07:49:770] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@2077346139 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x857f360c, L:/*************:64710 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@3407e8fc[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[13:07:49:775] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:07:49:828] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1763715635 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x23e792f4, L:/*************:64711 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@2c22a973[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[13:07:49:831] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:07:49:861] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@550420351 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xaea5d478, L:/*************:64712 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@65cc2502[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[13:07:49:865] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:07:49:898] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@467328609 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x933c4dcb, L:/*************:64713 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@5ed8537e[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[13:07:49:900] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:07:49:948] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1409326995 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd449d8d4, L:/*************:64714 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@11020b77[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[13:07:49:949] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:07:49:992] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1297673012 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x5d5fb589, L:/*************:64715 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@85a7cfb[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[13:07:49:999] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:07:50:021] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1761448370 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x62d1c941, L:/*************:64716 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@2dff12c9[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[13:07:50:025] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:07:50:121] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1943126025 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x8e712af1, L:/*************:64717 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@6224d71f[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[13:07:50:133] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1677288663 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2b597949, L:/*************:64718 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@26fbf6cc[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[13:07:50:133] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for 47.111.231.172/47.111.231.172:6379
[13:07:56:363] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.AccessController:
	{GET [/access/getTokenByTime]}: getTokenByTime(Integer,Long,HttpServletRequest)
	{GET [/access/getTokenUnlimit]}: getTokenUnlimit(Integer,HttpServletRequest)
	{GET [/access/heartbeat]}: heartbeat()
	{GET [/access/getTokenStr]}: getToken(Integer,HttpServletRequest)
[13:07:56:369] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ActivityController:
	{ [/activity/getActiveActivities]}: getActiveActivities(String)
[13:07:56:370] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.AnnualReportController:
	{GET [/annual/getReport]}: getEntity(Integer)
[13:07:56:371] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.AppStartNoticeController:
	{GET [/appStartNotice/getCurrentNoticeOfUser]}: getCurrentNoticeOfUser(String,Integer)
	{GET [/appStartNotice/getCurrentNotice]}: getCurrentNotice(String)
[13:07:56:373] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.AppStartNoticeReadUseController:
	{GET [/appStartNoticeReadUser/save]}: save(Integer,Integer)
[13:07:56:389] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CompanyController:
	{POST [/company/createCompany]}: createCompany(JSONObject)
	{GET [/company/getCompanyInfoByUserId]}: getCompanyInfoByUserId(int)
	{POST [/company/createDepartment]}: createDepartment(JSONObject)
	{POST [/company/createJobGrade]}: createJobGrade(JSONObject)
	{POST [/company/createFunctionRole]}: createFunctionRole(JSONObject)
	{GET [/company/deleteCompany]}: deleteCompany(int)
	{GET [/company/deleteDepartment]}: deleteDepartment(int)
	{GET [/company/deleteDepartmentCarefully]}: deleteDepartmentCarefully(Integer,Integer)
	{GET [/company/deleteJobGrade]}: deleteJobGrade(int)
	{GET [/company/deleteJobGradeCarefully]}: deleteJobGradeCarefully(Integer,Integer)
	{GET [/company/deleteFunctionRole]}: deleteFunctionRole(int)
	{GET [/company/deleteFunctionRoleCarefully]}: deleteFunctionRoleCarefully(Integer,Integer)
	{POST [/company/modifyCompany]}: modifyCompany(JSONObject)
	{GET [/company/modifyCompanyName]}: modifyCompanyName(String,Integer)
	{POST [/company/modifyDepartment]}: modifyDepartment(JSONObject)
	{POST [/company/modifyJobGrade]}: modifyJobGrade(JSONObject)
	{POST [/company/modifyFunctionRole]}: modifyFunctionRole(JSONObject)
	{GET [/company/getCompanyInfo]}: getCompanyInfo(Integer)
	{GET [/company/getCompanyInfoById]}: getCompanyInfoWrapperById(int)
	{GET [/company/getCompanyInfoByCodeOrName]}: getCompanyInfoByCodeOrName(String)
	{GET [/company/getCompanyInfoListByName]}: getCompanyInfoListByName(String)
	{GET [/company/getAllDepartmentOfCompany]}: getAllDepartmentOfCompany(Integer,Integer)
	{GET [/company/getNGradeDepartment]}: getLevelDepartmentOfLessThanOrEqualDesignativeGrade(Integer,Integer)
	{GET [/company/getAllDepartmentOfCompanyByUserId]}: getAllDepartmentOfCompanyByUserId(Integer)
	{GET [/company/getSubDepartmentExcludingSelf]}: getSubDepartmentExcludingSelf(String)
	{GET [/company/getSubDepartmentIncludingSelf]}: getSubDepartmentIncludingSelf(String)
	{GET [/company/getAllCompanyJobGradeOfCompany]}: getAllCompanyJobGradeOfCompany(int)
	{GET [/company/getAllCompanyInfo]}: getAllCompanyInfo(int)
	{POST [/company/getQiyeweixinCompanyList]}: getQiyeweixinCompanyList(JSONObject)
	{GET [/company/getAllCompanyInfoWith2Grade]}: getAllCompanyInfoWith2Grade(Integer)
	{GET [/company/getAllCompanyFunctionRoleOfCompany]}: getAllCompanyFunctionRoleOfCompany(int)
	{GET [/company/search]}: searchCompanyByKeyword(String)
	{GET [/company/batchCloseAccount]}: batchCloseAccount(String)
	{GET [/company/closeAccount]}: closeAccount(Integer,Integer)
	{GET [/company/getResidualFlow]}: getResidualFlow(Integer)
	{POST [/company/getOrganizationFrameworkInfoByIds]}: getOrganizationFrameworkInfoByIds(JSONObject)
	{POST [/company/modify]}: modify(Company)
[13:07:56:395] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CompanyDepartmentController:
	{GET [/companyDepartment/getCompanyDepartmentList]}: getCompanyDepartmentList(Integer,String)
[13:07:56:406] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CompanyGroupUserController:
	{POST [/companyGroupUser/create]}: createCompanyGroupUser(CompanyGroupUser)
	{POST [/companyGroupUser/batchCreate]}: batchCreateCompanyGroupUser(List)
	{POST [/companyGroupUser/update]}: updateCompanyGroupUser(CompanyGroupUser)
	{GET [/companyGroupUser/delete]}: deleteCompanyGroupUser(Integer,String)
	{GET [/companyGroupUser/getGroupsByUserId]}: getGroupsByUserId(Integer)
	{GET [/companyGroupUser/getUsersByGroupId]}: getUsersByGroupId(Integer)
	{GET [/companyGroupUser/checkUserInGroup]}: checkUserInGroup(Integer,Integer)
[13:07:56:408] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CompanyUserGroupController:
	{GET [/companyUserGroup/detail]}: getGroupDetail(Integer)
	{POST [/companyUserGroup/update]}: updateGroup(JSONObject)
	{GET [/companyUserGroup/delete]}: deleteGroup(Integer)
	{GET [/companyUserGroup/list]}: getGroupList(Integer,Integer,String,String)
	{POST [/companyUserGroup/create]}: createGroup(JSONObject)
[13:07:56:412] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentClassificationInfoController:
	{ [/contentClassification/getPracticeContentOfUnlock]}: getPracticeContentOfUnlock()
	{ [/contentClassification/getUnlockedPracticeContentOfUser]}: getUnlockedPracticeContentOfUser(Integer)
	{ [/contentClassification/getPracticeContentOfSubject]}: getPracticeContentOfSubject(Integer,Integer)
[13:07:56:416] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentProductOfUserController:
	{GET [/contentProductOfUser/getProductListOfUser]}: getProductListOfUser(Integer)
[13:07:56:429] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentSubjectCategoryController:
	{GET [/contentSubjectCategory/getCategoryListOfSubject]}: getCategoryListOfSubject(Integer)
	{GET [/contentSubjectCategory/getSubjectListOfCategory]}: getSubjectListOfCategory(Integer)
	{GET [/contentSubjectCategory/getPathListOfSubject]}: getPathListOfSubject(Integer)
	{GET [/contentSubjectCategory/getNLevelCategoryWithContentNum]}: getLevelCategoriesOfLessThanOrEqualDesignativeLevel(String,Integer,Integer)
	{POST [/contentSubjectCategory/update]}: update(JSONArray)
	{GET [/contentSubjectCategory/delete]}: delete(Integer,Integer)
	{POST [/contentSubjectCategory/save]}: save(JSONArray)
[13:07:56:432] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentSubjectController:
	{POST [/contentSubject/createSubject]}: createSubject(JSONObject)
	{GET [/contentSubject/getContentSubjectByContent]}: getContentSubjectByContent(String,Integer)
	{GET [/contentSubject/getAllContentSubject]}: getSubjectInfoList(String,Integer,Integer,Integer)
	{GET [/contentSubject/getSubjectListOfProduct]}: getSubjectListOfProduct(String)
	{GET [/contentSubject/getAllContentSubjectWithUserSelection]}: getAllContentSubjectWithUserSelection(Integer,String)
	{GET [/contentSubject/getSubjectWrapperBySubjectId]}: getSubjectWrapperBySubjectId(Integer)
	{GET [/contentSubject/getSubjectWrapper]}: getSubjectWrapper(Integer)
	{GET [/contentSubject/getSubjectWrapperWithFavoriteInfo]}: getSubjectWrapperWithFavoriteInfo(Integer,Integer)
	{POST [/contentSubject/modifySubject]}: modifySubject(JSONObject)
	{GET [/contentSubject/delete]}: delete(Integer)
[13:07:56:435] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentSubjectOfUserController:
	{POST [/contentSubjectOfUser/saveContentSubjectsOfUser]}: saveContentSubjectsOfUser(JSONObject)
	{GET [/contentSubjectOfUser/getContentSubjectListOfUser]}: getContentSubjectListOfUser(Integer,String,Integer,Integer,Integer)
[13:07:56:441] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentSubjectSummaryController:
	{GET [/contentSubjectSummary/getSummary]}: getSummary(Integer)
[13:07:56:442] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentUnlockController:
	{POST [/contentUnlock/save]}: save(JSONObject)
[13:07:56:442] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CryptographyController:
	{ [/cryptography/decrypt]}: decrypt(String)
	{ [/cryptography/encrypt]}: encrypt(String)
[13:07:56:443] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CustomerRequirementController:
	{POST [/cs/createRequirement]}: save(JSONObject)
[13:07:56:475] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.DataResolverController:
	{GET [/dataResolver/setSelectionChildType]}: setSelectionChildType(int)
	{GET [/dataResolver/reCalculateQuestionScore]}: reCalculateQuestionScore(int,Integer,Integer,String)
	{GET [/dataResolver/reCalculateExaminationInstanceByQuestionId]}: reCalculateExaminationInstance(int,int)
	{GET [/dataResolver/reCalculateExaminationInstance]}: reCalculateExaminationInstance(int,Integer,String)
	{GET [/dataResolver/reCreateCompletionOptions]}: reCreateCompletionOptions(Integer,Integer,Integer,String)
	{GET [/dataResolver/checkDataMigrationFromFreeVersion]}: checkDataMigrationFromFreeVersion(int)
	{GET [/dataResolver/migrateDataFromFreeVersionToEnterpriseVersion]}: migrateDataFromFreeVersionToEnterpriseVersion(int,int)
	{GET [/dataResolver/deleteExaminationIncludingWrongRandomQuestion]}: deleteExaminationIncludingWrongRandomQuestion()
	{GET [/dataResolver/configUserSysUserRole]}: configUserSysUserRole()
	{GET [/dataResolver/recaculateExaminationInstanceBecauseOfNullScore]}: recaculateExaminationInstanceBecauseOfNullScore()
	{GET [/dataResolver/deleteInValidExamination]}: deleteInValidExamination()
	{GET [/dataResolver/deleteExaminationInstanceAndProcessOfEtea]}: deleteExaminationInstanceAndProcessOfEtea(String,String)
	{GET [/dataResolver/deleteInvalidSubscirbeMessage]}: deleteInvalidSubscirbeMessage(String)
	{GET [/dataResolver/clearJSONErrorStr]}: clearJSONErrorStr(Integer)
	{GET [/dataResolver/recaculateExcerciseBookInfo]}: recaculateExcerciseBookInfo()
	{GET [/dataResolver/deleteInvalidQuestion]}: deleteInvalidQuestion()
	{GET [/dataResolver/deleteInvalidPaper]}: deleteInvalidPaper()
	{GET [/dataResolver/deleteExerciseBookAndRelation]}: deleteInvalidExerciseBookAndRelation()
	{GET [/dataResolver/deleteInvalidSystemMessage]}: deleteInvalidSystemMessage(String,String,String)
	{GET [/dataResolver/deleteExaminationInstance]}: deleteExaminationInstance()
	{GET [/dataResolver/handleDeleteQuestionsEvent]}: handleDeleteQuestionsEvent()
	{GET [/dataResolver/restoreExerciseBookByProcess]}: restoreExerciseBookByProcess()
	{GET [/dataResolver/clearRedisData]}: clearRedisData(String)
	{GET [/dataResolver/clearAccountPermently]}: clearAccountPermently(Integer,Integer)
	{GET [/dataResolver/generateQuestionJsonFileOfExamination]}: generateQuestionJsonFileOfExamination(Integer,Integer,Integer)
	{GET [/dataResolver/generateHotFileListOfCDN]}: generateHotFileListOfCDN(Integer,Integer)
	{GET [/dataResolver/resizeOssPicFile]}: resizeOssPicFile(String)
	{GET [/dataResolver/deleteQuestionAndRelatedRecords]}: deleteQuestionAndRelatedRecords(String,String)
	{GET [/dataResolver/deleteWrongQuestionBook]}: deleteWrongQuestionBook(String,String)
	{GET [/dataResolver/transformCompletionQuestion]}: transformCompletionQuestion(Integer,Integer)
	{GET [/dataResolver/restoreExerciseBook]}: restoreExerciseBook(Integer)
	{GET [/dataResolver/replaceDepartmentId]}: replaceDepartmentId(Integer)
	{GET [/dataResolver/generateAnnualReport]}: generateAnnualReport(Integer,Integer,String,String)
	{GET [/dataResolver/insertCompanyUser]}: insertCompanyUser(Integer)
	{GET [/dataResolver/getUnCompleteQuestion]}: getUnCompleteQuestion(Integer)
	{GET [/dataResolver/deleteDepartments]}: deleteDepartments()
	{POST [/dataResolver/processQuestionAndAnswers], consumes [multipart/form-data]}: processQuestionAndAnswers(HttpServletRequest,HttpServletResponse)
	{GET [/dataResolver/deletedRandomRangeQuestionWithQuestionDeletedState]}: deletedRandomRangeQuestionWithQuestionDeletedState(Integer,Integer)
	{GET [/dataResolver/clearExaminationCache]}: reloadExaminationCache(Integer,Integer)
[13:07:56:482] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.DownloadProxyController:
	{POST [/download/proxy]}: proxyDownload(HttpServletRequest,JSONObject)
[13:07:56:492] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationController:
	{GET [/examination/examination]}: getExaminationById(int)
	{POST [/examination/create]}: createExamination(JSONObject)
	{POST [/examination/createExaminationWithContentCheck]}: createExaminationWithContentCheck(JSONObject)
	{POST [/examination/createIncludingExamineeSelect]}: createExaminationIncludingExamineeSelect(JSONObject)
	{POST [/examination/createFixedExamination]}: createFixedExamination(JSONObject)
	{POST [/examination/createRandomExaminationIncludingExamineeSelect]}: createRandomExaminationIncludingExamineeSelect(JSONObject)
	{POST [/examination/createRandomExamination]}: createRandomExamination(JSONObject)
	{GET [/examination/delete]}: deleteExamination(String)
	{GET [/examination/examinationWrapperListOfCreater]}: getExaminationWrapperListOfCreater(int,int,int,int)
	{GET [/examination/examinationInViewListOfCreater]}: getExaminationInViewListOfCreater(Integer,Integer,Integer,Integer)
	{GET [/examination/getExaminationInViewListOfCreater]}: getExaminationInViewList(Integer,Integer,Integer,int)
	{GET [/examination/getExaminationInViewListAndTotalNumOfCreater]}: getExaminationInViewListAndTotalNumOfCreater(int,int,int,int)
	{GET [/examination/getRecommendExaminations]}: getRecommendExaminations(Integer,Integer,Integer,Integer)
	{GET [/examination/getRecommendExaminationsByProduct]}: getRecommendExaminationsByProduct(Integer,String)
	{POST [/examination/getExaminationInViewListOfCreaterInCompanyByTags]}: getExaminationInViewListOfCreaterInCompanyByTags(JSONObject)
	{POST [/examination/getExaminationInViewListOfCompanyByTags]}: getExaminationInViewListOfCompanyByTags(JSONObject)
	{GET [/examination/ongoingExaminationList]}: getOngoingExaminationList()
	{GET [/examination/getExaminationAndPaperById]}: getExaminationAndPaperById(int)
	{POST [/examination/getExaminationAndPaperByColumns]}: getExaminationAndPaperByColumns(JSONObject)
	{POST [/examination/getExaminationAndPaperByColumnsAfterEncode]}: getExaminationAndPaperByColumnsAfterEncode(JSONObject)
	{POST [/examination/getExaminationDetail]}: getExaminationDetail(JSONObject)
	{GET [/examination/examinationByCode]}: getExaminationByCode(String)
	{GET [/examination/getExaminationListByPaperId]}: getExaminationListByPaperId(Integer)
	{GET [/examination/getExaminationListByKeyword]}: getExaminationListByKeyword(String)
	{GET [/examination/getExaminationListOfCreater]}: getExaminationListOfCreater(Integer,Integer,Integer,Integer)
	{GET [/examination/idByCode]}: getExaminationIdByCode(String)
	{POST [/examination/edit]}: editExamination(JSONObject)
	{GET [/examination/examinationWrapper]}: getExaminationWrapperById(Integer)
	{GET [/examination/getExaminationWrapperSecure]}: getExaminationWrapperSecure(Integer,Integer)
	{GET [/examination/getExaminationWrapperStructure]}: getExaminationWrapperStructure(Integer,Integer)
	{POST [/examination/updateExaminationWrapperStructure]}: updateExaminationWrapperStructure(JSONObject)
	{GET [/examination/getExaminationAndPaperQuestionList]}: getExaminationAndPaperQuestionList(Integer)
	{GET [/examination/getExaminationAndPaperQuestionListAfterEncoded]}: getExaminationAndPaperQuestionListAfterEncoded(Integer)
	{GET [/examination/examinationWrapperWithRedLock]}: examinationWrapperWithRedLock(Integer,String)
	{GET [/examination/suspend]}: suspendExamination(Integer)
	{GET [/examination/resume]}: resumeExamination(Integer)
	{GET [/examination/changeExamineeDisplay]}: changeExamineeDisplay(Integer,Boolean)
	{GET [/examination/rename]}: renameExamination(int,int,String)
	{GET [/examination/renameExamCode]}: renameExamCode(Integer,String)
	{GET [/examination/modifyCode]}: modifyCode(Integer,String)
	{GET [/examination/configAdvancedOptions]}: configAdvancedOptions(String)
	{POST [/examination/saveAdvancedOptions]}: saveAdvancedOptions(JSONObject)
	{GET [/examination/checkIfExceedExaminationTimesLimit]}: checkIfExceedExaminationTimesLimit(Integer,Integer)
	{GET [/examination/copy]}: copyExamination(Integer,Integer,Boolean)
	{GET [/examination/transmit]}: transmitExamination(Integer,Integer,Integer)
	{GET [/examination/getLeftTime]}: getLeftFromBeginTime(Integer)
	{POST [/examination/sendQiyeweixinQkkNotice]}: sendQiyeweixinQkkNotice(JSONObject)
	{POST [/examination/update]}: update(Examination)
	{GET [/examination/reset]}: reset(Integer)
[13:07:56:500] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationExamineeController:
	{GET [/examinationExaminee/getExaminationExaminee]}: getExaminationExaminee(Integer)
	{POST [/examinationExaminee/updateExaminationExaminee]}: updateExaminationExaminee(JSONObject)
[13:07:56:511] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationExamineeSnapshotController:
	{GET [/examinationExamineeSnapshot/insertSnapshot]}: insertSnapshot(Integer,Integer,String,String)
	{GET [/examinationExamineeSnapshot/getUserSnapshotListOfExamination]}: getUserSnapshotListOfExamination(Integer,String,Integer,Integer)
	{GET [/examinationExamineeSnapshot/getUserSnapshotList]}: getUserSnapshotList(Integer,String,Integer,Integer)
	{GET [/examinationExamineeSnapshot/exportSnapshotListOfExamination]}: exportSnapshotListOfExamination(Integer)
	{GET [/examinationExamineeSnapshot/delete]}: delete(String)
[13:07:56:515] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationFavoriteController:
	{GET [/examinationFavorite/add]}: addFavorite(int,int)
	{GET [/examinationFavorite/cancel]}: cancelFavorite(int,int)
	{GET [/examinationFavorite/ifFavorite]}: ifFavorite(int,int)
	{GET [/examinationFavorite/getFavoriteList]}: getFavoriteList(int,int,int)
	{GET [/examinationFavorite/getFavoriteListAndNum]}: getFavoriteListAndNum(int,int,int)
[13:07:56:516] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationGroupExaminationController:
	{ [/examinationGroupExamination/list]}: getExaminationGroupExaminationServiceListById(int)
[13:07:56:545] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationInstanceController:
	{POST [/examinationInstance/getExaminationInstanceList]}: getExaminationInstanceList(JSONObject)
	{[GET, POST] [/examinationInstance/exportRankListOfExaminationInCompany]}: exportRankListOfExaminationInCompany(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/examinationInstance/exportNoRankListOfExaminationInCompany]}: exportNoRankListOfExaminationInCompany(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/examinationInstance/exportDetailRankListOfExamination]}: exportDetailRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{GET [/examinationInstance/getExaminationInstanceWrapperById]}: getExaminationInstanceWrapperById(Integer)
	{POST [/examinationInstance/createExaminationInstance]}: createExaminationInstance(JSONObject,HttpServletRequest)
	{POST [/examinationInstance/createExaminationInstanceWithContentCheck]}: createExaminationInstanceWithContentCheck(JSONObject,HttpServletRequest,HttpServletResponse)
	{GET [/examinationInstance/examinationInstanceWrapperListOfExaminee]}: getExaminationInstanceWrapperListOfExaminee(int,int,int,int)
	{POST [/examinationInstance/getExaminationInstanceWrapperListOfExaminee]}: getExaminationInstanceWrapperListOfExaminee(JSONObject)
	{ [/examinationInstance/getExaminationInstanceInfoById]}: getExaminationInstanceInfoById(Integer)
	{POST [/examinationInstance/getRecentExaminationInstanceListWithAverage]}: getRecentExaminationInstanceListWithAverage(JSONObject)
	{GET [/examinationInstance/getExaminationInstanceListWithUserInfoByExaminationId]}: getExaminationInstanceListWithUserInfoByExaminationId(int,int,int,int)
	{GET [/examinationInstance/examinationInstanceWrapperListOfExamination]}: getExaminationInstanceWrapperListOfExamination(String,int,Integer,Integer)
	{GET [/examinationInstance/rankOfExaminationInstance]}: getRankOfExaminationInstance(int,int,int)
	{GET [/examinationInstance/rankListOfExamination]}: getRankListOfExamination(Integer,Integer,Integer,Integer,Integer)
	{GET [/examinationInstance/getTopTenRankListAndTotalExamineeNumOfExamination]}: getTopTenRankListAndTotalExamineeNumOfExamination(int)
	{GET [/examinationInstance/getLatestTenListAndTotalExamineeNumOfExamination]}: getLatestTenListAndTotalExamineeNumOfExamination(Integer)
	{GET [/examinationInstance/rankListOfExaminationByCode]}: getRankListOfExaminationByCode(String)
	{GET [/examinationInstance/getTotalTimes]}: getTotalTimes(Integer,Integer)
	{GET [/examinationInstance/timesOfExaminationOfExaminee]}: getTimesOfExaminationOfExaminee(int,int,String,String)
	{POST [/examinationInstance/batchDelete]}: batchDeleteByExaminee(JSONArray)
	{POST [/examinationInstance/batchDeleteByAdmin]}: batchDeleteByAdmin(JSONArray)
	{GET [/examinationInstance/examinationInstanceStageSummary]}: getExaminationInstanceStageSummary(int,String,String)
	{GET [/examinationInstance/examinationInstanceStageSummaryList]}: getExaminationInstanceStageSummaryList(String,String,String,int,int,int)
	{POST [/examinationInstance/getExaminationInstanceStageSummaryListByTags]}: getExaminationInstanceStageSummaryList(JSONObject)
	{GET [/examinationInstance/examinationInstanceStageSummaryListExcludingExample]}: getExaminationInstanceStageSummaryListExcludingExample(String,String,String,int,int,int)
	{GET [/examinationInstance/getExaminationInstanceStageSummaryListExcludingCreater]}: getExaminationInstanceStageSummaryListExcludingCreater(String,String,String,int,int,int)
	{GET [/examinationInstance/examinationInstanceStageSummaryDetail]}: getExaminationInstanceStageSummaryDetail(int,String,String,Integer,Integer,String)
	{POST [/examinationInstance/getExaminationStageSummaryDetail]}: getExaminationStageSummaryDetail(JSONObject)
	{GET [/examinationInstance/individualScoreListOfExamination]}: getIndividualScoreListOfExamination(int,int,String,String)
	{GET [/examinationInstance/individualScoreList]}: getIndividualScoreList(int,String,String,int)
	{GET [/examinationInstance/getIndividualRankInfo]}: getIndividualRankInfo(Integer,Integer,Integer)
	{GET [/examinationInstance/getIndividualRankInfoWithDuplicateRemoval]}: getIndividualRankInfoWithDuplicateRemoval(Integer,Integer,Integer)
	{GET [/examinationInstance/getExaminationInstanceWrapperAndProcessById]}: getExaminationInstanceWrapperAndProcessById(Integer)
	{POST [/examinationInstance/getInstanceWrapperAndProcessList]}: getInstanceWrapperAndProcessList(JSONObject)
	{GET [/examinationInstance/notApprovedExaminationInstanceList]}: getNotApprovedExaminationInstanceList(Integer,Integer,Integer,Integer)
	{GET [/examinationInstance/getNotApprovedExaminationInstanceListAndLength]}: getNotApprovedExaminationInstanceListAndLength(Integer,Integer,Integer,Integer)
	{POST [/examinationInstance/approveInstance]}: approveInstance(JSONObject)
	{[GET, POST] [/examinationInstance/exportNoRankListOfExamination]}: exportNoRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/examinationInstance/exportRankListOfExamination]}: exportRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{POST [/examinationInstance/getCountResult]}: getCountResult(JSONObject)
	{[GET, POST] [/examinationInstance/exportDetailRankListOfExaminationInCompany]}: exportDetailRankListOfExaminationInCompany(HttpServletRequest,HttpServletResponse)
	{ [/examinationInstance/exportInstanceOfExaminee]}: exportInstanceOfExaminee(HttpServletRequest,HttpServletResponse)
	{GET [/examinationInstance/exportAbsentList]}: exportAbsentList(HttpServletRequest,HttpServletResponse)
	{GET [/examinationInstance/getAbsentList]}: getAbsentList(Integer)
	{GET [/examinationInstance/getUserListOfExamination]}: getUserListOfExamination(Integer)
	{GET [/examinationInstance/getExaminationAnalysisData]}: getAnalysisData(Integer)
	{GET [/examinationInstance/getDistributionOfScore]}: getDistributionOfScore(Integer)
	{GET [/examinationInstance/getDistributionOfDuration]}: getDistributionOfDuration(Integer)
	{POST [/examinationInstance/getExamineeList]}: getExamineeList(JSONObject)
	{GET [/examinationInstance/delete]}: delete(int,String)
	{POST [/examinationInstance/create]}: create(JSONObject,HttpServletRequest,HttpServletResponse)
[13:07:56:557] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationInstanceEventController:
	{POST [/examinationInstanceEvent/getList]}: getList(JSONObject)
	{POST [/examinationInstanceEvent/getSummaryInfo]}: getSummaryInfo(JSONObject)
	{POST [/examinationInstanceEvent/getExamBehaviorList]}: getExamBehaviorList(JSONObject)
	{POST [/examinationInstanceEvent/insert]}: insert(ExaminationInstanceEvent)
[13:07:56:573] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationInstanceProcessController:
	{GET [/examinationInstanceProcess/examinationInstanceProcessWrapperList]}: getExaminationInstanceProcessWrapperList(int,Integer,Integer,Integer)
	{GET [/examinationInstanceProcess/examinationInstanceProcessWrapperListAndNum]}: examinationInstanceProcessWrapperListAndNum(Integer,Integer,Integer,Integer)
	{GET [/examinationInstanceProcess/wrongQuestionList]}: getWrongQuestionsOfExaminee(int,int,int)
	{GET [/examinationInstanceProcess/rightWrongNumOfExamination]}: getQuestionRightWrongNumOfExamination(int)
	{GET [/examinationInstanceProcess/rightWrongNumOfQuestion]}: getQuestionRightWrongNum(int)
	{GET [/examinationInstanceProcess/getDistributionGroupByAnswerContent]}: getDistributionGroupByAnswerContent(Integer,Integer)
	{GET [/examinationInstanceProcess/getAnalysisOfExamination]}: getAnalysisOfExamination(Integer,Integer,Integer,String)
	{POST [/examinationInstanceProcess/changeQuestionResult]}: changeQuestionResult(JSONObject)
	{GET [/examinationInstanceProcess/exportQuestionAnalysis]}: exportQuestionAnalysis(Integer,HttpServletResponse)
	{POST [/examinationInstanceProcess/performAiScoring]}: performAiScoring(JSONObject)
[13:07:56:581] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationNameListController:
	{GET [/examinationNameList/getList]}: getList(Integer,Integer)
	{POST [/examinationNameList/upInsert]}: upInsert(JSONObject)
	{POST [/examinationNameList/addExaminationNameList]}: addExaminationNameList(JSONObject)
[13:07:56:610] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationStartUpController:
	{GET [/examinationStartUp/getCountGroupByUser]}: getCountGroupByUser(Integer,Integer,Integer)
	{GET [/examinationStartUp/add]}: add(Integer,Integer)
	{GET [/examinationStartUp/delete]}: delete(Integer,Integer)
	{GET [/examinationStartUp/getCount]}: getCount(Integer,Integer)
[13:07:56:644] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationTagsController:
	{POST [/examinationTags/createrExamTags]}: createExamTags(JSONObject)
	{GET [/examinationTags/getAllExamTagsOfCreaterIdInCompany]}: getAllExamTagsOfCreaterIdInCompany(Integer,Integer)
	{GET [/examinationTags/getAllExamTagsExcludingCreaterIdInCompany]}: getAllExamTagsExcludingCreaterIdInCompany(int,int)
	{ [/examinationTags/getAllExamTagsOfCompanyId]}: getAllExamTagsOfCompanyId(int,Boolean)
	{ [/examinationTags/getAllExamTagsOfUserInCompany]}: getAllExamTagsOfUserInCompany(Integer,Integer)
	{ [/examinationTags/getTagsOfExam]}: getTagsOfExam(int)
[13:07:56:646] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationWxGidController:
	{GET [/examinationWxGid/getList]}: getList(Integer)
	{POST [/examinationWxGid/addIfNoExist]}: addEntity(ExaminationWxGid)
	{GET [/examinationWxGid/delete]}: delete(Integer,String)
	{GET [/examinationWxGid/getEntity]}: getEntity(Integer,String)
[13:07:56:649] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookController:
	{GET [/exerciseBook/exerciseBookListOfCreater]}: getExerciseBookListOfCreater(Integer,String,String,String,Integer,Integer,Integer)
	{POST [/exerciseBook/getExerciseBookListOfCreater]}: getExerciseBookListOfCreater(JSONObject)
	{ [/exerciseBook/getExerciseBookList]}: getExerciseBookList(Integer,String,String,String,Integer,Integer,Integer)
	{POST [/exerciseBook/sendQiyeweixinQkkNotice]}: sendQiyeweixinQkkNotice(JSONObject)
	{POST [/exerciseBook/create]}: createExerciseBook(JSONObject)
	{POST [/exerciseBook/createExerciseBookByQuestionTypeAndCategories]}: createExerciseBookByQuestionTypeAndCategories(JSONObject)
	{GET [/exerciseBook/modifyExerciseBook]}: modifyExerciseBook(String,String)
	{GET [/exerciseBook/delete]}: deleteExerciseBook(int)
	{GET [/exerciseBook/deleteExerciseBookAndRelation]}: deleteExerciseBookAndRelation(Integer)
	{GET [/exerciseBook/getRecommendedKeyWords]}: getRecommendedKeyWords()
	{ [/exerciseBook/exerciseBookListOfCreaterIncludingFavorite]}: getExerciseBookListOfCreaterIncludingFavorite(int,int,int,int,int)
	{ [/exerciseBook/exerciseBook]}: getExerciseBookById(int)
	{GET [/exerciseBook/getExampleQuestions]}: getExampleQuestions(Integer)
	{ [/exerciseBook/exerciseBookBeginInfo]}: getExerciseBookBeginInfoById(Integer,Integer)
	{GET [/exerciseBook/getExerciseBookBeginInfoById]}: getKsiteExerciseBookBeginInfoById(Integer,Integer,Integer)
	{GET [/exerciseBook/getExerciseBookIndexPageInfo]}: getExerciseBookIndexPageInfo(Integer)
	{GET [/exerciseBook/getPracticeSummaryInfo]}: getPracticeSummaryInfo(Integer)
	{ [/exerciseBook/exerciseBookWrapper]}: getExerciseBookWrapperById(String)
	{ [/exerciseBook/switchDisplayToExaminee]}: switchDisplayToExaminee(Integer,Boolean)
	{GET [/exerciseBook/advancedExerciseBookWrapper]}: getAdvancedExerciseBookWrapperById(int)
	{POST [/exerciseBook/doExercise]}: doExercise(JSONObject)
	{POST [/exerciseBook/doExerciseSecure]}: doExerciseSecure(JSONObject)
	{GET [/exerciseBook/rename]}: renameExerciseBook(String,int)
	{POST [/exerciseBook/savePracticeSummary]}: savePracticeSummary(JSONObject)
	{GET [/exerciseBook/getCatagoriesAndNum]}: getCatagoriesAndNum(Integer,Integer,String)
	{GET [/exerciseBook/getTagsGroupByDomain]}: getTagsGroupByDomain(Integer,Integer,String)
	{GET [/exerciseBook/getExerciseBookListByCatagory]}: getExerciseBookListByCatagory(Integer,Integer,String,String,String,Integer,Integer)
	{GET [/exerciseBook/getExerciseBookListAndTotalNum]}: getExerciseBookListAndTotalNum(Integer,Integer,String,String,String,Integer,Integer)
	{GET [/exerciseBook/getDailyPracticeResult]}: getDailyPracticeResult(Integer,Integer,Date,Date)
	{GET [/exerciseBook/getRecommendedList]}: getRecommendedList(Integer,Integer,Integer)
	{GET [/exerciseBook/getFavoriteRankList]}: getFavoriteRankList(String,Integer,Integer,Integer,Integer)
	{POST [/exerciseBook/batchUpdate]}: batchUpdate(JSONArray)
	{POST [/exerciseBook/update]}: update(ExerciseBook)
[13:07:56:654] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookExamineeController:
	{GET [/exerciseBookExaminee/getExerciseBookExaminee]}: getExerciseBookExaminee(Integer)
	{POST [/exerciseBookExaminee/updateExerciseBookExaminee]}: updateExerciseBookExaminee(JSONObject)
[13:07:56:656] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookFavorateController:
	{ [/exerciseBookFavorate/addFavorite]}: addInFavoriteList(int,int)
	{ [/exerciseBookFavorate/addBatchFavorite]}: addBatchFavorite(String,int)
	{ [/exerciseBookFavorate/cancelFavorite]}: deleteFavoriteOfUser(Integer,Integer)
	{GET [/exerciseBookFavorate/myFavoriteExerciseBookPage]}: getMyFavoriteExerciseBookPageInfo(Integer,Integer)
	{GET [/exerciseBookFavorate/getMyFavoriteInfo]}: getMyFavoriteInfo(Integer,Integer)
	{ [/exerciseBookFavorate/getSomeoneExerciseBookAndUserPracticeInfo]}: getSomeoneExerciseBookAndUserPracticeInfo(int,int)
	{ [/exerciseBookFavorate/getCompanyExerciseBookAndUserPracticeInfo]}: getCompanyExerciseBookAndUserPracticeInfo(int,int)
	{POST [/exerciseBookFavorate/getCompanyExamineePracticeInfo]}: getCompanyExamineePracticeInfo(JSONObject)
	{GET [/exerciseBookFavorate/getCompanyPracticeSummary]}: getCompanyPracticeSummary(Integer,Integer)
	{ [/exerciseBookFavorate/getEntity]}: getEntity(Integer,Integer)
[13:07:56:657] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookGradeController:
	{POST [/exerciseBookGrade/add]}: add(ExerciseBookGrade)
[13:07:56:658] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookPracticeProcessController:
	{GET [/exerciseBookPracticeProcess/getRankOfInterval]}: getRankOfInterval(Integer,Integer,Integer,String,String,Integer,Integer)
	{GET [/exerciseBookPracticeProcess/getSequenceNoInRankOfInterval]}: getSequenceNoInRankOfInterval(Integer,Integer,Integer,Integer,String,String)
	{GET [/exerciseBookPracticeProcess/getRankInfo]}: getRankInfo(Integer,Integer,Integer,Integer,String,String,Integer,Integer)
[13:07:56:660] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookPracticeSummaryController:
	{GET [/exerciseBookPracticeSummary/exportUserPracticeSummary]}: exportUserPracticeSummary(HttpServletRequest,HttpServletResponse)
	{GET [/exerciseBookPracticeSummary/getRankOfInterval]}: getRankOfInterval(Integer,Integer,Integer,String,String,Integer,Integer)
	{GET [/exerciseBookPracticeSummary/getSequenceNoInRankOfInterval]}: getSequenceNoInRankOfInterval(Integer,Integer,Integer,Integer,String,String)
	{GET [/exerciseBookPracticeSummary/getRankInfo]}: getRankInfo(Integer,Integer,Integer,Integer,String,String,Integer,Integer)
	{GET [/exerciseBookPracticeSummary/getUserPracticeSummary]}: getUserPracticeSummary(Integer,Integer,String,String)
	{POST [/exerciseBookPracticeSummary/getUserPracticeListWithSummary]}: getUserPracticeListWithSummary(JSONObject)
	{POST [/exerciseBookPracticeSummary/getDepartmentPracticeStatistics]}: getDepartmentPracticeStatistics(JSONObject)
[13:07:56:674] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookPraiseController:
	{ [/exerciseBookPraise/add]}: addPraise(int,int)
	{ [/exerciseBookPraise/cancel]}: cancelPraise(int,int)
[13:07:56:675] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookQuestionController:
	{GET [/exerciseBookQuestion/exerciseBookQuestionWrapperList]}: getExerciseBookQuestionsById(int,Integer,Integer)
	{GET [/exerciseBookQuestion/getExerciseBookQuestionWrapperListWithRedLock]}: getExerciseBookQuestionWrapperList(Integer,Integer,Integer)
	{GET [/exerciseBookQuestion/exerciseBookQuestionWrapperListAndTotalNum]}: getExerciseBookQuestionsAndTotalNumById(Integer,Integer,Integer)
	{GET [/exerciseBookQuestion/getDayDayExerciseQuestionList]}: getDayDayExerciseQuestionList(Integer,Integer,Integer,Boolean)
[13:07:56:676] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookSummaryController:
	{GET [/exerciseBookSummary/getSummary]}: getSummary(Integer)
[13:07:56:676] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookUserRecordController:
	{GET [/exerciseBookUserRecord/exportUserPracticeSummary]}: exportUserPracticeSummary(Integer,HttpServletResponse)
	{GET [/exerciseBookUserRecord/updateHavePractisedNum]}: updateHavePractisedNum(Integer,Integer)
	{ [/exerciseBookUserRecord/getExerciseProgress]}: getExerciseBookProgressOfUser(int,int)
[13:07:56:677] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.FileAccessPermissionController:
	{POST [/fileAccessPermission/generateUnlockAdQRCode]}: generateUnlockAdQRCode(JSONObject)
	{POST [/fileAccessPermission/addFileAccessPermission]}: addFileAccessPermission(JSONObject)
	{POST [/fileAccessPermission/getFileAccessPermission]}: getFileAccessPermission(JSONObject)
[13:07:56:679] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.FileController:
	{ [/file/downloadFile]}: downLoad(String,HttpServletResponse,boolean)
	{[GET, POST] [/file/uploadFile]}: uploadFile(HttpServletRequest)
	{[GET, POST] [/file/uploadFileWithCheck]}: uploadFileWithCheck(HttpServletRequest)
	{GET [/file/downloadNetFile]}: downloadNetFile(HttpServletRequest,HttpServletResponse)
	{POST [/file/uploadOssFile]}: uploadOssFile(HttpServletRequest,HttpServletResponse)
[13:07:56:681] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.FreePracticeOfUserController:
	{POST [/freePracticeOfUser/getFreePractice]}: getFreePractice(JSONObject)
	{GET [/freePracticeOfUser/getQuestionListOfFreePractice]}: getQuestionListOfFreePractice(Integer,Integer)
	{GET [/freePracticeOfUser/getSummaryGroupByQuestionType]}: getSummaryGroupByQuestionType(Integer,Integer)
	{GET [/freePracticeOfUser/delete]}: delete(Integer,Integer)
	{POST [/freePracticeOfUser/save]}: save(JSONObject)
[13:07:56:683] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.GameController:
	{GET [/game/getGameWrapper]}: getGameWrapper(Integer)
	{POST [/game/updateInsert]}: updateInsert(JSONObject)
[13:07:56:726] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.GameResultController:
	{GET [/gameResult/getRankInfo]}: getRankInfo(String,Integer,Integer)
[13:07:56:737] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.HomepageUserFollowedController:
	{GET [/homepageUserFollowed/getUserFollowedInfo]}: getUserFollowedInfo(Integer,Integer)
	{GET [/homepageUserFollowed/getUserFollowedList]}: getUserFollowedList(Integer)
	{POST [/homepageUserFollowed/update]}: update(HomepageUserFollowedWrapper)
	{GET [/homepageUserFollowed/delete]}: delete(Integer,Integer)
	{POST [/homepageUserFollowed/save]}: save(HomepageUserFollowedWrapper)
[13:07:56:744] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.InviteRelationController:
	{GET [/inviteRelation/saveInviteRelation]}: saveInviteRelation(int,int,int)
	{GET [/inviteRelation/getRecentInviteRelationAnnouncement]}: getRecentInviteRelationAnnouncement(int)
	{GET [/inviteRelation/getExaminationInstanceListOfInvitee]}: getExaminationInstanceListOfInvitee(int,int,int,int,int)
	{GET [/inviteRelation/getEventResultOfInviter]}: getEventResultOfInviter(int,int,int)
	{GET [/inviteRelation/getResultOfEvent]}: getResultOfEvent(int,int,int,int,int)
	{[GET, POST] [/inviteRelation/exportRankListOfEvent]}: exportRankListOfExamination(HttpServletRequest,HttpServletResponse)
[13:07:56:750] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ManualServiceController:
	{POST [/manualService/addManualImportRecord]}: addManualImportRecord(JSONObject)
	{POST [/manualService/getManualImportRecords]}: getManualImportRecords(JSONObject)
	{POST [/manualService/updateManualImportRecord]}: updateManualImportRecord(ManualServiceRecord)
[13:07:56:753] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MessageController:
	{GET [/message/sendMessage]}: sendMessage(String)
[13:07:56:784] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MessagePushSubscriptionController:
	{POST [/messagePushSubscription/saveIfNotExisted]}: saveIfNotExisted(MessagePushSubscription)
	{GET [/messagePushSubscription/deleteEntity]}: deleteEntity(String,Integer,Integer)
	{POST [/messagePushSubscription/changeBatch]}: changeBatch(JSONObject)
	{POST [/messagePushSubscription/getList]}: getList(JSONObject)
	{GET [/messagePushSubscription/getEntity]}: getEntity(String,Integer,Integer)
[13:07:56:786] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MessageScheduleController:
	{POST [/messageSchedule/deleSave]}: deleSave(JSONObject)
	{POST [/messageSchedule/update]}: updateIgnoreNull(MessageSchedule)
	{GET [/messageSchedule/getList]}: getList(String,Integer,Integer)
	{GET [/messageSchedule/sendMessage]}: sendMessage(String)
[13:07:56:789] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MonitorViewController:
	{GET [/view/{sessionId}]}: viewMonitor(String,Model)
[13:07:56:794] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MultipleMarkingController:
	{GET [/multipleMarking/acceptMultipleMarking]}: acceptMultipleMarking(Integer,Integer)
	{GET [/multipleMarking/getMultipleMarkingExamintionListOfUser]}: getMultipleMarkingExamintionListOfUser(Integer)
	{GET [/multipleMarking/hasMultipleMarkingExaminationInstanceByExaminationInstanceId]}: hasMultipleMarkingExaminationInstanceByExaminationInstanceId(Integer)
	{GET [/multipleMarking/getWorkStateOfMarking]}: getWorkStateOfMarking(Integer,Integer)
	{GET [/multipleMarking/finishMarking]}: finishMarking(Integer)
[13:07:56:796] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.NameListController:
	{GET [/nameList/getList]}: getList(Integer)
	{GET [/nameList/getEntityWrapper]}: getEntityWrapper(Integer)
	{POST [/nameList/update]}: update(JSONObject)
	{GET [/nameList/delete]}: delete(Integer)
	{POST [/nameList/save]}: save(JSONObject)
	{GET [/nameList/getEntity]}: getEntity(Integer)
[13:07:56:800] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.NanXiangController:
	{GET [/nanxiang/check]}: check(String)
	{POST [/nanxiang/save]}: save(NanxiangRegister)
	{POST [/nanxiang/getEntity]}: getEntity(NanxiangRegister)
	{POST [/nanxiang/validate]}: validate(NanxiangRegister)
[13:07:56:802] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.OrderController:
	{GET [/order/getList]}: getList(String,String,Integer,Integer,Integer)
	{POST [/order/createManualOrder]}: createManualOrder(JSONObject)
	{ [/order/create]}: createOrder(JSONObject)
	{ [/order/createCountOrder]}: createCountOrder(JSONObject)
	{POST [/order/createCountOrderByWeChatNative]}: createOrderByWeChatNative(JSONObject)
	{POST [/order/createCountOrderByJSAPI]}: createCountOrderByJSAPI(JSONObject)
	{POST [/order/orderNotify]}: orderNotify(JSONObject)
	{ [/order/getUnconsumedValueOfAccount]}: getUnconsumedValueOfAccount(String,Integer)
	{GET [/order/getOrderById]}: getOrderById(Integer)
[13:07:56:805] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperController:
	{GET [/paper/getPaperById]}: getPaperById(Integer)
	{POST [/paper/batchUpdate]}: batchUpdate(JSONArray)
	{GET [/paper/getPaperWrapperWithRedLock]}: getPaperWrapperWithRedLock(Integer)
	{ [/paper/modifyPaper]}: modifyPaper(String,String)
	{POST [/paper/update]}: updatePaper(Paper)
	{ [/paper/paperListOfCreater]}: getPaperListOfCreater(String,int)
	{GET [/paper/getPaperWithFavoriteInfoAndOrderRelationInfo]}: getPaperWithFavoriteInfoAndOrderRelationInfo(Integer,Integer,Integer)
	{ [/paper/paperWrapper]}: getPaperWrapperById(String)
	{ [/paper/advancedPaperWrapper]}: getAdvancedPaperWrapperById(int)
	{ [/paper/paperListToAdmin]}: getPaperListToAdmin(String,String,int,int)
	{GET [/paper/getCategoriesAndNum]}: getCategoriesAndNum(Integer,Integer,String)
	{GET [/paper/getPaperList]}: getPaperList(Integer,Integer,String,String,String,Integer,Integer)
	{GET [/paper/getPaperListAndTotalNum]}: getPaperListAndTotalNum(Integer,Integer,String,String,String,Integer,Integer)
	{ [/paper/create]}: create(JSONObject)
[13:07:56:807] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperFavoriteController:
	{ [/paperFavorite/add]}: addFavorite(int,int)
	{ [/paperFavorite/cancel]}: cancelFavorite(int,int)
	{ [/paperFavorite/ifFavorite]}: ifFavorite(int,int)
	{ [/paperFavorite/getFavoriteList]}: getFavoriteList(int,int,int)
[13:07:56:809] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperPraiseController:
	{ [/paperPraise/add]}: addPraise(Integer,Integer)
	{ [/paperPraise/cancel]}: cancelPraise(Integer,Integer)
[13:07:56:810] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperQuestionController:
	{ [/paperQuestion/update]}: updateCompletion(BigDecimal,BigDecimal,int,BigDecimal,String,int)
	{ [/paperQuestion/updateSelection]}: updateSelection(BigDecimal,BigDecimal,int,BigDecimal,String,int)
	{GET [/paperQuestion/getQuestionWrapperListAndNum]}: getQuestionWrapperListAndNum(Integer,Integer,Integer)
[13:07:56:814] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperSummaryController:
	{GET [/paperSummary/getSummary]}: getSummary(Integer)
[13:07:56:829] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PassRaceGameController:
	{GET [/passRaceGame/getGameWrapper]}: getGameWrapper(Integer)
	{GET [/passRaceGame/getListAndTotalNum]}: getListAndTotalNum(Integer,Integer,Integer)
	{POST [/passRaceGame/update]}: update(JSONObject)
	{GET [/passRaceGame/delete]}: delete(Integer)
	{POST [/passRaceGame/save]}: save(JSONObject)
[13:07:56:839] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PassRaceResultController:
	{POST [/passRaceResult/save]}: save(JSONObject)
	{GET [/passRaceResult/getGameWrapper]}: getGameWrapper(Integer)
	{GET [/passRaceResult/getListAndTotalNum]}: getListAndTotalNum(Integer,Integer,Integer)
	{POST [/passRaceResult/update]}: update(JSONObject)
	{GET [/passRaceResult/delete]}: delete(Integer)
[13:07:56:855] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PassRaceStageController:
	{GET [/passRaceStage/getStageWrapper]}: getStageWrapper(Integer,Integer)
	{GET [/passRaceStage/getMyPlayedStageList]}: getMyPlayedStageList(Integer,Integer,Boolean)
[13:07:56:920] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PdfFileController:
	{ [/pdfFile/temporaryExaminationReport]}: createFreeExaminationReport(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/pdfFile/examinationReport]}: createExaminationPdfReport(HttpServletRequest,HttpServletResponse)
	{GET [/pdfFile/generatePdfByUser]}: generatePdfByUser(Integer,Integer)
[13:07:56:927] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PermissionAuthorizationController:
	{POST [/permissionAuthorization/changeBatch]}: changeBatch(JSONObject)
	{GET [/permissionAuthorization/getList]}: getList(Integer,Integer)
	{POST [/permissionAuthorization/getAuthorizedDepartmentList]}: getAuthorizedDepartmentList(JSONObject)
	{POST [/permissionAuthorization/getAuthorizedAdminList]}: getAuthorizedAdminList(JSONObject)
	{POST [/permissionAuthorization/getAuthorizedCompanyInfoWith2Grade]}: getAuthorizedCompanyInfoWithRecursionDepartment(JSONObject)
	{GET [/permissionAuthorization/getAuthorizedCompanyPracticeSummary]}: getAuthorizedCompanyPracticeSummary(Integer,Integer)
	{POST [/permissionAuthorization/getAuthorizedCompanyExamineePracticeInfo]}: getAuthorizedCompanyExamineePracticeInfo(JSONObject)
	{POST [/permissionAuthorization/save]}: save(List)
	{GET [/permissionAuthorization/getEntity]}: getEntity(Integer,Integer,Integer)
[13:07:56:931] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PermissionController:
	{GET [/permission/getList]}: getList()
[13:07:56:936] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PointConsumerationController:
	{POST [/pointConsumeration/getUserConsumeList]}: getUserConsumeList(JSONObject)
	{POST [/pointConsumeration/save]}: save(PointConsumeration)
[13:07:56:938] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PointDetailController:
	{GET [/pointDetail/exportPointRankOfCompany]}: exportPointRankOfCompany(HttpServletRequest,HttpServletResponse)
	{GET [/pointDetail/getRankInfo]}: getRankInfo(Integer,Integer,String,String,String,Integer,Integer)
	{POST [/pointDetail/getCompanyRankInfo]}: getCompanyRankInfo(JSONObject)
[13:07:56:939] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PointRuleController:
	{POST [/pointRule/upInsert]}: upInsert(PointRule)
	{GET [/pointRule/getEntity]}: getEntity(Integer)
[13:07:56:939] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PointUserController:
	{GET [/pointUser/getUserPointInfo]}: getUserPointInfo(Integer,Integer)
	{GET [/pointUser/clear]}: clear(Integer,Integer,Integer)
[13:07:56:939] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PracticePushController:
	{GET [/practicePush/list]}: getList(Integer,Integer,String,Integer,Integer,Integer)
	{GET [/practicePush/toggleStatus]}: toggleStatus(Integer,Integer,Integer)
	{GET [/practicePush/detail]}: getDetail(Integer,Integer)
	{GET [/practicePush/checkAndExecutePushTasks]}: checkAndExecutePushTasks()
	{POST [/practicePush/update]}: update(JSONObject)
	{GET [/practicePush/delete]}: delete(Integer,Integer)
	{POST [/practicePush/create]}: create(JSONObject)
[13:07:56:941] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductController:
	{ [/product/getProductList]}: getProductList(String)
	{GET [/product/getListOfProduct]}: getListOfProduct(String)
[13:07:56:944] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductCountTransactionController:
	{POST [/productCountTransaction/getList]}: getList(JSONObject)
[13:07:56:945] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductIntroductionController:
	{ [/productIntroduction/list]}: getProductIntroductionWrapperList()
[13:07:56:947] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductOrderRelationController:
	{GET [/productOrderRelation/getProductOrderRelation]}: getProductOrderRelation(String,Integer)
	{POST [/productOrderRelation/updateProductOrderRelation]}: updateProductOrderRelation(ProductOrderRelation)
	{GET [/productOrderRelation/getProductOrderRelationWrapper]}: getProductOrderRelationWrapper(String,Integer)
	{GET [/productOrderRelation/checkIfOverdue]}: checkIfOverdue(String,Integer)
	{GET [/productOrderRelation/refresh]}: refresh(Integer)
[13:07:56:953] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductOrderRelationCountController:
	{GET [/productOrderRelationCount/getProductOrderRelationCount]}: getProductOrderRelationCount(String,Integer,String)
	{POST [/productOrderRelationCount/getProductOrderRelationCountList]}: getProductOrderRelationCountList(JSONObject)
	{GET [/productOrderRelationCount/tryToConsumeVADVIP]}: tryToConsumeVADVIP(Integer,Integer)
	{GET [/productOrderRelationCount/consumeVADVIP]}: consumeVADVIP(Integer,Integer)
	{GET [/productOrderRelationCount/consume]}: consume(String,Integer,String,Integer)
[13:07:56:956] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QrcodeScanEntryController:
	{GET [/qrcodeScan/checkIfFollowed]}: checkIfFollowed(HttpServletRequest)
[13:07:56:956] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionBankQuestionController:
	{POST [/questionBankQuestion/changeQuestionBank]}: changeQuestionBank(JSONObject)
	{GET [/questionBankQuestion/deleteQuestionBankQuestion]}: deleteQuestionBankQuestion(Integer)
[13:07:56:958] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionCompositionChildController:
	{POST [/compositionChild/getPaperQuestionListMapByIds]}: getListByIds(JSONObject)
	{POST [/compositionChild/getListByIds]}: getListByIds(JSONArray)
	{GET [/compositionChild/getList]}: getList(Integer)
	{POST [/compositionChild/updateChildQuestionEntity]}: updateChildQuestionEntity(QuestionCompositionChild)
[13:07:56:994] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionController:
	{[GET, POST] [/question/uploadFile]}: uploadFile(HttpServletRequest)
	{[GET, POST] [/question/uploadFileWithCheck]}: uploadFileWithCheck(HttpServletRequest)
	{POST [/question/questionListOfCreater]}: getQuestionListOfCreater(JSONObject)
	{GET [/question/exportQuestions]}: exportQuestions(HttpServletRequest,HttpServletResponse)
	{POST [/question/saveBatch]}: saveBatch(JSONObject)
	{POST [/question/getQuestionsByGroupConditionAdvanced]}: getQuestionsByGroupConditionAdvanced(JSONObject)
	{GET [/question/getCategoryList]}: getCategoryList(Integer,String,Integer)
	{POST [/question/updateQuestion]}: updateQuestion(Question)
	{[GET, POST] [/question/modifyQuestion]}: modifyQuestion(HttpServletRequest)
	{GET [/question/deleteQuestion]}: deleteQuestion(String)
	{POST [/question/deleteQuestionByQuery]}: deleteQuestion(JSONObject)
	{GET [/question/deleteQuestionList]}: deleteQuestionList(String)
	{POST [/question/deleteBatchQuestion]}: deleteBatchQuestion(JSONObject)
	{GET [/question/deleteQuestionIncludingExample]}: deleteQuestionIncludingExample(int,int)
	{GET [/question/questionListOfCreaterByCategory]}: getQuestionListOfCreaterByCategory(String,String,int)
	{GET [/question/questionListOfCreaterByType]}: getQuestionListOfCreaterByType(String,String,int,int,int)
	{GET [/question/questionListOfCreaterByTypeIncludingExample]}: getQuestionListOfCreaterByTypeIncludingExample(String,String,int,int,int)
	{GET [/question/questionWrapperListOfCreaterByType]}: getQuestionWrapperListOfCreaterByType(String,String,String,String,int,int,int)
	{[GET, POST] [/question/questionWrapperListOfCreaterByTypeIncludingExample]}: getQuestionWrapperListOfCreaterByTypeIncludingExample(String,String,int,int,int)
	{GET [/question/questionListForPractice]}: getQuestionListOfCreaterForPractice(String,String,String,int,int,int)
	{GET [/question/question]}: getQuestionById(String)
	{GET [/question/questionWrapper]}: getQuestionWrapperById(String)
	{GET [/question/getQuestionWrapper]}: getQuestionWrapper(String)
	{POST [/question/importQuestionUnion], produces [application/json;charset=utf-8]}: importQuestionUnion(HttpServletRequest)
	{POST [/question/importWordQuestion], produces [application/json;charset=utf-8]}: importWordQuestion(HttpServletRequest)
	{[GET, POST] [/question/importExcelQuestion], produces [application/json;charset=utf-8]}: importExcelQuestion(HttpServletRequest)
	{GET [/question/modifyDefaultMark]}: modifyDefaultMarkOfQuestions(String,BigDecimal)
	{GET [/question/modifyCatagory]}: modifyCatagory(String,String)
	{POST [/question/modifyQuestionCatagory]}: modifyCatagory(JSONObject)
	{GET [/question/categoryAndTotalNum]}: getCategoryAndTotalNum(Integer,String,Integer)
	{POST [/question/getQuestionTypeAndCategoryAndTotalNum]}: getQuestionTypeAndCategoryAndTotalNum(JSONObject)
	{GET [/question/questionTypeAndCategoryAndTotalNum]}: getQuestionTypeAndCategoryAndTotalNum(Integer,Integer,Integer)
	{POST [/question/getQuestionTypeAndCategoryAndTotalNumWithPermission]}: getQuestionTypeAndCategoryAndTotalNumWithPermission(JSONObject)
	{GET [/question/getQuestionCatagoryAndTotalNum]}: getQuestionCatagoryAndTotalNum(Integer,int)
	{POST [/question/getQuestionCatagoryAndTotalNumWithPermission]}: getQuestionCatagoryAndTotalNumWithPermission(JSONObject)
	{GET [/question/machineChooseQuestion]}: getMachineChooseQuestion(int,String,int,int)
	{POST [/question/getQuestionsByGroupConditionAdvancedWithPermission]}: getQuestionsByGroupConditionAdvancedWithPermission(JSONObject)
	{GET [/question/getQuestionsByGroupCondition]}: getQuestionsAndLengthByGroupCondition(Integer,String,String,String,int,Integer,Integer)
	{GET [/question/getQuestionsList]}: getQuestionsList(Integer,String,String,String,int,Integer,Integer)
	{POST [/question/checkDuplicates]}: checkDuplicates(DuplicateCheckRequest)
	{[GET, POST] [/question/createQuestion]}: create(HttpServletRequest)
[13:07:56:998] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionFavoriteController:
	{ [/questionFavorite/add]}: addFavorite(int,int)
	{ [/questionFavorite/cancel]}: cancelFavorite(int,int)
	{ [/questionFavorite/ifFavorite]}: ifFavorite(int,int)
[13:07:56:999] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionRandomController:
	{POST [/questionRandom/modify]}: modify(JSONObject)
	{GET [/questionRandom/deleteQuestionList]}: deleteQuestionList(String)
	{POST [/questionRandom/createByPost]}: createByPost(JSONObject)
	{GET [/questionRandom/list]}: getQuestionRandomListByCreaterIdInCompany(Integer,int,int,int)
	{GET [/questionRandom/questionRandomExtend]}: getQuestionRandomExtend(int)
	{GET [/questionRandom/delete]}: delete(int)
	{GET [/questionRandom/create]}: create(String,String)
[13:07:57:001] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionSummaryController:
	{POST [/questionSummary/getWrongQuestionList]}: getWrongQuestionList(JSONObject)
	{POST [/questionSummary/removeWrongQuestion]}: removeWrongQuestion(JSONObject)
[13:07:57:207] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.SMSNoticeController:
	{GET [/notice/sendValidCodeSMS]}: sendValidCodeSMS(String,String)
	{GET [/notice/sendQKKRegisterNotice]}: sendQKKRegisterNotice(String,String)
	{GET [/notice/sendQKKTestOverdueNotice]}: sendQKKTemplateNotice(Integer)
	{GET [/notice/sendQKKTimeingNotice]}: sendQKKTimeingNotice(String)
[13:07:57:222] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.SoftVersionController:
	{GET [/version/reloadVersion]}: reloadVersion(String)
	{GET [/version/setSystemRunningState]}: setSystemRunningState(String)
	{GET [/version/setAdProvider]}: setAdProvider(String)
	{GET [/version/getSystemSettings]}: getSystemSettings(String)
	{GET [/version/setProductSystemRunningState]}: setProductSystemRunningState(String,String)
	{GET [/version/getProductSystemSettings]}: getProductSystemSettings(String)
	{GET [/version/getVersion]}: getVersion(String)
[13:07:57:228] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.SysUserRoleController:
	{GET [/sysUserRole/getSysUserRoleListOfCompany]}: getSysUserRoleListOfCompany(Integer,Integer)
	{POST [/sysUserRole/addSysUserRole]}: addSysUserRole(JSONObject)
	{GET [/sysUserRole/getSysUserRoleListOfUser]}: getSysUserRoleListOfUser(Integer,String,Integer)
	{GET [/sysUserRole/unbindExamineeUserRole]}: unbindExamineeUserRole(Integer,Integer)
	{POST [/sysUserRole/unbindExamineeUserRoleWithPermission]}: unbindExamineeUserRoleWithPermission(JSONObject)
	{GET [/sysUserRole/unbindAdminUserRole]}: unbindAdminUserRole(Integer,Integer)
	{POST [/sysUserRole/unbindAdminUserRoleWithPermission]}: unbindAdminUserRoleWithPermission(JSONObject)
	{POST [/sysUserRole/getSysUserRoleList]}: getSysUserRoleList(JSONObject)
	{GET [/sysUserRole/getSysUserRoleListByUsernameAndPassword]}: getSysUserRoleListByPhoneAndPassword(String,String,String,HttpServletRequest)
	{GET [/sysUserRole/getNewSysUserRoleList]}: getNewSysUserRoleList(String,String,String,HttpServletRequest)
	{GET [/sysUserRole/getSysUserRoleListOfUserInCompany]}: getSysUserRoleListOfUserInCompany(Integer,Integer,String)
	{GET [/sysUserRole/getCompanyInfoByAdminUserId]}: getCompanyInfoByAdminUserId(Integer)
	{GET [/sysUserRole/transmitAdminToOther]}: transmitAdminToOther(Integer,Integer,Integer)
	{POST [/sysUserRole/batchAddChildAdmin]}: batchAddChildAdmin(JSONObject)
[13:07:57:231] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.SystemMessageController:
	{GET [/systemMessage/batchDelete]}: batchDelete(String,String,String)
	{GET [/systemMessage/handleSystemMessage]}: handleSystemMessage()
	{GET [/systemMessage/changeDepartmentId]}: changeDepartmentId(Integer)
	{POST [/systemMessage/insert]}: insert(SystemMessage)
[13:07:57:234] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.TaxIdentityInfoController:
	{ [/taxIdentity/createTaxIdentityInfo]}: createTaxIdentityInfo(String)
	{ [/taxIdentity/modifyTaxIdentityInfo]}: modifyTaxIdentityInfo(String)
	{ [/taxIdentity/taxIdentityInfo]}: getTaxIdentityInfoByUserId(String,String)
[13:07:57:241] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.TaxSheetApplicationController:
	{ [/taxSheetApplication/createTaxSheetApplication]}: createTaxSheetApplication(String)
	{ [/taxSheetApplication/taxSheetApplicationList]}: getTaxSheetApplicationListOfUser(String)
	{ [/taxSheetApplication/taxSheetApplication]}: getTaxSheetApplicationById(String)
[13:07:57:243] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.TemplateController:
	{GET [/template/getMyTemplates]}: getMyTemplates(int,String)
[13:07:57:245] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.TestController:
	{GET [/test/api]}: apiAccess()
	{GET [/test/getKeysByPrefix]}: getKeysByPrefix(String)
	{GET [/test/deleteByPrex]}: deleteByPrex(String)
[13:07:57:252] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UniversalOrderController:
	{GET [/univsersalOrder/getList]}: getList(String,String,Integer,Integer,Integer)
	{ [/univsersalOrder/create]}: createOrder(JSONObject)
	{POST [/univsersalOrder/createOrderByWeChatNative]}: createOrderByWeChatNative(JSONObject)
	{POST [/univsersalOrder/createCountOrderByJSAPI]}: createCountOrderByJSAPI(JSONObject)
	{POST [/univsersalOrder/orderNotify]}: orderNotify(JSONObject)
	{GET [/univsersalOrder/getOrderById]}: getOrderById(Integer)
[13:07:57:253] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UniversalProductOrderRelationController:
	{POST [/universalProductOrderRelation/getProductOrderRelation]}: getProductOrderRelation(JSONObject)
[13:07:57:258] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserCompanyController:
	{GET [/userCompany/getCompanyInfoByUserId]}: getCompanyInfoByUserId(int)
	{GET [/userCompany/deleteUserOfCompany]}: deleteUserOfCompany(int,int)
	{POST [/userCompany/getUserInfoListByIds]}: getUserInfoListByIds(JSONObject)
	{POST [/userCompany/updateUserInfoAndCompanyInfo]}: saveUserInfoAndCompanyInfo(JSONObject)
	{GET [/userCompany/saveUserInfoAndCompanyInfo]}: saveUserInfoAndCompanyInfo(int,int,int,String,String)
	{POST [/userCompany/bindCompany]}: bindCompany(JSONObject)
	{POST [/userCompany/deleteUserOfCompanyWithPermission]}: deleteUserOfCompanyWithPermission(JSONObject)
	{POST [/userCompany/deleteBatch]}: deleteBatchWithPermission(JSONObject)
	{GET [/userCompany/deleteExamineeByDepartmentName]}: deleteExamineeByDepartmentName(Integer,String)
	{POST [/userCompany/deleteExamineeByDepartmentNameWithPermission]}: deleteExamineeByDepartmentNameWithPermission(JSONObject)
	{GET [/userCompany/getUserListByQkkQiyeCorpid]}: getUserListByQkkQiyeCorpid(String,String)
	{GET [/userCompany/getCompanyInfoByUserIdAndCompanyId]}: getUserCompanyInfoByUserIdAndCompanyId(int,int)
	{GET [/userCompany/getUserCompanyByPhone]}: getUserCompanyByPhone(String)
	{GET [/userCompany/getUserInfoWithCompanyInfo]}: getUserInfoWithCompanyInfo(Integer,Integer)
	{GET [/userCompany/getUserListByDepartmentId]}: getUserListByDepartmentId(Integer,Integer,Integer,Integer,Integer,Integer)
	{POST [/userCompany/getCompanyUserListByMap]}: getCompanyUserListByMap(JSONObject)
	{GET [/userCompany/getUserListOfDepartmentsInCludingSelf]}: getUserListOfDepartmentsInCludingSelf(String)
	{GET [/userCompany/getUserListOfDepartmentsExcludingSelf]}: getUserListOfDepartmentsExcludingSelf(String)
	{POST [/userCompany/getUserList]}: getUserListByName(JSONObject)
	{GET [/userCompany/getUserListByName]}: getUserListByName(String,Integer,Integer,Integer,Integer)
	{GET [/userCompany/ifRegisted]}: getIfRegisted(int,int)
	{GET [/userCompany/getUserListOfCompany]}: getUserListOfCompany(Integer,String,String,Integer,Integer)
	{GET [/userCompany/secureGetUserListOfCompany]}: secureGetUserListOfCompany(Integer,String,String,Integer,Integer,Integer,Integer,Integer,Integer)
	{GET [/userCompany/getGrantedUserListOfCompany]}: getGrantedUserListOfCompany(Integer,String,String,Integer,Integer,Integer,Integer,String,Integer,Integer,String,String)
	{POST [/userCompany/getCompanyUserList]}: getCompanyUserList(JSONObject)
	{GET [/userCompany/getExamineeStatisticsOfCompany]}: getExamineeStatisticsOfCompany(Integer)
	{GET [/userCompany/exportUserListOfCompany]}: exportUserListOfCompany(Integer,Integer,HttpServletResponse)
	{GET [/userCompany/checkIfExceedMemberNumLimit]}: checkIfExceedMemberNumLimit(Integer,Integer)
	{POST [/userCompany/approvedRegist]}: approvedRegist(UserCompany)
	{POST [/userCompany/importUser]}: importUser(HttpServletRequest)
[13:07:57:296] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserController:
	{GET [/user/getUserInfoByOpenId]}: getUserByEteaOpenId(String,String)
	{GET [/user/getUserInfoById]}: getUserInfoById(int)
	{GET [/user/getUserInfoListByNickName]}: getUserInfoListByNickName(String)
	{POST [/user/supplyPhoneNo]}: supplyPhoneNo(JSONObject)
	{GET [/user/loginByValidationCode]}: loginByValidationCode(String,String,HttpServletRequest)
	{GET [/user/validateSMSPhoneIfMatched]}: validateSMSPhoneIfMatched(String,String)
	{GET [/user/oneKeyLogin]}: oneKeyLogin(String,HttpServletRequest)
	{GET [/user/login]}: loginByPhoneAndPassword(String,String,HttpServletRequest)
	{GET [/user/loginById]}: loginById(Integer)
	{POST [/user/getUserInfoByUserIdAndCompanyId]}: getUserInfoByUserIdAndCompanyId(JSONObject,HttpServletRequest)
	{POST [/user/loginWithAutoRegist]}: loginWithAutoRegist(JSONObject,HttpServletRequest)
	{GET [/user/changePassword]}: changePassword(Integer,String,String)
	{GET [/user/updateUserInfo]}: updateUserInfo(String)
	{GET [/user/getUserByPhone]}: getUserByPhone(String)
	{POST [/user/getUserInfoListByIds]}: getUserInfoListByIds(List)
	{GET [/user/getUserListByQkkQiyeCorpid]}: getUserListByQkkQiyeCorpid(String,String)
	{GET [/user/getUserInfoWithCompanyInfo]}: getUserInfoWithCompanyInfo(Integer,Integer)
	{POST [/user/register]}: register(JSONObject)
	{POST [/user/update]}: update(User)
	{POST [/user/getPassword]}: getPassword(JSONObject)
[13:07:57:314] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserDeviceBindingController:
	{POST [/userDeviceBinding/getUserDeviceBinding]}: getUserDeviceBinding(JSONObject)
	{POST [/userDeviceBinding/bindDevice]}: bindDevice(JSONObject)
	{POST [/userDeviceBinding/unbindDevice]}: unbindDevice(JSONObject)
[13:07:57:355] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserExaminationPerformanceReportController:
	{ [/examinationPerformanceReport/consume]}: consume(Integer,Integer)
[13:07:57:438] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserFormController:
	{GET [/userForm/deleteForm]}: deleteFormOfUser(Integer,Integer)
	{GET [/userForm/getUserFormListByUserId]}: getUserFormListByUserId(Integer)
	{POST [/userForm/create]}: create(UserForm)
[13:07:57:448] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserFormFlowRecordController:
	{POST [/userFormFlowRecord/upsert]}: upsert(UserFormFlowRecord)
	{GET [/userFormFlowRecord/getFormFlowRecordWrapperById]}: getFormFlowRecordWrapper(Integer,Integer)
[13:07:57:449] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserOfYncxController:
	{ [/userOfYncx/exportDetailRankListOfExamination]}: exportDetailRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/userOfYncx/exportRankListOfExamination]}: exportRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{GET [/userOfYncx/exportExaminationInstanceDetailExcelBundle]}: exportExaminationInstanceDetailExcelBundle(Integer,Integer)
	{GET [/userOfYncx/exportExaminationInstancePdfBundle]}: exportExaminationInstancePdfBundle(Integer,Integer)
	{[GET, POST] [/userOfYncx/examinationReport]}: createExaminationPdfReport(HttpServletRequest,HttpServletResponse)
[13:07:57:450] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserStatisticController:
	{ [/userStatistic/totalItemNum]}: totalNumber(Integer,Integer)
	{ [/userStatistic/getOnlineUserNumberOfExamination]}: getOnlineUserNumberOfExamination(int,int)
[13:07:57:450] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserSummaryController:
	{POST [/userSummary/upInsert]}: upInsert(JSONObject)
	{POST [/userSummary/getEntity]}: getEntity(UserSummary)
[13:07:57:451] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserViewPerformanceWithoutAdController:
	{GET [/userViewPerformanceWithoutAd/insertIfNotExisted]}: insertIfNotExisted(Integer,Integer,Integer,String)
	{GET [/userViewPerformanceWithoutAd/adEndedCallback]}: adEndedCallback(String,String)
	{POST [/userViewPerformanceWithoutAd/deletePermently]}: deletePermently(JSONObject)
	{ [/userViewPerformanceWithoutAd/checkIfAllowUserViewPerformanceWithoutAd]}: checkIfAllowUserViewPerformanceWithoutAd(int,int,int)
	{ [/userViewPerformanceWithoutAd/getUserViewPerformanceWithoutAdTransaction]}: getUserViewPerformanceWithoutAdTransaction(String,int,String,Integer,Integer)
[13:07:57:468] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WeChatMiniProgramController:
	{POST [/weixinAccount/getQRCode]}: getQRCode(JSONObject)
	{POST [/weixinAccount/imgSecCheck]}: imgSecCheck(HttpServletRequest)
	{POST [/weixinAccount/imgSecCheckByMultipart], consumes [multipart/form-data]}: imgSecCheck(MultipartFile,String)
	{POST [/weixinAccount/msgSecCheck]}: msgSecCheck(JSONObject)
	{GET [/weixinAccount/getAccessToken]}: getAccessTokenOfAccount(String)
	{POST [/weixinAccount/registWithCode]}: registWithCode(JSONObject)
	{GET [/weixinAccount/autoLogin]}: autoLogin(String,String,HttpServletRequest)
	{GET [/weixinAccount/loginWithoutUserInfoAndAutoRegist]}: loginWithoutUserInfoAndAutoRegist(String,String,HttpServletRequest)
	{GET [/weixinAccount/login]}: loginWithAutoRegister(String,String,HttpServletRequest)
	{POST [/weixinAccount/loginWithUserAuthNoRegistByPost]}: loginWithUserAuthWithoutAutoRegistByPost(JSONObject,HttpServletRequest)
	{POST [/weixinAccount/loginWithAuth]}: loginWithAuth(JSONObject,HttpServletRequest)
	{POST [/weixinAccount/getUserInfoByWeixinCode]}: getUserInfoByWeixinCode(JSONObject)
	{POST [/weixinAccount/loginWithUserAuthByPost]}: loginWithUserAuthWithAutoRegistByPost(JSONObject,HttpServletRequest)
	{GET [/weixinAccount/loginWithUserAuth]}: loginWithUserAuthWithAutoRegistByGet(String,String,String,String,String,HttpServletRequest)
	{POST [/weixinAccount/getQRCodeBase64ByLimit]}: getQRCodeBase64ByLimit(JSONObject)
	{POST [/weixinAccount/getQRCodeBase64]}: getQRCodeBase64(JSONObject)
	{POST [/weixinAccount/getPhoneNumByPost]}: getPhoneNumByPost(JSONObject)
	{POST [/weixinAccount/getPhoneNum]}: getPhoneNum(JSONObject)
	{POST [/weixinAccount/getPhoneNumNew]}: getPhoneNumNew(JSONObject)
	{POST [/weixinAccount/getPhoneNumWithoutGetSession]}: getPhoneNumWithoutGetSession(JSONObject)
	{POST [/weixinAccount/searchProduct]}: searchProduct(JSONObject)
	{GET [/weixinAccount/getCouponList]}: getCoupon(String)
	{GET [/weixinAccount/receivedCouple]}: receivedCouple(String,String,String)
	{GET [/weixinAccount/getUserCoupleList]}: getUserCoupleList(String,String,String)
	{GET [/weixinAccount/session]}: getSession(String,String)
	{POST [/weixinAccount/getProductList]}: getProductList(JSONObject)
	{POST [/weixinAccount/getGroupId]}: getGroupId(JSONObject)
	{POST [/weixinAccount/bind]}: bind(JSONObject)
[13:07:57:598] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WeChatOfficialAccountController:
	{GET [/wx/loginWithAutoRegister]}: loginWithAutoRegister(String,String)
	{GET [/wx/getJSSDKConfiguration]}: getJSSDKConfiguration(String,String)
	{GET [/wx/getTicket]}: getTicket(HttpServletRequest)
	{GET [/wx/loginByWeChatOfficialAccount]}: loginByWeChatOfficialAccount(String,String)
	{GET [/wx/getPageAuthAccessToken]}: getPageAuthAccessToken(String,String)
	{GET [/wx/getTicketOfProduct]}: getTicketOfProduct(HttpServletRequest)
	{GET [/wx/createAccountMenu]}: createAccountMenu(String)
	{GET [/wx/receiveMessage]}: doGet(HttpServletRequest)
	{POST [/wx/receiveMessage], produces [application/xml;charset=utf-8]}: processRequest(HttpServletRequest)
	{GET [/wx/checkIfFollowed]}: getQrcodeScanEntry(HttpServletRequest)
[13:07:57:599] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WordFileController:
	{GET [/wordFile/exportExamPaper]}: exportExamPaper(HttpServletRequest,HttpServletResponse)
	{GET [/wordFile/exportWrongQuestions]}: exportWrongQuestions(HttpServletRequest,HttpServletResponse)
	{GET [/wordFile/exportExercisePaper]}: exportExercisePaper(HttpServletRequest,HttpServletResponse)
[13:07:57:599] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WrongBookController:
	{POST [/wrongBook/update]}: updateIgnoreNull(JSONObject)
	{GET [/wrongBook/list]}: getWrongBookList(Integer)
	{POST [/wrongBook/create]}: createWrongBook(JSONObject)
	{GET [/wrongBook/deletePermanently]}: deletePermanently(Integer)
	{POST [/wrongBook/getWrongBookQuestionList]}: getWrongBookQuestionList(JSONObject)
	{POST [/wrongBook/removeQuestion]}: removeQuestion(JSONObject)
[13:07:57:600] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WrongQuestionBookController:
	{POST [/wrongQuestionBook/doReview]}: doReview(JSONObject)
	{ [/wrongQuestionBook/wrongQuestionBookPageInfo]}: getWrongQuestionBookPageInfoOfUser(int,int)
	{ [/wrongQuestionBook/getWrongQuestionBookOfUser]}: getWrongQuestionBookOfUser(Integer)
	{POST [/wrongQuestionBook/batchSaveWrongQuestionBook]}: batchSaveWrongQuestionBook(JSONObject)
	{GET [/wrongQuestionBook/delete]}: delete(Integer)
[13:07:57:600] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WrongQuestionBookQuestionController:
	{ [/wrongQuestionBookQuestion/markAsReviewed]}: markAsReviewed(int,int)
	{ [/wrongQuestionBookQuestion/getQuestionWrapperListOfWrongQuestionBook]}: getQuestionWrapperListOfWrongQuestionBook(int)
	{ [/wrongQuestionBookQuestion/getQuestionWrapperListOfBook]}: getQuestionWrapperListOfBook(Integer)
	{GET [/wrongQuestionBookQuestion/getTotalWrongQuestionWrapperList]}: getTotalWrongQuestionWrapperList(Integer,Integer)
	{GET [/wrongQuestionBookQuestion/deleteByQuery]}: deleteByQuery(Integer,Integer)
[13:07:57:600] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WrongQuestionBookReviewSummaryController:
	{POST [/wrongQuestionBookReviewSummary/saveSummary]}: saveSummary(JSONObject)
[13:07:57:600] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WxMiniprogramSubscribeMessageOfEteaController:
	{GET [/wxMiniprogramSubscribeMessageOfEtea/batchSendSubscribedMessage]}: batchSendSubscribedMessage(Integer,Integer,Integer)
	{POST [/wxMiniprogramSubscribeMessageOfEtea/subscribe]}: subscribe(JSONObject)
[13:07:57:601] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.c.p.ExaminationControllerOfPC:
	{POST [/examination/getExaminationInViewListOfCreaterByTags]}: getExaminationInViewListOfCreaterByTags(JSONObject)
	{GET [/examination/examinationInViewListOfCreaterFromPC]}: getExaminationInViewListOfCreaterFromPC(Integer,Integer,Integer,Integer)
	{POST [/examination/getExaminationInViewListAndNum]}: getExaminationInViewListAndNum(JSONObject)
	{POST [/examination/getExaminationInViewListOfCreaterByTagsFromPC]}: getExaminationInViewListOfCreaterByTagsFromPC(JSONObject)
	{POST [/examination/getExaminationInViewListExcludingCreaterByTagsFromPC]}: getExaminationInViewListExcludingCreaterByTagsFromPC(JSONObject)
	{GET [/examination/getExaminationListAndSizeOfWillMark]}: getExaminationListAndSizeOfWillMark(Integer,Integer,Integer,Integer)
	{POST [/examination/getWillMarkExaminationListAndSize]}: getWillMarkExaminationListAndSize(JSONObject)
[13:07:57:603] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.c.p.ExaminationInstanceControllerOfPC:
	{GET [/examinationInstance/getExaminationInstanceListAndSizeWithUserInfoByExaminationId]}: getExaminationInstanceListWithUserInfoByExaminationId(Integer,Integer,Integer,String,Integer,Integer)
	{GET [/examinationInstance/examinationInstanceStageSummaryListFromPC]}: getExaminationInstanceStageSummaryListFromPC(String,String,String,int,int,int)
	{GET [/examinationInstance/getExaminationInstanceStageSummaryListAndTotalNumFromPC]}: getExaminationInstanceStageSummaryListAndTotalNumFromPC(String,String,String,int,int,int)
	{POST [/examinationInstance/getExaminationInstanceStageSummaryListAndNum]}: getExaminationInstanceStageSummaryListAndNum(JSONObject)
	{POST [/examinationInstance/getExaminationInstanceStageSummaryListAndLengthByTags]}: getExaminationInstanceStageSummaryListByTagsFromPC(JSONObject)
	{POST [/examinationInstance/getListAndSizeOfExaminationInstanceWithUserInfoByConditions]}: getListAndSizeOfExaminationInstanceWithUserInfoByConditions(JSONObject)
[13:07:57:605] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.c.p.PcPageDataController:
	{GET [/pageData/getCompanyCoreSummary]}: getCompanyCoreSummary(Integer,Integer)
[13:07:57:606] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.c.PageDataControllerOfCooperation:
	{GET [/pageDateControllerOfCooperation/getRankData]}: getRankData(Integer,Integer,Integer,Integer,String,String,Integer,Integer)
[13:07:57:606] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.e.ExaminationInstanceControllerOfEtea:
	{GET [/examinationInstanceControllerOfEtea/exportTotalScoreGroupByUser]}: exportTotalScoreGroupByUser(String,HttpServletResponse)
	{GET [/examinationInstanceControllerOfEtea/getUserTakenExaminations]}: getUserTakenExaminations(Integer,Integer,Integer)
	{GET [/examinationInstanceControllerOfEtea/getRecentOnGoingExaminations]}: getRecentOnGoingExaminations(Integer)
[13:07:57:606] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.e.PageDataControllerOfEtea:
	{POST [/pageDataOfEtea/getTotalScoreGroupByUser]}: getTotalScoreGroupByUser(JSONObject)
	{GET [/pageDataOfEtea/getHomePageSummaryInfo]}: getHomePageSummaryInfo(Integer)
[13:07:57:607] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.f.FormController:
	{POST [/formSystem/createForm]}: createForm(JSONObject)
	{GET [/formSystem/getFormWrapperById]}: getFormWrapperById(Integer)
	{POST [/formSystem/saveFormFlowRecord]}: saveFormFlowRecord(JSONObject)
	{POST [/formSystem/updateForm]}: updateForm(JSONObject)
[13:07:57:607] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteContentAccessController:
	{POST [/ksiteContentAccess/getContentAccessList]}: getContentAccessList(JSONObject)
	{POST [/ksiteContentAccess/add]}: add(KsiteContentAccess)
[13:07:57:608] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteFavoriteController:
	{POST [/ksiteFavorite/getFavoriteList]}: getFavoriteList(JSONObject)
	{POST [/ksiteFavorite/deleteByColumns]}: deleteByColumns(KsiteFavorite)
	{GET [/ksiteFavorite/getFavorite]}: getFavorite(Integer,String,Integer)
	{POST [/ksiteFavorite/add]}: add(KsiteFavorite)
[13:07:57:609] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteOrderController:
	{ [/ksiteOrder/createOrder]}: createKsiteOrder(JSONObject)
	{POST [/ksiteOrder/getOrderListWithKsiteInfo]}: getOrderListWithKsiteInfo(JSONObject)
	{POST [/ksiteOrder/getOrderList]}: getOrderListAndNum(JSONObject)
	{GET [/ksiteOrder/getAmountOfIncome]}: getAmountOfIncome(Integer)
	{GET [/ksiteOrder/getSettlementDashboardData]}: getSettlementDashboardData(Integer)
	{POST [/ksiteOrder/getOrderNum]}: getOrderNum(JSONObject)
	{ [/ksiteOrder/orderNotify]}: orderNotify(JSONObject)
	{POST [/ksiteOrder/update]}: update(JSONObject)
[13:07:57:610] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteOrderRelationController:
	{POST [/ksiteOrderRelation/checkContentAuth]}: checkContentAuth(KsiteOrderRelation)
[13:07:57:611] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsitePaperInstanceController:
	{GET [/ksitePaperInstance/getPaperInstanceStageSummaryDetail]}: getPaperInstanceStageSummaryDetail(Integer,String,String,Integer,Integer)
	{POST [/ksitePaperInstance/create]}: create(JSONObject)
[13:07:57:613] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsitePaperInstanceProcessController:
	{GET [/ksitePaperInstanceProcess/getPaperInstanceProcessWrapperListAndNum]}: getPaperInstanceProcessWrapperListAndNum(Integer,Boolean,Integer,Integer)
[13:07:57:614] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteUserSignatureController:
	{POST [/ksiteUserSignature/getList]}: getList(JSONArray)
	{POST [/ksiteUserSignature/signKsite]}: sign(KsiteUserSignatureWrapper)
	{GET [/ksiteUserSignature/getUserSignature]}: getUserSignature(Integer,Integer)
	{GET [/ksiteUserSignature/checkKsiteNameExisted]}: checkKsiteNameExisted(String)
	{GET [/ksiteUserSignature/getKsiteInfo]}: getKsiteInfo(String,Integer)
	{POST [/ksiteUserSignature/update]}: update(KsiteUserSignatureWrapper)
[13:07:57:616] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteWithdrawController:
	{POST [/ksiteWithdraw/withdraw]}: withdraw(KsiteWithdraw)
[13:07:57:618] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.m.c.CategoryController:
	{POST [/category/getBatchPathOfCategoryList]}: getBatchPathOfCategoryList(JSONArray)
	{GET [/category/getNLevelCategory]}: getLevelCategoriesOfLessThanOrEqualDesignativeLevel(String,Integer,Integer)
	{POST [/category/addNewCategory]}: addNewCategory(JSONObject)
	{GET [/category/getPathOfCategory]}: getPathOfCategory(Integer)
	{POST [/category/updateCategory]}: updateCategory(JSONObject)
	{POST [/category/updateCategoryList]}: updateCategoryList(JSONArray)
	{GET [/category/copySpecifiedSpaceName]}: copySpecifiedSpaceName(String,Integer,String)
	{POST [/category/add]}: add(JSONObject)
	{POST [/category/update]}: update(JSONObject)
[13:07:57:620] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QiyeweixinAccountController:
	{GET [/qiyeweixinAccount/transferLicense]}: transferLicense(String,List)
	{GET [/qiyeweixinAccount/getMemberActiveInfo]}: getMemberActiveInfo(String,String)
	{GET [/qiyeweixinAccount/queryAutoActiveStatus]}: queryAutoActiveStatus(String)
	{GET [/qiyeweixinAccount/getActivatedAccountList]}: getActivatedAccountList(String,Integer,String)
[13:07:57:623] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QiyeweixinCommunicationController:
	{ [/qiyeweixinCommunication/receiveData]}: receiveData(String,String,String,String,HttpServletRequest)
	{ [/qiyeweixinCommunication/receiveCommand]}: receiveCommand(String,String,String,String,HttpServletRequest)
	{GET [/qiyeweixinCommunication/getPreAuthCode]}: getPreAuthCode(String)
	{GET [/qiyeweixinCommunication/setSessionInfo]}: setSessionInfo(String)
	{GET [/qiyeweixinCommunication/generatePermanentCode]}: generatePermanentCode(String,String)
	{GET [/qiyeweixinCommunication/getAppQrcode]}: getAppQrcode(String)
	{GET [/qiyeweixinCommunication/getAppPermission]}: getAppPermission(String,String)
	{GET [/qiyeweixinCommunication/getAppAdmin]}: getAppAdmin(String,String)
	{GET [/qiyeweixinCommunication/getJsapiTicket]}: getJsapiTicket(String,String,String)
[13:07:57:624] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QiyeweixinSignatureController:
	{POST [/qiyeweixinSignature/getSignature]}: getSignature(JSONObject)
[13:07:57:626] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QiyeweixinUserController:
	{GET [/qiyeweixinUser/loginQkkQiyeweixin]}: wechatLogin()
	{GET [/qiyeweixinUser/loginQkkQiyeweixinFromPCWeb]}: wechatLoginFromPCWeb(String)
	{GET [/qiyeweixinUser/syncAllDepartmentNameAndUserNameByOCR]}: syncAllDepartmentNameAndUserNameByOCR(Integer)
	{GET [/qiyeweixinUser/refreshDepartmentNameAndUserNameByOCR]}: refreshDepartmentNameAndUserNameByOCR(Integer)
	{GET [/qiyeweixinUser/login]}: login(HttpServletRequest)
	{GET [/qiyeweixinUser/getUserInfo]}: getUserInfo(HttpServletRequest)
[13:07:57:627] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.CompanyControllerOfQKK:
	
[13:07:57:627] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.CompanyRegisterFormController:
	{GET [/companyRegisterForm/getCompanyRegisterForm]}: getCompanyRegisterForm(Integer)
	{GET [/companyRegisterForm/getCompanyRegisterFormFieldStructure]}: getCompanyRegisterFormFieldStructure(Integer)
	{POST [/companyRegisterForm/insertIfNotExisted]}: insertIfNotExisted(CompanyRegisterForm)
[13:07:57:628] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.CompanyRegisterFormFlowRecordController:
	{GET [/companyRegisterFormFlowRecord/getRegistrationDetail]}: getRegistrationDetail(Integer,Integer)
[13:07:57:628] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.ExaminationControllerOfQkk:
	{POST [/examination/getExaminationInViewListOfCompanyByTagesAndUserCompany]}: getExaminationInViewListOfCompanyByTagesAndUserCompany(JSONObject)
	{POST [/examination/getExaminationInViewListAndTotalSizeOfCompanyByTagesAndUserCompany]}: getExaminationInViewListAndTotalSizeOfCompanyByTagesAndUserCompany(JSONObject)
	{GET [/examination/getExamList]}: getExamList(Integer)
	{GET [/examination/checkIfAuthorized]}: checkIfAuthorized(Integer,Integer,Integer)
[13:07:57:628] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.ExaminationInstanceControllerOfQkk:
	{GET [/examinationInstanceOfQkk/getExaminationInstanceListWithUserInfoByExaminationId]}: getExaminationInstanceListWithUserInfoByExaminationId(Integer,Integer,Integer,String,Integer,Integer)
	{GET [/examinationInstanceOfQkk/examinationInstanceStageSummaryDetail]}: getExaminationInstanceStageSummaryDetail(Integer,Integer,Integer,String,String,Integer,Integer)
	{GET [/examinationInstanceOfQkk/exportTotalScoreGroupByUser]}: exportTotalScoreGroupByUser(String,HttpServletResponse)
	{POST [/examinationInstanceOfQkk/getTotalScoreOfExaminationListGroupByUser]}: getTotalScoreOfExaminationListGroupByUser(JSONObject)
	{POST [/examinationInstanceOfQkk/getSummaryGroupByDepartment]}: getSummaryGroupByDepartment(JSONObject)
	{GET [/examinationInstanceOfQkk/exportSummaryGroupByDepartment]}: exportSummaryGroupByDepartment(Integer,Integer,Integer,HttpServletResponse)
	{POST [/examinationInstanceOfQkk/getTopNDepartmentDistribution]}: getTopNDepartmentDistribution(JSONObject)
[13:07:57:629] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.PageDataController:
	{POST [/pageData/getTotalScoreGroupByUserInCompany]}: getTotalScoreGroupByUserInCompany(JSONObject)
[13:07:57:629] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QuestionControllerOfQkk:
	{POST [/question/questionListExcludingCreaterInCompany]}: getQuestionListExcludingCreaterInCompany(JSONObject)
[13:07:57:631] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.UserControllerOfQkk:
	{POST [/user/secureAdminRegister]}: secureAdminRegister(HttpServletRequest)
	{POST [/user/registerAdminFromMp]}: registerAdminFromMp(JSONObject,HttpServletRequest)
	{POST [/user/registerAdminFromH5]}: registerAdminFromH5(JSONObject,HttpServletRequest)
	{POST [/user/secureExamineeRegister]}: secureExamineeRegister(HttpServletRequest,HttpServletResponse)
	{POST [/user/registerExamineeFromMp]}: registerExamineeFromMp(JSONObject,HttpServletRequest)
	{POST [/user/registerExamineeFromH5]}: registerExamineeFromH5(JSONObject,HttpServletRequest)
	{POST [/user/secureLogin]}: secureLogin(HttpServletRequest,HttpServletResponse)
	{GET [/user/mergeCompanyUser]}: mergeCompanyUser(Integer)
[13:07:57:632] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoMiniProgramController:
	{POST [/toutiaoMiniProgram/regist]}: regist(JSONObject)
	{POST [/toutiaoMiniProgram/autoLogin]}: autologin(JSONObject)
	{POST [/toutiaoMiniProgram/update]}: update(JSONObject)
[13:07:57:633] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoQuestionStarController:
	{GET [/touTiaoQuestionStar/getQuestionStarWrapper]}: getQuestionStarWrapper(Integer)
[13:07:57:636] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoQuestionStarStageEventController:
	{POST [/toutiaoQuestionStarStageEvent/insertIfNotExisted]}: insertIfNotExisted(ToutiaoQuestionStarStageEvent)
	{GET [/toutiaoQuestionStarStageEvent/getMyPlayedStageList]}: getMyPlayedStageList(Integer,Integer)
[13:07:57:637] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoQuestionStarUserController:
	{GET [/toutiaoQuestionStarUser/getUserInfo]}: getUserInfo(Integer)
[13:07:57:674] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoQuestionStarUserProfileController:
	{POST [/toutiaoQuestionStarUserProfile/markPass]}: markPass(JSONObject)
	{POST [/toutiaoQuestionStarUserProfile/increaseExp]}: increaseExp(JSONObject)
	{POST [/toutiaoQuestionStarUserProfile/getRankInfoList]}: getRankInfoList(JSONObject)
	{GET [/toutiaoQuestionStarUserProfile/getUserInfo]}: getUserInfo(Integer)
[13:07:57:699] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.x.UserSchoolController:
	{POST [/userSchool/bindSchool]}: bindCompany(JSONObject)
[13:07:57:721] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.o.c.OssFileController:
	{POST [/ossFile/update]}: update(OssFile)
[13:07:58:000] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
	{ [/error]}: error(HttpServletRequest)
[13:07:58:240] [DEBUG] - org.springframework.web.servlet.handler.AbstractDetectingUrlHandlerMapping.detectHandlers(AbstractDetectingUrlHandlerMapping.java:86) - 'beanNameHandlerMapping' {}
[13:07:58:775] [DEBUG] - org.springframework.web.servlet.handler.SimpleUrlHandlerMapping.logMappings(SimpleUrlHandlerMapping.java:177) - 'webSocketHandlerMapping' {/wss/pkGameSocket=org.springframework.web.socket.server.support.WebSocketHttpRequestHandler@2b29361e}
[13:07:59:090] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$MapperScannerRegistrarNotFoundConfiguration.afterPropertiesSet(MybatisAutoConfiguration.java:305) - No org.mybatis.spring.mapper.MapperFactoryBean found.
[13:08:01:483] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[13:08:02:378] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[13:08:02:439] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 57 ms to scan 1 urls, producing 3 keys and 6 values 
[13:08:02:462] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype com.alibaba.nacos.api.remote.request.Request -> com.alibaba.nacos.api.remote.request.ServerRequest
[13:08:02:476] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[13:08:02:509] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 28 ms to scan 1 urls, producing 4 keys and 9 values 
[13:08:02:514] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype com.alibaba.nacos.api.remote.request.Request -> com.alibaba.nacos.api.remote.request.ServerRequest
[13:08:02:521] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[13:08:02:552] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 30 ms to scan 1 urls, producing 3 keys and 10 values 
[13:08:02:557] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
file:/Users/<USER>/Documents/git/taurus-cloud/exam-main-service/target/classes/
file:/private/var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T/cp_bglw5z4cph95pkrips04x9cfi.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/dnsns.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jaccess.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jfxrt.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunec.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/zipfs.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/nashorn.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/cldrdata.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/localedata.jar
[13:08:02:767] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 209 ms to scan 12 urls, producing 0 keys and 0 values 
[13:08:02:778] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[13:08:02:789] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
[13:08:02:792] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[13:08:02:807] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
[13:08:02:812] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[13:08:02:830] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 18 ms to scan 1 urls, producing 2 keys and 8 values 
[13:08:02:831] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype java.lang.Comparable -> java.lang.Enum
[13:08:02:831] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype java.io.Serializable -> java.lang.Enum
[13:08:02:832] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
file:/Users/<USER>/Documents/git/taurus-cloud/exam-main-service/target/classes/
file:/private/var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T/cp_bglw5z4cph95pkrips04x9cfi.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/dnsns.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jaccess.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jfxrt.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunec.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/zipfs.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/nashorn.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/cldrdata.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/localedata.jar
[13:08:02:865] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 33 ms to scan 12 urls, producing 0 keys and 0 values 
[13:08:04:469] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[13:08:04:470] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[13:08:04:471] [DEBUG] - com.taurus.examinationassistant.filter.AdminAuthFilter.init(AdminAuthFilter.java:45) - AdminAuthFilter初始化
[13:08:04:488] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[13:08:04:512] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[13:08:04:538] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[13:08:04:573] [DEBUG] - org.xnio.XnioWorker$Builder.build(XnioWorker.java:1191) - Creating worker:null, pool size:64, max pool size:64, keep alive:60000, io threads:8, stack size:0
[13:08:04:598] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[13:08:04:629] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-6', selector sun.nio.ch.KQueueSelectorImpl@18253f36
[13:08:04:629] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-3', selector sun.nio.ch.KQueueSelectorImpl@5281108
[13:08:04:629] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-7', selector sun.nio.ch.KQueueSelectorImpl@233c254a
[13:08:04:629] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-8', selector sun.nio.ch.KQueueSelectorImpl@298578a8
[13:08:04:628] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-1', selector sun.nio.ch.KQueueSelectorImpl@2fd5f166
[13:08:04:629] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 Accept', selector sun.nio.ch.KQueueSelectorImpl@151c5004
[13:08:04:629] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-4', selector sun.nio.ch.KQueueSelectorImpl@2a766a0e
[13:08:04:629] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-2', selector sun.nio.ch.KQueueSelectorImpl@9c8a1df
[13:08:04:629] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-5', selector sun.nio.ch.KQueueSelectorImpl@72d1478e
[13:08:04:633] [DEBUG] - io.undertow.Undertow.start(Undertow.java:160) - Configuring listener with protocol HTTP for interface 0.0.0.0 and port 8081
[13:08:04:769] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[13:08:05:188] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 44.249 seconds (JVM running for 45.811)
[13:08:05:213] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[13:08:05:350] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`account` , t.`app_id` , t.`app_secret` , t.`token` , t.`encoding_aes_key` FROM `weixin_account` t 
[13:08:05:422] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 
[13:08:05:513] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 13
[13:08:05:516] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:09:33:517] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /version/getVersion, authentication required: false
[13:09:33:517] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /wx/getTicket, authentication required: false
[13:09:33:529] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /wx/getTicket
[13:09:33:529] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /version/getVersion
[13:09:33:532] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /wx/getTicket
[13:09:33:532] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /version/getVersion
[13:09:33:544] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[13:09:34:953] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: INSERT INTO `qrcode_scan_entry` ( `ticket` , `scene_id` , `create_time` , `scan_time` , `openid` , `creater_id` , `phone` ) VALUES ( ? , ? , ? , ? , ? , ? , ? ) 
[13:09:34:967] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: gQGU7zwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyNmJueTFfaWtjeGwxbW5QNjFFMXoAAgSPbEZoAwQIBwAA(String), 93014328(Integer), 2025-06-09 13:09:34.874(Timestamp), null, null, null, null
[13:09:35:110] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[13:09:35:253] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:09:36:131] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /qrcodeScan/checkIfFollowed, authentication required: false
[13:09:36:135] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /qrcodeScan/checkIfFollowed
[13:09:36:136] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /qrcodeScan/checkIfFollowed
[13:09:36:204] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`ticket` , t.`scene_id` , t.`create_time` , t.`scan_time` , t.`openid` , t.`creater_id` , t.`phone` FROM `qrcode_scan_entry` t WHERE ticket = ? AND scene_id = ? LIMIT 1 
[13:09:36:204] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: gQGU7zwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyNmJueTFfaWtjeGwxbW5QNjFFMXoAAgSPbEZoAwQIBwAA(String), 93014328(String)
[13:09:36:247] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:09:36:247] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:09:37:244] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /qrcodeScan/checkIfFollowed, authentication required: false
[13:09:37:246] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /qrcodeScan/checkIfFollowed
[13:09:37:247] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /qrcodeScan/checkIfFollowed
[13:09:37:256] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`ticket` , t.`scene_id` , t.`create_time` , t.`scan_time` , t.`openid` , t.`creater_id` , t.`phone` FROM `qrcode_scan_entry` t WHERE ticket = ? AND scene_id = ? LIMIT 1 
[13:09:37:257] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: gQGU7zwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyNmJueTFfaWtjeGwxbW5QNjFFMXoAAgSPbEZoAwQIBwAA(String), 93014328(String)
[13:09:38:077] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xa8765202, L:/*************:64707 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[13:09:38:097] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x5cafa0d3, L:/*************:64708 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[13:09:38:137] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x22a144dd, L:/*************:64709 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[13:09:38:141] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x857f360c, L:/*************:64710 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[13:09:38:143] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@2105455101 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xa8765202, L:/*************:64707 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[13:09:38:157] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1606290934 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x5cafa0d3, L:/*************:64708 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[13:09:38:162] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:09:38:162] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:09:38:241] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x1b47e8fd, L:/*************:64692 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[13:09:38:246] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x23e792f4, L:/*************:64711 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[13:09:38:246] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xaea5d478, L:/*************:64712 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[13:09:38:246] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x933c4dcb, L:/*************:64713 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[13:09:38:246] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@556982211 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x22a144dd, L:/*************:64709 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[13:09:38:250] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@2077346139 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x857f360c, L:/*************:64710 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[13:09:38:262] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:09:38:266] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:09:38:336] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xb108cb2a, L:/*************:64691 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[13:09:38:339] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x78a32070, L:/*************:64690 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[13:09:38:340] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xde275dca, L:/*************:64693 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[13:09:38:340] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xd449d8d4, L:/*************:64714 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[13:09:38:340] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x5d5fb589, L:/*************:64715 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[13:09:38:340] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@543356765 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x1b47e8fd, L:/*************:64692 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[13:09:38:350] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@550420351 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xaea5d478, L:/*************:64712 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[13:09:38:351] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@467328609 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x933c4dcb, L:/*************:64713 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[13:09:38:351] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1763715635 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x23e792f4, L:/*************:64711 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[13:09:38:356] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:09:38:365] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:09:38:373] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:09:38:376] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:09:38:432] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@708236303 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x78a32070, L:/*************:64690 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[13:09:38:433] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1409326995 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd449d8d4, L:/*************:64714 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[13:09:38:437] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1297673012 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x5d5fb589, L:/*************:64715 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[13:09:38:441] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@287487249 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xde275dca, L:/*************:64693 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[13:09:38:447] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:09:38:447] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:09:38:451] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisPubSubConnection@1675116867 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xb108cb2a, L:/*************:64691 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@6e286cce[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0] to 47.111.231.172/47.111.231.172:6379 
[13:09:38:463] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:09:38:466] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:09:38:466] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:09:38:736] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xf247a483, L:/*************:64694 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[13:09:38:755] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x62d1c941, L:/*************:64716 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[13:09:38:757] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x737c5e19, L:/*************:64695 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[13:09:38:766] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xd130ade6, L:/*************:64696 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[13:09:38:766] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x8e712af1, L:/*************:64717 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[13:09:38:766] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x2b597949, L:/*************:64718 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[13:09:38:766] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xe402a442, L:/*************:64702 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[13:09:38:808] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x01a352e9, L:/*************:64701 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[13:09:38:838] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1593373923 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xf247a483, L:/*************:64694 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[13:09:38:840] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1761448370 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x62d1c941, L:/*************:64716 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[13:09:38:843] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:09:38:844] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1201700932 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x737c5e19, L:/*************:64695 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[13:09:38:846] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:09:38:849] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:09:38:849] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1943126025 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x8e712af1, L:/*************:64717 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[13:09:38:852] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1644559042 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd130ade6, L:/*************:64696 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[13:09:38:856] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1677288663 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2b597949, L:/*************:64718 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[13:09:38:857] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:09:38:857] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@413619253 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xe402a442, L:/*************:64702 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[13:09:38:857] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:09:38:858] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1761677223 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x01a352e9, L:/*************:64701 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[13:09:38:865] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:09:38:871] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:09:38:875] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:09:39:040] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x1b9e72e3, L:/*************:64703 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[13:09:39:054] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xab16c563, L:/*************:64704 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[13:09:39:054] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x9df24d66, L:/*************:64706 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[13:09:39:054] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x71dc16d9, L:/*************:64705 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[13:09:39:139] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@349494030 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x1b9e72e3, L:/*************:64703 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[13:09:39:140] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1407920086 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xab16c563, L:/*************:64704 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[13:09:39:140] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@728669949 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x9df24d66, L:/*************:64706 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[13:09:39:147] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@51320082 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x71dc16d9, L:/*************:64705 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[13:09:39:147] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:09:39:147] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:09:39:156] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:09:39:160] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[13:09:41:149] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:09:41:175] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1606290934 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x5cafa0d3, L:/*************:64708 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[13:09:41:175] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@543356765 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x1b47e8fd, L:/*************:64692 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[13:09:41:175] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@349494030 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x1b9e72e3, L:/*************:64703 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[13:09:41:175] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@728669949 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x9df24d66, L:/*************:64706 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[13:09:41:181] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1297673012 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x5d5fb589, L:/*************:64715 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[13:09:41:183] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@2105455101 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xa8765202, L:/*************:64707 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[13:09:41:230] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /qrcodeScan/checkIfFollowed, authentication required: false
[13:09:41:235] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /qrcodeScan/checkIfFollowed
[13:09:41:235] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /qrcodeScan/checkIfFollowed
[13:09:41:261] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`ticket` , t.`scene_id` , t.`create_time` , t.`scan_time` , t.`openid` , t.`creater_id` , t.`phone` FROM `qrcode_scan_entry` t WHERE ticket = ? AND scene_id = ? LIMIT 1 
[13:09:41:262] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: gQGU7zwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyNmJueTFfaWtjeGwxbW5QNjFFMXoAAgSPbEZoAwQIBwAA(String), 93014328(String)
[13:09:41:270] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1943126025 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x8e712af1, L:/*************:64717 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[13:09:41:271] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@287487249 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xde275dca, L:/*************:64693 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[13:09:41:270] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@556982211 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x22a144dd, L:/*************:64709 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[13:09:41:271] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1644559042 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd130ade6, L:/*************:64696 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[13:09:41:271] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1407920086 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xab16c563, L:/*************:64704 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[13:09:41:271] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisPubSubConnection@1675116867 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xb108cb2a, L:/*************:64691 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@6e286cce[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: CommandData [promise=java.util.concurrent.CompletableFuture@6e286cce[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec]
[13:09:41:271] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@467328609 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x933c4dcb, L:/*************:64713 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[13:09:41:271] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1761677223 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x01a352e9, L:/*************:64701 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[13:09:41:271] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1593373923 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xf247a483, L:/*************:64694 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[13:09:41:271] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@708236303 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x78a32070, L:/*************:64690 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[13:09:41:271] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1761448370 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x62d1c941, L:/*************:64716 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[13:09:41:271] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1763715635 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x23e792f4, L:/*************:64711 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[13:09:41:272] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@51320082 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x71dc16d9, L:/*************:64705 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[13:09:41:272] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1409326995 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd449d8d4, L:/*************:64714 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[13:09:41:272] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1201700932 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x737c5e19, L:/*************:64695 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[13:09:41:272] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@413619253 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xe402a442, L:/*************:64702 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[13:09:41:272] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1677288663 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2b597949, L:/*************:64718 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[13:09:41:272] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@2077346139 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x857f360c, L:/*************:64710 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[13:09:41:327] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:09:41:338] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@550420351 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xaea5d478, L:/*************:64712 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[13:09:41:363] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /qrcodeScan/checkIfFollowed, authentication required: false
[13:09:41:363] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /qrcodeScan/checkIfFollowed
[13:09:41:363] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /qrcodeScan/checkIfFollowed
[13:09:41:367] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`ticket` , t.`scene_id` , t.`create_time` , t.`scan_time` , t.`openid` , t.`creater_id` , t.`phone` FROM `qrcode_scan_entry` t WHERE ticket = ? AND scene_id = ? LIMIT 1 
[13:09:41:369] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: gQGU7zwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyNmJueTFfaWtjeGwxbW5QNjFFMXoAAgSPbEZoAwQIBwAA(String), 93014328(String)
[13:09:41:425] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:09:41:451] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /qrcodeScan/checkIfFollowed, authentication required: false
[13:09:41:452] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /qrcodeScan/checkIfFollowed
[13:09:41:452] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /qrcodeScan/checkIfFollowed
[13:09:41:455] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`ticket` , t.`scene_id` , t.`create_time` , t.`scan_time` , t.`openid` , t.`creater_id` , t.`phone` FROM `qrcode_scan_entry` t WHERE ticket = ? AND scene_id = ? LIMIT 1 
[13:09:41:455] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: gQGU7zwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyNmJueTFfaWtjeGwxbW5QNjFFMXoAAgSPbEZoAwQIBwAA(String), 93014328(String)
[13:09:41:495] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:09:41:527] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /qrcodeScan/checkIfFollowed, authentication required: false
[13:09:41:527] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /qrcodeScan/checkIfFollowed
[13:09:41:527] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /qrcodeScan/checkIfFollowed
[13:09:41:531] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`ticket` , t.`scene_id` , t.`create_time` , t.`scan_time` , t.`openid` , t.`creater_id` , t.`phone` FROM `qrcode_scan_entry` t WHERE ticket = ? AND scene_id = ? LIMIT 1 
[13:09:41:532] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: gQGU7zwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyNmJueTFfaWtjeGwxbW5QNjFFMXoAAgSPbEZoAwQIBwAA(String), 93014328(String)
[13:09:41:581] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:09:42:248] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /qrcodeScan/checkIfFollowed, authentication required: false
[13:09:42:248] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /qrcodeScan/checkIfFollowed
[13:09:42:249] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /qrcodeScan/checkIfFollowed
[13:09:42:251] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`ticket` , t.`scene_id` , t.`create_time` , t.`scan_time` , t.`openid` , t.`creater_id` , t.`phone` FROM `qrcode_scan_entry` t WHERE ticket = ? AND scene_id = ? LIMIT 1 
[13:09:42:251] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: gQGU7zwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyNmJueTFfaWtjeGwxbW5QNjFFMXoAAgSPbEZoAwQIBwAA(String), 93014328(String)
[13:09:42:304] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:09:43:249] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /qrcodeScan/checkIfFollowed, authentication required: false
[13:09:43:250] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /qrcodeScan/checkIfFollowed
[13:09:43:250] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /qrcodeScan/checkIfFollowed
[13:09:43:259] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`ticket` , t.`scene_id` , t.`create_time` , t.`scan_time` , t.`openid` , t.`creater_id` , t.`phone` FROM `qrcode_scan_entry` t WHERE ticket = ? AND scene_id = ? LIMIT 1 
[13:09:43:260] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: gQGU7zwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyNmJueTFfaWtjeGwxbW5QNjFFMXoAAgSPbEZoAwQIBwAA(String), 93014328(String)
[13:09:43:330] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:09:44:243] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /qrcodeScan/checkIfFollowed, authentication required: false
[13:09:44:243] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /qrcodeScan/checkIfFollowed
[13:09:44:243] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /qrcodeScan/checkIfFollowed
[13:09:44:246] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`ticket` , t.`scene_id` , t.`create_time` , t.`scan_time` , t.`openid` , t.`creater_id` , t.`phone` FROM `qrcode_scan_entry` t WHERE ticket = ? AND scene_id = ? LIMIT 1 
[13:09:44:247] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: gQGU7zwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyNmJueTFfaWtjeGwxbW5QNjFFMXoAAgSPbEZoAwQIBwAA(String), 93014328(String)
[13:09:44:366] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:09:45:233] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /qrcodeScan/checkIfFollowed, authentication required: false
[13:09:45:233] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /qrcodeScan/checkIfFollowed
[13:09:45:234] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /qrcodeScan/checkIfFollowed
[13:09:45:239] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`ticket` , t.`scene_id` , t.`create_time` , t.`scan_time` , t.`openid` , t.`creater_id` , t.`phone` FROM `qrcode_scan_entry` t WHERE ticket = ? AND scene_id = ? LIMIT 1 
[13:09:45:240] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: gQGU7zwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyNmJueTFfaWtjeGwxbW5QNjFFMXoAAgSPbEZoAwQIBwAA(String), 93014328(String)
[13:09:45:303] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:09:46:302] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /qrcodeScan/checkIfFollowed, authentication required: false
[13:09:46:304] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /qrcodeScan/checkIfFollowed
[13:09:46:304] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /qrcodeScan/checkIfFollowed
[13:09:46:311] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`ticket` , t.`scene_id` , t.`create_time` , t.`scan_time` , t.`openid` , t.`creater_id` , t.`phone` FROM `qrcode_scan_entry` t WHERE ticket = ? AND scene_id = ? LIMIT 1 
[13:09:46:311] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: gQGU7zwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyNmJueTFfaWtjeGwxbW5QNjFFMXoAAgSPbEZoAwQIBwAA(String), 93014328(String)
[13:09:46:385] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:09:47:246] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /qrcodeScan/checkIfFollowed, authentication required: false
[13:09:47:246] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /qrcodeScan/checkIfFollowed
[13:09:47:246] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /qrcodeScan/checkIfFollowed
[13:09:47:251] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`ticket` , t.`scene_id` , t.`create_time` , t.`scan_time` , t.`openid` , t.`creater_id` , t.`phone` FROM `qrcode_scan_entry` t WHERE ticket = ? AND scene_id = ? LIMIT 1 
[13:09:47:251] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: gQGU7zwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyNmJueTFfaWtjeGwxbW5QNjFFMXoAAgSPbEZoAwQIBwAA(String), 93014328(String)
[13:09:47:297] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:09:48:224] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /qrcodeScan/checkIfFollowed, authentication required: false
[13:09:48:224] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /qrcodeScan/checkIfFollowed
[13:09:48:224] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /qrcodeScan/checkIfFollowed
[13:09:48:227] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`ticket` , t.`scene_id` , t.`create_time` , t.`scan_time` , t.`openid` , t.`creater_id` , t.`phone` FROM `qrcode_scan_entry` t WHERE ticket = ? AND scene_id = ? LIMIT 1 
[13:09:48:227] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: gQGU7zwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyNmJueTFfaWtjeGwxbW5QNjFFMXoAAgSPbEZoAwQIBwAA(String), 93014328(String)
[13:09:48:299] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:09:52:571] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /version/getVersion, authentication required: false
[13:09:52:571] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /wx/getTicket, authentication required: false
[13:09:52:577] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /wx/getTicket
[13:09:52:577] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /version/getVersion
[13:09:52:578] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /wx/getTicket
[13:09:52:578] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /version/getVersion
[13:09:53:117] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: INSERT INTO `qrcode_scan_entry` ( `ticket` , `scene_id` , `create_time` , `scan_time` , `openid` , `creater_id` , `phone` ) VALUES ( ? , ? , ? , ? , ? , ? , ? ) 
[13:09:53:122] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: gQET8DwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyeEstOTBBaWtjeGwxbUZQNk5FMWkAAgShbEZoAwQIBwAA(String), 2523424(Integer), 2025-06-09 13:09:53.103(Timestamp), null, null, null, null
[13:09:53:219] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[13:09:54:090] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /user/getUserInfoById, authentication required: false
[13:09:54:091] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /user/getUserInfoById
[13:09:54:091] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /user/getUserInfoById
[13:09:54:164] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[13:09:54:165] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 37377(Integer)
[13:09:54:225] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:09:54:226] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:09:54:284] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /access/getTokenUnlimit, authentication required: false
[13:09:54:285] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /access/getTokenUnlimit
[13:09:54:286] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /access/getTokenUnlimit
[13:09:54:415] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /universalProductOrderRelation/getProductOrderRelation, authentication required: false
[13:09:54:415] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /universalProductOrderRelation/getProductOrderRelation
[13:09:54:415] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /universalProductOrderRelation/getProductOrderRelation
[13:09:54:746] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /product/getListOfProduct, authentication required: false
[13:09:54:746] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /productOrderRelationCount/getProductOrderRelationCountList, authentication required: false
[13:09:54:748] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /productOrderRelationCount/getProductOrderRelationCountList
[13:09:54:748] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /product/getListOfProduct
[13:09:54:748] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /productOrderRelationCount/getProductOrderRelationCountList
[13:09:54:748] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /product/getListOfProduct
[13:09:54:774] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /productOrderRelationCount/getProductOrderRelationCountList, authentication required: false
[13:09:54:773] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /product/getProductList, authentication required: false
[13:09:54:784] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /productOrderRelationCount/getProductOrderRelationCountList
[13:09:54:785] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /productOrderRelationCount/getProductOrderRelationCountList
[13:09:54:784] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /pageData/getCompanyCoreSummary, authentication required: false
[13:09:54:785] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /pageData/getCompanyCoreSummary
[13:09:54:784] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /product/getProductList
[13:09:54:785] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /pageData/getCompanyCoreSummary
[13:09:54:785] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /product/getProductList
[13:09:54:821] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`type` , t.`code` , t.`name` , t.`short_description` , t.`long_description` , t.`max_examinee_num` , t.`valid_days` , t.`price` FROM `product` t WHERE type = ? 
[13:09:54:834] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: P(String)
[13:09:54:909] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`relation_type` , t.`relation_id` , t.`modal_name` , t.`available_times` , t.`end_time` FROM `product_order_relation_count` t WHERE relation_type = ? AND relation_id = ? AND modal_name IN ( ? , ? , ? , ? , ? , ? , ? , ? ) ORDER BY id ASC 
[13:09:54:916] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: P(String), 37377(Integer), VPWAD(String), PDDS(String), VADVIPC1(String), VADVIPC2(String), VADVIPC3(String), VADVIPC4(String), VADVIPC5(String), VADVIPC6(String)
[13:09:54:921] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 8
[13:09:54:923] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:09:54:928] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`relation_type` , t.`relation_id` , t.`modal_name` , t.`available_times` , t.`end_time` FROM `product_order_relation_count` t WHERE relation_type = ? AND relation_id = ? AND modal_name IN ( ? , ? , ? , ? , ? , ? ) ORDER BY id ASC 
[13:09:54:929] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: P(String), 37377(Integer), VADVIPC1(String), VADVIPC2(String), VADVIPC3(String), VADVIPC4(String), VADVIPC5(String), VADVIPC6(String)
[13:09:54:931] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select type,child_type as childType, count(*) as num from question where enabled=1 and company_id=? and creater_id=? group by type,child_type 
[13:09:54:938] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer)
[13:09:54:986] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:09:54:986] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 3
[13:09:54:991] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:09:54:991] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:09:55:046] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 7
[13:09:55:047] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:09:55:065] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select count(distinct(category)) as num from question where enabled=1 and company_id=? and creater_id=? 
[13:09:55:066] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer)
[13:09:55:138] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:09:55:139] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:09:55:146] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select count(*) as totalPracticeTimes, count(distinct t.user_id) as totalUserNum, sum(t.total_right_times) as totalRightTimes,sum(t.total_wrong_times) as totalWrongTimes, sum(t.duration) as totalDuration from exercise_book_practice_summary t left join exercise_book t1 ON t1.id=t.exercise_book_id where t1.enabled=1 and t1.company_id=? and t1.creater_id=? 
[13:09:55:147] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer)
[13:09:55:216] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:09:55:218] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:09:55:255] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select type, count(*) as num from exercise_book where enabled=1 and company_id=? and creater_id in( ? ) group by type 
[13:09:55:256] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer)
[13:09:55:311] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 2
[13:09:55:312] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:09:55:326] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select DATE_FORMAT(t.end_date, '%Y-%m-%d') day, count(distinct(t.user_id)) as userNum,count(*) as times from exercise_book_practice_summary t left join exercise_book t1 ON t1.id= t.exercise_book_id where t1.enabled=1 and t1.company_id=? and t1.creater_id in( ? ) group by day order by day desc limit ? 
[13:09:55:329] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer), 20(Integer)
[13:09:55:392] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 6
[13:09:55:394] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:09:55:445] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT count(*) FROM `examination` t WHERE enabled = ? AND company_id = ? AND creater_id IN ( ? ) 
[13:09:55:446] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 0(Integer), 37377(Integer)
[13:09:55:497] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:09:55:499] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:09:55:505] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select count(distinct t.user_id) as totalUserNum,count(*) as totalTimes, sum(if(t.if_pass = true, 1, 0)) as totalPassed,sum(if(t.if_pass = false, 1, 0)) as totalFailed from examination_instance t left join examination t1 ON t1.id=t.examination_id where t.deleted_by_admin = 0 and t.enabled=1 and t1.company_id=? and t1.creater_id in( ? ) 
[13:09:55:506] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer)
[13:09:55:605] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:09:55:609] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:09:55:640] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select DATE_FORMAT(t.create_time, '%Y-%m-%d') day, count(distinct(t.user_id)) as userNum,count(*) as times from examination_instance t left join examination t1 ON t1.id=t.examination_id where t.enabled=1 and t.deleted_by_admin=0 and t1.company_id=? and t1.creater_id in( ? ) group by day order by day desc limit ? 
[13:09:55:643] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer), 20(Integer)
[13:09:55:748] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 2
[13:09:55:749] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:09:57:443] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /examination/getExaminationListOfCreater, authentication required: false
[13:09:57:446] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /examination/getExaminationListOfCreater
[13:09:57:447] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /examination/getExaminationListOfCreater
[13:09:57:500] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`code` , t.`name` , t.`paper_id` , t.`notice_enabled` , t.`notice` , t.`password_enabled` , t.`password` , t.`redo_type` , t.`redo_number` , t.`if_display_score` , t.`if_fill_in_name` , t.`if_fill_in_name_all_time` , t.`illustration_relative_url` , t.`illustration_file_name` , t.`state` , t.`pass_score` , t.`begin_time` , t.`end_time` , t.`duration` , t.`display_mode` , t.`creater_id` , t.`create_time` , t.`enabled` , t.`examinee_num` , t.`exam_total_times` , t.`if_display_review_process` , t.`quit_exam_as_auto_commit` , t.`warn_enabled` , t.`warn_times` , t.`if_display_wrong_question` , t.`if_display_right_question` , t.`if_display_answer` , t.`if_display_group` , t.`entrance_template` , t.`exit_template` , t.`examinee_info_template` , t.`examinee_collection_form_id` , t.`favorite_num` , t.`company_id` , t.`if_display_to_examinee` , t.`if_auto_next_question` , t.`if_shuffle_selection_options` , t.`no_ad_enabled` , t.`camera_enabled` , t.`hide_rank` , t.`auth_user` , t.`if_validate_content` , t.`time_per_question` , t.`wx_group_enabled` , t.`open_limit_enabled` , t.`open_limit_times` , t.`collect_wrong_answer` , t.`two_options_required` , t.`illustration_uri` , t.`handwriting_signature_enabled` , t.`record_screen_allowed` , t.`if_ai_scoring` , t.`client` , t.`network_limit_enabled` , t.`selected_wifi_list` FROM `examination` t WHERE creater_id = ? AND company_id = ? AND enabled = ? ORDER BY create_time DESC 
[13:09:57:502] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 37377(Integer), 0(Integer), 1(Integer)
[13:09:57:623] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 5
[13:09:57:624] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:09:57:660] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /examinationInstance/getExamineeList, authentication required: false
[13:09:57:660] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /examinationInstance/getExamineeList
[13:09:57:660] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /examinationInstance/getExamineeList
[13:09:57:681] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select distinct(t.user_id) as userId from examination_instance t left join examination t1 ON t.examination_id=t1.id where t1.creater_id=? and t.examination_id in( ? ) limit ?,? 
[13:09:57:682] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 37377(Integer), 169071(Integer), 0(Integer), 50(Integer)
[13:09:57:742] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[13:09:57:742] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:09:57:744] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select count(distinct(user_id)) as totalUserNum from examination_instance where deleted_by_admin=false and examination_id in( ? ) 
[13:09:57:744] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169071(Integer)
[13:09:57:820] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:09:57:821] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[13:09:57:821] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:10:01:041] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /company/getQiyeweixinCompanyList, authentication required: false
[13:10:01:042] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /company/getQiyeweixinCompanyList
[13:10:01:042] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /company/getQiyeweixinCompanyList
[13:10:01:072] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`type` , t.`name` , t.`contact` , t.`telephone` , t.`desc` , t.`enable_register_code` , t.`register_code` , t.`address` , t.`create_time` , t.`enabled` , t.`user_id` , t.`code` , t.`first_channel` , t.`sys_config` , t.`storage_capacity` , t.`residual_flow` , t.`qkk_qiye_corpid` , t.`qkk_qiye_permanent_code` , t.`qkk_qiye_agent_id` , t.`qkk_qiye_auth_mode` FROM `company` t WHERE enabled = ? AND (qkk_qiye_corpid IS NOT NULL) ORDER BY id DESC LIMIT ?,? 
[13:10:01:072] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 0(Integer), 20(Integer)
[13:10:01:146] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 20
[13:10:01:147] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[13:10:01:147] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:10:01:186] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:10:01:186] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6281(Integer), 1(Integer), 3(Integer)
[13:10:01:274] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[13:10:01:275] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[13:10:01:275] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:10:01:285] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:10:01:285] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6277(Integer), 1(Integer), 3(Integer)
[13:10:01:362] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[13:10:01:365] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:10:01:366] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6267(Integer), 1(Integer), 3(Integer)
[13:10:01:451] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:10:01:454] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:10:01:455] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6264(Integer), 1(Integer), 3(Integer)
[13:10:01:530] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:10:01:535] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:10:01:535] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6257(Integer), 1(Integer), 3(Integer)
[13:10:01:619] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 3
[13:10:01:622] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:10:01:622] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6256(Integer), 1(Integer), 3(Integer)
[13:10:01:723] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[13:10:01:725] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:10:01:725] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6251(Integer), 1(Integer), 3(Integer)
[13:10:01:819] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:10:01:823] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:10:01:823] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6184(Integer), 1(Integer), 3(Integer)
[13:10:01:915] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[13:10:01:925] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:10:01:926] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6165(Integer), 1(Integer), 3(Integer)
[13:10:02:002] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[13:10:02:024] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:10:02:024] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6164(Integer), 1(Integer), 3(Integer)
[13:10:02:106] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[13:10:02:110] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:10:02:110] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6162(Integer), 1(Integer), 3(Integer)
[13:10:02:171] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[13:10:02:174] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:10:02:175] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6141(Integer), 1(Integer), 3(Integer)
[13:10:02:254] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[13:10:02:256] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:10:02:256] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6140(Integer), 1(Integer), 3(Integer)
[13:10:02:342] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[13:10:02:345] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:10:02:347] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6131(Integer), 1(Integer), 3(Integer)
[13:10:02:418] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[13:10:02:422] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:10:02:422] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6127(Integer), 1(Integer), 3(Integer)
[13:10:02:512] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[13:10:02:520] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:10:02:521] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6118(Integer), 1(Integer), 3(Integer)
[13:10:02:607] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[13:10:02:621] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:10:02:621] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6112(Integer), 1(Integer), 3(Integer)
[13:10:02:713] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[13:10:02:721] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:10:02:721] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6107(Integer), 1(Integer), 3(Integer)
[13:10:02:804] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:10:02:810] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:10:02:810] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6064(Integer), 1(Integer), 3(Integer)
[13:10:02:901] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[13:10:02:916] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:10:02:917] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6060(Integer), 1(Integer), 3(Integer)
[13:10:03:009] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[13:10:03:018] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT count(*) FROM `company` t WHERE enabled = ? AND (qkk_qiye_corpid IS NOT NULL) 
[13:10:03:019] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean)
[13:10:03:087] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:10:03:087] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[13:10:03:087] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:10:10:669] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /qiyeweixinUser/refreshDepartmentNameAndUserNameByOCR, authentication required: false
[13:10:10:675] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /qiyeweixinUser/refreshDepartmentNameAndUserNameByOCR
[13:10:10:675] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /qiyeweixinUser/refreshDepartmentNameAndUserNameByOCR
[13:10:10:780] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`type` , t.`name` , t.`contact` , t.`telephone` , t.`desc` , t.`enable_register_code` , t.`register_code` , t.`address` , t.`create_time` , t.`enabled` , t.`user_id` , t.`code` , t.`first_channel` , t.`sys_config` , t.`storage_capacity` , t.`residual_flow` , t.`qkk_qiye_corpid` , t.`qkk_qiye_permanent_code` , t.`qkk_qiye_agent_id` , t.`qkk_qiye_auth_mode` FROM `company` t WHERE t.`id` = ? LIMIT 1 
[13:10:10:783] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 6264(Integer)
[13:10:10:854] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:10:10:855] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[13:10:10:855] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:10:10:857] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`type` , t.`name` , t.`contact` , t.`telephone` , t.`desc` , t.`enable_register_code` , t.`register_code` , t.`address` , t.`create_time` , t.`enabled` , t.`user_id` , t.`code` , t.`first_channel` , t.`sys_config` , t.`storage_capacity` , t.`residual_flow` , t.`qkk_qiye_corpid` , t.`qkk_qiye_permanent_code` , t.`qkk_qiye_agent_id` , t.`qkk_qiye_auth_mode` FROM `company` t WHERE t.`id` = ? LIMIT 1 
[13:10:10:857] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 6264(Integer)
[13:10:10:924] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:10:10:927] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:215) - activeProfile:test-etea
[13:10:10:928] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:222) - targetUrl:https://www.qikaokao.com/qiyeweixin/#/qiyeweixinCompanyInfo?companyId=6264&qkkQiyeCorpid=wpfqwcEQAAVSuad4wjYG9PaKuK-zUwtg&qkkQiyeAgentId=1000013
[13:10:10:929] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:226) - serviceUrl:http://127.0.0.1:8077/api/ocr/recognizeUrl?url=%s&ocr=ali
[13:10:10:930] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:229) - url:http://127.0.0.1:8077/api/ocr/recognizeUrl?url=https%3A%2F%2Fwww.qikaokao.com%2Fqiyeweixin%2F%23%2FqiyeweixinCompanyInfo%3FcompanyId%3D6264%26qkkQiyeCorpid%3DwpfqwcEQAAVSuad4wjYG9PaKuK-zUwtg%26qkkQiyeAgentId%3D1000013&ocr=ali
[13:10:30:979] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:241) - result:{data=[部门列表, 人员列表, $$ 1731350 $$ 陈彦如 $$ 分隔符 $$ 1731351 $$ 霍述生 $$分隔符], errorMessage=success, errorCode=0}
[13:10:30:991] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1731350, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=陈彦如, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[13:10:31:059] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[13:10:31:063] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 陈彦如(String), 1731350(Integer)
[13:10:31:221] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[13:10:31:222] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[13:10:31:222] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:10:31:237] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1731351, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=霍述生, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[13:10:31:239] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[13:10:31:240] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 霍述生(String), 1731351(Integer)
[13:10:31:286] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[13:10:31:287] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1731350(Integer)
[13:10:31:337] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[13:10:31:338] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[13:10:31:346] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1731351(Integer)
[13:10:31:353] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:10:31:354] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:10:31:483] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:10:31:484] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[13:10:31:484] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:10:55:827] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /*************:49555
[13:10:57:696] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /*************:49521
[13:10:57:875] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /*************:49553
[13:11:03:142] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /*************:49321
[13:11:21:325] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /qiyeweixinUser/refreshDepartmentNameAndUserNameByOCR, authentication required: false
[13:11:21:332] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /qiyeweixinUser/refreshDepartmentNameAndUserNameByOCR
[13:11:21:332] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /qiyeweixinUser/refreshDepartmentNameAndUserNameByOCR
[13:11:21:428] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`type` , t.`name` , t.`contact` , t.`telephone` , t.`desc` , t.`enable_register_code` , t.`register_code` , t.`address` , t.`create_time` , t.`enabled` , t.`user_id` , t.`code` , t.`first_channel` , t.`sys_config` , t.`storage_capacity` , t.`residual_flow` , t.`qkk_qiye_corpid` , t.`qkk_qiye_permanent_code` , t.`qkk_qiye_agent_id` , t.`qkk_qiye_auth_mode` FROM `company` t WHERE t.`id` = ? LIMIT 1 
[13:11:21:429] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 6257(Integer)
[13:11:21:508] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:11:21:511] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`type` , t.`name` , t.`contact` , t.`telephone` , t.`desc` , t.`enable_register_code` , t.`register_code` , t.`address` , t.`create_time` , t.`enabled` , t.`user_id` , t.`code` , t.`first_channel` , t.`sys_config` , t.`storage_capacity` , t.`residual_flow` , t.`qkk_qiye_corpid` , t.`qkk_qiye_permanent_code` , t.`qkk_qiye_agent_id` , t.`qkk_qiye_auth_mode` FROM `company` t WHERE t.`id` = ? LIMIT 1 
[13:11:21:512] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 6257(Integer)
[13:11:21:595] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:11:21:599] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:215) - activeProfile:test-etea
[13:11:21:600] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:222) - targetUrl:https://www.qikaokao.com/qiyeweixin/#/qiyeweixinCompanyInfo?companyId=6257&qkkQiyeCorpid=wpfqwcEQAARpzOc8-M3cDAq2_ZeX_-2A&qkkQiyeAgentId=1000104
[13:11:21:600] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:226) - serviceUrl:http://127.0.0.1:8077/api/ocr/recognizeUrl?url=%s&ocr=ali
[13:11:21:600] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:229) - url:http://127.0.0.1:8077/api/ocr/recognizeUrl?url=https%3A%2F%2Fwww.qikaokao.com%2Fqiyeweixin%2F%23%2FqiyeweixinCompanyInfo%3FcompanyId%3D6257%26qkkQiyeCorpid%3DwpfqwcEQAARpzOc8-M3cDAq2_ZeX_-2A%26qkkQiyeAgentId%3D1000104&ocr=ali
[13:11:32:792] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:241) - result:{data=[部门列表, $$ 9413 $$ 技术委员会$$分隔符, 人员列表, $$ 1729855 $$ 罗正平$$ 分隔符 $$ 1729856 $$ 王春雷 $$ 分隔符, $$ 1729857 $$$文刘润洋$$分隔符 $$ 1729858 $$ 林宜锴 $$ 分隔符, $$ 1729859 $$ 石顷田$$ 分隔符 $$ 1729860 $$鲁方$$分隔符$$1729861 $$ 谭鹏 $$ 分隔符, $$ 1729862 $$ 王大勇$$ 分隔符 $$ 1729863 $$ 肖建光 $$ 分隔符, $$ 1729864 $$ 许俊飞 $$ 分隔符 $$ 1729865 $$ 谢展扬 $$ 分隔符, $$ 1729866 $$ 陈丽华 $$ 分隔符], errorMessage=success, errorCode=0}
[13:11:32:798] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9413, departName=技术委员会, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[13:11:32:867] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `company_department` SET `depart_name`=? WHERE `id` = ? 
[13:11:32:869] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 技术委员会(String), 9413(Integer)
[13:11:32:958] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[13:11:32:959] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[13:11:32:959] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:11:32:959] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1729855, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=罗正平, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[13:11:32:961] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[13:11:32:962] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 罗正平(String), 1729855(Integer)
[13:11:33:038] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[13:11:33:040] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1729856, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=王春雷, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[13:11:33:042] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[13:11:33:043] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1729855(Integer)
[13:11:33:104] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:11:33:109] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[13:11:33:110] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 王春雷(String), 1729856(Integer)
[13:11:33:178] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[13:11:33:178] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:11:33:179] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1729857, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=$文刘润洋, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[13:11:33:179] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[13:11:33:180] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[13:11:33:180] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1729856(Integer)
[13:11:33:181] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: $文刘润洋(String), 1729857(Integer)
[13:11:33:238] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:11:33:259] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[13:11:33:259] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1729858, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=林宜锴, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[13:11:33:260] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[13:11:33:260] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1729857(Integer)
[13:11:33:261] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[13:11:33:261] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 林宜锴(String), 1729858(Integer)
[13:11:33:317] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:11:33:370] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[13:11:33:371] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1729859, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=石顷田, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[13:11:33:371] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[13:11:33:372] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1729858(Integer)
[13:11:33:372] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[13:11:33:372] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 石顷田(String), 1729859(Integer)
[13:11:33:447] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:11:33:467] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[13:11:33:468] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1729860, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=鲁方, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[13:11:33:469] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[13:11:33:470] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1729859(Integer)
[13:11:33:470] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[13:11:33:471] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 鲁方(String), 1729860(Integer)
[13:11:33:525] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:11:33:561] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[13:11:33:562] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1729861, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=谭鹏, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[13:11:33:563] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[13:11:33:565] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1729860(Integer)
[13:11:33:567] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[13:11:33:568] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 谭鹏(String), 1729861(Integer)
[13:11:33:623] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:11:33:671] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[13:11:33:674] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1729862, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=王大勇, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[13:11:33:676] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[13:11:33:677] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1729861(Integer)
[13:11:33:678] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[13:11:33:679] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 王大勇(String), 1729862(Integer)
[13:11:33:742] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:11:33:799] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[13:11:33:802] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1729863, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=肖建光, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[13:11:33:806] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[13:11:33:809] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1729862(Integer)
[13:11:33:809] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[13:11:33:810] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 肖建光(String), 1729863(Integer)
[13:11:33:862] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:11:33:891] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[13:11:33:891] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1729864, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=许俊飞, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[13:11:33:891] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[13:11:33:892] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1729863(Integer)
[13:11:33:894] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[13:11:33:894] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 许俊飞(String), 1729864(Integer)
[13:11:33:950] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:11:34:010] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[13:11:34:010] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1729865, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=谢展扬, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[13:11:34:011] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[13:11:34:011] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1729864(Integer)
[13:11:34:011] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[13:11:34:011] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 谢展扬(String), 1729865(Integer)
[13:11:34:079] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:11:34:106] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[13:11:34:106] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1729866, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=陈丽华, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[13:11:34:106] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[13:11:34:107] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1729865(Integer)
[13:11:34:107] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[13:11:34:107] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 陈丽华(String), 1729866(Integer)
[13:11:34:157] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:11:34:210] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[13:11:34:211] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[13:11:34:211] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1729866(Integer)
[13:11:34:262] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:12:34:281] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /*************:49554
[13:12:42:231] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /company/getQiyeweixinCompanyList, authentication required: false
[13:12:42:234] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1847f615 for /company/getQiyeweixinCompanyList
[13:12:42:234] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /company/getQiyeweixinCompanyList
[13:12:42:337] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`type` , t.`name` , t.`contact` , t.`telephone` , t.`desc` , t.`enable_register_code` , t.`register_code` , t.`address` , t.`create_time` , t.`enabled` , t.`user_id` , t.`code` , t.`first_channel` , t.`sys_config` , t.`storage_capacity` , t.`residual_flow` , t.`qkk_qiye_corpid` , t.`qkk_qiye_permanent_code` , t.`qkk_qiye_agent_id` , t.`qkk_qiye_auth_mode` FROM `company` t WHERE enabled = ? AND (qkk_qiye_corpid IS NOT NULL) ORDER BY id DESC LIMIT ?,? 
[13:12:42:344] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 20(Integer), 20(Integer)
[13:12:42:417] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 9
[13:12:42:418] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:12:42:430] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:12:42:431] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6040(Integer), 1(Integer), 3(Integer)
[13:12:42:538] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 6
[13:12:42:540] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:12:42:549] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:12:42:549] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6025(Integer), 1(Integer), 3(Integer)
[13:12:42:655] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 2
[13:12:42:661] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:12:42:662] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6023(Integer), 1(Integer), 3(Integer)
[13:12:42:748] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[13:12:42:749] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:12:42:750] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6018(Integer), 1(Integer), 3(Integer)
[13:12:42:843] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[13:12:42:846] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:12:42:846] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6016(Integer), 1(Integer), 3(Integer)
[13:12:42:921] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[13:12:42:932] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:12:42:933] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6014(Integer), 1(Integer), 3(Integer)
[13:12:43:002] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:12:43:008] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:12:43:009] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6012(Integer), 1(Integer), 3(Integer)
[13:12:43:090] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:12:43:096] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:12:43:097] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6007(Integer), 1(Integer), 3(Integer)
[13:12:43:199] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[13:12:43:202] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[13:12:43:202] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6002(Integer), 1(Integer), 3(Integer)
[13:12:43:283] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:12:43:286] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT count(*) FROM `company` t WHERE enabled = ? AND (qkk_qiye_corpid IS NOT NULL) 
[13:12:43:287] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean)
[13:12:43:336] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[13:12:43:337] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:13:43:407] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /*************:50544
