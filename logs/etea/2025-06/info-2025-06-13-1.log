[20:42:30:750] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[20:42:35:163] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[20:42:35:246] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[20:42:35:247] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[20:42:35:247] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[20:42:35:247] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[20:42:35:247] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[20:42:35:247] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[20:42:35:247] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[20:42:35:247] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[20:42:35:248] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[20:42:35:248] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[20:42:35:248] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[20:42:35:249] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[20:42:35:327] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[20:42:35:999] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[20:42:36:007] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[20:42:37:135] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[20:42:37:176] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[20:42:37:649] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[20:42:37:659] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[20:42:37:661] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[20:42:37:661] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[20:42:37:662] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[20:42:37:663] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[20:42:37:665] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[20:42:37:667] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[20:42:37:668] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[20:42:42:247] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[20:42:42:264] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[20:42:42:785] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[20:42:44:954] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：2.155秒
[20:42:56:999] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[20:42:57:163] [WARN] - io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:70) - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
[20:42:58:069] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[20:42:58:684] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[20:43:05:934] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[20:43:06:771] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 53 ms to scan 1 urls, producing 3 keys and 6 values 
[20:43:06:852] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 29 ms to scan 1 urls, producing 4 keys and 9 values 
[20:43:06:889] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 31 ms to scan 1 urls, producing 3 keys and 10 values 
[20:43:07:020] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 107 ms to scan 12 urls, producing 0 keys and 0 values 
[20:43:07:068] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
[20:43:07:088] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
[20:43:07:118] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 20 ms to scan 1 urls, producing 2 keys and 8 values 
[20:43:07:162] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 38 ms to scan 12 urls, producing 0 keys and 0 values 
[20:43:08:413] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[20:43:08:414] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[20:43:08:435] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[20:43:08:459] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[20:43:08:489] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[20:43:08:573] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[20:43:08:682] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service 192.168.31.135:8081 register finished
[20:43:08:932] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 40.967 seconds (JVM running for 42.363)
[20:43:08:959] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
