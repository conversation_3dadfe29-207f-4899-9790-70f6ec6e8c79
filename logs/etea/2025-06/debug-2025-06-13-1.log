[20:42:30:750] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[20:42:33:776] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$AutoConfiguredMapperScannerRegistrar.registerBeanDefinitions(MybatisAutoConfiguration.java:252) - Searching for mappers annotated with @Mapper
[20:42:33:788] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$AutoConfiguredMapperScannerRegistrar.registerBeanDefinitions(MybatisAutoConfiguration.java:264) - Using auto-configuration base package 'com.taurus.examinationassistant'
[20:42:35:163] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[20:42:35:246] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[20:42:35:247] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[20:42:35:247] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[20:42:35:247] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[20:42:35:247] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[20:42:35:247] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[20:42:35:247] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[20:42:35:247] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[20:42:35:248] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[20:42:35:248] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[20:42:35:248] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[20:42:35:249] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[20:42:35:327] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[20:42:35:999] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[20:42:36:007] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[20:42:37:135] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[20:42:37:163] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[20:42:37:176] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[20:42:37:515] [DEBUG] - io.micrometer.core.util.internal.logging.InternalLoggerFactory.newDefaultFactory(InternalLoggerFactory.java:59) - Using SLF4J as the default logging framework
[20:42:37:649] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[20:42:37:659] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[20:42:37:661] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[20:42:37:661] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[20:42:37:662] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[20:42:37:663] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[20:42:37:665] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[20:42:37:667] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[20:42:37:668] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[20:42:38:526] [DEBUG] - io.undertow.server.session.InMemorySessionManager.registerSessionListener(InMemorySessionManager.java:268) - Registered session listener io.undertow.servlet.core.SessionListenerBridge@4fd34de
[20:42:38:638] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[20:42:42:247] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[20:42:42:264] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[20:42:42:785] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[20:42:42:799] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.GameMapper对应的Mapper
[20:42:43:037] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookPraiseMapper对应的Mapper
[20:42:43:079] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MultipleMarkingExaminationInstanceMapper对应的Mapper
[20:42:43:132] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MessagePushSubscriptionMapper对应的Mapper
[20:42:43:174] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QrcodeScanEntryMapper对应的Mapper
[20:42:43:210] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QiyewxInterfaceLicenseOrderMapper对应的Mapper
[20:42:43:250] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PracticePushScheduleMapper对应的Mapper
[20:42:43:297] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationNameListMapper对应的Mapper
[20:42:43:325] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MessageTemplateMapper对应的Mapper
[20:42:43:340] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperQuestionMapper对应的Mapper
[20:42:43:362] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductIntroductionMapper对应的Mapper
[20:42:43:403] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentClassificationInfoMapper对应的Mapper
[20:42:43:461] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsitePaperInstanceMapper对应的Mapper
[20:42:43:495] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductCountTransactionMapper对应的Mapper
[20:42:43:525] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionRandomRangeMapper对应的Mapper
[20:42:43:540] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarUserProfileMapper对应的Mapper
[20:42:43:557] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.NameListMapper对应的Mapper
[20:42:43:578] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ViewPerformanceWithoutAdTransactionMapper对应的Mapper
[20:42:43:594] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongQuestionBookReviewSummaryMapper对应的Mapper
[20:42:43:604] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookTagMapper对应的Mapper
[20:42:43:616] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteOrderMapper对应的Mapper
[20:42:43:624] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationExamineeMapper对应的Mapper
[20:42:43:653] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyRegisterFormMapper对应的Mapper
[20:42:43:676] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.DiscountMapper对应的Mapper
[20:42:43:710] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SysLogMapper对应的Mapper
[20:42:43:718] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PointDetailMapper对应的Mapper
[20:42:43:727] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PointRuleMapper对应的Mapper
[20:42:43:744] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserSummaryMapper对应的Mapper
[20:42:43:752] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.VersionMapper对应的Mapper
[20:42:43:761] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarMapper对应的Mapper
[20:42:43:773] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarUserMapper对应的Mapper
[20:42:43:781] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AppMapper对应的Mapper
[20:42:43:788] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.HomepageUserFollowedMapper对应的Mapper
[20:42:43:795] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionExampleDeletedMapper对应的Mapper
[20:42:43:801] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongQuestionBookReviewProcessMapper对应的Mapper
[20:42:43:807] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationInstanceEventMapper对应的Mapper
[20:42:43:817] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PassRaceResultMapper对应的Mapper
[20:42:43:835] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserFormFlowRecordMapper对应的Mapper
[20:42:43:850] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WxMiniprogramSubscribeMessageOfEteaMapper对应的Mapper
[20:42:43:868] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductIntroductionMediaMapper对应的Mapper
[20:42:43:877] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyJobGradeMapper对应的Mapper
[20:42:43:885] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookSummaryMapper对应的Mapper
[20:42:43:895] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.GameResultMapper对应的Mapper
[20:42:43:909] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionMapper对应的Mapper
[20:42:43:983] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionAttachmentMapper对应的Mapper
[20:42:43:994] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyDepartmentMapper对应的Mapper
[20:42:44:007] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserReportRelationMapper对应的Mapper
[20:42:44:015] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationExamineeSnapshotMapper对应的Mapper
[20:42:44:024] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookMapper对应的Mapper
[20:42:44:031] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongQuestionBookQuestionMapper对应的Mapper
[20:42:44:043] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperTagMapper对应的Mapper
[20:42:44:055] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookUserRecordMapper对应的Mapper
[20:42:44:066] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentSubjectOfUserMapper对应的Mapper
[20:42:44:080] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyRegisterFormFlowRecordMapper对应的Mapper
[20:42:44:088] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarUserMilestoneMapper对应的Mapper
[20:42:44:096] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserMapper对应的Mapper
[20:42:44:105] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyFunctionRoleMapper对应的Mapper
[20:42:44:112] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CustomerRequirementMapper对应的Mapper
[20:42:44:121] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteWithdrawMapper对应的Mapper
[20:42:44:141] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.TaxIdentityInfoMapper对应的Mapper
[20:42:44:159] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyUserGroupMapper对应的Mapper
[20:42:44:174] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteContentAccessMapper对应的Mapper
[20:42:44:193] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteOrderRelationMapper对应的Mapper
[20:42:44:199] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.NameListDetailMapper对应的Mapper
[20:42:44:207] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QiyewxOrderMapper对应的Mapper
[20:42:44:219] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationMapper对应的Mapper
[20:42:44:239] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QiyewxUserAccountLicenseMapper对应的Mapper
[20:42:44:263] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.GameQuestionRandomRuleMapper对应的Mapper
[20:42:44:279] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyMapper对应的Mapper
[20:42:44:302] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionSummaryMapper对应的Mapper
[20:42:44:320] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UniversalOrderMapper对应的Mapper
[20:42:44:332] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserCompanyMapper对应的Mapper
[20:42:44:340] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongBookMapper对应的Mapper
[20:42:44:348] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.NanxiangRegisterMapper对应的Mapper
[20:42:44:363] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.FileAccessPermissionMapper对应的Mapper
[20:42:44:379] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserFormMapper对应的Mapper
[20:42:44:399] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationFavoriteMapper对应的Mapper
[20:42:44:412] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.FreePracticeOfUserMapper对应的Mapper
[20:42:44:430] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SmsMessageMapper对应的Mapper
[20:42:44:439] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ManualServiceRecordMapper对应的Mapper
[20:42:44:446] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteUserSignatureMapper对应的Mapper
[20:42:44:454] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SysRoleMapper对应的Mapper
[20:42:44:468] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookPracticeSummaryMapper对应的Mapper
[20:42:44:486] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionFavoriteMapper对应的Mapper
[20:42:44:496] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.OrdersMapper对应的Mapper
[20:42:44:504] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookPracticeProcessMapper对应的Mapper
[20:42:44:517] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserOfProductMapper对应的Mapper
[20:42:44:539] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AppStartNoticeMapper对应的Mapper
[20:42:44:550] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookQuestionMapper对应的Mapper
[20:42:44:555] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionRandomMapper对应的Mapper
[20:42:44:561] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PassRaceGameMapper对应的Mapper
[20:42:44:571] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AppStartNoticeReadUserMapper对应的Mapper
[20:42:44:578] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationStartupMapper对应的Mapper
[20:42:44:586] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SysUserRoleMapper对应的Mapper
[20:42:44:596] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongQuestionBookMapper对应的Mapper
[20:42:44:603] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationWxGidMapper对应的Mapper
[20:42:44:619] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AppGroupUserMapper对应的Mapper
[20:42:44:647] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WeixinAccountMapper对应的Mapper
[20:42:44:654] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyGroupUserMapper对应的Mapper
[20:42:44:662] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationInstanceMapper对应的Mapper
[20:42:44:668] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MultipleMarkingExaminationMapper对应的Mapper
[20:42:44:674] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteFavoriteMapper对应的Mapper
[20:42:44:679] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PermissionMapper对应的Mapper
[20:42:44:685] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.TemplateMapper对应的Mapper
[20:42:44:690] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.TaxSheetApplicationMapper对应的Mapper
[20:42:44:696] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserOfYncxMapper对应的Mapper
[20:42:44:700] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PermissionAuthorizationMapper对应的Mapper
[20:42:44:711] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperPraiseMapper对应的Mapper
[20:42:44:718] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductOrderRelationCountMapper对应的Mapper
[20:42:44:722] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentUnlockMapper对应的Mapper
[20:42:44:728] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationTagMapper对应的Mapper
[20:42:44:732] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookFavoriteMapper对应的Mapper
[20:42:44:737] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CategoryMapper对应的Mapper
[20:42:44:742] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserExaminationPerformanceReportMapper对应的Mapper
[20:42:44:747] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperFavoriteMapper对应的Mapper
[20:42:44:755] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarStageEventMapper对应的Mapper
[20:42:44:762] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.InviteRelationMapper对应的Mapper
[20:42:44:773] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentSubjectCategoryMapper对应的Mapper
[20:42:44:779] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SystemMessageMapper对应的Mapper
[20:42:44:784] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AnnualReportMapper对应的Mapper
[20:42:44:789] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentSubjectAccessInfoMapper对应的Mapper
[20:42:44:794] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductMapper对应的Mapper
[20:42:44:799] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductOrderRelationMapper对应的Mapper
[20:42:44:805] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PointConsumerationMapper对应的Mapper
[20:42:44:810] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentProductOfUserMapper对应的Mapper
[20:42:44:816] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MessagePlanMapper对应的Mapper
[20:42:44:822] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.OssFileMapper对应的Mapper
[20:42:44:832] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationInstanceProcessMapper对应的Mapper
[20:42:44:841] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsitePaperInstanceProcessMapper对应的Mapper
[20:42:44:850] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookExamineeMapper对应的Mapper
[20:42:44:863] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionBankQuestionMapper对应的Mapper
[20:42:44:870] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationGroupExaminationMapper对应的Mapper
[20:42:44:875] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.OssFileAudioMapper对应的Mapper
[20:42:44:880] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MessageScheduleMapper对应的Mapper
[20:42:44:886] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperMapper对应的Mapper
[20:42:44:893] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserExaminationInstanceReportMapper对应的Mapper
[20:42:44:898] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookGradeMapper对应的Mapper
[20:42:44:907] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserDeviceBindingMapper对应的Mapper
[20:42:44:916] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionCompositionChildMapper对应的Mapper
[20:42:44:922] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserViewPerformanceWithoutAdMapper对应的Mapper
[20:42:44:926] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PassRaceStageMapper对应的Mapper
[20:42:44:933] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UniversalProductOrderRelationMapper对应的Mapper
[20:42:44:939] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PointUserMapper对应的Mapper
[20:42:44:948] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentSubjectMapper对应的Mapper
[20:42:44:954] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：2.155秒
[20:42:56:896] [DEBUG] - io.netty.util.internal.logging.InternalLoggerFactory.useSlf4JLoggerFactory(InternalLoggerFactory.java:63) - Using SLF4J as the default logging framework
[20:42:56:910] [DEBUG] - io.netty.util.concurrent.GlobalEventExecutor.<clinit>(GlobalEventExecutor.java:53) - -Dio.netty.globalEventExecutor.quietPeriodSeconds: 1
[20:42:56:932] [DEBUG] - io.netty.util.internal.InternalThreadLocalMap.<clinit>(InternalThreadLocalMap.java:100) - -Dio.netty.threadLocalMap.stringBuilder.initialSize: 1024
[20:42:56:932] [DEBUG] - io.netty.util.internal.InternalThreadLocalMap.<clinit>(InternalThreadLocalMap.java:101) - -Dio.netty.threadLocalMap.stringBuilder.maxSize: 4096
[20:42:56:999] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[20:42:57:010] [DEBUG] - io.netty.channel.MultithreadEventLoopGroup.<clinit>(MultithreadEventLoopGroup.java:44) - -Dio.netty.eventLoopThreads: 16
[20:42:57:062] [DEBUG] - io.netty.util.internal.PlatformDependent0.explicitNoUnsafeCause0(PlatformDependent0.java:515) - -Dio.netty.noUnsafe: false
[20:42:57:066] [DEBUG] - io.netty.util.internal.PlatformDependent0.javaVersion0(PlatformDependent0.java:1026) - Java version: 8
[20:42:57:075] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:140) - sun.misc.Unsafe.theUnsafe: available
[20:42:57:080] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:164) - sun.misc.Unsafe.copyMemory: available
[20:42:57:085] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:196) - sun.misc.Unsafe.storeFence: available
[20:42:57:089] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:239) - java.nio.Buffer.address: available
[20:42:57:090] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:312) - direct buffer constructor: available
[20:42:57:092] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:403) - java.nio.Bits.unaligned: available, true
[20:42:57:093] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:478) - jdk.internal.misc.Unsafe.allocateUninitializedArray(int): unavailable prior to Java9
[20:42:57:093] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:501) - java.nio.DirectByteBuffer.<init>(long, {int,long}): available
[20:42:57:094] [DEBUG] - io.netty.util.internal.PlatformDependent.unsafeUnavailabilityCause0(PlatformDependent.java:1157) - sun.misc.Unsafe: available
[20:42:57:095] [DEBUG] - io.netty.util.internal.PlatformDependent.tmpdir0(PlatformDependent.java:1303) - -Dio.netty.tmpdir: /var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T (java.io.tmpdir)
[20:42:57:096] [DEBUG] - io.netty.util.internal.PlatformDependent.bitMode0(PlatformDependent.java:1382) - -Dio.netty.bitMode: 64 (sun.arch.data.model)
[20:42:57:101] [DEBUG] - io.netty.util.internal.PlatformDependent.isOsx0(PlatformDependent.java:1125) - Platform: MacOS
[20:42:57:106] [DEBUG] - io.netty.util.internal.PlatformDependent.<clinit>(PlatformDependent.java:176) - -Dio.netty.maxDirectMemory: 3817865216 bytes
[20:42:57:107] [DEBUG] - io.netty.util.internal.PlatformDependent.<clinit>(PlatformDependent.java:183) - -Dio.netty.uninitializedArrayAllocationThreshold: -1
[20:42:57:111] [DEBUG] - io.netty.util.internal.CleanerJava6.<clinit>(CleanerJava6.java:92) - java.nio.ByteBuffer.cleaner(): available
[20:42:57:112] [DEBUG] - io.netty.util.internal.PlatformDependent.<clinit>(PlatformDependent.java:203) - -Dio.netty.noPreferDirect: false
[20:42:57:113] [DEBUG] - io.netty.channel.nio.NioEventLoop.<clinit>(NioEventLoop.java:110) - -Dio.netty.noKeySetOptimization: false
[20:42:57:114] [DEBUG] - io.netty.channel.nio.NioEventLoop.<clinit>(NioEventLoop.java:111) - -Dio.netty.selectorAutoRebuildThreshold: 512
[20:42:57:125] [DEBUG] - io.netty.util.internal.PlatformDependent$Mpsc.<clinit>(PlatformDependent.java:1008) - org.jctools-core.MpscChunkedArrayQueue: available
[20:42:57:163] [WARN] - io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:70) - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
[20:42:57:177] [DEBUG] - io.netty.util.NetUtil.<clinit>(NetUtil.java:148) - -Djava.net.preferIPv4Stack: false
[20:42:57:178] [DEBUG] - io.netty.util.NetUtil.<clinit>(NetUtil.java:149) - -Djava.net.preferIPv6Addresses: false
[20:42:57:182] [DEBUG] - io.netty.util.NetUtilInitializations.determineLoopback(NetUtilInitializations.java:145) - Loopback interface: lo0 (lo0, 0:0:0:0:0:0:0:1%lo0)
[20:42:57:184] [DEBUG] - io.netty.util.NetUtil$SoMaxConnAction.run(NetUtil.java:206) - Failed to get SOMAXCONN from sysctl and file /proc/sys/net/core/somaxconn. Default: 128
[20:42:57:220] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:141) - Default ResolvedAddressTypes: IPV4_PREFERRED
[20:42:57:220] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:142) - Localhost address: localhost/127.0.0.1
[20:42:57:221] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:151) - Windows hostname: null
[20:42:57:224] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:164) - Default search domains: []
[20:42:57:226] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:173) - Default UnixResolverOptions{ndots=1, timeout=5, attempts=16}
[20:42:57:242] [DEBUG] - io.netty.resolver.DefaultHostsFileEntriesResolver.<clinit>(DefaultHostsFileEntriesResolver.java:53) - -Dio.netty.hostsFileRefreshInterval: 0
[20:42:57:267] [DEBUG] - io.netty.util.ResourceLeakDetector.<clinit>(ResourceLeakDetector.java:129) - -Dio.netty.leakDetection.level: simple
[20:42:57:271] [DEBUG] - io.netty.util.ResourceLeakDetector.<clinit>(ResourceLeakDetector.java:130) - -Dio.netty.leakDetection.targetRecords: 4
[20:42:57:272] [DEBUG] - io.netty.util.ResourceLeakDetectorFactory$DefaultResourceLeakDetectorFactory.newResourceLeakDetector(ResourceLeakDetectorFactory.java:196) - Loaded default ResourceLeakDetector: io.netty.util.ResourceLeakDetector@1c69b1e7
[20:42:57:427] [DEBUG] - io.netty.channel.DefaultChannelId.<clinit>(DefaultChannelId.java:79) - -Dio.netty.processId: 63469 (auto-detected)
[20:42:57:442] [DEBUG] - io.netty.channel.DefaultChannelId.<clinit>(DefaultChannelId.java:101) - -Dio.netty.machineId: 16:1e:07:ff:fe:46:12:6d (auto-detected)
[20:42:57:478] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:157) - -Dio.netty.allocator.numHeapArenas: 16
[20:42:57:479] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:158) - -Dio.netty.allocator.numDirectArenas: 16
[20:42:57:480] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:160) - -Dio.netty.allocator.pageSize: 8192
[20:42:57:481] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:165) - -Dio.netty.allocator.maxOrder: 9
[20:42:57:481] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:169) - -Dio.netty.allocator.chunkSize: 4194304
[20:42:57:482] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:170) - -Dio.netty.allocator.smallCacheSize: 256
[20:42:57:483] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:171) - -Dio.netty.allocator.normalCacheSize: 64
[20:42:57:483] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:172) - -Dio.netty.allocator.maxCachedBufferCapacity: 32768
[20:42:57:484] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:173) - -Dio.netty.allocator.cacheTrimInterval: 8192
[20:42:57:484] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:174) - -Dio.netty.allocator.cacheTrimIntervalMillis: 0
[20:42:57:485] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:175) - -Dio.netty.allocator.useCacheForAllThreads: false
[20:42:57:485] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:176) - -Dio.netty.allocator.maxCachedByteBuffersPerChunk: 1023
[20:42:57:503] [DEBUG] - io.netty.buffer.ByteBufUtil.<clinit>(ByteBufUtil.java:89) - -Dio.netty.allocator.type: pooled
[20:42:57:505] [DEBUG] - io.netty.buffer.ByteBufUtil.<clinit>(ByteBufUtil.java:98) - -Dio.netty.threadLocalDirectBufferSize: 0
[20:42:57:505] [DEBUG] - io.netty.buffer.ByteBufUtil.<clinit>(ByteBufUtil.java:101) - -Dio.netty.maxThreadLocalCharBufferSize: 16384
[20:42:57:532] [DEBUG] - io.netty.bootstrap.ChannelInitializerExtensions.getExtensions(ChannelInitializerExtensions.java:54) - -Dio.netty.bootstrap.extensions: null
[20:42:57:737] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[20:42:57:736] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[20:42:57:737] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[20:42:57:881] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:96) - -Dio.netty.recycler.maxCapacityPerThread: 4096
[20:42:57:882] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:97) - -Dio.netty.recycler.ratio: 8
[20:42:57:883] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:98) - -Dio.netty.recycler.chunkSize: 32
[20:42:57:883] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:99) - -Dio.netty.recycler.blocking: false
[20:42:57:883] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:100) - -Dio.netty.recycler.batchFastThreadLocalOnly: true
[20:42:57:892] [DEBUG] - io.netty.buffer.AbstractByteBuf.<clinit>(AbstractByteBuf.java:63) - -Dio.netty.buffer.checkAccessible: true
[20:42:57:892] [DEBUG] - io.netty.buffer.AbstractByteBuf.<clinit>(AbstractByteBuf.java:64) - -Dio.netty.buffer.checkBounds: true
[20:42:57:892] [DEBUG] - io.netty.util.ResourceLeakDetectorFactory$DefaultResourceLeakDetectorFactory.newResourceLeakDetector(ResourceLeakDetectorFactory.java:196) - Loaded default ResourceLeakDetector: io.netty.util.ResourceLeakDetector@4dde5425
[20:42:58:044] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1869797148 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xa1f7b814, L:/192.168.31.135:58457 - R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0]
[20:42:58:044] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@811620183 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xc8bc8498, L:/192.168.31.135:58458 - R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0]
[20:42:58:050] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connectPubSub$3(ClientConnectionsEntry.java:234) - new pubsub connection created: RedisPubSubConnection@2064401528 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x8d9aa8fd, L:/192.168.31.135:58459 - R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0]
[20:42:58:069] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for 47.111.231.172/47.111.231.172:6379
[20:42:58:096] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[20:42:58:101] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[20:42:58:141] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1057369320 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xa20f8b8d, L:/192.168.31.135:58466 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@31c1d578[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[20:42:58:141] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@909174670 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xc3d41572, L:/192.168.31.135:58467 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@38f70f38[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[20:42:58:151] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[20:42:58:155] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[20:42:58:195] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1684184112 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xc4a51854, L:/192.168.31.135:58468 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@6aa56ecc[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[20:42:58:195] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1958419234 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x3d00a825, L:/192.168.31.135:58469 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@7a7cfbc5[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[20:42:58:222] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[20:42:58:228] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[20:42:58:260] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@192777639 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x05af84d3, L:/192.168.31.135:58470 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@5ba62b8[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[20:42:58:260] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@125506917 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x676bbe02, L:/192.168.31.135:58471 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@9bcebfa[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[20:42:58:267] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[20:42:58:268] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[20:42:58:308] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1299209728 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x4f35c21b, L:/192.168.31.135:58473 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@43b7b9de[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[20:42:58:308] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@881677936 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7e81e5c8, L:/192.168.31.135:58472 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@3a4abe91[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[20:42:58:311] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[20:42:58:312] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[20:42:58:346] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@180635007 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x8b04c1eb, L:/192.168.31.135:58478 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@403ba57[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[20:42:58:346] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@46058448 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x8e481b8e, L:/192.168.31.135:58477 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@c7925f7[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[20:42:58:361] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[20:42:58:363] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[20:42:58:415] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@952283489 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x128fc9e2, L:/192.168.31.135:58480 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@36055809[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[20:42:58:415] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1881834002 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xaa8a6bd4, L:/192.168.31.135:58479 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@7eed7267[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[20:42:58:438] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[20:42:58:439] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[20:42:58:479] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@93987278 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x5b6d3675, L:/192.168.31.135:58482 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@70297b19[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[20:42:58:480] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@320139621 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x8d4940a0, L:/192.168.31.135:58481 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@a65a709[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[20:42:58:483] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[20:42:58:483] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[20:42:58:521] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@92805660 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xe9f7bc46, L:/192.168.31.135:58484 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@55d4e9ca[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[20:42:58:521] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1051180843 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x736853f6, L:/192.168.31.135:58483 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@3a983c27[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[20:42:58:543] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[20:42:58:543] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[20:42:58:590] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@905856920 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xabf71612, L:/192.168.31.135:58486 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@70fcdf43[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[20:42:58:590] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1019776254 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6f86eeee, L:/192.168.31.135:58487 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@79ca0503[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[20:42:58:616] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[20:42:58:616] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[20:42:58:652] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@2118348882 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xe2fdd120, L:/192.168.31.135:58490 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@3b41f1fe[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[20:42:58:652] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1855649600 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7bb4e9b2, L:/192.168.31.135:58491 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@2b9864f7[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[20:42:58:654] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[20:42:58:654] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[20:42:58:683] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@226721301 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7b9217d8, L:/192.168.31.135:58495 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@4881e1c1[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[20:42:58:683] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@25552599 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd1e2378f, L:/192.168.31.135:58494 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@44876883[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[20:42:58:684] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for 47.111.231.172/47.111.231.172:6379
[20:43:03:394] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.AccessController:
	{GET [/access/getTokenByTime]}: getTokenByTime(Integer,Long,HttpServletRequest)
	{GET [/access/getTokenUnlimit]}: getTokenUnlimit(Integer,HttpServletRequest)
	{GET [/access/heartbeat]}: heartbeat()
	{GET [/access/getTokenStr]}: getToken(Integer,HttpServletRequest)
[20:43:03:398] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ActivityController:
	{ [/activity/getActiveActivities]}: getActiveActivities(String)
[20:43:03:403] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.AnnualReportController:
	{GET [/annual/getReport]}: getEntity(Integer)
[20:43:03:404] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.AppStartNoticeController:
	{GET [/appStartNotice/getCurrentNoticeOfUser]}: getCurrentNoticeOfUser(String,Integer)
	{GET [/appStartNotice/getCurrentNotice]}: getCurrentNotice(String)
[20:43:03:406] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.AppStartNoticeReadUseController:
	{GET [/appStartNoticeReadUser/save]}: save(Integer,Integer)
[20:43:03:416] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CompanyController:
	{POST [/company/createCompany]}: createCompany(JSONObject)
	{POST [/company/createDepartment]}: createDepartment(JSONObject)
	{POST [/company/createJobGrade]}: createJobGrade(JSONObject)
	{POST [/company/createFunctionRole]}: createFunctionRole(JSONObject)
	{GET [/company/deleteCompany]}: deleteCompany(int)
	{GET [/company/deleteDepartment]}: deleteDepartment(int)
	{GET [/company/deleteDepartmentCarefully]}: deleteDepartmentCarefully(Integer,Integer)
	{GET [/company/deleteJobGrade]}: deleteJobGrade(int)
	{GET [/company/deleteJobGradeCarefully]}: deleteJobGradeCarefully(Integer,Integer)
	{GET [/company/deleteFunctionRole]}: deleteFunctionRole(int)
	{GET [/company/deleteFunctionRoleCarefully]}: deleteFunctionRoleCarefully(Integer,Integer)
	{POST [/company/modifyCompany]}: modifyCompany(JSONObject)
	{GET [/company/modifyCompanyName]}: modifyCompanyName(String,Integer)
	{POST [/company/modifyDepartment]}: modifyDepartment(JSONObject)
	{POST [/company/modifyJobGrade]}: modifyJobGrade(JSONObject)
	{POST [/company/modifyFunctionRole]}: modifyFunctionRole(JSONObject)
	{GET [/company/getCompanyInfo]}: getCompanyInfo(Integer)
	{GET [/company/getCompanyInfoById]}: getCompanyInfoWrapperById(int)
	{GET [/company/getCompanyInfoByCodeOrName]}: getCompanyInfoByCodeOrName(String)
	{GET [/company/getCompanyInfoListByName]}: getCompanyInfoListByName(String)
	{GET [/company/getCompanyInfoByUserId]}: getCompanyInfoByUserId(int)
	{GET [/company/getAllDepartmentOfCompany]}: getAllDepartmentOfCompany(Integer,Integer)
	{GET [/company/getNGradeDepartment]}: getLevelDepartmentOfLessThanOrEqualDesignativeGrade(Integer,Integer)
	{GET [/company/getAllDepartmentOfCompanyByUserId]}: getAllDepartmentOfCompanyByUserId(Integer)
	{GET [/company/getSubDepartmentExcludingSelf]}: getSubDepartmentExcludingSelf(String)
	{GET [/company/getSubDepartmentIncludingSelf]}: getSubDepartmentIncludingSelf(String)
	{GET [/company/getAllCompanyJobGradeOfCompany]}: getAllCompanyJobGradeOfCompany(int)
	{GET [/company/getAllCompanyInfo]}: getAllCompanyInfo(int)
	{POST [/company/getQiyeweixinCompanyList]}: getQiyeweixinCompanyList(JSONObject)
	{GET [/company/getAllCompanyInfoWith2Grade]}: getAllCompanyInfoWith2Grade(Integer)
	{GET [/company/getAllCompanyFunctionRoleOfCompany]}: getAllCompanyFunctionRoleOfCompany(int)
	{GET [/company/search]}: searchCompanyByKeyword(String)
	{GET [/company/batchCloseAccount]}: batchCloseAccount(String)
	{GET [/company/closeAccount]}: closeAccount(Integer,Integer)
	{GET [/company/getResidualFlow]}: getResidualFlow(Integer)
	{POST [/company/getOrganizationFrameworkInfoByIds]}: getOrganizationFrameworkInfoByIds(JSONObject)
	{POST [/company/modify]}: modify(Company)
[20:43:03:423] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CompanyDepartmentController:
	{GET [/companyDepartment/getCompanyDepartmentList]}: getCompanyDepartmentList(Integer,String)
[20:43:03:425] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CompanyGroupUserController:
	{POST [/companyGroupUser/create]}: createCompanyGroupUser(CompanyGroupUser)
	{POST [/companyGroupUser/batchCreate]}: batchCreateCompanyGroupUser(List)
	{POST [/companyGroupUser/update]}: updateCompanyGroupUser(CompanyGroupUser)
	{GET [/companyGroupUser/delete]}: deleteCompanyGroupUser(Integer,String)
	{GET [/companyGroupUser/getGroupsByUserId]}: getGroupsByUserId(Integer)
	{GET [/companyGroupUser/getUsersByGroupId]}: getUsersByGroupId(Integer)
	{GET [/companyGroupUser/checkUserInGroup]}: checkUserInGroup(Integer,Integer)
[20:43:03:429] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CompanyUserGroupController:
	{GET [/companyUserGroup/list]}: getGroupList(Integer,Integer,String,String)
	{GET [/companyUserGroup/detail]}: getGroupDetail(Integer)
	{POST [/companyUserGroup/update]}: updateGroup(JSONObject)
	{GET [/companyUserGroup/delete]}: deleteGroup(Integer)
	{POST [/companyUserGroup/create]}: createGroup(JSONObject)
[20:43:03:431] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentClassificationInfoController:
	{ [/contentClassification/getPracticeContentOfUnlock]}: getPracticeContentOfUnlock()
	{ [/contentClassification/getUnlockedPracticeContentOfUser]}: getUnlockedPracticeContentOfUser(Integer)
	{ [/contentClassification/getPracticeContentOfSubject]}: getPracticeContentOfSubject(Integer,Integer)
[20:43:03:432] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentProductOfUserController:
	{GET [/contentProductOfUser/getProductListOfUser]}: getProductListOfUser(Integer)
[20:43:03:436] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentSubjectCategoryController:
	{GET [/contentSubjectCategory/getCategoryListOfSubject]}: getCategoryListOfSubject(Integer)
	{GET [/contentSubjectCategory/getSubjectListOfCategory]}: getSubjectListOfCategory(Integer)
	{GET [/contentSubjectCategory/getPathListOfSubject]}: getPathListOfSubject(Integer)
	{GET [/contentSubjectCategory/getNLevelCategoryWithContentNum]}: getLevelCategoriesOfLessThanOrEqualDesignativeLevel(String,Integer,Integer)
	{POST [/contentSubjectCategory/update]}: update(JSONArray)
	{GET [/contentSubjectCategory/delete]}: delete(Integer,Integer)
	{POST [/contentSubjectCategory/save]}: save(JSONArray)
[20:43:03:439] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentSubjectController:
	{POST [/contentSubject/createSubject]}: createSubject(JSONObject)
	{GET [/contentSubject/getContentSubjectByContent]}: getContentSubjectByContent(String,Integer)
	{GET [/contentSubject/getAllContentSubject]}: getSubjectInfoList(String,Integer,Integer,Integer)
	{GET [/contentSubject/getSubjectListOfProduct]}: getSubjectListOfProduct(String)
	{GET [/contentSubject/getAllContentSubjectWithUserSelection]}: getAllContentSubjectWithUserSelection(Integer,String)
	{GET [/contentSubject/getSubjectWrapperBySubjectId]}: getSubjectWrapperBySubjectId(Integer)
	{GET [/contentSubject/getSubjectWrapper]}: getSubjectWrapper(Integer)
	{GET [/contentSubject/getSubjectWrapperWithFavoriteInfo]}: getSubjectWrapperWithFavoriteInfo(Integer,Integer)
	{POST [/contentSubject/modifySubject]}: modifySubject(JSONObject)
	{GET [/contentSubject/delete]}: delete(Integer)
[20:43:03:441] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentSubjectOfUserController:
	{POST [/contentSubjectOfUser/saveContentSubjectsOfUser]}: saveContentSubjectsOfUser(JSONObject)
	{GET [/contentSubjectOfUser/getContentSubjectListOfUser]}: getContentSubjectListOfUser(Integer,String,Integer,Integer,Integer)
[20:43:03:443] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentSubjectSummaryController:
	{GET [/contentSubjectSummary/getSummary]}: getSummary(Integer)
[20:43:03:443] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentUnlockController:
	{POST [/contentUnlock/save]}: save(JSONObject)
[20:43:03:443] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CryptographyController:
	{ [/cryptography/decrypt]}: decrypt(String)
	{ [/cryptography/encrypt]}: encrypt(String)
[20:43:03:444] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CustomerRequirementController:
	{POST [/cs/createRequirement]}: save(JSONObject)
[20:43:03:456] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.DataResolverController:
	{GET [/dataResolver/setSelectionChildType]}: setSelectionChildType(int)
	{GET [/dataResolver/reCalculateQuestionScore]}: reCalculateQuestionScore(int,Integer,Integer,String)
	{GET [/dataResolver/reCalculateExaminationInstance]}: reCalculateExaminationInstance(int,Integer,String)
	{GET [/dataResolver/reCalculateExaminationInstanceByQuestionId]}: reCalculateExaminationInstance(int,int)
	{GET [/dataResolver/reCreateCompletionOptions]}: reCreateCompletionOptions(Integer,Integer,Integer,String)
	{GET [/dataResolver/checkDataMigrationFromFreeVersion]}: checkDataMigrationFromFreeVersion(int)
	{GET [/dataResolver/migrateDataFromFreeVersionToEnterpriseVersion]}: migrateDataFromFreeVersionToEnterpriseVersion(int,int)
	{GET [/dataResolver/deleteExaminationIncludingWrongRandomQuestion]}: deleteExaminationIncludingWrongRandomQuestion()
	{GET [/dataResolver/configUserSysUserRole]}: configUserSysUserRole()
	{GET [/dataResolver/recaculateExaminationInstanceBecauseOfNullScore]}: recaculateExaminationInstanceBecauseOfNullScore()
	{GET [/dataResolver/deleteInValidExamination]}: deleteInValidExamination()
	{GET [/dataResolver/deleteExaminationInstanceAndProcessOfEtea]}: deleteExaminationInstanceAndProcessOfEtea(String,String)
	{GET [/dataResolver/deleteInvalidSubscirbeMessage]}: deleteInvalidSubscirbeMessage(String)
	{GET [/dataResolver/clearJSONErrorStr]}: clearJSONErrorStr(Integer)
	{GET [/dataResolver/recaculateExcerciseBookInfo]}: recaculateExcerciseBookInfo()
	{GET [/dataResolver/deleteInvalidQuestion]}: deleteInvalidQuestion()
	{GET [/dataResolver/deleteInvalidPaper]}: deleteInvalidPaper()
	{GET [/dataResolver/deleteExerciseBookAndRelation]}: deleteInvalidExerciseBookAndRelation()
	{GET [/dataResolver/deleteInvalidSystemMessage]}: deleteInvalidSystemMessage(String,String,String)
	{GET [/dataResolver/deleteExaminationInstance]}: deleteExaminationInstance()
	{GET [/dataResolver/handleDeleteQuestionsEvent]}: handleDeleteQuestionsEvent()
	{GET [/dataResolver/restoreExerciseBookByProcess]}: restoreExerciseBookByProcess()
	{GET [/dataResolver/clearRedisData]}: clearRedisData(String)
	{GET [/dataResolver/clearAccountPermently]}: clearAccountPermently(Integer,Integer)
	{GET [/dataResolver/generateQuestionJsonFileOfExamination]}: generateQuestionJsonFileOfExamination(Integer,Integer,Integer)
	{GET [/dataResolver/generateHotFileListOfCDN]}: generateHotFileListOfCDN(Integer,Integer)
	{GET [/dataResolver/resizeOssPicFile]}: resizeOssPicFile(String)
	{GET [/dataResolver/deleteQuestionAndRelatedRecords]}: deleteQuestionAndRelatedRecords(String,String)
	{GET [/dataResolver/deleteWrongQuestionBook]}: deleteWrongQuestionBook(String,String)
	{GET [/dataResolver/transformCompletionQuestion]}: transformCompletionQuestion(Integer,Integer)
	{GET [/dataResolver/restoreExerciseBook]}: restoreExerciseBook(Integer)
	{GET [/dataResolver/replaceDepartmentId]}: replaceDepartmentId(Integer)
	{GET [/dataResolver/generateAnnualReport]}: generateAnnualReport(Integer,Integer,String,String)
	{GET [/dataResolver/insertCompanyUser]}: insertCompanyUser(Integer)
	{GET [/dataResolver/getUnCompleteQuestion]}: getUnCompleteQuestion(Integer)
	{GET [/dataResolver/deleteDepartments]}: deleteDepartments()
	{POST [/dataResolver/processQuestionAndAnswers], consumes [multipart/form-data]}: processQuestionAndAnswers(HttpServletRequest,HttpServletResponse)
	{GET [/dataResolver/deletedRandomRangeQuestionWithQuestionDeletedState]}: deletedRandomRangeQuestionWithQuestionDeletedState(Integer,Integer)
	{GET [/dataResolver/clearExaminationCache]}: reloadExaminationCache(Integer,Integer)
[20:43:03:459] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.DownloadProxyController:
	{POST [/download/proxy]}: proxyDownload(HttpServletRequest,JSONObject)
[20:43:03:468] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationController:
	{GET [/examination/examination]}: getExaminationById(int)
	{POST [/examination/create]}: createExamination(JSONObject)
	{POST [/examination/createExaminationWithContentCheck]}: createExaminationWithContentCheck(JSONObject)
	{POST [/examination/createIncludingExamineeSelect]}: createExaminationIncludingExamineeSelect(JSONObject)
	{POST [/examination/createFixedExamination]}: createFixedExamination(JSONObject)
	{POST [/examination/createRandomExaminationIncludingExamineeSelect]}: createRandomExaminationIncludingExamineeSelect(JSONObject)
	{POST [/examination/createRandomExamination]}: createRandomExamination(JSONObject)
	{GET [/examination/delete]}: deleteExamination(String)
	{GET [/examination/examinationWrapperListOfCreater]}: getExaminationWrapperListOfCreater(int,int,int,int)
	{GET [/examination/examinationInViewListOfCreater]}: getExaminationInViewListOfCreater(Integer,Integer,Integer,Integer)
	{GET [/examination/getExaminationInViewListOfCreater]}: getExaminationInViewList(Integer,Integer,Integer,int)
	{GET [/examination/getExaminationInViewListAndTotalNumOfCreater]}: getExaminationInViewListAndTotalNumOfCreater(int,int,int,int)
	{GET [/examination/getRecommendExaminations]}: getRecommendExaminations(Integer,Integer,Integer,Integer)
	{GET [/examination/getRecommendExaminationsByProduct]}: getRecommendExaminationsByProduct(Integer,String)
	{POST [/examination/getExaminationInViewListOfCreaterInCompanyByTags]}: getExaminationInViewListOfCreaterInCompanyByTags(JSONObject)
	{POST [/examination/getExaminationInViewListOfCompanyByTags]}: getExaminationInViewListOfCompanyByTags(JSONObject)
	{GET [/examination/ongoingExaminationList]}: getOngoingExaminationList()
	{GET [/examination/getExaminationAndPaperById]}: getExaminationAndPaperById(int)
	{POST [/examination/getExaminationAndPaperByColumns]}: getExaminationAndPaperByColumns(JSONObject)
	{POST [/examination/getExaminationAndPaperByColumnsAfterEncode]}: getExaminationAndPaperByColumnsAfterEncode(JSONObject)
	{POST [/examination/getExaminationDetail]}: getExaminationDetail(JSONObject)
	{GET [/examination/examinationByCode]}: getExaminationByCode(String)
	{GET [/examination/getExaminationListByPaperId]}: getExaminationListByPaperId(Integer)
	{GET [/examination/getExaminationListByKeyword]}: getExaminationListByKeyword(String)
	{GET [/examination/getExaminationListOfCreater]}: getExaminationListOfCreater(Integer,Integer,Integer,Integer)
	{GET [/examination/idByCode]}: getExaminationIdByCode(String)
	{POST [/examination/edit]}: editExamination(JSONObject)
	{GET [/examination/examinationWrapper]}: getExaminationWrapperById(Integer)
	{GET [/examination/getExaminationWrapperSecure]}: getExaminationWrapperSecure(Integer,Integer)
	{GET [/examination/getExaminationWrapperStructure]}: getExaminationWrapperStructure(Integer,Integer)
	{POST [/examination/updateExaminationWrapperStructure]}: updateExaminationWrapperStructure(JSONObject)
	{GET [/examination/getExaminationAndPaperQuestionList]}: getExaminationAndPaperQuestionList(Integer)
	{GET [/examination/getExaminationAndPaperQuestionListAfterEncoded]}: getExaminationAndPaperQuestionListAfterEncoded(Integer)
	{GET [/examination/examinationWrapperWithRedLock]}: examinationWrapperWithRedLock(Integer,String)
	{GET [/examination/suspend]}: suspendExamination(Integer)
	{GET [/examination/resume]}: resumeExamination(Integer)
	{GET [/examination/changeExamineeDisplay]}: changeExamineeDisplay(Integer,Boolean)
	{GET [/examination/rename]}: renameExamination(int,int,String)
	{GET [/examination/renameExamCode]}: renameExamCode(Integer,String)
	{GET [/examination/modifyCode]}: modifyCode(Integer,String)
	{GET [/examination/configAdvancedOptions]}: configAdvancedOptions(String)
	{POST [/examination/saveAdvancedOptions]}: saveAdvancedOptions(JSONObject)
	{GET [/examination/checkIfExceedExaminationTimesLimit]}: checkIfExceedExaminationTimesLimit(Integer,Integer)
	{GET [/examination/copy]}: copyExamination(Integer,Integer,Boolean)
	{GET [/examination/transmit]}: transmitExamination(Integer,Integer,Integer)
	{GET [/examination/getLeftTime]}: getLeftFromBeginTime(Integer)
	{POST [/examination/sendQiyeweixinQkkNotice]}: sendQiyeweixinQkkNotice(JSONObject)
	{POST [/examination/update]}: update(Examination)
	{GET [/examination/reset]}: reset(Integer)
[20:43:03:472] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationExamineeController:
	{GET [/examinationExaminee/getExaminationExaminee]}: getExaminationExaminee(Integer)
	{POST [/examinationExaminee/updateExaminationExaminee]}: updateExaminationExaminee(JSONObject)
[20:43:03:473] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationExamineeSnapshotController:
	{GET [/examinationExamineeSnapshot/insertSnapshot]}: insertSnapshot(Integer,Integer,String,String)
	{GET [/examinationExamineeSnapshot/getUserSnapshotListOfExamination]}: getUserSnapshotListOfExamination(Integer,String,Integer,Integer)
	{GET [/examinationExamineeSnapshot/getUserSnapshotList]}: getUserSnapshotList(Integer,String,Integer,Integer)
	{GET [/examinationExamineeSnapshot/exportSnapshotListOfExamination]}: exportSnapshotListOfExamination(Integer)
	{GET [/examinationExamineeSnapshot/delete]}: delete(String)
[20:43:03:480] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationFavoriteController:
	{GET [/examinationFavorite/add]}: addFavorite(int,int)
	{GET [/examinationFavorite/cancel]}: cancelFavorite(int,int)
	{GET [/examinationFavorite/ifFavorite]}: ifFavorite(int,int)
	{GET [/examinationFavorite/getFavoriteList]}: getFavoriteList(int,int,int)
	{GET [/examinationFavorite/getFavoriteListAndNum]}: getFavoriteListAndNum(int,int,int)
[20:43:03:480] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationGroupExaminationController:
	{ [/examinationGroupExamination/list]}: getExaminationGroupExaminationServiceListById(int)
[20:43:03:490] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationInstanceController:
	{POST [/examinationInstance/getExaminationInstanceList]}: getExaminationInstanceList(JSONObject)
	{[GET, POST] [/examinationInstance/exportRankListOfExaminationInCompany]}: exportRankListOfExaminationInCompany(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/examinationInstance/exportNoRankListOfExaminationInCompany]}: exportNoRankListOfExaminationInCompany(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/examinationInstance/exportDetailRankListOfExamination]}: exportDetailRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{GET [/examinationInstance/getExaminationInstanceWrapperById]}: getExaminationInstanceWrapperById(Integer)
	{POST [/examinationInstance/createExaminationInstance]}: createExaminationInstance(JSONObject,HttpServletRequest)
	{POST [/examinationInstance/createExaminationInstanceWithContentCheck]}: createExaminationInstanceWithContentCheck(JSONObject,HttpServletRequest,HttpServletResponse)
	{GET [/examinationInstance/examinationInstanceWrapperListOfExaminee]}: getExaminationInstanceWrapperListOfExaminee(int,int,int,int)
	{POST [/examinationInstance/getExaminationInstanceWrapperListOfExaminee]}: getExaminationInstanceWrapperListOfExaminee(JSONObject)
	{ [/examinationInstance/getExaminationInstanceInfoById]}: getExaminationInstanceInfoById(Integer)
	{POST [/examinationInstance/getRecentExaminationInstanceListWithAverage]}: getRecentExaminationInstanceListWithAverage(JSONObject)
	{GET [/examinationInstance/getExaminationInstanceListWithUserInfoByExaminationId]}: getExaminationInstanceListWithUserInfoByExaminationId(int,int,int,int)
	{GET [/examinationInstance/examinationInstanceWrapperListOfExamination]}: getExaminationInstanceWrapperListOfExamination(String,int,Integer,Integer)
	{GET [/examinationInstance/rankOfExaminationInstance]}: getRankOfExaminationInstance(int,int,int)
	{GET [/examinationInstance/rankListOfExamination]}: getRankListOfExamination(Integer,Integer,Integer,Integer,Integer)
	{GET [/examinationInstance/getTopTenRankListAndTotalExamineeNumOfExamination]}: getTopTenRankListAndTotalExamineeNumOfExamination(int)
	{GET [/examinationInstance/getLatestTenListAndTotalExamineeNumOfExamination]}: getLatestTenListAndTotalExamineeNumOfExamination(Integer)
	{GET [/examinationInstance/rankListOfExaminationByCode]}: getRankListOfExaminationByCode(String)
	{GET [/examinationInstance/getTotalTimes]}: getTotalTimes(Integer,Integer)
	{GET [/examinationInstance/timesOfExaminationOfExaminee]}: getTimesOfExaminationOfExaminee(int,int,String,String)
	{POST [/examinationInstance/batchDelete]}: batchDeleteByExaminee(JSONArray)
	{POST [/examinationInstance/batchDeleteByAdmin]}: batchDeleteByAdmin(JSONArray)
	{GET [/examinationInstance/examinationInstanceStageSummary]}: getExaminationInstanceStageSummary(int,String,String)
	{POST [/examinationInstance/getExaminationInstanceStageSummaryListByTags]}: getExaminationInstanceStageSummaryList(JSONObject)
	{GET [/examinationInstance/examinationInstanceStageSummaryList]}: getExaminationInstanceStageSummaryList(String,String,String,int,int,int)
	{GET [/examinationInstance/examinationInstanceStageSummaryListExcludingExample]}: getExaminationInstanceStageSummaryListExcludingExample(String,String,String,int,int,int)
	{GET [/examinationInstance/getExaminationInstanceStageSummaryListExcludingCreater]}: getExaminationInstanceStageSummaryListExcludingCreater(String,String,String,int,int,int)
	{GET [/examinationInstance/examinationInstanceStageSummaryDetail]}: getExaminationInstanceStageSummaryDetail(int,String,String,Integer,Integer,String)
	{POST [/examinationInstance/getExaminationStageSummaryDetail]}: getExaminationStageSummaryDetail(JSONObject)
	{GET [/examinationInstance/individualScoreListOfExamination]}: getIndividualScoreListOfExamination(int,int,String,String)
	{GET [/examinationInstance/individualScoreList]}: getIndividualScoreList(int,String,String,int)
	{GET [/examinationInstance/getIndividualRankInfo]}: getIndividualRankInfo(Integer,Integer,Integer)
	{GET [/examinationInstance/getIndividualRankInfoWithDuplicateRemoval]}: getIndividualRankInfoWithDuplicateRemoval(Integer,Integer,Integer)
	{GET [/examinationInstance/getExaminationInstanceWrapperAndProcessById]}: getExaminationInstanceWrapperAndProcessById(Integer)
	{POST [/examinationInstance/getInstanceWrapperAndProcessList]}: getInstanceWrapperAndProcessList(JSONObject)
	{GET [/examinationInstance/notApprovedExaminationInstanceList]}: getNotApprovedExaminationInstanceList(Integer,Integer,Integer,Integer)
	{GET [/examinationInstance/getNotApprovedExaminationInstanceListAndLength]}: getNotApprovedExaminationInstanceListAndLength(Integer,Integer,Integer,Integer)
	{POST [/examinationInstance/approveInstance]}: approveInstance(JSONObject)
	{[GET, POST] [/examinationInstance/exportNoRankListOfExamination]}: exportNoRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/examinationInstance/exportRankListOfExamination]}: exportRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{POST [/examinationInstance/getCountResult]}: getCountResult(JSONObject)
	{[GET, POST] [/examinationInstance/exportDetailRankListOfExaminationInCompany]}: exportDetailRankListOfExaminationInCompany(HttpServletRequest,HttpServletResponse)
	{ [/examinationInstance/exportInstanceOfExaminee]}: exportInstanceOfExaminee(HttpServletRequest,HttpServletResponse)
	{GET [/examinationInstance/exportAbsentList]}: exportAbsentList(HttpServletRequest,HttpServletResponse)
	{GET [/examinationInstance/getUserListOfExamination]}: getUserListOfExamination(Integer)
	{GET [/examinationInstance/getAbsentList]}: getAbsentList(Integer)
	{GET [/examinationInstance/getExaminationAnalysisData]}: getAnalysisData(Integer)
	{GET [/examinationInstance/getDistributionOfScore]}: getDistributionOfScore(Integer)
	{GET [/examinationInstance/getDistributionOfDuration]}: getDistributionOfDuration(Integer)
	{POST [/examinationInstance/getExamineeList]}: getExamineeList(JSONObject)
	{GET [/examinationInstance/delete]}: delete(int,String)
	{POST [/examinationInstance/create]}: create(JSONObject,HttpServletRequest,HttpServletResponse)
[20:43:03:505] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationInstanceEventController:
	{POST [/examinationInstanceEvent/getList]}: getList(JSONObject)
	{POST [/examinationInstanceEvent/getSummaryInfo]}: getSummaryInfo(JSONObject)
	{POST [/examinationInstanceEvent/getExamBehaviorList]}: getExamBehaviorList(JSONObject)
	{POST [/examinationInstanceEvent/insert]}: insert(ExaminationInstanceEvent)
[20:43:03:508] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationInstanceProcessController:
	{GET [/examinationInstanceProcess/examinationInstanceProcessWrapperListAndNum]}: examinationInstanceProcessWrapperListAndNum(Integer,Integer,Integer,Integer)
	{GET [/examinationInstanceProcess/wrongQuestionList]}: getWrongQuestionsOfExaminee(int,int,int)
	{GET [/examinationInstanceProcess/rightWrongNumOfExamination]}: getQuestionRightWrongNumOfExamination(int)
	{GET [/examinationInstanceProcess/rightWrongNumOfQuestion]}: getQuestionRightWrongNum(int)
	{GET [/examinationInstanceProcess/getDistributionGroupByAnswerContent]}: getDistributionGroupByAnswerContent(Integer,Integer)
	{GET [/examinationInstanceProcess/getAnalysisOfExamination]}: getAnalysisOfExamination(Integer,Integer,Integer,String)
	{POST [/examinationInstanceProcess/changeQuestionResult]}: changeQuestionResult(JSONObject)
	{GET [/examinationInstanceProcess/exportQuestionAnalysis]}: exportQuestionAnalysis(Integer,HttpServletResponse)
	{POST [/examinationInstanceProcess/performAiScoring]}: performAiScoring(JSONObject)
	{GET [/examinationInstanceProcess/examinationInstanceProcessWrapperList]}: getExaminationInstanceProcessWrapperList(int,Integer,Integer,Integer)
[20:43:03:513] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationNameListController:
	{GET [/examinationNameList/getList]}: getList(Integer,Integer)
	{POST [/examinationNameList/addExaminationNameList]}: addExaminationNameList(JSONObject)
	{POST [/examinationNameList/upInsert]}: upInsert(JSONObject)
[20:43:03:517] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationStartUpController:
	{GET [/examinationStartUp/getCountGroupByUser]}: getCountGroupByUser(Integer,Integer,Integer)
	{GET [/examinationStartUp/add]}: add(Integer,Integer)
	{GET [/examinationStartUp/delete]}: delete(Integer,Integer)
	{GET [/examinationStartUp/getCount]}: getCount(Integer,Integer)
[20:43:03:520] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationTagsController:
	{POST [/examinationTags/createrExamTags]}: createExamTags(JSONObject)
	{GET [/examinationTags/getAllExamTagsOfCreaterIdInCompany]}: getAllExamTagsOfCreaterIdInCompany(Integer,Integer)
	{GET [/examinationTags/getAllExamTagsExcludingCreaterIdInCompany]}: getAllExamTagsExcludingCreaterIdInCompany(int,int)
	{ [/examinationTags/getAllExamTagsOfCompanyId]}: getAllExamTagsOfCompanyId(int,Boolean)
	{ [/examinationTags/getAllExamTagsOfUserInCompany]}: getAllExamTagsOfUserInCompany(Integer,Integer)
	{ [/examinationTags/getTagsOfExam]}: getTagsOfExam(int)
[20:43:03:521] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationWxGidController:
	{POST [/examinationWxGid/addIfNoExist]}: addEntity(ExaminationWxGid)
	{GET [/examinationWxGid/getList]}: getList(Integer)
	{GET [/examinationWxGid/delete]}: delete(Integer,String)
	{GET [/examinationWxGid/getEntity]}: getEntity(Integer,String)
[20:43:03:523] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookController:
	{POST [/exerciseBook/create]}: createExerciseBook(JSONObject)
	{POST [/exerciseBook/createExerciseBookByQuestionTypeAndCategories]}: createExerciseBookByQuestionTypeAndCategories(JSONObject)
	{GET [/exerciseBook/modifyExerciseBook]}: modifyExerciseBook(String,String)
	{GET [/exerciseBook/delete]}: deleteExerciseBook(int)
	{GET [/exerciseBook/deleteExerciseBookAndRelation]}: deleteExerciseBookAndRelation(Integer)
	{GET [/exerciseBook/getRecommendedKeyWords]}: getRecommendedKeyWords()
	{ [/exerciseBook/exerciseBookListOfCreaterIncludingFavorite]}: getExerciseBookListOfCreaterIncludingFavorite(int,int,int,int,int)
	{ [/exerciseBook/exerciseBook]}: getExerciseBookById(int)
	{GET [/exerciseBook/getExampleQuestions]}: getExampleQuestions(Integer)
	{ [/exerciseBook/exerciseBookBeginInfo]}: getExerciseBookBeginInfoById(Integer,Integer)
	{GET [/exerciseBook/getExerciseBookBeginInfoById]}: getKsiteExerciseBookBeginInfoById(Integer,Integer,Integer)
	{GET [/exerciseBook/getExerciseBookIndexPageInfo]}: getExerciseBookIndexPageInfo(Integer)
	{GET [/exerciseBook/getPracticeSummaryInfo]}: getPracticeSummaryInfo(Integer)
	{ [/exerciseBook/exerciseBookWrapper]}: getExerciseBookWrapperById(String)
	{ [/exerciseBook/switchDisplayToExaminee]}: switchDisplayToExaminee(Integer,Boolean)
	{GET [/exerciseBook/advancedExerciseBookWrapper]}: getAdvancedExerciseBookWrapperById(int)
	{POST [/exerciseBook/doExercise]}: doExercise(JSONObject)
	{POST [/exerciseBook/doExerciseSecure]}: doExerciseSecure(JSONObject)
	{GET [/exerciseBook/rename]}: renameExerciseBook(String,int)
	{POST [/exerciseBook/savePracticeSummary]}: savePracticeSummary(JSONObject)
	{GET [/exerciseBook/getCatagoriesAndNum]}: getCatagoriesAndNum(Integer,Integer,String)
	{GET [/exerciseBook/getTagsGroupByDomain]}: getTagsGroupByDomain(Integer,Integer,String)
	{GET [/exerciseBook/getExerciseBookListByCatagory]}: getExerciseBookListByCatagory(Integer,Integer,String,String,String,Integer,Integer)
	{GET [/exerciseBook/getExerciseBookListAndTotalNum]}: getExerciseBookListAndTotalNum(Integer,Integer,String,String,String,Integer,Integer)
	{GET [/exerciseBook/getDailyPracticeResult]}: getDailyPracticeResult(Integer,Integer,Date,Date)
	{GET [/exerciseBook/getRecommendedList]}: getRecommendedList(Integer,Integer,Integer)
	{GET [/exerciseBook/getFavoriteRankList]}: getFavoriteRankList(String,Integer,Integer,Integer,Integer)
	{POST [/exerciseBook/batchUpdate]}: batchUpdate(JSONArray)
	{GET [/exerciseBook/exerciseBookListOfCreater]}: getExerciseBookListOfCreater(Integer,String,String,String,Integer,Integer,Integer)
	{POST [/exerciseBook/getExerciseBookListOfCreater]}: getExerciseBookListOfCreater(JSONObject)
	{ [/exerciseBook/getExerciseBookList]}: getExerciseBookList(Integer,String,String,String,Integer,Integer,Integer)
	{POST [/exerciseBook/sendQiyeweixinQkkNotice]}: sendQiyeweixinQkkNotice(JSONObject)
	{POST [/exerciseBook/update]}: update(ExerciseBook)
[20:43:03:528] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookExamineeController:
	{GET [/exerciseBookExaminee/getExerciseBookExaminee]}: getExerciseBookExaminee(Integer)
	{POST [/exerciseBookExaminee/updateExerciseBookExaminee]}: updateExerciseBookExaminee(JSONObject)
[20:43:03:530] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookFavorateController:
	{ [/exerciseBookFavorate/addFavorite]}: addInFavoriteList(int,int)
	{ [/exerciseBookFavorate/addBatchFavorite]}: addBatchFavorite(String,int)
	{ [/exerciseBookFavorate/cancelFavorite]}: deleteFavoriteOfUser(Integer,Integer)
	{GET [/exerciseBookFavorate/myFavoriteExerciseBookPage]}: getMyFavoriteExerciseBookPageInfo(Integer,Integer)
	{GET [/exerciseBookFavorate/getMyFavoriteInfo]}: getMyFavoriteInfo(Integer,Integer)
	{ [/exerciseBookFavorate/getSomeoneExerciseBookAndUserPracticeInfo]}: getSomeoneExerciseBookAndUserPracticeInfo(int,int)
	{ [/exerciseBookFavorate/getCompanyExerciseBookAndUserPracticeInfo]}: getCompanyExerciseBookAndUserPracticeInfo(int,int)
	{POST [/exerciseBookFavorate/getCompanyExamineePracticeInfo]}: getCompanyExamineePracticeInfo(JSONObject)
	{GET [/exerciseBookFavorate/getCompanyPracticeSummary]}: getCompanyPracticeSummary(Integer,Integer)
	{ [/exerciseBookFavorate/getEntity]}: getEntity(Integer,Integer)
[20:43:03:531] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookGradeController:
	{POST [/exerciseBookGrade/add]}: add(ExerciseBookGrade)
[20:43:03:531] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookPracticeProcessController:
	{GET [/exerciseBookPracticeProcess/getRankOfInterval]}: getRankOfInterval(Integer,Integer,Integer,String,String,Integer,Integer)
	{GET [/exerciseBookPracticeProcess/getSequenceNoInRankOfInterval]}: getSequenceNoInRankOfInterval(Integer,Integer,Integer,Integer,String,String)
	{GET [/exerciseBookPracticeProcess/getRankInfo]}: getRankInfo(Integer,Integer,Integer,Integer,String,String,Integer,Integer)
[20:43:03:532] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookPracticeSummaryController:
	{GET [/exerciseBookPracticeSummary/exportUserPracticeSummary]}: exportUserPracticeSummary(HttpServletRequest,HttpServletResponse)
	{GET [/exerciseBookPracticeSummary/getRankOfInterval]}: getRankOfInterval(Integer,Integer,Integer,String,String,Integer,Integer)
	{GET [/exerciseBookPracticeSummary/getSequenceNoInRankOfInterval]}: getSequenceNoInRankOfInterval(Integer,Integer,Integer,Integer,String,String)
	{GET [/exerciseBookPracticeSummary/getRankInfo]}: getRankInfo(Integer,Integer,Integer,Integer,String,String,Integer,Integer)
	{GET [/exerciseBookPracticeSummary/getUserPracticeSummary]}: getUserPracticeSummary(Integer,Integer,String,String)
	{POST [/exerciseBookPracticeSummary/getUserPracticeListWithSummary]}: getUserPracticeListWithSummary(JSONObject)
	{POST [/exerciseBookPracticeSummary/getDepartmentPracticeStatistics]}: getDepartmentPracticeStatistics(JSONObject)
[20:43:03:532] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookPraiseController:
	{ [/exerciseBookPraise/add]}: addPraise(int,int)
	{ [/exerciseBookPraise/cancel]}: cancelPraise(int,int)
[20:43:03:533] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookQuestionController:
	{GET [/exerciseBookQuestion/exerciseBookQuestionWrapperList]}: getExerciseBookQuestionsById(int,Integer,Integer)
	{GET [/exerciseBookQuestion/getExerciseBookQuestionWrapperListWithRedLock]}: getExerciseBookQuestionWrapperList(Integer,Integer,Integer)
	{GET [/exerciseBookQuestion/exerciseBookQuestionWrapperListAndTotalNum]}: getExerciseBookQuestionsAndTotalNumById(Integer,Integer,Integer)
	{GET [/exerciseBookQuestion/getDayDayExerciseQuestionList]}: getDayDayExerciseQuestionList(Integer,Integer,Integer,Boolean)
[20:43:03:533] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookSummaryController:
	{GET [/exerciseBookSummary/getSummary]}: getSummary(Integer)
[20:43:03:533] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookUserRecordController:
	{GET [/exerciseBookUserRecord/updateHavePractisedNum]}: updateHavePractisedNum(Integer,Integer)
	{GET [/exerciseBookUserRecord/exportUserPracticeSummary]}: exportUserPracticeSummary(Integer,HttpServletResponse)
	{ [/exerciseBookUserRecord/getExerciseProgress]}: getExerciseBookProgressOfUser(int,int)
[20:43:03:534] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.FileAccessPermissionController:
	{POST [/fileAccessPermission/generateUnlockAdQRCode]}: generateUnlockAdQRCode(JSONObject)
	{POST [/fileAccessPermission/addFileAccessPermission]}: addFileAccessPermission(JSONObject)
	{POST [/fileAccessPermission/getFileAccessPermission]}: getFileAccessPermission(JSONObject)
[20:43:03:535] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.FileController:
	{ [/file/downloadFile]}: downLoad(String,HttpServletResponse,boolean)
	{[GET, POST] [/file/uploadFile]}: uploadFile(HttpServletRequest)
	{[GET, POST] [/file/uploadFileWithCheck]}: uploadFileWithCheck(HttpServletRequest)
	{GET [/file/downloadNetFile]}: downloadNetFile(HttpServletRequest,HttpServletResponse)
	{POST [/file/uploadOssFile]}: uploadOssFile(HttpServletRequest,HttpServletResponse)
[20:43:03:537] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.FreePracticeOfUserController:
	{POST [/freePracticeOfUser/getFreePractice]}: getFreePractice(JSONObject)
	{GET [/freePracticeOfUser/getQuestionListOfFreePractice]}: getQuestionListOfFreePractice(Integer,Integer)
	{GET [/freePracticeOfUser/getSummaryGroupByQuestionType]}: getSummaryGroupByQuestionType(Integer,Integer)
	{GET [/freePracticeOfUser/delete]}: delete(Integer,Integer)
	{POST [/freePracticeOfUser/save]}: save(JSONObject)
[20:43:03:537] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.GameController:
	{GET [/game/getGameWrapper]}: getGameWrapper(Integer)
	{POST [/game/updateInsert]}: updateInsert(JSONObject)
[20:43:03:538] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.GameResultController:
	{GET [/gameResult/getRankInfo]}: getRankInfo(String,Integer,Integer)
[20:43:03:538] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.HomepageUserFollowedController:
	{GET [/homepageUserFollowed/getUserFollowedInfo]}: getUserFollowedInfo(Integer,Integer)
	{GET [/homepageUserFollowed/getUserFollowedList]}: getUserFollowedList(Integer)
	{POST [/homepageUserFollowed/update]}: update(HomepageUserFollowedWrapper)
	{GET [/homepageUserFollowed/delete]}: delete(Integer,Integer)
	{POST [/homepageUserFollowed/save]}: save(HomepageUserFollowedWrapper)
[20:43:03:541] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.InviteRelationController:
	{GET [/inviteRelation/saveInviteRelation]}: saveInviteRelation(int,int,int)
	{GET [/inviteRelation/getRecentInviteRelationAnnouncement]}: getRecentInviteRelationAnnouncement(int)
	{GET [/inviteRelation/getExaminationInstanceListOfInvitee]}: getExaminationInstanceListOfInvitee(int,int,int,int,int)
	{GET [/inviteRelation/getEventResultOfInviter]}: getEventResultOfInviter(int,int,int)
	{GET [/inviteRelation/getResultOfEvent]}: getResultOfEvent(int,int,int,int,int)
	{[GET, POST] [/inviteRelation/exportRankListOfEvent]}: exportRankListOfExamination(HttpServletRequest,HttpServletResponse)
[20:43:03:542] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ManualServiceController:
	{POST [/manualService/addManualImportRecord]}: addManualImportRecord(JSONObject)
	{POST [/manualService/getManualImportRecords]}: getManualImportRecords(JSONObject)
	{POST [/manualService/updateManualImportRecord]}: updateManualImportRecord(ManualServiceRecord)
[20:43:03:543] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MessageController:
	{GET [/message/sendMessage]}: sendMessage(String)
[20:43:03:545] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MessagePushSubscriptionController:
	{POST [/messagePushSubscription/getList]}: getList(JSONObject)
	{POST [/messagePushSubscription/saveIfNotExisted]}: saveIfNotExisted(MessagePushSubscription)
	{GET [/messagePushSubscription/deleteEntity]}: deleteEntity(String,Integer,Integer)
	{POST [/messagePushSubscription/changeBatch]}: changeBatch(JSONObject)
	{GET [/messagePushSubscription/getEntity]}: getEntity(String,Integer,Integer)
[20:43:03:546] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MessageScheduleController:
	{GET [/messageSchedule/sendMessage]}: sendMessage(String)
	{GET [/messageSchedule/getList]}: getList(String,Integer,Integer)
	{POST [/messageSchedule/deleSave]}: deleSave(JSONObject)
	{POST [/messageSchedule/update]}: updateIgnoreNull(MessageSchedule)
[20:43:03:547] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MonitorViewController:
	{GET [/view/{sessionId}]}: viewMonitor(String,Model)
[20:43:03:553] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MultipleMarkingController:
	{GET [/multipleMarking/acceptMultipleMarking]}: acceptMultipleMarking(Integer,Integer)
	{GET [/multipleMarking/getMultipleMarkingExamintionListOfUser]}: getMultipleMarkingExamintionListOfUser(Integer)
	{GET [/multipleMarking/hasMultipleMarkingExaminationInstanceByExaminationInstanceId]}: hasMultipleMarkingExaminationInstanceByExaminationInstanceId(Integer)
	{GET [/multipleMarking/getWorkStateOfMarking]}: getWorkStateOfMarking(Integer,Integer)
	{GET [/multipleMarking/finishMarking]}: finishMarking(Integer)
[20:43:03:563] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.NameListController:
	{GET [/nameList/getList]}: getList(Integer)
	{GET [/nameList/getEntityWrapper]}: getEntityWrapper(Integer)
	{POST [/nameList/update]}: update(JSONObject)
	{GET [/nameList/delete]}: delete(Integer)
	{POST [/nameList/save]}: save(JSONObject)
	{GET [/nameList/getEntity]}: getEntity(Integer)
[20:43:03:566] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.NanXiangController:
	{GET [/nanxiang/check]}: check(String)
	{POST [/nanxiang/save]}: save(NanxiangRegister)
	{POST [/nanxiang/validate]}: validate(NanxiangRegister)
	{POST [/nanxiang/getEntity]}: getEntity(NanxiangRegister)
[20:43:03:569] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.OrderController:
	{GET [/order/getList]}: getList(String,String,Integer,Integer,Integer)
	{POST [/order/createManualOrder]}: createManualOrder(JSONObject)
	{ [/order/create]}: createOrder(JSONObject)
	{ [/order/createCountOrder]}: createCountOrder(JSONObject)
	{POST [/order/createCountOrderByWeChatNative]}: createOrderByWeChatNative(JSONObject)
	{POST [/order/createCountOrderByJSAPI]}: createCountOrderByJSAPI(JSONObject)
	{POST [/order/orderNotify]}: orderNotify(JSONObject)
	{ [/order/getUnconsumedValueOfAccount]}: getUnconsumedValueOfAccount(String,Integer)
	{GET [/order/getOrderById]}: getOrderById(Integer)
[20:43:03:571] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperController:
	{POST [/paper/batchUpdate]}: batchUpdate(JSONArray)
	{GET [/paper/getPaperWrapperWithRedLock]}: getPaperWrapperWithRedLock(Integer)
	{ [/paper/modifyPaper]}: modifyPaper(String,String)
	{POST [/paper/update]}: updatePaper(Paper)
	{ [/paper/paperListOfCreater]}: getPaperListOfCreater(String,int)
	{GET [/paper/getPaperWithFavoriteInfoAndOrderRelationInfo]}: getPaperWithFavoriteInfoAndOrderRelationInfo(Integer,Integer,Integer)
	{ [/paper/paperWrapper]}: getPaperWrapperById(String)
	{ [/paper/advancedPaperWrapper]}: getAdvancedPaperWrapperById(int)
	{ [/paper/paperListToAdmin]}: getPaperListToAdmin(String,String,int,int)
	{GET [/paper/getCategoriesAndNum]}: getCategoriesAndNum(Integer,Integer,String)
	{GET [/paper/getPaperList]}: getPaperList(Integer,Integer,String,String,String,Integer,Integer)
	{GET [/paper/getPaperListAndTotalNum]}: getPaperListAndTotalNum(Integer,Integer,String,String,String,Integer,Integer)
	{GET [/paper/getPaperById]}: getPaperById(Integer)
	{ [/paper/create]}: create(JSONObject)
[20:43:03:572] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperFavoriteController:
	{ [/paperFavorite/add]}: addFavorite(int,int)
	{ [/paperFavorite/cancel]}: cancelFavorite(int,int)
	{ [/paperFavorite/ifFavorite]}: ifFavorite(int,int)
	{ [/paperFavorite/getFavoriteList]}: getFavoriteList(int,int,int)
[20:43:03:572] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperPraiseController:
	{ [/paperPraise/add]}: addPraise(Integer,Integer)
	{ [/paperPraise/cancel]}: cancelPraise(Integer,Integer)
[20:43:03:572] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperQuestionController:
	{ [/paperQuestion/update]}: updateCompletion(BigDecimal,BigDecimal,int,BigDecimal,String,int)
	{ [/paperQuestion/updateSelection]}: updateSelection(BigDecimal,BigDecimal,int,BigDecimal,String,int)
	{GET [/paperQuestion/getQuestionWrapperListAndNum]}: getQuestionWrapperListAndNum(Integer,Integer,Integer)
[20:43:03:573] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperSummaryController:
	{GET [/paperSummary/getSummary]}: getSummary(Integer)
[20:43:03:574] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PassRaceGameController:
	{GET [/passRaceGame/getGameWrapper]}: getGameWrapper(Integer)
	{GET [/passRaceGame/getListAndTotalNum]}: getListAndTotalNum(Integer,Integer,Integer)
	{POST [/passRaceGame/update]}: update(JSONObject)
	{GET [/passRaceGame/delete]}: delete(Integer)
	{POST [/passRaceGame/save]}: save(JSONObject)
[20:43:03:575] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PassRaceResultController:
	{POST [/passRaceResult/save]}: save(JSONObject)
	{GET [/passRaceResult/getGameWrapper]}: getGameWrapper(Integer)
	{GET [/passRaceResult/getListAndTotalNum]}: getListAndTotalNum(Integer,Integer,Integer)
	{POST [/passRaceResult/update]}: update(JSONObject)
	{GET [/passRaceResult/delete]}: delete(Integer)
[20:43:03:576] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PassRaceStageController:
	{GET [/passRaceStage/getStageWrapper]}: getStageWrapper(Integer,Integer)
	{GET [/passRaceStage/getMyPlayedStageList]}: getMyPlayedStageList(Integer,Integer,Boolean)
[20:43:03:577] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PdfFileController:
	{ [/pdfFile/temporaryExaminationReport]}: createFreeExaminationReport(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/pdfFile/examinationReport]}: createExaminationPdfReport(HttpServletRequest,HttpServletResponse)
	{GET [/pdfFile/generatePdfByUser]}: generatePdfByUser(Integer,Integer)
[20:43:03:581] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PermissionAuthorizationController:
	{GET [/permissionAuthorization/getList]}: getList(Integer,Integer)
	{POST [/permissionAuthorization/changeBatch]}: changeBatch(JSONObject)
	{POST [/permissionAuthorization/getAuthorizedDepartmentList]}: getAuthorizedDepartmentList(JSONObject)
	{POST [/permissionAuthorization/getAuthorizedAdminList]}: getAuthorizedAdminList(JSONObject)
	{POST [/permissionAuthorization/getAuthorizedCompanyInfoWith2Grade]}: getAuthorizedCompanyInfoWithRecursionDepartment(JSONObject)
	{GET [/permissionAuthorization/getAuthorizedCompanyPracticeSummary]}: getAuthorizedCompanyPracticeSummary(Integer,Integer)
	{POST [/permissionAuthorization/getAuthorizedCompanyExamineePracticeInfo]}: getAuthorizedCompanyExamineePracticeInfo(JSONObject)
	{POST [/permissionAuthorization/save]}: save(List)
	{GET [/permissionAuthorization/getEntity]}: getEntity(Integer,Integer,Integer)
[20:43:03:582] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PermissionController:
	{GET [/permission/getList]}: getList()
[20:43:03:582] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PointConsumerationController:
	{POST [/pointConsumeration/getUserConsumeList]}: getUserConsumeList(JSONObject)
	{POST [/pointConsumeration/save]}: save(PointConsumeration)
[20:43:03:583] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PointDetailController:
	{GET [/pointDetail/exportPointRankOfCompany]}: exportPointRankOfCompany(HttpServletRequest,HttpServletResponse)
	{GET [/pointDetail/getRankInfo]}: getRankInfo(Integer,Integer,String,String,String,Integer,Integer)
	{POST [/pointDetail/getCompanyRankInfo]}: getCompanyRankInfo(JSONObject)
[20:43:03:583] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PointRuleController:
	{POST [/pointRule/upInsert]}: upInsert(PointRule)
	{GET [/pointRule/getEntity]}: getEntity(Integer)
[20:43:03:584] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PointUserController:
	{GET [/pointUser/getUserPointInfo]}: getUserPointInfo(Integer,Integer)
	{GET [/pointUser/clear]}: clear(Integer,Integer,Integer)
[20:43:03:584] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PracticePushController:
	{GET [/practicePush/list]}: getList(Integer,Integer,String,Integer,Integer,Integer)
	{GET [/practicePush/toggleStatus]}: toggleStatus(Integer,Integer,Integer)
	{GET [/practicePush/detail]}: getDetail(Integer,Integer)
	{GET [/practicePush/checkAndExecutePushTasks]}: checkAndExecutePushTasks()
	{POST [/practicePush/update]}: update(JSONObject)
	{GET [/practicePush/delete]}: delete(Integer,Integer)
	{POST [/practicePush/create]}: create(JSONObject)
[20:43:03:584] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductController:
	{ [/product/getProductList]}: getProductList(String)
	{GET [/product/getListOfProduct]}: getListOfProduct(String)
[20:43:03:584] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductCountTransactionController:
	{POST [/productCountTransaction/getList]}: getList(JSONObject)
[20:43:03:585] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductIntroductionController:
	{ [/productIntroduction/list]}: getProductIntroductionWrapperList()
[20:43:03:585] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductOrderRelationController:
	{GET [/productOrderRelation/getProductOrderRelationWrapper]}: getProductOrderRelationWrapper(String,Integer)
	{GET [/productOrderRelation/checkIfOverdue]}: checkIfOverdue(String,Integer)
	{GET [/productOrderRelation/getProductOrderRelation]}: getProductOrderRelation(String,Integer)
	{POST [/productOrderRelation/updateProductOrderRelation]}: updateProductOrderRelation(ProductOrderRelation)
	{GET [/productOrderRelation/refresh]}: refresh(Integer)
[20:43:03:589] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductOrderRelationCountController:
	{GET [/productOrderRelationCount/getProductOrderRelationCount]}: getProductOrderRelationCount(String,Integer,String)
	{POST [/productOrderRelationCount/getProductOrderRelationCountList]}: getProductOrderRelationCountList(JSONObject)
	{GET [/productOrderRelationCount/tryToConsumeVADVIP]}: tryToConsumeVADVIP(Integer,Integer)
	{GET [/productOrderRelationCount/consumeVADVIP]}: consumeVADVIP(Integer,Integer)
	{GET [/productOrderRelationCount/consume]}: consume(String,Integer,String,Integer)
[20:43:03:590] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QrcodeScanEntryController:
	{GET [/qrcodeScan/checkIfFollowed]}: checkIfFollowed(HttpServletRequest)
[20:43:03:590] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionBankQuestionController:
	{POST [/questionBankQuestion/changeQuestionBank]}: changeQuestionBank(JSONObject)
	{GET [/questionBankQuestion/deleteQuestionBankQuestion]}: deleteQuestionBankQuestion(Integer)
[20:43:03:591] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionCompositionChildController:
	{POST [/compositionChild/getListByIds]}: getListByIds(JSONArray)
	{POST [/compositionChild/getPaperQuestionListMapByIds]}: getListByIds(JSONObject)
	{GET [/compositionChild/getList]}: getList(Integer)
	{POST [/compositionChild/updateChildQuestionEntity]}: updateChildQuestionEntity(QuestionCompositionChild)
[20:43:03:605] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionController:
	{GET [/question/getCategoryList]}: getCategoryList(Integer,String,Integer)
	{POST [/question/updateQuestion]}: updateQuestion(Question)
	{[GET, POST] [/question/modifyQuestion]}: modifyQuestion(HttpServletRequest)
	{GET [/question/deleteQuestion]}: deleteQuestion(String)
	{POST [/question/deleteQuestionByQuery]}: deleteQuestion(JSONObject)
	{GET [/question/deleteQuestionList]}: deleteQuestionList(String)
	{POST [/question/deleteBatchQuestion]}: deleteBatchQuestion(JSONObject)
	{GET [/question/deleteQuestionIncludingExample]}: deleteQuestionIncludingExample(int,int)
	{GET [/question/questionListOfCreaterByCategory]}: getQuestionListOfCreaterByCategory(String,String,int)
	{GET [/question/questionListOfCreaterByType]}: getQuestionListOfCreaterByType(String,String,int,int,int)
	{GET [/question/questionListOfCreaterByTypeIncludingExample]}: getQuestionListOfCreaterByTypeIncludingExample(String,String,int,int,int)
	{GET [/question/questionWrapperListOfCreaterByType]}: getQuestionWrapperListOfCreaterByType(String,String,String,String,int,int,int)
	{[GET, POST] [/question/questionWrapperListOfCreaterByTypeIncludingExample]}: getQuestionWrapperListOfCreaterByTypeIncludingExample(String,String,int,int,int)
	{GET [/question/questionListForPractice]}: getQuestionListOfCreaterForPractice(String,String,String,int,int,int)
	{GET [/question/question]}: getQuestionById(String)
	{GET [/question/questionWrapper]}: getQuestionWrapperById(String)
	{GET [/question/getQuestionWrapper]}: getQuestionWrapper(String)
	{POST [/question/importQuestionUnion], produces [application/json;charset=utf-8]}: importQuestionUnion(HttpServletRequest)
	{[GET, POST] [/question/importExcelQuestion], produces [application/json;charset=utf-8]}: importExcelQuestion(HttpServletRequest)
	{POST [/question/importWordQuestion], produces [application/json;charset=utf-8]}: importWordQuestion(HttpServletRequest)
	{GET [/question/modifyDefaultMark]}: modifyDefaultMarkOfQuestions(String,BigDecimal)
	{GET [/question/modifyCatagory]}: modifyCatagory(String,String)
	{POST [/question/modifyQuestionCatagory]}: modifyCatagory(JSONObject)
	{GET [/question/categoryAndTotalNum]}: getCategoryAndTotalNum(Integer,String,Integer)
	{GET [/question/questionTypeAndCategoryAndTotalNum]}: getQuestionTypeAndCategoryAndTotalNum(Integer,Integer,Integer)
	{POST [/question/getQuestionTypeAndCategoryAndTotalNum]}: getQuestionTypeAndCategoryAndTotalNum(JSONObject)
	{POST [/question/getQuestionTypeAndCategoryAndTotalNumWithPermission]}: getQuestionTypeAndCategoryAndTotalNumWithPermission(JSONObject)
	{GET [/question/getQuestionCatagoryAndTotalNum]}: getQuestionCatagoryAndTotalNum(Integer,int)
	{POST [/question/getQuestionCatagoryAndTotalNumWithPermission]}: getQuestionCatagoryAndTotalNumWithPermission(JSONObject)
	{GET [/question/machineChooseQuestion]}: getMachineChooseQuestion(int,String,int,int)
	{POST [/question/getQuestionsByGroupConditionAdvancedWithPermission]}: getQuestionsByGroupConditionAdvancedWithPermission(JSONObject)
	{GET [/question/getQuestionsByGroupCondition]}: getQuestionsAndLengthByGroupCondition(Integer,String,String,String,int,Integer,Integer)
	{GET [/question/getQuestionsList]}: getQuestionsList(Integer,String,String,String,int,Integer,Integer)
	{POST [/question/checkDuplicates]}: checkDuplicates(DuplicateCheckRequest)
	{POST [/question/getQuestionsByGroupConditionAdvanced]}: getQuestionsByGroupConditionAdvanced(JSONObject)
	{POST [/question/questionListOfCreater]}: getQuestionListOfCreater(JSONObject)
	{GET [/question/exportQuestions]}: exportQuestions(HttpServletRequest,HttpServletResponse)
	{POST [/question/saveBatch]}: saveBatch(JSONObject)
	{[GET, POST] [/question/uploadFile]}: uploadFile(HttpServletRequest)
	{[GET, POST] [/question/uploadFileWithCheck]}: uploadFileWithCheck(HttpServletRequest)
	{[GET, POST] [/question/createQuestion]}: create(HttpServletRequest)
[20:43:03:613] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionFavoriteController:
	{ [/questionFavorite/add]}: addFavorite(int,int)
	{ [/questionFavorite/cancel]}: cancelFavorite(int,int)
	{ [/questionFavorite/ifFavorite]}: ifFavorite(int,int)
[20:43:03:621] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionRandomController:
	{GET [/questionRandom/deleteQuestionList]}: deleteQuestionList(String)
	{POST [/questionRandom/createByPost]}: createByPost(JSONObject)
	{GET [/questionRandom/list]}: getQuestionRandomListByCreaterIdInCompany(Integer,int,int,int)
	{GET [/questionRandom/questionRandomExtend]}: getQuestionRandomExtend(int)
	{GET [/questionRandom/delete]}: delete(int)
	{GET [/questionRandom/create]}: create(String,String)
	{POST [/questionRandom/modify]}: modify(JSONObject)
[20:43:03:622] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionSummaryController:
	{POST [/questionSummary/getWrongQuestionList]}: getWrongQuestionList(JSONObject)
	{POST [/questionSummary/removeWrongQuestion]}: removeWrongQuestion(JSONObject)
[20:43:03:623] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.SMSNoticeController:
	{GET [/notice/sendValidCodeSMS]}: sendValidCodeSMS(String,String)
	{GET [/notice/sendQKKRegisterNotice]}: sendQKKRegisterNotice(String,String)
	{GET [/notice/sendQKKTestOverdueNotice]}: sendQKKTemplateNotice(Integer)
	{GET [/notice/sendQKKTimeingNotice]}: sendQKKTimeingNotice(String)
[20:43:03:624] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.SoftVersionController:
	{GET [/version/reloadVersion]}: reloadVersion(String)
	{GET [/version/setSystemRunningState]}: setSystemRunningState(String)
	{GET [/version/setAdProvider]}: setAdProvider(String)
	{GET [/version/getSystemSettings]}: getSystemSettings(String)
	{GET [/version/setProductSystemRunningState]}: setProductSystemRunningState(String,String)
	{GET [/version/getProductSystemSettings]}: getProductSystemSettings(String)
	{GET [/version/getVersion]}: getVersion(String)
[20:43:03:624] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.SysUserRoleController:
	{GET [/sysUserRole/getSysUserRoleListOfCompany]}: getSysUserRoleListOfCompany(Integer,Integer)
	{GET [/sysUserRole/unbindExamineeUserRole]}: unbindExamineeUserRole(Integer,Integer)
	{POST [/sysUserRole/unbindExamineeUserRoleWithPermission]}: unbindExamineeUserRoleWithPermission(JSONObject)
	{GET [/sysUserRole/unbindAdminUserRole]}: unbindAdminUserRole(Integer,Integer)
	{POST [/sysUserRole/unbindAdminUserRoleWithPermission]}: unbindAdminUserRoleWithPermission(JSONObject)
	{POST [/sysUserRole/getSysUserRoleList]}: getSysUserRoleList(JSONObject)
	{GET [/sysUserRole/getSysUserRoleListByUsernameAndPassword]}: getSysUserRoleListByPhoneAndPassword(String,String,String,HttpServletRequest)
	{GET [/sysUserRole/getNewSysUserRoleList]}: getNewSysUserRoleList(String,String,String,HttpServletRequest)
	{GET [/sysUserRole/getSysUserRoleListOfUserInCompany]}: getSysUserRoleListOfUserInCompany(Integer,Integer,String)
	{GET [/sysUserRole/getCompanyInfoByAdminUserId]}: getCompanyInfoByAdminUserId(Integer)
	{GET [/sysUserRole/transmitAdminToOther]}: transmitAdminToOther(Integer,Integer,Integer)
	{POST [/sysUserRole/batchAddChildAdmin]}: batchAddChildAdmin(JSONObject)
	{POST [/sysUserRole/addSysUserRole]}: addSysUserRole(JSONObject)
	{GET [/sysUserRole/getSysUserRoleListOfUser]}: getSysUserRoleListOfUser(Integer,String,Integer)
[20:43:03:625] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.SystemMessageController:
	{GET [/systemMessage/handleSystemMessage]}: handleSystemMessage()
	{GET [/systemMessage/changeDepartmentId]}: changeDepartmentId(Integer)
	{GET [/systemMessage/batchDelete]}: batchDelete(String,String,String)
	{POST [/systemMessage/insert]}: insert(SystemMessage)
[20:43:03:626] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.TaxIdentityInfoController:
	{ [/taxIdentity/createTaxIdentityInfo]}: createTaxIdentityInfo(String)
	{ [/taxIdentity/modifyTaxIdentityInfo]}: modifyTaxIdentityInfo(String)
	{ [/taxIdentity/taxIdentityInfo]}: getTaxIdentityInfoByUserId(String,String)
[20:43:03:627] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.TaxSheetApplicationController:
	{ [/taxSheetApplication/createTaxSheetApplication]}: createTaxSheetApplication(String)
	{ [/taxSheetApplication/taxSheetApplicationList]}: getTaxSheetApplicationListOfUser(String)
	{ [/taxSheetApplication/taxSheetApplication]}: getTaxSheetApplicationById(String)
[20:43:03:631] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.TemplateController:
	{GET [/template/getMyTemplates]}: getMyTemplates(int,String)
[20:43:03:633] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.TestController:
	{GET [/test/api]}: apiAccess()
	{GET [/test/getKeysByPrefix]}: getKeysByPrefix(String)
	{GET [/test/deleteByPrex]}: deleteByPrex(String)
[20:43:03:633] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UniversalOrderController:
	{GET [/univsersalOrder/getList]}: getList(String,String,Integer,Integer,Integer)
	{ [/univsersalOrder/create]}: createOrder(JSONObject)
	{POST [/univsersalOrder/createOrderByWeChatNative]}: createOrderByWeChatNative(JSONObject)
	{POST [/univsersalOrder/createCountOrderByJSAPI]}: createCountOrderByJSAPI(JSONObject)
	{POST [/univsersalOrder/orderNotify]}: orderNotify(JSONObject)
	{GET [/univsersalOrder/getOrderById]}: getOrderById(Integer)
[20:43:03:634] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UniversalProductOrderRelationController:
	{POST [/universalProductOrderRelation/getProductOrderRelation]}: getProductOrderRelation(JSONObject)
[20:43:03:635] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserCompanyController:
	{GET [/userCompany/getCompanyInfoByUserId]}: getCompanyInfoByUserId(int)
	{GET [/userCompany/deleteUserOfCompany]}: deleteUserOfCompany(int,int)
	{GET [/userCompany/getUserCompanyByPhone]}: getUserCompanyByPhone(String)
	{GET [/userCompany/getCompanyInfoByUserIdAndCompanyId]}: getUserCompanyInfoByUserIdAndCompanyId(int,int)
	{POST [/userCompany/getCompanyUserListByMap]}: getCompanyUserListByMap(JSONObject)
	{GET [/userCompany/getUserInfoWithCompanyInfo]}: getUserInfoWithCompanyInfo(Integer,Integer)
	{GET [/userCompany/getUserListByDepartmentId]}: getUserListByDepartmentId(Integer,Integer,Integer,Integer,Integer,Integer)
	{GET [/userCompany/getUserListOfDepartmentsInCludingSelf]}: getUserListOfDepartmentsInCludingSelf(String)
	{GET [/userCompany/getUserListOfDepartmentsExcludingSelf]}: getUserListOfDepartmentsExcludingSelf(String)
	{GET [/userCompany/getUserListByName]}: getUserListByName(String,Integer,Integer,Integer,Integer)
	{POST [/userCompany/getUserList]}: getUserListByName(JSONObject)
	{GET [/userCompany/ifRegisted]}: getIfRegisted(int,int)
	{GET [/userCompany/getUserListOfCompany]}: getUserListOfCompany(Integer,String,String,Integer,Integer)
	{GET [/userCompany/secureGetUserListOfCompany]}: secureGetUserListOfCompany(Integer,String,String,Integer,Integer,Integer,Integer,Integer,Integer)
	{GET [/userCompany/getGrantedUserListOfCompany]}: getGrantedUserListOfCompany(Integer,String,String,Integer,Integer,Integer,Integer,String,Integer,Integer,String,String)
	{POST [/userCompany/getCompanyUserList]}: getCompanyUserList(JSONObject)
	{GET [/userCompany/getExamineeStatisticsOfCompany]}: getExamineeStatisticsOfCompany(Integer)
	{GET [/userCompany/exportUserListOfCompany]}: exportUserListOfCompany(Integer,Integer,HttpServletResponse)
	{GET [/userCompany/checkIfExceedMemberNumLimit]}: checkIfExceedMemberNumLimit(Integer,Integer)
	{POST [/userCompany/approvedRegist]}: approvedRegist(UserCompany)
	{POST [/userCompany/importUser]}: importUser(HttpServletRequest)
	{POST [/userCompany/getUserInfoListByIds]}: getUserInfoListByIds(JSONObject)
	{GET [/userCompany/getUserListByQkkQiyeCorpid]}: getUserListByQkkQiyeCorpid(String,String)
	{GET [/userCompany/deleteExamineeByDepartmentName]}: deleteExamineeByDepartmentName(Integer,String)
	{POST [/userCompany/deleteExamineeByDepartmentNameWithPermission]}: deleteExamineeByDepartmentNameWithPermission(JSONObject)
	{POST [/userCompany/updateUserInfoAndCompanyInfo]}: saveUserInfoAndCompanyInfo(JSONObject)
	{GET [/userCompany/saveUserInfoAndCompanyInfo]}: saveUserInfoAndCompanyInfo(int,int,int,String,String)
	{POST [/userCompany/bindCompany]}: bindCompany(JSONObject)
	{POST [/userCompany/deleteUserOfCompanyWithPermission]}: deleteUserOfCompanyWithPermission(JSONObject)
	{POST [/userCompany/deleteBatch]}: deleteBatchWithPermission(JSONObject)
[20:43:03:642] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserController:
	{GET [/user/getUserInfoByOpenId]}: getUserByEteaOpenId(String,String)
	{GET [/user/getUserInfoById]}: getUserInfoById(int)
	{GET [/user/getUserInfoListByNickName]}: getUserInfoListByNickName(String)
	{POST [/user/supplyPhoneNo]}: supplyPhoneNo(JSONObject)
	{GET [/user/loginByValidationCode]}: loginByValidationCode(String,String,HttpServletRequest)
	{GET [/user/validateSMSPhoneIfMatched]}: validateSMSPhoneIfMatched(String,String)
	{GET [/user/oneKeyLogin]}: oneKeyLogin(String,HttpServletRequest)
	{GET [/user/login]}: loginByPhoneAndPassword(String,String,HttpServletRequest)
	{GET [/user/loginById]}: loginById(Integer)
	{POST [/user/getUserInfoByUserIdAndCompanyId]}: getUserInfoByUserIdAndCompanyId(JSONObject,HttpServletRequest)
	{POST [/user/loginWithAutoRegist]}: loginWithAutoRegist(JSONObject,HttpServletRequest)
	{GET [/user/changePassword]}: changePassword(Integer,String,String)
	{GET [/user/updateUserInfo]}: updateUserInfo(String)
	{GET [/user/getUserInfoWithCompanyInfo]}: getUserInfoWithCompanyInfo(Integer,Integer)
	{POST [/user/getUserInfoListByIds]}: getUserInfoListByIds(List)
	{GET [/user/getUserByPhone]}: getUserByPhone(String)
	{GET [/user/getUserListByQkkQiyeCorpid]}: getUserListByQkkQiyeCorpid(String,String)
	{POST [/user/register]}: register(JSONObject)
	{POST [/user/update]}: update(User)
	{POST [/user/getPassword]}: getPassword(JSONObject)
[20:43:03:651] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserDeviceBindingController:
	{POST [/userDeviceBinding/getUserDeviceBinding]}: getUserDeviceBinding(JSONObject)
	{POST [/userDeviceBinding/bindDevice]}: bindDevice(JSONObject)
	{POST [/userDeviceBinding/unbindDevice]}: unbindDevice(JSONObject)
[20:43:03:653] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserExaminationPerformanceReportController:
	{ [/examinationPerformanceReport/consume]}: consume(Integer,Integer)
[20:43:03:654] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserFormController:
	{GET [/userForm/deleteForm]}: deleteFormOfUser(Integer,Integer)
	{GET [/userForm/getUserFormListByUserId]}: getUserFormListByUserId(Integer)
	{POST [/userForm/create]}: create(UserForm)
[20:43:03:654] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserFormFlowRecordController:
	{GET [/userFormFlowRecord/getFormFlowRecordWrapperById]}: getFormFlowRecordWrapper(Integer,Integer)
	{POST [/userFormFlowRecord/upsert]}: upsert(UserFormFlowRecord)
[20:43:03:654] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserOfYncxController:
	{GET [/userOfYncx/exportExaminationInstanceDetailExcelBundle]}: exportExaminationInstanceDetailExcelBundle(Integer,Integer)
	{GET [/userOfYncx/exportExaminationInstancePdfBundle]}: exportExaminationInstancePdfBundle(Integer,Integer)
	{ [/userOfYncx/exportDetailRankListOfExamination]}: exportDetailRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/userOfYncx/exportRankListOfExamination]}: exportRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/userOfYncx/examinationReport]}: createExaminationPdfReport(HttpServletRequest,HttpServletResponse)
[20:43:03:655] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserStatisticController:
	{ [/userStatistic/totalItemNum]}: totalNumber(Integer,Integer)
	{ [/userStatistic/getOnlineUserNumberOfExamination]}: getOnlineUserNumberOfExamination(int,int)
[20:43:03:655] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserSummaryController:
	{POST [/userSummary/upInsert]}: upInsert(JSONObject)
	{POST [/userSummary/getEntity]}: getEntity(UserSummary)
[20:43:03:655] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserViewPerformanceWithoutAdController:
	{GET [/userViewPerformanceWithoutAd/adEndedCallback]}: adEndedCallback(String,String)
	{POST [/userViewPerformanceWithoutAd/deletePermently]}: deletePermently(JSONObject)
	{ [/userViewPerformanceWithoutAd/checkIfAllowUserViewPerformanceWithoutAd]}: checkIfAllowUserViewPerformanceWithoutAd(int,int,int)
	{ [/userViewPerformanceWithoutAd/getUserViewPerformanceWithoutAdTransaction]}: getUserViewPerformanceWithoutAdTransaction(String,int,String,Integer,Integer)
	{GET [/userViewPerformanceWithoutAd/insertIfNotExisted]}: insertIfNotExisted(Integer,Integer,Integer,String)
[20:43:03:659] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WeChatMiniProgramController:
	{GET [/weixinAccount/session]}: getSession(String,String)
	{POST [/weixinAccount/getProductList]}: getProductList(JSONObject)
	{GET [/weixinAccount/getAccessToken]}: getAccessTokenOfAccount(String)
	{POST [/weixinAccount/registWithCode]}: registWithCode(JSONObject)
	{GET [/weixinAccount/autoLogin]}: autoLogin(String,String,HttpServletRequest)
	{GET [/weixinAccount/loginWithoutUserInfoAndAutoRegist]}: loginWithoutUserInfoAndAutoRegist(String,String,HttpServletRequest)
	{GET [/weixinAccount/login]}: loginWithAutoRegister(String,String,HttpServletRequest)
	{POST [/weixinAccount/loginWithUserAuthNoRegistByPost]}: loginWithUserAuthWithoutAutoRegistByPost(JSONObject,HttpServletRequest)
	{POST [/weixinAccount/loginWithAuth]}: loginWithAuth(JSONObject,HttpServletRequest)
	{POST [/weixinAccount/getUserInfoByWeixinCode]}: getUserInfoByWeixinCode(JSONObject)
	{POST [/weixinAccount/loginWithUserAuthByPost]}: loginWithUserAuthWithAutoRegistByPost(JSONObject,HttpServletRequest)
	{GET [/weixinAccount/loginWithUserAuth]}: loginWithUserAuthWithAutoRegistByGet(String,String,String,String,String,HttpServletRequest)
	{POST [/weixinAccount/getQRCodeBase64ByLimit]}: getQRCodeBase64ByLimit(JSONObject)
	{POST [/weixinAccount/getQRCodeBase64]}: getQRCodeBase64(JSONObject)
	{POST [/weixinAccount/getPhoneNumByPost]}: getPhoneNumByPost(JSONObject)
	{POST [/weixinAccount/getPhoneNum]}: getPhoneNum(JSONObject)
	{POST [/weixinAccount/getPhoneNumNew]}: getPhoneNumNew(JSONObject)
	{POST [/weixinAccount/getPhoneNumWithoutGetSession]}: getPhoneNumWithoutGetSession(JSONObject)
	{POST [/weixinAccount/searchProduct]}: searchProduct(JSONObject)
	{GET [/weixinAccount/getCouponList]}: getCoupon(String)
	{GET [/weixinAccount/receivedCouple]}: receivedCouple(String,String,String)
	{GET [/weixinAccount/getUserCoupleList]}: getUserCoupleList(String,String,String)
	{POST [/weixinAccount/msgSecCheck]}: msgSecCheck(JSONObject)
	{POST [/weixinAccount/getQRCode]}: getQRCode(JSONObject)
	{POST [/weixinAccount/imgSecCheck]}: imgSecCheck(HttpServletRequest)
	{POST [/weixinAccount/imgSecCheckByMultipart], consumes [multipart/form-data]}: imgSecCheck(MultipartFile,String)
	{POST [/weixinAccount/bind]}: bind(JSONObject)
	{POST [/weixinAccount/getGroupId]}: getGroupId(JSONObject)
[20:43:03:670] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WeChatOfficialAccountController:
	{GET [/wx/receiveMessage]}: doGet(HttpServletRequest)
	{POST [/wx/receiveMessage], produces [application/xml;charset=UTF-8]}: processRequest(HttpServletRequest)
	{GET [/wx/checkIfFollowed]}: getQrcodeScanEntry(HttpServletRequest)
	{GET [/wx/loginWithAutoRegister]}: loginWithAutoRegister(String,String)
	{GET [/wx/getJSSDKConfiguration]}: getJSSDKConfiguration(String,String)
	{GET [/wx/getTicket]}: getTicket(HttpServletRequest)
	{GET [/wx/loginByWeChatOfficialAccount]}: loginByWeChatOfficialAccount(String,String)
	{GET [/wx/getPageAuthAccessToken]}: getPageAuthAccessToken(String,String)
	{GET [/wx/getTicketOfProduct]}: getTicketOfProduct(HttpServletRequest)
	{GET [/wx/createAccountMenu]}: createAccountMenu(String)
[20:43:03:670] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WordFileController:
	{GET [/wordFile/exportExercisePaper]}: exportExercisePaper(HttpServletRequest,HttpServletResponse)
	{GET [/wordFile/exportExamPaper]}: exportExamPaper(HttpServletRequest,HttpServletResponse)
	{GET [/wordFile/exportWrongQuestions]}: exportWrongQuestions(HttpServletRequest,HttpServletResponse)
[20:43:03:671] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WrongBookController:
	{POST [/wrongBook/create]}: createWrongBook(JSONObject)
	{GET [/wrongBook/deletePermanently]}: deletePermanently(Integer)
	{POST [/wrongBook/getWrongBookQuestionList]}: getWrongBookQuestionList(JSONObject)
	{POST [/wrongBook/removeQuestion]}: removeQuestion(JSONObject)
	{POST [/wrongBook/update]}: updateIgnoreNull(JSONObject)
	{GET [/wrongBook/list]}: getWrongBookList(Integer)
[20:43:03:671] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WrongQuestionBookController:
	{POST [/wrongQuestionBook/doReview]}: doReview(JSONObject)
	{ [/wrongQuestionBook/wrongQuestionBookPageInfo]}: getWrongQuestionBookPageInfoOfUser(int,int)
	{ [/wrongQuestionBook/getWrongQuestionBookOfUser]}: getWrongQuestionBookOfUser(Integer)
	{POST [/wrongQuestionBook/batchSaveWrongQuestionBook]}: batchSaveWrongQuestionBook(JSONObject)
	{GET [/wrongQuestionBook/delete]}: delete(Integer)
[20:43:03:671] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WrongQuestionBookQuestionController:
	{ [/wrongQuestionBookQuestion/markAsReviewed]}: markAsReviewed(int,int)
	{ [/wrongQuestionBookQuestion/getQuestionWrapperListOfWrongQuestionBook]}: getQuestionWrapperListOfWrongQuestionBook(int)
	{ [/wrongQuestionBookQuestion/getQuestionWrapperListOfBook]}: getQuestionWrapperListOfBook(Integer)
	{GET [/wrongQuestionBookQuestion/getTotalWrongQuestionWrapperList]}: getTotalWrongQuestionWrapperList(Integer,Integer)
	{GET [/wrongQuestionBookQuestion/deleteByQuery]}: deleteByQuery(Integer,Integer)
[20:43:03:672] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WrongQuestionBookReviewSummaryController:
	{POST [/wrongQuestionBookReviewSummary/saveSummary]}: saveSummary(JSONObject)
[20:43:03:672] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WxMiniprogramSubscribeMessageOfEteaController:
	{GET [/wxMiniprogramSubscribeMessageOfEtea/batchSendSubscribedMessage]}: batchSendSubscribedMessage(Integer,Integer,Integer)
	{POST [/wxMiniprogramSubscribeMessageOfEtea/subscribe]}: subscribe(JSONObject)
[20:43:03:673] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.c.p.ExaminationControllerOfPC:
	{GET [/examination/examinationInViewListOfCreaterFromPC]}: getExaminationInViewListOfCreaterFromPC(Integer,Integer,Integer,Integer)
	{POST [/examination/getExaminationInViewListAndNum]}: getExaminationInViewListAndNum(JSONObject)
	{POST [/examination/getExaminationInViewListOfCreaterByTagsFromPC]}: getExaminationInViewListOfCreaterByTagsFromPC(JSONObject)
	{POST [/examination/getExaminationInViewListExcludingCreaterByTagsFromPC]}: getExaminationInViewListExcludingCreaterByTagsFromPC(JSONObject)
	{GET [/examination/getExaminationListAndSizeOfWillMark]}: getExaminationListAndSizeOfWillMark(Integer,Integer,Integer,Integer)
	{POST [/examination/getWillMarkExaminationListAndSize]}: getWillMarkExaminationListAndSize(JSONObject)
	{POST [/examination/getExaminationInViewListOfCreaterByTags]}: getExaminationInViewListOfCreaterByTags(JSONObject)
[20:43:03:673] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.c.p.ExaminationInstanceControllerOfPC:
	{GET [/examinationInstance/examinationInstanceStageSummaryListFromPC]}: getExaminationInstanceStageSummaryListFromPC(String,String,String,int,int,int)
	{GET [/examinationInstance/getExaminationInstanceStageSummaryListAndTotalNumFromPC]}: getExaminationInstanceStageSummaryListAndTotalNumFromPC(String,String,String,int,int,int)
	{POST [/examinationInstance/getExaminationInstanceStageSummaryListAndNum]}: getExaminationInstanceStageSummaryListAndNum(JSONObject)
	{POST [/examinationInstance/getExaminationInstanceStageSummaryListAndLengthByTags]}: getExaminationInstanceStageSummaryListByTagsFromPC(JSONObject)
	{POST [/examinationInstance/getListAndSizeOfExaminationInstanceWithUserInfoByConditions]}: getListAndSizeOfExaminationInstanceWithUserInfoByConditions(JSONObject)
	{GET [/examinationInstance/getExaminationInstanceListAndSizeWithUserInfoByExaminationId]}: getExaminationInstanceListWithUserInfoByExaminationId(Integer,Integer,Integer,String,Integer,Integer)
[20:43:03:674] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.c.p.PcPageDataController:
	{GET [/pageData/getCompanyCoreSummary]}: getCompanyCoreSummary(Integer,Integer)
[20:43:03:674] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.c.PageDataControllerOfCooperation:
	{GET [/pageDateControllerOfCooperation/getRankData]}: getRankData(Integer,Integer,Integer,Integer,String,String,Integer,Integer)
[20:43:03:675] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.e.ExaminationInstanceControllerOfEtea:
	{GET [/examinationInstanceControllerOfEtea/exportTotalScoreGroupByUser]}: exportTotalScoreGroupByUser(String,HttpServletResponse)
	{GET [/examinationInstanceControllerOfEtea/getUserTakenExaminations]}: getUserTakenExaminations(Integer,Integer,Integer)
	{GET [/examinationInstanceControllerOfEtea/getRecentOnGoingExaminations]}: getRecentOnGoingExaminations(Integer)
[20:43:03:679] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.e.PageDataControllerOfEtea:
	{POST [/pageDataOfEtea/getTotalScoreGroupByUser]}: getTotalScoreGroupByUser(JSONObject)
	{GET [/pageDataOfEtea/getHomePageSummaryInfo]}: getHomePageSummaryInfo(Integer)
[20:43:03:680] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.f.FormController:
	{POST [/formSystem/saveFormFlowRecord]}: saveFormFlowRecord(JSONObject)
	{POST [/formSystem/createForm]}: createForm(JSONObject)
	{GET [/formSystem/getFormWrapperById]}: getFormWrapperById(Integer)
	{POST [/formSystem/updateForm]}: updateForm(JSONObject)
[20:43:03:680] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteContentAccessController:
	{POST [/ksiteContentAccess/getContentAccessList]}: getContentAccessList(JSONObject)
	{POST [/ksiteContentAccess/add]}: add(KsiteContentAccess)
[20:43:03:680] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteFavoriteController:
	{POST [/ksiteFavorite/deleteByColumns]}: deleteByColumns(KsiteFavorite)
	{GET [/ksiteFavorite/getFavorite]}: getFavorite(Integer,String,Integer)
	{POST [/ksiteFavorite/getFavoriteList]}: getFavoriteList(JSONObject)
	{POST [/ksiteFavorite/add]}: add(KsiteFavorite)
[20:43:03:681] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteOrderController:
	{ [/ksiteOrder/createOrder]}: createKsiteOrder(JSONObject)
	{POST [/ksiteOrder/getOrderListWithKsiteInfo]}: getOrderListWithKsiteInfo(JSONObject)
	{POST [/ksiteOrder/getOrderList]}: getOrderListAndNum(JSONObject)
	{GET [/ksiteOrder/getAmountOfIncome]}: getAmountOfIncome(Integer)
	{GET [/ksiteOrder/getSettlementDashboardData]}: getSettlementDashboardData(Integer)
	{POST [/ksiteOrder/getOrderNum]}: getOrderNum(JSONObject)
	{ [/ksiteOrder/orderNotify]}: orderNotify(JSONObject)
	{POST [/ksiteOrder/update]}: update(JSONObject)
[20:43:03:681] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteOrderRelationController:
	{POST [/ksiteOrderRelation/checkContentAuth]}: checkContentAuth(KsiteOrderRelation)
[20:43:03:681] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsitePaperInstanceController:
	{GET [/ksitePaperInstance/getPaperInstanceStageSummaryDetail]}: getPaperInstanceStageSummaryDetail(Integer,String,String,Integer,Integer)
	{POST [/ksitePaperInstance/create]}: create(JSONObject)
[20:43:03:681] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsitePaperInstanceProcessController:
	{GET [/ksitePaperInstanceProcess/getPaperInstanceProcessWrapperListAndNum]}: getPaperInstanceProcessWrapperListAndNum(Integer,Boolean,Integer,Integer)
[20:43:03:682] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteUserSignatureController:
	{POST [/ksiteUserSignature/getList]}: getList(JSONArray)
	{POST [/ksiteUserSignature/signKsite]}: sign(KsiteUserSignatureWrapper)
	{GET [/ksiteUserSignature/getUserSignature]}: getUserSignature(Integer,Integer)
	{GET [/ksiteUserSignature/checkKsiteNameExisted]}: checkKsiteNameExisted(String)
	{GET [/ksiteUserSignature/getKsiteInfo]}: getKsiteInfo(String,Integer)
	{POST [/ksiteUserSignature/update]}: update(KsiteUserSignatureWrapper)
[20:43:03:682] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteWithdrawController:
	{POST [/ksiteWithdraw/withdraw]}: withdraw(KsiteWithdraw)
[20:43:03:682] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.m.c.CategoryController:
	{GET [/category/getNLevelCategory]}: getLevelCategoriesOfLessThanOrEqualDesignativeLevel(String,Integer,Integer)
	{POST [/category/getBatchPathOfCategoryList]}: getBatchPathOfCategoryList(JSONArray)
	{POST [/category/addNewCategory]}: addNewCategory(JSONObject)
	{GET [/category/getPathOfCategory]}: getPathOfCategory(Integer)
	{POST [/category/updateCategory]}: updateCategory(JSONObject)
	{POST [/category/updateCategoryList]}: updateCategoryList(JSONArray)
	{GET [/category/copySpecifiedSpaceName]}: copySpecifiedSpaceName(String,Integer,String)
	{POST [/category/add]}: add(JSONObject)
	{POST [/category/update]}: update(JSONObject)
[20:43:03:683] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QiyeweixinAccountController:
	{GET [/qiyeweixinAccount/transferLicense]}: transferLicense(String,List)
	{GET [/qiyeweixinAccount/getMemberActiveInfo]}: getMemberActiveInfo(String,String)
	{GET [/qiyeweixinAccount/queryAutoActiveStatus]}: queryAutoActiveStatus(String)
	{GET [/qiyeweixinAccount/getActivatedAccountList]}: getActivatedAccountList(String,Integer,String)
[20:43:03:685] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QiyeweixinCommunicationController:
	{ [/qiyeweixinCommunication/receiveData]}: receiveData(String,String,String,String,HttpServletRequest)
	{ [/qiyeweixinCommunication/receiveCommand]}: receiveCommand(String,String,String,String,HttpServletRequest)
	{GET [/qiyeweixinCommunication/getPreAuthCode]}: getPreAuthCode(String)
	{GET [/qiyeweixinCommunication/setSessionInfo]}: setSessionInfo(String)
	{GET [/qiyeweixinCommunication/generatePermanentCode]}: generatePermanentCode(String,String)
	{GET [/qiyeweixinCommunication/getAppQrcode]}: getAppQrcode(String)
	{GET [/qiyeweixinCommunication/getAppPermission]}: getAppPermission(String,String)
	{GET [/qiyeweixinCommunication/getAppAdmin]}: getAppAdmin(String,String)
	{GET [/qiyeweixinCommunication/getJsapiTicket]}: getJsapiTicket(String,String,String)
[20:43:03:689] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QiyeweixinSignatureController:
	{POST [/qiyeweixinSignature/getSignature]}: getSignature(JSONObject)
[20:43:03:689] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QiyeweixinUserController:
	{GET [/qiyeweixinUser/login]}: login(HttpServletRequest)
	{GET [/qiyeweixinUser/loginQkkQiyeweixin]}: wechatLogin()
	{GET [/qiyeweixinUser/loginQkkQiyeweixinFromPCWeb]}: wechatLoginFromPCWeb(String)
	{GET [/qiyeweixinUser/syncAllDepartmentNameAndUserNameByOCR]}: syncAllDepartmentNameAndUserNameByOCR(Integer)
	{GET [/qiyeweixinUser/refreshDepartmentNameAndUserNameByOCR]}: refreshDepartmentNameAndUserNameByOCR(Integer)
	{GET [/qiyeweixinUser/getUserInfo]}: getUserInfo(HttpServletRequest)
[20:43:03:690] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.CompanyControllerOfQKK:
	
[20:43:03:690] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.CompanyRegisterFormController:
	{GET [/companyRegisterForm/getCompanyRegisterForm]}: getCompanyRegisterForm(Integer)
	{GET [/companyRegisterForm/getCompanyRegisterFormFieldStructure]}: getCompanyRegisterFormFieldStructure(Integer)
	{POST [/companyRegisterForm/insertIfNotExisted]}: insertIfNotExisted(CompanyRegisterForm)
[20:43:03:690] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.CompanyRegisterFormFlowRecordController:
	{GET [/companyRegisterFormFlowRecord/getRegistrationDetail]}: getRegistrationDetail(Integer,Integer)
[20:43:03:691] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.ExaminationControllerOfQkk:
	{POST [/examination/getExaminationInViewListOfCompanyByTagesAndUserCompany]}: getExaminationInViewListOfCompanyByTagesAndUserCompany(JSONObject)
	{POST [/examination/getExaminationInViewListAndTotalSizeOfCompanyByTagesAndUserCompany]}: getExaminationInViewListAndTotalSizeOfCompanyByTagesAndUserCompany(JSONObject)
	{GET [/examination/getExamList]}: getExamList(Integer)
	{GET [/examination/checkIfAuthorized]}: checkIfAuthorized(Integer,Integer,Integer)
[20:43:03:692] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.ExaminationInstanceControllerOfQkk:
	{GET [/examinationInstanceOfQkk/exportTotalScoreGroupByUser]}: exportTotalScoreGroupByUser(String,HttpServletResponse)
	{POST [/examinationInstanceOfQkk/getTotalScoreOfExaminationListGroupByUser]}: getTotalScoreOfExaminationListGroupByUser(JSONObject)
	{POST [/examinationInstanceOfQkk/getSummaryGroupByDepartment]}: getSummaryGroupByDepartment(JSONObject)
	{GET [/examinationInstanceOfQkk/exportSummaryGroupByDepartment]}: exportSummaryGroupByDepartment(Integer,Integer,Integer,HttpServletResponse)
	{POST [/examinationInstanceOfQkk/getTopNDepartmentDistribution]}: getTopNDepartmentDistribution(JSONObject)
	{GET [/examinationInstanceOfQkk/getExaminationInstanceListWithUserInfoByExaminationId]}: getExaminationInstanceListWithUserInfoByExaminationId(Integer,Integer,Integer,String,Integer,Integer)
	{GET [/examinationInstanceOfQkk/examinationInstanceStageSummaryDetail]}: getExaminationInstanceStageSummaryDetail(Integer,Integer,Integer,String,String,Integer,Integer)
[20:43:03:694] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.PageDataController:
	{POST [/pageData/getTotalScoreGroupByUserInCompany]}: getTotalScoreGroupByUserInCompany(JSONObject)
[20:43:03:695] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QuestionControllerOfQkk:
	{POST [/question/questionListExcludingCreaterInCompany]}: getQuestionListExcludingCreaterInCompany(JSONObject)
[20:43:03:695] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.UserControllerOfQkk:
	{POST [/user/secureAdminRegister]}: secureAdminRegister(HttpServletRequest)
	{POST [/user/registerAdminFromMp]}: registerAdminFromMp(JSONObject,HttpServletRequest)
	{POST [/user/registerAdminFromH5]}: registerAdminFromH5(JSONObject,HttpServletRequest)
	{POST [/user/secureExamineeRegister]}: secureExamineeRegister(HttpServletRequest,HttpServletResponse)
	{POST [/user/registerExamineeFromMp]}: registerExamineeFromMp(JSONObject,HttpServletRequest)
	{POST [/user/registerExamineeFromH5]}: registerExamineeFromH5(JSONObject,HttpServletRequest)
	{POST [/user/secureLogin]}: secureLogin(HttpServletRequest,HttpServletResponse)
	{GET [/user/mergeCompanyUser]}: mergeCompanyUser(Integer)
[20:43:03:695] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoMiniProgramController:
	{POST [/toutiaoMiniProgram/regist]}: regist(JSONObject)
	{POST [/toutiaoMiniProgram/autoLogin]}: autologin(JSONObject)
	{POST [/toutiaoMiniProgram/update]}: update(JSONObject)
[20:43:03:696] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoQuestionStarController:
	{GET [/touTiaoQuestionStar/getQuestionStarWrapper]}: getQuestionStarWrapper(Integer)
[20:43:03:696] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoQuestionStarStageEventController:
	{GET [/toutiaoQuestionStarStageEvent/getMyPlayedStageList]}: getMyPlayedStageList(Integer,Integer)
	{POST [/toutiaoQuestionStarStageEvent/insertIfNotExisted]}: insertIfNotExisted(ToutiaoQuestionStarStageEvent)
[20:43:03:696] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoQuestionStarUserController:
	{GET [/toutiaoQuestionStarUser/getUserInfo]}: getUserInfo(Integer)
[20:43:03:697] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoQuestionStarUserProfileController:
	{POST [/toutiaoQuestionStarUserProfile/markPass]}: markPass(JSONObject)
	{POST [/toutiaoQuestionStarUserProfile/increaseExp]}: increaseExp(JSONObject)
	{POST [/toutiaoQuestionStarUserProfile/getRankInfoList]}: getRankInfoList(JSONObject)
	{GET [/toutiaoQuestionStarUserProfile/getUserInfo]}: getUserInfo(Integer)
[20:43:03:698] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.x.UserSchoolController:
	{POST [/userSchool/bindSchool]}: bindCompany(JSONObject)
[20:43:03:704] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.o.c.OssFileController:
	{POST [/ossFile/update]}: update(OssFile)
[20:43:03:719] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
	{ [/error]}: error(HttpServletRequest)
[20:43:03:799] [DEBUG] - org.springframework.web.servlet.handler.AbstractDetectingUrlHandlerMapping.detectHandlers(AbstractDetectingUrlHandlerMapping.java:86) - 'beanNameHandlerMapping' {}
[20:43:04:265] [DEBUG] - org.springframework.web.servlet.handler.SimpleUrlHandlerMapping.logMappings(SimpleUrlHandlerMapping.java:177) - 'webSocketHandlerMapping' {/wss/pkGameSocket=org.springframework.web.socket.server.support.WebSocketHttpRequestHandler@11ee7728}
[20:43:04:634] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$MapperScannerRegistrarNotFoundConfiguration.afterPropertiesSet(MybatisAutoConfiguration.java:305) - No org.mybatis.spring.mapper.MapperFactoryBean found.
[20:43:05:934] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[20:43:06:709] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[20:43:06:771] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 53 ms to scan 1 urls, producing 3 keys and 6 values 
[20:43:06:800] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype com.alibaba.nacos.api.remote.request.Request -> com.alibaba.nacos.api.remote.request.ServerRequest
[20:43:06:822] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[20:43:06:852] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 29 ms to scan 1 urls, producing 4 keys and 9 values 
[20:43:06:853] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype com.alibaba.nacos.api.remote.request.Request -> com.alibaba.nacos.api.remote.request.ServerRequest
[20:43:06:856] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[20:43:06:889] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 31 ms to scan 1 urls, producing 3 keys and 10 values 
[20:43:06:911] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
file:/Users/<USER>/Documents/git/taurus-cloud/exam-main-service/target/classes/
file:/private/var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T/cp_bglw5z4cph95pkrips04x9cfi.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/dnsns.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jaccess.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jfxrt.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunec.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/zipfs.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/nashorn.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/cldrdata.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/localedata.jar
[20:43:07:020] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 107 ms to scan 12 urls, producing 0 keys and 0 values 
[20:43:07:052] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[20:43:07:068] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
[20:43:07:075] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[20:43:07:088] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
[20:43:07:098] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[20:43:07:118] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 20 ms to scan 1 urls, producing 2 keys and 8 values 
[20:43:07:119] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype java.lang.Comparable -> java.lang.Enum
[20:43:07:119] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype java.io.Serializable -> java.lang.Enum
[20:43:07:124] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
file:/Users/<USER>/Documents/git/taurus-cloud/exam-main-service/target/classes/
file:/private/var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T/cp_bglw5z4cph95pkrips04x9cfi.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/dnsns.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jaccess.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jfxrt.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunec.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/zipfs.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/nashorn.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/cldrdata.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/localedata.jar
[20:43:07:162] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 38 ms to scan 12 urls, producing 0 keys and 0 values 
[20:43:08:413] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[20:43:08:414] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[20:43:08:416] [DEBUG] - com.taurus.examinationassistant.filter.AdminAuthFilter.init(AdminAuthFilter.java:45) - AdminAuthFilter初始化
[20:43:08:435] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[20:43:08:459] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[20:43:08:489] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[20:43:08:539] [DEBUG] - org.xnio.XnioWorker$Builder.build(XnioWorker.java:1191) - Creating worker:null, pool size:64, max pool size:64, keep alive:60000, io threads:8, stack size:0
[20:43:08:573] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[20:43:08:598] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-8', selector sun.nio.ch.KQueueSelectorImpl@56d58e92
[20:43:08:597] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-5', selector sun.nio.ch.KQueueSelectorImpl@666c247c
[20:43:08:597] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-7', selector sun.nio.ch.KQueueSelectorImpl@69cd9578
[20:43:08:597] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-2', selector sun.nio.ch.KQueueSelectorImpl@64a790a1
[20:43:08:598] [DEBUG] - io.undertow.Undertow.start(Undertow.java:160) - Configuring listener with protocol HTTP for interface 0.0.0.0 and port 8081
[20:43:08:597] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-6', selector sun.nio.ch.KQueueSelectorImpl@4b8c928e
[20:43:08:598] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 Accept', selector sun.nio.ch.KQueueSelectorImpl@6eec1d9
[20:43:08:597] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-4', selector sun.nio.ch.KQueueSelectorImpl@5893de1c
[20:43:08:597] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-1', selector sun.nio.ch.KQueueSelectorImpl@670e0b28
[20:43:08:597] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-3', selector sun.nio.ch.KQueueSelectorImpl@480a8b7f
[20:43:08:682] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service 192.168.31.135:8081 register finished
[20:43:08:932] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 40.967 seconds (JVM running for 42.363)
[20:43:08:959] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[20:43:09:078] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`account` , t.`app_id` , t.`app_secret` , t.`token` , t.`encoding_aes_key` FROM `weixin_account` t 
[20:43:09:115] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 
[20:43:09:190] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 13
[20:43:09:199] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[21:11:33:354] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xa1f7b814, L:/192.168.31.135:58457 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:11:33:368] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xc8bc8498, L:/192.168.31.135:58458 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:11:33:371] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x8d9aa8fd, L:/192.168.31.135:58459 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:11:33:450] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xa20f8b8d, L:/192.168.31.135:58466 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:11:33:451] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xc3d41572, L:/192.168.31.135:58467 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:11:33:452] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1869797148 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xa1f7b814, L:/192.168.31.135:58457 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:11:33:468] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:11:33:468] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@811620183 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xc8bc8498, L:/192.168.31.135:58458 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:11:33:474] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@226721301 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7b9217d8, L:/192.168.31.135:58495 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:11:33:478] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:11:33:480] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisPubSubConnection@2064401528 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x8d9aa8fd, L:/192.168.31.135:58459 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@6cd4b6a2[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:11:33:489] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:11:33:494] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:11:33:548] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xc4a51854, L:/192.168.31.135:58468 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:11:33:554] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x3d00a825, L:/192.168.31.135:58469 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:11:33:554] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x676bbe02, L:/192.168.31.135:58471 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:11:33:554] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x05af84d3, L:/192.168.31.135:58470 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:11:33:554] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1057369320 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xa20f8b8d, L:/192.168.31.135:58466 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:11:33:558] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@909174670 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xc3d41572, L:/192.168.31.135:58467 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:11:33:567] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:11:33:578] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:11:33:613] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1869797148 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xa1f7b814, L:/192.168.31.135:58457 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:11:33:613] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@226721301 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7b9217d8, L:/192.168.31.135:58495 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:11:33:614] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1057369320 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xa20f8b8d, L:/192.168.31.135:58466 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:11:33:613] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@811620183 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xc8bc8498, L:/192.168.31.135:58458 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:11:33:623] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisPubSubConnection@2064401528 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x8d9aa8fd, L:/192.168.31.135:58459 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@6cd4b6a2[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: CommandData [promise=java.util.concurrent.CompletableFuture@6cd4b6a2[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec]
[21:11:33:623] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@909174670 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xc3d41572, L:/192.168.31.135:58467 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:11:33:652] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x4f35c21b, L:/192.168.31.135:58473 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:11:33:654] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x7e81e5c8, L:/192.168.31.135:58472 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:11:33:654] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x8e481b8e, L:/192.168.31.135:58477 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:11:33:654] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x8b04c1eb, L:/192.168.31.135:58478 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:11:33:654] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1684184112 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xc4a51854, L:/192.168.31.135:58468 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:11:33:656] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1958419234 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x3d00a825, L:/192.168.31.135:58469 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:11:33:658] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@125506917 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x676bbe02, L:/192.168.31.135:58471 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:11:33:665] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@192777639 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x05af84d3, L:/192.168.31.135:58470 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:11:33:674] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:11:33:675] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:11:33:674] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:11:33:677] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:11:33:720] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1684184112 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xc4a51854, L:/192.168.31.135:58468 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:11:33:720] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@192777639 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x05af84d3, L:/192.168.31.135:58470 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:11:33:720] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@125506917 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x676bbe02, L:/192.168.31.135:58471 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:11:33:720] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1958419234 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x3d00a825, L:/192.168.31.135:58469 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:11:33:750] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xaa8a6bd4, L:/192.168.31.135:58479 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:11:33:751] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x128fc9e2, L:/192.168.31.135:58480 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:11:33:751] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1299209728 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x4f35c21b, L:/192.168.31.135:58473 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:11:33:751] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@881677936 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7e81e5c8, L:/192.168.31.135:58472 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:11:33:753] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@46058448 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x8e481b8e, L:/192.168.31.135:58477 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:11:33:756] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@180635007 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x8b04c1eb, L:/192.168.31.135:58478 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:11:33:756] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:11:33:760] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:11:33:760] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:11:33:760] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:11:33:791] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1299209728 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x4f35c21b, L:/192.168.31.135:58473 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:11:33:791] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@180635007 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x8b04c1eb, L:/192.168.31.135:58478 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:11:33:791] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@881677936 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7e81e5c8, L:/192.168.31.135:58472 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:11:33:793] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@46058448 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x8e481b8e, L:/192.168.31.135:58477 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:11:33:845] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x5b6d3675, L:/192.168.31.135:58482 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:11:33:852] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x8d4940a0, L:/192.168.31.135:58481 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:11:33:853] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x736853f6, L:/192.168.31.135:58483 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:11:33:853] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xe9f7bc46, L:/192.168.31.135:58484 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:11:33:854] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1881834002 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xaa8a6bd4, L:/192.168.31.135:58479 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:11:33:865] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@952283489 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x128fc9e2, L:/192.168.31.135:58480 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:11:33:869] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:11:33:869] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:11:33:909] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@952283489 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x128fc9e2, L:/192.168.31.135:58480 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:11:33:909] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1881834002 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xaa8a6bd4, L:/192.168.31.135:58479 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:11:33:948] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xabf71612, L:/192.168.31.135:58486 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:11:33:949] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x6f86eeee, L:/192.168.31.135:58487 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:11:33:949] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x7bb4e9b2, L:/192.168.31.135:58491 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:11:33:949] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xe2fdd120, L:/192.168.31.135:58490 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:11:33:949] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@93987278 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x5b6d3675, L:/192.168.31.135:58482 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:11:33:951] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@320139621 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x8d4940a0, L:/192.168.31.135:58481 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:11:33:957] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:11:33:957] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1051180843 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x736853f6, L:/192.168.31.135:58483 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:11:33:964] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:11:33:959] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@92805660 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xe9f7bc46, L:/192.168.31.135:58484 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:11:33:973] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:11:33:986] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:11:33:998] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@93987278 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x5b6d3675, L:/192.168.31.135:58482 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:11:33:999] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@320139621 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x8d4940a0, L:/192.168.31.135:58481 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:11:34:005] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1051180843 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x736853f6, L:/192.168.31.135:58483 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:11:34:019] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@92805660 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xe9f7bc46, L:/192.168.31.135:58484 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:11:34:052] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xd1e2378f, L:/192.168.31.135:58494 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:11:34:053] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@905856920 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xabf71612, L:/192.168.31.135:58486 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:11:34:056] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1019776254 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6f86eeee, L:/192.168.31.135:58487 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:11:34:058] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1855649600 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7bb4e9b2, L:/192.168.31.135:58491 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:11:34:063] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:11:34:063] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:11:34:064] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@2118348882 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xe2fdd120, L:/192.168.31.135:58490 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:11:34:074] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:11:34:074] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:11:34:103] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1019776254 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6f86eeee, L:/192.168.31.135:58487 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:11:34:104] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@905856920 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xabf71612, L:/192.168.31.135:58486 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:11:34:106] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1855649600 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7bb4e9b2, L:/192.168.31.135:58491 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:11:34:117] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@2118348882 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xe2fdd120, L:/192.168.31.135:58490 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:11:34:150] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@25552599 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd1e2378f, L:/192.168.31.135:58494 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:11:34:152] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:11:34:185] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@25552599 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd1e2378f, L:/192.168.31.135:58494 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:15:07:542] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x351480b4, L:/192.168.31.135:49345 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:15:07:553] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xd51d64a2, L:/192.168.31.135:49346 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:15:07:554] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x2e4562b7, L:/192.168.31.135:49351 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:15:07:554] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x5c69d557, L:/192.168.31.135:49347 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:15:07:554] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xc09d56fb, L:/192.168.31.135:49353 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:15:07:554] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x8dbf0636, L:/192.168.31.135:49348 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:15:07:637] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1869797148 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x351480b4, L:/192.168.31.135:49345 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:15:07:647] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@811620183 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd51d64a2, L:/192.168.31.135:49346 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:15:07:653] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1057369320 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2e4562b7, L:/192.168.31.135:49351 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:15:07:659] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@226721301 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x5c69d557, L:/192.168.31.135:49347 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:15:07:662] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@909174670 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xc09d56fb, L:/192.168.31.135:49353 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:15:07:663] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisPubSubConnection@2064401528 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x8dbf0636, L:/192.168.31.135:49348 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@1f16b41c[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:15:07:672] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:15:07:672] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:15:07:672] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:15:07:673] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:15:07:672] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:15:07:672] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:15:07:717] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@909174670 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xc09d56fb, L:/192.168.31.135:49353 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:15:07:717] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1869797148 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x351480b4, L:/192.168.31.135:49345 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:15:07:718] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@226721301 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x5c69d557, L:/192.168.31.135:49347 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:15:07:717] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@811620183 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd51d64a2, L:/192.168.31.135:49346 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:15:07:717] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1057369320 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2e4562b7, L:/192.168.31.135:49351 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:15:07:732] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisPubSubConnection@2064401528 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x8dbf0636, L:/192.168.31.135:49348 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@1f16b41c[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: CommandData [promise=java.util.concurrent.CompletableFuture@1f16b41c[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec]
[21:39:12:648] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x2151429f, L:/192.168.31.135:49362 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:39:12:677] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x17c345a1, L:/192.168.31.135:49363 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:39:12:741] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x7e4bef82, L:/192.168.31.135:49368 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:39:12:743] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xc396edd8, L:/192.168.31.135:49369 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:39:12:743] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x804bdd92, L:/192.168.31.135:49370 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:39:12:743] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x0905ec5a, L:/192.168.31.135:49371 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:39:12:744] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@952283489 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2151429f, L:/192.168.31.135:49362 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:39:12:750] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1881834002 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x17c345a1, L:/192.168.31.135:49363 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:39:12:772] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:39:12:772] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:39:12:811] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1881834002 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x17c345a1, L:/192.168.31.135:49363 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:39:12:811] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@952283489 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2151429f, L:/192.168.31.135:49362 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:39:12:834] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x027f06aa, L:/192.168.31.135:49372 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:39:12:835] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x1778f258, L:/192.168.31.135:49373 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:39:12:835] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x17d5a8c7, L:/192.168.31.135:49374 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:39:12:835] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x806385a6, L:/192.168.31.135:49375 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:39:12:835] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@93987278 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7e4bef82, L:/192.168.31.135:49368 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:39:12:844] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:39:12:839] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@320139621 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xc396edd8, L:/192.168.31.135:49369 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:39:12:845] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1051180843 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x804bdd92, L:/192.168.31.135:49370 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:39:12:849] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:39:12:851] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@92805660 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x0905ec5a, L:/192.168.31.135:49371 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:39:12:855] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:39:12:858] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:39:12:876] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@93987278 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7e4bef82, L:/192.168.31.135:49368 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:39:12:893] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@92805660 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x0905ec5a, L:/192.168.31.135:49371 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:39:12:894] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@320139621 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xc396edd8, L:/192.168.31.135:49369 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:39:12:893] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1051180843 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x804bdd92, L:/192.168.31.135:49370 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:39:12:941] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x29a1d3f9, L:/192.168.31.135:49376 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:39:12:943] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@2118348882 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x806385a6, L:/192.168.31.135:49375 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:39:12:949] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1855649600 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x17d5a8c7, L:/192.168.31.135:49374 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:39:12:950] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1019776254 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x027f06aa, L:/192.168.31.135:49372 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:39:12:953] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:39:12:953] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@905856920 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x1778f258, L:/192.168.31.135:49373 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:39:12:954] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:39:12:954] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:39:12:960] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:39:12:988] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@2118348882 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x806385a6, L:/192.168.31.135:49375 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:39:12:989] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1855649600 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x17d5a8c7, L:/192.168.31.135:49374 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:39:12:994] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@905856920 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x1778f258, L:/192.168.31.135:49373 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:39:12:994] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1019776254 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x027f06aa, L:/192.168.31.135:49372 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:39:13:044] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@25552599 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x29a1d3f9, L:/192.168.31.135:49376 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:39:13:048] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:39:13:082] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@25552599 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x29a1d3f9, L:/192.168.31.135:49376 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:39:13:542] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x42dd057f, L:/192.168.31.135:49354 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:39:13:543] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x96e8e49f, L:/192.168.31.135:49355 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:39:13:543] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xab783c6d, L:/192.168.31.135:49357 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:39:13:544] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x0e2b57a9, L:/192.168.31.135:49356 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:39:13:545] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x94c01967, L:/192.168.31.135:50098 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:39:13:545] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xdf9254c4, L:/192.168.31.135:50101 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:39:13:546] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x0cdf6b8e, L:/192.168.31.135:50099 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:39:13:547] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xc4faa092, L:/192.168.31.135:50100 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:39:13:548] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xd3ec919d, L:/192.168.31.135:50102 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:39:13:548] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xaf0cb9d9, L:/192.168.31.135:50103 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:39:13:633] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x6b3c08c9, L:/192.168.31.135:49358 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:39:13:634] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x4800e356, L:/192.168.31.135:49361 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:39:13:634] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x233fbafb, L:/192.168.31.135:49359 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:39:13:635] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xdc7e4ff9, L:/192.168.31.135:49360 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:39:13:635] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1958419234 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x42dd057f, L:/192.168.31.135:49354 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:39:13:641] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:39:13:637] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1684184112 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xab783c6d, L:/192.168.31.135:49357 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:39:13:649] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:39:13:648] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@125506917 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x96e8e49f, L:/192.168.31.135:49355 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:39:13:651] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@192777639 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x0e2b57a9, L:/192.168.31.135:49356 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:39:13:652] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:39:13:653] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@909174670 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x94c01967, L:/192.168.31.135:50098 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:39:13:655] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1869797148 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xdf9254c4, L:/192.168.31.135:50101 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:39:13:655] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:39:13:656] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:39:13:660] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1057369320 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x0cdf6b8e, L:/192.168.31.135:50099 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:39:13:661] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:39:13:673] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@811620183 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xc4faa092, L:/192.168.31.135:50100 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:39:13:675] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:39:13:682] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:39:13:682] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@226721301 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd3ec919d, L:/192.168.31.135:50102 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:39:13:682] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1958419234 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x42dd057f, L:/192.168.31.135:49354 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:39:13:685] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1684184112 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xab783c6d, L:/192.168.31.135:49357 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:39:13:693] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@192777639 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x0e2b57a9, L:/192.168.31.135:49356 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:39:13:689] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@125506917 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x96e8e49f, L:/192.168.31.135:49355 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:39:13:696] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisPubSubConnection@2064401528 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xaf0cb9d9, L:/192.168.31.135:50103 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@5b77be68[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:39:13:696] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:39:13:700] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@909174670 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x94c01967, L:/192.168.31.135:50098 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:39:13:706] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:39:13:709] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1057369320 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x0cdf6b8e, L:/192.168.31.135:50099 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:39:13:709] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1869797148 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xdf9254c4, L:/192.168.31.135:50101 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:39:13:716] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@811620183 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xc4faa092, L:/192.168.31.135:50100 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:39:13:727] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@226721301 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd3ec919d, L:/192.168.31.135:50102 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:39:13:734] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@180635007 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x4800e356, L:/192.168.31.135:49361 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:39:13:741] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@881677936 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x233fbafb, L:/192.168.31.135:49359 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:39:13:744] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@46058448 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xdc7e4ff9, L:/192.168.31.135:49360 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:39:13:745] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1299209728 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6b3c08c9, L:/192.168.31.135:49358 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:39:13:749] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:39:13:749] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:39:13:746] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:39:13:755] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:39:13:754] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisPubSubConnection@2064401528 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xaf0cb9d9, L:/192.168.31.135:50103 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@5b77be68[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: CommandData [promise=java.util.concurrent.CompletableFuture@5b77be68[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec]
[21:39:13:783] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@46058448 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xdc7e4ff9, L:/192.168.31.135:49360 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:39:13:786] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1299209728 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6b3c08c9, L:/192.168.31.135:49358 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:39:13:786] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@881677936 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x233fbafb, L:/192.168.31.135:49359 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:39:13:791] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@180635007 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x4800e356, L:/192.168.31.135:49361 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:56:13:964] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x2130acbe, L:/192.168.31.135:54671 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:56:13:977] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x72693e10, L:/192.168.31.135:54672 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:56:13:980] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xd993f32e, L:/192.168.31.135:54673 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:56:13:981] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x0fa5492a, L:/192.168.31.135:54674 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:56:13:981] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x6f239700, L:/192.168.31.135:54675 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:56:13:981] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xec7712c1, L:/192.168.31.135:54677 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:56:13:981] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xdfc66086, L:/192.168.31.135:54676 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:56:13:982] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x9b761287, L:/192.168.31.135:54678 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:56:13:982] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x9fc4d2b7, L:/192.168.31.135:54680 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:56:14:058] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x56030b16, L:/192.168.31.135:54681 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:56:14:062] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xe0f69091, L:/192.168.31.135:54683 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:56:14:063] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x6038af00, L:/192.168.31.135:54682 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:56:14:063] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x845486ac, L:/192.168.31.135:54685 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:56:14:063] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xf1e88788, L:/192.168.31.135:54684 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:56:14:063] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1958419234 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2130acbe, L:/192.168.31.135:54671 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:56:14:071] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1684184112 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x72693e10, L:/192.168.31.135:54672 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:56:14:072] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@125506917 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd993f32e, L:/192.168.31.135:54673 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:56:14:085] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@192777639 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x0fa5492a, L:/192.168.31.135:54674 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:56:14:085] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:56:14:084] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:56:14:085] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:56:14:090] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@909174670 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6f239700, L:/192.168.31.135:54675 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:56:14:095] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1869797148 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xdfc66086, L:/192.168.31.135:54676 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:56:14:100] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:56:14:102] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:56:14:102] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:56:14:102] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1057369320 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xec7712c1, L:/192.168.31.135:54677 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:56:14:103] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@811620183 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x9b761287, L:/192.168.31.135:54678 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:56:14:116] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:56:14:124] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:56:14:116] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@226721301 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x9fc4d2b7, L:/192.168.31.135:54680 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:56:14:131] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:56:14:135] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@125506917 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd993f32e, L:/192.168.31.135:54673 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:56:14:135] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1684184112 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x72693e10, L:/192.168.31.135:54672 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:56:14:138] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1869797148 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xdfc66086, L:/192.168.31.135:54676 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:56:14:138] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1958419234 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2130acbe, L:/192.168.31.135:54671 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:56:14:144] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@192777639 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x0fa5492a, L:/192.168.31.135:54674 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:56:14:144] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@909174670 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6f239700, L:/192.168.31.135:54675 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:56:14:154] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x6a556224, L:/192.168.31.135:54652 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:56:14:154] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1057369320 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xec7712c1, L:/192.168.31.135:54677 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:56:14:154] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xcbbe15b6, L:/192.168.31.135:54653 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:56:14:155] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisPubSubConnection@2064401528 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x56030b16, L:/192.168.31.135:54681 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@24e9aee5[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:56:14:157] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@811620183 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x9b761287, L:/192.168.31.135:54678 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:56:14:158] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@46058448 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xe0f69091, L:/192.168.31.135:54683 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:56:14:159] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@180635007 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xf1e88788, L:/192.168.31.135:54684 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:56:14:162] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@881677936 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6038af00, L:/192.168.31.135:54682 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:56:14:163] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:56:14:165] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:56:14:165] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:56:14:167] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1299209728 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x845486ac, L:/192.168.31.135:54685 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:56:14:171] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@226721301 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x9fc4d2b7, L:/192.168.31.135:54680 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:56:14:172] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:56:14:174] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:56:14:193] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@46058448 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xe0f69091, L:/192.168.31.135:54683 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:56:14:197] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@180635007 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xf1e88788, L:/192.168.31.135:54684 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:56:14:200] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@881677936 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6038af00, L:/192.168.31.135:54682 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:56:14:204] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1299209728 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x845486ac, L:/192.168.31.135:54685 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:56:14:210] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisPubSubConnection@2064401528 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x56030b16, L:/192.168.31.135:54681 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@24e9aee5[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: CommandData [promise=java.util.concurrent.CompletableFuture@24e9aee5[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec]
[21:56:14:254] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x7e9da59d, L:/192.168.31.135:54655 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:56:14:254] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x40527d55, L:/192.168.31.135:54657 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:56:14:254] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xdc26824f, L:/192.168.31.135:54658 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:56:14:254] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x834e5606, L:/192.168.31.135:54656 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:56:14:254] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@952283489 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xcbbe15b6, L:/192.168.31.135:54653 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:56:14:255] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1881834002 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6a556224, L:/192.168.31.135:54652 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:56:14:260] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:56:14:260] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:56:14:294] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@952283489 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xcbbe15b6, L:/192.168.31.135:54653 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:56:14:294] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1881834002 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6a556224, L:/192.168.31.135:54652 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:56:14:357] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x679551da, L:/192.168.31.135:54661 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:56:14:357] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xe9f00072, L:/192.168.31.135:54660 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:56:14:357] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xc26c4527, L:/192.168.31.135:54663 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:56:14:357] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xd60b1099, L:/192.168.31.135:54662 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:56:14:357] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@93987278 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7e9da59d, L:/192.168.31.135:54655 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:56:14:364] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1051180843 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x40527d55, L:/192.168.31.135:54657 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:56:14:367] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@92805660 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xdc26824f, L:/192.168.31.135:54658 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:56:14:367] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@320139621 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x834e5606, L:/192.168.31.135:54656 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:56:14:369] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:56:14:370] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:56:14:370] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:56:14:374] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:56:14:402] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@320139621 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x834e5606, L:/192.168.31.135:54656 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:56:14:401] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@92805660 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xdc26824f, L:/192.168.31.135:54658 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:56:14:407] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@93987278 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7e9da59d, L:/192.168.31.135:54655 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:56:14:418] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1051180843 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x40527d55, L:/192.168.31.135:54657 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:56:14:457] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xe6bf0033, L:/192.168.31.135:54664 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[21:56:14:459] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@2118348882 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x679551da, L:/192.168.31.135:54661 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:56:14:462] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1855649600 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xe9f00072, L:/192.168.31.135:54660 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:56:14:465] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:56:14:466] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1019776254 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd60b1099, L:/192.168.31.135:54662 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:56:14:466] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@905856920 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xc26c4527, L:/192.168.31.135:54663 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:56:14:468] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:56:14:468] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:56:14:468] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:56:14:496] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@2118348882 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x679551da, L:/192.168.31.135:54661 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:56:14:498] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1019776254 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd60b1099, L:/192.168.31.135:54662 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:56:14:498] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1855649600 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xe9f00072, L:/192.168.31.135:54660 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:56:14:501] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@905856920 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xc26c4527, L:/192.168.31.135:54663 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[21:56:14:556] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@25552599 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xe6bf0033, L:/192.168.31.135:54664 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[21:56:14:558] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[21:56:14:592] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@25552599 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xe6bf0033, L:/192.168.31.135:54664 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:12:25:007] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xdfd11476, L:/192.168.31.135:55531 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:12:25:099] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x283ac04e, L:/192.168.31.135:55533 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:12:25:103] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x071592c3, L:/192.168.31.135:55536 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:12:25:103] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xf20c5ddd, L:/192.168.31.135:55532 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:12:25:105] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xa05e8288, L:/192.168.31.135:55534 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:12:25:113] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xe2e88b47, L:/192.168.31.135:55535 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:12:25:115] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x2cc83fd1, L:/192.168.31.135:55537 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:12:25:115] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x0282b9fa, L:/192.168.31.135:55538 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:12:25:115] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xc10338da, L:/192.168.31.135:55539 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:12:25:115] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x480db83c, L:/192.168.31.135:55541 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:12:25:120] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x6b993941, L:/192.168.31.135:55542 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:12:25:120] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x080027f1, L:/192.168.31.135:55544 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:12:25:121] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x71cfeb92, L:/192.168.31.135:55545 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:12:25:121] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x318a255a, L:/192.168.31.135:55543 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:12:25:122] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@125506917 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xdfd11476, L:/192.168.31.135:55531 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:12:25:158] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:12:25:201] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@125506917 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xdfd11476, L:/192.168.31.135:55531 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:12:25:194] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xb62963c2, L:/192.168.31.135:55547 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:12:25:212] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xca99a71d, L:/192.168.31.135:55546 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:12:25:212] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1684184112 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x283ac04e, L:/192.168.31.135:55533 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:12:25:216] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1869797148 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x071592c3, L:/192.168.31.135:55536 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:12:25:218] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1958419234 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xf20c5ddd, L:/192.168.31.135:55532 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:12:25:218] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@192777639 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xa05e8288, L:/192.168.31.135:55534 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:12:25:218] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@909174670 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xe2e88b47, L:/192.168.31.135:55535 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:12:25:246] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1057369320 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2cc83fd1, L:/192.168.31.135:55537 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:12:25:246] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@811620183 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x0282b9fa, L:/192.168.31.135:55538 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:12:25:247] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:12:25:229] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:12:25:250] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:12:25:275] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:12:25:269] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@226721301 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xc10338da, L:/192.168.31.135:55539 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:12:25:301] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:12:25:349] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@46058448 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x480db83c, L:/192.168.31.135:55541 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:12:25:349] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@180635007 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6b993941, L:/192.168.31.135:55542 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:12:25:350] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@881677936 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x080027f1, L:/192.168.31.135:55544 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:12:25:368] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1299209728 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x71cfeb92, L:/192.168.31.135:55545 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:12:25:369] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisPubSubConnection@2064401528 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x318a255a, L:/192.168.31.135:55543 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@93a7226[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:12:25:370] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x843ea501, L:/192.168.31.135:55556 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:12:25:640] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xd74fba1b, L:/192.168.31.135:55557 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:12:25:643] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x9cde8023, L:/192.168.31.135:55554 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:12:25:643] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x693a9d4c, L:/192.168.31.135:55558 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:12:25:643] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1881834002 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xb62963c2, L:/192.168.31.135:55547 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:12:25:605] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1869797148 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x071592c3, L:/192.168.31.135:55536 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:12:25:586] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@192777639 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xa05e8288, L:/192.168.31.135:55534 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:12:25:501] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@909174670 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xe2e88b47, L:/192.168.31.135:55535 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:12:25:502] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1958419234 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xf20c5ddd, L:/192.168.31.135:55532 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:12:25:501] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1684184112 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x283ac04e, L:/192.168.31.135:55533 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:12:25:788] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:12:25:768] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@952283489 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xca99a71d, L:/192.168.31.135:55546 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:12:25:885] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:12:25:803] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:12:25:806] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x683d6104, L:/192.168.31.135:55559 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:12:25:950] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:12:25:945] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:12:25:950] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:12:25:875] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:12:25:875] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:12:25:803] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:12:26:128] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@881677936 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x080027f1, L:/192.168.31.135:55544 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:12:25:902] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:12:25:949] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisPubSubConnection@2064401528 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x318a255a, L:/192.168.31.135:55543 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@93a7226[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: CommandData [promise=java.util.concurrent.CompletableFuture@93a7226[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec]
[22:12:26:139] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xaaa8507f, L:/192.168.31.135:55560 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:12:26:127] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1881834002 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xb62963c2, L:/192.168.31.135:55547 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:12:26:124] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@46058448 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x480db83c, L:/192.168.31.135:55541 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:12:26:147] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x11f3187f, L:/192.168.31.135:55561 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:12:26:148] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x5c8bb053, L:/192.168.31.135:55562 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:12:26:148] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x9ece16c9, L:/192.168.31.135:55567 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:12:26:148] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@92805660 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x843ea501, L:/192.168.31.135:55556 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:12:26:149] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@320139621 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd74fba1b, L:/192.168.31.135:55557 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:12:26:149] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@93987278 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x9cde8023, L:/192.168.31.135:55554 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:12:26:183] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:12:26:149] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1051180843 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x693a9d4c, L:/192.168.31.135:55558 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:12:26:181] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@180635007 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6b993941, L:/192.168.31.135:55542 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:12:26:264] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:12:26:271] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:12:26:272] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@2118348882 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x683d6104, L:/192.168.31.135:55559 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:12:26:265] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@811620183 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x0282b9fa, L:/192.168.31.135:55538 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:12:26:269] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@952283489 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xca99a71d, L:/192.168.31.135:55546 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:12:26:280] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1299209728 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x71cfeb92, L:/192.168.31.135:55545 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:12:26:283] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1057369320 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2cc83fd1, L:/192.168.31.135:55537 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:12:26:282] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:12:26:283] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@226721301 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xc10338da, L:/192.168.31.135:55539 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:12:26:289] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1019776254 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xaaa8507f, L:/192.168.31.135:55560 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:12:26:297] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:12:26:297] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@25552599 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x9ece16c9, L:/192.168.31.135:55567 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:12:26:298] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:12:26:298] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1855649600 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x11f3187f, L:/192.168.31.135:55561 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:12:26:299] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@905856920 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x5c8bb053, L:/192.168.31.135:55562 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:12:26:299] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@320139621 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd74fba1b, L:/192.168.31.135:55557 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:12:26:306] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:12:26:309] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:12:26:309] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@92805660 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x843ea501, L:/192.168.31.135:55556 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:12:26:310] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:12:26:311] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@93987278 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x9cde8023, L:/192.168.31.135:55554 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:12:26:323] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1051180843 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x693a9d4c, L:/192.168.31.135:55558 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:12:26:326] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@2118348882 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x683d6104, L:/192.168.31.135:55559 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:12:26:337] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1019776254 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xaaa8507f, L:/192.168.31.135:55560 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:12:26:342] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1855649600 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x11f3187f, L:/192.168.31.135:55561 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:12:26:342] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@25552599 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x9ece16c9, L:/192.168.31.135:55567 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:12:26:343] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@905856920 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x5c8bb053, L:/192.168.31.135:55562 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:22:13:911] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1684184112 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x053bdbbc, L:/192.168.31.135:55938 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:22:13:956] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:22:13:992] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1684184112 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x053bdbbc, L:/192.168.31.135:55938 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:22:14:109] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xec8b9d82, L:/192.168.31.135:55949 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:22:14:112] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x36ced090, L:/192.168.31.135:55963 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:22:14:112] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x02dde90e, L:/192.168.31.135:55962 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:22:14:113] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x4da87a00, L:/192.168.31.135:55964 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:22:14:113] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x5939fb52, L:/192.168.31.135:55967 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:22:14:113] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x2df27327, L:/192.168.31.135:55968 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:22:14:113] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x2be0c336, L:/192.168.31.135:55966 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:22:14:113] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x6cd720f3, L:/192.168.31.135:55969 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:22:14:113] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xddffc49e, L:/192.168.31.135:55965 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:22:14:113] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x3e885a35, L:/192.168.31.135:55970 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:22:14:113] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xd6c44fb1, L:/192.168.31.135:55971 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:22:14:113] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x5a9427fd, L:/192.168.31.135:55936 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:22:14:114] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x8fd8d894, L:/192.168.31.135:55973 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:22:14:114] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x017ea120, L:/192.168.31.135:55972 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:22:14:114] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xe076810b, L:/192.168.31.135:55976 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:22:14:114] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x838f419f, L:/192.168.31.135:55977 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:22:14:114] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x3fc943f0, L:/192.168.31.135:55978 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:22:14:114] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x5077c36d, L:/192.168.31.135:55981 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:22:14:114] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x2f710fa0, L:/192.168.31.135:55979 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:22:14:114] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x76e9af8d, L:/192.168.31.135:55980 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:22:14:115] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x8bdb60c3, L:/192.168.31.135:55941 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:22:14:115] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xc3c2f363, L:/192.168.31.135:55940 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:22:14:115] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xbb31d463, L:/192.168.31.135:55937 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:22:14:115] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x9ad6a936, L:/192.168.31.135:55939 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:22:14:209] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisPubSubConnection@2064401528 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xec8b9d82, L:/192.168.31.135:55949 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@54662f5c[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:22:14:220] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@46058448 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x36ced090, L:/192.168.31.135:55963 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:22:14:221] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1881834002 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x02dde90e, L:/192.168.31.135:55962 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:22:14:221] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@881677936 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x4da87a00, L:/192.168.31.135:55964 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:22:14:221] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@180635007 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x5939fb52, L:/192.168.31.135:55967 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:22:14:221] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@952283489 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2be0c336, L:/192.168.31.135:55966 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:22:14:222] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@811620183 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2df27327, L:/192.168.31.135:55968 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:22:14:225] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1057369320 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6cd720f3, L:/192.168.31.135:55969 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:22:14:237] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:22:14:238] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:22:14:240] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:22:14:240] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:22:14:239] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:22:14:239] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:22:14:239] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:22:14:241] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:22:14:249] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1299209728 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xddffc49e, L:/192.168.31.135:55965 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:22:14:252] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@226721301 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x3e885a35, L:/192.168.31.135:55970 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:22:14:253] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:22:14:253] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@320139621 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd6c44fb1, L:/192.168.31.135:55971 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:22:14:253] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@125506917 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x5a9427fd, L:/192.168.31.135:55936 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:22:14:253] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@92805660 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x8fd8d894, L:/192.168.31.135:55973 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:22:14:254] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@93987278 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x017ea120, L:/192.168.31.135:55972 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:22:14:254] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@2118348882 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x838f419f, L:/192.168.31.135:55977 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:22:14:260] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:22:14:260] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:22:14:261] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:22:14:261] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:22:14:260] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:22:14:284] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1051180843 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xe076810b, L:/192.168.31.135:55976 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:22:14:295] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1881834002 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x02dde90e, L:/192.168.31.135:55962 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:22:14:293] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@180635007 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x5939fb52, L:/192.168.31.135:55967 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:22:14:293] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1057369320 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6cd720f3, L:/192.168.31.135:55969 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:22:14:295] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:22:14:296] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@811620183 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2df27327, L:/192.168.31.135:55968 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:22:14:297] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@881677936 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x4da87a00, L:/192.168.31.135:55964 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:22:14:301] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1019776254 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x3fc943f0, L:/192.168.31.135:55978 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:22:14:302] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:22:14:303] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1855649600 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x5077c36d, L:/192.168.31.135:55981 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:22:14:305] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@25552599 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2f710fa0, L:/192.168.31.135:55979 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:22:14:307] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@905856920 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x76e9af8d, L:/192.168.31.135:55980 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:22:14:312] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:22:14:313] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:22:14:313] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:22:14:313] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@226721301 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x3e885a35, L:/192.168.31.135:55970 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:22:14:313] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisPubSubConnection@2064401528 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xec8b9d82, L:/192.168.31.135:55949 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@54662f5c[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: CommandData [promise=java.util.concurrent.CompletableFuture@54662f5c[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec]
[22:22:14:321] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@93987278 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x017ea120, L:/192.168.31.135:55972 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:22:14:324] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@909174670 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x8bdb60c3, L:/192.168.31.135:55941 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:22:14:325] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:22:14:325] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1958419234 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xc3c2f363, L:/192.168.31.135:55940 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:22:14:330] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:22:14:334] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@192777639 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xbb31d463, L:/192.168.31.135:55937 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:22:14:336] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:22:14:339] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1051180843 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xe076810b, L:/192.168.31.135:55976 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:22:14:341] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1869797148 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x9ad6a936, L:/192.168.31.135:55939 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:22:14:344] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:22:14:344] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@25552599 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2f710fa0, L:/192.168.31.135:55979 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:22:14:345] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1855649600 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x5077c36d, L:/192.168.31.135:55981 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:22:14:350] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:22:14:350] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1019776254 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x3fc943f0, L:/192.168.31.135:55978 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:22:14:365] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@905856920 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x76e9af8d, L:/192.168.31.135:55980 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:22:14:366] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@909174670 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x8bdb60c3, L:/192.168.31.135:55941 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:22:14:382] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1869797148 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x9ad6a936, L:/192.168.31.135:55939 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:22:15:273] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@46058448 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x36ced090, L:/192.168.31.135:55963 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:22:15:283] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@952283489 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2be0c336, L:/192.168.31.135:55966 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:22:15:286] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1299209728 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xddffc49e, L:/192.168.31.135:55965 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:22:15:298] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@125506917 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x5a9427fd, L:/192.168.31.135:55936 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:22:15:308] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@92805660 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x8fd8d894, L:/192.168.31.135:55973 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:22:15:311] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@320139621 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd6c44fb1, L:/192.168.31.135:55971 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:22:15:330] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@2118348882 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x838f419f, L:/192.168.31.135:55977 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:22:15:368] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1958419234 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xc3c2f363, L:/192.168.31.135:55940 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:22:15:371] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@192777639 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xbb31d463, L:/192.168.31.135:55937 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:38:01:626] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xfa779821, L:/192.168.31.135:56415 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:38:01:723] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1684184112 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xfa779821, L:/192.168.31.135:56415 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:38:01:735] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:38:01:767] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1684184112 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xfa779821, L:/192.168.31.135:56415 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:38:01:827] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xdd7dd6f0, L:/192.168.31.135:56420 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:38:01:829] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x61dff2e7, L:/192.168.31.135:56425 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:38:01:829] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x6e2ab6a0, L:/192.168.31.135:56426 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:38:01:829] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x993442a3, L:/192.168.31.135:56427 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:38:01:925] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xf3ceb9e6, L:/192.168.31.135:56428 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:38:01:931] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x6bf528e6, L:/192.168.31.135:56429 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:38:01:931] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xe914dea9, L:/192.168.31.135:56418 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:38:01:931] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x3cd3ca65, L:/192.168.31.135:56421 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:38:01:932] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xbc52c0e7, L:/192.168.31.135:56419 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:38:01:932] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x2de6d548, L:/192.168.31.135:56422 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:38:01:932] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x95c9ae3a, L:/192.168.31.135:56423 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:38:01:932] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x54a5bde6, L:/192.168.31.135:56432 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:38:01:932] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x23da966c, L:/192.168.31.135:56439 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:38:01:932] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xdfc90844, L:/192.168.31.135:56440 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:38:01:932] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@46058448 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xdd7dd6f0, L:/192.168.31.135:56420 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:38:01:933] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@125506917 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x993442a3, L:/192.168.31.135:56427 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:38:01:934] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@952283489 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x61dff2e7, L:/192.168.31.135:56425 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:38:01:940] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:38:01:940] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:38:01:940] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:38:01:940] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1299209728 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6e2ab6a0, L:/192.168.31.135:56426 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:38:01:948] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:38:01:983] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@952283489 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x61dff2e7, L:/192.168.31.135:56425 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:38:01:982] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@46058448 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xdd7dd6f0, L:/192.168.31.135:56420 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:38:01:982] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@125506917 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x993442a3, L:/192.168.31.135:56427 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:38:01:984] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1299209728 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6e2ab6a0, L:/192.168.31.135:56426 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:38:02:035] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xc9db7f69, L:/192.168.31.135:56424 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:38:02:041] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x1dd06df5, L:/192.168.31.135:56430 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:38:02:042] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x6e572447, L:/192.168.31.135:56431 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:38:02:042] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x87d23347, L:/192.168.31.135:56433 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:38:02:042] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x6076b312, L:/192.168.31.135:56434 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:38:02:043] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xf547e701, L:/192.168.31.135:56435 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:38:02:043] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x72493405, L:/192.168.31.135:56436 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:38:02:045] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xf1505816, L:/192.168.31.135:56437 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:38:02:046] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x7a010f2e, L:/192.168.31.135:56438 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:38:02:046] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xbd6c09ec, L:/192.168.31.135:56441 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[22:38:02:046] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@92805660 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xf3ceb9e6, L:/192.168.31.135:56428 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:38:02:049] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@320139621 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6bf528e6, L:/192.168.31.135:56429 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:38:02:055] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@811620183 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xe914dea9, L:/192.168.31.135:56418 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:38:02:055] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:38:02:060] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@180635007 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x3cd3ca65, L:/192.168.31.135:56421 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:38:02:060] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:38:02:061] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:38:02:062] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1057369320 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xbc52c0e7, L:/192.168.31.135:56419 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:38:02:062] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1881834002 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2de6d548, L:/192.168.31.135:56422 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:38:02:064] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@881677936 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x95c9ae3a, L:/192.168.31.135:56423 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:38:02:064] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@2118348882 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x54a5bde6, L:/192.168.31.135:56432 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:38:02:068] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:38:02:068] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:38:02:069] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@192777639 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xdfc90844, L:/192.168.31.135:56440 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:38:02:069] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:38:02:068] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:38:02:070] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1958419234 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x23da966c, L:/192.168.31.135:56439 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:38:02:080] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:38:02:081] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:38:02:089] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:38:02:095] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@320139621 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6bf528e6, L:/192.168.31.135:56429 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:38:02:095] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@811620183 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xe914dea9, L:/192.168.31.135:56418 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:38:02:096] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@92805660 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xf3ceb9e6, L:/192.168.31.135:56428 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:38:02:100] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1057369320 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xbc52c0e7, L:/192.168.31.135:56419 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:38:02:104] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1881834002 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2de6d548, L:/192.168.31.135:56422 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:38:02:104] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@180635007 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x3cd3ca65, L:/192.168.31.135:56421 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:38:02:108] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@881677936 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x95c9ae3a, L:/192.168.31.135:56423 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:38:02:113] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@2118348882 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x54a5bde6, L:/192.168.31.135:56432 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:38:02:117] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@192777639 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xdfc90844, L:/192.168.31.135:56440 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:38:02:121] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1958419234 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x23da966c, L:/192.168.31.135:56439 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:38:02:126] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1855649600 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6076b312, L:/192.168.31.135:56434 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:38:02:129] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1019776254 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x72493405, L:/192.168.31.135:56436 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:38:02:131] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@93987278 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6e572447, L:/192.168.31.135:56431 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:38:02:132] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@226721301 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x1dd06df5, L:/192.168.31.135:56430 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:38:02:144] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:38:02:145] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:38:02:144] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:38:02:145] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1051180843 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x87d23347, L:/192.168.31.135:56433 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:38:02:153] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:38:02:153] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@25552599 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xf547e701, L:/192.168.31.135:56435 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:38:02:160] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:38:02:160] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:38:02:159] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisPubSubConnection@2064401528 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xc9db7f69, L:/192.168.31.135:56424 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@2b2cebd2[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:38:02:169] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@905856920 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xf1505816, L:/192.168.31.135:56437 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:38:02:174] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:38:02:174] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@909174670 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7a010f2e, L:/192.168.31.135:56438 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:38:02:175] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1869797148 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xbd6c09ec, L:/192.168.31.135:56441 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[22:38:02:176] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:38:02:181] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:38:02:181] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[22:38:02:183] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1855649600 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6076b312, L:/192.168.31.135:56434 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:38:02:184] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@226721301 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x1dd06df5, L:/192.168.31.135:56430 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:38:02:183] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1019776254 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x72493405, L:/192.168.31.135:56436 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:38:02:191] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@93987278 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6e572447, L:/192.168.31.135:56431 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:38:02:194] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1051180843 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x87d23347, L:/192.168.31.135:56433 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:38:02:196] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@25552599 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xf547e701, L:/192.168.31.135:56435 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:38:02:205] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@905856920 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xf1505816, L:/192.168.31.135:56437 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:38:02:211] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@909174670 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7a010f2e, L:/192.168.31.135:56438 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:38:02:222] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1869797148 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xbd6c09ec, L:/192.168.31.135:56441 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[22:38:02:228] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisPubSubConnection@2064401528 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xc9db7f69, L:/192.168.31.135:56424 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@2b2cebd2[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: CommandData [promise=java.util.concurrent.CompletableFuture@2b2cebd2[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec]
[23:12:31:794] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x5594c796, L:/192.168.31.135:61228 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:12:31:808] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x339e7a79, L:/192.168.31.135:61231 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:12:31:808] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xe5b99a31, L:/192.168.31.135:61229 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:12:31:809] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x45bd36c4, L:/192.168.31.135:61232 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:12:31:809] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xc9ca3859, L:/192.168.31.135:61230 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:12:31:809] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x1b7683ec, L:/192.168.31.135:61233 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:12:31:809] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x4ab296d4, L:/192.168.31.135:61234 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:12:31:809] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xf5c01700, L:/192.168.31.135:61235 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:12:31:809] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x767b7b0d, L:/192.168.31.135:61236 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:12:31:902] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xac4ad6fd, L:/192.168.31.135:61237 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:12:31:902] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x8d6a252a, L:/192.168.31.135:61238 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:12:31:904] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1958419234 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x5594c796, L:/192.168.31.135:61228 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:12:31:916] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1019776254 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x339e7a79, L:/192.168.31.135:61231 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:12:31:921] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1855649600 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xe5b99a31, L:/192.168.31.135:61229 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:12:31:933] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@226721301 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x45bd36c4, L:/192.168.31.135:61232 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:12:31:937] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:12:31:937] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:12:31:937] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:12:31:939] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@93987278 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xc9ca3859, L:/192.168.31.135:61230 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:12:31:944] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:12:31:945] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@25552599 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x4ab296d4, L:/192.168.31.135:61234 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:12:31:949] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1051180843 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x1b7683ec, L:/192.168.31.135:61233 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:12:31:950] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@905856920 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xf5c01700, L:/192.168.31.135:61235 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:12:31:950] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@909174670 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x767b7b0d, L:/192.168.31.135:61236 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:12:31:949] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:12:31:968] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:12:31:975] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:12:31:982] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:12:31:982] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:12:31:992] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1869797148 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xac4ad6fd, L:/192.168.31.135:61237 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:12:31:993] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisPubSubConnection@2064401528 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x8d6a252a, L:/192.168.31.135:61238 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@3ce90839[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:12:31:995] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:12:32:006] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1855649600 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xe5b99a31, L:/192.168.31.135:61229 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:12:32:006] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1958419234 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x5594c796, L:/192.168.31.135:61228 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:12:32:006] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@226721301 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x45bd36c4, L:/192.168.31.135:61232 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:12:32:006] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1019776254 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x339e7a79, L:/192.168.31.135:61231 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:12:32:006] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@93987278 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xc9ca3859, L:/192.168.31.135:61230 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:12:32:009] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:12:32:023] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1051180843 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x1b7683ec, L:/192.168.31.135:61233 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:12:32:020] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@25552599 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x4ab296d4, L:/192.168.31.135:61234 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:12:32:023] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@905856920 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xf5c01700, L:/192.168.31.135:61235 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:12:32:029] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@909174670 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x767b7b0d, L:/192.168.31.135:61236 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:12:32:032] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1869797148 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xac4ad6fd, L:/192.168.31.135:61237 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:12:32:061] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisPubSubConnection@2064401528 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x8d6a252a, L:/192.168.31.135:61238 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@3ce90839[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: CommandData [promise=java.util.concurrent.CompletableFuture@3ce90839[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec]
[23:12:32:498] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x925ec813, L:/192.168.31.135:61209 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:12:32:602] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1684184112 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x925ec813, L:/192.168.31.135:61209 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:12:32:618] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:12:32:662] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1684184112 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x925ec813, L:/192.168.31.135:61209 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:12:32:700] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x55fd9f0a, L:/192.168.31.135:61210 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:12:32:700] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x6a169b92, L:/192.168.31.135:61211 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:12:32:700] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x00647eec, L:/192.168.31.135:61212 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:12:32:701] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xabd31a76, L:/192.168.31.135:61213 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:12:32:801] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x9343dfa7, L:/192.168.31.135:61219 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:12:32:802] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x7726f608, L:/192.168.31.135:61220 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:12:32:802] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x08b2fb87, L:/192.168.31.135:61218 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:12:32:802] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xa78271eb, L:/192.168.31.135:61222 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:12:32:802] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x09e103ec, L:/192.168.31.135:61224 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:12:32:804] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x158d5bd0, L:/192.168.31.135:61223 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:12:32:804] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xd67aacba, L:/192.168.31.135:61225 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:12:32:804] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xa153a350, L:/192.168.31.135:61227 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:12:32:804] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xde9778c2, L:/192.168.31.135:61226 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:12:32:804] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@952283489 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x55fd9f0a, L:/192.168.31.135:61210 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:12:32:805] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@46058448 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x00647eec, L:/192.168.31.135:61212 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:12:32:808] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:12:32:809] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@125506917 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6a169b92, L:/192.168.31.135:61211 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:12:32:810] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:12:32:810] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1299209728 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xabd31a76, L:/192.168.31.135:61213 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:12:32:818] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:12:32:822] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:12:32:839] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@46058448 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x00647eec, L:/192.168.31.135:61212 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:12:32:842] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@952283489 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x55fd9f0a, L:/192.168.31.135:61210 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:12:32:852] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@125506917 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6a169b92, L:/192.168.31.135:61211 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:12:32:860] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1299209728 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xabd31a76, L:/192.168.31.135:61213 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:12:32:902] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@320139621 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x9343dfa7, L:/192.168.31.135:61219 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:12:32:903] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@92805660 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x08b2fb87, L:/192.168.31.135:61218 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:12:32:905] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@811620183 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7726f608, L:/192.168.31.135:61220 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:12:32:906] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:12:32:906] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:12:32:909] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1057369320 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xa78271eb, L:/192.168.31.135:61222 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:12:32:911] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1881834002 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x09e103ec, L:/192.168.31.135:61224 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:12:32:913] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@180635007 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x158d5bd0, L:/192.168.31.135:61223 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:12:32:917] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@881677936 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd67aacba, L:/192.168.31.135:61225 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:12:32:917] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:12:32:917] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:12:32:917] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:12:32:921] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@2118348882 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xa153a350, L:/192.168.31.135:61227 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:12:32:924] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@192777639 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xde9778c2, L:/192.168.31.135:61226 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:12:32:923] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:12:32:922] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:12:32:928] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:12:32:932] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:12:32:942] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@92805660 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x08b2fb87, L:/192.168.31.135:61218 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:12:32:945] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@320139621 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x9343dfa7, L:/192.168.31.135:61219 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:12:32:945] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@811620183 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7726f608, L:/192.168.31.135:61220 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:12:32:946] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1057369320 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xa78271eb, L:/192.168.31.135:61222 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:12:32:953] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1881834002 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x09e103ec, L:/192.168.31.135:61224 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:12:32:954] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@881677936 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd67aacba, L:/192.168.31.135:61225 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:12:32:955] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@180635007 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x158d5bd0, L:/192.168.31.135:61223 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:12:32:961] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@192777639 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xde9778c2, L:/192.168.31.135:61226 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:12:32:962] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@2118348882 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xa153a350, L:/192.168.31.135:61227 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:19:51:884] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x70449e70, L:/192.168.31.135:53817 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:19:51:931] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1684184112 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x70449e70, L:/192.168.31.135:53817 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:19:51:982] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:19:52:036] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1684184112 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x70449e70, L:/192.168.31.135:53817 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:19:52:036] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xcdee50b6, L:/192.168.31.135:53820 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:19:52:039] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xdbeddf37, L:/192.168.31.135:53819 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:19:52:048] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x3f7bb249, L:/192.168.31.135:53821 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:19:52:049] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x03bdb12f, L:/192.168.31.135:53822 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:19:52:152] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x891613e4, L:/192.168.31.135:53824 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:19:52:153] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x9e489501, L:/192.168.31.135:53826 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:19:52:153] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xd53b6b5c, L:/192.168.31.135:53823 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:19:52:153] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xfd9f8168, L:/192.168.31.135:53827 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:19:52:176] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xf4f7889b, L:/192.168.31.135:53825 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:19:52:176] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x1b22660c, L:/192.168.31.135:53828 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:19:52:181] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x8c12ce20, L:/192.168.31.135:53829 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:19:52:181] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xcaefff35, L:/192.168.31.135:53831 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:19:52:182] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x515e685d, L:/192.168.31.135:53830 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:19:52:182] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@46058448 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xcdee50b6, L:/192.168.31.135:53820 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:19:52:183] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@952283489 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xdbeddf37, L:/192.168.31.135:53819 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:19:52:187] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:19:52:200] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:19:52:200] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@125506917 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x3f7bb249, L:/192.168.31.135:53821 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:19:52:222] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1299209728 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x03bdb12f, L:/192.168.31.135:53822 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:19:52:225] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:19:52:239] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:19:52:240] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@92805660 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x891613e4, L:/192.168.31.135:53824 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:19:52:243] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@811620183 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x9e489501, L:/192.168.31.135:53826 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:19:52:244] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@320139621 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd53b6b5c, L:/192.168.31.135:53823 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:19:52:350] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:19:52:351] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:19:52:351] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@46058448 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xcdee50b6, L:/192.168.31.135:53820 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:19:52:367] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1057369320 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xfd9f8168, L:/192.168.31.135:53827 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:19:52:372] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:19:52:376] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1881834002 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xf4f7889b, L:/192.168.31.135:53825 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:19:52:377] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@881677936 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x1b22660c, L:/192.168.31.135:53828 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:19:52:377] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@180635007 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x8c12ce20, L:/192.168.31.135:53829 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:19:52:428] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@92805660 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x891613e4, L:/192.168.31.135:53824 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:19:52:430] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1299209728 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x03bdb12f, L:/192.168.31.135:53822 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:19:52:391] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:19:52:418] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@125506917 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x3f7bb249, L:/192.168.31.135:53821 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:19:52:429] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:19:52:391] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@952283489 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xdbeddf37, L:/192.168.31.135:53819 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:19:52:425] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:19:52:439] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@2118348882 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x515e685d, L:/192.168.31.135:53830 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:19:52:451] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@811620183 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x9e489501, L:/192.168.31.135:53826 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:19:52:454] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@320139621 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd53b6b5c, L:/192.168.31.135:53823 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:19:52:455] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:19:52:464] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@192777639 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xcaefff35, L:/192.168.31.135:53831 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:19:52:467] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:19:52:471] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:19:52:473] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xa4959014, L:/192.168.31.135:53799 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:19:52:477] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@881677936 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x1b22660c, L:/192.168.31.135:53828 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:19:52:477] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1881834002 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xf4f7889b, L:/192.168.31.135:53825 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:19:52:477] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xd1ff9fdf, L:/192.168.31.135:53802 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:19:52:478] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x3f11cbe5, L:/192.168.31.135:53801 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:19:52:478] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x7483eab5, L:/192.168.31.135:53800 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:19:52:478] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x0ab92bc1, L:/192.168.31.135:53803 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:19:52:478] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x7b52eff7, L:/192.168.31.135:53804 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:19:52:478] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xf6bddcda, L:/192.168.31.135:53805 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:19:52:478] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xcc958649, L:/192.168.31.135:53806 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:19:52:479] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x48a8cbc3, L:/192.168.31.135:53807 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:19:52:479] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xa86b696f, L:/192.168.31.135:53808 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:19:52:479] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x69f34f78, L:/192.168.31.135:53809 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[23:19:52:490] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1057369320 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xfd9f8168, L:/192.168.31.135:53827 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:19:52:493] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@180635007 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x8c12ce20, L:/192.168.31.135:53829 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:19:52:506] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@2118348882 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x515e685d, L:/192.168.31.135:53830 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:19:52:506] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@192777639 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xcaefff35, L:/192.168.31.135:53831 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:19:52:535] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1958419234 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xa4959014, L:/192.168.31.135:53799 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:19:52:537] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@226721301 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd1ff9fdf, L:/192.168.31.135:53802 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:19:52:537] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1855649600 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7483eab5, L:/192.168.31.135:53800 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:19:52:538] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1019776254 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x3f11cbe5, L:/192.168.31.135:53801 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:19:52:538] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@93987278 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x0ab92bc1, L:/192.168.31.135:53803 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:19:52:538] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@25552599 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7b52eff7, L:/192.168.31.135:53804 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:19:52:538] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1051180843 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xcc958649, L:/192.168.31.135:53806 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:19:52:538] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@905856920 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xf6bddcda, L:/192.168.31.135:53805 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:19:52:542] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@909174670 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x48a8cbc3, L:/192.168.31.135:53807 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:19:52:543] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1869797148 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xa86b696f, L:/192.168.31.135:53808 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:19:52:543] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisPubSubConnection@2064401528 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x69f34f78, L:/192.168.31.135:53809 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@44c3fe5d[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0] to 47.111.231.172/47.111.231.172:6379 
[23:19:52:546] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:19:52:546] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:19:52:546] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:19:52:547] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:19:52:547] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:19:52:547] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:19:52:582] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:19:52:583] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:19:52:581] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:19:52:582] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:19:52:586] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[23:19:52:598] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@25552599 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7b52eff7, L:/192.168.31.135:53804 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:19:52:598] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1855649600 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7483eab5, L:/192.168.31.135:53800 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:19:52:599] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1051180843 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xcc958649, L:/192.168.31.135:53806 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:19:52:601] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1958419234 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xa4959014, L:/192.168.31.135:53799 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:19:52:604] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@226721301 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd1ff9fdf, L:/192.168.31.135:53802 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:19:52:607] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1019776254 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x3f11cbe5, L:/192.168.31.135:53801 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:19:52:621] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1869797148 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xa86b696f, L:/192.168.31.135:53808 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:19:52:622] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@905856920 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xf6bddcda, L:/192.168.31.135:53805 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:19:52:625] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@93987278 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x0ab92bc1, L:/192.168.31.135:53803 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:19:52:632] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@909174670 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x48a8cbc3, L:/192.168.31.135:53807 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[23:19:52:647] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisPubSubConnection@2064401528 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x69f34f78, L:/192.168.31.135:53809 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@44c3fe5d[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: CommandData [promise=java.util.concurrent.CompletableFuture@44c3fe5d[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec]
