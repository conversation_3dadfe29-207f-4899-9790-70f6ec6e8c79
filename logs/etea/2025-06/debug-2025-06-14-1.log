[17:54:39:933] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[17:54:44:627] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$AutoConfiguredMapperScannerRegistrar.registerBeanDefinitions(MybatisAutoConfiguration.java:252) - Searching for mappers annotated with @Mapper
[17:54:44:658] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$AutoConfiguredMapperScannerRegistrar.registerBeanDefinitions(MybatisAutoConfiguration.java:264) - Using auto-configuration base package 'com.taurus.examinationassistant'
[17:54:47:868] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[17:54:48:075] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[17:54:48:082] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[17:54:48:083] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[17:54:48:084] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[17:54:48:084] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[17:54:48:084] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[17:54:48:085] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[17:54:48:085] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[17:54:48:086] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[17:54:48:086] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[17:54:48:086] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[17:54:48:086] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[17:54:48:265] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[17:54:49:991] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[17:54:50:035] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[17:54:53:352] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[17:54:53:488] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[17:54:53:552] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[17:54:54:337] [DEBUG] - io.micrometer.core.util.internal.logging.InternalLoggerFactory.newDefaultFactory(InternalLoggerFactory.java:59) - Using SLF4J as the default logging framework
[17:54:54:533] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[17:54:54:553] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[17:54:54:554] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[17:54:54:555] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[17:54:54:557] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[17:54:54:560] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[17:54:54:560] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[17:54:54:565] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[17:54:54:566] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[17:54:55:949] [DEBUG] - io.undertow.server.session.InMemorySessionManager.registerSessionListener(InMemorySessionManager.java:268) - Registered session listener io.undertow.servlet.core.SessionListenerBridge@79b89601
[17:54:56:112] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[17:55:01:625] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[17:55:01:643] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[17:55:02:234] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[17:55:02:249] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QiyewxInterfaceLicenseOrderMapper对应的Mapper
[17:55:02:528] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyFunctionRoleMapper对应的Mapper
[17:55:02:607] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserReportRelationMapper对应的Mapper
[17:55:02:638] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QrcodeScanEntryMapper对应的Mapper
[17:55:02:684] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SysLogMapper对应的Mapper
[17:55:02:723] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.TemplateMapper对应的Mapper
[17:55:02:765] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WeixinAccountMapper对应的Mapper
[17:55:02:781] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AppMapper对应的Mapper
[17:55:02:809] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationInstanceMapper对应的Mapper
[17:55:02:835] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.OssFileMapper对应的Mapper
[17:55:02:863] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookUserRecordMapper对应的Mapper
[17:55:02:883] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookMapper对应的Mapper
[17:55:02:920] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.OrdersMapper对应的Mapper
[17:55:02:964] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UniversalProductOrderRelationMapper对应的Mapper
[17:55:02:988] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionSummaryMapper对应的Mapper
[17:55:03:001] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyJobGradeMapper对应的Mapper
[17:55:03:011] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookPracticeSummaryMapper对应的Mapper
[17:55:03:023] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationWxGidMapper对应的Mapper
[17:55:03:034] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentSubjectCategoryMapper对应的Mapper
[17:55:03:047] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationExamineeSnapshotMapper对应的Mapper
[17:55:03:064] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PassRaceGameMapper对应的Mapper
[17:55:03:081] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongQuestionBookMapper对应的Mapper
[17:55:03:102] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionFavoriteMapper对应的Mapper
[17:55:03:108] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.HomepageUserFollowedMapper对应的Mapper
[17:55:03:139] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyRegisterFormFlowRecordMapper对应的Mapper
[17:55:03:145] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MessageTemplateMapper对应的Mapper
[17:55:03:162] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentSubjectOfUserMapper对应的Mapper
[17:55:03:175] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CategoryMapper对应的Mapper
[17:55:03:200] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AppGroupUserMapper对应的Mapper
[17:55:03:221] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserExaminationPerformanceReportMapper对应的Mapper
[17:55:03:247] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarStageEventMapper对应的Mapper
[17:55:03:334] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionBankQuestionMapper对应的Mapper
[17:55:03:346] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.NanxiangRegisterMapper对应的Mapper
[17:55:03:360] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QiyewxUserAccountLicenseMapper对应的Mapper
[17:55:03:397] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarUserMapper对应的Mapper
[17:55:03:416] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ManualServiceRecordMapper对应的Mapper
[17:55:03:422] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyMapper对应的Mapper
[17:55:03:433] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteUserSignatureMapper对应的Mapper
[17:55:03:450] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookGradeMapper对应的Mapper
[17:55:03:460] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookPracticeProcessMapper对应的Mapper
[17:55:03:467] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PointConsumerationMapper对应的Mapper
[17:55:03:472] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongQuestionBookReviewProcessMapper对应的Mapper
[17:55:03:478] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookExamineeMapper对应的Mapper
[17:55:03:487] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.FileAccessPermissionMapper对应的Mapper
[17:55:03:501] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookQuestionMapper对应的Mapper
[17:55:03:517] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PointUserMapper对应的Mapper
[17:55:03:533] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AnnualReportMapper对应的Mapper
[17:55:03:548] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongQuestionBookQuestionMapper对应的Mapper
[17:55:03:573] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookSummaryMapper对应的Mapper
[17:55:03:586] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentSubjectMapper对应的Mapper
[17:55:03:593] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.FreePracticeOfUserMapper对应的Mapper
[17:55:03:598] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MessageScheduleMapper对应的Mapper
[17:55:03:603] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductIntroductionMapper对应的Mapper
[17:55:03:610] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionAttachmentMapper对应的Mapper
[17:55:03:616] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PassRaceStageMapper对应的Mapper
[17:55:03:624] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentSubjectAccessInfoMapper对应的Mapper
[17:55:03:631] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperTagMapper对应的Mapper
[17:55:03:637] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UniversalOrderMapper对应的Mapper
[17:55:03:642] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyDepartmentMapper对应的Mapper
[17:55:03:649] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MessagePushSubscriptionMapper对应的Mapper
[17:55:03:655] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.GameMapper对应的Mapper
[17:55:03:662] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongQuestionBookReviewSummaryMapper对应的Mapper
[17:55:03:669] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteContentAccessMapper对应的Mapper
[17:55:03:676] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentUnlockMapper对应的Mapper
[17:55:03:681] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteOrderMapper对应的Mapper
[17:55:03:688] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationStartupMapper对应的Mapper
[17:55:03:695] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentProductOfUserMapper对应的Mapper
[17:55:03:705] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.GameQuestionRandomRuleMapper对应的Mapper
[17:55:03:718] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteFavoriteMapper对应的Mapper
[17:55:03:728] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.TaxSheetApplicationMapper对应的Mapper
[17:55:03:742] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SysRoleMapper对应的Mapper
[17:55:03:759] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductOrderRelationCountMapper对应的Mapper
[17:55:03:770] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookFavoriteMapper对应的Mapper
[17:55:03:780] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AppStartNoticeReadUserMapper对应的Mapper
[17:55:03:785] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyRegisterFormMapper对应的Mapper
[17:55:03:791] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationGroupExaminationMapper对应的Mapper
[17:55:03:803] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserOfYncxMapper对应的Mapper
[17:55:03:814] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationFavoriteMapper对应的Mapper
[17:55:03:835] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserDeviceBindingMapper对应的Mapper
[17:55:03:845] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationMapper对应的Mapper
[17:55:03:881] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserFormFlowRecordMapper对应的Mapper
[17:55:03:893] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MessagePlanMapper对应的Mapper
[17:55:03:903] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserExaminationInstanceReportMapper对应的Mapper
[17:55:03:916] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SmsMessageMapper对应的Mapper
[17:55:03:924] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PointDetailMapper对应的Mapper
[17:55:03:931] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MultipleMarkingExaminationMapper对应的Mapper
[17:55:03:937] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.DiscountMapper对应的Mapper
[17:55:03:953] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ViewPerformanceWithoutAdTransactionMapper对应的Mapper
[17:55:03:971] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsitePaperInstanceMapper对应的Mapper
[17:55:03:986] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PermissionMapper对应的Mapper
[17:55:03:992] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionExampleDeletedMapper对应的Mapper
[17:55:04:003] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyGroupUserMapper对应的Mapper
[17:55:04:014] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.TaxIdentityInfoMapper对应的Mapper
[17:55:04:019] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.InviteRelationMapper对应的Mapper
[17:55:04:024] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserFormMapper对应的Mapper
[17:55:04:029] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PermissionAuthorizationMapper对应的Mapper
[17:55:04:035] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserMapper对应的Mapper
[17:55:04:040] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsitePaperInstanceProcessMapper对应的Mapper
[17:55:04:053] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.NameListMapper对应的Mapper
[17:55:04:070] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductCountTransactionMapper对应的Mapper
[17:55:04:088] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookTagMapper对应的Mapper
[17:55:04:096] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserViewPerformanceWithoutAdMapper对应的Mapper
[17:55:04:100] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperPraiseMapper对应的Mapper
[17:55:04:107] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongBookMapper对应的Mapper
[17:55:04:113] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationExamineeMapper对应的Mapper
[17:55:04:116] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.GameResultMapper对应的Mapper
[17:55:04:118] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationTagMapper对应的Mapper
[17:55:04:120] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarUserProfileMapper对应的Mapper
[17:55:04:122] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationInstanceProcessMapper对应的Mapper
[17:55:04:129] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductOrderRelationMapper对应的Mapper
[17:55:04:134] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookPraiseMapper对应的Mapper
[17:55:04:136] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MultipleMarkingExaminationInstanceMapper对应的Mapper
[17:55:04:138] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductMapper对应的Mapper
[17:55:04:143] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperQuestionMapper对应的Mapper
[17:55:04:147] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WxMiniprogramSubscribeMessageOfEteaMapper对应的Mapper
[17:55:04:153] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AppStartNoticeMapper对应的Mapper
[17:55:04:158] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.VersionMapper对应的Mapper
[17:55:04:169] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserSummaryMapper对应的Mapper
[17:55:04:183] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarMapper对应的Mapper
[17:55:04:194] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentClassificationInfoMapper对应的Mapper
[17:55:04:205] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionMapper对应的Mapper
[17:55:04:607] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PointRuleMapper对应的Mapper
[17:55:04:621] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteWithdrawMapper对应的Mapper
[17:55:04:627] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QiyewxOrderMapper对应的Mapper
[17:55:04:637] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CustomerRequirementMapper对应的Mapper
[17:55:04:642] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteOrderRelationMapper对应的Mapper
[17:55:04:650] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserCompanyMapper对应的Mapper
[17:55:04:655] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperMapper对应的Mapper
[17:55:04:670] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductIntroductionMediaMapper对应的Mapper
[17:55:04:691] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationNameListMapper对应的Mapper
[17:55:04:705] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SysUserRoleMapper对应的Mapper
[17:55:04:709] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionCompositionChildMapper对应的Mapper
[17:55:04:712] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.NameListDetailMapper对应的Mapper
[17:55:04:716] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserOfProductMapper对应的Mapper
[17:55:04:721] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyUserGroupMapper对应的Mapper
[17:55:04:725] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarUserMilestoneMapper对应的Mapper
[17:55:04:732] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SystemMessageMapper对应的Mapper
[17:55:04:741] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.OssFileAudioMapper对应的Mapper
[17:55:04:745] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionRandomRangeMapper对应的Mapper
[17:55:04:749] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationInstanceEventMapper对应的Mapper
[17:55:04:752] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PracticePushScheduleMapper对应的Mapper
[17:55:04:770] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperFavoriteMapper对应的Mapper
[17:55:04:777] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionRandomMapper对应的Mapper
[17:55:04:924] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PassRaceResultMapper对应的Mapper
[17:55:04:980] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：2.728秒
[17:55:12:496] [DEBUG] - io.netty.util.internal.logging.InternalLoggerFactory.useSlf4JLoggerFactory(InternalLoggerFactory.java:63) - Using SLF4J as the default logging framework
[17:55:12:503] [DEBUG] - io.netty.util.concurrent.GlobalEventExecutor.<clinit>(GlobalEventExecutor.java:53) - -Dio.netty.globalEventExecutor.quietPeriodSeconds: 1
[17:55:12:517] [DEBUG] - io.netty.util.internal.InternalThreadLocalMap.<clinit>(InternalThreadLocalMap.java:100) - -Dio.netty.threadLocalMap.stringBuilder.initialSize: 1024
[17:55:12:517] [DEBUG] - io.netty.util.internal.InternalThreadLocalMap.<clinit>(InternalThreadLocalMap.java:101) - -Dio.netty.threadLocalMap.stringBuilder.maxSize: 4096
[17:55:12:553] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[17:55:12:559] [DEBUG] - io.netty.channel.MultithreadEventLoopGroup.<clinit>(MultithreadEventLoopGroup.java:44) - -Dio.netty.eventLoopThreads: 16
[17:55:12:594] [DEBUG] - io.netty.util.internal.PlatformDependent0.explicitNoUnsafeCause0(PlatformDependent0.java:515) - -Dio.netty.noUnsafe: false
[17:55:12:595] [DEBUG] - io.netty.util.internal.PlatformDependent0.javaVersion0(PlatformDependent0.java:1026) - Java version: 8
[17:55:12:597] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:140) - sun.misc.Unsafe.theUnsafe: available
[17:55:12:598] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:164) - sun.misc.Unsafe.copyMemory: available
[17:55:12:599] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:196) - sun.misc.Unsafe.storeFence: available
[17:55:12:600] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:239) - java.nio.Buffer.address: available
[17:55:12:602] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:312) - direct buffer constructor: available
[17:55:12:605] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:403) - java.nio.Bits.unaligned: available, true
[17:55:12:606] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:478) - jdk.internal.misc.Unsafe.allocateUninitializedArray(int): unavailable prior to Java9
[17:55:12:606] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:501) - java.nio.DirectByteBuffer.<init>(long, {int,long}): available
[17:55:12:606] [DEBUG] - io.netty.util.internal.PlatformDependent.unsafeUnavailabilityCause0(PlatformDependent.java:1157) - sun.misc.Unsafe: available
[17:55:12:608] [DEBUG] - io.netty.util.internal.PlatformDependent.tmpdir0(PlatformDependent.java:1303) - -Dio.netty.tmpdir: /var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T (java.io.tmpdir)
[17:55:12:608] [DEBUG] - io.netty.util.internal.PlatformDependent.bitMode0(PlatformDependent.java:1382) - -Dio.netty.bitMode: 64 (sun.arch.data.model)
[17:55:12:611] [DEBUG] - io.netty.util.internal.PlatformDependent.isOsx0(PlatformDependent.java:1125) - Platform: MacOS
[17:55:12:613] [DEBUG] - io.netty.util.internal.PlatformDependent.<clinit>(PlatformDependent.java:176) - -Dio.netty.maxDirectMemory: 3817865216 bytes
[17:55:12:613] [DEBUG] - io.netty.util.internal.PlatformDependent.<clinit>(PlatformDependent.java:183) - -Dio.netty.uninitializedArrayAllocationThreshold: -1
[17:55:12:615] [DEBUG] - io.netty.util.internal.CleanerJava6.<clinit>(CleanerJava6.java:92) - java.nio.ByteBuffer.cleaner(): available
[17:55:12:615] [DEBUG] - io.netty.util.internal.PlatformDependent.<clinit>(PlatformDependent.java:203) - -Dio.netty.noPreferDirect: false
[17:55:12:616] [DEBUG] - io.netty.channel.nio.NioEventLoop.<clinit>(NioEventLoop.java:110) - -Dio.netty.noKeySetOptimization: false
[17:55:12:616] [DEBUG] - io.netty.channel.nio.NioEventLoop.<clinit>(NioEventLoop.java:111) - -Dio.netty.selectorAutoRebuildThreshold: 512
[17:55:12:630] [DEBUG] - io.netty.util.internal.PlatformDependent$Mpsc.<clinit>(PlatformDependent.java:1008) - org.jctools-core.MpscChunkedArrayQueue: available
[17:55:12:652] [WARN] - io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:70) - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
[17:55:12:664] [DEBUG] - io.netty.util.NetUtil.<clinit>(NetUtil.java:148) - -Djava.net.preferIPv4Stack: false
[17:55:12:665] [DEBUG] - io.netty.util.NetUtil.<clinit>(NetUtil.java:149) - -Djava.net.preferIPv6Addresses: false
[17:55:12:670] [DEBUG] - io.netty.util.NetUtilInitializations.determineLoopback(NetUtilInitializations.java:145) - Loopback interface: lo0 (lo0, 0:0:0:0:0:0:0:1%lo0)
[17:55:12:674] [DEBUG] - io.netty.util.NetUtil$SoMaxConnAction.run(NetUtil.java:206) - Failed to get SOMAXCONN from sysctl and file /proc/sys/net/core/somaxconn. Default: 128
[17:55:12:702] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:141) - Default ResolvedAddressTypes: IPV4_PREFERRED
[17:55:12:703] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:142) - Localhost address: localhost/127.0.0.1
[17:55:12:703] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:151) - Windows hostname: null
[17:55:12:706] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:164) - Default search domains: []
[17:55:12:707] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:173) - Default UnixResolverOptions{ndots=1, timeout=5, attempts=16}
[17:55:12:717] [DEBUG] - io.netty.resolver.DefaultHostsFileEntriesResolver.<clinit>(DefaultHostsFileEntriesResolver.java:53) - -Dio.netty.hostsFileRefreshInterval: 0
[17:55:12:734] [DEBUG] - io.netty.util.ResourceLeakDetector.<clinit>(ResourceLeakDetector.java:129) - -Dio.netty.leakDetection.level: simple
[17:55:12:735] [DEBUG] - io.netty.util.ResourceLeakDetector.<clinit>(ResourceLeakDetector.java:130) - -Dio.netty.leakDetection.targetRecords: 4
[17:55:12:735] [DEBUG] - io.netty.util.ResourceLeakDetectorFactory$DefaultResourceLeakDetectorFactory.newResourceLeakDetector(ResourceLeakDetectorFactory.java:196) - Loaded default ResourceLeakDetector: io.netty.util.ResourceLeakDetector@41c19739
[17:55:12:802] [DEBUG] - io.netty.channel.DefaultChannelId.<clinit>(DefaultChannelId.java:79) - -Dio.netty.processId: 42123 (auto-detected)
[17:55:12:811] [DEBUG] - io.netty.channel.DefaultChannelId.<clinit>(DefaultChannelId.java:101) - -Dio.netty.machineId: 16:1e:07:ff:fe:46:12:6d (auto-detected)
[17:55:12:842] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:157) - -Dio.netty.allocator.numHeapArenas: 16
[17:55:12:843] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:158) - -Dio.netty.allocator.numDirectArenas: 16
[17:55:12:843] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:160) - -Dio.netty.allocator.pageSize: 8192
[17:55:12:843] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:165) - -Dio.netty.allocator.maxOrder: 9
[17:55:12:843] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:169) - -Dio.netty.allocator.chunkSize: 4194304
[17:55:12:844] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:170) - -Dio.netty.allocator.smallCacheSize: 256
[17:55:12:844] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:171) - -Dio.netty.allocator.normalCacheSize: 64
[17:55:12:845] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:172) - -Dio.netty.allocator.maxCachedBufferCapacity: 32768
[17:55:12:845] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:173) - -Dio.netty.allocator.cacheTrimInterval: 8192
[17:55:12:845] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:174) - -Dio.netty.allocator.cacheTrimIntervalMillis: 0
[17:55:12:845] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:175) - -Dio.netty.allocator.useCacheForAllThreads: false
[17:55:12:846] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:176) - -Dio.netty.allocator.maxCachedByteBuffersPerChunk: 1023
[17:55:12:861] [DEBUG] - io.netty.buffer.ByteBufUtil.<clinit>(ByteBufUtil.java:89) - -Dio.netty.allocator.type: pooled
[17:55:12:861] [DEBUG] - io.netty.buffer.ByteBufUtil.<clinit>(ByteBufUtil.java:98) - -Dio.netty.threadLocalDirectBufferSize: 0
[17:55:12:862] [DEBUG] - io.netty.buffer.ByteBufUtil.<clinit>(ByteBufUtil.java:101) - -Dio.netty.maxThreadLocalCharBufferSize: 16384
[17:55:12:873] [DEBUG] - io.netty.bootstrap.ChannelInitializerExtensions.getExtensions(ChannelInitializerExtensions.java:54) - -Dio.netty.bootstrap.extensions: null
[17:55:12:993] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[17:55:12:993] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[17:55:12:993] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[17:55:13:043] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:96) - -Dio.netty.recycler.maxCapacityPerThread: 4096
[17:55:13:047] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:97) - -Dio.netty.recycler.ratio: 8
[17:55:13:047] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:98) - -Dio.netty.recycler.chunkSize: 32
[17:55:13:047] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:99) - -Dio.netty.recycler.blocking: false
[17:55:13:047] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:100) - -Dio.netty.recycler.batchFastThreadLocalOnly: true
[17:55:13:054] [DEBUG] - io.netty.buffer.AbstractByteBuf.<clinit>(AbstractByteBuf.java:63) - -Dio.netty.buffer.checkAccessible: true
[17:55:13:055] [DEBUG] - io.netty.buffer.AbstractByteBuf.<clinit>(AbstractByteBuf.java:64) - -Dio.netty.buffer.checkBounds: true
[17:55:13:055] [DEBUG] - io.netty.util.ResourceLeakDetectorFactory$DefaultResourceLeakDetectorFactory.newResourceLeakDetector(ResourceLeakDetectorFactory.java:196) - Loaded default ResourceLeakDetector: io.netty.util.ResourceLeakDetector@701a3ce2
[17:55:13:097] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@244756792 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xb40f352b, L:/**************:55223 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@2ec68e69[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[17:55:13:111] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connectPubSub$3(ClientConnectionsEntry.java:234) - new pubsub connection created: RedisPubSubConnection@327845443 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6994aeba, L:/**************:55224 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@1d4d6a42[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[17:55:13:097] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@332825122 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x4a4c1b20, L:/**************:55225 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@7b4c3a26[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[17:55:13:112] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for 47.111.231.172/47.111.231.172:6379
[17:55:13:125] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[17:55:13:124] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[17:55:13:169] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1114812479 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2ac55bc4, L:/**************:55227 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@4cb54601[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[17:55:13:169] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1373109440 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x47f63cae, L:/**************:55226 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@5f10088a[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[17:55:13:187] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[17:55:13:188] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[17:55:13:228] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@137892460 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x12120552, L:/**************:55229 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@6fffd1b[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[17:55:13:229] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1431427704 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xfe472052, L:/**************:55228 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@5b962ea2[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[17:55:13:234] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[17:55:13:234] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[17:55:13:266] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@997058400 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xa99ccc3f, L:/**************:55230 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@35aa1e7d[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[17:55:13:268] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1181540094 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x39fc89a7, L:/**************:55231 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@48ab3843[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[17:55:13:274] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[17:55:13:281] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[17:55:13:315] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1422078845 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xb85341cb, L:/**************:55232 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@5a04c795[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[17:55:13:315] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@859005963 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd212e12c, L:/**************:55233 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@3df48c1d[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[17:55:13:332] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[17:55:13:332] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[17:55:13:366] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1593373923 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x568b41c8, L:/**************:55235 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@503f014c[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[17:55:13:369] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@691043432 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7c28231e, L:/**************:55234 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@27f7873e[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[17:55:13:376] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[17:55:13:378] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[17:55:13:416] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@788079579 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xb2f32fea, L:/**************:55236 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@203ed874[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[17:55:13:416] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@331749411 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x39126d53, L:/**************:55237 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@1d01e42b[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[17:55:13:440] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[17:55:13:442] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[17:55:13:475] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1779111991 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x25d92ae8, L:/**************:55238 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@74a5d787[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[17:55:13:477] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[17:55:13:478] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1599686857 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x1ec5b065, L:/**************:55239 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@7870dcaf[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[17:55:13:480] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[17:55:13:508] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@646192112 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x5d5751b1, L:/**************:55240 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@1cdedc5a[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[17:55:13:510] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[17:55:13:512] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1827468930 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x552e66c0, L:/**************:55241 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@3c19be22[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[17:55:13:516] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[17:55:13:540] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1529845680 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xba19d287, L:/**************:55242 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@1e2d02b1[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[17:55:13:547] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1217059663 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6392e63f, L:/**************:55243 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@d884c3a[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[17:55:13:554] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[17:55:13:554] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[17:55:13:587] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1604956424 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x83eea3e3, L:/**************:55248 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@1aab2499[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[17:55:13:597] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[17:55:13:597] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1205810757 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xb8c06612, L:/**************:55249 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@5979bddf[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[17:55:13:598] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[17:55:13:630] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1284814222 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xb6203588, L:/**************:55251 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@9963278[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[17:55:13:630] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@831884304 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x05b61aae, L:/**************:55250 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@74971446[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[17:55:13:630] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for 47.111.231.172/47.111.231.172:6379
[17:55:18:170] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.AccessController:
	{GET [/access/getTokenStr]}: getToken(Integer,HttpServletRequest)
	{GET [/access/heartbeat]}: heartbeat()
	{GET [/access/getTokenUnlimit]}: getTokenUnlimit(Integer,HttpServletRequest)
	{GET [/access/getTokenByTime]}: getTokenByTime(Integer,Long,HttpServletRequest)
[17:55:18:172] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ActivityController:
	{ [/activity/getActiveActivities]}: getActiveActivities(String)
[17:55:18:173] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.AnnualReportController:
	{GET [/annual/getReport]}: getEntity(Integer)
[17:55:18:174] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.AppStartNoticeController:
	{GET [/appStartNotice/getCurrentNoticeOfUser]}: getCurrentNoticeOfUser(String,Integer)
	{GET [/appStartNotice/getCurrentNotice]}: getCurrentNotice(String)
[17:55:18:175] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.AppStartNoticeReadUseController:
	{GET [/appStartNoticeReadUser/save]}: save(Integer,Integer)
[17:55:18:186] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CompanyController:
	{POST [/company/createCompany]}: createCompany(JSONObject)
	{GET [/company/getCompanyInfoByUserId]}: getCompanyInfoByUserId(int)
	{POST [/company/createDepartment]}: createDepartment(JSONObject)
	{POST [/company/createJobGrade]}: createJobGrade(JSONObject)
	{POST [/company/createFunctionRole]}: createFunctionRole(JSONObject)
	{GET [/company/deleteCompany]}: deleteCompany(int)
	{GET [/company/deleteDepartment]}: deleteDepartment(int)
	{GET [/company/deleteDepartmentCarefully]}: deleteDepartmentCarefully(Integer,Integer)
	{GET [/company/deleteJobGrade]}: deleteJobGrade(int)
	{GET [/company/deleteJobGradeCarefully]}: deleteJobGradeCarefully(Integer,Integer)
	{GET [/company/deleteFunctionRole]}: deleteFunctionRole(int)
	{GET [/company/deleteFunctionRoleCarefully]}: deleteFunctionRoleCarefully(Integer,Integer)
	{POST [/company/modifyCompany]}: modifyCompany(JSONObject)
	{GET [/company/modifyCompanyName]}: modifyCompanyName(String,Integer)
	{POST [/company/modifyDepartment]}: modifyDepartment(JSONObject)
	{POST [/company/modifyJobGrade]}: modifyJobGrade(JSONObject)
	{POST [/company/modifyFunctionRole]}: modifyFunctionRole(JSONObject)
	{GET [/company/getCompanyInfo]}: getCompanyInfo(Integer)
	{GET [/company/getCompanyInfoById]}: getCompanyInfoWrapperById(int)
	{GET [/company/getCompanyInfoByCodeOrName]}: getCompanyInfoByCodeOrName(String)
	{GET [/company/getCompanyInfoListByName]}: getCompanyInfoListByName(String)
	{GET [/company/getAllDepartmentOfCompany]}: getAllDepartmentOfCompany(Integer,Integer)
	{GET [/company/getNGradeDepartment]}: getLevelDepartmentOfLessThanOrEqualDesignativeGrade(Integer,Integer)
	{GET [/company/getAllDepartmentOfCompanyByUserId]}: getAllDepartmentOfCompanyByUserId(Integer)
	{GET [/company/getSubDepartmentExcludingSelf]}: getSubDepartmentExcludingSelf(String)
	{GET [/company/getSubDepartmentIncludingSelf]}: getSubDepartmentIncludingSelf(String)
	{GET [/company/getAllCompanyJobGradeOfCompany]}: getAllCompanyJobGradeOfCompany(int)
	{GET [/company/getAllCompanyInfo]}: getAllCompanyInfo(int)
	{POST [/company/getQiyeweixinCompanyList]}: getQiyeweixinCompanyList(JSONObject)
	{GET [/company/getAllCompanyInfoWith2Grade]}: getAllCompanyInfoWith2Grade(Integer)
	{GET [/company/getAllCompanyFunctionRoleOfCompany]}: getAllCompanyFunctionRoleOfCompany(int)
	{GET [/company/search]}: searchCompanyByKeyword(String)
	{GET [/company/batchCloseAccount]}: batchCloseAccount(String)
	{GET [/company/closeAccount]}: closeAccount(Integer,Integer)
	{GET [/company/getResidualFlow]}: getResidualFlow(Integer)
	{POST [/company/getOrganizationFrameworkInfoByIds]}: getOrganizationFrameworkInfoByIds(JSONObject)
	{POST [/company/modify]}: modify(Company)
[17:55:18:192] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CompanyDepartmentController:
	{GET [/companyDepartment/getCompanyDepartmentList]}: getCompanyDepartmentList(Integer,String)
[17:55:18:196] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CompanyGroupUserController:
	{POST [/companyGroupUser/create]}: createCompanyGroupUser(CompanyGroupUser)
	{POST [/companyGroupUser/batchCreate]}: batchCreateCompanyGroupUser(List)
	{POST [/companyGroupUser/update]}: updateCompanyGroupUser(CompanyGroupUser)
	{GET [/companyGroupUser/delete]}: deleteCompanyGroupUser(Integer,String)
	{GET [/companyGroupUser/getGroupsByUserId]}: getGroupsByUserId(Integer)
	{GET [/companyGroupUser/getUsersByGroupId]}: getUsersByGroupId(Integer)
	{GET [/companyGroupUser/checkUserInGroup]}: checkUserInGroup(Integer,Integer)
[17:55:18:200] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CompanyUserGroupController:
	{GET [/companyUserGroup/list]}: getGroupList(Integer,Integer,String,String)
	{POST [/companyUserGroup/create]}: createGroup(JSONObject)
	{GET [/companyUserGroup/detail]}: getGroupDetail(Integer)
	{POST [/companyUserGroup/update]}: updateGroup(JSONObject)
	{GET [/companyUserGroup/delete]}: deleteGroup(Integer)
[17:55:18:207] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentClassificationInfoController:
	{ [/contentClassification/getPracticeContentOfUnlock]}: getPracticeContentOfUnlock()
	{ [/contentClassification/getUnlockedPracticeContentOfUser]}: getUnlockedPracticeContentOfUser(Integer)
	{ [/contentClassification/getPracticeContentOfSubject]}: getPracticeContentOfSubject(Integer,Integer)
[17:55:18:210] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentProductOfUserController:
	{GET [/contentProductOfUser/getProductListOfUser]}: getProductListOfUser(Integer)
[17:55:18:211] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentSubjectCategoryController:
	{GET [/contentSubjectCategory/getCategoryListOfSubject]}: getCategoryListOfSubject(Integer)
	{GET [/contentSubjectCategory/getSubjectListOfCategory]}: getSubjectListOfCategory(Integer)
	{GET [/contentSubjectCategory/getPathListOfSubject]}: getPathListOfSubject(Integer)
	{GET [/contentSubjectCategory/getNLevelCategoryWithContentNum]}: getLevelCategoriesOfLessThanOrEqualDesignativeLevel(String,Integer,Integer)
	{POST [/contentSubjectCategory/update]}: update(JSONArray)
	{GET [/contentSubjectCategory/delete]}: delete(Integer,Integer)
	{POST [/contentSubjectCategory/save]}: save(JSONArray)
[17:55:18:212] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentSubjectController:
	{POST [/contentSubject/createSubject]}: createSubject(JSONObject)
	{GET [/contentSubject/getContentSubjectByContent]}: getContentSubjectByContent(String,Integer)
	{GET [/contentSubject/getAllContentSubject]}: getSubjectInfoList(String,Integer,Integer,Integer)
	{GET [/contentSubject/getSubjectListOfProduct]}: getSubjectListOfProduct(String)
	{GET [/contentSubject/getAllContentSubjectWithUserSelection]}: getAllContentSubjectWithUserSelection(Integer,String)
	{GET [/contentSubject/getSubjectWrapperBySubjectId]}: getSubjectWrapperBySubjectId(Integer)
	{GET [/contentSubject/getSubjectWrapper]}: getSubjectWrapper(Integer)
	{GET [/contentSubject/getSubjectWrapperWithFavoriteInfo]}: getSubjectWrapperWithFavoriteInfo(Integer,Integer)
	{POST [/contentSubject/modifySubject]}: modifySubject(JSONObject)
	{GET [/contentSubject/delete]}: delete(Integer)
[17:55:18:218] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentSubjectOfUserController:
	{POST [/contentSubjectOfUser/saveContentSubjectsOfUser]}: saveContentSubjectsOfUser(JSONObject)
	{GET [/contentSubjectOfUser/getContentSubjectListOfUser]}: getContentSubjectListOfUser(Integer,String,Integer,Integer,Integer)
[17:55:18:219] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentSubjectSummaryController:
	{GET [/contentSubjectSummary/getSummary]}: getSummary(Integer)
[17:55:18:220] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentUnlockController:
	{POST [/contentUnlock/save]}: save(JSONObject)
[17:55:18:221] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CryptographyController:
	{ [/cryptography/encrypt]}: encrypt(String)
	{ [/cryptography/decrypt]}: decrypt(String)
[17:55:18:223] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CustomerRequirementController:
	{POST [/cs/createRequirement]}: save(JSONObject)
[17:55:18:242] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.DataResolverController:
	{GET [/dataResolver/setSelectionChildType]}: setSelectionChildType(int)
	{GET [/dataResolver/reCalculateQuestionScore]}: reCalculateQuestionScore(int,Integer,Integer,String)
	{GET [/dataResolver/reCalculateExaminationInstanceByQuestionId]}: reCalculateExaminationInstance(int,int)
	{GET [/dataResolver/reCalculateExaminationInstance]}: reCalculateExaminationInstance(int,Integer,String)
	{GET [/dataResolver/reCreateCompletionOptions]}: reCreateCompletionOptions(Integer,Integer,Integer,String)
	{GET [/dataResolver/checkDataMigrationFromFreeVersion]}: checkDataMigrationFromFreeVersion(int)
	{GET [/dataResolver/migrateDataFromFreeVersionToEnterpriseVersion]}: migrateDataFromFreeVersionToEnterpriseVersion(int,int)
	{GET [/dataResolver/deleteExaminationIncludingWrongRandomQuestion]}: deleteExaminationIncludingWrongRandomQuestion()
	{GET [/dataResolver/configUserSysUserRole]}: configUserSysUserRole()
	{GET [/dataResolver/recaculateExaminationInstanceBecauseOfNullScore]}: recaculateExaminationInstanceBecauseOfNullScore()
	{GET [/dataResolver/deleteInValidExamination]}: deleteInValidExamination()
	{GET [/dataResolver/deleteExaminationInstanceAndProcessOfEtea]}: deleteExaminationInstanceAndProcessOfEtea(String,String)
	{GET [/dataResolver/deleteInvalidSubscirbeMessage]}: deleteInvalidSubscirbeMessage(String)
	{GET [/dataResolver/clearJSONErrorStr]}: clearJSONErrorStr(Integer)
	{GET [/dataResolver/recaculateExcerciseBookInfo]}: recaculateExcerciseBookInfo()
	{GET [/dataResolver/deleteInvalidQuestion]}: deleteInvalidQuestion()
	{GET [/dataResolver/deleteInvalidPaper]}: deleteInvalidPaper()
	{GET [/dataResolver/deleteExerciseBookAndRelation]}: deleteInvalidExerciseBookAndRelation()
	{GET [/dataResolver/deleteInvalidSystemMessage]}: deleteInvalidSystemMessage(String,String,String)
	{GET [/dataResolver/deleteExaminationInstance]}: deleteExaminationInstance()
	{GET [/dataResolver/handleDeleteQuestionsEvent]}: handleDeleteQuestionsEvent()
	{GET [/dataResolver/restoreExerciseBookByProcess]}: restoreExerciseBookByProcess()
	{GET [/dataResolver/clearRedisData]}: clearRedisData(String)
	{GET [/dataResolver/clearAccountPermently]}: clearAccountPermently(Integer,Integer)
	{GET [/dataResolver/generateQuestionJsonFileOfExamination]}: generateQuestionJsonFileOfExamination(Integer,Integer,Integer)
	{GET [/dataResolver/generateHotFileListOfCDN]}: generateHotFileListOfCDN(Integer,Integer)
	{GET [/dataResolver/resizeOssPicFile]}: resizeOssPicFile(String)
	{GET [/dataResolver/deleteQuestionAndRelatedRecords]}: deleteQuestionAndRelatedRecords(String,String)
	{GET [/dataResolver/deleteWrongQuestionBook]}: deleteWrongQuestionBook(String,String)
	{GET [/dataResolver/transformCompletionQuestion]}: transformCompletionQuestion(Integer,Integer)
	{GET [/dataResolver/restoreExerciseBook]}: restoreExerciseBook(Integer)
	{GET [/dataResolver/replaceDepartmentId]}: replaceDepartmentId(Integer)
	{GET [/dataResolver/generateAnnualReport]}: generateAnnualReport(Integer,Integer,String,String)
	{GET [/dataResolver/insertCompanyUser]}: insertCompanyUser(Integer)
	{GET [/dataResolver/getUnCompleteQuestion]}: getUnCompleteQuestion(Integer)
	{GET [/dataResolver/deleteDepartments]}: deleteDepartments()
	{POST [/dataResolver/processQuestionAndAnswers], consumes [multipart/form-data]}: processQuestionAndAnswers(HttpServletRequest,HttpServletResponse)
	{GET [/dataResolver/deletedRandomRangeQuestionWithQuestionDeletedState]}: deletedRandomRangeQuestionWithQuestionDeletedState(Integer,Integer)
	{GET [/dataResolver/clearExaminationCache]}: reloadExaminationCache(Integer,Integer)
[17:55:18:252] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.DownloadProxyController:
	{POST [/download/proxy]}: proxyDownload(HttpServletRequest,JSONObject)
[17:55:18:273] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationController:
	{GET [/examination/examination]}: getExaminationById(int)
	{POST [/examination/create]}: createExamination(JSONObject)
	{POST [/examination/createExaminationWithContentCheck]}: createExaminationWithContentCheck(JSONObject)
	{POST [/examination/createIncludingExamineeSelect]}: createExaminationIncludingExamineeSelect(JSONObject)
	{POST [/examination/createFixedExamination]}: createFixedExamination(JSONObject)
	{POST [/examination/createRandomExaminationIncludingExamineeSelect]}: createRandomExaminationIncludingExamineeSelect(JSONObject)
	{POST [/examination/createRandomExamination]}: createRandomExamination(JSONObject)
	{GET [/examination/delete]}: deleteExamination(String)
	{GET [/examination/examinationWrapperListOfCreater]}: getExaminationWrapperListOfCreater(int,int,int,int)
	{GET [/examination/examinationInViewListOfCreater]}: getExaminationInViewListOfCreater(Integer,Integer,Integer,Integer)
	{GET [/examination/getExaminationInViewListOfCreater]}: getExaminationInViewList(Integer,Integer,Integer,int)
	{GET [/examination/getExaminationInViewListAndTotalNumOfCreater]}: getExaminationInViewListAndTotalNumOfCreater(int,int,int,int)
	{GET [/examination/getRecommendExaminations]}: getRecommendExaminations(Integer,Integer,Integer,Integer)
	{GET [/examination/getRecommendExaminationsByProduct]}: getRecommendExaminationsByProduct(Integer,String)
	{POST [/examination/getExaminationInViewListOfCreaterInCompanyByTags]}: getExaminationInViewListOfCreaterInCompanyByTags(JSONObject)
	{POST [/examination/getExaminationInViewListOfCompanyByTags]}: getExaminationInViewListOfCompanyByTags(JSONObject)
	{GET [/examination/ongoingExaminationList]}: getOngoingExaminationList()
	{GET [/examination/getExaminationAndPaperById]}: getExaminationAndPaperById(int)
	{POST [/examination/getExaminationAndPaperByColumns]}: getExaminationAndPaperByColumns(JSONObject)
	{POST [/examination/getExaminationAndPaperByColumnsAfterEncode]}: getExaminationAndPaperByColumnsAfterEncode(JSONObject)
	{POST [/examination/getExaminationDetail]}: getExaminationDetail(JSONObject)
	{GET [/examination/examinationByCode]}: getExaminationByCode(String)
	{GET [/examination/getExaminationListByPaperId]}: getExaminationListByPaperId(Integer)
	{GET [/examination/getExaminationListByKeyword]}: getExaminationListByKeyword(String)
	{GET [/examination/getExaminationListOfCreater]}: getExaminationListOfCreater(Integer,Integer,Integer,Integer)
	{GET [/examination/idByCode]}: getExaminationIdByCode(String)
	{POST [/examination/edit]}: editExamination(JSONObject)
	{GET [/examination/examinationWrapper]}: getExaminationWrapperById(Integer)
	{GET [/examination/getExaminationWrapperSecure]}: getExaminationWrapperSecure(Integer,Integer)
	{GET [/examination/getExaminationWrapperStructure]}: getExaminationWrapperStructure(Integer,Integer)
	{POST [/examination/updateExaminationWrapperStructure]}: updateExaminationWrapperStructure(JSONObject)
	{GET [/examination/getExaminationAndPaperQuestionList]}: getExaminationAndPaperQuestionList(Integer)
	{GET [/examination/getExaminationAndPaperQuestionListAfterEncoded]}: getExaminationAndPaperQuestionListAfterEncoded(Integer)
	{GET [/examination/examinationWrapperWithRedLock]}: examinationWrapperWithRedLock(Integer,String)
	{GET [/examination/suspend]}: suspendExamination(Integer)
	{GET [/examination/resume]}: resumeExamination(Integer)
	{GET [/examination/changeExamineeDisplay]}: changeExamineeDisplay(Integer,Boolean)
	{GET [/examination/rename]}: renameExamination(int,int,String)
	{GET [/examination/renameExamCode]}: renameExamCode(Integer,String)
	{GET [/examination/modifyCode]}: modifyCode(Integer,String)
	{GET [/examination/configAdvancedOptions]}: configAdvancedOptions(String)
	{POST [/examination/saveAdvancedOptions]}: saveAdvancedOptions(JSONObject)
	{GET [/examination/checkIfExceedExaminationTimesLimit]}: checkIfExceedExaminationTimesLimit(Integer,Integer)
	{GET [/examination/copy]}: copyExamination(Integer,Integer,Boolean)
	{GET [/examination/transmit]}: transmitExamination(Integer,Integer,Integer)
	{GET [/examination/getLeftTime]}: getLeftFromBeginTime(Integer)
	{POST [/examination/sendQiyeweixinQkkNotice]}: sendQiyeweixinQkkNotice(JSONObject)
	{POST [/examination/update]}: update(Examination)
	{GET [/examination/reset]}: reset(Integer)
[17:55:18:279] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationExamineeController:
	{GET [/examinationExaminee/getExaminationExaminee]}: getExaminationExaminee(Integer)
	{POST [/examinationExaminee/updateExaminationExaminee]}: updateExaminationExaminee(JSONObject)
[17:55:18:292] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationExamineeSnapshotController:
	{GET [/examinationExamineeSnapshot/insertSnapshot]}: insertSnapshot(Integer,Integer,String,String)
	{GET [/examinationExamineeSnapshot/getUserSnapshotListOfExamination]}: getUserSnapshotListOfExamination(Integer,String,Integer,Integer)
	{GET [/examinationExamineeSnapshot/getUserSnapshotList]}: getUserSnapshotList(Integer,String,Integer,Integer)
	{GET [/examinationExamineeSnapshot/exportSnapshotListOfExamination]}: exportSnapshotListOfExamination(Integer)
	{GET [/examinationExamineeSnapshot/delete]}: delete(String)
[17:55:18:292] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationFavoriteController:
	{GET [/examinationFavorite/add]}: addFavorite(int,int)
	{GET [/examinationFavorite/cancel]}: cancelFavorite(int,int)
	{GET [/examinationFavorite/ifFavorite]}: ifFavorite(int,int)
	{GET [/examinationFavorite/getFavoriteList]}: getFavoriteList(int,int,int)
	{GET [/examinationFavorite/getFavoriteListAndNum]}: getFavoriteListAndNum(int,int,int)
[17:55:18:294] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationGroupExaminationController:
	{ [/examinationGroupExamination/list]}: getExaminationGroupExaminationServiceListById(int)
[17:55:18:307] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationInstanceController:
	{POST [/examinationInstance/getExaminationInstanceList]}: getExaminationInstanceList(JSONObject)
	{[GET, POST] [/examinationInstance/exportRankListOfExaminationInCompany]}: exportRankListOfExaminationInCompany(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/examinationInstance/exportNoRankListOfExaminationInCompany]}: exportNoRankListOfExaminationInCompany(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/examinationInstance/exportDetailRankListOfExamination]}: exportDetailRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{GET [/examinationInstance/getExaminationInstanceWrapperById]}: getExaminationInstanceWrapperById(Integer)
	{POST [/examinationInstance/createExaminationInstance]}: createExaminationInstance(JSONObject,HttpServletRequest)
	{POST [/examinationInstance/createExaminationInstanceWithContentCheck]}: createExaminationInstanceWithContentCheck(JSONObject,HttpServletRequest,HttpServletResponse)
	{POST [/examinationInstance/getExaminationInstanceWrapperListOfExaminee]}: getExaminationInstanceWrapperListOfExaminee(JSONObject)
	{GET [/examinationInstance/examinationInstanceWrapperListOfExaminee]}: getExaminationInstanceWrapperListOfExaminee(int,int,int,int)
	{ [/examinationInstance/getExaminationInstanceInfoById]}: getExaminationInstanceInfoById(Integer)
	{POST [/examinationInstance/getRecentExaminationInstanceListWithAverage]}: getRecentExaminationInstanceListWithAverage(JSONObject)
	{GET [/examinationInstance/getExaminationInstanceListWithUserInfoByExaminationId]}: getExaminationInstanceListWithUserInfoByExaminationId(int,int,int,int)
	{GET [/examinationInstance/examinationInstanceWrapperListOfExamination]}: getExaminationInstanceWrapperListOfExamination(String,int,Integer,Integer)
	{GET [/examinationInstance/rankOfExaminationInstance]}: getRankOfExaminationInstance(int,int,int)
	{GET [/examinationInstance/rankListOfExamination]}: getRankListOfExamination(Integer,Integer,Integer,Integer,Integer)
	{GET [/examinationInstance/getTopTenRankListAndTotalExamineeNumOfExamination]}: getTopTenRankListAndTotalExamineeNumOfExamination(int)
	{GET [/examinationInstance/getLatestTenListAndTotalExamineeNumOfExamination]}: getLatestTenListAndTotalExamineeNumOfExamination(Integer)
	{GET [/examinationInstance/rankListOfExaminationByCode]}: getRankListOfExaminationByCode(String)
	{GET [/examinationInstance/getTotalTimes]}: getTotalTimes(Integer,Integer)
	{GET [/examinationInstance/timesOfExaminationOfExaminee]}: getTimesOfExaminationOfExaminee(int,int,String,String)
	{POST [/examinationInstance/batchDelete]}: batchDeleteByExaminee(JSONArray)
	{POST [/examinationInstance/batchDeleteByAdmin]}: batchDeleteByAdmin(JSONArray)
	{GET [/examinationInstance/examinationInstanceStageSummary]}: getExaminationInstanceStageSummary(int,String,String)
	{POST [/examinationInstance/getExaminationInstanceStageSummaryListByTags]}: getExaminationInstanceStageSummaryList(JSONObject)
	{GET [/examinationInstance/examinationInstanceStageSummaryList]}: getExaminationInstanceStageSummaryList(String,String,String,int,int,int)
	{GET [/examinationInstance/examinationInstanceStageSummaryListExcludingExample]}: getExaminationInstanceStageSummaryListExcludingExample(String,String,String,int,int,int)
	{GET [/examinationInstance/getExaminationInstanceStageSummaryListExcludingCreater]}: getExaminationInstanceStageSummaryListExcludingCreater(String,String,String,int,int,int)
	{GET [/examinationInstance/examinationInstanceStageSummaryDetail]}: getExaminationInstanceStageSummaryDetail(int,String,String,Integer,Integer,String)
	{POST [/examinationInstance/getExaminationStageSummaryDetail]}: getExaminationStageSummaryDetail(JSONObject)
	{GET [/examinationInstance/individualScoreListOfExamination]}: getIndividualScoreListOfExamination(int,int,String,String)
	{GET [/examinationInstance/individualScoreList]}: getIndividualScoreList(int,String,String,int)
	{GET [/examinationInstance/getIndividualRankInfo]}: getIndividualRankInfo(Integer,Integer,Integer)
	{GET [/examinationInstance/getIndividualRankInfoWithDuplicateRemoval]}: getIndividualRankInfoWithDuplicateRemoval(Integer,Integer,Integer)
	{GET [/examinationInstance/getExaminationInstanceWrapperAndProcessById]}: getExaminationInstanceWrapperAndProcessById(Integer)
	{POST [/examinationInstance/getInstanceWrapperAndProcessList]}: getInstanceWrapperAndProcessList(JSONObject)
	{GET [/examinationInstance/notApprovedExaminationInstanceList]}: getNotApprovedExaminationInstanceList(Integer,Integer,Integer,Integer)
	{GET [/examinationInstance/getNotApprovedExaminationInstanceListAndLength]}: getNotApprovedExaminationInstanceListAndLength(Integer,Integer,Integer,Integer)
	{POST [/examinationInstance/approveInstance]}: approveInstance(JSONObject)
	{[GET, POST] [/examinationInstance/exportNoRankListOfExamination]}: exportNoRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/examinationInstance/exportRankListOfExamination]}: exportRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{POST [/examinationInstance/getCountResult]}: getCountResult(JSONObject)
	{[GET, POST] [/examinationInstance/exportDetailRankListOfExaminationInCompany]}: exportDetailRankListOfExaminationInCompany(HttpServletRequest,HttpServletResponse)
	{ [/examinationInstance/exportInstanceOfExaminee]}: exportInstanceOfExaminee(HttpServletRequest,HttpServletResponse)
	{GET [/examinationInstance/exportAbsentList]}: exportAbsentList(HttpServletRequest,HttpServletResponse)
	{GET [/examinationInstance/getAbsentList]}: getAbsentList(Integer)
	{GET [/examinationInstance/getUserListOfExamination]}: getUserListOfExamination(Integer)
	{GET [/examinationInstance/getExaminationAnalysisData]}: getAnalysisData(Integer)
	{GET [/examinationInstance/getDistributionOfScore]}: getDistributionOfScore(Integer)
	{GET [/examinationInstance/getDistributionOfDuration]}: getDistributionOfDuration(Integer)
	{POST [/examinationInstance/getExamineeList]}: getExamineeList(JSONObject)
	{GET [/examinationInstance/delete]}: delete(int,String)
	{POST [/examinationInstance/create]}: create(JSONObject,HttpServletRequest,HttpServletResponse)
[17:55:18:313] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationInstanceEventController:
	{POST [/examinationInstanceEvent/getSummaryInfo]}: getSummaryInfo(JSONObject)
	{POST [/examinationInstanceEvent/getExamBehaviorList]}: getExamBehaviorList(JSONObject)
	{POST [/examinationInstanceEvent/insert]}: insert(ExaminationInstanceEvent)
	{POST [/examinationInstanceEvent/getList]}: getList(JSONObject)
[17:55:18:323] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationInstanceProcessController:
	{GET [/examinationInstanceProcess/examinationInstanceProcessWrapperListAndNum]}: examinationInstanceProcessWrapperListAndNum(Integer,Integer,Integer,Integer)
	{GET [/examinationInstanceProcess/wrongQuestionList]}: getWrongQuestionsOfExaminee(int,int,int)
	{GET [/examinationInstanceProcess/rightWrongNumOfExamination]}: getQuestionRightWrongNumOfExamination(int)
	{GET [/examinationInstanceProcess/rightWrongNumOfQuestion]}: getQuestionRightWrongNum(int)
	{GET [/examinationInstanceProcess/getDistributionGroupByAnswerContent]}: getDistributionGroupByAnswerContent(Integer,Integer)
	{GET [/examinationInstanceProcess/getAnalysisOfExamination]}: getAnalysisOfExamination(Integer,Integer,Integer,String)
	{POST [/examinationInstanceProcess/changeQuestionResult]}: changeQuestionResult(JSONObject)
	{GET [/examinationInstanceProcess/exportQuestionAnalysis]}: exportQuestionAnalysis(Integer,HttpServletResponse)
	{POST [/examinationInstanceProcess/performAiScoring]}: performAiScoring(JSONObject)
	{GET [/examinationInstanceProcess/examinationInstanceProcessWrapperList]}: getExaminationInstanceProcessWrapperList(int,Integer,Integer,Integer)
[17:55:18:330] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationNameListController:
	{POST [/examinationNameList/addExaminationNameList]}: addExaminationNameList(JSONObject)
	{POST [/examinationNameList/upInsert]}: upInsert(JSONObject)
	{GET [/examinationNameList/getList]}: getList(Integer,Integer)
[17:55:18:340] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationStartUpController:
	{GET [/examinationStartUp/getCountGroupByUser]}: getCountGroupByUser(Integer,Integer,Integer)
	{GET [/examinationStartUp/add]}: add(Integer,Integer)
	{GET [/examinationStartUp/delete]}: delete(Integer,Integer)
	{GET [/examinationStartUp/getCount]}: getCount(Integer,Integer)
[17:55:18:342] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationTagsController:
	{POST [/examinationTags/createrExamTags]}: createExamTags(JSONObject)
	{ [/examinationTags/getTagsOfExam]}: getTagsOfExam(int)
	{GET [/examinationTags/getAllExamTagsOfCreaterIdInCompany]}: getAllExamTagsOfCreaterIdInCompany(Integer,Integer)
	{GET [/examinationTags/getAllExamTagsExcludingCreaterIdInCompany]}: getAllExamTagsExcludingCreaterIdInCompany(int,int)
	{ [/examinationTags/getAllExamTagsOfCompanyId]}: getAllExamTagsOfCompanyId(int,Boolean)
	{ [/examinationTags/getAllExamTagsOfUserInCompany]}: getAllExamTagsOfUserInCompany(Integer,Integer)
[17:55:18:350] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationWxGidController:
	{POST [/examinationWxGid/addIfNoExist]}: addEntity(ExaminationWxGid)
	{GET [/examinationWxGid/getEntity]}: getEntity(Integer,String)
	{GET [/examinationWxGid/delete]}: delete(Integer,String)
	{GET [/examinationWxGid/getList]}: getList(Integer)
[17:55:18:352] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookController:
	{POST [/exerciseBook/batchUpdate]}: batchUpdate(JSONArray)
	{POST [/exerciseBook/create]}: createExerciseBook(JSONObject)
	{POST [/exerciseBook/createExerciseBookByQuestionTypeAndCategories]}: createExerciseBookByQuestionTypeAndCategories(JSONObject)
	{GET [/exerciseBook/modifyExerciseBook]}: modifyExerciseBook(String,String)
	{GET [/exerciseBook/delete]}: deleteExerciseBook(int)
	{GET [/exerciseBook/deleteExerciseBookAndRelation]}: deleteExerciseBookAndRelation(Integer)
	{GET [/exerciseBook/getRecommendedKeyWords]}: getRecommendedKeyWords()
	{ [/exerciseBook/exerciseBookListOfCreaterIncludingFavorite]}: getExerciseBookListOfCreaterIncludingFavorite(int,int,int,int,int)
	{ [/exerciseBook/exerciseBook]}: getExerciseBookById(int)
	{GET [/exerciseBook/getExampleQuestions]}: getExampleQuestions(Integer)
	{ [/exerciseBook/exerciseBookBeginInfo]}: getExerciseBookBeginInfoById(Integer,Integer)
	{GET [/exerciseBook/getExerciseBookBeginInfoById]}: getKsiteExerciseBookBeginInfoById(Integer,Integer,Integer)
	{GET [/exerciseBook/getExerciseBookIndexPageInfo]}: getExerciseBookIndexPageInfo(Integer)
	{GET [/exerciseBook/getPracticeSummaryInfo]}: getPracticeSummaryInfo(Integer)
	{ [/exerciseBook/exerciseBookWrapper]}: getExerciseBookWrapperById(String)
	{ [/exerciseBook/switchDisplayToExaminee]}: switchDisplayToExaminee(Integer,Boolean)
	{GET [/exerciseBook/advancedExerciseBookWrapper]}: getAdvancedExerciseBookWrapperById(int)
	{POST [/exerciseBook/doExercise]}: doExercise(JSONObject)
	{POST [/exerciseBook/doExerciseSecure]}: doExerciseSecure(JSONObject)
	{GET [/exerciseBook/rename]}: renameExerciseBook(String,int)
	{POST [/exerciseBook/savePracticeSummary]}: savePracticeSummary(JSONObject)
	{GET [/exerciseBook/getCatagoriesAndNum]}: getCatagoriesAndNum(Integer,Integer,String)
	{GET [/exerciseBook/getTagsGroupByDomain]}: getTagsGroupByDomain(Integer,Integer,String)
	{GET [/exerciseBook/getExerciseBookListByCatagory]}: getExerciseBookListByCatagory(Integer,Integer,String,String,String,Integer,Integer)
	{GET [/exerciseBook/getExerciseBookListAndTotalNum]}: getExerciseBookListAndTotalNum(Integer,Integer,String,String,String,Integer,Integer)
	{GET [/exerciseBook/getDailyPracticeResult]}: getDailyPracticeResult(Integer,Integer,Date,Date)
	{GET [/exerciseBook/getRecommendedList]}: getRecommendedList(Integer,Integer,Integer)
	{GET [/exerciseBook/getFavoriteRankList]}: getFavoriteRankList(String,Integer,Integer,Integer,Integer)
	{POST [/exerciseBook/getExerciseBookListOfCreater]}: getExerciseBookListOfCreater(JSONObject)
	{GET [/exerciseBook/exerciseBookListOfCreater]}: getExerciseBookListOfCreater(Integer,String,String,String,Integer,Integer,Integer)
	{ [/exerciseBook/getExerciseBookList]}: getExerciseBookList(Integer,String,String,String,Integer,Integer,Integer)
	{POST [/exerciseBook/sendQiyeweixinQkkNotice]}: sendQiyeweixinQkkNotice(JSONObject)
	{POST [/exerciseBook/update]}: update(ExerciseBook)
[17:55:18:354] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookExamineeController:
	{GET [/exerciseBookExaminee/getExerciseBookExaminee]}: getExerciseBookExaminee(Integer)
	{POST [/exerciseBookExaminee/updateExerciseBookExaminee]}: updateExerciseBookExaminee(JSONObject)
[17:55:18:355] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookFavorateController:
	{ [/exerciseBookFavorate/addFavorite]}: addInFavoriteList(int,int)
	{ [/exerciseBookFavorate/addBatchFavorite]}: addBatchFavorite(String,int)
	{ [/exerciseBookFavorate/cancelFavorite]}: deleteFavoriteOfUser(Integer,Integer)
	{GET [/exerciseBookFavorate/myFavoriteExerciseBookPage]}: getMyFavoriteExerciseBookPageInfo(Integer,Integer)
	{GET [/exerciseBookFavorate/getMyFavoriteInfo]}: getMyFavoriteInfo(Integer,Integer)
	{ [/exerciseBookFavorate/getSomeoneExerciseBookAndUserPracticeInfo]}: getSomeoneExerciseBookAndUserPracticeInfo(int,int)
	{ [/exerciseBookFavorate/getCompanyExerciseBookAndUserPracticeInfo]}: getCompanyExerciseBookAndUserPracticeInfo(int,int)
	{POST [/exerciseBookFavorate/getCompanyExamineePracticeInfo]}: getCompanyExamineePracticeInfo(JSONObject)
	{GET [/exerciseBookFavorate/getCompanyPracticeSummary]}: getCompanyPracticeSummary(Integer,Integer)
	{ [/exerciseBookFavorate/getEntity]}: getEntity(Integer,Integer)
[17:55:18:357] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookGradeController:
	{POST [/exerciseBookGrade/add]}: add(ExerciseBookGrade)
[17:55:18:357] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookPracticeProcessController:
	{GET [/exerciseBookPracticeProcess/getRankOfInterval]}: getRankOfInterval(Integer,Integer,Integer,String,String,Integer,Integer)
	{GET [/exerciseBookPracticeProcess/getSequenceNoInRankOfInterval]}: getSequenceNoInRankOfInterval(Integer,Integer,Integer,Integer,String,String)
	{GET [/exerciseBookPracticeProcess/getRankInfo]}: getRankInfo(Integer,Integer,Integer,Integer,String,String,Integer,Integer)
[17:55:18:358] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookPracticeSummaryController:
	{GET [/exerciseBookPracticeSummary/getRankOfInterval]}: getRankOfInterval(Integer,Integer,Integer,String,String,Integer,Integer)
	{GET [/exerciseBookPracticeSummary/getSequenceNoInRankOfInterval]}: getSequenceNoInRankOfInterval(Integer,Integer,Integer,Integer,String,String)
	{GET [/exerciseBookPracticeSummary/getRankInfo]}: getRankInfo(Integer,Integer,Integer,Integer,String,String,Integer,Integer)
	{GET [/exerciseBookPracticeSummary/getUserPracticeSummary]}: getUserPracticeSummary(Integer,Integer,String,String)
	{POST [/exerciseBookPracticeSummary/getUserPracticeListWithSummary]}: getUserPracticeListWithSummary(JSONObject)
	{POST [/exerciseBookPracticeSummary/getDepartmentPracticeStatistics]}: getDepartmentPracticeStatistics(JSONObject)
	{GET [/exerciseBookPracticeSummary/exportUserPracticeSummary]}: exportUserPracticeSummary(HttpServletRequest,HttpServletResponse)
[17:55:18:358] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookPraiseController:
	{ [/exerciseBookPraise/add]}: addPraise(int,int)
	{ [/exerciseBookPraise/cancel]}: cancelPraise(int,int)
[17:55:18:359] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookQuestionController:
	{GET [/exerciseBookQuestion/exerciseBookQuestionWrapperList]}: getExerciseBookQuestionsById(int,Integer,Integer)
	{GET [/exerciseBookQuestion/getExerciseBookQuestionWrapperListWithRedLock]}: getExerciseBookQuestionWrapperList(Integer,Integer,Integer)
	{GET [/exerciseBookQuestion/exerciseBookQuestionWrapperListAndTotalNum]}: getExerciseBookQuestionsAndTotalNumById(Integer,Integer,Integer)
	{GET [/exerciseBookQuestion/getDayDayExerciseQuestionList]}: getDayDayExerciseQuestionList(Integer,Integer,Integer,Boolean)
[17:55:18:359] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookSummaryController:
	{GET [/exerciseBookSummary/getSummary]}: getSummary(Integer)
[17:55:18:359] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookUserRecordController:
	{GET [/exerciseBookUserRecord/updateHavePractisedNum]}: updateHavePractisedNum(Integer,Integer)
	{ [/exerciseBookUserRecord/getExerciseProgress]}: getExerciseBookProgressOfUser(int,int)
	{GET [/exerciseBookUserRecord/exportUserPracticeSummary]}: exportUserPracticeSummary(Integer,HttpServletResponse)
[17:55:18:359] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.FileAccessPermissionController:
	{POST [/fileAccessPermission/generateUnlockAdQRCode]}: generateUnlockAdQRCode(JSONObject)
	{POST [/fileAccessPermission/addFileAccessPermission]}: addFileAccessPermission(JSONObject)
	{POST [/fileAccessPermission/getFileAccessPermission]}: getFileAccessPermission(JSONObject)
[17:55:18:359] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.FileController:
	{[GET, POST] [/file/uploadFile]}: uploadFile(HttpServletRequest)
	{[GET, POST] [/file/uploadFileWithCheck]}: uploadFileWithCheck(HttpServletRequest)
	{ [/file/downloadFile]}: downLoad(String,HttpServletResponse,boolean)
	{GET [/file/downloadNetFile]}: downloadNetFile(HttpServletRequest,HttpServletResponse)
	{POST [/file/uploadOssFile]}: uploadOssFile(HttpServletRequest,HttpServletResponse)
[17:55:18:360] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.FreePracticeOfUserController:
	{POST [/freePracticeOfUser/getFreePractice]}: getFreePractice(JSONObject)
	{GET [/freePracticeOfUser/getQuestionListOfFreePractice]}: getQuestionListOfFreePractice(Integer,Integer)
	{GET [/freePracticeOfUser/getSummaryGroupByQuestionType]}: getSummaryGroupByQuestionType(Integer,Integer)
	{GET [/freePracticeOfUser/delete]}: delete(Integer,Integer)
	{POST [/freePracticeOfUser/save]}: save(JSONObject)
[17:55:18:360] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.GameController:
	{GET [/game/getGameWrapper]}: getGameWrapper(Integer)
	{POST [/game/updateInsert]}: updateInsert(JSONObject)
[17:55:18:360] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.GameResultController:
	{GET [/gameResult/getRankInfo]}: getRankInfo(String,Integer,Integer)
[17:55:18:361] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.HomepageUserFollowedController:
	{GET [/homepageUserFollowed/getUserFollowedInfo]}: getUserFollowedInfo(Integer,Integer)
	{GET [/homepageUserFollowed/getUserFollowedList]}: getUserFollowedList(Integer)
	{POST [/homepageUserFollowed/update]}: update(HomepageUserFollowedWrapper)
	{GET [/homepageUserFollowed/delete]}: delete(Integer,Integer)
	{POST [/homepageUserFollowed/save]}: save(HomepageUserFollowedWrapper)
[17:55:18:361] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.InviteRelationController:
	{GET [/inviteRelation/saveInviteRelation]}: saveInviteRelation(int,int,int)
	{GET [/inviteRelation/getRecentInviteRelationAnnouncement]}: getRecentInviteRelationAnnouncement(int)
	{GET [/inviteRelation/getExaminationInstanceListOfInvitee]}: getExaminationInstanceListOfInvitee(int,int,int,int,int)
	{GET [/inviteRelation/getEventResultOfInviter]}: getEventResultOfInviter(int,int,int)
	{GET [/inviteRelation/getResultOfEvent]}: getResultOfEvent(int,int,int,int,int)
	{[GET, POST] [/inviteRelation/exportRankListOfEvent]}: exportRankListOfExamination(HttpServletRequest,HttpServletResponse)
[17:55:18:363] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ManualServiceController:
	{POST [/manualService/addManualImportRecord]}: addManualImportRecord(JSONObject)
	{POST [/manualService/getManualImportRecords]}: getManualImportRecords(JSONObject)
	{POST [/manualService/updateManualImportRecord]}: updateManualImportRecord(ManualServiceRecord)
[17:55:18:365] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MessageController:
	{GET [/message/sendMessage]}: sendMessage(String)
[17:55:18:366] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MessagePushSubscriptionController:
	{POST [/messagePushSubscription/saveIfNotExisted]}: saveIfNotExisted(MessagePushSubscription)
	{GET [/messagePushSubscription/deleteEntity]}: deleteEntity(String,Integer,Integer)
	{POST [/messagePushSubscription/changeBatch]}: changeBatch(JSONObject)
	{GET [/messagePushSubscription/getEntity]}: getEntity(String,Integer,Integer)
	{POST [/messagePushSubscription/getList]}: getList(JSONObject)
[17:55:18:368] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MessageScheduleController:
	{GET [/messageSchedule/sendMessage]}: sendMessage(String)
	{POST [/messageSchedule/deleSave]}: deleSave(JSONObject)
	{POST [/messageSchedule/update]}: updateIgnoreNull(MessageSchedule)
	{GET [/messageSchedule/getList]}: getList(String,Integer,Integer)
[17:55:18:369] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MonitorViewController:
	{GET [/view/{sessionId}]}: viewMonitor(String,Model)
[17:55:18:369] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MultipleMarkingController:
	{GET [/multipleMarking/acceptMultipleMarking]}: acceptMultipleMarking(Integer,Integer)
	{GET [/multipleMarking/getMultipleMarkingExamintionListOfUser]}: getMultipleMarkingExamintionListOfUser(Integer)
	{GET [/multipleMarking/hasMultipleMarkingExaminationInstanceByExaminationInstanceId]}: hasMultipleMarkingExaminationInstanceByExaminationInstanceId(Integer)
	{GET [/multipleMarking/getWorkStateOfMarking]}: getWorkStateOfMarking(Integer,Integer)
	{GET [/multipleMarking/finishMarking]}: finishMarking(Integer)
[17:55:18:370] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.NameListController:
	{GET [/nameList/getEntityWrapper]}: getEntityWrapper(Integer)
	{GET [/nameList/getEntity]}: getEntity(Integer)
	{POST [/nameList/update]}: update(JSONObject)
	{GET [/nameList/delete]}: delete(Integer)
	{POST [/nameList/save]}: save(JSONObject)
	{GET [/nameList/getList]}: getList(Integer)
[17:55:18:380] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.NanXiangController:
	{POST [/nanxiang/getEntity]}: getEntity(NanxiangRegister)
	{GET [/nanxiang/check]}: check(String)
	{POST [/nanxiang/save]}: save(NanxiangRegister)
	{POST [/nanxiang/validate]}: validate(NanxiangRegister)
[17:55:18:383] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.OrderController:
	{POST [/order/createManualOrder]}: createManualOrder(JSONObject)
	{ [/order/create]}: createOrder(JSONObject)
	{ [/order/createCountOrder]}: createCountOrder(JSONObject)
	{POST [/order/createCountOrderByWeChatNative]}: createOrderByWeChatNative(JSONObject)
	{POST [/order/createCountOrderByJSAPI]}: createCountOrderByJSAPI(JSONObject)
	{POST [/order/orderNotify]}: orderNotify(JSONObject)
	{ [/order/getUnconsumedValueOfAccount]}: getUnconsumedValueOfAccount(String,Integer)
	{GET [/order/getOrderById]}: getOrderById(Integer)
	{GET [/order/getList]}: getList(String,String,Integer,Integer,Integer)
[17:55:18:385] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperController:
	{POST [/paper/batchUpdate]}: batchUpdate(JSONArray)
	{GET [/paper/getPaperWrapperWithRedLock]}: getPaperWrapperWithRedLock(Integer)
	{ [/paper/modifyPaper]}: modifyPaper(String,String)
	{POST [/paper/update]}: updatePaper(Paper)
	{ [/paper/paperListOfCreater]}: getPaperListOfCreater(String,int)
	{GET [/paper/getPaperById]}: getPaperById(Integer)
	{ [/paper/paperWrapper]}: getPaperWrapperById(String)
	{GET [/paper/getPaperWithFavoriteInfoAndOrderRelationInfo]}: getPaperWithFavoriteInfoAndOrderRelationInfo(Integer,Integer,Integer)
	{ [/paper/advancedPaperWrapper]}: getAdvancedPaperWrapperById(int)
	{ [/paper/paperListToAdmin]}: getPaperListToAdmin(String,String,int,int)
	{GET [/paper/getCategoriesAndNum]}: getCategoriesAndNum(Integer,Integer,String)
	{GET [/paper/getPaperList]}: getPaperList(Integer,Integer,String,String,String,Integer,Integer)
	{ [/paper/create]}: create(JSONObject)
	{GET [/paper/getPaperListAndTotalNum]}: getPaperListAndTotalNum(Integer,Integer,String,String,String,Integer,Integer)
[17:55:18:386] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperFavoriteController:
	{ [/paperFavorite/add]}: addFavorite(int,int)
	{ [/paperFavorite/cancel]}: cancelFavorite(int,int)
	{ [/paperFavorite/ifFavorite]}: ifFavorite(int,int)
	{ [/paperFavorite/getFavoriteList]}: getFavoriteList(int,int,int)
[17:55:18:386] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperPraiseController:
	{ [/paperPraise/add]}: addPraise(Integer,Integer)
	{ [/paperPraise/cancel]}: cancelPraise(Integer,Integer)
[17:55:18:386] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperQuestionController:
	{ [/paperQuestion/update]}: updateCompletion(BigDecimal,BigDecimal,int,BigDecimal,String,int)
	{ [/paperQuestion/updateSelection]}: updateSelection(BigDecimal,BigDecimal,int,BigDecimal,String,int)
	{GET [/paperQuestion/getQuestionWrapperListAndNum]}: getQuestionWrapperListAndNum(Integer,Integer,Integer)
[17:55:18:386] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperSummaryController:
	{GET [/paperSummary/getSummary]}: getSummary(Integer)
[17:55:18:387] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PassRaceGameController:
	{GET [/passRaceGame/getListAndTotalNum]}: getListAndTotalNum(Integer,Integer,Integer)
	{GET [/passRaceGame/getGameWrapper]}: getGameWrapper(Integer)
	{POST [/passRaceGame/update]}: update(JSONObject)
	{GET [/passRaceGame/delete]}: delete(Integer)
	{POST [/passRaceGame/save]}: save(JSONObject)
[17:55:18:388] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PassRaceResultController:
	{POST [/passRaceResult/save]}: save(JSONObject)
	{GET [/passRaceResult/getListAndTotalNum]}: getListAndTotalNum(Integer,Integer,Integer)
	{GET [/passRaceResult/getGameWrapper]}: getGameWrapper(Integer)
	{POST [/passRaceResult/update]}: update(JSONObject)
	{GET [/passRaceResult/delete]}: delete(Integer)
[17:55:18:390] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PassRaceStageController:
	{GET [/passRaceStage/getStageWrapper]}: getStageWrapper(Integer,Integer)
	{GET [/passRaceStage/getMyPlayedStageList]}: getMyPlayedStageList(Integer,Integer,Boolean)
[17:55:18:391] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PdfFileController:
	{[GET, POST] [/pdfFile/examinationReport]}: createExaminationPdfReport(HttpServletRequest,HttpServletResponse)
	{GET [/pdfFile/generatePdfByUser]}: generatePdfByUser(Integer,Integer)
	{ [/pdfFile/temporaryExaminationReport]}: createFreeExaminationReport(HttpServletRequest,HttpServletResponse)
[17:55:18:392] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PermissionAuthorizationController:
	{POST [/permissionAuthorization/getAuthorizedDepartmentList]}: getAuthorizedDepartmentList(JSONObject)
	{POST [/permissionAuthorization/getAuthorizedAdminList]}: getAuthorizedAdminList(JSONObject)
	{POST [/permissionAuthorization/getAuthorizedCompanyInfoWith2Grade]}: getAuthorizedCompanyInfoWithRecursionDepartment(JSONObject)
	{GET [/permissionAuthorization/getAuthorizedCompanyPracticeSummary]}: getAuthorizedCompanyPracticeSummary(Integer,Integer)
	{POST [/permissionAuthorization/getAuthorizedCompanyExamineePracticeInfo]}: getAuthorizedCompanyExamineePracticeInfo(JSONObject)
	{POST [/permissionAuthorization/changeBatch]}: changeBatch(JSONObject)
	{GET [/permissionAuthorization/getEntity]}: getEntity(Integer,Integer,Integer)
	{POST [/permissionAuthorization/save]}: save(List)
	{GET [/permissionAuthorization/getList]}: getList(Integer,Integer)
[17:55:18:393] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PermissionController:
	{GET [/permission/getList]}: getList()
[17:55:18:393] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PointConsumerationController:
	{POST [/pointConsumeration/getUserConsumeList]}: getUserConsumeList(JSONObject)
	{POST [/pointConsumeration/save]}: save(PointConsumeration)
[17:55:18:394] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PointDetailController:
	{POST [/pointDetail/getCompanyRankInfo]}: getCompanyRankInfo(JSONObject)
	{GET [/pointDetail/getRankInfo]}: getRankInfo(Integer,Integer,String,String,String,Integer,Integer)
	{GET [/pointDetail/exportPointRankOfCompany]}: exportPointRankOfCompany(HttpServletRequest,HttpServletResponse)
[17:55:18:394] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PointRuleController:
	{POST [/pointRule/upInsert]}: upInsert(PointRule)
	{GET [/pointRule/getEntity]}: getEntity(Integer)
[17:55:18:394] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PointUserController:
	{GET [/pointUser/getUserPointInfo]}: getUserPointInfo(Integer,Integer)
	{GET [/pointUser/clear]}: clear(Integer,Integer,Integer)
[17:55:18:394] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PracticePushController:
	{GET [/practicePush/toggleStatus]}: toggleStatus(Integer,Integer,Integer)
	{GET [/practicePush/detail]}: getDetail(Integer,Integer)
	{GET [/practicePush/checkAndExecutePushTasks]}: checkAndExecutePushTasks()
	{POST [/practicePush/update]}: update(JSONObject)
	{GET [/practicePush/delete]}: delete(Integer,Integer)
	{POST [/practicePush/create]}: create(JSONObject)
	{GET [/practicePush/list]}: getList(Integer,Integer,String,Integer,Integer,Integer)
[17:55:18:397] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductController:
	{ [/product/getProductList]}: getProductList(String)
	{GET [/product/getListOfProduct]}: getListOfProduct(String)
[17:55:18:397] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductCountTransactionController:
	{POST [/productCountTransaction/getList]}: getList(JSONObject)
[17:55:18:397] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductIntroductionController:
	{ [/productIntroduction/list]}: getProductIntroductionWrapperList()
[17:55:18:397] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductOrderRelationController:
	{POST [/productOrderRelation/updateProductOrderRelation]}: updateProductOrderRelation(ProductOrderRelation)
	{GET [/productOrderRelation/getProductOrderRelationWrapper]}: getProductOrderRelationWrapper(String,Integer)
	{GET [/productOrderRelation/checkIfOverdue]}: checkIfOverdue(String,Integer)
	{GET [/productOrderRelation/getProductOrderRelation]}: getProductOrderRelation(String,Integer)
	{GET [/productOrderRelation/refresh]}: refresh(Integer)
[17:55:18:401] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductOrderRelationCountController:
	{GET [/productOrderRelationCount/getProductOrderRelationCount]}: getProductOrderRelationCount(String,Integer,String)
	{POST [/productOrderRelationCount/getProductOrderRelationCountList]}: getProductOrderRelationCountList(JSONObject)
	{GET [/productOrderRelationCount/tryToConsumeVADVIP]}: tryToConsumeVADVIP(Integer,Integer)
	{GET [/productOrderRelationCount/consumeVADVIP]}: consumeVADVIP(Integer,Integer)
	{GET [/productOrderRelationCount/consume]}: consume(String,Integer,String,Integer)
[17:55:18:401] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QrcodeScanEntryController:
	{GET [/qrcodeScan/checkIfFollowed]}: checkIfFollowed(HttpServletRequest)
[17:55:18:402] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionBankQuestionController:
	{POST [/questionBankQuestion/changeQuestionBank]}: changeQuestionBank(JSONObject)
	{GET [/questionBankQuestion/deleteQuestionBankQuestion]}: deleteQuestionBankQuestion(Integer)
[17:55:18:402] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionCompositionChildController:
	{POST [/compositionChild/updateChildQuestionEntity]}: updateChildQuestionEntity(QuestionCompositionChild)
	{POST [/compositionChild/getPaperQuestionListMapByIds]}: getListByIds(JSONObject)
	{POST [/compositionChild/getListByIds]}: getListByIds(JSONArray)
	{GET [/compositionChild/getList]}: getList(Integer)
[17:55:18:419] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionController:
	{POST [/question/getQuestionsByGroupConditionAdvanced]}: getQuestionsByGroupConditionAdvanced(JSONObject)
	{GET [/question/getCategoryList]}: getCategoryList(Integer,String,Integer)
	{POST [/question/updateQuestion]}: updateQuestion(Question)
	{[GET, POST] [/question/modifyQuestion]}: modifyQuestion(HttpServletRequest)
	{POST [/question/deleteQuestionByQuery]}: deleteQuestion(JSONObject)
	{GET [/question/deleteQuestion]}: deleteQuestion(String)
	{GET [/question/deleteQuestionList]}: deleteQuestionList(String)
	{POST [/question/deleteBatchQuestion]}: deleteBatchQuestion(JSONObject)
	{GET [/question/deleteQuestionIncludingExample]}: deleteQuestionIncludingExample(int,int)
	{GET [/question/questionListOfCreaterByCategory]}: getQuestionListOfCreaterByCategory(String,String,int)
	{GET [/question/questionListOfCreaterByType]}: getQuestionListOfCreaterByType(String,String,int,int,int)
	{GET [/question/questionListOfCreaterByTypeIncludingExample]}: getQuestionListOfCreaterByTypeIncludingExample(String,String,int,int,int)
	{GET [/question/questionWrapperListOfCreaterByType]}: getQuestionWrapperListOfCreaterByType(String,String,String,String,int,int,int)
	{[GET, POST] [/question/questionWrapperListOfCreaterByTypeIncludingExample]}: getQuestionWrapperListOfCreaterByTypeIncludingExample(String,String,int,int,int)
	{GET [/question/questionListForPractice]}: getQuestionListOfCreaterForPractice(String,String,String,int,int,int)
	{GET [/question/question]}: getQuestionById(String)
	{GET [/question/questionWrapper]}: getQuestionWrapperById(String)
	{GET [/question/getQuestionWrapper]}: getQuestionWrapper(String)
	{POST [/question/importQuestionUnion], produces [application/json;charset=utf-8]}: importQuestionUnion(HttpServletRequest)
	{POST [/question/importWordQuestion], produces [application/json;charset=utf-8]}: importWordQuestion(HttpServletRequest)
	{[GET, POST] [/question/importExcelQuestion], produces [application/json;charset=utf-8]}: importExcelQuestion(HttpServletRequest)
	{GET [/question/modifyDefaultMark]}: modifyDefaultMarkOfQuestions(String,BigDecimal)
	{POST [/question/modifyQuestionCatagory]}: modifyCatagory(JSONObject)
	{GET [/question/modifyCatagory]}: modifyCatagory(String,String)
	{GET [/question/categoryAndTotalNum]}: getCategoryAndTotalNum(Integer,String,Integer)
	{POST [/question/getQuestionTypeAndCategoryAndTotalNum]}: getQuestionTypeAndCategoryAndTotalNum(JSONObject)
	{GET [/question/questionTypeAndCategoryAndTotalNum]}: getQuestionTypeAndCategoryAndTotalNum(Integer,Integer,Integer)
	{POST [/question/getQuestionTypeAndCategoryAndTotalNumWithPermission]}: getQuestionTypeAndCategoryAndTotalNumWithPermission(JSONObject)
	{GET [/question/getQuestionCatagoryAndTotalNum]}: getQuestionCatagoryAndTotalNum(Integer,int)
	{POST [/question/getQuestionCatagoryAndTotalNumWithPermission]}: getQuestionCatagoryAndTotalNumWithPermission(JSONObject)
	{GET [/question/machineChooseQuestion]}: getMachineChooseQuestion(int,String,int,int)
	{POST [/question/getQuestionsByGroupConditionAdvancedWithPermission]}: getQuestionsByGroupConditionAdvancedWithPermission(JSONObject)
	{GET [/question/getQuestionsByGroupCondition]}: getQuestionsAndLengthByGroupCondition(Integer,String,String,String,int,Integer,Integer)
	{GET [/question/getQuestionsList]}: getQuestionsList(Integer,String,String,String,int,Integer,Integer)
	{POST [/question/checkDuplicates]}: checkDuplicates(DuplicateCheckRequest)
	{[GET, POST] [/question/uploadFile]}: uploadFile(HttpServletRequest)
	{[GET, POST] [/question/uploadFileWithCheck]}: uploadFileWithCheck(HttpServletRequest)
	{POST [/question/questionListOfCreater]}: getQuestionListOfCreater(JSONObject)
	{GET [/question/exportQuestions]}: exportQuestions(HttpServletRequest,HttpServletResponse)
	{POST [/question/saveBatch]}: saveBatch(JSONObject)
	{[GET, POST] [/question/createQuestion]}: create(HttpServletRequest)
[17:55:18:433] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionFavoriteController:
	{ [/questionFavorite/add]}: addFavorite(int,int)
	{ [/questionFavorite/cancel]}: cancelFavorite(int,int)
	{ [/questionFavorite/ifFavorite]}: ifFavorite(int,int)
[17:55:18:436] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionRandomController:
	{GET [/questionRandom/deleteQuestionList]}: deleteQuestionList(String)
	{POST [/questionRandom/createByPost]}: createByPost(JSONObject)
	{GET [/questionRandom/list]}: getQuestionRandomListByCreaterIdInCompany(Integer,int,int,int)
	{GET [/questionRandom/questionRandomExtend]}: getQuestionRandomExtend(int)
	{GET [/questionRandom/delete]}: delete(int)
	{GET [/questionRandom/create]}: create(String,String)
	{POST [/questionRandom/modify]}: modify(JSONObject)
[17:55:18:436] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionSummaryController:
	{POST [/questionSummary/getWrongQuestionList]}: getWrongQuestionList(JSONObject)
	{POST [/questionSummary/removeWrongQuestion]}: removeWrongQuestion(JSONObject)
[17:55:18:436] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.SMSNoticeController:
	{GET [/notice/sendValidCodeSMS]}: sendValidCodeSMS(String,String)
	{GET [/notice/sendQKKRegisterNotice]}: sendQKKRegisterNotice(String,String)
	{GET [/notice/sendQKKTestOverdueNotice]}: sendQKKTemplateNotice(Integer)
	{GET [/notice/sendQKKTimeingNotice]}: sendQKKTimeingNotice(String)
[17:55:18:437] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.SoftVersionController:
	{GET [/version/reloadVersion]}: reloadVersion(String)
	{GET [/version/setSystemRunningState]}: setSystemRunningState(String)
	{GET [/version/setAdProvider]}: setAdProvider(String)
	{GET [/version/getSystemSettings]}: getSystemSettings(String)
	{GET [/version/setProductSystemRunningState]}: setProductSystemRunningState(String,String)
	{GET [/version/getProductSystemSettings]}: getProductSystemSettings(String)
	{GET [/version/getVersion]}: getVersion(String)
[17:55:18:438] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.SysUserRoleController:
	{GET [/sysUserRole/unbindExamineeUserRole]}: unbindExamineeUserRole(Integer,Integer)
	{POST [/sysUserRole/unbindExamineeUserRoleWithPermission]}: unbindExamineeUserRoleWithPermission(JSONObject)
	{GET [/sysUserRole/unbindAdminUserRole]}: unbindAdminUserRole(Integer,Integer)
	{POST [/sysUserRole/unbindAdminUserRoleWithPermission]}: unbindAdminUserRoleWithPermission(JSONObject)
	{POST [/sysUserRole/getSysUserRoleList]}: getSysUserRoleList(JSONObject)
	{GET [/sysUserRole/getSysUserRoleListByUsernameAndPassword]}: getSysUserRoleListByPhoneAndPassword(String,String,String,HttpServletRequest)
	{GET [/sysUserRole/getNewSysUserRoleList]}: getNewSysUserRoleList(String,String,String,HttpServletRequest)
	{GET [/sysUserRole/getSysUserRoleListOfUserInCompany]}: getSysUserRoleListOfUserInCompany(Integer,Integer,String)
	{GET [/sysUserRole/getCompanyInfoByAdminUserId]}: getCompanyInfoByAdminUserId(Integer)
	{GET [/sysUserRole/transmitAdminToOther]}: transmitAdminToOther(Integer,Integer,Integer)
	{POST [/sysUserRole/batchAddChildAdmin]}: batchAddChildAdmin(JSONObject)
	{GET [/sysUserRole/getSysUserRoleListOfUser]}: getSysUserRoleListOfUser(Integer,String,Integer)
	{GET [/sysUserRole/getSysUserRoleListOfCompany]}: getSysUserRoleListOfCompany(Integer,Integer)
	{POST [/sysUserRole/addSysUserRole]}: addSysUserRole(JSONObject)
[17:55:18:440] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.SystemMessageController:
	{GET [/systemMessage/handleSystemMessage]}: handleSystemMessage()
	{GET [/systemMessage/changeDepartmentId]}: changeDepartmentId(Integer)
	{GET [/systemMessage/batchDelete]}: batchDelete(String,String,String)
	{POST [/systemMessage/insert]}: insert(SystemMessage)
[17:55:18:441] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.TaxIdentityInfoController:
	{ [/taxIdentity/createTaxIdentityInfo]}: createTaxIdentityInfo(String)
	{ [/taxIdentity/modifyTaxIdentityInfo]}: modifyTaxIdentityInfo(String)
	{ [/taxIdentity/taxIdentityInfo]}: getTaxIdentityInfoByUserId(String,String)
[17:55:18:441] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.TaxSheetApplicationController:
	{ [/taxSheetApplication/createTaxSheetApplication]}: createTaxSheetApplication(String)
	{ [/taxSheetApplication/taxSheetApplicationList]}: getTaxSheetApplicationListOfUser(String)
	{ [/taxSheetApplication/taxSheetApplication]}: getTaxSheetApplicationById(String)
[17:55:18:441] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.TemplateController:
	{GET [/template/getMyTemplates]}: getMyTemplates(int,String)
[17:55:18:441] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.TestController:
	{GET [/test/api]}: apiAccess()
	{GET [/test/getKeysByPrefix]}: getKeysByPrefix(String)
	{GET [/test/deleteByPrex]}: deleteByPrex(String)
[17:55:18:442] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UniversalOrderController:
	{ [/univsersalOrder/create]}: createOrder(JSONObject)
	{POST [/univsersalOrder/createOrderByWeChatNative]}: createOrderByWeChatNative(JSONObject)
	{POST [/univsersalOrder/createCountOrderByJSAPI]}: createCountOrderByJSAPI(JSONObject)
	{POST [/univsersalOrder/orderNotify]}: orderNotify(JSONObject)
	{GET [/univsersalOrder/getOrderById]}: getOrderById(Integer)
	{GET [/univsersalOrder/getList]}: getList(String,String,Integer,Integer,Integer)
[17:55:18:442] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UniversalProductOrderRelationController:
	{POST [/universalProductOrderRelation/getProductOrderRelation]}: getProductOrderRelation(JSONObject)
[17:55:18:444] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserCompanyController:
	{POST [/userCompany/updateUserInfoAndCompanyInfo]}: saveUserInfoAndCompanyInfo(JSONObject)
	{GET [/userCompany/saveUserInfoAndCompanyInfo]}: saveUserInfoAndCompanyInfo(int,int,int,String,String)
	{POST [/userCompany/bindCompany]}: bindCompany(JSONObject)
	{POST [/userCompany/deleteUserOfCompanyWithPermission]}: deleteUserOfCompanyWithPermission(JSONObject)
	{POST [/userCompany/deleteBatch]}: deleteBatchWithPermission(JSONObject)
	{GET [/userCompany/deleteExamineeByDepartmentName]}: deleteExamineeByDepartmentName(Integer,String)
	{POST [/userCompany/deleteExamineeByDepartmentNameWithPermission]}: deleteExamineeByDepartmentNameWithPermission(JSONObject)
	{GET [/userCompany/getUserListByQkkQiyeCorpid]}: getUserListByQkkQiyeCorpid(String,String)
	{GET [/userCompany/getCompanyInfoByUserIdAndCompanyId]}: getUserCompanyInfoByUserIdAndCompanyId(int,int)
	{GET [/userCompany/getUserCompanyByPhone]}: getUserCompanyByPhone(String)
	{GET [/userCompany/getUserInfoWithCompanyInfo]}: getUserInfoWithCompanyInfo(Integer,Integer)
	{GET [/userCompany/getUserListByDepartmentId]}: getUserListByDepartmentId(Integer,Integer,Integer,Integer,Integer,Integer)
	{POST [/userCompany/getCompanyUserListByMap]}: getCompanyUserListByMap(JSONObject)
	{GET [/userCompany/getUserListOfDepartmentsInCludingSelf]}: getUserListOfDepartmentsInCludingSelf(String)
	{GET [/userCompany/getUserListOfDepartmentsExcludingSelf]}: getUserListOfDepartmentsExcludingSelf(String)
	{GET [/userCompany/getUserListByName]}: getUserListByName(String,Integer,Integer,Integer,Integer)
	{POST [/userCompany/getUserList]}: getUserListByName(JSONObject)
	{GET [/userCompany/ifRegisted]}: getIfRegisted(int,int)
	{GET [/userCompany/getUserListOfCompany]}: getUserListOfCompany(Integer,String,String,Integer,Integer)
	{GET [/userCompany/secureGetUserListOfCompany]}: secureGetUserListOfCompany(Integer,String,String,Integer,Integer,Integer,Integer,Integer,Integer)
	{GET [/userCompany/getGrantedUserListOfCompany]}: getGrantedUserListOfCompany(Integer,String,String,Integer,Integer,Integer,Integer,String,Integer,Integer,String,String)
	{POST [/userCompany/getCompanyUserList]}: getCompanyUserList(JSONObject)
	{GET [/userCompany/getExamineeStatisticsOfCompany]}: getExamineeStatisticsOfCompany(Integer)
	{GET [/userCompany/exportUserListOfCompany]}: exportUserListOfCompany(Integer,Integer,HttpServletResponse)
	{GET [/userCompany/checkIfExceedMemberNumLimit]}: checkIfExceedMemberNumLimit(Integer,Integer)
	{POST [/userCompany/approvedRegist]}: approvedRegist(UserCompany)
	{POST [/userCompany/importUser]}: importUser(HttpServletRequest)
	{GET [/userCompany/getCompanyInfoByUserId]}: getCompanyInfoByUserId(int)
	{GET [/userCompany/deleteUserOfCompany]}: deleteUserOfCompany(int,int)
	{POST [/userCompany/getUserInfoListByIds]}: getUserInfoListByIds(JSONObject)
[17:55:18:448] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserController:
	{GET [/user/getUserListByQkkQiyeCorpid]}: getUserListByQkkQiyeCorpid(String,String)
	{GET [/user/getUserInfoWithCompanyInfo]}: getUserInfoWithCompanyInfo(Integer,Integer)
	{GET [/user/updateUserInfo]}: updateUserInfo(String)
	{GET [/user/getUserByPhone]}: getUserByPhone(String)
	{POST [/user/getUserInfoListByIds]}: getUserInfoListByIds(List)
	{GET [/user/getUserInfoByOpenId]}: getUserByEteaOpenId(String,String)
	{GET [/user/getUserInfoById]}: getUserInfoById(int)
	{GET [/user/getUserInfoListByNickName]}: getUserInfoListByNickName(String)
	{POST [/user/supplyPhoneNo]}: supplyPhoneNo(JSONObject)
	{GET [/user/loginByValidationCode]}: loginByValidationCode(String,String,HttpServletRequest)
	{GET [/user/validateSMSPhoneIfMatched]}: validateSMSPhoneIfMatched(String,String)
	{GET [/user/oneKeyLogin]}: oneKeyLogin(String,HttpServletRequest)
	{GET [/user/login]}: loginByPhoneAndPassword(String,String,HttpServletRequest)
	{GET [/user/loginById]}: loginById(Integer)
	{POST [/user/getUserInfoByUserIdAndCompanyId]}: getUserInfoByUserIdAndCompanyId(JSONObject,HttpServletRequest)
	{POST [/user/loginWithAutoRegist]}: loginWithAutoRegist(JSONObject,HttpServletRequest)
	{GET [/user/changePassword]}: changePassword(Integer,String,String)
	{POST [/user/register]}: register(JSONObject)
	{POST [/user/update]}: update(User)
	{POST [/user/getPassword]}: getPassword(JSONObject)
[17:55:18:457] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserDeviceBindingController:
	{POST [/userDeviceBinding/getUserDeviceBinding]}: getUserDeviceBinding(JSONObject)
	{POST [/userDeviceBinding/bindDevice]}: bindDevice(JSONObject)
	{POST [/userDeviceBinding/unbindDevice]}: unbindDevice(JSONObject)
[17:55:18:458] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserExaminationPerformanceReportController:
	{ [/examinationPerformanceReport/consume]}: consume(Integer,Integer)
[17:55:18:459] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserFormController:
	{GET [/userForm/deleteForm]}: deleteFormOfUser(Integer,Integer)
	{GET [/userForm/getUserFormListByUserId]}: getUserFormListByUserId(Integer)
	{POST [/userForm/create]}: create(UserForm)
[17:55:18:464] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserFormFlowRecordController:
	{POST [/userFormFlowRecord/upsert]}: upsert(UserFormFlowRecord)
	{GET [/userFormFlowRecord/getFormFlowRecordWrapperById]}: getFormFlowRecordWrapper(Integer,Integer)
[17:55:18:465] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserOfYncxController:
	{[GET, POST] [/userOfYncx/examinationReport]}: createExaminationPdfReport(HttpServletRequest,HttpServletResponse)
	{GET [/userOfYncx/exportExaminationInstanceDetailExcelBundle]}: exportExaminationInstanceDetailExcelBundle(Integer,Integer)
	{GET [/userOfYncx/exportExaminationInstancePdfBundle]}: exportExaminationInstancePdfBundle(Integer,Integer)
	{ [/userOfYncx/exportDetailRankListOfExamination]}: exportDetailRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/userOfYncx/exportRankListOfExamination]}: exportRankListOfExamination(HttpServletRequest,HttpServletResponse)
[17:55:18:467] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserStatisticController:
	{ [/userStatistic/totalItemNum]}: totalNumber(Integer,Integer)
	{ [/userStatistic/getOnlineUserNumberOfExamination]}: getOnlineUserNumberOfExamination(int,int)
[17:55:18:468] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserSummaryController:
	{POST [/userSummary/upInsert]}: upInsert(JSONObject)
	{POST [/userSummary/getEntity]}: getEntity(UserSummary)
[17:55:18:468] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserViewPerformanceWithoutAdController:
	{GET [/userViewPerformanceWithoutAd/insertIfNotExisted]}: insertIfNotExisted(Integer,Integer,Integer,String)
	{GET [/userViewPerformanceWithoutAd/adEndedCallback]}: adEndedCallback(String,String)
	{POST [/userViewPerformanceWithoutAd/deletePermently]}: deletePermently(JSONObject)
	{ [/userViewPerformanceWithoutAd/checkIfAllowUserViewPerformanceWithoutAd]}: checkIfAllowUserViewPerformanceWithoutAd(int,int,int)
	{ [/userViewPerformanceWithoutAd/getUserViewPerformanceWithoutAdTransaction]}: getUserViewPerformanceWithoutAdTransaction(String,int,String,Integer,Integer)
[17:55:18:473] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WeChatMiniProgramController:
	{POST [/weixinAccount/getProductList]}: getProductList(JSONObject)
	{POST [/weixinAccount/getGroupId]}: getGroupId(JSONObject)
	{GET [/weixinAccount/session]}: getSession(String,String)
	{POST [/weixinAccount/getQRCode]}: getQRCode(JSONObject)
	{POST [/weixinAccount/imgSecCheck]}: imgSecCheck(HttpServletRequest)
	{POST [/weixinAccount/imgSecCheckByMultipart], consumes [multipart/form-data]}: imgSecCheck(MultipartFile,String)
	{GET [/weixinAccount/getAccessToken]}: getAccessTokenOfAccount(String)
	{POST [/weixinAccount/registWithCode]}: registWithCode(JSONObject)
	{GET [/weixinAccount/autoLogin]}: autoLogin(String,String,HttpServletRequest)
	{GET [/weixinAccount/loginWithoutUserInfoAndAutoRegist]}: loginWithoutUserInfoAndAutoRegist(String,String,HttpServletRequest)
	{GET [/weixinAccount/login]}: loginWithAutoRegister(String,String,HttpServletRequest)
	{POST [/weixinAccount/loginWithUserAuthNoRegistByPost]}: loginWithUserAuthWithoutAutoRegistByPost(JSONObject,HttpServletRequest)
	{POST [/weixinAccount/loginWithAuth]}: loginWithAuth(JSONObject,HttpServletRequest)
	{POST [/weixinAccount/getUserInfoByWeixinCode]}: getUserInfoByWeixinCode(JSONObject)
	{POST [/weixinAccount/loginWithUserAuthByPost]}: loginWithUserAuthWithAutoRegistByPost(JSONObject,HttpServletRequest)
	{GET [/weixinAccount/loginWithUserAuth]}: loginWithUserAuthWithAutoRegistByGet(String,String,String,String,String,HttpServletRequest)
	{POST [/weixinAccount/getQRCodeBase64ByLimit]}: getQRCodeBase64ByLimit(JSONObject)
	{POST [/weixinAccount/getQRCodeBase64]}: getQRCodeBase64(JSONObject)
	{POST [/weixinAccount/getPhoneNumByPost]}: getPhoneNumByPost(JSONObject)
	{POST [/weixinAccount/getPhoneNum]}: getPhoneNum(JSONObject)
	{POST [/weixinAccount/getPhoneNumNew]}: getPhoneNumNew(JSONObject)
	{POST [/weixinAccount/getPhoneNumWithoutGetSession]}: getPhoneNumWithoutGetSession(JSONObject)
	{POST [/weixinAccount/searchProduct]}: searchProduct(JSONObject)
	{GET [/weixinAccount/getCouponList]}: getCoupon(String)
	{GET [/weixinAccount/receivedCouple]}: receivedCouple(String,String,String)
	{GET [/weixinAccount/getUserCoupleList]}: getUserCoupleList(String,String,String)
	{POST [/weixinAccount/msgSecCheck]}: msgSecCheck(JSONObject)
	{POST [/weixinAccount/bind]}: bind(JSONObject)
[17:55:18:490] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WeChatOfficialAccountController:
	{GET [/wx/receiveMessage]}: doGet(HttpServletRequest)
	{POST [/wx/receiveMessage], produces [application/xml;charset=UTF-8]}: processRequest(HttpServletRequest)
	{GET [/wx/checkIfFollowed]}: getQrcodeScanEntry(HttpServletRequest)
	{GET [/wx/loginWithAutoRegister]}: loginWithAutoRegister(String,String)
	{GET [/wx/getJSSDKConfiguration]}: getJSSDKConfiguration(String,String)
	{GET [/wx/getTicket]}: getTicket(HttpServletRequest)
	{GET [/wx/loginByWeChatOfficialAccount]}: loginByWeChatOfficialAccount(String,String)
	{GET [/wx/getPageAuthAccessToken]}: getPageAuthAccessToken(String,String)
	{GET [/wx/getTicketOfProduct]}: getTicketOfProduct(HttpServletRequest)
	{GET [/wx/createAccountMenu]}: createAccountMenu(String)
[17:55:18:496] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WordFileController:
	{GET [/wordFile/exportExercisePaper]}: exportExercisePaper(HttpServletRequest,HttpServletResponse)
	{GET [/wordFile/exportExamPaper]}: exportExamPaper(HttpServletRequest,HttpServletResponse)
	{GET [/wordFile/exportWrongQuestions]}: exportWrongQuestions(HttpServletRequest,HttpServletResponse)
[17:55:18:497] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WrongBookController:
	{POST [/wrongBook/update]}: updateIgnoreNull(JSONObject)
	{POST [/wrongBook/create]}: createWrongBook(JSONObject)
	{GET [/wrongBook/deletePermanently]}: deletePermanently(Integer)
	{POST [/wrongBook/getWrongBookQuestionList]}: getWrongBookQuestionList(JSONObject)
	{POST [/wrongBook/removeQuestion]}: removeQuestion(JSONObject)
	{GET [/wrongBook/list]}: getWrongBookList(Integer)
[17:55:18:497] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WrongQuestionBookController:
	{POST [/wrongQuestionBook/doReview]}: doReview(JSONObject)
	{ [/wrongQuestionBook/wrongQuestionBookPageInfo]}: getWrongQuestionBookPageInfoOfUser(int,int)
	{ [/wrongQuestionBook/getWrongQuestionBookOfUser]}: getWrongQuestionBookOfUser(Integer)
	{POST [/wrongQuestionBook/batchSaveWrongQuestionBook]}: batchSaveWrongQuestionBook(JSONObject)
	{GET [/wrongQuestionBook/delete]}: delete(Integer)
[17:55:18:498] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WrongQuestionBookQuestionController:
	{ [/wrongQuestionBookQuestion/markAsReviewed]}: markAsReviewed(int,int)
	{ [/wrongQuestionBookQuestion/getQuestionWrapperListOfWrongQuestionBook]}: getQuestionWrapperListOfWrongQuestionBook(int)
	{ [/wrongQuestionBookQuestion/getQuestionWrapperListOfBook]}: getQuestionWrapperListOfBook(Integer)
	{GET [/wrongQuestionBookQuestion/getTotalWrongQuestionWrapperList]}: getTotalWrongQuestionWrapperList(Integer,Integer)
	{GET [/wrongQuestionBookQuestion/deleteByQuery]}: deleteByQuery(Integer,Integer)
[17:55:18:498] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WrongQuestionBookReviewSummaryController:
	{POST [/wrongQuestionBookReviewSummary/saveSummary]}: saveSummary(JSONObject)
[17:55:18:498] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WxMiniprogramSubscribeMessageOfEteaController:
	{GET [/wxMiniprogramSubscribeMessageOfEtea/batchSendSubscribedMessage]}: batchSendSubscribedMessage(Integer,Integer,Integer)
	{POST [/wxMiniprogramSubscribeMessageOfEtea/subscribe]}: subscribe(JSONObject)
[17:55:18:498] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.c.p.ExaminationControllerOfPC:
	{GET [/examination/examinationInViewListOfCreaterFromPC]}: getExaminationInViewListOfCreaterFromPC(Integer,Integer,Integer,Integer)
	{POST [/examination/getExaminationInViewListAndNum]}: getExaminationInViewListAndNum(JSONObject)
	{POST [/examination/getExaminationInViewListOfCreaterByTagsFromPC]}: getExaminationInViewListOfCreaterByTagsFromPC(JSONObject)
	{POST [/examination/getExaminationInViewListExcludingCreaterByTagsFromPC]}: getExaminationInViewListExcludingCreaterByTagsFromPC(JSONObject)
	{GET [/examination/getExaminationListAndSizeOfWillMark]}: getExaminationListAndSizeOfWillMark(Integer,Integer,Integer,Integer)
	{POST [/examination/getWillMarkExaminationListAndSize]}: getWillMarkExaminationListAndSize(JSONObject)
	{POST [/examination/getExaminationInViewListOfCreaterByTags]}: getExaminationInViewListOfCreaterByTags(JSONObject)
[17:55:18:499] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.c.p.ExaminationInstanceControllerOfPC:
	{GET [/examinationInstance/examinationInstanceStageSummaryListFromPC]}: getExaminationInstanceStageSummaryListFromPC(String,String,String,int,int,int)
	{GET [/examinationInstance/getExaminationInstanceStageSummaryListAndTotalNumFromPC]}: getExaminationInstanceStageSummaryListAndTotalNumFromPC(String,String,String,int,int,int)
	{POST [/examinationInstance/getExaminationInstanceStageSummaryListAndNum]}: getExaminationInstanceStageSummaryListAndNum(JSONObject)
	{POST [/examinationInstance/getExaminationInstanceStageSummaryListAndLengthByTags]}: getExaminationInstanceStageSummaryListByTagsFromPC(JSONObject)
	{POST [/examinationInstance/getListAndSizeOfExaminationInstanceWithUserInfoByConditions]}: getListAndSizeOfExaminationInstanceWithUserInfoByConditions(JSONObject)
	{GET [/examinationInstance/getExaminationInstanceListAndSizeWithUserInfoByExaminationId]}: getExaminationInstanceListWithUserInfoByExaminationId(Integer,Integer,Integer,String,Integer,Integer)
[17:55:18:499] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.c.p.PcPageDataController:
	{GET [/pageData/getCompanyCoreSummary]}: getCompanyCoreSummary(Integer,Integer)
[17:55:18:500] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.c.PageDataControllerOfCooperation:
	{GET [/pageDateControllerOfCooperation/getRankData]}: getRankData(Integer,Integer,Integer,Integer,String,String,Integer,Integer)
[17:55:18:500] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.e.ExaminationInstanceControllerOfEtea:
	{GET [/examinationInstanceControllerOfEtea/exportTotalScoreGroupByUser]}: exportTotalScoreGroupByUser(String,HttpServletResponse)
	{GET [/examinationInstanceControllerOfEtea/getUserTakenExaminations]}: getUserTakenExaminations(Integer,Integer,Integer)
	{GET [/examinationInstanceControllerOfEtea/getRecentOnGoingExaminations]}: getRecentOnGoingExaminations(Integer)
[17:55:18:500] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.e.PageDataControllerOfEtea:
	{POST [/pageDataOfEtea/getTotalScoreGroupByUser]}: getTotalScoreGroupByUser(JSONObject)
	{GET [/pageDataOfEtea/getHomePageSummaryInfo]}: getHomePageSummaryInfo(Integer)
[17:55:18:501] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.f.FormController:
	{POST [/formSystem/saveFormFlowRecord]}: saveFormFlowRecord(JSONObject)
	{POST [/formSystem/createForm]}: createForm(JSONObject)
	{GET [/formSystem/getFormWrapperById]}: getFormWrapperById(Integer)
	{POST [/formSystem/updateForm]}: updateForm(JSONObject)
[17:55:18:501] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteContentAccessController:
	{POST [/ksiteContentAccess/getContentAccessList]}: getContentAccessList(JSONObject)
	{POST [/ksiteContentAccess/add]}: add(KsiteContentAccess)
[17:55:18:501] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteFavoriteController:
	{POST [/ksiteFavorite/deleteByColumns]}: deleteByColumns(KsiteFavorite)
	{GET [/ksiteFavorite/getFavorite]}: getFavorite(Integer,String,Integer)
	{POST [/ksiteFavorite/getFavoriteList]}: getFavoriteList(JSONObject)
	{POST [/ksiteFavorite/add]}: add(KsiteFavorite)
[17:55:18:502] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteOrderController:
	{ [/ksiteOrder/orderNotify]}: orderNotify(JSONObject)
	{ [/ksiteOrder/createOrder]}: createKsiteOrder(JSONObject)
	{POST [/ksiteOrder/getOrderListWithKsiteInfo]}: getOrderListWithKsiteInfo(JSONObject)
	{POST [/ksiteOrder/getOrderList]}: getOrderListAndNum(JSONObject)
	{GET [/ksiteOrder/getAmountOfIncome]}: getAmountOfIncome(Integer)
	{GET [/ksiteOrder/getSettlementDashboardData]}: getSettlementDashboardData(Integer)
	{POST [/ksiteOrder/getOrderNum]}: getOrderNum(JSONObject)
	{POST [/ksiteOrder/update]}: update(JSONObject)
[17:55:18:502] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteOrderRelationController:
	{POST [/ksiteOrderRelation/checkContentAuth]}: checkContentAuth(KsiteOrderRelation)
[17:55:18:502] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsitePaperInstanceController:
	{GET [/ksitePaperInstance/getPaperInstanceStageSummaryDetail]}: getPaperInstanceStageSummaryDetail(Integer,String,String,Integer,Integer)
	{POST [/ksitePaperInstance/create]}: create(JSONObject)
[17:55:18:502] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsitePaperInstanceProcessController:
	{GET [/ksitePaperInstanceProcess/getPaperInstanceProcessWrapperListAndNum]}: getPaperInstanceProcessWrapperListAndNum(Integer,Boolean,Integer,Integer)
[17:55:18:503] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteUserSignatureController:
	{POST [/ksiteUserSignature/signKsite]}: sign(KsiteUserSignatureWrapper)
	{GET [/ksiteUserSignature/getUserSignature]}: getUserSignature(Integer,Integer)
	{GET [/ksiteUserSignature/checkKsiteNameExisted]}: checkKsiteNameExisted(String)
	{GET [/ksiteUserSignature/getKsiteInfo]}: getKsiteInfo(String,Integer)
	{POST [/ksiteUserSignature/update]}: update(KsiteUserSignatureWrapper)
	{POST [/ksiteUserSignature/getList]}: getList(JSONArray)
[17:55:18:503] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteWithdrawController:
	{POST [/ksiteWithdraw/withdraw]}: withdraw(KsiteWithdraw)
[17:55:18:503] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.m.c.CategoryController:
	{POST [/category/getBatchPathOfCategoryList]}: getBatchPathOfCategoryList(JSONArray)
	{GET [/category/getNLevelCategory]}: getLevelCategoriesOfLessThanOrEqualDesignativeLevel(String,Integer,Integer)
	{POST [/category/addNewCategory]}: addNewCategory(JSONObject)
	{GET [/category/getPathOfCategory]}: getPathOfCategory(Integer)
	{POST [/category/updateCategory]}: updateCategory(JSONObject)
	{POST [/category/updateCategoryList]}: updateCategoryList(JSONArray)
	{GET [/category/copySpecifiedSpaceName]}: copySpecifiedSpaceName(String,Integer,String)
	{POST [/category/add]}: add(JSONObject)
	{POST [/category/update]}: update(JSONObject)
[17:55:18:504] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QiyeweixinAccountController:
	{GET [/qiyeweixinAccount/transferLicense]}: transferLicense(String,List)
	{GET [/qiyeweixinAccount/getMemberActiveInfo]}: getMemberActiveInfo(String,String)
	{GET [/qiyeweixinAccount/queryAutoActiveStatus]}: queryAutoActiveStatus(String)
	{GET [/qiyeweixinAccount/getActivatedAccountList]}: getActivatedAccountList(String,Integer,String)
[17:55:18:505] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QiyeweixinCommunicationController:
	{ [/qiyeweixinCommunication/receiveData]}: receiveData(String,String,String,String,HttpServletRequest)
	{ [/qiyeweixinCommunication/receiveCommand]}: receiveCommand(String,String,String,String,HttpServletRequest)
	{GET [/qiyeweixinCommunication/getPreAuthCode]}: getPreAuthCode(String)
	{GET [/qiyeweixinCommunication/setSessionInfo]}: setSessionInfo(String)
	{GET [/qiyeweixinCommunication/generatePermanentCode]}: generatePermanentCode(String,String)
	{GET [/qiyeweixinCommunication/getAppQrcode]}: getAppQrcode(String)
	{GET [/qiyeweixinCommunication/getAppPermission]}: getAppPermission(String,String)
	{GET [/qiyeweixinCommunication/getAppAdmin]}: getAppAdmin(String,String)
	{GET [/qiyeweixinCommunication/getJsapiTicket]}: getJsapiTicket(String,String,String)
[17:55:18:505] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QiyeweixinSignatureController:
	{POST [/qiyeweixinSignature/getSignature]}: getSignature(JSONObject)
[17:55:18:506] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QiyeweixinUserController:
	{GET [/qiyeweixinUser/login]}: login(HttpServletRequest)
	{GET [/qiyeweixinUser/loginQkkQiyeweixin]}: wechatLogin()
	{GET [/qiyeweixinUser/loginQkkQiyeweixinFromPCWeb]}: wechatLoginFromPCWeb(String)
	{GET [/qiyeweixinUser/syncAllDepartmentNameAndUserNameByOCR]}: syncAllDepartmentNameAndUserNameByOCR(Integer)
	{GET [/qiyeweixinUser/refreshDepartmentNameAndUserNameByOCR]}: refreshDepartmentNameAndUserNameByOCR(Integer)
	{GET [/qiyeweixinUser/getUserInfo]}: getUserInfo(HttpServletRequest)
[17:55:18:506] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.CompanyControllerOfQKK:
	
[17:55:18:506] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.CompanyRegisterFormController:
	{GET [/companyRegisterForm/getCompanyRegisterForm]}: getCompanyRegisterForm(Integer)
	{GET [/companyRegisterForm/getCompanyRegisterFormFieldStructure]}: getCompanyRegisterFormFieldStructure(Integer)
	{POST [/companyRegisterForm/insertIfNotExisted]}: insertIfNotExisted(CompanyRegisterForm)
[17:55:18:506] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.CompanyRegisterFormFlowRecordController:
	{GET [/companyRegisterFormFlowRecord/getRegistrationDetail]}: getRegistrationDetail(Integer,Integer)
[17:55:18:506] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.ExaminationControllerOfQkk:
	{POST [/examination/getExaminationInViewListOfCompanyByTagesAndUserCompany]}: getExaminationInViewListOfCompanyByTagesAndUserCompany(JSONObject)
	{POST [/examination/getExaminationInViewListAndTotalSizeOfCompanyByTagesAndUserCompany]}: getExaminationInViewListAndTotalSizeOfCompanyByTagesAndUserCompany(JSONObject)
	{GET [/examination/getExamList]}: getExamList(Integer)
	{GET [/examination/checkIfAuthorized]}: checkIfAuthorized(Integer,Integer,Integer)
[17:55:18:507] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.ExaminationInstanceControllerOfQkk:
	{GET [/examinationInstanceOfQkk/exportTotalScoreGroupByUser]}: exportTotalScoreGroupByUser(String,HttpServletResponse)
	{GET [/examinationInstanceOfQkk/getExaminationInstanceListWithUserInfoByExaminationId]}: getExaminationInstanceListWithUserInfoByExaminationId(Integer,Integer,Integer,String,Integer,Integer)
	{GET [/examinationInstanceOfQkk/examinationInstanceStageSummaryDetail]}: getExaminationInstanceStageSummaryDetail(Integer,Integer,Integer,String,String,Integer,Integer)
	{POST [/examinationInstanceOfQkk/getTotalScoreOfExaminationListGroupByUser]}: getTotalScoreOfExaminationListGroupByUser(JSONObject)
	{POST [/examinationInstanceOfQkk/getSummaryGroupByDepartment]}: getSummaryGroupByDepartment(JSONObject)
	{GET [/examinationInstanceOfQkk/exportSummaryGroupByDepartment]}: exportSummaryGroupByDepartment(Integer,Integer,Integer,HttpServletResponse)
	{POST [/examinationInstanceOfQkk/getTopNDepartmentDistribution]}: getTopNDepartmentDistribution(JSONObject)
[17:55:18:507] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.PageDataController:
	{POST [/pageData/getTotalScoreGroupByUserInCompany]}: getTotalScoreGroupByUserInCompany(JSONObject)
[17:55:18:507] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QuestionControllerOfQkk:
	{POST [/question/questionListExcludingCreaterInCompany]}: getQuestionListExcludingCreaterInCompany(JSONObject)
[17:55:18:508] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.UserControllerOfQkk:
	{POST [/user/secureAdminRegister]}: secureAdminRegister(HttpServletRequest)
	{POST [/user/registerAdminFromMp]}: registerAdminFromMp(JSONObject,HttpServletRequest)
	{POST [/user/registerAdminFromH5]}: registerAdminFromH5(JSONObject,HttpServletRequest)
	{POST [/user/secureExamineeRegister]}: secureExamineeRegister(HttpServletRequest,HttpServletResponse)
	{POST [/user/registerExamineeFromMp]}: registerExamineeFromMp(JSONObject,HttpServletRequest)
	{POST [/user/registerExamineeFromH5]}: registerExamineeFromH5(JSONObject,HttpServletRequest)
	{POST [/user/secureLogin]}: secureLogin(HttpServletRequest,HttpServletResponse)
	{GET [/user/mergeCompanyUser]}: mergeCompanyUser(Integer)
[17:55:18:508] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoMiniProgramController:
	{POST [/toutiaoMiniProgram/regist]}: regist(JSONObject)
	{POST [/toutiaoMiniProgram/autoLogin]}: autologin(JSONObject)
	{POST [/toutiaoMiniProgram/update]}: update(JSONObject)
[17:55:18:508] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoQuestionStarController:
	{GET [/touTiaoQuestionStar/getQuestionStarWrapper]}: getQuestionStarWrapper(Integer)
[17:55:18:508] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoQuestionStarStageEventController:
	{GET [/toutiaoQuestionStarStageEvent/getMyPlayedStageList]}: getMyPlayedStageList(Integer,Integer)
	{POST [/toutiaoQuestionStarStageEvent/insertIfNotExisted]}: insertIfNotExisted(ToutiaoQuestionStarStageEvent)
[17:55:18:508] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoQuestionStarUserController:
	{GET [/toutiaoQuestionStarUser/getUserInfo]}: getUserInfo(Integer)
[17:55:18:509] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoQuestionStarUserProfileController:
	{POST [/toutiaoQuestionStarUserProfile/markPass]}: markPass(JSONObject)
	{POST [/toutiaoQuestionStarUserProfile/increaseExp]}: increaseExp(JSONObject)
	{POST [/toutiaoQuestionStarUserProfile/getRankInfoList]}: getRankInfoList(JSONObject)
	{GET [/toutiaoQuestionStarUserProfile/getUserInfo]}: getUserInfo(Integer)
[17:55:18:510] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.x.UserSchoolController:
	{POST [/userSchool/bindSchool]}: bindCompany(JSONObject)
[17:55:18:515] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.o.c.OssFileController:
	{POST [/ossFile/update]}: update(OssFile)
[17:55:18:535] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
	{ [/error]}: error(HttpServletRequest)
[17:55:18:602] [DEBUG] - org.springframework.web.servlet.handler.AbstractDetectingUrlHandlerMapping.detectHandlers(AbstractDetectingUrlHandlerMapping.java:86) - 'beanNameHandlerMapping' {}
[17:55:19:017] [DEBUG] - org.springframework.web.servlet.handler.SimpleUrlHandlerMapping.logMappings(SimpleUrlHandlerMapping.java:177) - 'webSocketHandlerMapping' {/wss/pkGameSocket=org.springframework.web.socket.server.support.WebSocketHttpRequestHandler@1779d23f}
[17:55:19:446] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$MapperScannerRegistrarNotFoundConfiguration.afterPropertiesSet(MybatisAutoConfiguration.java:305) - No org.mybatis.spring.mapper.MapperFactoryBean found.
[17:55:20:659] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[17:55:21:360] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[17:55:21:417] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 49 ms to scan 1 urls, producing 3 keys and 6 values 
[17:55:21:443] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype com.alibaba.nacos.api.remote.request.Request -> com.alibaba.nacos.api.remote.request.ServerRequest
[17:55:21:459] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[17:55:21:485] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 23 ms to scan 1 urls, producing 4 keys and 9 values 
[17:55:21:489] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype com.alibaba.nacos.api.remote.request.Request -> com.alibaba.nacos.api.remote.request.ServerRequest
[17:55:21:497] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[17:55:21:531] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 34 ms to scan 1 urls, producing 3 keys and 10 values 
[17:55:21:536] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
file:/Users/<USER>/Documents/git/taurus-cloud/exam-main-service/target/classes/
file:/private/var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T/cp_bglw5z4cph95pkrips04x9cfi.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/dnsns.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jaccess.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jfxrt.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunec.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/zipfs.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/nashorn.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/cldrdata.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/localedata.jar
[17:55:21:575] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 38 ms to scan 12 urls, producing 0 keys and 0 values 
[17:55:21:578] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[17:55:21:592] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 14 ms to scan 1 urls, producing 1 keys and 5 values 
[17:55:21:595] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[17:55:21:606] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
[17:55:21:612] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[17:55:21:627] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
[17:55:21:627] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype java.lang.Comparable -> java.lang.Enum
[17:55:21:627] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype java.io.Serializable -> java.lang.Enum
[17:55:21:631] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
file:/Users/<USER>/Documents/git/taurus-cloud/exam-main-service/target/classes/
file:/private/var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T/cp_bglw5z4cph95pkrips04x9cfi.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/dnsns.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jaccess.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jfxrt.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunec.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/zipfs.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/nashorn.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/cldrdata.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/localedata.jar
[17:55:21:662] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 30 ms to scan 12 urls, producing 0 keys and 0 values 
[17:55:22:904] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[17:55:22:905] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[17:55:22:905] [DEBUG] - com.taurus.examinationassistant.filter.AdminAuthFilter.init(AdminAuthFilter.java:45) - AdminAuthFilter初始化
[17:55:22:925] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[17:55:22:952] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[17:55:22:971] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[17:55:22:998] [DEBUG] - org.xnio.XnioWorker$Builder.build(XnioWorker.java:1191) - Creating worker:null, pool size:64, max pool size:64, keep alive:60000, io threads:8, stack size:0
[17:55:23:016] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[17:55:23:032] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-4', selector sun.nio.ch.KQueueSelectorImpl@209b4e95
[17:55:23:031] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-2', selector sun.nio.ch.KQueueSelectorImpl@3e74e172
[17:55:23:032] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-7', selector sun.nio.ch.KQueueSelectorImpl@2d9e4dd0
[17:55:23:032] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-6', selector sun.nio.ch.KQueueSelectorImpl@42580f56
[17:55:23:033] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 Accept', selector sun.nio.ch.KQueueSelectorImpl@41ff6407
[17:55:23:031] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-3', selector sun.nio.ch.KQueueSelectorImpl@336b4ec1
[17:55:23:031] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-1', selector sun.nio.ch.KQueueSelectorImpl@58368f45
[17:55:23:032] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-8', selector sun.nio.ch.KQueueSelectorImpl@37ed52a9
[17:55:23:035] [DEBUG] - io.undertow.Undertow.start(Undertow.java:160) - Configuring listener with protocol HTTP for interface 0.0.0.0 and port 8081
[17:55:23:032] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-5', selector sun.nio.ch.KQueueSelectorImpl@b72e5a2
[17:55:23:110] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service **************:8081 register finished
[17:55:23:427] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 47.505 seconds (JVM running for 49.471)
[17:55:23:494] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[17:55:23:594] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`account` , t.`app_id` , t.`app_secret` , t.`token` , t.`encoding_aes_key` FROM `weixin_account` t 
[17:55:23:627] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 
[17:55:23:688] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 13
[17:55:23:693] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:00:58:533] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /version/getSystemSettings, authentication required: false
[18:00:58:533] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /access/getTokenStr, authentication required: false
[18:00:58:533] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /examination/getRecommendExaminations, authentication required: false
[18:00:58:533] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /weixinAccount/loginWithoutUserInfoAndAutoRegist, authentication required: false
[18:00:58:558] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@5e539726 for /version/getSystemSettings
[18:00:58:559] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /version/getSystemSettings
[18:00:58:558] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@5e539726 for /access/getTokenStr
[18:00:58:558] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@5e539726 for /weixinAccount/loginWithoutUserInfoAndAutoRegist
[18:00:58:559] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /access/getTokenStr
[18:00:58:559] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /weixinAccount/loginWithoutUserInfoAndAutoRegist
[18:00:58:558] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@5e539726 for /examination/getRecommendExaminations
[18:00:58:566] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /examination/getRecommendExaminations
[18:00:58:569] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[18:00:58:613] [DEBUG] - com.taurus.examinationassistant.filter.SqlXssFilter.doFilter(SqlXssFilter.java:109) - Skipping SqlXssFilter for excluded path: /access/getTokenStr
[18:01:00:290] [DEBUG] - io.undertow.conduits.ChunkedStreamSinkConduit.terminateWrites(ChunkedStreamSinkConduit.java:327) - UT005013: An IOException occurred
java.io.IOException: Broken pipe
	at sun.nio.ch.FileDispatcherImpl.write0(Native Method) ~[?:1.8.0_281]
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:47) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.write(IOUtil.java:65) ~[?:1.8.0_281]
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:469) ~[?:1.8.0_281]
	at org.xnio.nio.NioSocketConduit.write(NioSocketConduit.java:153) ~[xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at io.undertow.server.protocol.http.HttpResponseConduit.write(HttpResponseConduit.java:629) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.conduits.ChunkedStreamSinkConduit.flush(ChunkedStreamSinkConduit.java:271) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.conduits.ChunkedStreamSinkConduit.terminateWrites(ChunkedStreamSinkConduit.java:324) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.xnio.conduits.ConduitStreamSinkChannel.shutdownWrites(ConduitStreamSinkChannel.java:178) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at io.undertow.channels.DetachableStreamSinkChannel.shutdownWrites(DetachableStreamSinkChannel.java:79) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.spec.ServletOutputStreamImpl.close(ServletOutputStreamImpl.java:626) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.spec.HttpServletResponseImpl.closeStreamAndWriter(HttpServletResponseImpl.java:521) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.spec.HttpServletResponseImpl.responseDone(HttpServletResponseImpl.java:610) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:334) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1423) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_281]
[18:01:00:798] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /version/getSystemSettings, authentication required: false
[18:01:00:798] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /access/getTokenStr, authentication required: false
[18:01:00:803] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@5e539726 for /access/getTokenStr
[18:01:00:803] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@5e539726 for /version/getSystemSettings
[18:01:00:806] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /version/getSystemSettings
[18:01:00:806] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /access/getTokenStr
[18:01:00:808] [DEBUG] - com.taurus.examinationassistant.filter.SqlXssFilter.doFilter(SqlXssFilter.java:109) - Skipping SqlXssFilter for excluded path: /access/getTokenStr
[18:01:01:022] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /examination/getRecommendExaminations, authentication required: false
[18:01:01:023] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@5e539726 for /examination/getRecommendExaminations
[18:01:01:024] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /examination/getRecommendExaminations
[18:01:01:190] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /weixinAccount/loginWithoutUserInfoAndAutoRegist, authentication required: false
[18:01:01:191] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@5e539726 for /weixinAccount/loginWithoutUserInfoAndAutoRegist
[18:01:01:192] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /weixinAccount/loginWithoutUserInfoAndAutoRegist
[18:01:01:583] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /universalProductOrderRelation/getProductOrderRelation, authentication required: false
[18:01:01:584] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@5e539726 for /universalProductOrderRelation/getProductOrderRelation
[18:01:01:584] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /universalProductOrderRelation/getProductOrderRelation
[18:01:07:279] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /examinationTags/getAllExamTagsOfCreaterIdInCompany, authentication required: false
[18:01:07:279] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /examination/getExaminationInViewListOfCreater, authentication required: false
[18:01:07:287] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@5e539726 for /examinationTags/getAllExamTagsOfCreaterIdInCompany
[18:01:07:288] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /examinationTags/getAllExamTagsOfCreaterIdInCompany
[18:01:07:287] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@5e539726 for /examination/getExaminationInViewListOfCreater
[18:01:07:288] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /examination/getExaminationInViewListOfCreater
[18:01:07:294] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:55) - 产品：etea 页面：/pages/personal/admin/exam/index?
[18:01:07:295] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:56) - 请求接口：/examination/getExaminationInViewListOfCreater
[18:01:07:296] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:57) - 请求参数：{"companyId":"0","pageIndex":"1","pageSize":"20","refresh":"true","createrId":"37377","terminal":"etea","callLocationCode":"/pages/personal/admin/exam/index?"}
[18:01:07:406] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select s.tag,count(s.tag) as number from (select IFNULL(t1.tag,'无标签') as tag from examination t LEFT JOIN examination_tag t1 ON t.id=t1.examination_id where t.creater_id=? and t.company_id=? and t.enabled=1) s GROUP BY s.tag 
[18:01:07:410] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 37377(Integer), 0(Integer)
[18:01:07:446] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 2
[18:01:07:448] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:01:07:461] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`code` , t.`name` , t.`paper_id` , t.`notice_enabled` , t.`notice` , t.`password_enabled` , t.`password` , t.`redo_type` , t.`redo_number` , t.`if_display_score` , t.`if_fill_in_name` , t.`if_fill_in_name_all_time` , t.`illustration_relative_url` , t.`illustration_file_name` , t.`state` , t.`pass_score` , t.`begin_time` , t.`end_time` , t.`duration` , t.`display_mode` , t.`creater_id` , t.`create_time` , t.`enabled` , t.`examinee_num` , t.`exam_total_times` , t.`if_display_review_process` , t.`quit_exam_as_auto_commit` , t.`warn_enabled` , t.`warn_times` , t.`if_display_wrong_question` , t.`if_display_right_question` , t.`if_display_answer` , t.`if_display_group` , t.`entrance_template` , t.`exit_template` , t.`examinee_info_template` , t.`examinee_collection_form_id` , t.`favorite_num` , t.`company_id` , t.`if_display_to_examinee` , t.`if_auto_next_question` , t.`if_shuffle_selection_options` , t.`no_ad_enabled` , t.`camera_enabled` , t.`hide_rank` , t.`auth_user` , t.`if_validate_content` , t.`time_per_question` , t.`wx_group_enabled` , t.`open_limit_enabled` , t.`open_limit_times` , t.`collect_wrong_answer` , t.`two_options_required` , t.`illustration_uri` , t.`handwriting_signature_enabled` , t.`record_screen_allowed` , t.`if_ai_scoring` , t.`client` , t.`network_limit_enabled` , t.`selected_wifi_list` FROM `examination` t WHERE creater_id = ? AND company_id = ? AND enabled = ? ORDER BY create_time DESC LIMIT ?,? 
[18:01:07:466] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 37377(Integer), 0(Integer), 1(Integer), 0(Integer), 20(Integer)
[18:01:07:497] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 5
[18:01:07:497] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:01:07:544] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: (select distinct(t.user_id) as userId,t1.id as examinationId,t2.avatar_url as avatarUrl from examination_instance t left join examination t1 ON t1.id = t.examination_id left join user t2 ON t2.id=t.user_id where t.examination_id = ? and t.deleted_by_admin=false order by t.id desc limit ?) union all (select distinct(t.user_id) as userId,t1.id as examinationId,t2.avatar_url as avatarUrl from examination_instance t left join examination t1 ON t1.id = t.examination_id left join user t2 ON t2.id=t.user_id where t.examination_id = ? and t.deleted_by_admin=false order by t.id desc limit ?) union all (select distinct(t.user_id) as userId,t1.id as examinationId,t2.avatar_url as avatarUrl from examination_instance t left join examination t1 ON t1.id = t.examination_id left join user t2 ON t2.id=t.user_id where t.examination_id = ? and t.deleted_by_admin=false order by t.id desc limit ?) union all (select distinct(t.user_id) as userId,t1.id as examinationId,t2.avatar_url as avatarUrl from examination_instance t left join examination t1 ON t1.id = t.examination_id left join user t2 ON t2.id=t.user_id where t.examination_id = ? and t.deleted_by_admin=false order by t.id desc limit ?) union all (select distinct(t.user_id) as userId,t1.id as examinationId,t2.avatar_url as avatarUrl from examination_instance t left join examination t1 ON t1.id = t.examination_id left join user t2 ON t2.id=t.user_id where t.examination_id = ? and t.deleted_by_admin=false order by t.id desc limit ?) 
[18:01:07:548] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169071(Integer), 5(Integer), 169000(Integer), 5(Integer), 167855(Integer), 5(Integer), 167785(Integer), 5(Integer), 147157(Integer), 5(Integer)
[18:01:07:570] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 2
[18:01:07:570] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:01:07:850] [DEBUG] - reactor.util.Loggers$Slf4JLogger.debug(Loggers.java:254) - Using Slf4j logging framework
[18:01:08:094] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[18:01:08:127] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`domain` , t.`title` , t.`description` , t.`duration` , t.`question_number` , t.`selection_number` , t.`completion_number` , t.`judgment_number` , t.`discuss_number` , t.`composition_number` , t.`mark` , t.`selection_mark` , t.`completion_mark` , t.`judgment_mark` , t.`discuss_mark` , t.`composition_mark` , t.`creater_id` , t.`create_time` , t.`price` , t.`company_id` , t.`passed_score` , t.`enabled` , t.`source` FROM `paper` t WHERE t.`id` = ? LIMIT 1 
[18:01:08:138] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 179222(Integer)
[18:01:08:163] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:08:164] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:01:08:169] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[18:01:08:171] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 37377(Integer)
[18:01:08:192] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:08:192] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:01:08:217] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[18:01:08:218] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`domain` , t.`title` , t.`description` , t.`duration` , t.`question_number` , t.`selection_number` , t.`completion_number` , t.`judgment_number` , t.`discuss_number` , t.`composition_number` , t.`mark` , t.`selection_mark` , t.`completion_mark` , t.`judgment_mark` , t.`discuss_mark` , t.`composition_mark` , t.`creater_id` , t.`create_time` , t.`price` , t.`company_id` , t.`passed_score` , t.`enabled` , t.`source` FROM `paper` t WHERE t.`id` = ? LIMIT 1 
[18:01:08:219] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 179151(Integer)
[18:01:08:241] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:08:244] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[18:01:08:244] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 37377(Integer)
[18:01:08:263] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:08:280] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[18:01:08:282] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`domain` , t.`title` , t.`description` , t.`duration` , t.`question_number` , t.`selection_number` , t.`completion_number` , t.`judgment_number` , t.`discuss_number` , t.`composition_number` , t.`mark` , t.`selection_mark` , t.`completion_mark` , t.`judgment_mark` , t.`discuss_mark` , t.`composition_mark` , t.`creater_id` , t.`create_time` , t.`price` , t.`company_id` , t.`passed_score` , t.`enabled` , t.`source` FROM `paper` t WHERE t.`id` = ? LIMIT 1 
[18:01:08:282] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 178002(Integer)
[18:01:08:298] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:08:300] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[18:01:08:301] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 37377(Integer)
[18:01:08:332] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:08:358] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[18:01:08:359] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`domain` , t.`title` , t.`description` , t.`duration` , t.`question_number` , t.`selection_number` , t.`completion_number` , t.`judgment_number` , t.`discuss_number` , t.`composition_number` , t.`mark` , t.`selection_mark` , t.`completion_mark` , t.`judgment_mark` , t.`discuss_mark` , t.`composition_mark` , t.`creater_id` , t.`create_time` , t.`price` , t.`company_id` , t.`passed_score` , t.`enabled` , t.`source` FROM `paper` t WHERE t.`id` = ? LIMIT 1 
[18:01:08:360] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 177932(Integer)
[18:01:08:381] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:08:383] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[18:01:08:383] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 37377(Integer)
[18:01:08:404] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:08:414] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/qkk/qkkExamination/********/********100755d788.jpg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[18:01:08:415] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`domain` , t.`title` , t.`description` , t.`duration` , t.`question_number` , t.`selection_number` , t.`completion_number` , t.`judgment_number` , t.`discuss_number` , t.`composition_number` , t.`mark` , t.`selection_mark` , t.`completion_mark` , t.`judgment_mark` , t.`discuss_mark` , t.`composition_mark` , t.`creater_id` , t.`create_time` , t.`price` , t.`company_id` , t.`passed_score` , t.`enabled` , t.`source` FROM `paper` t WHERE t.`id` = ? LIMIT 1 
[18:01:08:415] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 156551(Integer)
[18:01:08:437] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:08:438] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[18:01:08:439] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 37377(Integer)
[18:01:08:453] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:08:459] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/qkk/qkkExamination/********/********100755d788.jpg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[18:01:12:984] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /examination/getExaminationAndPaperQuestionListAfterEncoded, authentication required: false
[18:01:12:985] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@5e539726 for /examination/getExaminationAndPaperQuestionListAfterEncoded
[18:01:12:985] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /examination/getExaminationAndPaperQuestionListAfterEncoded
[18:01:12:998] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`code` , t.`name` , t.`paper_id` , t.`notice_enabled` , t.`notice` , t.`password_enabled` , t.`password` , t.`redo_type` , t.`redo_number` , t.`if_display_score` , t.`if_fill_in_name` , t.`if_fill_in_name_all_time` , t.`illustration_relative_url` , t.`illustration_file_name` , t.`state` , t.`pass_score` , t.`begin_time` , t.`end_time` , t.`duration` , t.`display_mode` , t.`creater_id` , t.`create_time` , t.`enabled` , t.`examinee_num` , t.`exam_total_times` , t.`if_display_review_process` , t.`quit_exam_as_auto_commit` , t.`warn_enabled` , t.`warn_times` , t.`if_display_wrong_question` , t.`if_display_right_question` , t.`if_display_answer` , t.`if_display_group` , t.`entrance_template` , t.`exit_template` , t.`examinee_info_template` , t.`examinee_collection_form_id` , t.`favorite_num` , t.`company_id` , t.`if_display_to_examinee` , t.`if_auto_next_question` , t.`if_shuffle_selection_options` , t.`no_ad_enabled` , t.`camera_enabled` , t.`hide_rank` , t.`auth_user` , t.`if_validate_content` , t.`time_per_question` , t.`wx_group_enabled` , t.`open_limit_enabled` , t.`open_limit_times` , t.`collect_wrong_answer` , t.`two_options_required` , t.`illustration_uri` , t.`handwriting_signature_enabled` , t.`record_screen_allowed` , t.`if_ai_scoring` , t.`client` , t.`network_limit_enabled` , t.`selected_wifi_list` FROM `examination` t WHERE t.`id` = ? LIMIT 1 
[18:01:12:999] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169071(Integer)
[18:01:13:018] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:13:019] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:01:13:031] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t1.*,t2.mark,t2.question_number from examination t1 left join paper t2 ON t1.paper_id= t2.id where 1=1 and t1.id=? limit 1 
[18:01:13:031] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169071(Integer)
[18:01:13:056] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:13:058] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:01:13:069] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.question_type as questionType , t.question_mark as questionMark , t.sequence_no as sequenceNo , t.paper_id as paperId , t.question_id as questionId , t.completion_rule as completionRule , t1.main_content as mainContent , t1.options , t1.child_type as childType FROM `paper_question` t left join question t1 ON t.question_id=t1.id WHERE t.question_type <> ? AND t.paper_id = ? 
[18:01:13:070] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: RAN(String), 179222(Integer)
[18:01:13:093] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 4
[18:01:13:094] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:01:13:108] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.question_type as questionType , t.question_mark as questionMark , t.sequence_no as sequenceNo , t.paper_id as paperId , t.question_id as questionId , t.completion_rule as completionRule , t1.id , t1.total_question_num as totalQuestionNum , t1.single_mark as singleMark , t1.take_num as takeNum FROM `paper_question` t left join question_random t1 ON t.question_id=t1.id WHERE t.question_type = ? AND t.paper_id = ? 
[18:01:13:110] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: RAN(String), 179222(Integer)
[18:01:13:124] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[18:01:13:124] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:01:13:126] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`examination_id` , t.`tag` FROM `examination_tag` t WHERE t.`examination_id`= ? 
[18:01:13:128] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169071(Integer)
[18:01:13:148] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[18:01:13:149] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:01:13:150] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:01:20:384] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /exerciseBook/exerciseBookListOfCreater, authentication required: false
[18:01:20:385] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@5e539726 for /exerciseBook/exerciseBookListOfCreater
[18:01:20:385] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /exerciseBook/exerciseBookListOfCreater
[18:01:20:489] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.* , t1.total_star_level as totalStarLevel , t1.total_start_level_user_num as totalStarLevelUserNum , t1.total_user_number as totalUserNumber , t1.total_practice_times as totalPracticeTimes , IFNULL(t1.total_favorite,0) as favoriteNum , t2.nickname , t2.name as userName FROM `exercise_book` t left join exercise_book_summary t1 ON t1.exercise_book_id=t.id left join user t2 ON t2.id=t.creater_id WHERE t.enabled = ? AND t.creater_id = ? AND t.company_id = ? ORDER BY t.id DESC LIMIT ?,? 
[18:01:20:493] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 37377(Integer), 0(Integer), 0(Integer), 10(Integer)
[18:01:20:522] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:20:525] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:01:20:526] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:01:25:161] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /exerciseBook/getExerciseBookIndexPageInfo, authentication required: false
[18:01:25:161] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /exerciseBookFavorate/getEntity, authentication required: false
[18:01:25:162] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@5e539726 for /exerciseBookFavorate/getEntity
[18:01:25:162] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@5e539726 for /exerciseBook/getExerciseBookIndexPageInfo
[18:01:25:163] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /exerciseBookFavorate/getEntity
[18:01:25:163] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /exerciseBook/getExerciseBookIndexPageInfo
[18:01:25:189] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,t2.total_favorite as totalFavorite,t2.total_star_level,t2.total_start_level_user_num,t3.name as username,t3.avatar_url,t3.nickname from exercise_book t left join exercise_book_summary t2 ON t2.exercise_book_id=t.id left join user t3 ON t3.id=t.creater_id where t.id=? 
[18:01:25:193] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 38487(Integer)
[18:01:25:222] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:25:223] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:01:25:223] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:01:25:227] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select count(t.id) as times, count(distinct(date_format(t.end_date,'%Y-%m-%d'))) as days, t.user_id as userId,sum(t.selection_right_times) as totalSelectionRightTimes,sum(t.selection_wrong_times) as selectionWrongTimes,sum(t.completion_right_times) as totalCompletionRightTimes,sum(t.completion_wrong_times) as completionWrongTimes,sum(t.judgment_right_times) as judgmentRightTimes,sum(t.judgment_wrong_times) as totalJudgmentWrongTimes,sum(t.total_right_times) as totalRightTimes,sum(t.total_wrong_times) as totalWrongTimes,sum(t.total_right_times+t.total_wrong_times) as totalTimes, sum(t.duration) as duration, t1.name,t1.nickname,t1.avatar_url as avatarUrl from exercise_book_practice_summary t Left join user t1 ON t1.id = t.user_id where t.exercise_book_id=? group by t.user_id order by totalRightTimes desc,duration desc limit ?,? 
[18:01:25:228] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 38487(Integer), 0(Integer), 10(Integer)
[18:01:25:248] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`exercise_book_id` , t.`user_id` , t.`create_time` , t.`enabled` FROM `exercise_book_favorite` t WHERE exercise_book_id = ? AND user_id = ? AND enabled = ? LIMIT 1 
[18:01:25:250] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 38487(Integer), 37377(Integer), true(Boolean)
[18:01:25:251] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:25:253] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:01:25:253] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:01:25:285] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[18:01:25:286] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:01:39:451] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /examination/getExaminationInViewListOfCreater, authentication required: false
[18:01:39:451] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /examinationTags/getAllExamTagsOfCreaterIdInCompany, authentication required: false
[18:01:39:469] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@5e539726 for /examination/getExaminationInViewListOfCreater
[18:01:39:469] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@5e539726 for /examinationTags/getAllExamTagsOfCreaterIdInCompany
[18:01:39:470] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /examination/getExaminationInViewListOfCreater
[18:01:39:470] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /examinationTags/getAllExamTagsOfCreaterIdInCompany
[18:01:39:474] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:55) - 产品：etea 页面：/pages/personal/admin/exam/index?
[18:01:39:475] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:56) - 请求接口：/examination/getExaminationInViewListOfCreater
[18:01:39:475] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:57) - 请求参数：{"companyId":"0","pageIndex":"1","pageSize":"20","refresh":"true","createrId":"37377","terminal":"etea","callLocationCode":"/pages/personal/admin/exam/index?"}
[18:01:39:525] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select s.tag,count(s.tag) as number from (select IFNULL(t1.tag,'无标签') as tag from examination t LEFT JOIN examination_tag t1 ON t.id=t1.examination_id where t.creater_id=? and t.company_id=? and t.enabled=1) s GROUP BY s.tag 
[18:01:39:527] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 37377(Integer), 0(Integer)
[18:01:39:537] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`code` , t.`name` , t.`paper_id` , t.`notice_enabled` , t.`notice` , t.`password_enabled` , t.`password` , t.`redo_type` , t.`redo_number` , t.`if_display_score` , t.`if_fill_in_name` , t.`if_fill_in_name_all_time` , t.`illustration_relative_url` , t.`illustration_file_name` , t.`state` , t.`pass_score` , t.`begin_time` , t.`end_time` , t.`duration` , t.`display_mode` , t.`creater_id` , t.`create_time` , t.`enabled` , t.`examinee_num` , t.`exam_total_times` , t.`if_display_review_process` , t.`quit_exam_as_auto_commit` , t.`warn_enabled` , t.`warn_times` , t.`if_display_wrong_question` , t.`if_display_right_question` , t.`if_display_answer` , t.`if_display_group` , t.`entrance_template` , t.`exit_template` , t.`examinee_info_template` , t.`examinee_collection_form_id` , t.`favorite_num` , t.`company_id` , t.`if_display_to_examinee` , t.`if_auto_next_question` , t.`if_shuffle_selection_options` , t.`no_ad_enabled` , t.`camera_enabled` , t.`hide_rank` , t.`auth_user` , t.`if_validate_content` , t.`time_per_question` , t.`wx_group_enabled` , t.`open_limit_enabled` , t.`open_limit_times` , t.`collect_wrong_answer` , t.`two_options_required` , t.`illustration_uri` , t.`handwriting_signature_enabled` , t.`record_screen_allowed` , t.`if_ai_scoring` , t.`client` , t.`network_limit_enabled` , t.`selected_wifi_list` FROM `examination` t WHERE creater_id = ? AND company_id = ? AND enabled = ? ORDER BY create_time DESC LIMIT ?,? 
[18:01:39:538] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 37377(Integer), 0(Integer), 1(Integer), 0(Integer), 20(Integer)
[18:01:39:549] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 2
[18:01:39:550] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:01:39:564] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 5
[18:01:39:565] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:01:39:565] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:01:39:567] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: (select distinct(t.user_id) as userId,t1.id as examinationId,t2.avatar_url as avatarUrl from examination_instance t left join examination t1 ON t1.id = t.examination_id left join user t2 ON t2.id=t.user_id where t.examination_id = ? and t.deleted_by_admin=false order by t.id desc limit ?) union all (select distinct(t.user_id) as userId,t1.id as examinationId,t2.avatar_url as avatarUrl from examination_instance t left join examination t1 ON t1.id = t.examination_id left join user t2 ON t2.id=t.user_id where t.examination_id = ? and t.deleted_by_admin=false order by t.id desc limit ?) union all (select distinct(t.user_id) as userId,t1.id as examinationId,t2.avatar_url as avatarUrl from examination_instance t left join examination t1 ON t1.id = t.examination_id left join user t2 ON t2.id=t.user_id where t.examination_id = ? and t.deleted_by_admin=false order by t.id desc limit ?) union all (select distinct(t.user_id) as userId,t1.id as examinationId,t2.avatar_url as avatarUrl from examination_instance t left join examination t1 ON t1.id = t.examination_id left join user t2 ON t2.id=t.user_id where t.examination_id = ? and t.deleted_by_admin=false order by t.id desc limit ?) union all (select distinct(t.user_id) as userId,t1.id as examinationId,t2.avatar_url as avatarUrl from examination_instance t left join examination t1 ON t1.id = t.examination_id left join user t2 ON t2.id=t.user_id where t.examination_id = ? and t.deleted_by_admin=false order by t.id desc limit ?) 
[18:01:39:568] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169071(Integer), 5(Integer), 169000(Integer), 5(Integer), 167855(Integer), 5(Integer), 167785(Integer), 5(Integer), 147157(Integer), 5(Integer)
[18:01:39:583] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 2
[18:01:39:584] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:01:39:584] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:01:39:606] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[18:01:39:607] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`domain` , t.`title` , t.`description` , t.`duration` , t.`question_number` , t.`selection_number` , t.`completion_number` , t.`judgment_number` , t.`discuss_number` , t.`composition_number` , t.`mark` , t.`selection_mark` , t.`completion_mark` , t.`judgment_mark` , t.`discuss_mark` , t.`composition_mark` , t.`creater_id` , t.`create_time` , t.`price` , t.`company_id` , t.`passed_score` , t.`enabled` , t.`source` FROM `paper` t WHERE t.`id` = ? LIMIT 1 
[18:01:39:610] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 179222(Integer)
[18:01:39:625] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:39:625] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:01:39:626] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:01:39:629] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[18:01:39:630] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 37377(Integer)
[18:01:39:644] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:39:644] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:01:39:644] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:01:39:648] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[18:01:39:648] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`domain` , t.`title` , t.`description` , t.`duration` , t.`question_number` , t.`selection_number` , t.`completion_number` , t.`judgment_number` , t.`discuss_number` , t.`composition_number` , t.`mark` , t.`selection_mark` , t.`completion_mark` , t.`judgment_mark` , t.`discuss_mark` , t.`composition_mark` , t.`creater_id` , t.`create_time` , t.`price` , t.`company_id` , t.`passed_score` , t.`enabled` , t.`source` FROM `paper` t WHERE t.`id` = ? LIMIT 1 
[18:01:39:651] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 179151(Integer)
[18:01:39:668] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:39:670] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[18:01:39:671] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 37377(Integer)
[18:01:39:686] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:39:688] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[18:01:39:689] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`domain` , t.`title` , t.`description` , t.`duration` , t.`question_number` , t.`selection_number` , t.`completion_number` , t.`judgment_number` , t.`discuss_number` , t.`composition_number` , t.`mark` , t.`selection_mark` , t.`completion_mark` , t.`judgment_mark` , t.`discuss_mark` , t.`composition_mark` , t.`creater_id` , t.`create_time` , t.`price` , t.`company_id` , t.`passed_score` , t.`enabled` , t.`source` FROM `paper` t WHERE t.`id` = ? LIMIT 1 
[18:01:39:689] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 178002(Integer)
[18:01:39:702] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:39:703] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[18:01:39:703] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 37377(Integer)
[18:01:39:717] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:39:720] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[18:01:39:721] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`domain` , t.`title` , t.`description` , t.`duration` , t.`question_number` , t.`selection_number` , t.`completion_number` , t.`judgment_number` , t.`discuss_number` , t.`composition_number` , t.`mark` , t.`selection_mark` , t.`completion_mark` , t.`judgment_mark` , t.`discuss_mark` , t.`composition_mark` , t.`creater_id` , t.`create_time` , t.`price` , t.`company_id` , t.`passed_score` , t.`enabled` , t.`source` FROM `paper` t WHERE t.`id` = ? LIMIT 1 
[18:01:39:721] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 177932(Integer)
[18:01:39:737] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:39:739] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[18:01:39:739] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 37377(Integer)
[18:01:39:756] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:39:760] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/qkk/qkkExamination/********/********100755d788.jpg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[18:01:39:767] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`domain` , t.`title` , t.`description` , t.`duration` , t.`question_number` , t.`selection_number` , t.`completion_number` , t.`judgment_number` , t.`discuss_number` , t.`composition_number` , t.`mark` , t.`selection_mark` , t.`completion_mark` , t.`judgment_mark` , t.`discuss_mark` , t.`composition_mark` , t.`creater_id` , t.`create_time` , t.`price` , t.`company_id` , t.`passed_score` , t.`enabled` , t.`source` FROM `paper` t WHERE t.`id` = ? LIMIT 1 
[18:01:39:767] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 156551(Integer)
[18:01:39:783] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:39:784] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[18:01:39:784] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 37377(Integer)
[18:01:39:799] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:39:802] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/qkk/qkkExamination/********/********100755d788.jpg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[18:01:44:406] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /examination/getExaminationAndPaperQuestionListAfterEncoded, authentication required: false
[18:01:44:410] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@5e539726 for /examination/getExaminationAndPaperQuestionListAfterEncoded
[18:01:44:410] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /examination/getExaminationAndPaperQuestionListAfterEncoded
[18:01:44:414] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`code` , t.`name` , t.`paper_id` , t.`notice_enabled` , t.`notice` , t.`password_enabled` , t.`password` , t.`redo_type` , t.`redo_number` , t.`if_display_score` , t.`if_fill_in_name` , t.`if_fill_in_name_all_time` , t.`illustration_relative_url` , t.`illustration_file_name` , t.`state` , t.`pass_score` , t.`begin_time` , t.`end_time` , t.`duration` , t.`display_mode` , t.`creater_id` , t.`create_time` , t.`enabled` , t.`examinee_num` , t.`exam_total_times` , t.`if_display_review_process` , t.`quit_exam_as_auto_commit` , t.`warn_enabled` , t.`warn_times` , t.`if_display_wrong_question` , t.`if_display_right_question` , t.`if_display_answer` , t.`if_display_group` , t.`entrance_template` , t.`exit_template` , t.`examinee_info_template` , t.`examinee_collection_form_id` , t.`favorite_num` , t.`company_id` , t.`if_display_to_examinee` , t.`if_auto_next_question` , t.`if_shuffle_selection_options` , t.`no_ad_enabled` , t.`camera_enabled` , t.`hide_rank` , t.`auth_user` , t.`if_validate_content` , t.`time_per_question` , t.`wx_group_enabled` , t.`open_limit_enabled` , t.`open_limit_times` , t.`collect_wrong_answer` , t.`two_options_required` , t.`illustration_uri` , t.`handwriting_signature_enabled` , t.`record_screen_allowed` , t.`if_ai_scoring` , t.`client` , t.`network_limit_enabled` , t.`selected_wifi_list` FROM `examination` t WHERE t.`id` = ? LIMIT 1 
[18:01:44:416] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169071(Integer)
[18:01:44:432] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:44:432] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:01:44:433] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:01:44:437] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t1.*,t2.mark,t2.question_number from examination t1 left join paper t2 ON t1.paper_id= t2.id where 1=1 and t1.id=? limit 1 
[18:01:44:438] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169071(Integer)
[18:01:44:457] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:44:458] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:01:44:459] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:01:44:464] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.question_type as questionType , t.question_mark as questionMark , t.sequence_no as sequenceNo , t.paper_id as paperId , t.question_id as questionId , t.completion_rule as completionRule , t1.main_content as mainContent , t1.options , t1.child_type as childType FROM `paper_question` t left join question t1 ON t.question_id=t1.id WHERE t.question_type <> ? AND t.paper_id = ? 
[18:01:44:464] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: RAN(String), 179222(Integer)
[18:01:44:482] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 4
[18:01:44:483] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:01:44:483] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:01:44:487] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.question_type as questionType , t.question_mark as questionMark , t.sequence_no as sequenceNo , t.paper_id as paperId , t.question_id as questionId , t.completion_rule as completionRule , t1.id , t1.total_question_num as totalQuestionNum , t1.single_mark as singleMark , t1.take_num as takeNum FROM `paper_question` t left join question_random t1 ON t.question_id=t1.id WHERE t.question_type = ? AND t.paper_id = ? 
[18:01:44:488] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: RAN(String), 179222(Integer)
[18:01:44:502] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[18:01:44:504] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:01:44:504] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:01:44:506] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`examination_id` , t.`tag` FROM `examination_tag` t WHERE t.`examination_id`= ? 
[18:01:44:506] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169071(Integer)
[18:01:44:519] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[18:01:44:520] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:01:44:520] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:01:50:339] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /examinationInstance/examinationInstanceStageSummaryListExcludingExample, authentication required: false
[18:01:50:343] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@5e539726 for /examinationInstance/examinationInstanceStageSummaryListExcludingExample
[18:01:50:343] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /examinationInstance/examinationInstanceStageSummaryListExcludingExample
[18:01:50:455] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`code` , t.`name` , t.`paper_id` , t.`notice_enabled` , t.`notice` , t.`password_enabled` , t.`password` , t.`redo_type` , t.`redo_number` , t.`if_display_score` , t.`if_fill_in_name` , t.`if_fill_in_name_all_time` , t.`illustration_relative_url` , t.`illustration_file_name` , t.`state` , t.`pass_score` , t.`begin_time` , t.`end_time` , t.`duration` , t.`display_mode` , t.`creater_id` , t.`create_time` , t.`enabled` , t.`examinee_num` , t.`exam_total_times` , t.`if_display_review_process` , t.`quit_exam_as_auto_commit` , t.`warn_enabled` , t.`warn_times` , t.`if_display_wrong_question` , t.`if_display_right_question` , t.`if_display_answer` , t.`if_display_group` , t.`entrance_template` , t.`exit_template` , t.`examinee_info_template` , t.`examinee_collection_form_id` , t.`favorite_num` , t.`company_id` , t.`if_display_to_examinee` , t.`if_auto_next_question` , t.`if_shuffle_selection_options` , t.`no_ad_enabled` , t.`camera_enabled` , t.`hide_rank` , t.`auth_user` , t.`if_validate_content` , t.`time_per_question` , t.`wx_group_enabled` , t.`open_limit_enabled` , t.`open_limit_times` , t.`collect_wrong_answer` , t.`two_options_required` , t.`illustration_uri` , t.`handwriting_signature_enabled` , t.`record_screen_allowed` , t.`if_ai_scoring` , t.`client` , t.`network_limit_enabled` , t.`selected_wifi_list` FROM `examination` t WHERE creater_id = ? AND company_id = ? AND enabled = ? ORDER BY create_time DESC LIMIT ?,? 
[18:01:50:458] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 37377(String), 0(Integer), 1(Integer), 0(Integer), 10(Integer)
[18:01:50:502] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 5
[18:01:50:523] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.* , t2.name as examinationName , t2.creater_id as createrId , t2.create_time as createTime , t2.camera_enabled as cameraEnabled , t2.illustration_relative_url FROM `examination_instance` t LEFT JOIN examination t2 ON t.examination_id = t2.id WHERE t.examination_id = ? AND ( (t.deleted_by_admin is null or t.deleted_by_admin =false) ) 
[18:01:50:532] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169071(Integer)
[18:01:50:545] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[18:01:50:546] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:01:50:546] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:01:50:547] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`code` , t.`name` , t.`paper_id` , t.`notice_enabled` , t.`notice` , t.`password_enabled` , t.`password` , t.`redo_type` , t.`redo_number` , t.`if_display_score` , t.`if_fill_in_name` , t.`if_fill_in_name_all_time` , t.`illustration_relative_url` , t.`illustration_file_name` , t.`state` , t.`pass_score` , t.`begin_time` , t.`end_time` , t.`duration` , t.`display_mode` , t.`creater_id` , t.`create_time` , t.`enabled` , t.`examinee_num` , t.`exam_total_times` , t.`if_display_review_process` , t.`quit_exam_as_auto_commit` , t.`warn_enabled` , t.`warn_times` , t.`if_display_wrong_question` , t.`if_display_right_question` , t.`if_display_answer` , t.`if_display_group` , t.`entrance_template` , t.`exit_template` , t.`examinee_info_template` , t.`examinee_collection_form_id` , t.`favorite_num` , t.`company_id` , t.`if_display_to_examinee` , t.`if_auto_next_question` , t.`if_shuffle_selection_options` , t.`no_ad_enabled` , t.`camera_enabled` , t.`hide_rank` , t.`auth_user` , t.`if_validate_content` , t.`time_per_question` , t.`wx_group_enabled` , t.`open_limit_enabled` , t.`open_limit_times` , t.`collect_wrong_answer` , t.`two_options_required` , t.`illustration_uri` , t.`handwriting_signature_enabled` , t.`record_screen_allowed` , t.`if_ai_scoring` , t.`client` , t.`network_limit_enabled` , t.`selected_wifi_list` FROM `examination` t WHERE t.`id` = ? LIMIT 1 
[18:01:50:547] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169071(Integer)
[18:01:50:564] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:50:566] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.* , t2.name as examinationName , t2.creater_id as createrId , t2.create_time as createTime , t2.camera_enabled as cameraEnabled , t2.illustration_relative_url FROM `examination_instance` t LEFT JOIN examination t2 ON t.examination_id = t2.id WHERE t.examination_id = ? AND ( (t.deleted_by_admin is null or t.deleted_by_admin =false) ) 
[18:01:50:566] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169000(Integer)
[18:01:50:583] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:50:606] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.* , t2.name as examinationName , t2.creater_id as createrId , t2.create_time as createTime , t2.camera_enabled as cameraEnabled , t2.illustration_relative_url FROM `examination_instance` t LEFT JOIN examination t2 ON t.examination_id = t2.id WHERE t.examination_id = ? AND ( (t.deleted_by_admin is null or t.deleted_by_admin =false) ) 
[18:01:50:606] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 167855(Integer)
[18:01:50:619] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[18:01:50:621] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`code` , t.`name` , t.`paper_id` , t.`notice_enabled` , t.`notice` , t.`password_enabled` , t.`password` , t.`redo_type` , t.`redo_number` , t.`if_display_score` , t.`if_fill_in_name` , t.`if_fill_in_name_all_time` , t.`illustration_relative_url` , t.`illustration_file_name` , t.`state` , t.`pass_score` , t.`begin_time` , t.`end_time` , t.`duration` , t.`display_mode` , t.`creater_id` , t.`create_time` , t.`enabled` , t.`examinee_num` , t.`exam_total_times` , t.`if_display_review_process` , t.`quit_exam_as_auto_commit` , t.`warn_enabled` , t.`warn_times` , t.`if_display_wrong_question` , t.`if_display_right_question` , t.`if_display_answer` , t.`if_display_group` , t.`entrance_template` , t.`exit_template` , t.`examinee_info_template` , t.`examinee_collection_form_id` , t.`favorite_num` , t.`company_id` , t.`if_display_to_examinee` , t.`if_auto_next_question` , t.`if_shuffle_selection_options` , t.`no_ad_enabled` , t.`camera_enabled` , t.`hide_rank` , t.`auth_user` , t.`if_validate_content` , t.`time_per_question` , t.`wx_group_enabled` , t.`open_limit_enabled` , t.`open_limit_times` , t.`collect_wrong_answer` , t.`two_options_required` , t.`illustration_uri` , t.`handwriting_signature_enabled` , t.`record_screen_allowed` , t.`if_ai_scoring` , t.`client` , t.`network_limit_enabled` , t.`selected_wifi_list` FROM `examination` t WHERE t.`id` = ? LIMIT 1 
[18:01:50:621] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 167855(Integer)
[18:01:50:637] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:50:639] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.* , t2.name as examinationName , t2.creater_id as createrId , t2.create_time as createTime , t2.camera_enabled as cameraEnabled , t2.illustration_relative_url FROM `examination_instance` t LEFT JOIN examination t2 ON t.examination_id = t2.id WHERE t.examination_id = ? AND ( (t.deleted_by_admin is null or t.deleted_by_admin =false) ) 
[18:01:50:639] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 167785(Integer)
[18:01:50:652] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[18:01:50:652] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`code` , t.`name` , t.`paper_id` , t.`notice_enabled` , t.`notice` , t.`password_enabled` , t.`password` , t.`redo_type` , t.`redo_number` , t.`if_display_score` , t.`if_fill_in_name` , t.`if_fill_in_name_all_time` , t.`illustration_relative_url` , t.`illustration_file_name` , t.`state` , t.`pass_score` , t.`begin_time` , t.`end_time` , t.`duration` , t.`display_mode` , t.`creater_id` , t.`create_time` , t.`enabled` , t.`examinee_num` , t.`exam_total_times` , t.`if_display_review_process` , t.`quit_exam_as_auto_commit` , t.`warn_enabled` , t.`warn_times` , t.`if_display_wrong_question` , t.`if_display_right_question` , t.`if_display_answer` , t.`if_display_group` , t.`entrance_template` , t.`exit_template` , t.`examinee_info_template` , t.`examinee_collection_form_id` , t.`favorite_num` , t.`company_id` , t.`if_display_to_examinee` , t.`if_auto_next_question` , t.`if_shuffle_selection_options` , t.`no_ad_enabled` , t.`camera_enabled` , t.`hide_rank` , t.`auth_user` , t.`if_validate_content` , t.`time_per_question` , t.`wx_group_enabled` , t.`open_limit_enabled` , t.`open_limit_times` , t.`collect_wrong_answer` , t.`two_options_required` , t.`illustration_uri` , t.`handwriting_signature_enabled` , t.`record_screen_allowed` , t.`if_ai_scoring` , t.`client` , t.`network_limit_enabled` , t.`selected_wifi_list` FROM `examination` t WHERE t.`id` = ? LIMIT 1 
[18:01:50:653] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 167785(Integer)
[18:01:50:669] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:50:670] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.* , t2.name as examinationName , t2.creater_id as createrId , t2.create_time as createTime , t2.camera_enabled as cameraEnabled , t2.illustration_relative_url FROM `examination_instance` t LEFT JOIN examination t2 ON t.examination_id = t2.id WHERE t.examination_id = ? AND ( (t.deleted_by_admin is null or t.deleted_by_admin =false) ) 
[18:01:50:671] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 147157(Integer)
[18:01:50:688] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 2
[18:01:50:734] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /examinationInstance/examinationInstanceStageSummaryListExcludingExample, authentication required: false
[18:01:50:737] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@5e539726 for /examinationInstance/examinationInstanceStageSummaryListExcludingExample
[18:01:50:737] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /examinationInstance/examinationInstanceStageSummaryListExcludingExample
[18:01:50:762] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`code` , t.`name` , t.`paper_id` , t.`notice_enabled` , t.`notice` , t.`password_enabled` , t.`password` , t.`redo_type` , t.`redo_number` , t.`if_display_score` , t.`if_fill_in_name` , t.`if_fill_in_name_all_time` , t.`illustration_relative_url` , t.`illustration_file_name` , t.`state` , t.`pass_score` , t.`begin_time` , t.`end_time` , t.`duration` , t.`display_mode` , t.`creater_id` , t.`create_time` , t.`enabled` , t.`examinee_num` , t.`exam_total_times` , t.`if_display_review_process` , t.`quit_exam_as_auto_commit` , t.`warn_enabled` , t.`warn_times` , t.`if_display_wrong_question` , t.`if_display_right_question` , t.`if_display_answer` , t.`if_display_group` , t.`entrance_template` , t.`exit_template` , t.`examinee_info_template` , t.`examinee_collection_form_id` , t.`favorite_num` , t.`company_id` , t.`if_display_to_examinee` , t.`if_auto_next_question` , t.`if_shuffle_selection_options` , t.`no_ad_enabled` , t.`camera_enabled` , t.`hide_rank` , t.`auth_user` , t.`if_validate_content` , t.`time_per_question` , t.`wx_group_enabled` , t.`open_limit_enabled` , t.`open_limit_times` , t.`collect_wrong_answer` , t.`two_options_required` , t.`illustration_uri` , t.`handwriting_signature_enabled` , t.`record_screen_allowed` , t.`if_ai_scoring` , t.`client` , t.`network_limit_enabled` , t.`selected_wifi_list` FROM `examination` t WHERE creater_id = ? AND company_id = ? AND enabled = ? ORDER BY create_time DESC LIMIT ?,? 
[18:01:50:762] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 37377(String), 0(Integer), 1(Integer), 0(Integer), 10(Integer)
[18:01:50:785] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 5
[18:01:50:791] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.* , t2.name as examinationName , t2.creater_id as createrId , t2.create_time as createTime , t2.camera_enabled as cameraEnabled , t2.illustration_relative_url FROM `examination_instance` t LEFT JOIN examination t2 ON t.examination_id = t2.id WHERE t.examination_id = ? AND ( (t.deleted_by_admin is null or t.deleted_by_admin =false) ) 
[18:01:50:792] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169071(Integer)
[18:01:50:806] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[18:01:50:807] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`code` , t.`name` , t.`paper_id` , t.`notice_enabled` , t.`notice` , t.`password_enabled` , t.`password` , t.`redo_type` , t.`redo_number` , t.`if_display_score` , t.`if_fill_in_name` , t.`if_fill_in_name_all_time` , t.`illustration_relative_url` , t.`illustration_file_name` , t.`state` , t.`pass_score` , t.`begin_time` , t.`end_time` , t.`duration` , t.`display_mode` , t.`creater_id` , t.`create_time` , t.`enabled` , t.`examinee_num` , t.`exam_total_times` , t.`if_display_review_process` , t.`quit_exam_as_auto_commit` , t.`warn_enabled` , t.`warn_times` , t.`if_display_wrong_question` , t.`if_display_right_question` , t.`if_display_answer` , t.`if_display_group` , t.`entrance_template` , t.`exit_template` , t.`examinee_info_template` , t.`examinee_collection_form_id` , t.`favorite_num` , t.`company_id` , t.`if_display_to_examinee` , t.`if_auto_next_question` , t.`if_shuffle_selection_options` , t.`no_ad_enabled` , t.`camera_enabled` , t.`hide_rank` , t.`auth_user` , t.`if_validate_content` , t.`time_per_question` , t.`wx_group_enabled` , t.`open_limit_enabled` , t.`open_limit_times` , t.`collect_wrong_answer` , t.`two_options_required` , t.`illustration_uri` , t.`handwriting_signature_enabled` , t.`record_screen_allowed` , t.`if_ai_scoring` , t.`client` , t.`network_limit_enabled` , t.`selected_wifi_list` FROM `examination` t WHERE t.`id` = ? LIMIT 1 
[18:01:50:807] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169071(Integer)
[18:01:50:821] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:50:822] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.* , t2.name as examinationName , t2.creater_id as createrId , t2.create_time as createTime , t2.camera_enabled as cameraEnabled , t2.illustration_relative_url FROM `examination_instance` t LEFT JOIN examination t2 ON t.examination_id = t2.id WHERE t.examination_id = ? AND ( (t.deleted_by_admin is null or t.deleted_by_admin =false) ) 
[18:01:50:823] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169000(Integer)
[18:01:50:842] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:50:844] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.* , t2.name as examinationName , t2.creater_id as createrId , t2.create_time as createTime , t2.camera_enabled as cameraEnabled , t2.illustration_relative_url FROM `examination_instance` t LEFT JOIN examination t2 ON t.examination_id = t2.id WHERE t.examination_id = ? AND ( (t.deleted_by_admin is null or t.deleted_by_admin =false) ) 
[18:01:50:844] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 167855(Integer)
[18:01:50:859] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[18:01:50:859] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`code` , t.`name` , t.`paper_id` , t.`notice_enabled` , t.`notice` , t.`password_enabled` , t.`password` , t.`redo_type` , t.`redo_number` , t.`if_display_score` , t.`if_fill_in_name` , t.`if_fill_in_name_all_time` , t.`illustration_relative_url` , t.`illustration_file_name` , t.`state` , t.`pass_score` , t.`begin_time` , t.`end_time` , t.`duration` , t.`display_mode` , t.`creater_id` , t.`create_time` , t.`enabled` , t.`examinee_num` , t.`exam_total_times` , t.`if_display_review_process` , t.`quit_exam_as_auto_commit` , t.`warn_enabled` , t.`warn_times` , t.`if_display_wrong_question` , t.`if_display_right_question` , t.`if_display_answer` , t.`if_display_group` , t.`entrance_template` , t.`exit_template` , t.`examinee_info_template` , t.`examinee_collection_form_id` , t.`favorite_num` , t.`company_id` , t.`if_display_to_examinee` , t.`if_auto_next_question` , t.`if_shuffle_selection_options` , t.`no_ad_enabled` , t.`camera_enabled` , t.`hide_rank` , t.`auth_user` , t.`if_validate_content` , t.`time_per_question` , t.`wx_group_enabled` , t.`open_limit_enabled` , t.`open_limit_times` , t.`collect_wrong_answer` , t.`two_options_required` , t.`illustration_uri` , t.`handwriting_signature_enabled` , t.`record_screen_allowed` , t.`if_ai_scoring` , t.`client` , t.`network_limit_enabled` , t.`selected_wifi_list` FROM `examination` t WHERE t.`id` = ? LIMIT 1 
[18:01:50:859] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 167855(Integer)
[18:01:50:875] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:50:877] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.* , t2.name as examinationName , t2.creater_id as createrId , t2.create_time as createTime , t2.camera_enabled as cameraEnabled , t2.illustration_relative_url FROM `examination_instance` t LEFT JOIN examination t2 ON t.examination_id = t2.id WHERE t.examination_id = ? AND ( (t.deleted_by_admin is null or t.deleted_by_admin =false) ) 
[18:01:50:878] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 167785(Integer)
[18:01:50:891] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[18:01:50:892] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`code` , t.`name` , t.`paper_id` , t.`notice_enabled` , t.`notice` , t.`password_enabled` , t.`password` , t.`redo_type` , t.`redo_number` , t.`if_display_score` , t.`if_fill_in_name` , t.`if_fill_in_name_all_time` , t.`illustration_relative_url` , t.`illustration_file_name` , t.`state` , t.`pass_score` , t.`begin_time` , t.`end_time` , t.`duration` , t.`display_mode` , t.`creater_id` , t.`create_time` , t.`enabled` , t.`examinee_num` , t.`exam_total_times` , t.`if_display_review_process` , t.`quit_exam_as_auto_commit` , t.`warn_enabled` , t.`warn_times` , t.`if_display_wrong_question` , t.`if_display_right_question` , t.`if_display_answer` , t.`if_display_group` , t.`entrance_template` , t.`exit_template` , t.`examinee_info_template` , t.`examinee_collection_form_id` , t.`favorite_num` , t.`company_id` , t.`if_display_to_examinee` , t.`if_auto_next_question` , t.`if_shuffle_selection_options` , t.`no_ad_enabled` , t.`camera_enabled` , t.`hide_rank` , t.`auth_user` , t.`if_validate_content` , t.`time_per_question` , t.`wx_group_enabled` , t.`open_limit_enabled` , t.`open_limit_times` , t.`collect_wrong_answer` , t.`two_options_required` , t.`illustration_uri` , t.`handwriting_signature_enabled` , t.`record_screen_allowed` , t.`if_ai_scoring` , t.`client` , t.`network_limit_enabled` , t.`selected_wifi_list` FROM `examination` t WHERE t.`id` = ? LIMIT 1 
[18:01:50:892] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 167785(Integer)
[18:01:50:906] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:50:907] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.* , t2.name as examinationName , t2.creater_id as createrId , t2.create_time as createTime , t2.camera_enabled as cameraEnabled , t2.illustration_relative_url FROM `examination_instance` t LEFT JOIN examination t2 ON t.examination_id = t2.id WHERE t.examination_id = ? AND ( (t.deleted_by_admin is null or t.deleted_by_admin =false) ) 
[18:01:50:908] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 147157(Integer)
[18:01:50:927] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 2
[18:01:56:794] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /examinationInstance/getCountResult, authentication required: false
[18:01:56:794] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@5e539726 for /examinationInstance/getCountResult
[18:01:56:795] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /examinationInstance/getCountResult
[18:01:56:809] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT count(*) FROM `examination_instance` t WHERE examination_id = ? AND deleted_by_admin = ? AND company_id = ? 
[18:01:56:810] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169000(Integer), false(Boolean), 0(Integer)
[18:01:56:831] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:56:831] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:01:56:832] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:01:56:882] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /examinationInstance/exportDetailRankListOfExamination, authentication required: false
[18:01:56:882] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@5e539726 for /examinationInstance/exportDetailRankListOfExamination
[18:01:56:882] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /examinationInstance/exportDetailRankListOfExamination
[18:01:56:884] [DEBUG] - com.taurus.examinationassistant.controller.ExaminationInstanceController.exportDetailRankListOfExamination(ExaminationInstanceController.java:1359) - 全民考试助手-导出某场考试所有的考生答题详情，对错情况等，按时间顺序排列，不去重，无排名
[18:01:56:903] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT count(*) FROM `examination_instance` t WHERE examination_id = ? AND deleted_by_admin = ? AND company_id = ? 
[18:01:56:903] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169000(Integer), false(Boolean), 0(Integer)
[18:01:56:918] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:01:56:928] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/examinationInstance/exportDetailRankListOfExamination，出现异常:java.lang.NullPointerException；
[18:01:56:933] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","User-Agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.06.2503300 MicroMessenger/8.0.5 Language/zh_CN webview/","X-Forwarded-Proto":"http","Referer":"https://servicewechat.com/wxa38e954efdceffbe/devtools/page-frame.html","X-Forwarded-Host":"localhost:8000","Accept-Encoding":"gzip, deflate","userId":"37377","X-Forwarded-Port":"8000","accept":"*/*","token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJ5aWRhbyIsImV4cCI6MTc1MDUwMDA2MSwiaWF0IjoxNzQ5ODk1MjYxLCJ1c2VySWQiOjM3Mzc3fQ.wbtDVtksyPWNMXtUL8TslC62vzIcqIAOtRwDEscJMgo","Forwarded":"proto=http;host=\"localhost:8000\";for=\"127.0.0.1:60811\"","host":"**************:8081","client":"eteamobile","X-Forwarded-For":"127.0.0.1"}
[18:01:56:933] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"beginDate":"","companyId":"0","endDate":"","createrId":"37377","examinationId":"169000"}
[18:01:56:934] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：java.lang.NullPointerException，异常信息：null，
[18:01:59:428] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /version/getSystemSettings, authentication required: false
[18:01:59:428] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@5e539726 for /version/getSystemSettings
[18:01:59:428] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /version/getSystemSettings
[18:01:59:429] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /access/getTokenStr, authentication required: false
[18:01:59:429] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@5e539726 for /access/getTokenStr
[18:01:59:429] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /access/getTokenStr
[18:01:59:429] [DEBUG] - com.taurus.examinationassistant.filter.SqlXssFilter.doFilter(SqlXssFilter.java:109) - Skipping SqlXssFilter for excluded path: /access/getTokenStr
[18:01:59:736] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /weixinAccount/loginWithoutUserInfoAndAutoRegist, authentication required: false
[18:01:59:736] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@5e539726 for /weixinAccount/loginWithoutUserInfoAndAutoRegist
[18:01:59:736] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /weixinAccount/loginWithoutUserInfoAndAutoRegist
[18:02:00:081] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /universalProductOrderRelation/getProductOrderRelation, authentication required: false
[18:02:00:081] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@5e539726 for /universalProductOrderRelation/getProductOrderRelation
[18:02:00:081] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /universalProductOrderRelation/getProductOrderRelation
[18:02:59:503] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /**************:60171
[18:03:00:107] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /**************:60172
[18:03:00:154] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /**************:60170
[18:03:56:146] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[18:03:56:167] [DEBUG] - com.taurus.examinationassistant.filter.AdminAuthFilter.destroy(AdminAuthFilter.java:131) - AdminAuthFilter销毁
[18:03:56:167] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[18:03:56:168] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Destroying Spring FrameworkServlet 'dispatcherServlet'
[18:03:56:450] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-2-29
[18:03:56:450] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-2-7
[18:03:56:458] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-2-3
[18:03:56:455] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-2-9
[18:03:56:455] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-2-25
[18:03:56:458] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-2-8
[18:03:56:450] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-2-2
[18:03:56:450] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-2-4
[18:03:56:449] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-2-21
[18:03:56:449] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-2-13
[18:03:56:458] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-2-12
[18:03:56:459] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-2-5
[18:03:56:459] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-2-20
[18:03:56:455] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-2-14
[18:03:56:449] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-2-17
[18:03:56:449] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-2-28
[18:03:56:458] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-2-1
[18:03:56:457] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-2-16
[18:03:56:457] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-2-32
[18:03:56:457] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-2-24
[18:03:56:540] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[18:03:56:593] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[18:03:57:079] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-1} closing ...
[18:03:57:104] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:03:57:107] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:03:57:107] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:03:57:107] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:03:57:107] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:03:57:107] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:03:57:107] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:03:57:108] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:03:57:108] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:03:57:108] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:03:57:108] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:03:57:108] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:03:57:113] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-1} closed
[18:03:58:710] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[18:03:59:908] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$AutoConfiguredMapperScannerRegistrar.registerBeanDefinitions(MybatisAutoConfiguration.java:252) - Searching for mappers annotated with @Mapper
[18:03:59:918] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$AutoConfiguredMapperScannerRegistrar.registerBeanDefinitions(MybatisAutoConfiguration.java:264) - Using auto-configuration base package 'com.taurus.examinationassistant'
[18:04:00:663] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[18:04:00:717] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[18:04:00:718] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[18:04:00:718] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[18:04:00:718] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[18:04:00:718] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[18:04:00:718] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[18:04:00:718] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[18:04:00:718] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[18:04:00:718] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[18:04:00:718] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[18:04:00:718] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[18:04:00:718] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[18:04:00:775] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[18:04:00:959] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[18:04:00:961] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[18:04:01:157] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[18:04:01:164] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[18:04:01:166] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[18:04:01:393] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[18:04:01:397] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[18:04:01:398] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[18:04:01:399] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[18:04:01:400] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[18:04:01:401] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[18:04:01:401] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[18:04:01:403] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[18:04:01:403] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[18:04:01:502] [DEBUG] - io.undertow.server.session.InMemorySessionManager.registerSessionListener(InMemorySessionManager.java:268) - Registered session listener io.undertow.servlet.core.SessionListenerBridge@6c62427
[18:04:01:507] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[18:04:02:839] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-2} inited
[18:04:02:840] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[18:04:02:956] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[18:04:02:957] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QiyewxInterfaceLicenseOrderMapper对应的Mapper
[18:04:03:008] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyFunctionRoleMapper对应的Mapper
[18:04:03:012] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserReportRelationMapper对应的Mapper
[18:04:03:016] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QrcodeScanEntryMapper对应的Mapper
[18:04:03:020] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SysLogMapper对应的Mapper
[18:04:03:023] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.TemplateMapper对应的Mapper
[18:04:03:026] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WeixinAccountMapper对应的Mapper
[18:04:03:028] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AppMapper对应的Mapper
[18:04:03:032] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationInstanceMapper对应的Mapper
[18:04:03:035] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.OssFileMapper对应的Mapper
[18:04:03:039] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookUserRecordMapper对应的Mapper
[18:04:03:042] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookMapper对应的Mapper
[18:04:03:046] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.OrdersMapper对应的Mapper
[18:04:03:049] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UniversalProductOrderRelationMapper对应的Mapper
[18:04:03:052] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionSummaryMapper对应的Mapper
[18:04:03:055] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyJobGradeMapper对应的Mapper
[18:04:03:057] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookPracticeSummaryMapper对应的Mapper
[18:04:03:063] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationWxGidMapper对应的Mapper
[18:04:03:066] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentSubjectCategoryMapper对应的Mapper
[18:04:03:068] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationExamineeSnapshotMapper对应的Mapper
[18:04:03:071] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PassRaceGameMapper对应的Mapper
[18:04:03:074] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongQuestionBookMapper对应的Mapper
[18:04:03:077] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionFavoriteMapper对应的Mapper
[18:04:03:082] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.HomepageUserFollowedMapper对应的Mapper
[18:04:03:085] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyRegisterFormFlowRecordMapper对应的Mapper
[18:04:03:088] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MessageTemplateMapper对应的Mapper
[18:04:03:091] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentSubjectOfUserMapper对应的Mapper
[18:04:03:094] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CategoryMapper对应的Mapper
[18:04:03:097] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AppGroupUserMapper对应的Mapper
[18:04:03:101] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserExaminationPerformanceReportMapper对应的Mapper
[18:04:03:111] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarStageEventMapper对应的Mapper
[18:04:03:115] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionBankQuestionMapper对应的Mapper
[18:04:03:118] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.NanxiangRegisterMapper对应的Mapper
[18:04:03:120] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QiyewxUserAccountLicenseMapper对应的Mapper
[18:04:03:122] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarUserMapper对应的Mapper
[18:04:03:125] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ManualServiceRecordMapper对应的Mapper
[18:04:03:127] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyMapper对应的Mapper
[18:04:03:131] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteUserSignatureMapper对应的Mapper
[18:04:03:135] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookGradeMapper对应的Mapper
[18:04:03:138] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookPracticeProcessMapper对应的Mapper
[18:04:03:141] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PointConsumerationMapper对应的Mapper
[18:04:03:144] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongQuestionBookReviewProcessMapper对应的Mapper
[18:04:03:147] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookExamineeMapper对应的Mapper
[18:04:03:150] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.FileAccessPermissionMapper对应的Mapper
[18:04:03:153] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookQuestionMapper对应的Mapper
[18:04:03:155] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PointUserMapper对应的Mapper
[18:04:03:159] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AnnualReportMapper对应的Mapper
[18:04:03:162] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongQuestionBookQuestionMapper对应的Mapper
[18:04:03:166] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookSummaryMapper对应的Mapper
[18:04:03:170] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentSubjectMapper对应的Mapper
[18:04:03:174] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.FreePracticeOfUserMapper对应的Mapper
[18:04:03:178] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MessageScheduleMapper对应的Mapper
[18:04:03:184] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductIntroductionMapper对应的Mapper
[18:04:03:187] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionAttachmentMapper对应的Mapper
[18:04:03:191] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PassRaceStageMapper对应的Mapper
[18:04:03:194] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentSubjectAccessInfoMapper对应的Mapper
[18:04:03:201] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperTagMapper对应的Mapper
[18:04:03:210] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UniversalOrderMapper对应的Mapper
[18:04:03:216] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyDepartmentMapper对应的Mapper
[18:04:03:223] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MessagePushSubscriptionMapper对应的Mapper
[18:04:03:226] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.GameMapper对应的Mapper
[18:04:03:229] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongQuestionBookReviewSummaryMapper对应的Mapper
[18:04:03:232] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteContentAccessMapper对应的Mapper
[18:04:03:234] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentUnlockMapper对应的Mapper
[18:04:03:236] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteOrderMapper对应的Mapper
[18:04:03:238] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationStartupMapper对应的Mapper
[18:04:03:240] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentProductOfUserMapper对应的Mapper
[18:04:03:242] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.GameQuestionRandomRuleMapper对应的Mapper
[18:04:03:244] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteFavoriteMapper对应的Mapper
[18:04:03:246] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.TaxSheetApplicationMapper对应的Mapper
[18:04:03:250] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SysRoleMapper对应的Mapper
[18:04:03:255] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductOrderRelationCountMapper对应的Mapper
[18:04:03:258] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookFavoriteMapper对应的Mapper
[18:04:03:260] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AppStartNoticeReadUserMapper对应的Mapper
[18:04:03:262] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyRegisterFormMapper对应的Mapper
[18:04:03:265] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationGroupExaminationMapper对应的Mapper
[18:04:03:267] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserOfYncxMapper对应的Mapper
[18:04:03:269] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationFavoriteMapper对应的Mapper
[18:04:03:271] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserDeviceBindingMapper对应的Mapper
[18:04:03:274] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationMapper对应的Mapper
[18:04:03:278] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserFormFlowRecordMapper对应的Mapper
[18:04:03:281] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MessagePlanMapper对应的Mapper
[18:04:03:284] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserExaminationInstanceReportMapper对应的Mapper
[18:04:03:286] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SmsMessageMapper对应的Mapper
[18:04:03:288] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PointDetailMapper对应的Mapper
[18:04:03:290] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MultipleMarkingExaminationMapper对应的Mapper
[18:04:03:292] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.DiscountMapper对应的Mapper
[18:04:03:295] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ViewPerformanceWithoutAdTransactionMapper对应的Mapper
[18:04:03:298] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsitePaperInstanceMapper对应的Mapper
[18:04:03:308] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PermissionMapper对应的Mapper
[18:04:03:311] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionExampleDeletedMapper对应的Mapper
[18:04:03:315] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyGroupUserMapper对应的Mapper
[18:04:03:317] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.TaxIdentityInfoMapper对应的Mapper
[18:04:03:320] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.InviteRelationMapper对应的Mapper
[18:04:03:323] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserFormMapper对应的Mapper
[18:04:03:326] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PermissionAuthorizationMapper对应的Mapper
[18:04:03:329] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserMapper对应的Mapper
[18:04:03:333] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsitePaperInstanceProcessMapper对应的Mapper
[18:04:03:336] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.NameListMapper对应的Mapper
[18:04:03:338] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductCountTransactionMapper对应的Mapper
[18:04:03:341] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookTagMapper对应的Mapper
[18:04:03:343] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserViewPerformanceWithoutAdMapper对应的Mapper
[18:04:03:345] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperPraiseMapper对应的Mapper
[18:04:03:348] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongBookMapper对应的Mapper
[18:04:03:351] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationExamineeMapper对应的Mapper
[18:04:03:354] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.GameResultMapper对应的Mapper
[18:04:03:359] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationTagMapper对应的Mapper
[18:04:03:361] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarUserProfileMapper对应的Mapper
[18:04:03:363] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationInstanceProcessMapper对应的Mapper
[18:04:03:366] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductOrderRelationMapper对应的Mapper
[18:04:03:368] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookPraiseMapper对应的Mapper
[18:04:03:373] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MultipleMarkingExaminationInstanceMapper对应的Mapper
[18:04:03:377] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductMapper对应的Mapper
[18:04:03:392] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperQuestionMapper对应的Mapper
[18:04:03:397] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WxMiniprogramSubscribeMessageOfEteaMapper对应的Mapper
[18:04:03:400] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AppStartNoticeMapper对应的Mapper
[18:04:03:402] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.VersionMapper对应的Mapper
[18:04:03:406] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserSummaryMapper对应的Mapper
[18:04:03:409] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarMapper对应的Mapper
[18:04:03:413] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentClassificationInfoMapper对应的Mapper
[18:04:03:417] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionMapper对应的Mapper
[18:04:03:454] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PointRuleMapper对应的Mapper
[18:04:03:458] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteWithdrawMapper对应的Mapper
[18:04:03:460] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QiyewxOrderMapper对应的Mapper
[18:04:03:463] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CustomerRequirementMapper对应的Mapper
[18:04:03:466] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteOrderRelationMapper对应的Mapper
[18:04:03:468] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserCompanyMapper对应的Mapper
[18:04:03:471] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperMapper对应的Mapper
[18:04:03:474] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductIntroductionMediaMapper对应的Mapper
[18:04:03:476] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationNameListMapper对应的Mapper
[18:04:03:478] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SysUserRoleMapper对应的Mapper
[18:04:03:480] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionCompositionChildMapper对应的Mapper
[18:04:03:483] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.NameListDetailMapper对应的Mapper
[18:04:03:485] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserOfProductMapper对应的Mapper
[18:04:03:488] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyUserGroupMapper对应的Mapper
[18:04:03:490] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarUserMilestoneMapper对应的Mapper
[18:04:03:492] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SystemMessageMapper对应的Mapper
[18:04:03:494] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.OssFileAudioMapper对应的Mapper
[18:04:03:495] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionRandomRangeMapper对应的Mapper
[18:04:03:497] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationInstanceEventMapper对应的Mapper
[18:04:03:499] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PracticePushScheduleMapper对应的Mapper
[18:04:03:502] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperFavoriteMapper对应的Mapper
[18:04:03:507] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionRandomMapper对应的Mapper
[18:04:03:510] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PassRaceResultMapper对应的Mapper
[18:04:03:512] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.556秒
[18:04:08:567] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[18:04:08:599] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[18:04:08:595] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[18:04:08:602] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[18:04:08:690] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@558507254 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2de7a28c, L:/**************:62532 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@2f8ddeaf[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[18:04:08:689] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1037990156 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x4e240392, L:/**************:62531 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@331980c7[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[18:04:08:694] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connectPubSub$3(ClientConnectionsEntry.java:234) - new pubsub connection created: RedisPubSubConnection@895471336 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x4ce9cfa3, L:/**************:62530 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@3b982633[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[18:04:08:696] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for 47.111.231.172/47.111.231.172:6379
[18:04:08:709] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[18:04:08:711] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[18:04:08:763] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@972090945 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x525f17b2, L:/**************:62533 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@3737170f[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[18:04:08:763] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@356574689 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x8b2cfb0b, L:/**************:62534 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@1b871939[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[18:04:08:781] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[18:04:08:781] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[18:04:08:827] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@800112000 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x00a6b3f9, L:/**************:62538 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@21775606[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[18:04:08:826] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@459700390 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xf0083495, L:/**************:62537 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@15a185ba[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[18:04:08:840] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[18:04:08:840] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[18:04:08:874] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1466830976 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x57ac699c, L:/**************:62541 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@59a9e41d[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[18:04:08:877] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1602300353 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xd89e4663, L:/**************:62542 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@5146cc41[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[18:04:08:883] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[18:04:08:884] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[18:04:08:920] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@12895221 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x4ac93cd1, L:/**************:62547 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@e033d9d[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[18:04:08:920] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@979678538 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xedd0890f, L:/**************:62548 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@34a35c76[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[18:04:08:936] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[18:04:08:936] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[18:04:08:972] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1983228686 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x30a95b84, L:/**************:62550 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@78f24bb8[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[18:04:08:972] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@151327218 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xb37befb7, L:/**************:62549 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@7c2fea2[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[18:04:08:979] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[18:04:08:979] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[18:04:09:016] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@574826972 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xe23d5e1d, L:/**************:62554 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@2c84c3e4[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[18:04:09:016] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@2035921010 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x09e8c455, L:/**************:62553 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@779e5129[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[18:04:09:022] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[18:04:09:022] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[18:04:09:059] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1435769863 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xfd6a7486, L:/**************:62556 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@50820dd8[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[18:04:09:068] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[18:04:09:070] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1643329345 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xcd8e8ae9, L:/**************:62555 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@7622273b[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[18:04:09:073] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[18:04:09:101] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@925241980 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2737f241, L:/**************:62560 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@72248afc[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[18:04:09:102] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1067951512 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xa608b382, L:/**************:62559 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@7aa52c08[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[18:04:09:103] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[18:04:09:106] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[18:04:09:131] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@532188817 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xa6608332, L:/**************:62561 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@5aba1302[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[18:04:09:135] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[18:04:09:139] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@697286034 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x02457204, L:/**************:62562 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@19444fab[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[18:04:09:141] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[18:04:09:162] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@625530608 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x9e484a09, L:/**************:62563 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@604a5c3d[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[18:04:09:164] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[18:04:09:175] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@266049821 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x5d43a3c3, L:/**************:62564 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@53da1f6b[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[18:04:09:176] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[18:04:09:199] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1570143216 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x91a2925a, L:/**************:62565 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@1894ee26[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[18:04:09:211] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1434010289 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xf4cfbe63, L:/**************:62566 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@107bc67a[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[18:04:09:213] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for 47.111.231.172/47.111.231.172:6379
[18:04:11:697] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.AccessController:
	{GET [/access/getTokenStr]}: getToken(Integer,HttpServletRequest)
	{GET [/access/heartbeat]}: heartbeat()
	{GET [/access/getTokenUnlimit]}: getTokenUnlimit(Integer,HttpServletRequest)
	{GET [/access/getTokenByTime]}: getTokenByTime(Integer,Long,HttpServletRequest)
[18:04:11:704] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ActivityController:
	{ [/activity/getActiveActivities]}: getActiveActivities(String)
[18:04:11:704] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.AnnualReportController:
	{GET [/annual/getReport]}: getEntity(Integer)
[18:04:11:704] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.AppStartNoticeController:
	{GET [/appStartNotice/getCurrentNoticeOfUser]}: getCurrentNoticeOfUser(String,Integer)
	{GET [/appStartNotice/getCurrentNotice]}: getCurrentNotice(String)
[18:04:11:705] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.AppStartNoticeReadUseController:
	{GET [/appStartNoticeReadUser/save]}: save(Integer,Integer)
[18:04:11:706] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CompanyController:
	{POST [/company/createCompany]}: createCompany(JSONObject)
	{GET [/company/getCompanyInfoByUserId]}: getCompanyInfoByUserId(int)
	{POST [/company/createDepartment]}: createDepartment(JSONObject)
	{POST [/company/createJobGrade]}: createJobGrade(JSONObject)
	{POST [/company/createFunctionRole]}: createFunctionRole(JSONObject)
	{GET [/company/deleteCompany]}: deleteCompany(int)
	{GET [/company/deleteDepartment]}: deleteDepartment(int)
	{GET [/company/deleteDepartmentCarefully]}: deleteDepartmentCarefully(Integer,Integer)
	{GET [/company/deleteJobGrade]}: deleteJobGrade(int)
	{GET [/company/deleteJobGradeCarefully]}: deleteJobGradeCarefully(Integer,Integer)
	{GET [/company/deleteFunctionRole]}: deleteFunctionRole(int)
	{GET [/company/deleteFunctionRoleCarefully]}: deleteFunctionRoleCarefully(Integer,Integer)
	{POST [/company/modifyCompany]}: modifyCompany(JSONObject)
	{GET [/company/modifyCompanyName]}: modifyCompanyName(String,Integer)
	{POST [/company/modifyDepartment]}: modifyDepartment(JSONObject)
	{POST [/company/modifyJobGrade]}: modifyJobGrade(JSONObject)
	{POST [/company/modifyFunctionRole]}: modifyFunctionRole(JSONObject)
	{GET [/company/getCompanyInfo]}: getCompanyInfo(Integer)
	{GET [/company/getCompanyInfoById]}: getCompanyInfoWrapperById(int)
	{GET [/company/getCompanyInfoByCodeOrName]}: getCompanyInfoByCodeOrName(String)
	{GET [/company/getCompanyInfoListByName]}: getCompanyInfoListByName(String)
	{GET [/company/getAllDepartmentOfCompany]}: getAllDepartmentOfCompany(Integer,Integer)
	{GET [/company/getNGradeDepartment]}: getLevelDepartmentOfLessThanOrEqualDesignativeGrade(Integer,Integer)
	{GET [/company/getAllDepartmentOfCompanyByUserId]}: getAllDepartmentOfCompanyByUserId(Integer)
	{GET [/company/getSubDepartmentExcludingSelf]}: getSubDepartmentExcludingSelf(String)
	{GET [/company/getSubDepartmentIncludingSelf]}: getSubDepartmentIncludingSelf(String)
	{GET [/company/getAllCompanyJobGradeOfCompany]}: getAllCompanyJobGradeOfCompany(int)
	{GET [/company/getAllCompanyInfo]}: getAllCompanyInfo(int)
	{POST [/company/getQiyeweixinCompanyList]}: getQiyeweixinCompanyList(JSONObject)
	{GET [/company/getAllCompanyInfoWith2Grade]}: getAllCompanyInfoWith2Grade(Integer)
	{GET [/company/getAllCompanyFunctionRoleOfCompany]}: getAllCompanyFunctionRoleOfCompany(int)
	{GET [/company/search]}: searchCompanyByKeyword(String)
	{GET [/company/batchCloseAccount]}: batchCloseAccount(String)
	{GET [/company/closeAccount]}: closeAccount(Integer,Integer)
	{GET [/company/getResidualFlow]}: getResidualFlow(Integer)
	{POST [/company/getOrganizationFrameworkInfoByIds]}: getOrganizationFrameworkInfoByIds(JSONObject)
	{POST [/company/modify]}: modify(Company)
[18:04:11:707] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CompanyDepartmentController:
	{GET [/companyDepartment/getCompanyDepartmentList]}: getCompanyDepartmentList(Integer,String)
[18:04:11:708] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CompanyGroupUserController:
	{POST [/companyGroupUser/create]}: createCompanyGroupUser(CompanyGroupUser)
	{POST [/companyGroupUser/batchCreate]}: batchCreateCompanyGroupUser(List)
	{POST [/companyGroupUser/update]}: updateCompanyGroupUser(CompanyGroupUser)
	{GET [/companyGroupUser/delete]}: deleteCompanyGroupUser(Integer,String)
	{GET [/companyGroupUser/getGroupsByUserId]}: getGroupsByUserId(Integer)
	{GET [/companyGroupUser/getUsersByGroupId]}: getUsersByGroupId(Integer)
	{GET [/companyGroupUser/checkUserInGroup]}: checkUserInGroup(Integer,Integer)
[18:04:11:708] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CompanyUserGroupController:
	{GET [/companyUserGroup/list]}: getGroupList(Integer,Integer,String,String)
	{POST [/companyUserGroup/create]}: createGroup(JSONObject)
	{GET [/companyUserGroup/detail]}: getGroupDetail(Integer)
	{POST [/companyUserGroup/update]}: updateGroup(JSONObject)
	{GET [/companyUserGroup/delete]}: deleteGroup(Integer)
[18:04:11:709] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentClassificationInfoController:
	{ [/contentClassification/getPracticeContentOfUnlock]}: getPracticeContentOfUnlock()
	{ [/contentClassification/getUnlockedPracticeContentOfUser]}: getUnlockedPracticeContentOfUser(Integer)
	{ [/contentClassification/getPracticeContentOfSubject]}: getPracticeContentOfSubject(Integer,Integer)
[18:04:11:716] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentProductOfUserController:
	{GET [/contentProductOfUser/getProductListOfUser]}: getProductListOfUser(Integer)
[18:04:11:718] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentSubjectCategoryController:
	{GET [/contentSubjectCategory/getCategoryListOfSubject]}: getCategoryListOfSubject(Integer)
	{GET [/contentSubjectCategory/getSubjectListOfCategory]}: getSubjectListOfCategory(Integer)
	{GET [/contentSubjectCategory/getPathListOfSubject]}: getPathListOfSubject(Integer)
	{GET [/contentSubjectCategory/getNLevelCategoryWithContentNum]}: getLevelCategoriesOfLessThanOrEqualDesignativeLevel(String,Integer,Integer)
	{POST [/contentSubjectCategory/update]}: update(JSONArray)
	{GET [/contentSubjectCategory/delete]}: delete(Integer,Integer)
	{POST [/contentSubjectCategory/save]}: save(JSONArray)
[18:04:11:719] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentSubjectController:
	{POST [/contentSubject/createSubject]}: createSubject(JSONObject)
	{GET [/contentSubject/getContentSubjectByContent]}: getContentSubjectByContent(String,Integer)
	{GET [/contentSubject/getAllContentSubject]}: getSubjectInfoList(String,Integer,Integer,Integer)
	{GET [/contentSubject/getSubjectListOfProduct]}: getSubjectListOfProduct(String)
	{GET [/contentSubject/getAllContentSubjectWithUserSelection]}: getAllContentSubjectWithUserSelection(Integer,String)
	{GET [/contentSubject/getSubjectWrapperBySubjectId]}: getSubjectWrapperBySubjectId(Integer)
	{GET [/contentSubject/getSubjectWrapper]}: getSubjectWrapper(Integer)
	{GET [/contentSubject/getSubjectWrapperWithFavoriteInfo]}: getSubjectWrapperWithFavoriteInfo(Integer,Integer)
	{POST [/contentSubject/modifySubject]}: modifySubject(JSONObject)
	{GET [/contentSubject/delete]}: delete(Integer)
[18:04:11:719] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentSubjectOfUserController:
	{POST [/contentSubjectOfUser/saveContentSubjectsOfUser]}: saveContentSubjectsOfUser(JSONObject)
	{GET [/contentSubjectOfUser/getContentSubjectListOfUser]}: getContentSubjectListOfUser(Integer,String,Integer,Integer,Integer)
[18:04:11:720] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentSubjectSummaryController:
	{GET [/contentSubjectSummary/getSummary]}: getSummary(Integer)
[18:04:11:720] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentUnlockController:
	{POST [/contentUnlock/save]}: save(JSONObject)
[18:04:11:720] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CryptographyController:
	{ [/cryptography/encrypt]}: encrypt(String)
	{ [/cryptography/decrypt]}: decrypt(String)
[18:04:11:720] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CustomerRequirementController:
	{POST [/cs/createRequirement]}: save(JSONObject)
[18:04:11:726] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.DataResolverController:
	{GET [/dataResolver/setSelectionChildType]}: setSelectionChildType(int)
	{GET [/dataResolver/reCalculateQuestionScore]}: reCalculateQuestionScore(int,Integer,Integer,String)
	{GET [/dataResolver/reCalculateExaminationInstanceByQuestionId]}: reCalculateExaminationInstance(int,int)
	{GET [/dataResolver/reCalculateExaminationInstance]}: reCalculateExaminationInstance(int,Integer,String)
	{GET [/dataResolver/reCreateCompletionOptions]}: reCreateCompletionOptions(Integer,Integer,Integer,String)
	{GET [/dataResolver/checkDataMigrationFromFreeVersion]}: checkDataMigrationFromFreeVersion(int)
	{GET [/dataResolver/migrateDataFromFreeVersionToEnterpriseVersion]}: migrateDataFromFreeVersionToEnterpriseVersion(int,int)
	{GET [/dataResolver/deleteExaminationIncludingWrongRandomQuestion]}: deleteExaminationIncludingWrongRandomQuestion()
	{GET [/dataResolver/configUserSysUserRole]}: configUserSysUserRole()
	{GET [/dataResolver/recaculateExaminationInstanceBecauseOfNullScore]}: recaculateExaminationInstanceBecauseOfNullScore()
	{GET [/dataResolver/deleteInValidExamination]}: deleteInValidExamination()
	{GET [/dataResolver/deleteExaminationInstanceAndProcessOfEtea]}: deleteExaminationInstanceAndProcessOfEtea(String,String)
	{GET [/dataResolver/deleteInvalidSubscirbeMessage]}: deleteInvalidSubscirbeMessage(String)
	{GET [/dataResolver/clearJSONErrorStr]}: clearJSONErrorStr(Integer)
	{GET [/dataResolver/recaculateExcerciseBookInfo]}: recaculateExcerciseBookInfo()
	{GET [/dataResolver/deleteInvalidQuestion]}: deleteInvalidQuestion()
	{GET [/dataResolver/deleteInvalidPaper]}: deleteInvalidPaper()
	{GET [/dataResolver/deleteExerciseBookAndRelation]}: deleteInvalidExerciseBookAndRelation()
	{GET [/dataResolver/deleteInvalidSystemMessage]}: deleteInvalidSystemMessage(String,String,String)
	{GET [/dataResolver/deleteExaminationInstance]}: deleteExaminationInstance()
	{GET [/dataResolver/handleDeleteQuestionsEvent]}: handleDeleteQuestionsEvent()
	{GET [/dataResolver/restoreExerciseBookByProcess]}: restoreExerciseBookByProcess()
	{GET [/dataResolver/clearRedisData]}: clearRedisData(String)
	{GET [/dataResolver/clearAccountPermently]}: clearAccountPermently(Integer,Integer)
	{GET [/dataResolver/generateQuestionJsonFileOfExamination]}: generateQuestionJsonFileOfExamination(Integer,Integer,Integer)
	{GET [/dataResolver/generateHotFileListOfCDN]}: generateHotFileListOfCDN(Integer,Integer)
	{GET [/dataResolver/resizeOssPicFile]}: resizeOssPicFile(String)
	{GET [/dataResolver/deleteQuestionAndRelatedRecords]}: deleteQuestionAndRelatedRecords(String,String)
	{GET [/dataResolver/deleteWrongQuestionBook]}: deleteWrongQuestionBook(String,String)
	{GET [/dataResolver/transformCompletionQuestion]}: transformCompletionQuestion(Integer,Integer)
	{GET [/dataResolver/restoreExerciseBook]}: restoreExerciseBook(Integer)
	{GET [/dataResolver/replaceDepartmentId]}: replaceDepartmentId(Integer)
	{GET [/dataResolver/generateAnnualReport]}: generateAnnualReport(Integer,Integer,String,String)
	{GET [/dataResolver/insertCompanyUser]}: insertCompanyUser(Integer)
	{GET [/dataResolver/getUnCompleteQuestion]}: getUnCompleteQuestion(Integer)
	{GET [/dataResolver/deleteDepartments]}: deleteDepartments()
	{POST [/dataResolver/processQuestionAndAnswers], consumes [multipart/form-data]}: processQuestionAndAnswers(HttpServletRequest,HttpServletResponse)
	{GET [/dataResolver/deletedRandomRangeQuestionWithQuestionDeletedState]}: deletedRandomRangeQuestionWithQuestionDeletedState(Integer,Integer)
	{GET [/dataResolver/clearExaminationCache]}: reloadExaminationCache(Integer,Integer)
[18:04:11:734] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.DownloadProxyController:
	{POST [/download/proxy]}: proxyDownload(HttpServletRequest,JSONObject)
[18:04:11:736] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationController:
	{GET [/examination/examination]}: getExaminationById(int)
	{POST [/examination/create]}: createExamination(JSONObject)
	{POST [/examination/createExaminationWithContentCheck]}: createExaminationWithContentCheck(JSONObject)
	{POST [/examination/createIncludingExamineeSelect]}: createExaminationIncludingExamineeSelect(JSONObject)
	{POST [/examination/createFixedExamination]}: createFixedExamination(JSONObject)
	{POST [/examination/createRandomExaminationIncludingExamineeSelect]}: createRandomExaminationIncludingExamineeSelect(JSONObject)
	{POST [/examination/createRandomExamination]}: createRandomExamination(JSONObject)
	{GET [/examination/delete]}: deleteExamination(String)
	{GET [/examination/examinationWrapperListOfCreater]}: getExaminationWrapperListOfCreater(int,int,int,int)
	{GET [/examination/examinationInViewListOfCreater]}: getExaminationInViewListOfCreater(Integer,Integer,Integer,Integer)
	{GET [/examination/getExaminationInViewListOfCreater]}: getExaminationInViewList(Integer,Integer,Integer,int)
	{GET [/examination/getExaminationInViewListAndTotalNumOfCreater]}: getExaminationInViewListAndTotalNumOfCreater(int,int,int,int)
	{GET [/examination/getRecommendExaminations]}: getRecommendExaminations(Integer,Integer,Integer,Integer)
	{GET [/examination/getRecommendExaminationsByProduct]}: getRecommendExaminationsByProduct(Integer,String)
	{POST [/examination/getExaminationInViewListOfCreaterInCompanyByTags]}: getExaminationInViewListOfCreaterInCompanyByTags(JSONObject)
	{POST [/examination/getExaminationInViewListOfCompanyByTags]}: getExaminationInViewListOfCompanyByTags(JSONObject)
	{GET [/examination/ongoingExaminationList]}: getOngoingExaminationList()
	{GET [/examination/getExaminationAndPaperById]}: getExaminationAndPaperById(int)
	{POST [/examination/getExaminationAndPaperByColumns]}: getExaminationAndPaperByColumns(JSONObject)
	{POST [/examination/getExaminationAndPaperByColumnsAfterEncode]}: getExaminationAndPaperByColumnsAfterEncode(JSONObject)
	{POST [/examination/getExaminationDetail]}: getExaminationDetail(JSONObject)
	{GET [/examination/examinationByCode]}: getExaminationByCode(String)
	{GET [/examination/getExaminationListByPaperId]}: getExaminationListByPaperId(Integer)
	{GET [/examination/getExaminationListByKeyword]}: getExaminationListByKeyword(String)
	{GET [/examination/getExaminationListOfCreater]}: getExaminationListOfCreater(Integer,Integer,Integer,Integer)
	{GET [/examination/idByCode]}: getExaminationIdByCode(String)
	{POST [/examination/edit]}: editExamination(JSONObject)
	{GET [/examination/examinationWrapper]}: getExaminationWrapperById(Integer)
	{GET [/examination/getExaminationWrapperSecure]}: getExaminationWrapperSecure(Integer,Integer)
	{GET [/examination/getExaminationWrapperStructure]}: getExaminationWrapperStructure(Integer,Integer)
	{POST [/examination/updateExaminationWrapperStructure]}: updateExaminationWrapperStructure(JSONObject)
	{GET [/examination/getExaminationAndPaperQuestionList]}: getExaminationAndPaperQuestionList(Integer)
	{GET [/examination/getExaminationAndPaperQuestionListAfterEncoded]}: getExaminationAndPaperQuestionListAfterEncoded(Integer)
	{GET [/examination/examinationWrapperWithRedLock]}: examinationWrapperWithRedLock(Integer,String)
	{GET [/examination/suspend]}: suspendExamination(Integer)
	{GET [/examination/resume]}: resumeExamination(Integer)
	{GET [/examination/changeExamineeDisplay]}: changeExamineeDisplay(Integer,Boolean)
	{GET [/examination/rename]}: renameExamination(int,int,String)
	{GET [/examination/renameExamCode]}: renameExamCode(Integer,String)
	{GET [/examination/modifyCode]}: modifyCode(Integer,String)
	{GET [/examination/configAdvancedOptions]}: configAdvancedOptions(String)
	{POST [/examination/saveAdvancedOptions]}: saveAdvancedOptions(JSONObject)
	{GET [/examination/checkIfExceedExaminationTimesLimit]}: checkIfExceedExaminationTimesLimit(Integer,Integer)
	{GET [/examination/copy]}: copyExamination(Integer,Integer,Boolean)
	{GET [/examination/transmit]}: transmitExamination(Integer,Integer,Integer)
	{GET [/examination/getLeftTime]}: getLeftFromBeginTime(Integer)
	{POST [/examination/sendQiyeweixinQkkNotice]}: sendQiyeweixinQkkNotice(JSONObject)
	{POST [/examination/update]}: update(Examination)
	{GET [/examination/reset]}: reset(Integer)
[18:04:11:737] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationExamineeController:
	{GET [/examinationExaminee/getExaminationExaminee]}: getExaminationExaminee(Integer)
	{POST [/examinationExaminee/updateExaminationExaminee]}: updateExaminationExaminee(JSONObject)
[18:04:11:737] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationExamineeSnapshotController:
	{GET [/examinationExamineeSnapshot/insertSnapshot]}: insertSnapshot(Integer,Integer,String,String)
	{GET [/examinationExamineeSnapshot/getUserSnapshotListOfExamination]}: getUserSnapshotListOfExamination(Integer,String,Integer,Integer)
	{GET [/examinationExamineeSnapshot/getUserSnapshotList]}: getUserSnapshotList(Integer,String,Integer,Integer)
	{GET [/examinationExamineeSnapshot/exportSnapshotListOfExamination]}: exportSnapshotListOfExamination(Integer)
	{GET [/examinationExamineeSnapshot/delete]}: delete(String)
[18:04:11:738] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationFavoriteController:
	{GET [/examinationFavorite/add]}: addFavorite(int,int)
	{GET [/examinationFavorite/cancel]}: cancelFavorite(int,int)
	{GET [/examinationFavorite/ifFavorite]}: ifFavorite(int,int)
	{GET [/examinationFavorite/getFavoriteList]}: getFavoriteList(int,int,int)
	{GET [/examinationFavorite/getFavoriteListAndNum]}: getFavoriteListAndNum(int,int,int)
[18:04:11:738] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationGroupExaminationController:
	{ [/examinationGroupExamination/list]}: getExaminationGroupExaminationServiceListById(int)
[18:04:11:741] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationInstanceController:
	{POST [/examinationInstance/getExaminationInstanceList]}: getExaminationInstanceList(JSONObject)
	{[GET, POST] [/examinationInstance/exportRankListOfExaminationInCompany]}: exportRankListOfExaminationInCompany(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/examinationInstance/exportNoRankListOfExaminationInCompany]}: exportNoRankListOfExaminationInCompany(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/examinationInstance/exportDetailRankListOfExamination]}: exportDetailRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{GET [/examinationInstance/getExaminationInstanceWrapperById]}: getExaminationInstanceWrapperById(Integer)
	{POST [/examinationInstance/createExaminationInstance]}: createExaminationInstance(JSONObject,HttpServletRequest)
	{POST [/examinationInstance/createExaminationInstanceWithContentCheck]}: createExaminationInstanceWithContentCheck(JSONObject,HttpServletRequest,HttpServletResponse)
	{POST [/examinationInstance/getExaminationInstanceWrapperListOfExaminee]}: getExaminationInstanceWrapperListOfExaminee(JSONObject)
	{GET [/examinationInstance/examinationInstanceWrapperListOfExaminee]}: getExaminationInstanceWrapperListOfExaminee(int,int,int,int)
	{ [/examinationInstance/getExaminationInstanceInfoById]}: getExaminationInstanceInfoById(Integer)
	{POST [/examinationInstance/getRecentExaminationInstanceListWithAverage]}: getRecentExaminationInstanceListWithAverage(JSONObject)
	{GET [/examinationInstance/getExaminationInstanceListWithUserInfoByExaminationId]}: getExaminationInstanceListWithUserInfoByExaminationId(int,int,int,int)
	{GET [/examinationInstance/examinationInstanceWrapperListOfExamination]}: getExaminationInstanceWrapperListOfExamination(String,int,Integer,Integer)
	{GET [/examinationInstance/rankOfExaminationInstance]}: getRankOfExaminationInstance(int,int,int)
	{GET [/examinationInstance/rankListOfExamination]}: getRankListOfExamination(Integer,Integer,Integer,Integer,Integer)
	{GET [/examinationInstance/getTopTenRankListAndTotalExamineeNumOfExamination]}: getTopTenRankListAndTotalExamineeNumOfExamination(int)
	{GET [/examinationInstance/getLatestTenListAndTotalExamineeNumOfExamination]}: getLatestTenListAndTotalExamineeNumOfExamination(Integer)
	{GET [/examinationInstance/rankListOfExaminationByCode]}: getRankListOfExaminationByCode(String)
	{GET [/examinationInstance/getTotalTimes]}: getTotalTimes(Integer,Integer)
	{GET [/examinationInstance/timesOfExaminationOfExaminee]}: getTimesOfExaminationOfExaminee(int,int,String,String)
	{POST [/examinationInstance/batchDelete]}: batchDeleteByExaminee(JSONArray)
	{POST [/examinationInstance/batchDeleteByAdmin]}: batchDeleteByAdmin(JSONArray)
	{GET [/examinationInstance/examinationInstanceStageSummary]}: getExaminationInstanceStageSummary(int,String,String)
	{POST [/examinationInstance/getExaminationInstanceStageSummaryListByTags]}: getExaminationInstanceStageSummaryList(JSONObject)
	{GET [/examinationInstance/examinationInstanceStageSummaryList]}: getExaminationInstanceStageSummaryList(String,String,String,int,int,int)
	{GET [/examinationInstance/examinationInstanceStageSummaryListExcludingExample]}: getExaminationInstanceStageSummaryListExcludingExample(String,String,String,int,int,int)
	{GET [/examinationInstance/getExaminationInstanceStageSummaryListExcludingCreater]}: getExaminationInstanceStageSummaryListExcludingCreater(String,String,String,int,int,int)
	{GET [/examinationInstance/examinationInstanceStageSummaryDetail]}: getExaminationInstanceStageSummaryDetail(int,String,String,Integer,Integer,String)
	{POST [/examinationInstance/getExaminationStageSummaryDetail]}: getExaminationStageSummaryDetail(JSONObject)
	{GET [/examinationInstance/individualScoreListOfExamination]}: getIndividualScoreListOfExamination(int,int,String,String)
	{GET [/examinationInstance/individualScoreList]}: getIndividualScoreList(int,String,String,int)
	{GET [/examinationInstance/getIndividualRankInfo]}: getIndividualRankInfo(Integer,Integer,Integer)
	{GET [/examinationInstance/getIndividualRankInfoWithDuplicateRemoval]}: getIndividualRankInfoWithDuplicateRemoval(Integer,Integer,Integer)
	{GET [/examinationInstance/getExaminationInstanceWrapperAndProcessById]}: getExaminationInstanceWrapperAndProcessById(Integer)
	{POST [/examinationInstance/getInstanceWrapperAndProcessList]}: getInstanceWrapperAndProcessList(JSONObject)
	{GET [/examinationInstance/notApprovedExaminationInstanceList]}: getNotApprovedExaminationInstanceList(Integer,Integer,Integer,Integer)
	{GET [/examinationInstance/getNotApprovedExaminationInstanceListAndLength]}: getNotApprovedExaminationInstanceListAndLength(Integer,Integer,Integer,Integer)
	{POST [/examinationInstance/approveInstance]}: approveInstance(JSONObject)
	{[GET, POST] [/examinationInstance/exportNoRankListOfExamination]}: exportNoRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/examinationInstance/exportRankListOfExamination]}: exportRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{POST [/examinationInstance/getCountResult]}: getCountResult(JSONObject)
	{[GET, POST] [/examinationInstance/exportDetailRankListOfExaminationInCompany]}: exportDetailRankListOfExaminationInCompany(HttpServletRequest,HttpServletResponse)
	{ [/examinationInstance/exportInstanceOfExaminee]}: exportInstanceOfExaminee(HttpServletRequest,HttpServletResponse)
	{GET [/examinationInstance/exportAbsentList]}: exportAbsentList(HttpServletRequest,HttpServletResponse)
	{GET [/examinationInstance/getAbsentList]}: getAbsentList(Integer)
	{GET [/examinationInstance/getUserListOfExamination]}: getUserListOfExamination(Integer)
	{GET [/examinationInstance/getExaminationAnalysisData]}: getAnalysisData(Integer)
	{GET [/examinationInstance/getDistributionOfScore]}: getDistributionOfScore(Integer)
	{GET [/examinationInstance/getDistributionOfDuration]}: getDistributionOfDuration(Integer)
	{POST [/examinationInstance/getExamineeList]}: getExamineeList(JSONObject)
	{GET [/examinationInstance/delete]}: delete(int,String)
	{POST [/examinationInstance/create]}: create(JSONObject,HttpServletRequest,HttpServletResponse)
[18:04:11:743] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationInstanceEventController:
	{POST [/examinationInstanceEvent/getSummaryInfo]}: getSummaryInfo(JSONObject)
	{POST [/examinationInstanceEvent/getExamBehaviorList]}: getExamBehaviorList(JSONObject)
	{POST [/examinationInstanceEvent/insert]}: insert(ExaminationInstanceEvent)
	{POST [/examinationInstanceEvent/getList]}: getList(JSONObject)
[18:04:11:744] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationInstanceProcessController:
	{GET [/examinationInstanceProcess/examinationInstanceProcessWrapperListAndNum]}: examinationInstanceProcessWrapperListAndNum(Integer,Integer,Integer,Integer)
	{GET [/examinationInstanceProcess/wrongQuestionList]}: getWrongQuestionsOfExaminee(int,int,int)
	{GET [/examinationInstanceProcess/rightWrongNumOfExamination]}: getQuestionRightWrongNumOfExamination(int)
	{GET [/examinationInstanceProcess/rightWrongNumOfQuestion]}: getQuestionRightWrongNum(int)
	{GET [/examinationInstanceProcess/getDistributionGroupByAnswerContent]}: getDistributionGroupByAnswerContent(Integer,Integer)
	{GET [/examinationInstanceProcess/getAnalysisOfExamination]}: getAnalysisOfExamination(Integer,Integer,Integer,String)
	{POST [/examinationInstanceProcess/changeQuestionResult]}: changeQuestionResult(JSONObject)
	{GET [/examinationInstanceProcess/exportQuestionAnalysis]}: exportQuestionAnalysis(Integer,HttpServletResponse)
	{POST [/examinationInstanceProcess/performAiScoring]}: performAiScoring(JSONObject)
	{GET [/examinationInstanceProcess/examinationInstanceProcessWrapperList]}: getExaminationInstanceProcessWrapperList(int,Integer,Integer,Integer)
[18:04:11:745] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationNameListController:
	{POST [/examinationNameList/addExaminationNameList]}: addExaminationNameList(JSONObject)
	{POST [/examinationNameList/upInsert]}: upInsert(JSONObject)
	{GET [/examinationNameList/getList]}: getList(Integer,Integer)
[18:04:11:746] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationStartUpController:
	{GET [/examinationStartUp/getCountGroupByUser]}: getCountGroupByUser(Integer,Integer,Integer)
	{GET [/examinationStartUp/add]}: add(Integer,Integer)
	{GET [/examinationStartUp/delete]}: delete(Integer,Integer)
	{GET [/examinationStartUp/getCount]}: getCount(Integer,Integer)
[18:04:11:747] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationTagsController:
	{POST [/examinationTags/createrExamTags]}: createExamTags(JSONObject)
	{ [/examinationTags/getTagsOfExam]}: getTagsOfExam(int)
	{GET [/examinationTags/getAllExamTagsOfCreaterIdInCompany]}: getAllExamTagsOfCreaterIdInCompany(Integer,Integer)
	{GET [/examinationTags/getAllExamTagsExcludingCreaterIdInCompany]}: getAllExamTagsExcludingCreaterIdInCompany(int,int)
	{ [/examinationTags/getAllExamTagsOfCompanyId]}: getAllExamTagsOfCompanyId(int,Boolean)
	{ [/examinationTags/getAllExamTagsOfUserInCompany]}: getAllExamTagsOfUserInCompany(Integer,Integer)
[18:04:11:747] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationWxGidController:
	{POST [/examinationWxGid/addIfNoExist]}: addEntity(ExaminationWxGid)
	{GET [/examinationWxGid/getEntity]}: getEntity(Integer,String)
	{GET [/examinationWxGid/delete]}: delete(Integer,String)
	{GET [/examinationWxGid/getList]}: getList(Integer)
[18:04:11:749] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookController:
	{POST [/exerciseBook/batchUpdate]}: batchUpdate(JSONArray)
	{POST [/exerciseBook/create]}: createExerciseBook(JSONObject)
	{POST [/exerciseBook/createExerciseBookByQuestionTypeAndCategories]}: createExerciseBookByQuestionTypeAndCategories(JSONObject)
	{GET [/exerciseBook/modifyExerciseBook]}: modifyExerciseBook(String,String)
	{GET [/exerciseBook/delete]}: deleteExerciseBook(int)
	{GET [/exerciseBook/deleteExerciseBookAndRelation]}: deleteExerciseBookAndRelation(Integer)
	{GET [/exerciseBook/getRecommendedKeyWords]}: getRecommendedKeyWords()
	{ [/exerciseBook/exerciseBookListOfCreaterIncludingFavorite]}: getExerciseBookListOfCreaterIncludingFavorite(int,int,int,int,int)
	{ [/exerciseBook/exerciseBook]}: getExerciseBookById(int)
	{GET [/exerciseBook/getExampleQuestions]}: getExampleQuestions(Integer)
	{ [/exerciseBook/exerciseBookBeginInfo]}: getExerciseBookBeginInfoById(Integer,Integer)
	{GET [/exerciseBook/getExerciseBookBeginInfoById]}: getKsiteExerciseBookBeginInfoById(Integer,Integer,Integer)
	{GET [/exerciseBook/getExerciseBookIndexPageInfo]}: getExerciseBookIndexPageInfo(Integer)
	{GET [/exerciseBook/getPracticeSummaryInfo]}: getPracticeSummaryInfo(Integer)
	{ [/exerciseBook/exerciseBookWrapper]}: getExerciseBookWrapperById(String)
	{ [/exerciseBook/switchDisplayToExaminee]}: switchDisplayToExaminee(Integer,Boolean)
	{GET [/exerciseBook/advancedExerciseBookWrapper]}: getAdvancedExerciseBookWrapperById(int)
	{POST [/exerciseBook/doExercise]}: doExercise(JSONObject)
	{POST [/exerciseBook/doExerciseSecure]}: doExerciseSecure(JSONObject)
	{GET [/exerciseBook/rename]}: renameExerciseBook(String,int)
	{POST [/exerciseBook/savePracticeSummary]}: savePracticeSummary(JSONObject)
	{GET [/exerciseBook/getCatagoriesAndNum]}: getCatagoriesAndNum(Integer,Integer,String)
	{GET [/exerciseBook/getTagsGroupByDomain]}: getTagsGroupByDomain(Integer,Integer,String)
	{GET [/exerciseBook/getExerciseBookListByCatagory]}: getExerciseBookListByCatagory(Integer,Integer,String,String,String,Integer,Integer)
	{GET [/exerciseBook/getExerciseBookListAndTotalNum]}: getExerciseBookListAndTotalNum(Integer,Integer,String,String,String,Integer,Integer)
	{GET [/exerciseBook/getDailyPracticeResult]}: getDailyPracticeResult(Integer,Integer,Date,Date)
	{GET [/exerciseBook/getRecommendedList]}: getRecommendedList(Integer,Integer,Integer)
	{GET [/exerciseBook/getFavoriteRankList]}: getFavoriteRankList(String,Integer,Integer,Integer,Integer)
	{POST [/exerciseBook/getExerciseBookListOfCreater]}: getExerciseBookListOfCreater(JSONObject)
	{GET [/exerciseBook/exerciseBookListOfCreater]}: getExerciseBookListOfCreater(Integer,String,String,String,Integer,Integer,Integer)
	{ [/exerciseBook/getExerciseBookList]}: getExerciseBookList(Integer,String,String,String,Integer,Integer,Integer)
	{POST [/exerciseBook/sendQiyeweixinQkkNotice]}: sendQiyeweixinQkkNotice(JSONObject)
	{POST [/exerciseBook/update]}: update(ExerciseBook)
[18:04:11:750] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookExamineeController:
	{GET [/exerciseBookExaminee/getExerciseBookExaminee]}: getExerciseBookExaminee(Integer)
	{POST [/exerciseBookExaminee/updateExerciseBookExaminee]}: updateExerciseBookExaminee(JSONObject)
[18:04:11:751] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookFavorateController:
	{ [/exerciseBookFavorate/addFavorite]}: addInFavoriteList(int,int)
	{ [/exerciseBookFavorate/addBatchFavorite]}: addBatchFavorite(String,int)
	{ [/exerciseBookFavorate/cancelFavorite]}: deleteFavoriteOfUser(Integer,Integer)
	{GET [/exerciseBookFavorate/myFavoriteExerciseBookPage]}: getMyFavoriteExerciseBookPageInfo(Integer,Integer)
	{GET [/exerciseBookFavorate/getMyFavoriteInfo]}: getMyFavoriteInfo(Integer,Integer)
	{ [/exerciseBookFavorate/getSomeoneExerciseBookAndUserPracticeInfo]}: getSomeoneExerciseBookAndUserPracticeInfo(int,int)
	{ [/exerciseBookFavorate/getCompanyExerciseBookAndUserPracticeInfo]}: getCompanyExerciseBookAndUserPracticeInfo(int,int)
	{POST [/exerciseBookFavorate/getCompanyExamineePracticeInfo]}: getCompanyExamineePracticeInfo(JSONObject)
	{GET [/exerciseBookFavorate/getCompanyPracticeSummary]}: getCompanyPracticeSummary(Integer,Integer)
	{ [/exerciseBookFavorate/getEntity]}: getEntity(Integer,Integer)
[18:04:11:751] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookGradeController:
	{POST [/exerciseBookGrade/add]}: add(ExerciseBookGrade)
[18:04:11:751] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookPracticeProcessController:
	{GET [/exerciseBookPracticeProcess/getRankOfInterval]}: getRankOfInterval(Integer,Integer,Integer,String,String,Integer,Integer)
	{GET [/exerciseBookPracticeProcess/getSequenceNoInRankOfInterval]}: getSequenceNoInRankOfInterval(Integer,Integer,Integer,Integer,String,String)
	{GET [/exerciseBookPracticeProcess/getRankInfo]}: getRankInfo(Integer,Integer,Integer,Integer,String,String,Integer,Integer)
[18:04:11:751] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookPracticeSummaryController:
	{GET [/exerciseBookPracticeSummary/getRankOfInterval]}: getRankOfInterval(Integer,Integer,Integer,String,String,Integer,Integer)
	{GET [/exerciseBookPracticeSummary/getSequenceNoInRankOfInterval]}: getSequenceNoInRankOfInterval(Integer,Integer,Integer,Integer,String,String)
	{GET [/exerciseBookPracticeSummary/getRankInfo]}: getRankInfo(Integer,Integer,Integer,Integer,String,String,Integer,Integer)
	{GET [/exerciseBookPracticeSummary/getUserPracticeSummary]}: getUserPracticeSummary(Integer,Integer,String,String)
	{POST [/exerciseBookPracticeSummary/getUserPracticeListWithSummary]}: getUserPracticeListWithSummary(JSONObject)
	{POST [/exerciseBookPracticeSummary/getDepartmentPracticeStatistics]}: getDepartmentPracticeStatistics(JSONObject)
	{GET [/exerciseBookPracticeSummary/exportUserPracticeSummary]}: exportUserPracticeSummary(HttpServletRequest,HttpServletResponse)
[18:04:11:752] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookPraiseController:
	{ [/exerciseBookPraise/add]}: addPraise(int,int)
	{ [/exerciseBookPraise/cancel]}: cancelPraise(int,int)
[18:04:11:753] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookQuestionController:
	{GET [/exerciseBookQuestion/exerciseBookQuestionWrapperList]}: getExerciseBookQuestionsById(int,Integer,Integer)
	{GET [/exerciseBookQuestion/getExerciseBookQuestionWrapperListWithRedLock]}: getExerciseBookQuestionWrapperList(Integer,Integer,Integer)
	{GET [/exerciseBookQuestion/exerciseBookQuestionWrapperListAndTotalNum]}: getExerciseBookQuestionsAndTotalNumById(Integer,Integer,Integer)
	{GET [/exerciseBookQuestion/getDayDayExerciseQuestionList]}: getDayDayExerciseQuestionList(Integer,Integer,Integer,Boolean)
[18:04:11:753] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookSummaryController:
	{GET [/exerciseBookSummary/getSummary]}: getSummary(Integer)
[18:04:11:753] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookUserRecordController:
	{GET [/exerciseBookUserRecord/updateHavePractisedNum]}: updateHavePractisedNum(Integer,Integer)
	{ [/exerciseBookUserRecord/getExerciseProgress]}: getExerciseBookProgressOfUser(int,int)
	{GET [/exerciseBookUserRecord/exportUserPracticeSummary]}: exportUserPracticeSummary(Integer,HttpServletResponse)
[18:04:11:753] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.FileAccessPermissionController:
	{POST [/fileAccessPermission/generateUnlockAdQRCode]}: generateUnlockAdQRCode(JSONObject)
	{POST [/fileAccessPermission/addFileAccessPermission]}: addFileAccessPermission(JSONObject)
	{POST [/fileAccessPermission/getFileAccessPermission]}: getFileAccessPermission(JSONObject)
[18:04:11:754] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.FileController:
	{[GET, POST] [/file/uploadFile]}: uploadFile(HttpServletRequest)
	{[GET, POST] [/file/uploadFileWithCheck]}: uploadFileWithCheck(HttpServletRequest)
	{ [/file/downloadFile]}: downLoad(String,HttpServletResponse,boolean)
	{GET [/file/downloadNetFile]}: downloadNetFile(HttpServletRequest,HttpServletResponse)
	{POST [/file/uploadOssFile]}: uploadOssFile(HttpServletRequest,HttpServletResponse)
[18:04:11:754] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.FreePracticeOfUserController:
	{POST [/freePracticeOfUser/getFreePractice]}: getFreePractice(JSONObject)
	{GET [/freePracticeOfUser/getQuestionListOfFreePractice]}: getQuestionListOfFreePractice(Integer,Integer)
	{GET [/freePracticeOfUser/getSummaryGroupByQuestionType]}: getSummaryGroupByQuestionType(Integer,Integer)
	{GET [/freePracticeOfUser/delete]}: delete(Integer,Integer)
	{POST [/freePracticeOfUser/save]}: save(JSONObject)
[18:04:11:755] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.GameController:
	{GET [/game/getGameWrapper]}: getGameWrapper(Integer)
	{POST [/game/updateInsert]}: updateInsert(JSONObject)
[18:04:11:755] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.GameResultController:
	{GET [/gameResult/getRankInfo]}: getRankInfo(String,Integer,Integer)
[18:04:11:755] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.HomepageUserFollowedController:
	{GET [/homepageUserFollowed/getUserFollowedInfo]}: getUserFollowedInfo(Integer,Integer)
	{GET [/homepageUserFollowed/getUserFollowedList]}: getUserFollowedList(Integer)
	{POST [/homepageUserFollowed/update]}: update(HomepageUserFollowedWrapper)
	{GET [/homepageUserFollowed/delete]}: delete(Integer,Integer)
	{POST [/homepageUserFollowed/save]}: save(HomepageUserFollowedWrapper)
[18:04:11:757] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.InviteRelationController:
	{GET [/inviteRelation/saveInviteRelation]}: saveInviteRelation(int,int,int)
	{GET [/inviteRelation/getRecentInviteRelationAnnouncement]}: getRecentInviteRelationAnnouncement(int)
	{GET [/inviteRelation/getExaminationInstanceListOfInvitee]}: getExaminationInstanceListOfInvitee(int,int,int,int,int)
	{GET [/inviteRelation/getEventResultOfInviter]}: getEventResultOfInviter(int,int,int)
	{GET [/inviteRelation/getResultOfEvent]}: getResultOfEvent(int,int,int,int,int)
	{[GET, POST] [/inviteRelation/exportRankListOfEvent]}: exportRankListOfExamination(HttpServletRequest,HttpServletResponse)
[18:04:11:759] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ManualServiceController:
	{POST [/manualService/addManualImportRecord]}: addManualImportRecord(JSONObject)
	{POST [/manualService/getManualImportRecords]}: getManualImportRecords(JSONObject)
	{POST [/manualService/updateManualImportRecord]}: updateManualImportRecord(ManualServiceRecord)
[18:04:11:760] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MessageController:
	{GET [/message/sendMessage]}: sendMessage(String)
[18:04:11:760] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MessagePushSubscriptionController:
	{POST [/messagePushSubscription/saveIfNotExisted]}: saveIfNotExisted(MessagePushSubscription)
	{GET [/messagePushSubscription/deleteEntity]}: deleteEntity(String,Integer,Integer)
	{POST [/messagePushSubscription/changeBatch]}: changeBatch(JSONObject)
	{GET [/messagePushSubscription/getEntity]}: getEntity(String,Integer,Integer)
	{POST [/messagePushSubscription/getList]}: getList(JSONObject)
[18:04:11:760] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MessageScheduleController:
	{GET [/messageSchedule/sendMessage]}: sendMessage(String)
	{POST [/messageSchedule/deleSave]}: deleSave(JSONObject)
	{POST [/messageSchedule/update]}: updateIgnoreNull(MessageSchedule)
	{GET [/messageSchedule/getList]}: getList(String,Integer,Integer)
[18:04:11:761] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MonitorViewController:
	{GET [/view/{sessionId}]}: viewMonitor(String,Model)
[18:04:11:763] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MultipleMarkingController:
	{GET [/multipleMarking/acceptMultipleMarking]}: acceptMultipleMarking(Integer,Integer)
	{GET [/multipleMarking/getMultipleMarkingExamintionListOfUser]}: getMultipleMarkingExamintionListOfUser(Integer)
	{GET [/multipleMarking/hasMultipleMarkingExaminationInstanceByExaminationInstanceId]}: hasMultipleMarkingExaminationInstanceByExaminationInstanceId(Integer)
	{GET [/multipleMarking/getWorkStateOfMarking]}: getWorkStateOfMarking(Integer,Integer)
	{GET [/multipleMarking/finishMarking]}: finishMarking(Integer)
[18:04:11:764] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.NameListController:
	{GET [/nameList/getEntityWrapper]}: getEntityWrapper(Integer)
	{GET [/nameList/getEntity]}: getEntity(Integer)
	{POST [/nameList/update]}: update(JSONObject)
	{GET [/nameList/delete]}: delete(Integer)
	{POST [/nameList/save]}: save(JSONObject)
	{GET [/nameList/getList]}: getList(Integer)
[18:04:11:764] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.NanXiangController:
	{POST [/nanxiang/getEntity]}: getEntity(NanxiangRegister)
	{GET [/nanxiang/check]}: check(String)
	{POST [/nanxiang/save]}: save(NanxiangRegister)
	{POST [/nanxiang/validate]}: validate(NanxiangRegister)
[18:04:11:764] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.OrderController:
	{POST [/order/createManualOrder]}: createManualOrder(JSONObject)
	{ [/order/create]}: createOrder(JSONObject)
	{ [/order/createCountOrder]}: createCountOrder(JSONObject)
	{POST [/order/createCountOrderByWeChatNative]}: createOrderByWeChatNative(JSONObject)
	{POST [/order/createCountOrderByJSAPI]}: createCountOrderByJSAPI(JSONObject)
	{POST [/order/orderNotify]}: orderNotify(JSONObject)
	{ [/order/getUnconsumedValueOfAccount]}: getUnconsumedValueOfAccount(String,Integer)
	{GET [/order/getOrderById]}: getOrderById(Integer)
	{GET [/order/getList]}: getList(String,String,Integer,Integer,Integer)
[18:04:11:765] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperController:
	{POST [/paper/batchUpdate]}: batchUpdate(JSONArray)
	{GET [/paper/getPaperWrapperWithRedLock]}: getPaperWrapperWithRedLock(Integer)
	{ [/paper/modifyPaper]}: modifyPaper(String,String)
	{POST [/paper/update]}: updatePaper(Paper)
	{ [/paper/paperListOfCreater]}: getPaperListOfCreater(String,int)
	{GET [/paper/getPaperById]}: getPaperById(Integer)
	{ [/paper/paperWrapper]}: getPaperWrapperById(String)
	{GET [/paper/getPaperWithFavoriteInfoAndOrderRelationInfo]}: getPaperWithFavoriteInfoAndOrderRelationInfo(Integer,Integer,Integer)
	{ [/paper/advancedPaperWrapper]}: getAdvancedPaperWrapperById(int)
	{ [/paper/paperListToAdmin]}: getPaperListToAdmin(String,String,int,int)
	{GET [/paper/getCategoriesAndNum]}: getCategoriesAndNum(Integer,Integer,String)
	{GET [/paper/getPaperList]}: getPaperList(Integer,Integer,String,String,String,Integer,Integer)
	{ [/paper/create]}: create(JSONObject)
	{GET [/paper/getPaperListAndTotalNum]}: getPaperListAndTotalNum(Integer,Integer,String,String,String,Integer,Integer)
[18:04:11:765] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperFavoriteController:
	{ [/paperFavorite/add]}: addFavorite(int,int)
	{ [/paperFavorite/cancel]}: cancelFavorite(int,int)
	{ [/paperFavorite/ifFavorite]}: ifFavorite(int,int)
	{ [/paperFavorite/getFavoriteList]}: getFavoriteList(int,int,int)
[18:04:11:765] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperPraiseController:
	{ [/paperPraise/add]}: addPraise(Integer,Integer)
	{ [/paperPraise/cancel]}: cancelPraise(Integer,Integer)
[18:04:11:765] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperQuestionController:
	{ [/paperQuestion/update]}: updateCompletion(BigDecimal,BigDecimal,int,BigDecimal,String,int)
	{ [/paperQuestion/updateSelection]}: updateSelection(BigDecimal,BigDecimal,int,BigDecimal,String,int)
	{GET [/paperQuestion/getQuestionWrapperListAndNum]}: getQuestionWrapperListAndNum(Integer,Integer,Integer)
[18:04:11:765] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperSummaryController:
	{GET [/paperSummary/getSummary]}: getSummary(Integer)
[18:04:11:766] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PassRaceGameController:
	{GET [/passRaceGame/getListAndTotalNum]}: getListAndTotalNum(Integer,Integer,Integer)
	{GET [/passRaceGame/getGameWrapper]}: getGameWrapper(Integer)
	{POST [/passRaceGame/update]}: update(JSONObject)
	{GET [/passRaceGame/delete]}: delete(Integer)
	{POST [/passRaceGame/save]}: save(JSONObject)
[18:04:11:766] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PassRaceResultController:
	{POST [/passRaceResult/save]}: save(JSONObject)
	{GET [/passRaceResult/getListAndTotalNum]}: getListAndTotalNum(Integer,Integer,Integer)
	{GET [/passRaceResult/getGameWrapper]}: getGameWrapper(Integer)
	{POST [/passRaceResult/update]}: update(JSONObject)
	{GET [/passRaceResult/delete]}: delete(Integer)
[18:04:11:769] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PassRaceStageController:
	{GET [/passRaceStage/getStageWrapper]}: getStageWrapper(Integer,Integer)
	{GET [/passRaceStage/getMyPlayedStageList]}: getMyPlayedStageList(Integer,Integer,Boolean)
[18:04:11:775] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PdfFileController:
	{[GET, POST] [/pdfFile/examinationReport]}: createExaminationPdfReport(HttpServletRequest,HttpServletResponse)
	{GET [/pdfFile/generatePdfByUser]}: generatePdfByUser(Integer,Integer)
	{ [/pdfFile/temporaryExaminationReport]}: createFreeExaminationReport(HttpServletRequest,HttpServletResponse)
[18:04:11:776] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PermissionAuthorizationController:
	{POST [/permissionAuthorization/getAuthorizedDepartmentList]}: getAuthorizedDepartmentList(JSONObject)
	{POST [/permissionAuthorization/getAuthorizedAdminList]}: getAuthorizedAdminList(JSONObject)
	{POST [/permissionAuthorization/getAuthorizedCompanyInfoWith2Grade]}: getAuthorizedCompanyInfoWithRecursionDepartment(JSONObject)
	{GET [/permissionAuthorization/getAuthorizedCompanyPracticeSummary]}: getAuthorizedCompanyPracticeSummary(Integer,Integer)
	{POST [/permissionAuthorization/getAuthorizedCompanyExamineePracticeInfo]}: getAuthorizedCompanyExamineePracticeInfo(JSONObject)
	{POST [/permissionAuthorization/changeBatch]}: changeBatch(JSONObject)
	{GET [/permissionAuthorization/getEntity]}: getEntity(Integer,Integer,Integer)
	{POST [/permissionAuthorization/save]}: save(List)
	{GET [/permissionAuthorization/getList]}: getList(Integer,Integer)
[18:04:11:776] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PermissionController:
	{GET [/permission/getList]}: getList()
[18:04:11:776] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PointConsumerationController:
	{POST [/pointConsumeration/getUserConsumeList]}: getUserConsumeList(JSONObject)
	{POST [/pointConsumeration/save]}: save(PointConsumeration)
[18:04:11:776] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PointDetailController:
	{POST [/pointDetail/getCompanyRankInfo]}: getCompanyRankInfo(JSONObject)
	{GET [/pointDetail/getRankInfo]}: getRankInfo(Integer,Integer,String,String,String,Integer,Integer)
	{GET [/pointDetail/exportPointRankOfCompany]}: exportPointRankOfCompany(HttpServletRequest,HttpServletResponse)
[18:04:11:776] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PointRuleController:
	{POST [/pointRule/upInsert]}: upInsert(PointRule)
	{GET [/pointRule/getEntity]}: getEntity(Integer)
[18:04:11:776] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PointUserController:
	{GET [/pointUser/getUserPointInfo]}: getUserPointInfo(Integer,Integer)
	{GET [/pointUser/clear]}: clear(Integer,Integer,Integer)
[18:04:11:777] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PracticePushController:
	{GET [/practicePush/toggleStatus]}: toggleStatus(Integer,Integer,Integer)
	{GET [/practicePush/detail]}: getDetail(Integer,Integer)
	{GET [/practicePush/checkAndExecutePushTasks]}: checkAndExecutePushTasks()
	{POST [/practicePush/update]}: update(JSONObject)
	{GET [/practicePush/delete]}: delete(Integer,Integer)
	{POST [/practicePush/create]}: create(JSONObject)
	{GET [/practicePush/list]}: getList(Integer,Integer,String,Integer,Integer,Integer)
[18:04:11:777] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductController:
	{ [/product/getProductList]}: getProductList(String)
	{GET [/product/getListOfProduct]}: getListOfProduct(String)
[18:04:11:777] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductCountTransactionController:
	{POST [/productCountTransaction/getList]}: getList(JSONObject)
[18:04:11:777] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductIntroductionController:
	{ [/productIntroduction/list]}: getProductIntroductionWrapperList()
[18:04:11:777] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductOrderRelationController:
	{POST [/productOrderRelation/updateProductOrderRelation]}: updateProductOrderRelation(ProductOrderRelation)
	{GET [/productOrderRelation/getProductOrderRelationWrapper]}: getProductOrderRelationWrapper(String,Integer)
	{GET [/productOrderRelation/checkIfOverdue]}: checkIfOverdue(String,Integer)
	{GET [/productOrderRelation/getProductOrderRelation]}: getProductOrderRelation(String,Integer)
	{GET [/productOrderRelation/refresh]}: refresh(Integer)
[18:04:11:777] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductOrderRelationCountController:
	{GET [/productOrderRelationCount/getProductOrderRelationCount]}: getProductOrderRelationCount(String,Integer,String)
	{POST [/productOrderRelationCount/getProductOrderRelationCountList]}: getProductOrderRelationCountList(JSONObject)
	{GET [/productOrderRelationCount/tryToConsumeVADVIP]}: tryToConsumeVADVIP(Integer,Integer)
	{GET [/productOrderRelationCount/consumeVADVIP]}: consumeVADVIP(Integer,Integer)
	{GET [/productOrderRelationCount/consume]}: consume(String,Integer,String,Integer)
[18:04:11:778] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QrcodeScanEntryController:
	{GET [/qrcodeScan/checkIfFollowed]}: checkIfFollowed(HttpServletRequest)
[18:04:11:778] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionBankQuestionController:
	{POST [/questionBankQuestion/changeQuestionBank]}: changeQuestionBank(JSONObject)
	{GET [/questionBankQuestion/deleteQuestionBankQuestion]}: deleteQuestionBankQuestion(Integer)
[18:04:11:778] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionCompositionChildController:
	{POST [/compositionChild/updateChildQuestionEntity]}: updateChildQuestionEntity(QuestionCompositionChild)
	{POST [/compositionChild/getPaperQuestionListMapByIds]}: getListByIds(JSONObject)
	{POST [/compositionChild/getListByIds]}: getListByIds(JSONArray)
	{GET [/compositionChild/getList]}: getList(Integer)
[18:04:11:780] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionController:
	{POST [/question/getQuestionsByGroupConditionAdvanced]}: getQuestionsByGroupConditionAdvanced(JSONObject)
	{GET [/question/getCategoryList]}: getCategoryList(Integer,String,Integer)
	{POST [/question/updateQuestion]}: updateQuestion(Question)
	{[GET, POST] [/question/modifyQuestion]}: modifyQuestion(HttpServletRequest)
	{POST [/question/deleteQuestionByQuery]}: deleteQuestion(JSONObject)
	{GET [/question/deleteQuestion]}: deleteQuestion(String)
	{GET [/question/deleteQuestionList]}: deleteQuestionList(String)
	{POST [/question/deleteBatchQuestion]}: deleteBatchQuestion(JSONObject)
	{GET [/question/deleteQuestionIncludingExample]}: deleteQuestionIncludingExample(int,int)
	{GET [/question/questionListOfCreaterByCategory]}: getQuestionListOfCreaterByCategory(String,String,int)
	{GET [/question/questionListOfCreaterByType]}: getQuestionListOfCreaterByType(String,String,int,int,int)
	{GET [/question/questionListOfCreaterByTypeIncludingExample]}: getQuestionListOfCreaterByTypeIncludingExample(String,String,int,int,int)
	{GET [/question/questionWrapperListOfCreaterByType]}: getQuestionWrapperListOfCreaterByType(String,String,String,String,int,int,int)
	{[GET, POST] [/question/questionWrapperListOfCreaterByTypeIncludingExample]}: getQuestionWrapperListOfCreaterByTypeIncludingExample(String,String,int,int,int)
	{GET [/question/questionListForPractice]}: getQuestionListOfCreaterForPractice(String,String,String,int,int,int)
	{GET [/question/question]}: getQuestionById(String)
	{GET [/question/questionWrapper]}: getQuestionWrapperById(String)
	{GET [/question/getQuestionWrapper]}: getQuestionWrapper(String)
	{POST [/question/importQuestionUnion], produces [application/json;charset=utf-8]}: importQuestionUnion(HttpServletRequest)
	{POST [/question/importWordQuestion], produces [application/json;charset=utf-8]}: importWordQuestion(HttpServletRequest)
	{[GET, POST] [/question/importExcelQuestion], produces [application/json;charset=utf-8]}: importExcelQuestion(HttpServletRequest)
	{GET [/question/modifyDefaultMark]}: modifyDefaultMarkOfQuestions(String,BigDecimal)
	{POST [/question/modifyQuestionCatagory]}: modifyCatagory(JSONObject)
	{GET [/question/modifyCatagory]}: modifyCatagory(String,String)
	{GET [/question/categoryAndTotalNum]}: getCategoryAndTotalNum(Integer,String,Integer)
	{POST [/question/getQuestionTypeAndCategoryAndTotalNum]}: getQuestionTypeAndCategoryAndTotalNum(JSONObject)
	{GET [/question/questionTypeAndCategoryAndTotalNum]}: getQuestionTypeAndCategoryAndTotalNum(Integer,Integer,Integer)
	{POST [/question/getQuestionTypeAndCategoryAndTotalNumWithPermission]}: getQuestionTypeAndCategoryAndTotalNumWithPermission(JSONObject)
	{GET [/question/getQuestionCatagoryAndTotalNum]}: getQuestionCatagoryAndTotalNum(Integer,int)
	{POST [/question/getQuestionCatagoryAndTotalNumWithPermission]}: getQuestionCatagoryAndTotalNumWithPermission(JSONObject)
	{GET [/question/machineChooseQuestion]}: getMachineChooseQuestion(int,String,int,int)
	{POST [/question/getQuestionsByGroupConditionAdvancedWithPermission]}: getQuestionsByGroupConditionAdvancedWithPermission(JSONObject)
	{GET [/question/getQuestionsByGroupCondition]}: getQuestionsAndLengthByGroupCondition(Integer,String,String,String,int,Integer,Integer)
	{GET [/question/getQuestionsList]}: getQuestionsList(Integer,String,String,String,int,Integer,Integer)
	{POST [/question/checkDuplicates]}: checkDuplicates(DuplicateCheckRequest)
	{[GET, POST] [/question/uploadFile]}: uploadFile(HttpServletRequest)
	{[GET, POST] [/question/uploadFileWithCheck]}: uploadFileWithCheck(HttpServletRequest)
	{POST [/question/questionListOfCreater]}: getQuestionListOfCreater(JSONObject)
	{GET [/question/exportQuestions]}: exportQuestions(HttpServletRequest,HttpServletResponse)
	{POST [/question/saveBatch]}: saveBatch(JSONObject)
	{[GET, POST] [/question/createQuestion]}: create(HttpServletRequest)
[18:04:11:781] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionFavoriteController:
	{ [/questionFavorite/add]}: addFavorite(int,int)
	{ [/questionFavorite/cancel]}: cancelFavorite(int,int)
	{ [/questionFavorite/ifFavorite]}: ifFavorite(int,int)
[18:04:11:781] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionRandomController:
	{GET [/questionRandom/deleteQuestionList]}: deleteQuestionList(String)
	{POST [/questionRandom/createByPost]}: createByPost(JSONObject)
	{GET [/questionRandom/list]}: getQuestionRandomListByCreaterIdInCompany(Integer,int,int,int)
	{GET [/questionRandom/questionRandomExtend]}: getQuestionRandomExtend(int)
	{GET [/questionRandom/delete]}: delete(int)
	{GET [/questionRandom/create]}: create(String,String)
	{POST [/questionRandom/modify]}: modify(JSONObject)
[18:04:11:782] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionSummaryController:
	{POST [/questionSummary/getWrongQuestionList]}: getWrongQuestionList(JSONObject)
	{POST [/questionSummary/removeWrongQuestion]}: removeWrongQuestion(JSONObject)
[18:04:11:782] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.SMSNoticeController:
	{GET [/notice/sendValidCodeSMS]}: sendValidCodeSMS(String,String)
	{GET [/notice/sendQKKRegisterNotice]}: sendQKKRegisterNotice(String,String)
	{GET [/notice/sendQKKTestOverdueNotice]}: sendQKKTemplateNotice(Integer)
	{GET [/notice/sendQKKTimeingNotice]}: sendQKKTimeingNotice(String)
[18:04:11:782] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.SoftVersionController:
	{GET [/version/reloadVersion]}: reloadVersion(String)
	{GET [/version/setSystemRunningState]}: setSystemRunningState(String)
	{GET [/version/setAdProvider]}: setAdProvider(String)
	{GET [/version/getSystemSettings]}: getSystemSettings(String)
	{GET [/version/setProductSystemRunningState]}: setProductSystemRunningState(String,String)
	{GET [/version/getProductSystemSettings]}: getProductSystemSettings(String)
	{GET [/version/getVersion]}: getVersion(String)
[18:04:11:783] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.SysUserRoleController:
	{GET [/sysUserRole/unbindExamineeUserRole]}: unbindExamineeUserRole(Integer,Integer)
	{POST [/sysUserRole/unbindExamineeUserRoleWithPermission]}: unbindExamineeUserRoleWithPermission(JSONObject)
	{GET [/sysUserRole/unbindAdminUserRole]}: unbindAdminUserRole(Integer,Integer)
	{POST [/sysUserRole/unbindAdminUserRoleWithPermission]}: unbindAdminUserRoleWithPermission(JSONObject)
	{POST [/sysUserRole/getSysUserRoleList]}: getSysUserRoleList(JSONObject)
	{GET [/sysUserRole/getSysUserRoleListByUsernameAndPassword]}: getSysUserRoleListByPhoneAndPassword(String,String,String,HttpServletRequest)
	{GET [/sysUserRole/getNewSysUserRoleList]}: getNewSysUserRoleList(String,String,String,HttpServletRequest)
	{GET [/sysUserRole/getSysUserRoleListOfUserInCompany]}: getSysUserRoleListOfUserInCompany(Integer,Integer,String)
	{GET [/sysUserRole/getCompanyInfoByAdminUserId]}: getCompanyInfoByAdminUserId(Integer)
	{GET [/sysUserRole/transmitAdminToOther]}: transmitAdminToOther(Integer,Integer,Integer)
	{POST [/sysUserRole/batchAddChildAdmin]}: batchAddChildAdmin(JSONObject)
	{GET [/sysUserRole/getSysUserRoleListOfUser]}: getSysUserRoleListOfUser(Integer,String,Integer)
	{GET [/sysUserRole/getSysUserRoleListOfCompany]}: getSysUserRoleListOfCompany(Integer,Integer)
	{POST [/sysUserRole/addSysUserRole]}: addSysUserRole(JSONObject)
[18:04:11:783] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.SystemMessageController:
	{GET [/systemMessage/handleSystemMessage]}: handleSystemMessage()
	{GET [/systemMessage/changeDepartmentId]}: changeDepartmentId(Integer)
	{GET [/systemMessage/batchDelete]}: batchDelete(String,String,String)
	{POST [/systemMessage/insert]}: insert(SystemMessage)
[18:04:11:783] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.TaxIdentityInfoController:
	{ [/taxIdentity/createTaxIdentityInfo]}: createTaxIdentityInfo(String)
	{ [/taxIdentity/modifyTaxIdentityInfo]}: modifyTaxIdentityInfo(String)
	{ [/taxIdentity/taxIdentityInfo]}: getTaxIdentityInfoByUserId(String,String)
[18:04:11:783] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.TaxSheetApplicationController:
	{ [/taxSheetApplication/createTaxSheetApplication]}: createTaxSheetApplication(String)
	{ [/taxSheetApplication/taxSheetApplicationList]}: getTaxSheetApplicationListOfUser(String)
	{ [/taxSheetApplication/taxSheetApplication]}: getTaxSheetApplicationById(String)
[18:04:11:784] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.TemplateController:
	{GET [/template/getMyTemplates]}: getMyTemplates(int,String)
[18:04:11:784] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.TestController:
	{GET [/test/api]}: apiAccess()
	{GET [/test/getKeysByPrefix]}: getKeysByPrefix(String)
	{GET [/test/deleteByPrex]}: deleteByPrex(String)
[18:04:11:784] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UniversalOrderController:
	{ [/univsersalOrder/create]}: createOrder(JSONObject)
	{POST [/univsersalOrder/createOrderByWeChatNative]}: createOrderByWeChatNative(JSONObject)
	{POST [/univsersalOrder/createCountOrderByJSAPI]}: createCountOrderByJSAPI(JSONObject)
	{POST [/univsersalOrder/orderNotify]}: orderNotify(JSONObject)
	{GET [/univsersalOrder/getOrderById]}: getOrderById(Integer)
	{GET [/univsersalOrder/getList]}: getList(String,String,Integer,Integer,Integer)
[18:04:11:784] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UniversalProductOrderRelationController:
	{POST [/universalProductOrderRelation/getProductOrderRelation]}: getProductOrderRelation(JSONObject)
[18:04:11:785] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserCompanyController:
	{POST [/userCompany/updateUserInfoAndCompanyInfo]}: saveUserInfoAndCompanyInfo(JSONObject)
	{GET [/userCompany/saveUserInfoAndCompanyInfo]}: saveUserInfoAndCompanyInfo(int,int,int,String,String)
	{POST [/userCompany/bindCompany]}: bindCompany(JSONObject)
	{POST [/userCompany/deleteUserOfCompanyWithPermission]}: deleteUserOfCompanyWithPermission(JSONObject)
	{POST [/userCompany/deleteBatch]}: deleteBatchWithPermission(JSONObject)
	{GET [/userCompany/deleteExamineeByDepartmentName]}: deleteExamineeByDepartmentName(Integer,String)
	{POST [/userCompany/deleteExamineeByDepartmentNameWithPermission]}: deleteExamineeByDepartmentNameWithPermission(JSONObject)
	{GET [/userCompany/getUserListByQkkQiyeCorpid]}: getUserListByQkkQiyeCorpid(String,String)
	{GET [/userCompany/getCompanyInfoByUserIdAndCompanyId]}: getUserCompanyInfoByUserIdAndCompanyId(int,int)
	{GET [/userCompany/getUserCompanyByPhone]}: getUserCompanyByPhone(String)
	{GET [/userCompany/getUserInfoWithCompanyInfo]}: getUserInfoWithCompanyInfo(Integer,Integer)
	{GET [/userCompany/getUserListByDepartmentId]}: getUserListByDepartmentId(Integer,Integer,Integer,Integer,Integer,Integer)
	{POST [/userCompany/getCompanyUserListByMap]}: getCompanyUserListByMap(JSONObject)
	{GET [/userCompany/getUserListOfDepartmentsInCludingSelf]}: getUserListOfDepartmentsInCludingSelf(String)
	{GET [/userCompany/getUserListOfDepartmentsExcludingSelf]}: getUserListOfDepartmentsExcludingSelf(String)
	{GET [/userCompany/getUserListByName]}: getUserListByName(String,Integer,Integer,Integer,Integer)
	{POST [/userCompany/getUserList]}: getUserListByName(JSONObject)
	{GET [/userCompany/ifRegisted]}: getIfRegisted(int,int)
	{GET [/userCompany/getUserListOfCompany]}: getUserListOfCompany(Integer,String,String,Integer,Integer)
	{GET [/userCompany/secureGetUserListOfCompany]}: secureGetUserListOfCompany(Integer,String,String,Integer,Integer,Integer,Integer,Integer,Integer)
	{GET [/userCompany/getGrantedUserListOfCompany]}: getGrantedUserListOfCompany(Integer,String,String,Integer,Integer,Integer,Integer,String,Integer,Integer,String,String)
	{POST [/userCompany/getCompanyUserList]}: getCompanyUserList(JSONObject)
	{GET [/userCompany/getExamineeStatisticsOfCompany]}: getExamineeStatisticsOfCompany(Integer)
	{GET [/userCompany/exportUserListOfCompany]}: exportUserListOfCompany(Integer,Integer,HttpServletResponse)
	{GET [/userCompany/checkIfExceedMemberNumLimit]}: checkIfExceedMemberNumLimit(Integer,Integer)
	{POST [/userCompany/approvedRegist]}: approvedRegist(UserCompany)
	{POST [/userCompany/importUser]}: importUser(HttpServletRequest)
	{GET [/userCompany/getCompanyInfoByUserId]}: getCompanyInfoByUserId(int)
	{GET [/userCompany/deleteUserOfCompany]}: deleteUserOfCompany(int,int)
	{POST [/userCompany/getUserInfoListByIds]}: getUserInfoListByIds(JSONObject)
[18:04:11:786] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserController:
	{GET [/user/getUserListByQkkQiyeCorpid]}: getUserListByQkkQiyeCorpid(String,String)
	{GET [/user/getUserInfoWithCompanyInfo]}: getUserInfoWithCompanyInfo(Integer,Integer)
	{GET [/user/updateUserInfo]}: updateUserInfo(String)
	{GET [/user/getUserByPhone]}: getUserByPhone(String)
	{POST [/user/getUserInfoListByIds]}: getUserInfoListByIds(List)
	{GET [/user/getUserInfoByOpenId]}: getUserByEteaOpenId(String,String)
	{GET [/user/getUserInfoById]}: getUserInfoById(int)
	{GET [/user/getUserInfoListByNickName]}: getUserInfoListByNickName(String)
	{POST [/user/supplyPhoneNo]}: supplyPhoneNo(JSONObject)
	{GET [/user/loginByValidationCode]}: loginByValidationCode(String,String,HttpServletRequest)
	{GET [/user/validateSMSPhoneIfMatched]}: validateSMSPhoneIfMatched(String,String)
	{GET [/user/oneKeyLogin]}: oneKeyLogin(String,HttpServletRequest)
	{GET [/user/login]}: loginByPhoneAndPassword(String,String,HttpServletRequest)
	{GET [/user/loginById]}: loginById(Integer)
	{POST [/user/getUserInfoByUserIdAndCompanyId]}: getUserInfoByUserIdAndCompanyId(JSONObject,HttpServletRequest)
	{POST [/user/loginWithAutoRegist]}: loginWithAutoRegist(JSONObject,HttpServletRequest)
	{GET [/user/changePassword]}: changePassword(Integer,String,String)
	{POST [/user/register]}: register(JSONObject)
	{POST [/user/update]}: update(User)
	{POST [/user/getPassword]}: getPassword(JSONObject)
[18:04:11:786] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserDeviceBindingController:
	{POST [/userDeviceBinding/getUserDeviceBinding]}: getUserDeviceBinding(JSONObject)
	{POST [/userDeviceBinding/bindDevice]}: bindDevice(JSONObject)
	{POST [/userDeviceBinding/unbindDevice]}: unbindDevice(JSONObject)
[18:04:11:787] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserExaminationPerformanceReportController:
	{ [/examinationPerformanceReport/consume]}: consume(Integer,Integer)
[18:04:11:787] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserFormController:
	{GET [/userForm/deleteForm]}: deleteFormOfUser(Integer,Integer)
	{GET [/userForm/getUserFormListByUserId]}: getUserFormListByUserId(Integer)
	{POST [/userForm/create]}: create(UserForm)
[18:04:11:787] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserFormFlowRecordController:
	{POST [/userFormFlowRecord/upsert]}: upsert(UserFormFlowRecord)
	{GET [/userFormFlowRecord/getFormFlowRecordWrapperById]}: getFormFlowRecordWrapper(Integer,Integer)
[18:04:11:787] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserOfYncxController:
	{[GET, POST] [/userOfYncx/examinationReport]}: createExaminationPdfReport(HttpServletRequest,HttpServletResponse)
	{GET [/userOfYncx/exportExaminationInstanceDetailExcelBundle]}: exportExaminationInstanceDetailExcelBundle(Integer,Integer)
	{GET [/userOfYncx/exportExaminationInstancePdfBundle]}: exportExaminationInstancePdfBundle(Integer,Integer)
	{ [/userOfYncx/exportDetailRankListOfExamination]}: exportDetailRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/userOfYncx/exportRankListOfExamination]}: exportRankListOfExamination(HttpServletRequest,HttpServletResponse)
[18:04:11:787] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserStatisticController:
	{ [/userStatistic/totalItemNum]}: totalNumber(Integer,Integer)
	{ [/userStatistic/getOnlineUserNumberOfExamination]}: getOnlineUserNumberOfExamination(int,int)
[18:04:11:788] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserSummaryController:
	{POST [/userSummary/upInsert]}: upInsert(JSONObject)
	{POST [/userSummary/getEntity]}: getEntity(UserSummary)
[18:04:11:789] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserViewPerformanceWithoutAdController:
	{GET [/userViewPerformanceWithoutAd/insertIfNotExisted]}: insertIfNotExisted(Integer,Integer,Integer,String)
	{GET [/userViewPerformanceWithoutAd/adEndedCallback]}: adEndedCallback(String,String)
	{POST [/userViewPerformanceWithoutAd/deletePermently]}: deletePermently(JSONObject)
	{ [/userViewPerformanceWithoutAd/checkIfAllowUserViewPerformanceWithoutAd]}: checkIfAllowUserViewPerformanceWithoutAd(int,int,int)
	{ [/userViewPerformanceWithoutAd/getUserViewPerformanceWithoutAdTransaction]}: getUserViewPerformanceWithoutAdTransaction(String,int,String,Integer,Integer)
[18:04:11:790] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WeChatMiniProgramController:
	{POST [/weixinAccount/getProductList]}: getProductList(JSONObject)
	{POST [/weixinAccount/getGroupId]}: getGroupId(JSONObject)
	{GET [/weixinAccount/session]}: getSession(String,String)
	{POST [/weixinAccount/getQRCode]}: getQRCode(JSONObject)
	{POST [/weixinAccount/imgSecCheck]}: imgSecCheck(HttpServletRequest)
	{POST [/weixinAccount/imgSecCheckByMultipart], consumes [multipart/form-data]}: imgSecCheck(MultipartFile,String)
	{GET [/weixinAccount/getAccessToken]}: getAccessTokenOfAccount(String)
	{POST [/weixinAccount/registWithCode]}: registWithCode(JSONObject)
	{GET [/weixinAccount/autoLogin]}: autoLogin(String,String,HttpServletRequest)
	{GET [/weixinAccount/loginWithoutUserInfoAndAutoRegist]}: loginWithoutUserInfoAndAutoRegist(String,String,HttpServletRequest)
	{GET [/weixinAccount/login]}: loginWithAutoRegister(String,String,HttpServletRequest)
	{POST [/weixinAccount/loginWithUserAuthNoRegistByPost]}: loginWithUserAuthWithoutAutoRegistByPost(JSONObject,HttpServletRequest)
	{POST [/weixinAccount/loginWithAuth]}: loginWithAuth(JSONObject,HttpServletRequest)
	{POST [/weixinAccount/getUserInfoByWeixinCode]}: getUserInfoByWeixinCode(JSONObject)
	{POST [/weixinAccount/loginWithUserAuthByPost]}: loginWithUserAuthWithAutoRegistByPost(JSONObject,HttpServletRequest)
	{GET [/weixinAccount/loginWithUserAuth]}: loginWithUserAuthWithAutoRegistByGet(String,String,String,String,String,HttpServletRequest)
	{POST [/weixinAccount/getQRCodeBase64ByLimit]}: getQRCodeBase64ByLimit(JSONObject)
	{POST [/weixinAccount/getQRCodeBase64]}: getQRCodeBase64(JSONObject)
	{POST [/weixinAccount/getPhoneNumByPost]}: getPhoneNumByPost(JSONObject)
	{POST [/weixinAccount/getPhoneNum]}: getPhoneNum(JSONObject)
	{POST [/weixinAccount/getPhoneNumNew]}: getPhoneNumNew(JSONObject)
	{POST [/weixinAccount/getPhoneNumWithoutGetSession]}: getPhoneNumWithoutGetSession(JSONObject)
	{POST [/weixinAccount/searchProduct]}: searchProduct(JSONObject)
	{GET [/weixinAccount/getCouponList]}: getCoupon(String)
	{GET [/weixinAccount/receivedCouple]}: receivedCouple(String,String,String)
	{GET [/weixinAccount/getUserCoupleList]}: getUserCoupleList(String,String,String)
	{POST [/weixinAccount/msgSecCheck]}: msgSecCheck(JSONObject)
	{POST [/weixinAccount/bind]}: bind(JSONObject)
[18:04:11:799] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WeChatOfficialAccountController:
	{GET [/wx/receiveMessage]}: doGet(HttpServletRequest)
	{POST [/wx/receiveMessage], produces [application/xml;charset=UTF-8]}: processRequest(HttpServletRequest)
	{GET [/wx/checkIfFollowed]}: getQrcodeScanEntry(HttpServletRequest)
	{GET [/wx/loginWithAutoRegister]}: loginWithAutoRegister(String,String)
	{GET [/wx/getJSSDKConfiguration]}: getJSSDKConfiguration(String,String)
	{GET [/wx/getTicket]}: getTicket(HttpServletRequest)
	{GET [/wx/loginByWeChatOfficialAccount]}: loginByWeChatOfficialAccount(String,String)
	{GET [/wx/getPageAuthAccessToken]}: getPageAuthAccessToken(String,String)
	{GET [/wx/getTicketOfProduct]}: getTicketOfProduct(HttpServletRequest)
	{GET [/wx/createAccountMenu]}: createAccountMenu(String)
[18:04:11:801] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WordFileController:
	{GET [/wordFile/exportExercisePaper]}: exportExercisePaper(HttpServletRequest,HttpServletResponse)
	{GET [/wordFile/exportExamPaper]}: exportExamPaper(HttpServletRequest,HttpServletResponse)
	{GET [/wordFile/exportWrongQuestions]}: exportWrongQuestions(HttpServletRequest,HttpServletResponse)
[18:04:11:801] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WrongBookController:
	{POST [/wrongBook/update]}: updateIgnoreNull(JSONObject)
	{POST [/wrongBook/create]}: createWrongBook(JSONObject)
	{GET [/wrongBook/deletePermanently]}: deletePermanently(Integer)
	{POST [/wrongBook/getWrongBookQuestionList]}: getWrongBookQuestionList(JSONObject)
	{POST [/wrongBook/removeQuestion]}: removeQuestion(JSONObject)
	{GET [/wrongBook/list]}: getWrongBookList(Integer)
[18:04:11:801] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WrongQuestionBookController:
	{POST [/wrongQuestionBook/doReview]}: doReview(JSONObject)
	{ [/wrongQuestionBook/wrongQuestionBookPageInfo]}: getWrongQuestionBookPageInfoOfUser(int,int)
	{ [/wrongQuestionBook/getWrongQuestionBookOfUser]}: getWrongQuestionBookOfUser(Integer)
	{POST [/wrongQuestionBook/batchSaveWrongQuestionBook]}: batchSaveWrongQuestionBook(JSONObject)
	{GET [/wrongQuestionBook/delete]}: delete(Integer)
[18:04:11:802] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WrongQuestionBookQuestionController:
	{ [/wrongQuestionBookQuestion/markAsReviewed]}: markAsReviewed(int,int)
	{ [/wrongQuestionBookQuestion/getQuestionWrapperListOfWrongQuestionBook]}: getQuestionWrapperListOfWrongQuestionBook(int)
	{ [/wrongQuestionBookQuestion/getQuestionWrapperListOfBook]}: getQuestionWrapperListOfBook(Integer)
	{GET [/wrongQuestionBookQuestion/getTotalWrongQuestionWrapperList]}: getTotalWrongQuestionWrapperList(Integer,Integer)
	{GET [/wrongQuestionBookQuestion/deleteByQuery]}: deleteByQuery(Integer,Integer)
[18:04:11:802] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WrongQuestionBookReviewSummaryController:
	{POST [/wrongQuestionBookReviewSummary/saveSummary]}: saveSummary(JSONObject)
[18:04:11:802] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WxMiniprogramSubscribeMessageOfEteaController:
	{GET [/wxMiniprogramSubscribeMessageOfEtea/batchSendSubscribedMessage]}: batchSendSubscribedMessage(Integer,Integer,Integer)
	{POST [/wxMiniprogramSubscribeMessageOfEtea/subscribe]}: subscribe(JSONObject)
[18:04:11:802] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.c.p.ExaminationControllerOfPC:
	{GET [/examination/examinationInViewListOfCreaterFromPC]}: getExaminationInViewListOfCreaterFromPC(Integer,Integer,Integer,Integer)
	{POST [/examination/getExaminationInViewListAndNum]}: getExaminationInViewListAndNum(JSONObject)
	{POST [/examination/getExaminationInViewListOfCreaterByTagsFromPC]}: getExaminationInViewListOfCreaterByTagsFromPC(JSONObject)
	{POST [/examination/getExaminationInViewListExcludingCreaterByTagsFromPC]}: getExaminationInViewListExcludingCreaterByTagsFromPC(JSONObject)
	{GET [/examination/getExaminationListAndSizeOfWillMark]}: getExaminationListAndSizeOfWillMark(Integer,Integer,Integer,Integer)
	{POST [/examination/getWillMarkExaminationListAndSize]}: getWillMarkExaminationListAndSize(JSONObject)
	{POST [/examination/getExaminationInViewListOfCreaterByTags]}: getExaminationInViewListOfCreaterByTags(JSONObject)
[18:04:11:802] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.c.p.ExaminationInstanceControllerOfPC:
	{GET [/examinationInstance/examinationInstanceStageSummaryListFromPC]}: getExaminationInstanceStageSummaryListFromPC(String,String,String,int,int,int)
	{GET [/examinationInstance/getExaminationInstanceStageSummaryListAndTotalNumFromPC]}: getExaminationInstanceStageSummaryListAndTotalNumFromPC(String,String,String,int,int,int)
	{POST [/examinationInstance/getExaminationInstanceStageSummaryListAndNum]}: getExaminationInstanceStageSummaryListAndNum(JSONObject)
	{POST [/examinationInstance/getExaminationInstanceStageSummaryListAndLengthByTags]}: getExaminationInstanceStageSummaryListByTagsFromPC(JSONObject)
	{POST [/examinationInstance/getListAndSizeOfExaminationInstanceWithUserInfoByConditions]}: getListAndSizeOfExaminationInstanceWithUserInfoByConditions(JSONObject)
	{GET [/examinationInstance/getExaminationInstanceListAndSizeWithUserInfoByExaminationId]}: getExaminationInstanceListWithUserInfoByExaminationId(Integer,Integer,Integer,String,Integer,Integer)
[18:04:11:803] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.c.p.PcPageDataController:
	{GET [/pageData/getCompanyCoreSummary]}: getCompanyCoreSummary(Integer,Integer)
[18:04:11:803] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.c.PageDataControllerOfCooperation:
	{GET [/pageDateControllerOfCooperation/getRankData]}: getRankData(Integer,Integer,Integer,Integer,String,String,Integer,Integer)
[18:04:11:803] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.e.ExaminationInstanceControllerOfEtea:
	{GET [/examinationInstanceControllerOfEtea/exportTotalScoreGroupByUser]}: exportTotalScoreGroupByUser(String,HttpServletResponse)
	{GET [/examinationInstanceControllerOfEtea/getUserTakenExaminations]}: getUserTakenExaminations(Integer,Integer,Integer)
	{GET [/examinationInstanceControllerOfEtea/getRecentOnGoingExaminations]}: getRecentOnGoingExaminations(Integer)
[18:04:11:803] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.e.PageDataControllerOfEtea:
	{POST [/pageDataOfEtea/getTotalScoreGroupByUser]}: getTotalScoreGroupByUser(JSONObject)
	{GET [/pageDataOfEtea/getHomePageSummaryInfo]}: getHomePageSummaryInfo(Integer)
[18:04:11:803] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.f.FormController:
	{POST [/formSystem/saveFormFlowRecord]}: saveFormFlowRecord(JSONObject)
	{POST [/formSystem/createForm]}: createForm(JSONObject)
	{GET [/formSystem/getFormWrapperById]}: getFormWrapperById(Integer)
	{POST [/formSystem/updateForm]}: updateForm(JSONObject)
[18:04:11:803] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteContentAccessController:
	{POST [/ksiteContentAccess/getContentAccessList]}: getContentAccessList(JSONObject)
	{POST [/ksiteContentAccess/add]}: add(KsiteContentAccess)
[18:04:11:804] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteFavoriteController:
	{POST [/ksiteFavorite/deleteByColumns]}: deleteByColumns(KsiteFavorite)
	{GET [/ksiteFavorite/getFavorite]}: getFavorite(Integer,String,Integer)
	{POST [/ksiteFavorite/getFavoriteList]}: getFavoriteList(JSONObject)
	{POST [/ksiteFavorite/add]}: add(KsiteFavorite)
[18:04:11:804] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteOrderController:
	{ [/ksiteOrder/orderNotify]}: orderNotify(JSONObject)
	{ [/ksiteOrder/createOrder]}: createKsiteOrder(JSONObject)
	{POST [/ksiteOrder/getOrderListWithKsiteInfo]}: getOrderListWithKsiteInfo(JSONObject)
	{POST [/ksiteOrder/getOrderList]}: getOrderListAndNum(JSONObject)
	{GET [/ksiteOrder/getAmountOfIncome]}: getAmountOfIncome(Integer)
	{GET [/ksiteOrder/getSettlementDashboardData]}: getSettlementDashboardData(Integer)
	{POST [/ksiteOrder/getOrderNum]}: getOrderNum(JSONObject)
	{POST [/ksiteOrder/update]}: update(JSONObject)
[18:04:11:805] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteOrderRelationController:
	{POST [/ksiteOrderRelation/checkContentAuth]}: checkContentAuth(KsiteOrderRelation)
[18:04:11:806] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsitePaperInstanceController:
	{GET [/ksitePaperInstance/getPaperInstanceStageSummaryDetail]}: getPaperInstanceStageSummaryDetail(Integer,String,String,Integer,Integer)
	{POST [/ksitePaperInstance/create]}: create(JSONObject)
[18:04:11:806] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsitePaperInstanceProcessController:
	{GET [/ksitePaperInstanceProcess/getPaperInstanceProcessWrapperListAndNum]}: getPaperInstanceProcessWrapperListAndNum(Integer,Boolean,Integer,Integer)
[18:04:11:808] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteUserSignatureController:
	{POST [/ksiteUserSignature/signKsite]}: sign(KsiteUserSignatureWrapper)
	{GET [/ksiteUserSignature/getUserSignature]}: getUserSignature(Integer,Integer)
	{GET [/ksiteUserSignature/checkKsiteNameExisted]}: checkKsiteNameExisted(String)
	{GET [/ksiteUserSignature/getKsiteInfo]}: getKsiteInfo(String,Integer)
	{POST [/ksiteUserSignature/update]}: update(KsiteUserSignatureWrapper)
	{POST [/ksiteUserSignature/getList]}: getList(JSONArray)
[18:04:11:810] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteWithdrawController:
	{POST [/ksiteWithdraw/withdraw]}: withdraw(KsiteWithdraw)
[18:04:11:849] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.m.c.CategoryController:
	{POST [/category/getBatchPathOfCategoryList]}: getBatchPathOfCategoryList(JSONArray)
	{GET [/category/getNLevelCategory]}: getLevelCategoriesOfLessThanOrEqualDesignativeLevel(String,Integer,Integer)
	{POST [/category/addNewCategory]}: addNewCategory(JSONObject)
	{GET [/category/getPathOfCategory]}: getPathOfCategory(Integer)
	{POST [/category/updateCategory]}: updateCategory(JSONObject)
	{POST [/category/updateCategoryList]}: updateCategoryList(JSONArray)
	{GET [/category/copySpecifiedSpaceName]}: copySpecifiedSpaceName(String,Integer,String)
	{POST [/category/add]}: add(JSONObject)
	{POST [/category/update]}: update(JSONObject)
[18:04:11:850] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QiyeweixinAccountController:
	{GET [/qiyeweixinAccount/transferLicense]}: transferLicense(String,List)
	{GET [/qiyeweixinAccount/getMemberActiveInfo]}: getMemberActiveInfo(String,String)
	{GET [/qiyeweixinAccount/queryAutoActiveStatus]}: queryAutoActiveStatus(String)
	{GET [/qiyeweixinAccount/getActivatedAccountList]}: getActivatedAccountList(String,Integer,String)
[18:04:11:851] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QiyeweixinCommunicationController:
	{ [/qiyeweixinCommunication/receiveData]}: receiveData(String,String,String,String,HttpServletRequest)
	{ [/qiyeweixinCommunication/receiveCommand]}: receiveCommand(String,String,String,String,HttpServletRequest)
	{GET [/qiyeweixinCommunication/getPreAuthCode]}: getPreAuthCode(String)
	{GET [/qiyeweixinCommunication/setSessionInfo]}: setSessionInfo(String)
	{GET [/qiyeweixinCommunication/generatePermanentCode]}: generatePermanentCode(String,String)
	{GET [/qiyeweixinCommunication/getAppQrcode]}: getAppQrcode(String)
	{GET [/qiyeweixinCommunication/getAppPermission]}: getAppPermission(String,String)
	{GET [/qiyeweixinCommunication/getAppAdmin]}: getAppAdmin(String,String)
	{GET [/qiyeweixinCommunication/getJsapiTicket]}: getJsapiTicket(String,String,String)
[18:04:11:851] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QiyeweixinSignatureController:
	{POST [/qiyeweixinSignature/getSignature]}: getSignature(JSONObject)
[18:04:11:851] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QiyeweixinUserController:
	{GET [/qiyeweixinUser/login]}: login(HttpServletRequest)
	{GET [/qiyeweixinUser/loginQkkQiyeweixin]}: wechatLogin()
	{GET [/qiyeweixinUser/loginQkkQiyeweixinFromPCWeb]}: wechatLoginFromPCWeb(String)
	{GET [/qiyeweixinUser/syncAllDepartmentNameAndUserNameByOCR]}: syncAllDepartmentNameAndUserNameByOCR(Integer)
	{GET [/qiyeweixinUser/refreshDepartmentNameAndUserNameByOCR]}: refreshDepartmentNameAndUserNameByOCR(Integer)
	{GET [/qiyeweixinUser/getUserInfo]}: getUserInfo(HttpServletRequest)
[18:04:11:852] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.CompanyControllerOfQKK:
	
[18:04:11:852] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.CompanyRegisterFormController:
	{GET [/companyRegisterForm/getCompanyRegisterForm]}: getCompanyRegisterForm(Integer)
	{GET [/companyRegisterForm/getCompanyRegisterFormFieldStructure]}: getCompanyRegisterFormFieldStructure(Integer)
	{POST [/companyRegisterForm/insertIfNotExisted]}: insertIfNotExisted(CompanyRegisterForm)
[18:04:11:853] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.CompanyRegisterFormFlowRecordController:
	{GET [/companyRegisterFormFlowRecord/getRegistrationDetail]}: getRegistrationDetail(Integer,Integer)
[18:04:11:853] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.ExaminationControllerOfQkk:
	{POST [/examination/getExaminationInViewListOfCompanyByTagesAndUserCompany]}: getExaminationInViewListOfCompanyByTagesAndUserCompany(JSONObject)
	{POST [/examination/getExaminationInViewListAndTotalSizeOfCompanyByTagesAndUserCompany]}: getExaminationInViewListAndTotalSizeOfCompanyByTagesAndUserCompany(JSONObject)
	{GET [/examination/getExamList]}: getExamList(Integer)
	{GET [/examination/checkIfAuthorized]}: checkIfAuthorized(Integer,Integer,Integer)
[18:04:11:853] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.ExaminationInstanceControllerOfQkk:
	{GET [/examinationInstanceOfQkk/exportTotalScoreGroupByUser]}: exportTotalScoreGroupByUser(String,HttpServletResponse)
	{GET [/examinationInstanceOfQkk/getExaminationInstanceListWithUserInfoByExaminationId]}: getExaminationInstanceListWithUserInfoByExaminationId(Integer,Integer,Integer,String,Integer,Integer)
	{GET [/examinationInstanceOfQkk/examinationInstanceStageSummaryDetail]}: getExaminationInstanceStageSummaryDetail(Integer,Integer,Integer,String,String,Integer,Integer)
	{POST [/examinationInstanceOfQkk/getTotalScoreOfExaminationListGroupByUser]}: getTotalScoreOfExaminationListGroupByUser(JSONObject)
	{POST [/examinationInstanceOfQkk/getSummaryGroupByDepartment]}: getSummaryGroupByDepartment(JSONObject)
	{GET [/examinationInstanceOfQkk/exportSummaryGroupByDepartment]}: exportSummaryGroupByDepartment(Integer,Integer,Integer,HttpServletResponse)
	{POST [/examinationInstanceOfQkk/getTopNDepartmentDistribution]}: getTopNDepartmentDistribution(JSONObject)
[18:04:11:858] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.PageDataController:
	{POST [/pageData/getTotalScoreGroupByUserInCompany]}: getTotalScoreGroupByUserInCompany(JSONObject)
[18:04:11:858] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QuestionControllerOfQkk:
	{POST [/question/questionListExcludingCreaterInCompany]}: getQuestionListExcludingCreaterInCompany(JSONObject)
[18:04:11:858] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.UserControllerOfQkk:
	{POST [/user/secureAdminRegister]}: secureAdminRegister(HttpServletRequest)
	{POST [/user/registerAdminFromMp]}: registerAdminFromMp(JSONObject,HttpServletRequest)
	{POST [/user/registerAdminFromH5]}: registerAdminFromH5(JSONObject,HttpServletRequest)
	{POST [/user/secureExamineeRegister]}: secureExamineeRegister(HttpServletRequest,HttpServletResponse)
	{POST [/user/registerExamineeFromMp]}: registerExamineeFromMp(JSONObject,HttpServletRequest)
	{POST [/user/registerExamineeFromH5]}: registerExamineeFromH5(JSONObject,HttpServletRequest)
	{POST [/user/secureLogin]}: secureLogin(HttpServletRequest,HttpServletResponse)
	{GET [/user/mergeCompanyUser]}: mergeCompanyUser(Integer)
[18:04:11:859] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoMiniProgramController:
	{POST [/toutiaoMiniProgram/regist]}: regist(JSONObject)
	{POST [/toutiaoMiniProgram/autoLogin]}: autologin(JSONObject)
	{POST [/toutiaoMiniProgram/update]}: update(JSONObject)
[18:04:11:859] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoQuestionStarController:
	{GET [/touTiaoQuestionStar/getQuestionStarWrapper]}: getQuestionStarWrapper(Integer)
[18:04:11:859] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoQuestionStarStageEventController:
	{GET [/toutiaoQuestionStarStageEvent/getMyPlayedStageList]}: getMyPlayedStageList(Integer,Integer)
	{POST [/toutiaoQuestionStarStageEvent/insertIfNotExisted]}: insertIfNotExisted(ToutiaoQuestionStarStageEvent)
[18:04:11:859] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoQuestionStarUserController:
	{GET [/toutiaoQuestionStarUser/getUserInfo]}: getUserInfo(Integer)
[18:04:11:859] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoQuestionStarUserProfileController:
	{POST [/toutiaoQuestionStarUserProfile/markPass]}: markPass(JSONObject)
	{POST [/toutiaoQuestionStarUserProfile/increaseExp]}: increaseExp(JSONObject)
	{POST [/toutiaoQuestionStarUserProfile/getRankInfoList]}: getRankInfoList(JSONObject)
	{GET [/toutiaoQuestionStarUserProfile/getUserInfo]}: getUserInfo(Integer)
[18:04:11:860] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.x.UserSchoolController:
	{POST [/userSchool/bindSchool]}: bindCompany(JSONObject)
[18:04:11:864] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.o.c.OssFileController:
	{POST [/ossFile/update]}: update(OssFile)
[18:04:11:870] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
	{ [/error]}: error(HttpServletRequest)
[18:04:11:896] [DEBUG] - org.springframework.web.servlet.handler.AbstractDetectingUrlHandlerMapping.detectHandlers(AbstractDetectingUrlHandlerMapping.java:86) - 'beanNameHandlerMapping' {}
[18:04:11:979] [DEBUG] - org.springframework.web.servlet.handler.SimpleUrlHandlerMapping.logMappings(SimpleUrlHandlerMapping.java:177) - 'webSocketHandlerMapping' {/wss/pkGameSocket=org.springframework.web.socket.server.support.WebSocketHttpRequestHandler@707237b7}
[18:04:12:067] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$MapperScannerRegistrarNotFoundConfiguration.afterPropertiesSet(MybatisAutoConfiguration.java:305) - No org.mybatis.spring.mapper.MapperFactoryBean found.
[18:04:12:525] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[18:04:13:091] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[18:04:13:094] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[18:04:13:095] [DEBUG] - com.taurus.examinationassistant.filter.AdminAuthFilter.init(AdminAuthFilter.java:45) - AdminAuthFilter初始化
[18:04:13:097] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[18:04:13:104] [DEBUG] - org.xnio.XnioWorker$Builder.build(XnioWorker.java:1191) - Creating worker:null, pool size:64, max pool size:64, keep alive:60000, io threads:8, stack size:0
[18:04:13:116] [DEBUG] - io.undertow.Undertow.start(Undertow.java:160) - Configuring listener with protocol HTTP for interface 0.0.0.0 and port 8081
[18:04:13:117] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-2 I/O-6', selector sun.nio.ch.KQueueSelectorImpl@1bcc6ef4
[18:04:13:116] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-2 I/O-7', selector sun.nio.ch.KQueueSelectorImpl@49db559c
[18:04:13:118] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-2 I/O-3', selector sun.nio.ch.KQueueSelectorImpl@458534f1
[18:04:13:116] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-2 I/O-1', selector sun.nio.ch.KQueueSelectorImpl@54a1d77c
[18:04:13:116] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-2 I/O-2', selector sun.nio.ch.KQueueSelectorImpl@4ced8d34
[18:04:13:116] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-2 I/O-8', selector sun.nio.ch.KQueueSelectorImpl@4ad76af8
[18:04:13:116] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-2 Accept', selector sun.nio.ch.KQueueSelectorImpl@e31e96c
[18:04:13:117] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-2 I/O-4', selector sun.nio.ch.KQueueSelectorImpl@72635535
[18:04:13:117] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-2 I/O-5', selector sun.nio.ch.KQueueSelectorImpl@551526bd
[18:04:13:149] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service **************:8081 register finished
[18:04:13:281] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 15.614 seconds (JVM running for 579.344)
[18:04:13:296] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[18:04:13:331] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`account` , t.`app_id` , t.`app_secret` , t.`token` , t.`encoding_aes_key` FROM `weixin_account` t 
[18:04:13:337] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 
[18:04:13:364] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 13
[18:04:13:364] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:04:35:679] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /examinationInstance/getCountResult, authentication required: false
[18:04:35:687] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@7bae3dce for /examinationInstance/getCountResult
[18:04:35:688] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /examinationInstance/getCountResult
[18:04:35:691] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[18:04:35:886] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT count(*) FROM `examination_instance` t WHERE examination_id = ? AND deleted_by_admin = ? AND company_id = ? 
[18:04:35:891] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169000(Integer), false(Boolean), 0(Integer)
[18:04:35:916] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:04:35:917] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:04:36:054] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /examinationInstance/exportDetailRankListOfExamination, authentication required: false
[18:04:36:055] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@7bae3dce for /examinationInstance/exportDetailRankListOfExamination
[18:04:36:055] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /examinationInstance/exportDetailRankListOfExamination
[18:04:36:067] [DEBUG] - com.taurus.examinationassistant.controller.ExaminationInstanceController.exportDetailRankListOfExamination(ExaminationInstanceController.java:1359) - 全民考试助手-导出某场考试所有的考生答题详情，对错情况等，按时间顺序排列，不去重，无排名
[18:04:36:156] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT count(*) FROM `examination_instance` t WHERE examination_id = ? AND deleted_by_admin = ? AND company_id = ? 
[18:04:36:156] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169000(Integer), false(Boolean), 0(Integer)
[18:04:36:172] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:04:36:175] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`code` , t.`name` , t.`paper_id` , t.`notice_enabled` , t.`notice` , t.`password_enabled` , t.`password` , t.`redo_type` , t.`redo_number` , t.`if_display_score` , t.`if_fill_in_name` , t.`if_fill_in_name_all_time` , t.`illustration_relative_url` , t.`illustration_file_name` , t.`state` , t.`pass_score` , t.`begin_time` , t.`end_time` , t.`duration` , t.`display_mode` , t.`creater_id` , t.`create_time` , t.`enabled` , t.`examinee_num` , t.`exam_total_times` , t.`if_display_review_process` , t.`quit_exam_as_auto_commit` , t.`warn_enabled` , t.`warn_times` , t.`if_display_wrong_question` , t.`if_display_right_question` , t.`if_display_answer` , t.`if_display_group` , t.`entrance_template` , t.`exit_template` , t.`examinee_info_template` , t.`examinee_collection_form_id` , t.`favorite_num` , t.`company_id` , t.`if_display_to_examinee` , t.`if_auto_next_question` , t.`if_shuffle_selection_options` , t.`no_ad_enabled` , t.`camera_enabled` , t.`hide_rank` , t.`auth_user` , t.`if_validate_content` , t.`time_per_question` , t.`wx_group_enabled` , t.`open_limit_enabled` , t.`open_limit_times` , t.`collect_wrong_answer` , t.`two_options_required` , t.`illustration_uri` , t.`handwriting_signature_enabled` , t.`record_screen_allowed` , t.`if_ai_scoring` , t.`client` , t.`network_limit_enabled` , t.`selected_wifi_list` FROM `examination` t WHERE t.`id` = ? LIMIT 1 
[18:04:36:176] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169000(Integer)
[18:04:36:201] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:04:36:202] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:04:36:209] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.id as instanceId , t.begin_time as beginTime , t.end_time as endTime , t.total_score as totalScore , t.duration , t2.id as userId , t2.nickname , t2.avatar_url as avatarUrl , t2.name , t2.age , t2.memo , t2.phone , t2.company , t2.department , t2.store , t2.area , t2.position FROM `examination_instance` t INNER JOIN user t2 ON t.user_id=t2.id WHERE t.examination_id = ? AND t.company_id = ? AND ( (t.deleted_by_admin is null or t.deleted_by_admin =false) ) ORDER BY t.create_time DESC 
[18:04:36:209] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169000(Integer), 0(Integer)
[18:04:36:238] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:04:36:239] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:04:36:252] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`code` , t.`name` , t.`paper_id` , t.`notice_enabled` , t.`notice` , t.`password_enabled` , t.`password` , t.`redo_type` , t.`redo_number` , t.`if_display_score` , t.`if_fill_in_name` , t.`if_fill_in_name_all_time` , t.`illustration_relative_url` , t.`illustration_file_name` , t.`state` , t.`pass_score` , t.`begin_time` , t.`end_time` , t.`duration` , t.`display_mode` , t.`creater_id` , t.`create_time` , t.`enabled` , t.`examinee_num` , t.`exam_total_times` , t.`if_display_review_process` , t.`quit_exam_as_auto_commit` , t.`warn_enabled` , t.`warn_times` , t.`if_display_wrong_question` , t.`if_display_right_question` , t.`if_display_answer` , t.`if_display_group` , t.`entrance_template` , t.`exit_template` , t.`examinee_info_template` , t.`examinee_collection_form_id` , t.`favorite_num` , t.`company_id` , t.`if_display_to_examinee` , t.`if_auto_next_question` , t.`if_shuffle_selection_options` , t.`no_ad_enabled` , t.`camera_enabled` , t.`hide_rank` , t.`auth_user` , t.`if_validate_content` , t.`time_per_question` , t.`wx_group_enabled` , t.`open_limit_enabled` , t.`open_limit_times` , t.`collect_wrong_answer` , t.`two_options_required` , t.`illustration_uri` , t.`handwriting_signature_enabled` , t.`record_screen_allowed` , t.`if_ai_scoring` , t.`client` , t.`network_limit_enabled` , t.`selected_wifi_list` FROM `examination` t WHERE t.`id` = ? LIMIT 1 
[18:04:36:252] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169000(Integer)
[18:04:36:269] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:04:36:286] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select user_id as userId,form_flow_record_id as formFlowRecordId from user_form_flow_record where form_id=? and user_id in ( ? ) 
[18:04:36:287] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 3(Integer), 37377(Integer)
[18:04:36:304] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:04:36:304] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:04:36:525] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.id,t.examination_id as examinationId,t.examination_instance_id as examinationInstanceId,t.user_id as userId,t.question_type as questionType,t.question_id as questionId,t.sequence_number_in_paper as sequenceNumberInPaper,t.answer_content as answerContent,t.question_mark as questionMark,t.score,t.if_right as ifRight,t.create_time as createTime , t2.main_content as questionMainContent , t2.options , t2.tip_content as questionTipContent FROM `examination_instance_process` t LEFT JOIN question t2 ON t.question_id = t2.id WHERE t.examination_id = ? ORDER BY t.question_type DESC,t.sequence_number_in_paper ASC 
[18:04:36:526] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169000(Integer)
[18:04:36:563] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 5
[18:04:36:565] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[18:04:37:142] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`code` , t.`name` , t.`paper_id` , t.`notice_enabled` , t.`notice` , t.`password_enabled` , t.`password` , t.`redo_type` , t.`redo_number` , t.`if_display_score` , t.`if_fill_in_name` , t.`if_fill_in_name_all_time` , t.`illustration_relative_url` , t.`illustration_file_name` , t.`state` , t.`pass_score` , t.`begin_time` , t.`end_time` , t.`duration` , t.`display_mode` , t.`creater_id` , t.`create_time` , t.`enabled` , t.`examinee_num` , t.`exam_total_times` , t.`if_display_review_process` , t.`quit_exam_as_auto_commit` , t.`warn_enabled` , t.`warn_times` , t.`if_display_wrong_question` , t.`if_display_right_question` , t.`if_display_answer` , t.`if_display_group` , t.`entrance_template` , t.`exit_template` , t.`examinee_info_template` , t.`examinee_collection_form_id` , t.`favorite_num` , t.`company_id` , t.`if_display_to_examinee` , t.`if_auto_next_question` , t.`if_shuffle_selection_options` , t.`no_ad_enabled` , t.`camera_enabled` , t.`hide_rank` , t.`auth_user` , t.`if_validate_content` , t.`time_per_question` , t.`wx_group_enabled` , t.`open_limit_enabled` , t.`open_limit_times` , t.`collect_wrong_answer` , t.`two_options_required` , t.`illustration_uri` , t.`handwriting_signature_enabled` , t.`record_screen_allowed` , t.`if_ai_scoring` , t.`client` , t.`network_limit_enabled` , t.`selected_wifi_list` FROM `examination` t WHERE t.`id` = ? LIMIT 1 
[18:04:37:144] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169000(Integer)
[18:04:37:174] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:04:37:834] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /version/getSystemSettings, authentication required: false
[18:04:37:834] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /access/getTokenStr, authentication required: false
[18:04:37:844] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@7bae3dce for /version/getSystemSettings
[18:04:37:844] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@7bae3dce for /access/getTokenStr
[18:04:37:846] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /access/getTokenStr
[18:04:37:846] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /version/getSystemSettings
[18:04:37:849] [DEBUG] - com.taurus.examinationassistant.filter.SqlXssFilter.doFilter(SqlXssFilter.java:109) - Skipping SqlXssFilter for excluded path: /access/getTokenStr
[18:04:38:266] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /weixinAccount/loginWithoutUserInfoAndAutoRegist, authentication required: false
[18:04:38:269] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@7bae3dce for /weixinAccount/loginWithoutUserInfoAndAutoRegist
[18:04:38:269] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /weixinAccount/loginWithoutUserInfoAndAutoRegist
[18:04:38:850] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /universalProductOrderRelation/getProductOrderRelation, authentication required: false
[18:04:38:852] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@7bae3dce for /universalProductOrderRelation/getProductOrderRelation
[18:04:38:852] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /universalProductOrderRelation/getProductOrderRelation
[18:05:13:720] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /examinationInstance/getCountResult, authentication required: false
[18:05:13:725] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@7bae3dce for /examinationInstance/getCountResult
[18:05:13:725] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /examinationInstance/getCountResult
[18:05:13:777] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT count(*) FROM `examination_instance` t WHERE examination_id = ? AND deleted_by_admin = ? AND company_id = ? 
[18:05:13:779] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169000(Integer), false(Boolean), 0(Integer)
[18:05:13:801] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:05:13:858] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /examinationInstance/exportDetailRankListOfExamination, authentication required: false
[18:05:13:859] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@7bae3dce for /examinationInstance/exportDetailRankListOfExamination
[18:05:13:859] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /examinationInstance/exportDetailRankListOfExamination
[18:05:13:861] [DEBUG] - com.taurus.examinationassistant.controller.ExaminationInstanceController.exportDetailRankListOfExamination(ExaminationInstanceController.java:1359) - 全民考试助手-导出某场考试所有的考生答题详情，对错情况等，按时间顺序排列，不去重，无排名
[18:05:13:883] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT count(*) FROM `examination_instance` t WHERE examination_id = ? AND deleted_by_admin = ? AND company_id = ? 
[18:05:13:883] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169000(Integer), false(Boolean), 0(Integer)
[18:05:13:899] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:05:13:900] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`code` , t.`name` , t.`paper_id` , t.`notice_enabled` , t.`notice` , t.`password_enabled` , t.`password` , t.`redo_type` , t.`redo_number` , t.`if_display_score` , t.`if_fill_in_name` , t.`if_fill_in_name_all_time` , t.`illustration_relative_url` , t.`illustration_file_name` , t.`state` , t.`pass_score` , t.`begin_time` , t.`end_time` , t.`duration` , t.`display_mode` , t.`creater_id` , t.`create_time` , t.`enabled` , t.`examinee_num` , t.`exam_total_times` , t.`if_display_review_process` , t.`quit_exam_as_auto_commit` , t.`warn_enabled` , t.`warn_times` , t.`if_display_wrong_question` , t.`if_display_right_question` , t.`if_display_answer` , t.`if_display_group` , t.`entrance_template` , t.`exit_template` , t.`examinee_info_template` , t.`examinee_collection_form_id` , t.`favorite_num` , t.`company_id` , t.`if_display_to_examinee` , t.`if_auto_next_question` , t.`if_shuffle_selection_options` , t.`no_ad_enabled` , t.`camera_enabled` , t.`hide_rank` , t.`auth_user` , t.`if_validate_content` , t.`time_per_question` , t.`wx_group_enabled` , t.`open_limit_enabled` , t.`open_limit_times` , t.`collect_wrong_answer` , t.`two_options_required` , t.`illustration_uri` , t.`handwriting_signature_enabled` , t.`record_screen_allowed` , t.`if_ai_scoring` , t.`client` , t.`network_limit_enabled` , t.`selected_wifi_list` FROM `examination` t WHERE t.`id` = ? LIMIT 1 
[18:05:13:901] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169000(Integer)
[18:05:13:923] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:05:13:926] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.id as instanceId , t.begin_time as beginTime , t.end_time as endTime , t.total_score as totalScore , t.duration , t2.id as userId , t2.nickname , t2.avatar_url as avatarUrl , t2.name , t2.age , t2.memo , t2.phone , t2.company , t2.department , t2.store , t2.area , t2.position FROM `examination_instance` t INNER JOIN user t2 ON t.user_id=t2.id WHERE t.examination_id = ? AND t.company_id = ? AND ( (t.deleted_by_admin is null or t.deleted_by_admin =false) ) ORDER BY t.create_time DESC 
[18:05:13:927] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169000(Integer), 0(Integer)
[18:05:13:946] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:05:13:946] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`code` , t.`name` , t.`paper_id` , t.`notice_enabled` , t.`notice` , t.`password_enabled` , t.`password` , t.`redo_type` , t.`redo_number` , t.`if_display_score` , t.`if_fill_in_name` , t.`if_fill_in_name_all_time` , t.`illustration_relative_url` , t.`illustration_file_name` , t.`state` , t.`pass_score` , t.`begin_time` , t.`end_time` , t.`duration` , t.`display_mode` , t.`creater_id` , t.`create_time` , t.`enabled` , t.`examinee_num` , t.`exam_total_times` , t.`if_display_review_process` , t.`quit_exam_as_auto_commit` , t.`warn_enabled` , t.`warn_times` , t.`if_display_wrong_question` , t.`if_display_right_question` , t.`if_display_answer` , t.`if_display_group` , t.`entrance_template` , t.`exit_template` , t.`examinee_info_template` , t.`examinee_collection_form_id` , t.`favorite_num` , t.`company_id` , t.`if_display_to_examinee` , t.`if_auto_next_question` , t.`if_shuffle_selection_options` , t.`no_ad_enabled` , t.`camera_enabled` , t.`hide_rank` , t.`auth_user` , t.`if_validate_content` , t.`time_per_question` , t.`wx_group_enabled` , t.`open_limit_enabled` , t.`open_limit_times` , t.`collect_wrong_answer` , t.`two_options_required` , t.`illustration_uri` , t.`handwriting_signature_enabled` , t.`record_screen_allowed` , t.`if_ai_scoring` , t.`client` , t.`network_limit_enabled` , t.`selected_wifi_list` FROM `examination` t WHERE t.`id` = ? LIMIT 1 
[18:05:13:947] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169000(Integer)
[18:05:13:963] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:05:13:964] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select user_id as userId,form_flow_record_id as formFlowRecordId from user_form_flow_record where form_id=? and user_id in ( ? ) 
[18:05:13:965] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 3(Integer), 37377(Integer)
[18:05:13:981] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:05:14:046] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.id,t.examination_id as examinationId,t.examination_instance_id as examinationInstanceId,t.user_id as userId,t.question_type as questionType,t.question_id as questionId,t.sequence_number_in_paper as sequenceNumberInPaper,t.answer_content as answerContent,t.question_mark as questionMark,t.score,t.if_right as ifRight,t.create_time as createTime , t2.main_content as questionMainContent , t2.options , t2.tip_content as questionTipContent FROM `examination_instance_process` t LEFT JOIN question t2 ON t.question_id = t2.id WHERE t.examination_id = ? ORDER BY t.question_type DESC,t.sequence_number_in_paper ASC 
[18:05:14:047] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169000(Integer)
[18:05:14:080] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 5
[18:05:14:141] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`code` , t.`name` , t.`paper_id` , t.`notice_enabled` , t.`notice` , t.`password_enabled` , t.`password` , t.`redo_type` , t.`redo_number` , t.`if_display_score` , t.`if_fill_in_name` , t.`if_fill_in_name_all_time` , t.`illustration_relative_url` , t.`illustration_file_name` , t.`state` , t.`pass_score` , t.`begin_time` , t.`end_time` , t.`duration` , t.`display_mode` , t.`creater_id` , t.`create_time` , t.`enabled` , t.`examinee_num` , t.`exam_total_times` , t.`if_display_review_process` , t.`quit_exam_as_auto_commit` , t.`warn_enabled` , t.`warn_times` , t.`if_display_wrong_question` , t.`if_display_right_question` , t.`if_display_answer` , t.`if_display_group` , t.`entrance_template` , t.`exit_template` , t.`examinee_info_template` , t.`examinee_collection_form_id` , t.`favorite_num` , t.`company_id` , t.`if_display_to_examinee` , t.`if_auto_next_question` , t.`if_shuffle_selection_options` , t.`no_ad_enabled` , t.`camera_enabled` , t.`hide_rank` , t.`auth_user` , t.`if_validate_content` , t.`time_per_question` , t.`wx_group_enabled` , t.`open_limit_enabled` , t.`open_limit_times` , t.`collect_wrong_answer` , t.`two_options_required` , t.`illustration_uri` , t.`handwriting_signature_enabled` , t.`record_screen_allowed` , t.`if_ai_scoring` , t.`client` , t.`network_limit_enabled` , t.`selected_wifi_list` FROM `examination` t WHERE t.`id` = ? LIMIT 1 
[18:05:14:141] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 169000(Integer)
[18:05:14:161] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[18:05:14:590] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /access/getTokenStr, authentication required: false
[18:05:14:593] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@7bae3dce for /access/getTokenStr
[18:05:14:593] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /access/getTokenStr
[18:05:14:595] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /version/getSystemSettings, authentication required: false
[18:05:14:596] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@7bae3dce for /version/getSystemSettings
[18:05:14:596] [DEBUG] - com.taurus.examinationassistant.filter.SqlXssFilter.doFilter(SqlXssFilter.java:109) - Skipping SqlXssFilter for excluded path: /access/getTokenStr
[18:05:14:599] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /version/getSystemSettings
[18:05:15:189] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /weixinAccount/loginWithoutUserInfoAndAutoRegist, authentication required: false
[18:05:15:190] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@7bae3dce for /weixinAccount/loginWithoutUserInfoAndAutoRegist
[18:05:15:190] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /weixinAccount/loginWithoutUserInfoAndAutoRegist
[18:05:15:531] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /universalProductOrderRelation/getProductOrderRelation, authentication required: false
[18:05:15:532] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@7bae3dce for /universalProductOrderRelation/getProductOrderRelation
[18:05:15:532] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /universalProductOrderRelation/getProductOrderRelation
[18:06:15:158] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /**************:62836
[18:06:15:553] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /**************:63241
[18:06:15:610] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /**************:62816
[18:12:35:179] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[18:12:35:196] [DEBUG] - com.taurus.examinationassistant.filter.AdminAuthFilter.destroy(AdminAuthFilter.java:131) - AdminAuthFilter销毁
[18:12:35:196] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[18:12:35:197] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Destroying Spring FrameworkServlet 'dispatcherServlet'
[18:12:35:363] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-5-28
[18:12:35:362] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-5-13
[18:12:35:364] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-5-24
[18:12:35:363] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-5-29
[18:12:35:364] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-5-11
[18:12:35:361] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-5-12
[18:12:35:365] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-5-16
[18:12:35:363] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-5-2
[18:12:35:365] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-5-20
[18:12:35:363] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-5-32
[18:12:35:366] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-5-1
[18:12:35:366] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-5-25
[18:12:35:366] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-5-4
[18:12:35:361] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-5-17
[18:12:35:367] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-5-5
[18:12:35:367] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-5-9
[18:12:35:367] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-5-14
[18:12:35:367] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-5-8
[18:12:35:367] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-5-21
[18:12:35:361] [DEBUG] - io.netty.buffer.PoolThreadCache.free(PoolThreadCache.java:221) - Freed 15 thread-local buffer(s) from thread: redisson-netty-5-3
[18:12:35:416] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[18:12:35:464] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[18:12:35:824] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-2} closing ...
[18:12:35:837] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:12:35:838] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:12:35:838] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:12:35:838] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:12:35:838] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:12:35:838] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[18:12:35:843] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-2} closed
