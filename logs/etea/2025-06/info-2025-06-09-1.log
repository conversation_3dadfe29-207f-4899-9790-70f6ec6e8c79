[13:07:23:471] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[13:07:27:500] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[13:07:27:576] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[13:07:27:576] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[13:07:27:576] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[13:07:27:576] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[13:07:27:577] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[13:07:27:577] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[13:07:27:577] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[13:07:27:577] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[13:07:27:578] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[13:07:27:578] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[13:07:27:578] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[13:07:27:578] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[13:07:27:629] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[13:07:28:323] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[13:07:28:328] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[13:07:29:232] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[13:07:29:277] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[13:07:29:689] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[13:07:29:706] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[13:07:29:707] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[13:07:29:708] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[13:07:29:710] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[13:07:29:712] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[13:07:29:713] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[13:07:29:714] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[13:07:29:714] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[13:07:35:587] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[13:07:35:604] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[13:07:35:924] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[13:07:38:070] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：2.132秒
[13:07:47:856] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[13:07:47:940] [WARN] - io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:70) - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
[13:07:48:835] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[13:07:50:133] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[13:08:01:483] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[13:08:02:439] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 57 ms to scan 1 urls, producing 3 keys and 6 values 
[13:08:02:509] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 28 ms to scan 1 urls, producing 4 keys and 9 values 
[13:08:02:552] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 30 ms to scan 1 urls, producing 3 keys and 10 values 
[13:08:02:767] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 209 ms to scan 12 urls, producing 0 keys and 0 values 
[13:08:02:789] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
[13:08:02:807] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
[13:08:02:830] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 18 ms to scan 1 urls, producing 2 keys and 8 values 
[13:08:02:865] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 33 ms to scan 12 urls, producing 0 keys and 0 values 
[13:08:04:469] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[13:08:04:470] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[13:08:04:488] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[13:08:04:512] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[13:08:04:538] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[13:08:04:598] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[13:08:04:769] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service 192.168.8.183:8081 register finished
[13:08:05:188] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 44.249 seconds (JVM running for 45.811)
[13:08:05:213] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[13:09:33:544] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[13:10:10:927] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:215) - activeProfile:test-etea
[13:10:10:928] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:222) - targetUrl:https://www.qikaokao.com/qiyeweixin/#/qiyeweixinCompanyInfo?companyId=6264&qkkQiyeCorpid=wpfqwcEQAAVSuad4wjYG9PaKuK-zUwtg&qkkQiyeAgentId=1000013
[13:10:10:929] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:226) - serviceUrl:http://127.0.0.1:8077/api/ocr/recognizeUrl?url=%s&ocr=ali
[13:10:10:930] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:229) - url:http://127.0.0.1:8077/api/ocr/recognizeUrl?url=https%3A%2F%2Fwww.qikaokao.com%2Fqiyeweixin%2F%23%2FqiyeweixinCompanyInfo%3FcompanyId%3D6264%26qkkQiyeCorpid%3DwpfqwcEQAAVSuad4wjYG9PaKuK-zUwtg%26qkkQiyeAgentId%3D1000013&ocr=ali
[13:10:30:979] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:241) - result:{data=[部门列表, 人员列表, $$ 1731350 $$ 陈彦如 $$ 分隔符 $$ 1731351 $$ 霍述生 $$分隔符], errorMessage=success, errorCode=0}
[13:10:30:991] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1731350, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=陈彦如, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[13:10:31:237] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1731351, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=霍述生, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[13:11:21:599] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:215) - activeProfile:test-etea
[13:11:21:600] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:222) - targetUrl:https://www.qikaokao.com/qiyeweixin/#/qiyeweixinCompanyInfo?companyId=6257&qkkQiyeCorpid=wpfqwcEQAARpzOc8-M3cDAq2_ZeX_-2A&qkkQiyeAgentId=1000104
[13:11:21:600] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:226) - serviceUrl:http://127.0.0.1:8077/api/ocr/recognizeUrl?url=%s&ocr=ali
[13:11:21:600] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:229) - url:http://127.0.0.1:8077/api/ocr/recognizeUrl?url=https%3A%2F%2Fwww.qikaokao.com%2Fqiyeweixin%2F%23%2FqiyeweixinCompanyInfo%3FcompanyId%3D6257%26qkkQiyeCorpid%3DwpfqwcEQAARpzOc8-M3cDAq2_ZeX_-2A%26qkkQiyeAgentId%3D1000104&ocr=ali
[13:11:32:792] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:241) - result:{data=[部门列表, $$ 9413 $$ 技术委员会$$分隔符, 人员列表, $$ 1729855 $$ 罗正平$$ 分隔符 $$ 1729856 $$ 王春雷 $$ 分隔符, $$ 1729857 $$$文刘润洋$$分隔符 $$ 1729858 $$ 林宜锴 $$ 分隔符, $$ 1729859 $$ 石顷田$$ 分隔符 $$ 1729860 $$鲁方$$分隔符$$1729861 $$ 谭鹏 $$ 分隔符, $$ 1729862 $$ 王大勇$$ 分隔符 $$ 1729863 $$ 肖建光 $$ 分隔符, $$ 1729864 $$ 许俊飞 $$ 分隔符 $$ 1729865 $$ 谢展扬 $$ 分隔符, $$ 1729866 $$ 陈丽华 $$ 分隔符], errorMessage=success, errorCode=0}
[13:11:32:798] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9413, departName=技术委员会, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[13:11:32:959] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1729855, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=罗正平, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[13:11:33:040] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1729856, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=王春雷, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[13:11:33:179] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1729857, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=$文刘润洋, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[13:11:33:259] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1729858, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=林宜锴, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[13:11:33:371] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1729859, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=石顷田, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[13:11:33:468] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1729860, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=鲁方, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[13:11:33:562] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1729861, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=谭鹏, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[13:11:33:674] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1729862, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=王大勇, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[13:11:33:802] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1729863, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=肖建光, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[13:11:33:891] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1729864, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=许俊飞, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[13:11:34:010] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1729865, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=谢展扬, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[13:11:34:106] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1729866, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=陈丽华, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
