[11:01:27:868] [ERROR] - io.undertow.servlet.api.LoggingExceptionHandler.handleThrowable(LoggingExceptionHandler.java:80) - UT005023: Exception handling request to /permissionAuthorization/getAuthorizedCompanyInfoWith2Grade
java.lang.IllegalStateException: org.springframework.context.annotation.AnnotationConfigApplicationContext@4016e904 has been closed already
	at org.springframework.context.support.AbstractApplicationContext.assertBeanFactoryActive(AbstractApplicationContext.java:1152) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.getBeansOfType(AbstractApplicationContext.java:1321) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.BeanFactoryUtils.beansOfTypeIncludingAncestors(BeanFactoryUtils.java:378) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.BeanFactoryUtils.beansOfTypeIncludingAncestors(BeanFactoryUtils.java:382) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.initHandlerMappings(DispatcherServlet.java:595) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.initStrategies(DispatcherServlet.java:502) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.onRefresh(DispatcherServlet.java:491) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:599) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.GenericServlet.init(GenericServlet.java:180) ~[jakarta.servlet-api-4.0.4.jar:4.0.4]
	at io.undertow.servlet.core.LifecyleInterceptorInvocation.proceed(LifecyleInterceptorInvocation.java:117) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ManagedServlet$DefaultInstanceStrategy.start(ManagedServlet.java:309) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ManagedServlet.forceInit(ManagedServlet.java:212) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain.forceInit(ServletChain.java:130) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:63) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SessionRestoringHandler.handleRequest(SessionRestoringHandler.java:119) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1423) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_281]
[11:01:27:868] [ERROR] - io.undertow.servlet.api.LoggingExceptionHandler.handleThrowable(LoggingExceptionHandler.java:80) - UT005023: Exception handling request to /companyUserGroup/list
java.lang.IllegalStateException: org.springframework.context.annotation.AnnotationConfigApplicationContext@4016e904 has been closed already
	at org.springframework.context.support.AbstractApplicationContext.assertBeanFactoryActive(AbstractApplicationContext.java:1152) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.getBeansOfType(AbstractApplicationContext.java:1321) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.BeanFactoryUtils.beansOfTypeIncludingAncestors(BeanFactoryUtils.java:378) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.BeanFactoryUtils.beansOfTypeIncludingAncestors(BeanFactoryUtils.java:382) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.initHandlerMappings(DispatcherServlet.java:595) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.initStrategies(DispatcherServlet.java:502) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.onRefresh(DispatcherServlet.java:491) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:599) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.GenericServlet.init(GenericServlet.java:180) ~[jakarta.servlet-api-4.0.4.jar:4.0.4]
	at io.undertow.servlet.core.LifecyleInterceptorInvocation.proceed(LifecyleInterceptorInvocation.java:117) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ManagedServlet$DefaultInstanceStrategy.start(ManagedServlet.java:309) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ManagedServlet.forceInit(ManagedServlet.java:212) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain.forceInit(ServletChain.java:130) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:63) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SessionRestoringHandler.handleRequest(SessionRestoringHandler.java:119) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1423) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_281]
[11:01:27:952] [ERROR] - io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:316) - UT005022: Exception generating error page /error
java.lang.RuntimeException: java.lang.IllegalStateException: org.springframework.context.annotation.AnnotationConfigApplicationContext@4016e904 has been closed already
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:418) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:372) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:314) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1423) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_281]
Caused by: java.lang.IllegalStateException: org.springframework.context.annotation.AnnotationConfigApplicationContext@4016e904 has been closed already
	at org.springframework.context.support.AbstractApplicationContext.assertBeanFactoryActive(AbstractApplicationContext.java:1152) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.getBeansOfType(AbstractApplicationContext.java:1321) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.BeanFactoryUtils.beansOfTypeIncludingAncestors(BeanFactoryUtils.java:378) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.BeanFactoryUtils.beansOfTypeIncludingAncestors(BeanFactoryUtils.java:382) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.initHandlerMappings(DispatcherServlet.java:595) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.initStrategies(DispatcherServlet.java:502) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.onRefresh(DispatcherServlet.java:491) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:599) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.GenericServlet.init(GenericServlet.java:180) ~[jakarta.servlet-api-4.0.4.jar:4.0.4]
	at io.undertow.servlet.core.LifecyleInterceptorInvocation.proceed(LifecyleInterceptorInvocation.java:117) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ManagedServlet$DefaultInstanceStrategy.start(ManagedServlet.java:309) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ManagedServlet.forceInit(ManagedServlet.java:212) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain.forceInit(ServletChain.java:130) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:63) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SessionRestoringHandler.handleRequest(SessionRestoringHandler.java:119) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:257) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchToPath(ServletInitialHandler.java:182) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:414) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	... 18 more
[11:01:27:958] [ERROR] - io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:316) - UT005022: Exception generating error page /error
java.lang.RuntimeException: java.lang.IllegalStateException: org.springframework.context.annotation.AnnotationConfigApplicationContext@4016e904 has been closed already
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:418) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:372) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:314) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1423) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_281]
Caused by: java.lang.IllegalStateException: org.springframework.context.annotation.AnnotationConfigApplicationContext@4016e904 has been closed already
	at org.springframework.context.support.AbstractApplicationContext.assertBeanFactoryActive(AbstractApplicationContext.java:1152) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.getBeansOfType(AbstractApplicationContext.java:1321) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.BeanFactoryUtils.beansOfTypeIncludingAncestors(BeanFactoryUtils.java:378) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.BeanFactoryUtils.beansOfTypeIncludingAncestors(BeanFactoryUtils.java:382) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.initHandlerMappings(DispatcherServlet.java:595) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.initStrategies(DispatcherServlet.java:502) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.onRefresh(DispatcherServlet.java:491) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:599) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.GenericServlet.init(GenericServlet.java:180) ~[jakarta.servlet-api-4.0.4.jar:4.0.4]
	at io.undertow.servlet.core.LifecyleInterceptorInvocation.proceed(LifecyleInterceptorInvocation.java:117) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ManagedServlet$DefaultInstanceStrategy.start(ManagedServlet.java:309) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ManagedServlet.forceInit(ManagedServlet.java:212) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain.forceInit(ServletChain.java:130) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:63) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SessionRestoringHandler.handleRequest(SessionRestoringHandler.java:119) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:257) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchToPath(ServletInitialHandler.java:182) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:414) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	... 18 more
[12:05:41:349] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/wx/getTicket，出现异常:java.lang.NullPointerException；
[12:05:41:370] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"Origin":"http://localhost:8080","content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"application/json, text/plain, */*","Referer":"http://localhost:8080/","X-Forwarded-Host":"127.0.0.1:8000","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","X-Forwarded-Proto":"http","Sec-Fetch-Site":"cross-site","Sec-Fetch-Dest":"empty","Accept-Encoding":"gzip, deflate, br, zstd","userId":"1645573","X-Forwarded-Port":"8000","token":"","Sec-Fetch-Mode":"cors","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","host":"*************:8081","Forwarded":"proto=http;host=\"127.0.0.1:8000\";for=\"127.0.0.1:57379\"","client":"pc_admin_qkk","X-Forwarded-For":"127.0.0.1","Accept-Language":"zh-CN,zh;q=0.9"}
[12:05:41:371] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{}
[12:05:41:371] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：java.lang.NullPointerException，异常信息：null，
[12:05:43:280] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/wx/getTicket，出现异常:java.lang.NullPointerException；
[12:05:43:285] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"Origin":"http://localhost:8080","content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"application/json, text/plain, */*","Referer":"http://localhost:8080/","X-Forwarded-Host":"127.0.0.1:8000","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","X-Forwarded-Proto":"http","Sec-Fetch-Site":"cross-site","Sec-Fetch-Dest":"empty","Accept-Encoding":"gzip, deflate, br, zstd","userId":"1645573","X-Forwarded-Port":"8000","token":"","Sec-Fetch-Mode":"cors","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","host":"*************:8081","Forwarded":"proto=http;host=\"127.0.0.1:8000\";for=\"127.0.0.1:57400\"","client":"pc_admin_qkk","X-Forwarded-For":"127.0.0.1","Accept-Language":"zh-CN,zh;q=0.9"}
[12:05:43:286] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{}
[12:05:43:287] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：java.lang.NullPointerException，异常信息：null，
[12:06:19:265] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/examinationInstance/getExaminationStageSummaryDetail，出现异常:org.springframework.jdbc.BadSqlGrammarException；
[12:06:19:269] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"Origin":"http://localhost:8080","X-Forwarded-Prefix":"/exam","Accept":"application/json, text/plain, */*","Referer":"http://localhost:8080/","X-Forwarded-Host":"127.0.0.1:8000","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","X-Forwarded-Proto":"http","Sec-Fetch-Site":"cross-site","Sec-Fetch-Dest":"empty","Accept-Encoding":"gzip, deflate, br, zstd","userId":"1645573","X-Forwarded-Port":"8000","token":"","Sec-Fetch-Mode":"cors","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","host":"*************:8081","Forwarded":"proto=http;host=\"127.0.0.1:8000\";for=\"127.0.0.1:57511\"","client":"pc_admin_qkk","X-Forwarded-For":"127.0.0.1","Accept-Language":"zh-CN,zh;q=0.9","Content-Length":"130","Content-Type":"application/json"}
[12:06:19:270] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{}
[12:06:19:270] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: execute command denied to user 'exjjssid'@'223.160.207.%' for routine 'examination_assistant.queryChildDepartmentIncludingSelf'
### The error may exist in com/taurus/examinationassistant/mapper/ExaminationInstanceMapper.java (best guess)
### The error may involve com.taurus.examinationassistant.mapper.ExaminationInstanceMapper.getExaminationInstanceListOfExamination-Inline
### The error occurred while setting parameters
### SQL: select t.*,t2.name as examinationName,t2.pass_score,t3.id as userId,t3.nickname,t3.name,t3.qkk_qiye_user_id as qkkQiyeUserId,t4.mark ,t6.depart_name as departmentName,t6.qiyewx_depart_id as qiyewxDepartId  from examination_instance t LEFT JOIN examination t2 ON t.examination_id = t2.id LEFT JOIN user t3 ON t.user_id = t3.id LEFT JOIN paper t4 ON t2.paper_id=t4.id  LEFT JOIN user_company t5 ON t5.user_id = t.user_id and t5.company_id=? LEFT JOIN company_department t6 ON t5.department_id=t6.id  where t.enabled=true and (t.deleted_by_admin is null or t.deleted_by_admin=false) and t.examination_id=?  and t.company_id=?  and  FIND_IN_SET(t5.department_id,queryChildDepartmentIncludingSelf(?))   order by t.create_time desc
### Cause: java.sql.SQLSyntaxErrorException: execute command denied to user 'exjjssid'@'223.160.207.%' for routine 'examination_assistant.queryChildDepartmentIncludingSelf'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: execute command denied to user 'exjjssid'@'223.160.207.%' for routine 'examination_assistant.queryChildDepartmentIncludingSelf'，异常信息：
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: execute command denied to user 'exjjssid'@'223.160.207.%' for routine 'examination_assistant.queryChildDepartmentIncludingSelf'
### The error may exist in com/taurus/examinationassistant/mapper/ExaminationInstanceMapper.java (best guess)
### The error may involve com.taurus.examinationassistant.mapper.ExaminationInstanceMapper.getExaminationInstanceListOfExamination-Inline
### The error occurred while setting parameters
### SQL: select t.*,t2.name as examinationName,t2.pass_score,t3.id as userId,t3.nickname,t3.name,t3.qkk_qiye_user_id as qkkQiyeUserId,t4.mark ,t6.depart_name as departmentName,t6.qiyewx_depart_id as qiyewxDepartId  from examination_instance t LEFT JOIN examination t2 ON t.examination_id = t2.id LEFT JOIN user t3 ON t.user_id = t3.id LEFT JOIN paper t4 ON t2.paper_id=t4.id  LEFT JOIN user_company t5 ON t5.user_id = t.user_id and t5.company_id=? LEFT JOIN company_department t6 ON t5.department_id=t6.id  where t.enabled=true and (t.deleted_by_admin is null or t.deleted_by_admin=false) and t.examination_id=?  and t.company_id=?  and  FIND_IN_SET(t5.department_id,queryChildDepartmentIncludingSelf(?))   order by t.create_time desc
### Cause: java.sql.SQLSyntaxErrorException: execute command denied to user 'exjjssid'@'223.160.207.%' for routine 'examination_assistant.queryChildDepartmentIncludingSelf'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: execute command denied to user 'exjjssid'@'223.160.207.%' for routine 'examination_assistant.queryChildDepartmentIncludingSelf'，
[22:10:54:836] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/examinationInstance/getExaminationStageSummaryDetail，出现异常:org.apache.ibatis.exceptions.PersistenceException；
[22:10:54:849] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"Origin":"http://localhost:8080","X-Forwarded-Prefix":"/exam","Accept":"application/json, text/plain, */*","Referer":"http://localhost:8080/","X-Forwarded-Host":"127.0.0.1:8000","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","X-Forwarded-Proto":"http","Sec-Fetch-Site":"cross-site","Sec-Fetch-Dest":"empty","Accept-Encoding":"gzip, deflate, br, zstd","userId":"1645573","X-Forwarded-Port":"8000","token":"","Sec-Fetch-Mode":"cors","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","host":"**************:8081","Forwarded":"proto=http;host=\"127.0.0.1:8000\";for=\"127.0.0.1:55231\"","client":"pc_admin_qkk","X-Forwarded-For":"127.0.0.1","Accept-Language":"zh-CN,zh;q=0.9","Content-Length":"130","Content-Type":"application/json"}
[22:10:54:851] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{}
[22:10:54:851] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.sql.SQLException: Incorrect string value: '\xAC\xED\x00\x05sr...' for column 'departmentId' at row 1
### The error may exist in com/taurus/examinationassistant/mapper/ExaminationInstanceMapper.java (best guess)
### The error may involve com.taurus.examinationassistant.mapper.ExaminationInstanceMapper.getExaminationInstanceListOfExamination-Inline
### The error occurred while setting parameters
### SQL: select t.*,t2.name as examinationName,t2.pass_score,t3.id as userId,t3.nickname,t3.name,t3.qkk_qiye_user_id as qkkQiyeUserId,t4.mark ,t6.depart_name as departmentName,t6.qiyewx_depart_id as qiyewxDepartId  from examination_instance t LEFT JOIN examination t2 ON t.examination_id = t2.id LEFT JOIN user t3 ON t.user_id = t3.id LEFT JOIN paper t4 ON t2.paper_id=t4.id  LEFT JOIN user_company t5 ON t5.user_id = t.user_id and t5.company_id=? LEFT JOIN company_department t6 ON t5.department_id=t6.id  where t.enabled=true and (t.deleted_by_admin is null or t.deleted_by_admin=false) and t.examination_id=?  and t.company_id=?  and  FIND_IN_SET(t5.department_id,queryChildDepartmentIncludingSelf(?))   order by t.create_time desc
### Cause: java.sql.SQLException: Incorrect string value: '\xAC\xED\x00\x05sr...' for column 'departmentId' at row 1，异常信息：
### Error querying database.  Cause: java.sql.SQLException: Incorrect string value: '\xAC\xED\x00\x05sr...' for column 'departmentId' at row 1
### The error may exist in com/taurus/examinationassistant/mapper/ExaminationInstanceMapper.java (best guess)
### The error may involve com.taurus.examinationassistant.mapper.ExaminationInstanceMapper.getExaminationInstanceListOfExamination-Inline
### The error occurred while setting parameters
### SQL: select t.*,t2.name as examinationName,t2.pass_score,t3.id as userId,t3.nickname,t3.name,t3.qkk_qiye_user_id as qkkQiyeUserId,t4.mark ,t6.depart_name as departmentName,t6.qiyewx_depart_id as qiyewxDepartId  from examination_instance t LEFT JOIN examination t2 ON t.examination_id = t2.id LEFT JOIN user t3 ON t.user_id = t3.id LEFT JOIN paper t4 ON t2.paper_id=t4.id  LEFT JOIN user_company t5 ON t5.user_id = t.user_id and t5.company_id=? LEFT JOIN company_department t6 ON t5.department_id=t6.id  where t.enabled=true and (t.deleted_by_admin is null or t.deleted_by_admin=false) and t.examination_id=?  and t.company_id=?  and  FIND_IN_SET(t5.department_id,queryChildDepartmentIncludingSelf(?))   order by t.create_time desc
### Cause: java.sql.SQLException: Incorrect string value: '\xAC\xED\x00\x05sr...' for column 'departmentId' at row 1，
[22:21:28:937] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/examinationInstance/getExaminationStageSummaryDetail，出现异常:org.apache.ibatis.exceptions.PersistenceException；
[22:21:28:947] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"Origin":"http://localhost:8080","X-Forwarded-Prefix":"/exam","Accept":"application/json, text/plain, */*","Referer":"http://localhost:8080/","X-Forwarded-Host":"127.0.0.1:8000","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","X-Forwarded-Proto":"http","Sec-Fetch-Site":"cross-site","Sec-Fetch-Dest":"empty","Accept-Encoding":"gzip, deflate, br, zstd","userId":"1645573","X-Forwarded-Port":"8000","token":"","Sec-Fetch-Mode":"cors","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","host":"**************:8081","Forwarded":"proto=http;host=\"127.0.0.1:8000\";for=\"127.0.0.1:55679\"","client":"pc_admin_qkk","X-Forwarded-For":"127.0.0.1","Accept-Language":"zh-CN,zh;q=0.9","Content-Length":"100","Content-Type":"application/json"}
[22:21:28:947] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{}
[22:21:28:948] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.sql.SQLException: Incorrect string value: '\xAC\xED\x00\x05sr...' for column 'departmentId' at row 1
### The error may exist in com/taurus/examinationassistant/mapper/ExaminationInstanceMapper.java (best guess)
### The error may involve com.taurus.examinationassistant.mapper.ExaminationInstanceMapper.getExaminationInstanceListOfExamination-Inline
### The error occurred while setting parameters
### SQL: select t.*,t2.name as examinationName,t2.pass_score,t3.id as userId,t3.nickname,t3.name,t3.qkk_qiye_user_id as qkkQiyeUserId,t4.mark ,t6.depart_name as departmentName,t6.qiyewx_depart_id as qiyewxDepartId  from examination_instance t LEFT JOIN examination t2 ON t.examination_id = t2.id LEFT JOIN user t3 ON t.user_id = t3.id LEFT JOIN paper t4 ON t2.paper_id=t4.id  LEFT JOIN user_company t5 ON t5.user_id = t.user_id and t5.company_id=? LEFT JOIN company_department t6 ON t5.department_id=t6.id  where t.enabled=true and (t.deleted_by_admin is null or t.deleted_by_admin=false) and t.examination_id=?  and t.company_id=?  and  FIND_IN_SET(t5.department_id,queryChildDepartmentIncludingSelf(?))   order by t.create_time desc
### Cause: java.sql.SQLException: Incorrect string value: '\xAC\xED\x00\x05sr...' for column 'departmentId' at row 1，异常信息：
### Error querying database.  Cause: java.sql.SQLException: Incorrect string value: '\xAC\xED\x00\x05sr...' for column 'departmentId' at row 1
### The error may exist in com/taurus/examinationassistant/mapper/ExaminationInstanceMapper.java (best guess)
### The error may involve com.taurus.examinationassistant.mapper.ExaminationInstanceMapper.getExaminationInstanceListOfExamination-Inline
### The error occurred while setting parameters
### SQL: select t.*,t2.name as examinationName,t2.pass_score,t3.id as userId,t3.nickname,t3.name,t3.qkk_qiye_user_id as qkkQiyeUserId,t4.mark ,t6.depart_name as departmentName,t6.qiyewx_depart_id as qiyewxDepartId  from examination_instance t LEFT JOIN examination t2 ON t.examination_id = t2.id LEFT JOIN user t3 ON t.user_id = t3.id LEFT JOIN paper t4 ON t2.paper_id=t4.id  LEFT JOIN user_company t5 ON t5.user_id = t.user_id and t5.company_id=? LEFT JOIN company_department t6 ON t5.department_id=t6.id  where t.enabled=true and (t.deleted_by_admin is null or t.deleted_by_admin=false) and t.examination_id=?  and t.company_id=?  and  FIND_IN_SET(t5.department_id,queryChildDepartmentIncludingSelf(?))   order by t.create_time desc
### Cause: java.sql.SQLException: Incorrect string value: '\xAC\xED\x00\x05sr...' for column 'departmentId' at row 1，
[22:30:36:446] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/examinationInstance/getExaminationStageSummaryDetail，出现异常:org.apache.ibatis.exceptions.PersistenceException；
[22:30:36:452] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"Origin":"http://localhost:8080","X-Forwarded-Prefix":"/exam","Accept":"application/json, text/plain, */*","Referer":"http://localhost:8080/","X-Forwarded-Host":"127.0.0.1:8000","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","X-Forwarded-Proto":"http","Sec-Fetch-Site":"cross-site","Sec-Fetch-Dest":"empty","Accept-Encoding":"gzip, deflate, br, zstd","userId":"1645573","X-Forwarded-Port":"8000","token":"","Sec-Fetch-Mode":"cors","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","host":"**************:8081","Forwarded":"proto=http;host=\"127.0.0.1:8000\";for=\"127.0.0.1:55682\"","client":"pc_admin_qkk","X-Forwarded-For":"127.0.0.1","Accept-Language":"zh-CN,zh;q=0.9","Content-Length":"100","Content-Type":"application/json"}
[22:30:36:453] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{}
[22:30:36:453] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.sql.SQLException: Incorrect string value: '\xAC\xED\x00\x05sr...' for column 'departmentId' at row 1
### The error may exist in com/taurus/examinationassistant/mapper/ExaminationInstanceMapper.java (best guess)
### The error may involve com.taurus.examinationassistant.mapper.ExaminationInstanceMapper.getExaminationInstanceListOfExamination-Inline
### The error occurred while setting parameters
### SQL: select t.*,t2.name as examinationName,t2.pass_score,t3.id as userId,t3.nickname,t3.name,t3.qkk_qiye_user_id as qkkQiyeUserId,t4.mark ,t6.depart_name as departmentName,t6.qiyewx_depart_id as qiyewxDepartId  from examination_instance t LEFT JOIN examination t2 ON t.examination_id = t2.id LEFT JOIN user t3 ON t.user_id = t3.id LEFT JOIN paper t4 ON t2.paper_id=t4.id  LEFT JOIN user_company t5 ON t5.user_id = t.user_id and t5.company_id=? LEFT JOIN company_department t6 ON t5.department_id=t6.id  where t.enabled=true and (t.deleted_by_admin is null or t.deleted_by_admin=false) and t.examination_id=?  and t.company_id=?  and  FIND_IN_SET(t5.department_id,queryChildDepartmentIncludingSelf(?))   order by t.create_time desc
### Cause: java.sql.SQLException: Incorrect string value: '\xAC\xED\x00\x05sr...' for column 'departmentId' at row 1，异常信息：
### Error querying database.  Cause: java.sql.SQLException: Incorrect string value: '\xAC\xED\x00\x05sr...' for column 'departmentId' at row 1
### The error may exist in com/taurus/examinationassistant/mapper/ExaminationInstanceMapper.java (best guess)
### The error may involve com.taurus.examinationassistant.mapper.ExaminationInstanceMapper.getExaminationInstanceListOfExamination-Inline
### The error occurred while setting parameters
### SQL: select t.*,t2.name as examinationName,t2.pass_score,t3.id as userId,t3.nickname,t3.name,t3.qkk_qiye_user_id as qkkQiyeUserId,t4.mark ,t6.depart_name as departmentName,t6.qiyewx_depart_id as qiyewxDepartId  from examination_instance t LEFT JOIN examination t2 ON t.examination_id = t2.id LEFT JOIN user t3 ON t.user_id = t3.id LEFT JOIN paper t4 ON t2.paper_id=t4.id  LEFT JOIN user_company t5 ON t5.user_id = t.user_id and t5.company_id=? LEFT JOIN company_department t6 ON t5.department_id=t6.id  where t.enabled=true and (t.deleted_by_admin is null or t.deleted_by_admin=false) and t.examination_id=?  and t.company_id=?  and  FIND_IN_SET(t5.department_id,queryChildDepartmentIncludingSelf(?))   order by t.create_time desc
### Cause: java.sql.SQLException: Incorrect string value: '\xAC\xED\x00\x05sr...' for column 'departmentId' at row 1，
[23:30:48:922] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/wx/getTicket，出现异常:java.lang.NullPointerException；
[23:30:48:935] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"Origin":"http://localhost:8080","content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"application/json, text/plain, */*","Referer":"http://localhost:8080/","X-Forwarded-Host":"127.0.0.1:8000","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","X-Forwarded-Proto":"http","Sec-Fetch-Site":"cross-site","Sec-Fetch-Dest":"empty","Accept-Encoding":"gzip, deflate, br, zstd","userId":"1634696","X-Forwarded-Port":"8000","token":"","Sec-Fetch-Mode":"cors","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","host":"**************:8081","Forwarded":"proto=http;host=\"127.0.0.1:8000\";for=\"127.0.0.1:61359\"","client":"pc_admin_qkk","X-Forwarded-For":"127.0.0.1","Accept-Language":"zh-CN,zh;q=0.9"}
[23:30:48:936] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{}
[23:30:48:939] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：java.lang.NullPointerException，异常信息：null，
[23:30:49:429] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/wx/getTicket，出现异常:java.lang.NullPointerException；
[23:30:49:438] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"Origin":"http://localhost:8080","content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"application/json, text/plain, */*","Referer":"http://localhost:8080/","X-Forwarded-Host":"127.0.0.1:8000","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","X-Forwarded-Proto":"http","Sec-Fetch-Site":"cross-site","Sec-Fetch-Dest":"empty","Accept-Encoding":"gzip, deflate, br, zstd","userId":"1634696","X-Forwarded-Port":"8000","token":"","Sec-Fetch-Mode":"cors","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","host":"**************:8081","Forwarded":"proto=http;host=\"127.0.0.1:8000\";for=\"127.0.0.1:61341\"","client":"pc_admin_qkk","X-Forwarded-For":"127.0.0.1","Accept-Language":"zh-CN,zh;q=0.9"}
[23:30:49:440] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{}
[23:30:49:441] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：java.lang.NullPointerException，异常信息：null，
[23:30:51:137] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/wx/getTicket，出现异常:java.lang.NullPointerException；
[23:30:51:138] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"Origin":"http://localhost:8080","content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"application/json, text/plain, */*","Referer":"http://localhost:8080/","X-Forwarded-Host":"127.0.0.1:8000","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","X-Forwarded-Proto":"http","Sec-Fetch-Site":"cross-site","Sec-Fetch-Dest":"empty","Accept-Encoding":"gzip, deflate, br, zstd","userId":"1634696","X-Forwarded-Port":"8000","token":"","Sec-Fetch-Mode":"cors","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","host":"**************:8081","Forwarded":"proto=http;host=\"127.0.0.1:8000\";for=\"127.0.0.1:61381\"","client":"pc_admin_qkk","X-Forwarded-For":"127.0.0.1","Accept-Language":"zh-CN,zh;q=0.9"}
[23:30:51:139] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{}
[23:30:51:139] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：java.lang.NullPointerException，异常信息：null，
