[08:45:27:336] [ERROR] - com.taurus.monolith.config.MicroserviceConfigManager.checkMonolithConfigFile(MicroserviceConfigManager.java:710) - 检查monolith配置文件时出错: null
[12:59:40:875] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/version/getSystemSettings，出现异常:java.lang.NullPointerException；
[12:59:41:003] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"*/*","User-Agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.06.2412050 MicroMessenger/8.0.5 Language/zh_CN webview/","X-Forwarded-Proto":"http","Referer":"https://servicewechat.com/wxa38e954efdceffbe/devtools/page-frame.html","X-Forwarded-Host":"localhost:8000","Sec-Fetch-Dest":"empty","Sec-Fetch-Site":"cross-site","Accept-Encoding":"gzip, deflate, br","X-Forwarded-Port":"8000","Sec-Fetch-Mode":"cors","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:51685\"","host":"**************:8081","X-Forwarded-For":"0:0:0:0:0:0:0:1","content-type":"application/json"}
[12:59:41:007] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{}
[12:59:41:030] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：java.lang.NullPointerException，异常信息：null，
[12:59:41:173] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/examination/getRecommendExaminations，出现异常:java.lang.NullPointerException；
[12:59:41:175] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"*/*","User-Agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.06.2412050 MicroMessenger/8.0.5 Language/zh_CN webview/","X-Forwarded-Proto":"http","Referer":"https://servicewechat.com/wxa38e954efdceffbe/devtools/page-frame.html","X-Forwarded-Host":"localhost:8000","Sec-Fetch-Dest":"empty","Sec-Fetch-Site":"cross-site","Accept-Encoding":"gzip, deflate, br","X-Forwarded-Port":"8000","Sec-Fetch-Mode":"cors","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:51690\"","host":"**************:8081","X-Forwarded-For":"0:0:0:0:0:0:0:1","content-type":"application/json"}
[12:59:41:176] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"companyId":"0","pageIndex":"1","pageSize":"6","refresh":"true","createrId":"5","type":"public"}
[12:59:41:176] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：java.lang.NullPointerException，异常信息：null，
[12:59:41:253] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/weixinAccount/loginWithoutUserInfoAndAutoRegist，出现异常:java.lang.NullPointerException；
[12:59:41:254] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"*/*","Referer":"https://servicewechat.com/wxa38e954efdceffbe/devtools/page-frame.html","X-Forwarded-Host":"localhost:8000","User-Agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.06.2412050 MicroMessenger/8.0.5 Language/zh_CN webview/","X-Forwarded-Proto":"http","Sec-Fetch-Site":"cross-site","Sec-Fetch-Dest":"empty","Accept-Encoding":"gzip, deflate, br","userId":"0","X-Forwarded-Port":"8000","token":"","Sec-Fetch-Mode":"cors","host":"**************:8081","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:51695\"","client":"eteamobile","X-Forwarded-For":"0:0:0:0:0:0:0:1","content-type":"application/json;charset=UTF-8"}
[12:59:41:256] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"product":"etea","code":"0e1Czeml2wRzuf4nlqol23YCZp2CzemN","terminal":"etea","callLocationCode":"/pages/index/index?"}
[12:59:41:256] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：java.lang.NullPointerException，异常信息：null，
[13:13:28:653] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/version/getSystemSettings，出现异常:java.lang.NullPointerException；
[13:13:28:666] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"*/*","User-Agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.06.2412050 MicroMessenger/8.0.5 Language/zh_CN webview/","X-Forwarded-Proto":"http","Referer":"https://servicewechat.com/wxa38e954efdceffbe/devtools/page-frame.html","X-Forwarded-Host":"localhost:8000","Sec-Fetch-Dest":"empty","Sec-Fetch-Site":"cross-site","Accept-Encoding":"gzip, deflate, br","X-Forwarded-Port":"8000","Sec-Fetch-Mode":"cors","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:55478\"","host":"**************:8081","X-Forwarded-For":"0:0:0:0:0:0:0:1","content-type":"application/json"}
[13:13:28:668] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{}
[13:13:28:668] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：java.lang.NullPointerException，异常信息：null，
[13:13:28:813] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/examination/getRecommendExaminations，出现异常:java.lang.NullPointerException；
[13:13:28:825] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"*/*","User-Agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.06.2412050 MicroMessenger/8.0.5 Language/zh_CN webview/","X-Forwarded-Proto":"http","Referer":"https://servicewechat.com/wxa38e954efdceffbe/devtools/page-frame.html","X-Forwarded-Host":"localhost:8000","Sec-Fetch-Dest":"empty","Sec-Fetch-Site":"cross-site","Accept-Encoding":"gzip, deflate, br","X-Forwarded-Port":"8000","Sec-Fetch-Mode":"cors","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:55483\"","host":"**************:8081","X-Forwarded-For":"0:0:0:0:0:0:0:1","content-type":"application/json"}
[13:13:28:825] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"companyId":"0","pageIndex":"1","pageSize":"6","refresh":"true","createrId":"5","type":"public"}
[13:13:28:863] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：java.lang.NullPointerException，异常信息：null，
[13:13:29:716] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/weixinAccount/loginWithoutUserInfoAndAutoRegist，出现异常:java.lang.NullPointerException；
[13:13:29:720] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"*/*","Referer":"https://servicewechat.com/wxa38e954efdceffbe/devtools/page-frame.html","X-Forwarded-Host":"localhost:8000","User-Agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.06.2412050 MicroMessenger/8.0.5 Language/zh_CN webview/","X-Forwarded-Proto":"http","Sec-Fetch-Site":"cross-site","Sec-Fetch-Dest":"empty","Accept-Encoding":"gzip, deflate, br","userId":"0","X-Forwarded-Port":"8000","token":"","Sec-Fetch-Mode":"cors","host":"**************:8081","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:55488\"","client":"eteamobile","X-Forwarded-For":"0:0:0:0:0:0:0:1","content-type":"application/json;charset=UTF-8"}
[13:13:29:721] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"product":"etea","code":"0b1LaSkl2pjsvf4sWRnl2wEmtl2LaSkg","terminal":"etea","callLocationCode":"/pages/index/index?"}
[13:13:29:722] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：java.lang.NullPointerException，异常信息：null，
