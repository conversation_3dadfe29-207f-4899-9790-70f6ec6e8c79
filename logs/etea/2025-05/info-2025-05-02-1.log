[08:45:18:555] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:74) - 初始化阶段开始加载微服务配置文件...
[08:45:18:558] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:139) - 开始加载微服务[exam-main]配置，模块路径: exam-main-service
[08:45:18:567] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:569] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:573] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:578] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:579] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:580] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:580] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:582] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:583] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:584] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:587] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:587] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:590] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/exam-main-service/application.{yml]找到1个配置文件
[08:45:18:595] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:596] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:597] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:598] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:exam-main-service/application.{yml]找到1个配置文件
[08:45:18:603] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:603] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:604] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:607] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:234) - 微服务[exam-main-service]找到0个基础配置文件
[08:45:18:612] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.getActiveProfiles(MicroserviceConfigManager.java:397) - 服务[exam-main]未找到默认激活的profile，将使用基础配置
[08:45:18:615] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[classpath*:wxinfo.properties]找到1个配置文件
[08:45:18:615] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:18:615] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/exam-main-service/wxinfo.properties]找到1个配置文件
[08:45:18:616] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:config/exam-main-service/wxinfo.properties
[08:45:18:616] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:exam-main-service/wxinfo.properties]找到1个配置文件
[08:45:18:616] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:exam-main-service/wxinfo.properties
[08:45:18:616] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[exam-main-service]配置文件: wxinfo.properties
[08:45:18:616] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:18:620] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:313) - 已加载微服务[exam-main-service]配置文件: wxinfo.properties
[08:45:18:620] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[exam-main-service]配置文件: wxinfo.properties
[08:45:18:620] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:config/exam-main-service/wxinfo.properties
[08:45:18:622] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: config/exam-main-service/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:18:632] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[exam-main-service]配置文件: wxinfo.properties
[08:45:18:635] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:exam-main-service/wxinfo.properties
[08:45:18:637] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: exam-main-service/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:18:638] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:261) - 微服务[exam-main-service]共加载了7个配置项
[08:45:18:639] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:174) - 微服务[exam-main-service]配置已加载, 配置项数量: 7
[08:45:18:640] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:139) - 开始加载微服务[sse]配置，模块路径: taurus-sse
[08:45:18:643] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:643] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:644] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:645] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:648] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:651] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:653] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:654] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:655] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:656] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:657] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:657] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:660] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/taurus-sse/application.{yml]找到1个配置文件
[08:45:18:663] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:665] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:665] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:668] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:taurus-sse/application.{yml]找到1个配置文件
[08:45:18:669] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:670] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:671] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:675] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:234) - 微服务[taurus-sse]找到0个基础配置文件
[08:45:18:679] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.getActiveProfiles(MicroserviceConfigManager.java:397) - 服务[sse]未找到默认激活的profile，将使用基础配置
[08:45:18:681] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[classpath*:wxinfo.properties]找到1个配置文件
[08:45:18:681] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:18:682] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/taurus-sse/wxinfo.properties]找到1个配置文件
[08:45:18:683] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:config/taurus-sse/wxinfo.properties
[08:45:18:684] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:taurus-sse/wxinfo.properties]找到1个配置文件
[08:45:18:684] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:taurus-sse/wxinfo.properties
[08:45:18:684] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-sse]配置文件: wxinfo.properties
[08:45:18:684] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:18:685] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:313) - 已加载微服务[taurus-sse]配置文件: wxinfo.properties
[08:45:18:685] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-sse]配置文件: wxinfo.properties
[08:45:18:686] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:config/taurus-sse/wxinfo.properties
[08:45:18:686] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: config/taurus-sse/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:18:688] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-sse]配置文件: wxinfo.properties
[08:45:18:688] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:taurus-sse/wxinfo.properties
[08:45:18:689] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: taurus-sse/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:18:691] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:261) - 微服务[taurus-sse]共加载了7个配置项
[08:45:18:692] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:174) - 微服务[taurus-sse]配置已加载, 配置项数量: 7
[08:45:18:692] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:139) - 开始加载微服务[exam-qkk-study]配置，模块路径: exam-qkk-study-service
[08:45:18:695] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:696] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:697] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:698] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:698] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:699] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:700] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:700] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:701] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:709] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:710] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:710] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:711] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/exam-qkk-study-service/application.{yml]找到1个配置文件
[08:45:18:711] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:712] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:712] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:714] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:exam-qkk-study-service/application.{yml]找到1个配置文件
[08:45:18:714] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:716] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:720] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:723] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:234) - 微服务[exam-qkk-study-service]找到0个基础配置文件
[08:45:18:725] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.getActiveProfiles(MicroserviceConfigManager.java:397) - 服务[exam-qkk-study]未找到默认激活的profile，将使用基础配置
[08:45:18:726] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[classpath*:wxinfo.properties]找到1个配置文件
[08:45:18:726] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:18:727] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/exam-qkk-study-service/wxinfo.properties]找到1个配置文件
[08:45:18:727] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:config/exam-qkk-study-service/wxinfo.properties
[08:45:18:728] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:exam-qkk-study-service/wxinfo.properties]找到1个配置文件
[08:45:18:728] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:exam-qkk-study-service/wxinfo.properties
[08:45:18:728] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[exam-qkk-study-service]配置文件: wxinfo.properties
[08:45:18:728] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:18:728] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:313) - 已加载微服务[exam-qkk-study-service]配置文件: wxinfo.properties
[08:45:18:728] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[exam-qkk-study-service]配置文件: wxinfo.properties
[08:45:18:728] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:config/exam-qkk-study-service/wxinfo.properties
[08:45:18:729] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: config/exam-qkk-study-service/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:18:731] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[exam-qkk-study-service]配置文件: wxinfo.properties
[08:45:18:731] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:exam-qkk-study-service/wxinfo.properties
[08:45:18:731] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: exam-qkk-study-service/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:18:735] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:261) - 微服务[exam-qkk-study-service]共加载了7个配置项
[08:45:18:738] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:174) - 微服务[exam-qkk-study-service]配置已加载, 配置项数量: 7
[08:45:18:738] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:139) - 开始加载微服务[pay]配置，模块路径: taurus-pay
[08:45:18:739] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:743] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:744] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:744] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:744] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:745] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:745] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:746] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:747] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:749] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:750] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:750] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:751] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/taurus-pay/application.{yml]找到1个配置文件
[08:45:18:751] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:752] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:752] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:752] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:taurus-pay/application.{yml]找到1个配置文件
[08:45:18:753] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:753] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:753] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:754] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:234) - 微服务[taurus-pay]找到0个基础配置文件
[08:45:18:754] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.getActiveProfiles(MicroserviceConfigManager.java:394) - 服务[pay]使用默认的激活profile: test
[08:45:18:755] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:243) - 微服务[taurus-pay]激活的profiles: [test]
[08:45:18:757] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:759] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:761] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:762] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:762] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:763] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:763] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:763] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:764] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:765] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:767] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:767] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:767] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/taurus-pay/application-test.{yml]找到1个配置文件
[08:45:18:768] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:768] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:769] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:769] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:taurus-pay/application-test.{yml]找到1个配置文件
[08:45:18:769] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:771] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:773] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:774] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:250) - 微服务[taurus-pay]找到0个profile[test]配置文件
[08:45:18:776] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[classpath*:wxinfo.properties]找到1个配置文件
[08:45:18:776] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:18:776] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/taurus-pay/wxinfo.properties]找到1个配置文件
[08:45:18:776] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:config/taurus-pay/wxinfo.properties
[08:45:18:776] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:taurus-pay/wxinfo.properties]找到1个配置文件
[08:45:18:776] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:taurus-pay/wxinfo.properties
[08:45:18:776] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-pay]配置文件: wxinfo.properties
[08:45:18:776] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:18:777] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:313) - 已加载微服务[taurus-pay]配置文件: wxinfo.properties
[08:45:18:777] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-pay]配置文件: wxinfo.properties
[08:45:18:777] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:config/taurus-pay/wxinfo.properties
[08:45:18:777] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: config/taurus-pay/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:18:779] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-pay]配置文件: wxinfo.properties
[08:45:18:780] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:taurus-pay/wxinfo.properties
[08:45:18:780] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: taurus-pay/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:18:781] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:261) - 微服务[taurus-pay]共加载了7个配置项
[08:45:18:781] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:174) - 微服务[taurus-pay]配置已加载, 配置项数量: 7
[08:45:18:781] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:139) - 开始加载微服务[comment]配置，模块路径: taurus-comment
[08:45:18:782] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:783] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:784] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:785] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:785] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:786] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:786] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:787] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:787] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:787] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:788] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:788] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:788] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/taurus-comment/application.{yml]找到1个配置文件
[08:45:18:789] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:789] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:790] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:790] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:taurus-comment/application.{yml]找到1个配置文件
[08:45:18:790] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:792] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:794] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:794] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:234) - 微服务[taurus-comment]找到0个基础配置文件
[08:45:18:795] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.getActiveProfiles(MicroserviceConfigManager.java:394) - 服务[comment]使用默认的激活profile: dev
[08:45:18:795] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:243) - 微服务[taurus-comment]激活的profiles: [dev]
[08:45:18:795] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:796] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:796] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:797] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:797] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:797] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:798] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:798] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:798] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:799] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:800] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:801] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:802] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/taurus-comment/application-dev.{yml]找到1个配置文件
[08:45:18:802] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:802] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:802] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:803] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:taurus-comment/application-dev.{yml]找到1个配置文件
[08:45:18:803] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:804] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:804] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:804] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:250) - 微服务[taurus-comment]找到0个profile[dev]配置文件
[08:45:18:806] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[classpath*:wxinfo.properties]找到1个配置文件
[08:45:18:806] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:18:806] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/taurus-comment/wxinfo.properties]找到1个配置文件
[08:45:18:806] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:config/taurus-comment/wxinfo.properties
[08:45:18:807] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:taurus-comment/wxinfo.properties]找到1个配置文件
[08:45:18:807] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:taurus-comment/wxinfo.properties
[08:45:18:807] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-comment]配置文件: wxinfo.properties
[08:45:18:808] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:18:808] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:313) - 已加载微服务[taurus-comment]配置文件: wxinfo.properties
[08:45:18:808] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-comment]配置文件: wxinfo.properties
[08:45:18:808] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:config/taurus-comment/wxinfo.properties
[08:45:18:809] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: config/taurus-comment/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:18:809] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-comment]配置文件: wxinfo.properties
[08:45:18:810] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:taurus-comment/wxinfo.properties
[08:45:18:810] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: taurus-comment/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:18:810] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:261) - 微服务[taurus-comment]共加载了7个配置项
[08:45:18:812] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:174) - 微服务[taurus-comment]配置已加载, 配置项数量: 7
[08:45:18:813] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:139) - 开始加载微服务[llm]配置，模块路径: taurus-llm
[08:45:18:818] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:821] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:822] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:823] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:823] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:826] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:827] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:827] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:828] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:828] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:828] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:829] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:829] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/taurus-llm/application.{yml]找到1个配置文件
[08:45:18:829] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:830] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:831] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:833] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:taurus-llm/application.{yml]找到1个配置文件
[08:45:18:834] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:835] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:835] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:835] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:234) - 微服务[taurus-llm]找到0个基础配置文件
[08:45:18:836] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.getActiveProfiles(MicroserviceConfigManager.java:397) - 服务[llm]未找到默认激活的profile，将使用基础配置
[08:45:18:837] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[classpath*:wxinfo.properties]找到1个配置文件
[08:45:18:837] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:18:837] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/taurus-llm/wxinfo.properties]找到1个配置文件
[08:45:18:837] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:config/taurus-llm/wxinfo.properties
[08:45:18:837] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:taurus-llm/wxinfo.properties]找到1个配置文件
[08:45:18:837] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:taurus-llm/wxinfo.properties
[08:45:18:837] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-llm]配置文件: wxinfo.properties
[08:45:18:837] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:18:837] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:313) - 已加载微服务[taurus-llm]配置文件: wxinfo.properties
[08:45:18:838] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-llm]配置文件: wxinfo.properties
[08:45:18:838] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:config/taurus-llm/wxinfo.properties
[08:45:18:838] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: config/taurus-llm/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:18:839] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-llm]配置文件: wxinfo.properties
[08:45:18:839] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:taurus-llm/wxinfo.properties
[08:45:18:839] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: taurus-llm/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:18:840] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:261) - 微服务[taurus-llm]共加载了7个配置项
[08:45:18:840] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:174) - 微服务[taurus-llm]配置已加载, 配置项数量: 7
[08:45:18:840] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:139) - 开始加载微服务[form-system]配置，模块路径: taurus-form-system
[08:45:18:842] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:844] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:844] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:844] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:845] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:845] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:845] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:846] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:846] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:846] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:847] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:847] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:847] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/taurus-form-system/application.{yml]找到1个配置文件
[08:45:18:848] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:848] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:848] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:848] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:taurus-form-system/application.{yml]找到1个配置文件
[08:45:18:849] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:849] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:849] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:850] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:234) - 微服务[taurus-form-system]找到0个基础配置文件
[08:45:18:850] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.getActiveProfiles(MicroserviceConfigManager.java:397) - 服务[form-system]未找到默认激活的profile，将使用基础配置
[08:45:18:851] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[classpath*:wxinfo.properties]找到1个配置文件
[08:45:18:851] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:18:851] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/taurus-form-system/wxinfo.properties]找到1个配置文件
[08:45:18:851] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:config/taurus-form-system/wxinfo.properties
[08:45:18:851] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:taurus-form-system/wxinfo.properties]找到1个配置文件
[08:45:18:852] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:taurus-form-system/wxinfo.properties
[08:45:18:852] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-form-system]配置文件: wxinfo.properties
[08:45:18:852] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:18:852] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:313) - 已加载微服务[taurus-form-system]配置文件: wxinfo.properties
[08:45:18:852] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-form-system]配置文件: wxinfo.properties
[08:45:18:852] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:config/taurus-form-system/wxinfo.properties
[08:45:18:852] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: config/taurus-form-system/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:18:854] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-form-system]配置文件: wxinfo.properties
[08:45:18:855] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:taurus-form-system/wxinfo.properties
[08:45:18:856] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: taurus-form-system/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:18:856] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:261) - 微服务[taurus-form-system]共加载了7个配置项
[08:45:18:857] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:174) - 微服务[taurus-form-system]配置已加载, 配置项数量: 7
[08:45:18:857] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:139) - 开始加载微服务[oss]配置，模块路径: taurus-oss
[08:45:18:862] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:863] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:864] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:864] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:865] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:865] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:865] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:866] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:866] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:867] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:867] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:867] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:867] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/taurus-oss/application.{yml]找到1个配置文件
[08:45:18:868] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:869] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:870] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:870] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:taurus-oss/application.{yml]找到1个配置文件
[08:45:18:871] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:18:872] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:18:872] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:18:873] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:234) - 微服务[taurus-oss]找到0个基础配置文件
[08:45:18:873] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.getActiveProfiles(MicroserviceConfigManager.java:397) - 服务[oss]未找到默认激活的profile，将使用基础配置
[08:45:18:875] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[classpath*:wxinfo.properties]找到1个配置文件
[08:45:18:875] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:18:876] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/taurus-oss/wxinfo.properties]找到1个配置文件
[08:45:18:876] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:config/taurus-oss/wxinfo.properties
[08:45:18:876] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:taurus-oss/wxinfo.properties]找到1个配置文件
[08:45:18:876] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:taurus-oss/wxinfo.properties
[08:45:18:876] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-oss]配置文件: wxinfo.properties
[08:45:18:876] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:18:876] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:313) - 已加载微服务[taurus-oss]配置文件: wxinfo.properties
[08:45:18:876] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-oss]配置文件: wxinfo.properties
[08:45:18:876] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:config/taurus-oss/wxinfo.properties
[08:45:18:876] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: config/taurus-oss/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:18:877] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-oss]配置文件: wxinfo.properties
[08:45:18:877] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:taurus-oss/wxinfo.properties
[08:45:18:877] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: taurus-oss/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:18:878] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:261) - 微服务[taurus-oss]共加载了7个配置项
[08:45:18:878] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:174) - 微服务[taurus-oss]配置已加载, 配置项数量: 7
[08:45:18:883] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 2 profiles are active: "monolith", "test-etea"
[08:45:24:557] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[08:45:24:613] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource taurus-oss-config [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[08:45:24:614] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource taurus-oss-namespaced-config [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[08:45:24:614] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource taurus-form-system-config [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[08:45:24:615] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource taurus-form-system-namespaced-config [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[08:45:24:615] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource taurus-llm-config [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[08:45:24:615] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource taurus-llm-namespaced-config [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[08:45:24:615] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource taurus-comment-config [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[08:45:24:615] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource taurus-comment-namespaced-config [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[08:45:24:615] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource taurus-pay-config [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[08:45:24:615] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource taurus-pay-namespaced-config [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[08:45:24:615] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource exam-qkk-study-service-config [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[08:45:24:615] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource exam-qkk-study-service-namespaced-config [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[08:45:24:615] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource taurus-sse-config [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[08:45:24:615] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource taurus-sse-namespaced-config [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[08:45:24:615] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource exam-main-service-config [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[08:45:24:615] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource exam-main-service-namespaced-config [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[08:45:24:616] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[08:45:24:617] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[08:45:24:618] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[08:45:24:618] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[08:45:24:618] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[08:45:24:618] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[08:45:24:618] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[08:45:24:619] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[08:45:24:619] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[08:45:24:619] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-monolith.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[08:45:24:619] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[08:45:24:619] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[08:45:24:619] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[08:45:24:619] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[08:45:24:673] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[08:45:25:040] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[08:45:25:047] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[08:45:25:839] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[08:45:25:888] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[08:45:26:396] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[08:45:26:409] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[08:45:26:410] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[08:45:26:410] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[08:45:26:411] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[08:45:26:411] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[08:45:26:411] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[08:45:26:412] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[08:45:26:413] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[08:45:27:256] [INFO] - com.taurus.monolith.config.MonolithServiceManager.init(MonolithServiceManager.java:51) - 初始化单体化部署服务管理器
[08:45:27:258] [INFO] - com.taurus.monolith.config.MonolithServiceManager.init(MonolithServiceManager.java:52) - 已启用服务: [exam-main, exam-qkk-study, form-system, oss, pay, comment, llm, sse]
[08:45:27:259] [INFO] - com.taurus.monolith.config.MonolithServiceManager.init(MonolithServiceManager.java:53) - 服务映射关系: {exam-main=exam-main-service, sse=taurus-sse, exam-qkk-study=exam-qkk-study-service, pay=taurus-pay, comment=taurus-comment, llm=taurus-llm, form-system=taurus-form-system, oss=taurus-oss}
[08:45:27:259] [INFO] - com.taurus.monolith.config.MonolithServiceManager.init(MonolithServiceManager.java:54) - 多服务注册配置: enabled=true, serviceCount=7
[08:45:27:273] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.postInitialize(MicroserviceConfigManager.java:93) - Bean初始化后开始完善微服务配置...
[08:45:27:335] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.checkMonolithConfigFile(MicroserviceConfigManager.java:698) - 找到monolith配置文件: [file [/Users/<USER>/Documents/git/taurus-cloud/taurus-monolith/target/classes/application-monolith.yml]]
[08:45:27:336] [ERROR] - com.taurus.monolith.config.MicroserviceConfigManager.checkMonolithConfigFile(MicroserviceConfigManager.java:710) - 检查monolith配置文件时出错: null
[08:45:27:337] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.postInitialize(MicroserviceConfigManager.java:106) - 从serviceManager获取到8个微服务映射
[08:45:27:338] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:139) - 开始加载微服务[exam-main]配置，模块路径: exam-main-service
[08:45:27:343] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:345] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:346] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:347] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:347] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:348] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:353] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:355] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:356] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:356] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:357] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:357] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:359] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/exam-main-service/application.{yml]找到1个配置文件
[08:45:27:362] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:363] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:363] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:363] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:exam-main-service/application.{yml]找到1个配置文件
[08:45:27:365] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:366] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:367] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:367] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:234) - 微服务[exam-main-service]找到0个基础配置文件
[08:45:27:368] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[classpath*:wxinfo.properties]找到1个配置文件
[08:45:27:368] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:27:369] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/exam-main-service/wxinfo.properties]找到1个配置文件
[08:45:27:369] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:config/exam-main-service/wxinfo.properties
[08:45:27:369] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:exam-main-service/wxinfo.properties]找到1个配置文件
[08:45:27:369] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:exam-main-service/wxinfo.properties
[08:45:27:370] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[exam-main-service]配置文件: wxinfo.properties
[08:45:27:370] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:27:373] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:313) - 已加载微服务[exam-main-service]配置文件: wxinfo.properties
[08:45:27:373] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[exam-main-service]配置文件: wxinfo.properties
[08:45:27:373] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:config/exam-main-service/wxinfo.properties
[08:45:27:377] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: config/exam-main-service/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.postInitialize(MicroserviceConfigManager.java:115) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:27:385] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[exam-main-service]配置文件: wxinfo.properties
[08:45:27:386] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:exam-main-service/wxinfo.properties
[08:45:27:387] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: exam-main-service/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.postInitialize(MicroserviceConfigManager.java:115) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:27:388] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:261) - 微服务[exam-main-service]共加载了7个配置项
[08:45:27:391] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:174) - 微服务[exam-main-service]配置已加载, 配置项数量: 7
[08:45:27:392] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:139) - 开始加载微服务[sse]配置，模块路径: taurus-sse
[08:45:27:393] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:394] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:395] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:396] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:397] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:397] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:398] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:399] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:399] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:400] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:400] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:401] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:401] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/taurus-sse/application.{yml]找到1个配置文件
[08:45:27:402] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:402] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:403] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:403] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:taurus-sse/application.{yml]找到1个配置文件
[08:45:27:403] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:404] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:405] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:405] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:234) - 微服务[taurus-sse]找到0个基础配置文件
[08:45:27:408] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[classpath*:wxinfo.properties]找到1个配置文件
[08:45:27:408] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:27:409] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/taurus-sse/wxinfo.properties]找到1个配置文件
[08:45:27:409] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:config/taurus-sse/wxinfo.properties
[08:45:27:409] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:taurus-sse/wxinfo.properties]找到1个配置文件
[08:45:27:409] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:taurus-sse/wxinfo.properties
[08:45:27:409] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-sse]配置文件: wxinfo.properties
[08:45:27:411] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:27:412] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:313) - 已加载微服务[taurus-sse]配置文件: wxinfo.properties
[08:45:27:412] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-sse]配置文件: wxinfo.properties
[08:45:27:412] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:config/taurus-sse/wxinfo.properties
[08:45:27:413] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: config/taurus-sse/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.postInitialize(MicroserviceConfigManager.java:115) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:27:414] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-sse]配置文件: wxinfo.properties
[08:45:27:415] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:taurus-sse/wxinfo.properties
[08:45:27:415] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: taurus-sse/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.postInitialize(MicroserviceConfigManager.java:115) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:27:416] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:261) - 微服务[taurus-sse]共加载了7个配置项
[08:45:27:417] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:174) - 微服务[taurus-sse]配置已加载, 配置项数量: 7
[08:45:27:417] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:139) - 开始加载微服务[exam-qkk-study]配置，模块路径: exam-qkk-study-service
[08:45:27:419] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:420] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:420] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:421] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:422] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:422] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:423] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:423] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:423] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:426] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:427] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:428] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:428] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/exam-qkk-study-service/application.{yml]找到1个配置文件
[08:45:27:429] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:429] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:431] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:432] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:exam-qkk-study-service/application.{yml]找到1个配置文件
[08:45:27:432] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:432] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:433] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:433] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:234) - 微服务[exam-qkk-study-service]找到0个基础配置文件
[08:45:27:435] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[classpath*:wxinfo.properties]找到1个配置文件
[08:45:27:435] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:27:436] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/exam-qkk-study-service/wxinfo.properties]找到1个配置文件
[08:45:27:436] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:config/exam-qkk-study-service/wxinfo.properties
[08:45:27:436] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:exam-qkk-study-service/wxinfo.properties]找到1个配置文件
[08:45:27:436] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:exam-qkk-study-service/wxinfo.properties
[08:45:27:436] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[exam-qkk-study-service]配置文件: wxinfo.properties
[08:45:27:436] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:27:437] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:313) - 已加载微服务[exam-qkk-study-service]配置文件: wxinfo.properties
[08:45:27:437] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[exam-qkk-study-service]配置文件: wxinfo.properties
[08:45:27:437] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:config/exam-qkk-study-service/wxinfo.properties
[08:45:27:437] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: config/exam-qkk-study-service/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.postInitialize(MicroserviceConfigManager.java:115) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:27:438] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[exam-qkk-study-service]配置文件: wxinfo.properties
[08:45:27:439] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:exam-qkk-study-service/wxinfo.properties
[08:45:27:439] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: exam-qkk-study-service/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.postInitialize(MicroserviceConfigManager.java:115) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:27:444] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:261) - 微服务[exam-qkk-study-service]共加载了7个配置项
[08:45:27:445] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:174) - 微服务[exam-qkk-study-service]配置已加载, 配置项数量: 7
[08:45:27:446] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:139) - 开始加载微服务[pay]配置，模块路径: taurus-pay
[08:45:27:447] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:447] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:448] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:449] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:449] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:449] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:450] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:450] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:451] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:451] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:452] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:452] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:452] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/taurus-pay/application.{yml]找到1个配置文件
[08:45:27:453] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:455] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:456] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:459] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:taurus-pay/application.{yml]找到1个配置文件
[08:45:27:460] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:460] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:461] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:461] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:234) - 微服务[taurus-pay]找到0个基础配置文件
[08:45:27:462] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[classpath*:wxinfo.properties]找到1个配置文件
[08:45:27:463] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:27:463] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/taurus-pay/wxinfo.properties]找到1个配置文件
[08:45:27:463] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:config/taurus-pay/wxinfo.properties
[08:45:27:463] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:taurus-pay/wxinfo.properties]找到1个配置文件
[08:45:27:464] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:taurus-pay/wxinfo.properties
[08:45:27:464] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-pay]配置文件: wxinfo.properties
[08:45:27:464] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:27:464] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:313) - 已加载微服务[taurus-pay]配置文件: wxinfo.properties
[08:45:27:464] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-pay]配置文件: wxinfo.properties
[08:45:27:464] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:config/taurus-pay/wxinfo.properties
[08:45:27:465] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: config/taurus-pay/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.postInitialize(MicroserviceConfigManager.java:115) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:27:467] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-pay]配置文件: wxinfo.properties
[08:45:27:467] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:taurus-pay/wxinfo.properties
[08:45:27:468] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: taurus-pay/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.postInitialize(MicroserviceConfigManager.java:115) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:27:470] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:261) - 微服务[taurus-pay]共加载了7个配置项
[08:45:27:471] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:174) - 微服务[taurus-pay]配置已加载, 配置项数量: 7
[08:45:27:471] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:139) - 开始加载微服务[comment]配置，模块路径: taurus-comment
[08:45:27:472] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:473] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:473] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:475] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:477] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:478] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:479] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:479] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:479] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:480] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:481] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:482] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:482] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/taurus-comment/application.{yml]找到1个配置文件
[08:45:27:482] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:483] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:483] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:484] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:taurus-comment/application.{yml]找到1个配置文件
[08:45:27:486] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:486] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:487] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:487] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:234) - 微服务[taurus-comment]找到0个基础配置文件
[08:45:27:488] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[classpath*:wxinfo.properties]找到1个配置文件
[08:45:27:488] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:27:489] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/taurus-comment/wxinfo.properties]找到1个配置文件
[08:45:27:489] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:config/taurus-comment/wxinfo.properties
[08:45:27:489] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:taurus-comment/wxinfo.properties]找到1个配置文件
[08:45:27:490] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:taurus-comment/wxinfo.properties
[08:45:27:490] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-comment]配置文件: wxinfo.properties
[08:45:27:490] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:27:491] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:313) - 已加载微服务[taurus-comment]配置文件: wxinfo.properties
[08:45:27:491] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-comment]配置文件: wxinfo.properties
[08:45:27:491] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:config/taurus-comment/wxinfo.properties
[08:45:27:492] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: config/taurus-comment/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.postInitialize(MicroserviceConfigManager.java:115) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:27:495] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-comment]配置文件: wxinfo.properties
[08:45:27:495] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:taurus-comment/wxinfo.properties
[08:45:27:496] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: taurus-comment/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.postInitialize(MicroserviceConfigManager.java:115) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:27:498] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:261) - 微服务[taurus-comment]共加载了7个配置项
[08:45:27:500] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:174) - 微服务[taurus-comment]配置已加载, 配置项数量: 7
[08:45:27:501] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:139) - 开始加载微服务[llm]配置，模块路径: taurus-llm
[08:45:27:507] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:510] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:510] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:511] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:511] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:512] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:513] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:513] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:514] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:514] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:514] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:515] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:515] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/taurus-llm/application.{yml]找到1个配置文件
[08:45:27:516] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:516] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:517] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:517] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:taurus-llm/application.{yml]找到1个配置文件
[08:45:27:517] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:518] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:519] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:519] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:234) - 微服务[taurus-llm]找到0个基础配置文件
[08:45:27:520] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[classpath*:wxinfo.properties]找到1个配置文件
[08:45:27:520] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:27:521] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/taurus-llm/wxinfo.properties]找到1个配置文件
[08:45:27:521] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:config/taurus-llm/wxinfo.properties
[08:45:27:521] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:taurus-llm/wxinfo.properties]找到1个配置文件
[08:45:27:521] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:taurus-llm/wxinfo.properties
[08:45:27:522] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-llm]配置文件: wxinfo.properties
[08:45:27:522] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:27:522] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:313) - 已加载微服务[taurus-llm]配置文件: wxinfo.properties
[08:45:27:522] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-llm]配置文件: wxinfo.properties
[08:45:27:522] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:config/taurus-llm/wxinfo.properties
[08:45:27:523] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: config/taurus-llm/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.postInitialize(MicroserviceConfigManager.java:115) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:27:524] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-llm]配置文件: wxinfo.properties
[08:45:27:525] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:taurus-llm/wxinfo.properties
[08:45:27:526] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: taurus-llm/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.postInitialize(MicroserviceConfigManager.java:115) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:27:527] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:261) - 微服务[taurus-llm]共加载了7个配置项
[08:45:27:530] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:174) - 微服务[taurus-llm]配置已加载, 配置项数量: 7
[08:45:27:530] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:139) - 开始加载微服务[form-system]配置，模块路径: taurus-form-system
[08:45:27:531] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:531] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:532] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:532] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:533] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:533] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:537] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:539] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:539] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:540] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:541] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:542] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:544] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/taurus-form-system/application.{yml]找到1个配置文件
[08:45:27:545] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:545] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:546] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:547] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:taurus-form-system/application.{yml]找到1个配置文件
[08:45:27:547] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:548] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:549] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:550] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:234) - 微服务[taurus-form-system]找到0个基础配置文件
[08:45:27:551] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[classpath*:wxinfo.properties]找到1个配置文件
[08:45:27:551] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:27:551] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/taurus-form-system/wxinfo.properties]找到1个配置文件
[08:45:27:551] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:config/taurus-form-system/wxinfo.properties
[08:45:27:551] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:taurus-form-system/wxinfo.properties]找到1个配置文件
[08:45:27:551] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:taurus-form-system/wxinfo.properties
[08:45:27:551] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-form-system]配置文件: wxinfo.properties
[08:45:27:551] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:27:553] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:313) - 已加载微服务[taurus-form-system]配置文件: wxinfo.properties
[08:45:27:553] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-form-system]配置文件: wxinfo.properties
[08:45:27:553] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:config/taurus-form-system/wxinfo.properties
[08:45:27:553] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: config/taurus-form-system/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.postInitialize(MicroserviceConfigManager.java:115) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:27:555] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-form-system]配置文件: wxinfo.properties
[08:45:27:555] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:taurus-form-system/wxinfo.properties
[08:45:27:555] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: taurus-form-system/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.postInitialize(MicroserviceConfigManager.java:115) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:27:556] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:261) - 微服务[taurus-form-system]共加载了7个配置项
[08:45:27:557] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:174) - 微服务[taurus-form-system]配置已加载, 配置项数量: 7
[08:45:27:557] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:139) - 开始加载微服务[oss]配置，模块路径: taurus-oss
[08:45:27:558] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:558] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:560] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:562] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:563] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:564] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:565] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:566] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:568] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:568] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:569] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:569] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:570] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/taurus-oss/application.{yml]找到1个配置文件
[08:45:27:570] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:573] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:575] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:575] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:taurus-oss/application.{yml]找到1个配置文件
[08:45:27:576] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[yaml]找到1个配置文件
[08:45:27:576] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[properties]找到1个配置文件
[08:45:27:577] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[xml}]找到1个配置文件
[08:45:27:578] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:234) - 微服务[taurus-oss]找到0个基础配置文件
[08:45:27:579] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[classpath*:wxinfo.properties]找到1个配置文件
[08:45:27:580] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:27:580] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:config/taurus-oss/wxinfo.properties]找到1个配置文件
[08:45:27:580] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:config/taurus-oss/wxinfo.properties
[08:45:27:580] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:566) - 在路径[file:taurus-oss/wxinfo.properties]找到1个配置文件
[08:45:27:580] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.processPattern(MicroserviceConfigManager.java:571) - 找到配置文件: file:taurus-oss/wxinfo.properties
[08:45:27:580] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-oss]配置文件: wxinfo.properties
[08:45:27:580] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:/Users/<USER>/Documents/git/taurus-cloud/taurus-pay/target/classes/wxinfo.properties
[08:45:27:581] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:313) - 已加载微服务[taurus-oss]配置文件: wxinfo.properties
[08:45:27:581] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-oss]配置文件: wxinfo.properties
[08:45:27:581] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:config/taurus-oss/wxinfo.properties
[08:45:27:582] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: config/taurus-oss/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.postInitialize(MicroserviceConfigManager.java:115) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:27:583] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:296) - 处理微服务[taurus-oss]配置文件: wxinfo.properties
[08:45:27:584] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:300) - 配置文件URI: file:taurus-oss/wxinfo.properties
[08:45:27:586] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: taurus-oss/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) ~[classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.postInitialize(MicroserviceConfigManager.java:115) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:65) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[08:45:27:587] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:261) - 微服务[taurus-oss]共加载了7个配置项
[08:45:27:588] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:174) - 微服务[taurus-oss]配置已加载, 配置项数量: 7
[08:45:27:588] [INFO] - com.taurus.monolith.config.MicroserviceConfigManager.postInitialize(MicroserviceConfigManager.java:117) - 微服务配置加载完成，共管理8个微服务的配置
[08:45:27:624] [INFO] - com.taurus.monolith.config.ApplicationShutdownHook.registerShutdownHook(ApplicationShutdownHook.java:26) - 注册应用关闭钩子，确保应用关闭时注销所有微服务...
[08:45:29:206] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1002) - {dataSource-1} inited
[08:45:29:227] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[08:45:30:267] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[08:45:30:955] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[08:45:31:431] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[08:45:34:907] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1885) - {dataSource-1} closing ...
[08:45:34:931] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1955) - {dataSource-1} closed
[08:45:34:933] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1885) - {dataSource-0} closing ...
[08:45:35:114] [INFO] - com.taurus.monolith.config.ApplicationShutdownHook.lambda$0(ApplicationShutdownHook.java:28) - 应用正在关闭，开始注销所有注册的微服务...
[08:45:35:120] [INFO] - com.taurus.monolith.config.ApplicationShutdownHook.lambda$0(ApplicationShutdownHook.java:30) - 所有微服务注销完成
[08:56:55:677] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[08:56:59:085] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[08:56:59:154] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[08:56:59:156] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[08:56:59:157] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[08:56:59:157] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[08:56:59:157] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[08:56:59:158] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[08:56:59:158] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[08:56:59:158] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[08:56:59:158] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[08:56:59:158] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[08:56:59:158] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[08:56:59:158] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[08:56:59:210] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[08:56:59:778] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[08:56:59:782] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[08:57:00:456] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[08:57:00:489] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[08:57:00:856] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[08:57:00:868] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[08:57:00:869] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[08:57:00:869] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[08:57:00:869] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[08:57:00:870] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[08:57:00:870] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[08:57:00:873] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[08:57:00:874] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[08:57:03:896] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[08:57:03:943] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[08:57:04:190] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[08:57:05:868] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：1.673秒
[08:57:12:147] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[08:57:12:252] [WARN] - io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:70) - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
[08:57:12:762] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[08:57:13:275] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[08:57:19:334] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[08:57:20:066] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 50 ms to scan 1 urls, producing 3 keys and 6 values 
[08:57:20:117] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 24 ms to scan 1 urls, producing 4 keys and 9 values 
[08:57:20:138] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 17 ms to scan 1 urls, producing 3 keys and 10 values 
[08:57:20:178] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 35 ms to scan 12 urls, producing 0 keys and 0 values 
[08:57:20:191] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
[08:57:20:203] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
[08:57:20:218] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
[08:57:20:247] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 26 ms to scan 12 urls, producing 0 keys and 0 values 
[08:57:21:454] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[08:57:21:455] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[08:57:21:469] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[08:57:21:489] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[08:57:21:508] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[08:57:21:551] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[08:57:21:658] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service **************:8081 register finished
[08:57:21:913] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 28.467 seconds (JVM running for 29.976)
[08:57:21:982] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:55) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[09:23:56:757] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[09:23:56:779] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[09:23:57:060] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[09:23:57:127] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[09:23:57:550] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-1} closing ...
[09:23:57:588] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-1} closed
[2025-05-02 09:23:58:978] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "prod-172"
[2025-05-02 09:24:00:172] [INFO] - org.springframework.data.repository.config.RepositoryConfigurationDelegate.multipleStoresDetected(RepositoryConfigurationDelegate.java:262) - Multiple Spring Data modules found, entering strict repository configuration mode
[2025-05-02 09:24:00:178] [INFO] - org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn(RepositoryConfigurationDelegate.java:132) - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2025-05-02 09:24:00:218] [INFO] - org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn(RepositoryConfigurationDelegate.java:201) - Finished Spring Data repository scanning in 38 ms. Found 0 Redis repository interfaces.
[2025-05-02 09:24:00:365] [INFO] - org.springframework.cloud.context.scope.GenericScope.setSerializationId(GenericScope.java:283) - BeanFactory id=815f1045-d1c3-3947-9ea7-13ced486604a
[2025-05-02 09:24:00:438] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[2025-05-02 09:24:00:468] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[2025-05-02 09:24:00:469] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[2025-05-02 09:24:00:469] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[2025-05-02 09:24:00:469] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[2025-05-02 09:24:00:469] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[2025-05-02 09:24:00:469] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[2025-05-02 09:24:00:470] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[2025-05-02 09:24:00:470] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-05-02 09:24:00:470] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-prod-172.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-05-02 09:24:00:470] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-05-02 09:24:00:470] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-05-02 09:24:00:470] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-05-02 09:24:00:514] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[2025-05-02 09:24:00:620] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[2025-05-02 09:24:00:622] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[2025-05-02 09:24:00:749] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[2025-05-02 09:24:00:749] [INFO] - org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.prepareWebApplicationContext(ServletWebServerApplicationContext.java:292) - Root WebApplicationContext: initialization completed in 1752 ms
[12:58:36:625] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[12:58:40:820] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[12:58:40:910] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[12:58:40:911] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[12:58:40:911] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[12:58:40:911] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[12:58:40:912] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[12:58:40:912] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[12:58:40:912] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[12:58:40:912] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[12:58:40:912] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[12:58:40:912] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[12:58:40:912] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[12:58:40:913] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[12:58:41:001] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[12:58:41:771] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[12:58:41:775] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[12:58:42:781] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[12:58:42:856] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[12:58:43:296] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[12:58:43:307] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[12:58:43:307] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[12:58:43:308] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[12:58:43:309] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[12:58:43:310] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[12:58:43:310] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[12:58:43:312] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[12:58:43:312] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[12:58:46:841] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[12:58:46:879] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[12:58:47:251] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[12:58:48:867] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：1.605秒
[12:58:56:396] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[12:58:56:503] [WARN] - io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:70) - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
[12:58:57:025] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[12:58:57:691] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[12:59:04:569] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[12:59:05:334] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 58 ms to scan 1 urls, producing 3 keys and 6 values 
[12:59:05:417] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 41 ms to scan 1 urls, producing 4 keys and 9 values 
[12:59:05:444] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 22 ms to scan 1 urls, producing 3 keys and 10 values 
[12:59:05:489] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 41 ms to scan 12 urls, producing 0 keys and 0 values 
[12:59:05:509] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 15 ms to scan 1 urls, producing 1 keys and 5 values 
[12:59:05:529] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
[12:59:05:557] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 25 ms to scan 1 urls, producing 2 keys and 8 values 
[12:59:05:595] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 34 ms to scan 12 urls, producing 0 keys and 0 values 
[12:59:07:002] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[12:59:07:003] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[12:59:07:023] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[12:59:07:049] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[12:59:07:071] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[12:59:07:143] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[12:59:07:258] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service **************:8081 register finished
[12:59:07:610] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 33.665 seconds (JVM running for 35.458)
[12:59:07:641] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:55) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[12:59:40:554] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[12:59:40:875] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/version/getSystemSettings，出现异常:java.lang.NullPointerException；
[12:59:41:003] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"*/*","User-Agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.06.2412050 MicroMessenger/8.0.5 Language/zh_CN webview/","X-Forwarded-Proto":"http","Referer":"https://servicewechat.com/wxa38e954efdceffbe/devtools/page-frame.html","X-Forwarded-Host":"localhost:8000","Sec-Fetch-Dest":"empty","Sec-Fetch-Site":"cross-site","Accept-Encoding":"gzip, deflate, br","X-Forwarded-Port":"8000","Sec-Fetch-Mode":"cors","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:51685\"","host":"**************:8081","X-Forwarded-For":"0:0:0:0:0:0:0:1","content-type":"application/json"}
[12:59:41:007] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{}
[12:59:41:030] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：java.lang.NullPointerException，异常信息：null，
[12:59:41:173] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/examination/getRecommendExaminations，出现异常:java.lang.NullPointerException；
[12:59:41:175] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"*/*","User-Agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.06.2412050 MicroMessenger/8.0.5 Language/zh_CN webview/","X-Forwarded-Proto":"http","Referer":"https://servicewechat.com/wxa38e954efdceffbe/devtools/page-frame.html","X-Forwarded-Host":"localhost:8000","Sec-Fetch-Dest":"empty","Sec-Fetch-Site":"cross-site","Accept-Encoding":"gzip, deflate, br","X-Forwarded-Port":"8000","Sec-Fetch-Mode":"cors","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:51690\"","host":"**************:8081","X-Forwarded-For":"0:0:0:0:0:0:0:1","content-type":"application/json"}
[12:59:41:176] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"companyId":"0","pageIndex":"1","pageSize":"6","refresh":"true","createrId":"5","type":"public"}
[12:59:41:176] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：java.lang.NullPointerException，异常信息：null，
[12:59:41:253] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/weixinAccount/loginWithoutUserInfoAndAutoRegist，出现异常:java.lang.NullPointerException；
[12:59:41:254] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"*/*","Referer":"https://servicewechat.com/wxa38e954efdceffbe/devtools/page-frame.html","X-Forwarded-Host":"localhost:8000","User-Agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.06.2412050 MicroMessenger/8.0.5 Language/zh_CN webview/","X-Forwarded-Proto":"http","Sec-Fetch-Site":"cross-site","Sec-Fetch-Dest":"empty","Accept-Encoding":"gzip, deflate, br","userId":"0","X-Forwarded-Port":"8000","token":"","Sec-Fetch-Mode":"cors","host":"**************:8081","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:51695\"","client":"eteamobile","X-Forwarded-For":"0:0:0:0:0:0:0:1","content-type":"application/json;charset=UTF-8"}
[12:59:41:256] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"product":"etea","code":"0e1Czeml2wRzuf4nlqol23YCZp2CzemN","terminal":"etea","callLocationCode":"/pages/index/index?"}
[12:59:41:256] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：java.lang.NullPointerException，异常信息：null，
[13:08:35:224] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[13:08:35:258] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[13:08:35:259] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Destroying Spring FrameworkServlet 'dispatcherServlet'
[13:08:35:587] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[13:08:35:646] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[13:08:36:049] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-1} closing ...
[13:08:36:087] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-1} closed
[13:08:37:733] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[13:08:39:568] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[13:08:39:606] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[13:08:39:607] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[13:08:39:607] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[13:08:39:607] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[13:08:39:607] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[13:08:39:607] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[13:08:39:607] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[13:08:39:607] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[13:08:39:607] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[13:08:39:607] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[13:08:39:607] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[13:08:39:607] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[13:08:39:679] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[13:08:39:914] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[13:08:39:915] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[13:08:40:073] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[13:08:40:077] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[13:08:40:253] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[13:08:40:256] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[13:08:40:257] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[13:08:40:257] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[13:08:40:257] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[13:08:40:258] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[13:08:40:258] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[13:08:40:259] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[13:08:40:260] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[13:08:41:696] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-2} inited
[13:08:41:697] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[13:08:41:813] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[13:08:42:380] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.566秒
[13:08:47:779] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[13:08:47:920] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[13:08:48:449] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[13:08:51:506] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[13:08:52:230] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[13:08:52:231] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[13:08:52:234] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[13:08:52:318] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service **************:8081 register finished
[13:08:52:453] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 15.835 seconds (JVM running for 620.281)
[13:08:52:464] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:55) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[13:08:52:538] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[13:08:52:546] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[13:08:52:742] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[13:08:52:792] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[13:08:53:218] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-2} closing ...
[13:08:53:232] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-2} closed
[13:12:00:295] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[13:12:01:390] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[13:12:01:430] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[13:12:01:430] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[13:12:01:430] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[13:12:01:430] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[13:12:01:430] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[13:12:01:431] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[13:12:01:431] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[13:12:01:431] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[13:12:01:431] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[13:12:01:431] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[13:12:01:431] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[13:12:01:431] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[13:12:01:461] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[13:12:01:592] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[13:12:01:594] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[13:12:01:736] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[13:12:01:742] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[13:12:01:901] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[13:12:01:904] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[13:12:01:904] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[13:12:01:905] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[13:12:01:905] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[13:12:01:906] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[13:12:01:906] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[13:12:01:906] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[13:12:01:907] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[13:12:03:267] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-3} inited
[13:12:03:268] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[13:12:03:409] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[13:12:04:130] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.72秒
[13:12:08:925] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[13:12:09:117] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[13:12:09:620] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[13:12:12:075] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[13:12:12:547] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[13:12:12:547] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[13:12:12:549] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[13:12:12:592] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service **************:8081 register finished
[13:12:12:699] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 13.217 seconds (JVM running for 820.527)
[13:12:12:708] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:55) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[13:12:12:764] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[13:12:12:768] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[13:12:12:881] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[13:12:12:901] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[13:12:13:524] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-3} closing ...
[13:12:13:532] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-3} closed
[13:12:21:861] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[13:12:23:009] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[13:12:23:031] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[13:12:23:031] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[13:12:23:032] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[13:12:23:032] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[13:12:23:032] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[13:12:23:032] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[13:12:23:032] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[13:12:23:032] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[13:12:23:032] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[13:12:23:032] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[13:12:23:032] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[13:12:23:032] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[13:12:23:052] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[13:12:23:172] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[13:12:23:172] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[13:12:23:379] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[13:12:23:386] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[13:12:23:498] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[13:12:23:501] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[13:12:23:502] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[13:12:23:502] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[13:12:23:503] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[13:12:23:503] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[13:12:23:504] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[13:12:23:504] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[13:12:23:504] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[13:12:24:763] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-4} inited
[13:12:24:764] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[13:12:24:849] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[13:12:25:221] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.37秒
[13:12:29:355] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[13:12:29:520] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[13:12:29:943] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[13:12:31:743] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[13:12:32:253] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[13:12:32:254] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[13:12:32:256] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[13:12:32:306] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service **************:8081 register finished
[13:12:32:409] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 11.047 seconds (JVM running for 840.238)
[13:12:32:420] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:55) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[13:13:28:483] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[13:13:28:653] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/version/getSystemSettings，出现异常:java.lang.NullPointerException；
[13:13:28:666] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"*/*","User-Agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.06.2412050 MicroMessenger/8.0.5 Language/zh_CN webview/","X-Forwarded-Proto":"http","Referer":"https://servicewechat.com/wxa38e954efdceffbe/devtools/page-frame.html","X-Forwarded-Host":"localhost:8000","Sec-Fetch-Dest":"empty","Sec-Fetch-Site":"cross-site","Accept-Encoding":"gzip, deflate, br","X-Forwarded-Port":"8000","Sec-Fetch-Mode":"cors","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:55478\"","host":"**************:8081","X-Forwarded-For":"0:0:0:0:0:0:0:1","content-type":"application/json"}
[13:13:28:668] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{}
[13:13:28:668] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：java.lang.NullPointerException，异常信息：null，
[13:13:28:813] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/examination/getRecommendExaminations，出现异常:java.lang.NullPointerException；
[13:13:28:825] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"*/*","User-Agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.06.2412050 MicroMessenger/8.0.5 Language/zh_CN webview/","X-Forwarded-Proto":"http","Referer":"https://servicewechat.com/wxa38e954efdceffbe/devtools/page-frame.html","X-Forwarded-Host":"localhost:8000","Sec-Fetch-Dest":"empty","Sec-Fetch-Site":"cross-site","Accept-Encoding":"gzip, deflate, br","X-Forwarded-Port":"8000","Sec-Fetch-Mode":"cors","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:55483\"","host":"**************:8081","X-Forwarded-For":"0:0:0:0:0:0:0:1","content-type":"application/json"}
[13:13:28:825] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"companyId":"0","pageIndex":"1","pageSize":"6","refresh":"true","createrId":"5","type":"public"}
[13:13:28:863] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：java.lang.NullPointerException，异常信息：null，
[13:13:29:716] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/weixinAccount/loginWithoutUserInfoAndAutoRegist，出现异常:java.lang.NullPointerException；
[13:13:29:720] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"*/*","Referer":"https://servicewechat.com/wxa38e954efdceffbe/devtools/page-frame.html","X-Forwarded-Host":"localhost:8000","User-Agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.06.2412050 MicroMessenger/8.0.5 Language/zh_CN webview/","X-Forwarded-Proto":"http","Sec-Fetch-Site":"cross-site","Sec-Fetch-Dest":"empty","Accept-Encoding":"gzip, deflate, br","userId":"0","X-Forwarded-Port":"8000","token":"","Sec-Fetch-Mode":"cors","host":"**************:8081","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:55488\"","client":"eteamobile","X-Forwarded-For":"0:0:0:0:0:0:0:1","content-type":"application/json;charset=UTF-8"}
[13:13:29:721] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"product":"etea","code":"0b1LaSkl2pjsvf4sWRnl2wEmtl2LaSkg","terminal":"etea","callLocationCode":"/pages/index/index?"}
[13:13:29:722] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：java.lang.NullPointerException，异常信息：null，
[13:14:00:681] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[13:14:00:719] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.destroy(ReplaceStreamFilter.java:58) - ReplaceStreamFilter销毁...
[13:14:00:720] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Destroying Spring FrameworkServlet 'dispatcherServlet'
[13:14:00:907] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[13:14:00:956] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[13:14:01:339] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-4} closing ...
[13:14:01:372] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-4} closed
[13:14:03:077] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[13:14:04:190] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[13:14:04:202] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[13:14:04:202] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[13:14:04:202] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[13:14:04:202] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[13:14:04:203] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[13:14:04:203] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[13:14:04:203] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[13:14:04:203] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[13:14:04:203] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[13:14:04:203] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[13:14:04:203] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[13:14:04:203] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[13:14:04:227] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[13:14:04:310] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[13:14:04:311] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[13:14:04:395] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[13:14:04:404] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[13:14:04:466] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[13:14:04:468] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[13:14:04:468] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[13:14:04:468] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[13:14:04:469] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[13:14:04:469] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[13:14:04:469] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[13:14:04:471] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[13:14:04:472] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[13:14:05:864] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-5} inited
[13:14:05:870] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[13:14:05:965] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[13:14:06:443] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.476秒
[13:14:11:181] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[13:14:11:310] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[13:14:11:723] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[13:14:13:641] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[13:14:14:098] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[13:14:14:099] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[13:14:14:100] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[13:14:14:134] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service **************:8081 register finished
[13:14:14:251] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 12.072 seconds (JVM running for 942.08)
[13:14:14:263] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[13:14:27:994] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[20:29:10:268] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[20:29:17:881] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[20:29:18:003] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[20:29:18:004] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[20:29:18:005] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[20:29:18:005] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[20:29:18:005] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[20:29:18:005] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[20:29:18:005] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[20:29:18:005] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[20:29:18:005] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[20:29:18:006] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[20:29:18:006] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[20:29:18:006] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[20:29:18:052] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[20:29:18:695] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[20:29:18:701] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[20:29:19:696] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[20:29:19:723] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[20:29:20:041] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[20:29:20:050] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[20:29:20:050] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[20:29:20:051] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[20:29:20:051] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[20:29:20:052] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[20:29:20:052] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[20:29:20:053] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[20:29:20:054] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[20:29:23:977] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[20:29:23:988] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[20:29:24:305] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[20:29:25:975] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：1.662秒
[20:29:32:707] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[20:29:32:787] [WARN] - io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:70) - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
[20:29:33:265] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[20:29:33:769] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[20:29:40:669] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[20:29:41:510] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 62 ms to scan 1 urls, producing 3 keys and 6 values 
[20:29:41:581] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 30 ms to scan 1 urls, producing 4 keys and 9 values 
[20:29:41:617] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 20 ms to scan 1 urls, producing 3 keys and 10 values 
[20:29:41:660] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 35 ms to scan 12 urls, producing 0 keys and 0 values 
[20:29:41:677] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 12 ms to scan 1 urls, producing 1 keys and 5 values 
[20:29:41:690] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
[20:29:41:707] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
[20:29:41:739] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 29 ms to scan 12 urls, producing 0 keys and 0 values 
[20:29:42:993] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[20:29:42:994] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[20:29:43:014] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[20:29:43:036] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[20:29:43:056] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[20:29:43:139] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[20:29:43:256] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service **************:8081 register finished
[20:29:43:522] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 38.243 seconds (JVM running for 40.819)
[20:29:43:559] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[20:52:47:874] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[22:39:43:268] [WARN] - redis.clients.jedis.JedisFactory.destroyObject(JedisFactory.java:168) - Error while close
redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketException: Broken pipe (Write failed)
	at redis.clients.jedis.Connection.disconnect(Connection.java:256) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.BinaryClient.disconnect(BinaryClient.java:162) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.Connection.close(Connection.java:246) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.BinaryClient.close(BinaryClient.java:168) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.BinaryJedis.close(BinaryJedis.java:340) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.Jedis.close(Jedis.java:4084) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.JedisFactory.destroyObject(JedisFactory.java:166) ~[jedis-3.8.0.jar:?]
	at org.apache.commons.pool2.PooledObjectFactory.destroyObject(PooledObjectFactory.java:127) ~[commons-pool2-2.11.1.jar:2.11.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.destroy(GenericObjectPool.java:611) ~[commons-pool2-2.11.1.jar:2.11.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.invalidateObject(GenericObjectPool.java:941) ~[commons-pool2-2.11.1.jar:2.11.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.invalidateObject(GenericObjectPool.java:914) ~[commons-pool2-2.11.1.jar:2.11.1]
	at redis.clients.jedis.util.Pool.returnBrokenResourceObject(Pool.java:124) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.util.Pool.returnBrokenResource(Pool.java:103) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.Jedis.close(Jedis.java:4079) ~[jedis-3.8.0.jar:?]
	at org.springframework.data.redis.connection.jedis.JedisConnection.close(JedisConnection.java:352) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at org.springframework.data.redis.listener.RedisMessageListenerContainer$Subscriber.closeConnection(RedisMessageListenerContainer.java:1320) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at org.springframework.data.redis.listener.RedisMessageListenerContainer.handleSubscriptionException(RedisMessageListenerContainer.java:890) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at org.springframework.data.redis.listener.RedisMessageListenerContainer$BlockingSubscriber.lambda$eventuallyPerformSubscription$2(RedisMessageListenerContainer.java:1434) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_281]
Caused by: java.net.SocketException: Broken pipe (Write failed)
	at java.net.SocketOutputStream.socketWrite0(Native Method) ~[?:1.8.0_281]
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:111) ~[?:1.8.0_281]
	at java.net.SocketOutputStream.write(SocketOutputStream.java:155) ~[?:1.8.0_281]
	at redis.clients.jedis.util.RedisOutputStream.flushBuffer(RedisOutputStream.java:52) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.util.RedisOutputStream.flush(RedisOutputStream.java:133) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.Connection.disconnect(Connection.java:252) ~[jedis-3.8.0.jar:?]
	... 18 more
