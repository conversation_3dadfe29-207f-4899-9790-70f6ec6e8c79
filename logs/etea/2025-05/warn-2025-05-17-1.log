[08:19:36:685] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[08:19:56:469] [WARN] - io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:70) - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
[08:27:44:996] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/wrongQuestionBookQuestion/getQuestionWrapperListOfBook，出现异常:java.lang.NullPointerException；
[08:27:45:020] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","X-Forwarded-Host":"localhost:8000","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","X-Forwarded-Proto":"http","Sec-Fetch-Site":"none","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","X-Forwarded-Port":"8000","Sec-Fetch-Mode":"navigate","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","Upgrade-Insecure-Requests":"1","sec-ch-ua-platform":"\"macOS\"","host":"*************:8081","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:58058\"","X-Forwarded-For":"0:0:0:0:0:0:0:1","Sec-Fetch-User":"?1","Accept-Language":"zh-CN,zh;q=0.9"}
[08:27:45:023] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"id":"2091121"}
[08:27:45:024] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：java.lang.NullPointerException，异常信息：null，
[08:29:34:145] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/wrongQuestionBookQuestion/getQuestionWrapperListOfBook，出现异常:java.lang.NullPointerException；
[08:29:34:147] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","X-Forwarded-Host":"localhost:8000","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","X-Forwarded-Proto":"http","Sec-Fetch-Site":"none","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","X-Forwarded-Port":"8000","Sec-Fetch-Mode":"navigate","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","Upgrade-Insecure-Requests":"1","sec-ch-ua-platform":"\"macOS\"","host":"*************:8081","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:58058\"","X-Forwarded-For":"0:0:0:0:0:0:0:1","Sec-Fetch-User":"?1","Accept-Language":"zh-CN,zh;q=0.9"}
[08:29:34:147] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"id":"2091121"}
[08:29:34:148] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：java.lang.NullPointerException，异常信息：null，
[08:30:02:023] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/wrongQuestionBookQuestion/getQuestionWrapperListOfBook，出现异常:java.lang.NullPointerException；
[08:30:02:025] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","X-Forwarded-Host":"localhost:8000","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","X-Forwarded-Proto":"http","Sec-Fetch-Site":"none","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","X-Forwarded-Port":"8000","Sec-Fetch-Mode":"navigate","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","Upgrade-Insecure-Requests":"1","sec-ch-ua-platform":"\"macOS\"","host":"*************:8081","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:58058\"","X-Forwarded-For":"0:0:0:0:0:0:0:1","Sec-Fetch-User":"?1","Accept-Language":"zh-CN,zh;q=0.9"}
[08:30:02:027] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"id":"2091121"}
[08:30:02:028] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：java.lang.NullPointerException，异常信息：null，
[08:32:51:643] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[08:33:15:321] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/wrongQuestionBookQuestion/getQuestionWrapperListOfBook，出现异常:java.lang.NullPointerException；
[08:33:15:330] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","X-Forwarded-Host":"localhost:8000","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","X-Forwarded-Proto":"http","Sec-Fetch-Site":"none","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","X-Forwarded-Port":"8000","Sec-Fetch-Mode":"navigate","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","Upgrade-Insecure-Requests":"1","sec-ch-ua-platform":"\"macOS\"","host":"*************:8081","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:58058\"","X-Forwarded-For":"0:0:0:0:0:0:0:1","Sec-Fetch-User":"?1","Accept-Language":"zh-CN,zh;q=0.9"}
[08:33:15:332] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"id":"2091121"}
[08:33:15:333] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：java.lang.NullPointerException，异常信息：null，
[08:33:15:380] [ERROR] - io.undertow.servlet.api.LoggingExceptionHandler.handleThrowable(LoggingExceptionHandler.java:80) - UT005023: Exception handling request to /wrongQuestionBookQuestion/getQuestionWrapperListOfBook
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.NullPointerException
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:497) ~[jakarta.servlet-api-4.0.4.jar:4.0.4]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:584) ~[jakarta.servlet-api-4.0.4.jar:4.0.4]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.websockets.jsr.JsrWebSocketFilter.doFilter(JsrWebSocketFilter.java:173) ~[undertow-websockets-jsr-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.taurus.examinationassistant.filter.ReplaceStreamFilter.doFilter(ReplaceStreamFilter.java:53) ~[classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.taurus.examinationassistant.filter.SqlXssFilter.doFilter(SqlXssFilter.java:135) ~[classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) ~[undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SessionRestoringHandler.handleRequest(SessionRestoringHandler.java:119) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_281]
Caused by: java.lang.NullPointerException
	at com.taurus.utils.MapUtil.formatHumpName(MapUtil.java:21) ~[classes/:?]
	at com.taurus.examinationassistant.service.QuestionService.getQuestionWrapperById(QuestionService.java:303) ~[classes/:?]
	at com.taurus.examinationassistant.service.QuestionService$$FastClassBySpringCGLIB$$b04e1c0e.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.taurus.examinationassistant.service.QuestionService$$EnhancerBySpringCGLIB$$7ea4769c.getQuestionWrapperById(<generated>) ~[classes/:?]
	at com.taurus.examinationassistant.service.WrongQuestionBookQuestionService.getQuestionWrapperListOfWrongQuestionBook(WrongQuestionBookQuestionService.java:77) ~[classes/:?]
	at com.taurus.examinationassistant.service.WrongQuestionBookQuestionService$$FastClassBySpringCGLIB$$3f9b37d4.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.taurus.examinationassistant.service.WrongQuestionBookQuestionService$$EnhancerBySpringCGLIB$$366055ae.getQuestionWrapperListOfWrongQuestionBook(<generated>) ~[classes/:?]
	at com.taurus.examinationassistant.controller.WrongQuestionBookQuestionController.getQuestionWrapperListOfBook(WrongQuestionBookQuestionController.java:65) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.31.jar:5.3.31]
	... 57 more
[08:33:31:146] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[08:34:07:635] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/wrongQuestionBookQuestion/getQuestionWrapperListOfBook，出现异常:java.lang.IllegalArgumentException；
[08:34:07:646] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","X-Forwarded-Host":"localhost:8000","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","X-Forwarded-Proto":"http","Sec-Fetch-Site":"none","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","X-Forwarded-Port":"8000","Sec-Fetch-Mode":"navigate","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","Cache-Control":"max-age=0","Upgrade-Insecure-Requests":"1","sec-ch-ua-platform":"\"macOS\"","host":"*************:8081","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:58058\"","X-Forwarded-For":"0:0:0:0:0:0:0:1","Sec-Fetch-User":"?1","Accept-Language":"zh-CN,zh;q=0.9"}
[08:34:07:648] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"id":"2091121"}
[08:34:07:648] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：java.lang.IllegalArgumentException: Source must not be null，异常信息：Source must not be null，
[08:34:57:285] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[08:35:13:416] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[08:38:27:661] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[08:42:10:989] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[09:32:25:562] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[09:32:41:219] [WARN] - io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:70) - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
[10:23:01:955] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[10:33:02:493] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[10:33:17:417] [WARN] - io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:70) - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
[16:16:06:943] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:19:16:456] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:20:18:042] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:20:35:798] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:20:50:725] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:21:14:686] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:23:32:693] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:23:49:873] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:24:26:359] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:25:04:885] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:30:16:641] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:31:20:232] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
