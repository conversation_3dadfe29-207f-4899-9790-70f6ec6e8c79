[09:55:11:792] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[09:55:16:671] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[09:55:16:815] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[09:55:16:816] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[09:55:16:816] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[09:55:16:817] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[09:55:16:817] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[09:55:16:817] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[09:55:16:817] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[09:55:16:819] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[09:55:16:827] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[09:55:16:829] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[09:55:16:830] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[09:55:16:830] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[09:55:16:924] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[09:55:17:655] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[09:55:17:667] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[09:55:18:931] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[09:55:18:968] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[09:55:19:680] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[09:55:19:698] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[09:55:19:699] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[09:55:19:700] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[09:55:19:701] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[09:55:19:703] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[09:55:19:706] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[09:55:19:711] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[09:55:19:712] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[09:55:26:083] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[09:55:26:099] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[09:55:26:434] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[09:55:28:029] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：1.588秒
[09:55:34:798] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[09:55:34:874] [WARN] - io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:70) - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
[09:55:35:356] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[09:55:36:544] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[09:55:43:184] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[09:55:44:150] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 47 ms to scan 1 urls, producing 3 keys and 6 values 
[09:55:44:209] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 36 ms to scan 1 urls, producing 4 keys and 9 values 
[09:55:44:229] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 15 ms to scan 1 urls, producing 3 keys and 10 values 
[09:55:44:274] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 40 ms to scan 12 urls, producing 0 keys and 0 values 
[09:55:44:285] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
[09:55:44:297] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
[09:55:44:327] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 23 ms to scan 1 urls, producing 2 keys and 8 values 
[09:55:44:363] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 25 ms to scan 12 urls, producing 0 keys and 0 values 
[09:55:45:510] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[09:55:45:511] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[09:55:45:528] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[09:55:45:550] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[09:55:45:565] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[09:55:45:610] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[09:55:45:759] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service 192.168.8.183:8081 register finished
[09:55:46:186] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 37.52 seconds (JVM running for 39.443)
[09:55:46:218] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[09:56:14:227] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
