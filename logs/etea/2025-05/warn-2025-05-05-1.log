[21:57:57:921] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:58:11:308] [WARN] - io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:70) - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
[22:01:13:738] [ERROR] - com.alibaba.druid.pool.DruidDataSource.put(DruidDataSource.java:2405) - create connection holder error
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1,020 milliseconds ago. The last packet sent successfully to the server was 2,241 milliseconds ago.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.isReadOnly(ConnectionImpl.java:1374) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.isReadOnly(ConnectionImpl.java:1359) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.alibaba.druid.pool.DruidConnectionHolder.<init>(DruidConnectionHolder.java:137) ~[druid-1.1.17.jar:1.1.17]
	at com.alibaba.druid.pool.DruidConnectionHolder.<init>(DruidConnectionHolder.java:77) ~[druid-1.1.17.jar:1.1.17]
	at com.alibaba.druid.pool.DruidDataSource.put(DruidDataSource.java:2395) [druid-1.1.17.jar:1.1.17]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2739) [druid-1.1.17.jar:1.1.17]
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet successfully received from the server was 1,020 milliseconds ago. The last packet sent successfully to the server was 2,241 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_281]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_281]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.protocol.a.NativeProtocol.clearInputStream(NativeProtocol.java:870) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:682) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:156) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.NativeSession.queryServerVariable(NativeSession.java:589) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.isReadOnly(ConnectionImpl.java:1366) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	... 5 more
Caused by: java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.protocol.a.NativeProtocol.clearInputStream(NativeProtocol.java:866) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:682) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:156) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.NativeSession.queryServerVariable(NativeSession.java:589) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.isReadOnly(ConnectionImpl.java:1366) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	... 5 more
[22:14:27:544] [ERROR] - com.alibaba.druid.pool.DruidDataSource.put(DruidDataSource.java:2405) - create connection holder error
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1,017 milliseconds ago. The last packet sent successfully to the server was 9,895 milliseconds ago.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.isReadOnly(ConnectionImpl.java:1374) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.isReadOnly(ConnectionImpl.java:1359) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.alibaba.druid.pool.DruidConnectionHolder.<init>(DruidConnectionHolder.java:137) ~[druid-1.1.17.jar:1.1.17]
	at com.alibaba.druid.pool.DruidConnectionHolder.<init>(DruidConnectionHolder.java:77) ~[druid-1.1.17.jar:1.1.17]
	at com.alibaba.druid.pool.DruidDataSource.put(DruidDataSource.java:2395) [druid-1.1.17.jar:1.1.17]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2739) [druid-1.1.17.jar:1.1.17]
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet successfully received from the server was 1,017 milliseconds ago. The last packet sent successfully to the server was 9,895 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_281]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_281]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.protocol.a.NativeProtocol.clearInputStream(NativeProtocol.java:870) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:682) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:156) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.NativeSession.queryServerVariable(NativeSession.java:589) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.isReadOnly(ConnectionImpl.java:1366) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	... 5 more
Caused by: java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.protocol.a.NativeProtocol.clearInputStream(NativeProtocol.java:866) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:682) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:156) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.NativeSession.queryServerVariable(NativeSession.java:589) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.isReadOnly(ConnectionImpl.java:1366) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	... 5 more
[22:16:07:266] [WARN] - redis.clients.jedis.JedisFactory.destroyObject(JedisFactory.java:163) - Error while QUIT
redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: Read timed out
	at redis.clients.jedis.util.RedisInputStream.ensureFill(RedisInputStream.java:205) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.util.RedisInputStream.readByte(RedisInputStream.java:43) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.Protocol.process(Protocol.java:165) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.Protocol.read(Protocol.java:230) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.Connection.readProtocolWithCheckingBroken(Connection.java:352) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.Connection.getStatusCodeReply(Connection.java:270) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.BinaryJedis.quit(BinaryJedis.java:475) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.JedisFactory.destroyObject(JedisFactory.java:160) [jedis-3.8.0.jar:?]
	at org.apache.commons.pool2.PooledObjectFactory.destroyObject(PooledObjectFactory.java:127) [commons-pool2-2.11.1.jar:2.11.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.destroy(GenericObjectPool.java:611) [commons-pool2-2.11.1.jar:2.11.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.evict(GenericObjectPool.java:729) [commons-pool2-2.11.1.jar:2.11.1]
	at org.apache.commons.pool2.impl.BaseGenericObjectPool$Evictor.run(BaseGenericObjectPool.java:160) [commons-pool2-2.11.1.jar:2.11.1]
	at org.apache.commons.pool2.impl.EvictionTimer$WeakRunner.run(EvictionTimer.java:113) [commons-pool2-2.11.1.jar:2.11.1]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_281]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [?:1.8.0_281]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_281]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [?:1.8.0_281]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_281]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_281]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_281]
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method) ~[?:1.8.0_281]
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116) ~[?:1.8.0_281]
	at java.net.SocketInputStream.read(SocketInputStream.java:171) ~[?:1.8.0_281]
	at java.net.SocketInputStream.read(SocketInputStream.java:141) ~[?:1.8.0_281]
	at java.net.SocketInputStream.read(SocketInputStream.java:127) ~[?:1.8.0_281]
	at redis.clients.jedis.util.RedisInputStream.ensureFill(RedisInputStream.java:199) ~[jedis-3.8.0.jar:?]
	... 19 more
