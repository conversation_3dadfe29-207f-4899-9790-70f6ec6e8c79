[10:21:22:392] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[10:21:34:299] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[10:21:34:447] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[10:21:34:448] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[10:21:34:449] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[10:21:34:450] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[10:21:34:450] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[10:21:34:450] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[10:21:34:450] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[10:21:34:451] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[10:21:34:452] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[10:21:34:453] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[10:21:34:454] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[10:21:34:454] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[10:21:34:540] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[10:21:35:977] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[10:21:35:986] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[10:21:37:522] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[10:21:37:570] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[10:21:38:349] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[10:21:38:362] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[10:21:38:363] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[10:21:38:365] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[10:21:38:367] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[10:21:38:369] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[10:21:38:371] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[10:21:38:377] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[10:21:38:379] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[10:21:46:117] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[10:21:46:131] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[10:21:46:485] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[10:21:48:561] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：2.072秒
[10:21:55:941] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[10:21:56:028] [WARN] - io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:70) - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
[10:21:56:594] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[10:21:57:787] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[10:22:04:970] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[10:22:06:261] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 63 ms to scan 1 urls, producing 3 keys and 6 values 
[10:22:06:314] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 30 ms to scan 1 urls, producing 4 keys and 9 values 
[10:22:06:348] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 28 ms to scan 1 urls, producing 3 keys and 10 values 
[10:22:06:407] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 54 ms to scan 12 urls, producing 0 keys and 0 values 
[10:22:06:424] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 12 ms to scan 1 urls, producing 1 keys and 5 values 
[10:22:06:444] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 14 ms to scan 1 urls, producing 1 keys and 7 values 
[10:22:06:512] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 46 ms to scan 1 urls, producing 2 keys and 8 values 
[10:22:06:554] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 35 ms to scan 12 urls, producing 0 keys and 0 values 
[10:22:08:127] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[10:22:08:129] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[10:22:08:153] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[10:22:08:173] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[10:22:08:189] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[10:22:08:229] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[10:22:08:400] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service 192.168.8.183:8081 register finished
[10:22:08:914] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 49.048 seconds (JVM running for 51.002)
[10:22:08:964] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[10:25:12:414] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[10:25:32:621] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinAccountService.getAutoActiveStatus(QiyeweixinAccountService.java:603) - 查询企业的许可自动激活状态, corpId:wpfqwcEQAAKhl2yYldZWlyMp4OKKMbNw
[10:25:33:089] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinAccountService.getAutoActiveStatus(QiyeweixinAccountService.java:620) - 查询企业的许可自动激活状态响应: {errcode=0, auto_active_status=1, errmsg=ok}
[10:25:36:060] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinAccountService.getAccountList(QiyeweixinAccountService.java:352) - 获取企业的账号列表, corpId:wpfqwcEQAAKhl2yYldZWlyMp4OKKMbNw, limit:50, cursor:
[10:25:36:463] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinAccountService.getAccountList(QiyeweixinAccountService.java:375) - 获取企业的账号列表响应: {errcode=0, next_cursor=69mjmzr9QZ-Cl0ny8XY9PQ, errmsg=ok, account_list=[], has_more=0}
[10:25:43:926] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:215) - activeProfile:test-etea
[10:25:43:926] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:222) - targetUrl:https://www.qikaokao.com/qiyeweixin/#/qiyeweixinCompanyInfo?companyId=6040&qkkQiyeCorpid=wpfqwcEQAAKhl2yYldZWlyMp4OKKMbNw&qkkQiyeAgentId=1000088
[10:25:43:927] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:226) - serviceUrl:http://127.0.0.1:8077/api/ocr/recognizeUrl?url=%s&ocr=ali
[10:25:43:927] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:229) - url:http://127.0.0.1:8077/api/ocr/recognizeUrl?url=https%3A%2F%2Fwww.qikaokao.com%2Fqiyeweixin%2F%23%2FqiyeweixinCompanyInfo%3FcompanyId%3D6040%26qkkQiyeCorpid%3DwpfqwcEQAAKhl2yYldZWlyMp4OKKMbNw%26qkkQiyeAgentId%3D1000088&ocr=ali
[10:26:08:300] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:241) - result:{data=[部门列表, $$ 9356 $$ 华东一区 $$ 分隔符 $$ 9357 $$ 车间管理$$ 分隔符, $$9358$$质量与服务部$$分隔符$$9359$$ 华东二区 $$ 分隔符, $$ 9360 $$ 大功率流水线组$$分隔符 $$ 9361 $$ 大功率单工位组$$分隔符, $$ 9362 $$ 负载测试部$$分隔符 $$ 9363 $$ 负载测试组$$分隔符, $$ 9364 $$ 车间管理$$分隔符 $$ 9365 $$ 生产一部$$分隔符$$9366$$ 生产二部 $$分隔符, $$ 9367 $$ 车间管理$$分隔符 $$9368$$小功率变频组 $$分隔符, $$ 9369 $$ 小功率电气组$$分隔符 $$ 9370 $$ 前沿技术部$$分隔符, 人员列表, $$ 1727169 $$ 李帅军$$ 分隔符 $$ 1727170 $$ 王鑫潮 $$分隔符, $$ 1727171 $$ 雷芸菲$$ 分隔符 $$ 1727172 $$ 徐梦影 $$ 分隔符 $$ 1727173 $$ 郑雅$$分隔符, $$ 1727174$$ 史安也 $$ 分隔符 $$ 1727175 $$ 韩永庆 $$ 分隔符, $$ 1727176 $$ 杨赵文 $$分隔符 $$ 1727177 $$ 刘奥 $$ 分隔符 $$ 1727178 $$ 归李辉$$分隔符, $$ 1727179 $$ 詹继杨 $$ 分隔符 $$ 1727180 $$ 娄恩会 $$ 分隔符, $$ 1727181 $$ 乐玉龙 $$ 分隔符 $$ 1727182 $$ 王美伊 $$ 分隔符 $$ 1727183 $$ 丁惠$$分隔符, $$ 1727184 $$ 魏大智$$ 分隔符 $$ 1727185 $$ 熊啟铭 $$ 分隔符 $$ 1727186 $$ 罗星$$分隔符, $$ 1727187 $$ 刘勇杰 $$ 分隔符 $$ 1727188 $$ 靳通 $$ 分隔符 $$ 1727189 $$ 蔡子贤$$分隔符, $$ 1727190 $$ 刘胜男 $$ 分隔符 $$ 1727191 $$ 彭江 $$ 分隔符$$ 1727192 $$ 闫金山 $$ 分隔符, $$ 1727193 $$ 秦焱翔 $$ 分隔符 $$ 1727194 $$ 李小霞 $$ 分隔符 $$ 1727195 $$ 杨平$$分隔符, $$ 1727196 $$ 张瑞利 $$ 分隔符 $$ 1727197 $$ 刘泽文 $$ 分隔符, $$ 1727198 $$ 黄湘成 $$ 分隔符 $$ 1727199 $$ 吕荣祥 $$分隔符, $$ 1727200 $$ 顾佳明$$分隔符 $$ 1727201 $$ 张博宇 $$ 分隔符 $$ 1727202 $$ 符野$$ 分隔符, $$ 1727203 $$ 黄梦辉 $$ 分隔符 $$ 1727204 $$ 戴忠强 $$ 分隔符, $$ 1727205 $$ 彭家贵 $$ 分隔符 $$ 1727206 $$ 曹冻 $$ 分隔符$$ 1727207 $$ 乔佳琦 $$分隔符, $$ 1727208 $$ 张昆 $$ 分隔符 $$ 1727209 $$ 彭少杰 $$ 分隔符 $$ 1727210 $$ 苗磊 $$分隔符, $$ 1727211 $$ 朱永平$$ 分隔符 $$ 1727212 $$ 吴梓鑫 $$分隔符, $$ 1727213$$ 鲁东海 $$ 分隔符 _$$ 1727214 $$ 何海伟 $$ 分隔符$$ 1727215 $$ 赵艺 $$ 分隔符, $$ 1727187 $$ 刘勇杰 $$ 分隔符 $$ 1727188 $$ 靳通 $$ 分隔符$$ 1727189 $$ 蔡子贤 $$分隔符, $$ 1727190 $$ 刘胜男$$ 分隔符 $$ 1727191 $$ 彭江 $$ 分隔符 $$ 1727192 $$ 闫金山$$分隔符, $$ 1727193 $$ 秦焱翔 $$ 分隔符 $$ 1727194 $$ 李小霞 $$ 分隔符 $$ 1727195 $$ 杨平$$分隔符, $$ 1727196 $$ 张瑞利 $$ 分隔符 $$ 1727197 $$ 刘泽文 $$ 分隔符, $$ 1727198 $$ 黄湘成 $$ 分隔符 $$ 1727199 $$ 吕荣祥 $$分隔符, $$ 1727200 $$ 顾佳明$$分隔符 $$ 1727201 $$ 张博宇 $$ 分隔符 $$ 1727202 $$ 符野$$ 分隔符, $$ 1727203 $$ 黄梦辉 $$ 分隔符 $$ 1727204 $$ 戴忠强 $$ 分隔符, $$ 1727205 $$ 彭家贵 $$ 分隔符 $$ 1727206 $$ 曹冻 $$ 分隔符$$ 1727207 $$ 乔佳琦 $$分隔符, $$ 1727208 $$ 张昆 $$ 分隔符 $$ 1727209 $$ 彭少杰 $$ 分隔符$$1727210 $$ 苗磊 $$ 分隔符, $$ 1727211$$ 朱永平$$分隔符$$ 1727212 $$ 吴梓鑫 $$分隔符, $$ 1727213 $$ 鲁东海 $$ 分隔符 $$ 1727214 $$ 何海伟 $$ 分隔符 $$ 1727215 $$ 赵艺$$分隔符, $$ 1727216 $$ 蒋贵盛 $$ 分隔符 $$ 1727217 $$ 陈灶晖 $$ 分隔符, $$ 1727218$$ 於宗海$$分隔符], errorMessage=success, errorCode=0}
[10:26:08:317] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9356, departName=华东一区, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:08:541] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9357, departName=车间管理, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:08:648] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9358, departName=质量与服务部, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:08:822] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9359, departName=华东二区, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:08:935] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9360, departName=大功率流水线组, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:09:041] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9361, departName=大功率单工位组, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:09:133] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9362, departName=负载测试部, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:09:294] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9363, departName=负载测试组, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:09:431] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9364, departName=车间管理, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:09:626] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9365, departName=生产一部, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:09:737] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9366, departName=生产二部, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:09:932] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9367, departName=车间管理, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:10:101] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9368, departName=小功率变频组, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:10:262] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9369, departName=小功率电气组, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:10:456] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9370, departName=前沿技术部, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:10:586] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727169, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=李帅军, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:10:772] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727170, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=王鑫潮, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:10:963] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727171, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=雷芸菲, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:11:125] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727172, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=徐梦影, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:11:286] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727173, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=郑雅, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:11:610] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727174, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=史安也, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:11:769] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727175, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=韩永庆, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:11:920] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727176, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=杨赵文, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:12:019] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727177, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=刘奥, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:12:109] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727178, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=归李辉, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:12:218] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727179, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=詹继杨, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:12:324] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727180, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=娄恩会, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:12:423] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727181, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=乐玉龙, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:12:512] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727182, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=王美伊, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:12:618] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727183, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=丁惠, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:12:706] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727184, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=魏大智, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:12:822] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727185, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=熊啟铭, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:12:987] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727186, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=罗星, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:13:077] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727187, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=刘勇杰, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:13:163] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727188, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=靳通, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:13:238] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727189, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=蔡子贤, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:13:314] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727190, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=刘胜男, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:13:422] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727191, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=彭江, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:13:512] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727192, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=闫金山, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:13:597] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727193, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=秦焱翔, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:13:665] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727194, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=李小霞, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:13:740] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727195, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=杨平, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:13:824] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727196, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=张瑞利, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:13:934] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727197, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=刘泽文, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:14:037] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727198, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=黄湘成, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:14:126] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727199, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=吕荣祥, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:14:208] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727200, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=顾佳明, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:14:292] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727201, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=张博宇, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:14:347] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727202, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=符野, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:14:462] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727203, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=黄梦辉, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:14:533] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727204, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=戴忠强, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:14:607] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727205, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=彭家贵, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:14:707] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727206, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=曹冻, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:14:793] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727207, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=乔佳琦, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:14:878] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727208, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=张昆, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:14:948] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727209, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=彭少杰, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:15:053] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727210, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=苗磊, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:15:137] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727211, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=朱永平, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:15:231] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727212, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=吴梓鑫, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:15:308] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727213, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=鲁东海, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:15:407] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727214, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=何海伟, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:15:473] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727215, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=赵艺, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:15:561] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727187, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=刘勇杰, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:15:623] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727188, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=靳通, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:15:732] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727189, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=蔡子贤, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:15:818] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727190, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=刘胜男, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:15:918] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727191, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=彭江, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:16:047] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727192, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=闫金山, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:16:133] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727193, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=秦焱翔, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:16:239] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727194, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=李小霞, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:16:390] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727195, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=杨平, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:16:588] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727196, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=张瑞利, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:16:702] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727197, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=刘泽文, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:16:787] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727198, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=黄湘成, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:16:881] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727199, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=吕荣祥, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:16:981] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727200, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=顾佳明, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:17:069] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727201, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=张博宇, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:17:173] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727202, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=符野, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:17:258] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727203, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=黄梦辉, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:17:382] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727204, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=戴忠强, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:17:470] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727205, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=彭家贵, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:17:569] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727206, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=曹冻, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:17:660] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727207, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=乔佳琦, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:17:743] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727208, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=张昆, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:17:816] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727209, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=彭少杰, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:17:938] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727210, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=苗磊, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:18:028] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727211, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=朱永平, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:18:197] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727212, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=吴梓鑫, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:18:283] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727213, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=鲁东海, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:18:389] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727214, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=何海伟, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:18:482] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727215, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=赵艺, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:18:581] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727216, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=蒋贵盛, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:18:669] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727217, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=陈灶晖, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:18:761] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727218, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=於宗海, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:40:24:505] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinAccountService.getAutoActiveStatus(QiyeweixinAccountService.java:603) - 查询企业的许可自动激活状态, corpId:wpfqwcEQAAWaU9nX3bDFZlntOKTU_rsg
[10:40:25:038] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinAccountService.getAutoActiveStatus(QiyeweixinAccountService.java:620) - 查询企业的许可自动激活状态响应: {errcode=701071, errmsg=never buy any license for corp, hint: [1748313625200120391793910], from ip: ***************, more info at https://open.work.weixin.qq.com/devtool/query?e=701071}
[10:40:25:052] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinAccountService.getAutoActiveStatus(QiyeweixinAccountService.java:625) - 查询企业的许可自动激活状态失败：errcode:701071,errmsg:never buy any license for corp, hint: [1748313625200120391793910], from ip: ***************, more info at https://open.work.weixin.qq.com/devtool/query?e=701071
