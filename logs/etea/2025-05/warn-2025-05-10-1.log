[09:31:58:448] [WARN] - redis.clients.jedis.JedisFactory.destroyObject(JedisFactory.java:168) - Error while close
redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketException: Broken pipe (Write failed)
	at redis.clients.jedis.Connection.disconnect(Connection.java:256) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.BinaryClient.disconnect(BinaryClient.java:162) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.Connection.close(Connection.java:246) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.BinaryClient.close(BinaryClient.java:168) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.BinaryJedis.close(BinaryJedis.java:340) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.Jedis.close(Jedis.java:4084) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.JedisFactory.destroyObject(JedisFactory.java:166) ~[jedis-3.8.0.jar:?]
	at org.apache.commons.pool2.PooledObjectFactory.destroyObject(PooledObjectFactory.java:127) ~[commons-pool2-2.11.1.jar:2.11.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.destroy(GenericObjectPool.java:611) ~[commons-pool2-2.11.1.jar:2.11.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.invalidateObject(GenericObjectPool.java:941) ~[commons-pool2-2.11.1.jar:2.11.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.invalidateObject(GenericObjectPool.java:914) ~[commons-pool2-2.11.1.jar:2.11.1]
	at redis.clients.jedis.util.Pool.returnBrokenResourceObject(Pool.java:124) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.util.Pool.returnBrokenResource(Pool.java:103) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.Jedis.close(Jedis.java:4079) ~[jedis-3.8.0.jar:?]
	at org.springframework.data.redis.connection.jedis.JedisConnection.close(JedisConnection.java:352) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at org.springframework.data.redis.listener.RedisMessageListenerContainer$Subscriber.closeConnection(RedisMessageListenerContainer.java:1320) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at org.springframework.data.redis.listener.RedisMessageListenerContainer.handleSubscriptionException(RedisMessageListenerContainer.java:890) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at org.springframework.data.redis.listener.RedisMessageListenerContainer$BlockingSubscriber.lambda$eventuallyPerformSubscription$2(RedisMessageListenerContainer.java:1434) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_281]
Caused by: java.net.SocketException: Broken pipe (Write failed)
	at java.net.SocketOutputStream.socketWrite0(Native Method) ~[?:1.8.0_281]
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:111) ~[?:1.8.0_281]
	at java.net.SocketOutputStream.write(SocketOutputStream.java:155) ~[?:1.8.0_281]
	at redis.clients.jedis.util.RedisOutputStream.flushBuffer(RedisOutputStream.java:52) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.util.RedisOutputStream.flush(RedisOutputStream.java:133) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.Connection.disconnect(Connection.java:252) ~[jedis-3.8.0.jar:?]
	... 18 more
[21:44:36:741] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:44:53:279] [WARN] - io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:70) - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
[22:14:50:845] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[22:22:50:190] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[22:53:15:426] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[22:54:04:392] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[22:54:59:659] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[22:56:09:722] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[22:59:47:882] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[23:06:34:042] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[23:23:30:473] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[23:34:10:703] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[23:44:21:159] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[23:56:58:157] [WARN] - redis.clients.jedis.JedisFactory.destroyObject(JedisFactory.java:163) - Error while QUIT
redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: Read timed out
	at redis.clients.jedis.util.RedisInputStream.ensureFill(RedisInputStream.java:205) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.util.RedisInputStream.readByte(RedisInputStream.java:43) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.Protocol.process(Protocol.java:165) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.Protocol.read(Protocol.java:230) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.Connection.readProtocolWithCheckingBroken(Connection.java:352) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.Connection.getStatusCodeReply(Connection.java:270) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.BinaryJedis.quit(BinaryJedis.java:475) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.JedisFactory.destroyObject(JedisFactory.java:160) [jedis-3.8.0.jar:?]
	at org.apache.commons.pool2.PooledObjectFactory.destroyObject(PooledObjectFactory.java:127) [commons-pool2-2.11.1.jar:2.11.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.destroy(GenericObjectPool.java:611) [commons-pool2-2.11.1.jar:2.11.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.evict(GenericObjectPool.java:729) [commons-pool2-2.11.1.jar:2.11.1]
	at org.apache.commons.pool2.impl.BaseGenericObjectPool$Evictor.run(BaseGenericObjectPool.java:160) [commons-pool2-2.11.1.jar:2.11.1]
	at org.apache.commons.pool2.impl.EvictionTimer$WeakRunner.run(EvictionTimer.java:113) [commons-pool2-2.11.1.jar:2.11.1]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_281]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [?:1.8.0_281]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_281]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [?:1.8.0_281]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_281]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_281]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_281]
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method) ~[?:1.8.0_281]
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116) ~[?:1.8.0_281]
	at java.net.SocketInputStream.read(SocketInputStream.java:171) ~[?:1.8.0_281]
	at java.net.SocketInputStream.read(SocketInputStream.java:141) ~[?:1.8.0_281]
	at java.net.SocketInputStream.read(SocketInputStream.java:127) ~[?:1.8.0_281]
	at redis.clients.jedis.util.RedisInputStream.ensureFill(RedisInputStream.java:199) ~[jedis-3.8.0.jar:?]
	... 19 more
[23:58:51:012] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
