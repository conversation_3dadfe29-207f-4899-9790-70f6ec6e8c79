[11:56:26:389] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/question/importQuestionUnion，出现异常:org.mybatis.spring.MyBatisSystemException；
[11:56:26:466] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"Origin":"http://localhost:8080","X-Forwarded-Prefix":"/exam","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","Sec-Fetch-Dest":"empty","Accept-Encoding":"gzip, deflate, br, zstd","Sec-Fetch-Mode":"cors","sec-ch-ua-mobile":"?0","host":"*************:8081","client":"pc_admin_etea","Content-Length":"671271","Content-Type":"multipart/form-data; boundary=----WebKitFormBoundarygvEfNErTEAYBmqzo","enctype":"multipart/form-data","Accept":"application/json, text/plain, */*","Referer":"http://localhost:8080/","X-Forwarded-Host":"127.0.0.1:8000","X-Forwarded-Proto":"http","Sec-Fetch-Site":"cross-site","userId":"37377","X-Forwarded-Port":"8000","token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJ5aWRhbyIsImV4cCI6MTc0NzI4MTI5OSwiaWF0IjoxNzQ2Njc2NDk5LCJ1c2VySWQiOjM3Mzc3fQ.FZPNPJdUJafZJF_-Sfrn2gaNB0u9Te3zc4-o1EVZXyA","sec-ch-ua":"\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"","sec-ch-ua-platform":"\"macOS\"","Forwarded":"proto=http;host=\"127.0.0.1:8000\";for=\"127.0.0.1:56671\"","X-Forwarded-For":"127.0.0.1","Accept-Language":"zh-CN,zh;q=0.9"}
[11:56:26:470] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"companyId":"0","userId":"37377"}
[11:56:26:470] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error updating database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLException: interrupt
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLException: interrupt，异常信息：nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error updating database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLException: interrupt
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLException: interrupt，
