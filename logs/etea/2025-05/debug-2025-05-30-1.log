[09:55:11:792] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[09:55:14:885] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$AutoConfiguredMapperScannerRegistrar.registerBeanDefinitions(MybatisAutoConfiguration.java:252) - Searching for mappers annotated with @Mapper
[09:55:14:903] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$AutoConfiguredMapperScannerRegistrar.registerBeanDefinitions(MybatisAutoConfiguration.java:264) - Using auto-configuration base package 'com.taurus.examinationassistant'
[09:55:16:671] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[09:55:16:815] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[09:55:16:816] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[09:55:16:816] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[09:55:16:817] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[09:55:16:817] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[09:55:16:817] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[09:55:16:817] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[09:55:16:819] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[09:55:16:827] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[09:55:16:829] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[09:55:16:830] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[09:55:16:830] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[09:55:16:924] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[09:55:17:655] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[09:55:17:667] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[09:55:18:931] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[09:55:18:959] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[09:55:18:968] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[09:55:19:391] [DEBUG] - io.micrometer.core.util.internal.logging.InternalLoggerFactory.newDefaultFactory(InternalLoggerFactory.java:59) - Using SLF4J as the default logging framework
[09:55:19:680] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[09:55:19:698] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[09:55:19:699] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[09:55:19:700] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[09:55:19:701] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[09:55:19:703] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[09:55:19:706] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[09:55:19:711] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[09:55:19:712] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[09:55:20:939] [DEBUG] - io.undertow.server.session.InMemorySessionManager.registerSessionListener(InMemorySessionManager.java:268) - Registered session listener io.undertow.servlet.core.SessionListenerBridge@699626a6
[09:55:21:039] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[09:55:26:083] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[09:55:26:099] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[09:55:26:434] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[09:55:26:440] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.OssFileAudioMapper对应的Mapper
[09:55:26:634] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserOfProductMapper对应的Mapper
[09:55:26:667] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.NameListDetailMapper对应的Mapper
[09:55:26:724] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PermissionAuthorizationMapper对应的Mapper
[09:55:26:769] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserDeviceBindingMapper对应的Mapper
[09:55:26:811] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AppGroupUserMapper对应的Mapper
[09:55:26:858] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.FreePracticeOfUserMapper对应的Mapper
[09:55:26:893] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.OssFileMapper对应的Mapper
[09:55:26:920] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyGroupUserMapper对应的Mapper
[09:55:26:932] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MessageScheduleMapper对应的Mapper
[09:55:26:949] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongQuestionBookQuestionMapper对应的Mapper
[09:55:26:978] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CustomerRequirementMapper对应的Mapper
[09:55:27:025] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionExampleDeletedMapper对应的Mapper
[09:55:27:048] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.OrdersMapper对应的Mapper
[09:55:27:072] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteUserSignatureMapper对应的Mapper
[09:55:27:085] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QrcodeScanEntryMapper对应的Mapper
[09:55:27:092] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.GameQuestionRandomRuleMapper对应的Mapper
[09:55:27:103] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentProductOfUserMapper对应的Mapper
[09:55:27:108] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationExamineeSnapshotMapper对应的Mapper
[09:55:27:114] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ViewPerformanceWithoutAdTransactionMapper对应的Mapper
[09:55:27:125] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookExamineeMapper对应的Mapper
[09:55:27:134] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MultipleMarkingExaminationMapper对应的Mapper
[09:55:27:143] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentSubjectOfUserMapper对应的Mapper
[09:55:27:153] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionFavoriteMapper对应的Mapper
[09:55:27:164] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookTagMapper对应的Mapper
[09:55:27:172] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyRegisterFormMapper对应的Mapper
[09:55:27:181] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentClassificationInfoMapper对应的Mapper
[09:55:27:188] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.DiscountMapper对应的Mapper
[09:55:27:198] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SysLogMapper对应的Mapper
[09:55:27:208] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentUnlockMapper对应的Mapper
[09:55:27:220] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookSummaryMapper对应的Mapper
[09:55:27:232] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarUserMapper对应的Mapper
[09:55:27:248] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SystemMessageMapper对应的Mapper
[09:55:27:257] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.NanxiangRegisterMapper对应的Mapper
[09:55:27:263] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ManualServiceRecordMapper对应的Mapper
[09:55:27:270] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyUserGroupMapper对应的Mapper
[09:55:27:278] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarStageEventMapper对应的Mapper
[09:55:27:283] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperPraiseMapper对应的Mapper
[09:55:27:290] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyDepartmentMapper对应的Mapper
[09:55:27:300] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UniversalProductOrderRelationMapper对应的Mapper
[09:55:27:313] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductIntroductionMediaMapper对应的Mapper
[09:55:27:323] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionBankQuestionMapper对应的Mapper
[09:55:27:330] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationWxGidMapper对应的Mapper
[09:55:27:336] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserExaminationInstanceReportMapper对应的Mapper
[09:55:27:349] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductCountTransactionMapper对应的Mapper
[09:55:27:354] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyFunctionRoleMapper对应的Mapper
[09:55:27:366] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookPracticeSummaryMapper对应的Mapper
[09:55:27:377] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserSummaryMapper对应的Mapper
[09:55:27:388] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AppMapper对应的Mapper
[09:55:27:394] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserExaminationPerformanceReportMapper对应的Mapper
[09:55:27:401] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductOrderRelationMapper对应的Mapper
[09:55:27:409] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CategoryMapper对应的Mapper
[09:55:27:414] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.InviteRelationMapper对应的Mapper
[09:55:27:424] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QiyewxUserAccountLicenseMapper对应的Mapper
[09:55:27:434] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.FileAccessPermissionMapper对应的Mapper
[09:55:27:438] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.TaxIdentityInfoMapper对应的Mapper
[09:55:27:445] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarMapper对应的Mapper
[09:55:27:450] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AnnualReportMapper对应的Mapper
[09:55:27:453] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationTagMapper对应的Mapper
[09:55:27:465] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AppStartNoticeMapper对应的Mapper
[09:55:27:471] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PassRaceStageMapper对应的Mapper
[09:55:27:481] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationInstanceEventMapper对应的Mapper
[09:55:27:486] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookGradeMapper对应的Mapper
[09:55:27:489] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteContentAccessMapper对应的Mapper
[09:55:27:494] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperMapper对应的Mapper
[09:55:27:500] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionSummaryMapper对应的Mapper
[09:55:27:504] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongQuestionBookMapper对应的Mapper
[09:55:27:509] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AppStartNoticeReadUserMapper对应的Mapper
[09:55:27:513] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationStartupMapper对应的Mapper
[09:55:27:518] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongBookMapper对应的Mapper
[09:55:27:524] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationExamineeMapper对应的Mapper
[09:55:27:531] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperQuestionMapper对应的Mapper
[09:55:27:535] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MultipleMarkingExaminationInstanceMapper对应的Mapper
[09:55:27:540] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductOrderRelationCountMapper对应的Mapper
[09:55:27:545] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyJobGradeMapper对应的Mapper
[09:55:27:553] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionRandomMapper对应的Mapper
[09:55:27:560] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteOrderRelationMapper对应的Mapper
[09:55:27:565] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserReportRelationMapper对应的Mapper
[09:55:27:576] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionCompositionChildMapper对应的Mapper
[09:55:27:583] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WxMiniprogramSubscribeMessageOfEteaMapper对应的Mapper
[09:55:27:596] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PointConsumerationMapper对应的Mapper
[09:55:27:601] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarUserMilestoneMapper对应的Mapper
[09:55:27:606] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperTagMapper对应的Mapper
[09:55:27:612] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentSubjectAccessInfoMapper对应的Mapper
[09:55:27:617] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PermissionMapper对应的Mapper
[09:55:27:623] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyRegisterFormFlowRecordMapper对应的Mapper
[09:55:27:629] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionRandomRangeMapper对应的Mapper
[09:55:27:637] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookFavoriteMapper对应的Mapper
[09:55:27:648] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsitePaperInstanceMapper对应的Mapper
[09:55:27:657] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PracticePushScheduleMapper对应的Mapper
[09:55:27:687] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UniversalOrderMapper对应的Mapper
[09:55:27:700] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PointRuleMapper对应的Mapper
[09:55:27:706] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserMapper对应的Mapper
[09:55:27:715] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QiyewxInterfaceLicenseOrderMapper对应的Mapper
[09:55:27:721] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SysUserRoleMapper对应的Mapper
[09:55:27:727] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductMapper对应的Mapper
[09:55:27:731] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationNameListMapper对应的Mapper
[09:55:27:735] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MessagePlanMapper对应的Mapper
[09:55:27:741] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserOfYncxMapper对应的Mapper
[09:55:27:747] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SysRoleMapper对应的Mapper
[09:55:27:751] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PassRaceResultMapper对应的Mapper
[09:55:27:756] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarUserProfileMapper对应的Mapper
[09:55:27:762] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationMapper对应的Mapper
[09:55:27:768] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PointUserMapper对应的Mapper
[09:55:27:772] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionMapper对应的Mapper
[09:55:27:858] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookUserRecordMapper对应的Mapper
[09:55:27:880] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongQuestionBookReviewSummaryMapper对应的Mapper
[09:55:27:884] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentSubjectMapper对应的Mapper
[09:55:27:890] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookQuestionMapper对应的Mapper
[09:55:27:894] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationFavoriteMapper对应的Mapper
[09:55:27:896] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PointDetailMapper对应的Mapper
[09:55:27:901] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookMapper对应的Mapper
[09:55:27:915] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserFormFlowRecordMapper对应的Mapper
[09:55:27:921] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QiyewxOrderMapper对应的Mapper
[09:55:27:926] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteWithdrawMapper对应的Mapper
[09:55:27:931] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.HomepageUserFollowedMapper对应的Mapper
[09:55:27:934] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperFavoriteMapper对应的Mapper
[09:55:27:936] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WeixinAccountMapper对应的Mapper
[09:55:27:938] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookPraiseMapper对应的Mapper
[09:55:27:942] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.GameResultMapper对应的Mapper
[09:55:27:945] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsitePaperInstanceProcessMapper对应的Mapper
[09:55:27:951] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SmsMessageMapper对应的Mapper
[09:55:27:953] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserCompanyMapper对应的Mapper
[09:55:27:956] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserFormMapper对应的Mapper
[09:55:27:961] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.GameMapper对应的Mapper
[09:55:27:965] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookPracticeProcessMapper对应的Mapper
[09:55:27:968] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentSubjectCategoryMapper对应的Mapper
[09:55:27:970] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.TemplateMapper对应的Mapper
[09:55:27:973] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationInstanceProcessMapper对应的Mapper
[09:55:27:984] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.VersionMapper对应的Mapper
[09:55:27:986] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserViewPerformanceWithoutAdMapper对应的Mapper
[09:55:27:988] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MessageTemplateMapper对应的Mapper
[09:55:27:991] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongQuestionBookReviewProcessMapper对应的Mapper
[09:55:27:994] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyMapper对应的Mapper
[09:55:27:996] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationInstanceMapper对应的Mapper
[09:55:28:000] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MessagePushSubscriptionMapper对应的Mapper
[09:55:28:003] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.NameListMapper对应的Mapper
[09:55:28:005] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteOrderMapper对应的Mapper
[09:55:28:008] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductIntroductionMapper对应的Mapper
[09:55:28:011] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.TaxSheetApplicationMapper对应的Mapper
[09:55:28:014] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PassRaceGameMapper对应的Mapper
[09:55:28:017] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionAttachmentMapper对应的Mapper
[09:55:28:020] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationGroupExaminationMapper对应的Mapper
[09:55:28:023] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteFavoriteMapper对应的Mapper
[09:55:28:029] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：1.588秒
[09:55:34:752] [DEBUG] - io.netty.util.internal.logging.InternalLoggerFactory.useSlf4JLoggerFactory(InternalLoggerFactory.java:63) - Using SLF4J as the default logging framework
[09:55:34:758] [DEBUG] - io.netty.util.concurrent.GlobalEventExecutor.<clinit>(GlobalEventExecutor.java:53) - -Dio.netty.globalEventExecutor.quietPeriodSeconds: 1
[09:55:34:769] [DEBUG] - io.netty.util.internal.InternalThreadLocalMap.<clinit>(InternalThreadLocalMap.java:100) - -Dio.netty.threadLocalMap.stringBuilder.initialSize: 1024
[09:55:34:770] [DEBUG] - io.netty.util.internal.InternalThreadLocalMap.<clinit>(InternalThreadLocalMap.java:101) - -Dio.netty.threadLocalMap.stringBuilder.maxSize: 4096
[09:55:34:798] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[09:55:34:802] [DEBUG] - io.netty.channel.MultithreadEventLoopGroup.<clinit>(MultithreadEventLoopGroup.java:44) - -Dio.netty.eventLoopThreads: 16
[09:55:34:832] [DEBUG] - io.netty.util.internal.PlatformDependent0.explicitNoUnsafeCause0(PlatformDependent0.java:515) - -Dio.netty.noUnsafe: false
[09:55:34:833] [DEBUG] - io.netty.util.internal.PlatformDependent0.javaVersion0(PlatformDependent0.java:1026) - Java version: 8
[09:55:34:834] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:140) - sun.misc.Unsafe.theUnsafe: available
[09:55:34:836] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:164) - sun.misc.Unsafe.copyMemory: available
[09:55:34:837] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:196) - sun.misc.Unsafe.storeFence: available
[09:55:34:838] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:239) - java.nio.Buffer.address: available
[09:55:34:839] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:312) - direct buffer constructor: available
[09:55:34:840] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:403) - java.nio.Bits.unaligned: available, true
[09:55:34:841] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:478) - jdk.internal.misc.Unsafe.allocateUninitializedArray(int): unavailable prior to Java9
[09:55:34:841] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:501) - java.nio.DirectByteBuffer.<init>(long, {int,long}): available
[09:55:34:841] [DEBUG] - io.netty.util.internal.PlatformDependent.unsafeUnavailabilityCause0(PlatformDependent.java:1157) - sun.misc.Unsafe: available
[09:55:34:842] [DEBUG] - io.netty.util.internal.PlatformDependent.tmpdir0(PlatformDependent.java:1303) - -Dio.netty.tmpdir: /var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T (java.io.tmpdir)
[09:55:34:842] [DEBUG] - io.netty.util.internal.PlatformDependent.bitMode0(PlatformDependent.java:1382) - -Dio.netty.bitMode: 64 (sun.arch.data.model)
[09:55:34:846] [DEBUG] - io.netty.util.internal.PlatformDependent.isOsx0(PlatformDependent.java:1125) - Platform: MacOS
[09:55:34:847] [DEBUG] - io.netty.util.internal.PlatformDependent.<clinit>(PlatformDependent.java:176) - -Dio.netty.maxDirectMemory: 3817865216 bytes
[09:55:34:848] [DEBUG] - io.netty.util.internal.PlatformDependent.<clinit>(PlatformDependent.java:183) - -Dio.netty.uninitializedArrayAllocationThreshold: -1
[09:55:34:849] [DEBUG] - io.netty.util.internal.CleanerJava6.<clinit>(CleanerJava6.java:92) - java.nio.ByteBuffer.cleaner(): available
[09:55:34:849] [DEBUG] - io.netty.util.internal.PlatformDependent.<clinit>(PlatformDependent.java:203) - -Dio.netty.noPreferDirect: false
[09:55:34:850] [DEBUG] - io.netty.channel.nio.NioEventLoop.<clinit>(NioEventLoop.java:110) - -Dio.netty.noKeySetOptimization: false
[09:55:34:850] [DEBUG] - io.netty.channel.nio.NioEventLoop.<clinit>(NioEventLoop.java:111) - -Dio.netty.selectorAutoRebuildThreshold: 512
[09:55:34:858] [DEBUG] - io.netty.util.internal.PlatformDependent$Mpsc.<clinit>(PlatformDependent.java:1008) - org.jctools-core.MpscChunkedArrayQueue: available
[09:55:34:874] [WARN] - io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:70) - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
[09:55:34:883] [DEBUG] - io.netty.util.NetUtil.<clinit>(NetUtil.java:148) - -Djava.net.preferIPv4Stack: false
[09:55:34:883] [DEBUG] - io.netty.util.NetUtil.<clinit>(NetUtil.java:149) - -Djava.net.preferIPv6Addresses: false
[09:55:34:886] [DEBUG] - io.netty.util.NetUtilInitializations.determineLoopback(NetUtilInitializations.java:145) - Loopback interface: lo0 (lo0, 0:0:0:0:0:0:0:1%lo0)
[09:55:34:887] [DEBUG] - io.netty.util.NetUtil$SoMaxConnAction.run(NetUtil.java:206) - Failed to get SOMAXCONN from sysctl and file /proc/sys/net/core/somaxconn. Default: 128
[09:55:34:909] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:141) - Default ResolvedAddressTypes: IPV4_PREFERRED
[09:55:34:909] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:142) - Localhost address: localhost/127.0.0.1
[09:55:34:910] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:151) - Windows hostname: null
[09:55:34:913] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:164) - Default search domains: []
[09:55:34:914] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:173) - Default UnixResolverOptions{ndots=1, timeout=5, attempts=16}
[09:55:34:926] [DEBUG] - io.netty.resolver.DefaultHostsFileEntriesResolver.<clinit>(DefaultHostsFileEntriesResolver.java:53) - -Dio.netty.hostsFileRefreshInterval: 0
[09:55:34:941] [DEBUG] - io.netty.util.ResourceLeakDetector.<clinit>(ResourceLeakDetector.java:129) - -Dio.netty.leakDetection.level: simple
[09:55:34:941] [DEBUG] - io.netty.util.ResourceLeakDetector.<clinit>(ResourceLeakDetector.java:130) - -Dio.netty.leakDetection.targetRecords: 4
[09:55:34:942] [DEBUG] - io.netty.util.ResourceLeakDetectorFactory$DefaultResourceLeakDetectorFactory.newResourceLeakDetector(ResourceLeakDetectorFactory.java:196) - Loaded default ResourceLeakDetector: io.netty.util.ResourceLeakDetector@1b960737
[09:55:35:004] [DEBUG] - io.netty.channel.DefaultChannelId.<clinit>(DefaultChannelId.java:79) - -Dio.netty.processId: 96806 (auto-detected)
[09:55:35:012] [DEBUG] - io.netty.channel.DefaultChannelId.<clinit>(DefaultChannelId.java:101) - -Dio.netty.machineId: 76:f7:10:ff:fe:89:1e:3c (auto-detected)
[09:55:35:039] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:157) - -Dio.netty.allocator.numHeapArenas: 16
[09:55:35:040] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:158) - -Dio.netty.allocator.numDirectArenas: 16
[09:55:35:040] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:160) - -Dio.netty.allocator.pageSize: 8192
[09:55:35:040] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:165) - -Dio.netty.allocator.maxOrder: 9
[09:55:35:040] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:169) - -Dio.netty.allocator.chunkSize: 4194304
[09:55:35:041] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:170) - -Dio.netty.allocator.smallCacheSize: 256
[09:55:35:042] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:171) - -Dio.netty.allocator.normalCacheSize: 64
[09:55:35:042] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:172) - -Dio.netty.allocator.maxCachedBufferCapacity: 32768
[09:55:35:042] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:173) - -Dio.netty.allocator.cacheTrimInterval: 8192
[09:55:35:042] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:174) - -Dio.netty.allocator.cacheTrimIntervalMillis: 0
[09:55:35:043] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:175) - -Dio.netty.allocator.useCacheForAllThreads: false
[09:55:35:043] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:176) - -Dio.netty.allocator.maxCachedByteBuffersPerChunk: 1023
[09:55:35:054] [DEBUG] - io.netty.buffer.ByteBufUtil.<clinit>(ByteBufUtil.java:89) - -Dio.netty.allocator.type: pooled
[09:55:35:055] [DEBUG] - io.netty.buffer.ByteBufUtil.<clinit>(ByteBufUtil.java:98) - -Dio.netty.threadLocalDirectBufferSize: 0
[09:55:35:055] [DEBUG] - io.netty.buffer.ByteBufUtil.<clinit>(ByteBufUtil.java:101) - -Dio.netty.maxThreadLocalCharBufferSize: 16384
[09:55:35:062] [DEBUG] - io.netty.bootstrap.ChannelInitializerExtensions.getExtensions(ChannelInitializerExtensions.java:54) - -Dio.netty.bootstrap.extensions: null
[09:55:35:158] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[09:55:35:158] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[09:55:35:166] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[09:55:35:244] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:96) - -Dio.netty.recycler.maxCapacityPerThread: 4096
[09:55:35:245] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:97) - -Dio.netty.recycler.ratio: 8
[09:55:35:245] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:98) - -Dio.netty.recycler.chunkSize: 32
[09:55:35:246] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:99) - -Dio.netty.recycler.blocking: false
[09:55:35:246] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:100) - -Dio.netty.recycler.batchFastThreadLocalOnly: true
[09:55:35:255] [DEBUG] - io.netty.buffer.AbstractByteBuf.<clinit>(AbstractByteBuf.java:63) - -Dio.netty.buffer.checkAccessible: true
[09:55:35:255] [DEBUG] - io.netty.buffer.AbstractByteBuf.<clinit>(AbstractByteBuf.java:64) - -Dio.netty.buffer.checkBounds: true
[09:55:35:256] [DEBUG] - io.netty.util.ResourceLeakDetectorFactory$DefaultResourceLeakDetectorFactory.newResourceLeakDetector(ResourceLeakDetectorFactory.java:196) - Loaded default ResourceLeakDetector: io.netty.util.ResourceLeakDetector@612a37e7
[09:55:35:345] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connectPubSub$3(ClientConnectionsEntry.java:234) - new pubsub connection created: RedisPubSubConnection@419719498 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7a727f55, L:/192.168.8.183:61399 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@17c3941a[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[09:55:35:341] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1497621981 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6b01af27, L:/192.168.8.183:61401 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@501268b9[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[09:55:35:340] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1968256351 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xfcfd83b9, L:/192.168.8.183:61400 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@6ba95486[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[09:55:35:356] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for 47.111.231.172/47.111.231.172:6379
[09:55:35:371] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[09:55:35:371] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[09:55:35:479] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@483155522 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7c34b00d, L:/192.168.8.183:61402 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@120bb3ab[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[09:55:35:479] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@593676545 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2693fdde, L:/192.168.8.183:61403 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@2da5231d[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[09:55:35:500] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[09:55:35:503] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[09:55:35:586] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1641702055 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x27dc158c, L:/192.168.8.183:61404 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@6f1d90ec[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[09:55:35:583] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@477532454 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xccb42c40, L:/192.168.8.183:61405 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@12b16cd8[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[09:55:35:601] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[09:55:35:601] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[09:55:35:676] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@385431887 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x5f990f89, L:/192.168.8.183:61406 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@183ec5e0[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[09:55:35:676] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@880663859 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x14726fe2, L:/192.168.8.183:61407 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@3aba35cc[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[09:55:35:683] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[09:55:35:684] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[09:55:35:755] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1288582161 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xc927e4be, L:/192.168.8.183:61408 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@4209c3f8[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[09:55:35:756] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@427876348 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7e55193c, L:/192.168.8.183:61409 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@174732bc[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[09:55:35:760] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[09:55:35:761] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[09:55:35:845] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1256262216 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x97c2767d, L:/192.168.8.183:61411 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@4426ed64[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[09:55:35:845] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1593373923 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x766cec9f, L:/192.168.8.183:61410 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@503f014c[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[09:55:35:862] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[09:55:35:864] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[09:55:35:938] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1747717612 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xcd560f0c, L:/192.168.8.183:61413 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@66ebee99[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[09:55:35:948] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@612857137 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x1bee8951, L:/192.168.8.183:61414 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@2a408fd1[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[09:55:35:958] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[09:55:35:958] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[09:55:36:116] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@716193739 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x15b77ccc, L:/192.168.8.183:61416 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@f04813a[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[09:55:36:116] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@383954872 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x19a576c7, L:/192.168.8.183:61415 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@764c699d[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[09:55:36:122] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[09:55:36:122] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[09:55:36:195] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@669374998 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2386bc48, L:/192.168.8.183:61418 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@28a4e6a1[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[09:55:36:208] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[09:55:36:283] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1038102791 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xadfe7ed0, L:/192.168.8.183:61419 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@4c23a7f8[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[09:55:36:303] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[09:55:36:365] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@697999985 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2bb82127, L:/192.168.8.183:61421 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@6c982926[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[09:55:36:375] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@372520242 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xcda55fd6, L:/192.168.8.183:61422 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@5336b990[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[09:55:36:383] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[09:55:36:383] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[09:55:36:451] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@378468950 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x79439fe9, L:/192.168.8.183:61423 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@538c66e3[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[09:55:36:460] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[09:55:36:464] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@372390155 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xa1b4eb5c, L:/192.168.8.183:61424 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@10ba41fc[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[09:55:36:465] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[09:55:36:531] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@469848639 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xbcda18db, L:/192.168.8.183:61426 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@5903cfdb[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[09:55:36:543] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1048950339 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x93fecaa1, L:/192.168.8.183:61427 - R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@7b873ff7[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[09:55:36:544] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for 47.111.231.172/47.111.231.172:6379
[09:55:40:490] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.AccessController:
	{GET [/access/getTokenStr]}: getToken(Integer,HttpServletRequest)
	{GET [/access/getTokenByTime]}: getTokenByTime(Integer,Long,HttpServletRequest)
	{GET [/access/getTokenUnlimit]}: getTokenUnlimit(Integer,HttpServletRequest)
	{GET [/access/heartbeat]}: heartbeat()
[09:55:40:492] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ActivityController:
	{ [/activity/getActiveActivities]}: getActiveActivities(String)
[09:55:40:492] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.AnnualReportController:
	{GET [/annual/getReport]}: getEntity(Integer)
[09:55:40:493] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.AppStartNoticeController:
	{GET [/appStartNotice/getCurrentNoticeOfUser]}: getCurrentNoticeOfUser(String,Integer)
	{GET [/appStartNotice/getCurrentNotice]}: getCurrentNotice(String)
[09:55:40:495] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.AppStartNoticeReadUseController:
	{GET [/appStartNoticeReadUser/save]}: save(Integer,Integer)
[09:55:40:513] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CompanyController:
	{POST [/company/modify]}: modify(Company)
	{POST [/company/createCompany]}: createCompany(JSONObject)
	{GET [/company/getCompanyInfoByUserId]}: getCompanyInfoByUserId(int)
	{POST [/company/createDepartment]}: createDepartment(JSONObject)
	{POST [/company/createJobGrade]}: createJobGrade(JSONObject)
	{POST [/company/createFunctionRole]}: createFunctionRole(JSONObject)
	{GET [/company/deleteCompany]}: deleteCompany(int)
	{GET [/company/deleteDepartment]}: deleteDepartment(int)
	{GET [/company/deleteDepartmentCarefully]}: deleteDepartmentCarefully(Integer,Integer)
	{GET [/company/deleteJobGrade]}: deleteJobGrade(int)
	{GET [/company/deleteJobGradeCarefully]}: deleteJobGradeCarefully(Integer,Integer)
	{GET [/company/deleteFunctionRole]}: deleteFunctionRole(int)
	{GET [/company/deleteFunctionRoleCarefully]}: deleteFunctionRoleCarefully(Integer,Integer)
	{POST [/company/modifyCompany]}: modifyCompany(JSONObject)
	{GET [/company/modifyCompanyName]}: modifyCompanyName(String,Integer)
	{POST [/company/modifyDepartment]}: modifyDepartment(JSONObject)
	{POST [/company/modifyJobGrade]}: modifyJobGrade(JSONObject)
	{POST [/company/modifyFunctionRole]}: modifyFunctionRole(JSONObject)
	{GET [/company/getCompanyInfo]}: getCompanyInfo(Integer)
	{GET [/company/getCompanyInfoById]}: getCompanyInfoWrapperById(int)
	{GET [/company/getCompanyInfoByCodeOrName]}: getCompanyInfoByCodeOrName(String)
	{GET [/company/getCompanyInfoListByName]}: getCompanyInfoListByName(String)
	{GET [/company/getAllDepartmentOfCompany]}: getAllDepartmentOfCompany(Integer,Integer)
	{GET [/company/getNGradeDepartment]}: getLevelDepartmentOfLessThanOrEqualDesignativeGrade(Integer,Integer)
	{GET [/company/getAllDepartmentOfCompanyByUserId]}: getAllDepartmentOfCompanyByUserId(Integer)
	{GET [/company/getSubDepartmentExcludingSelf]}: getSubDepartmentExcludingSelf(String)
	{GET [/company/getSubDepartmentIncludingSelf]}: getSubDepartmentIncludingSelf(String)
	{GET [/company/getAllCompanyJobGradeOfCompany]}: getAllCompanyJobGradeOfCompany(int)
	{GET [/company/getAllCompanyInfo]}: getAllCompanyInfo(int)
	{POST [/company/getQiyeweixinCompanyList]}: getQiyeweixinCompanyList(JSONObject)
	{GET [/company/getAllCompanyInfoWith2Grade]}: getAllCompanyInfoWith2Grade(Integer)
	{GET [/company/getAllCompanyFunctionRoleOfCompany]}: getAllCompanyFunctionRoleOfCompany(int)
	{GET [/company/search]}: searchCompanyByKeyword(String)
	{GET [/company/batchCloseAccount]}: batchCloseAccount(String)
	{GET [/company/closeAccount]}: closeAccount(Integer,Integer)
	{GET [/company/getResidualFlow]}: getResidualFlow(Integer)
	{POST [/company/getOrganizationFrameworkInfoByIds]}: getOrganizationFrameworkInfoByIds(JSONObject)
[09:55:40:521] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CompanyDepartmentController:
	{GET [/companyDepartment/getCompanyDepartmentList]}: getCompanyDepartmentList(Integer,String)
[09:55:40:522] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CompanyGroupUserController:
	{POST [/companyGroupUser/create]}: createCompanyGroupUser(CompanyGroupUser)
	{POST [/companyGroupUser/batchCreate]}: batchCreateCompanyGroupUser(List)
	{POST [/companyGroupUser/update]}: updateCompanyGroupUser(CompanyGroupUser)
	{GET [/companyGroupUser/delete]}: deleteCompanyGroupUser(Integer,String)
	{GET [/companyGroupUser/getGroupsByUserId]}: getGroupsByUserId(Integer)
	{GET [/companyGroupUser/getUsersByGroupId]}: getUsersByGroupId(Integer)
	{GET [/companyGroupUser/checkUserInGroup]}: checkUserInGroup(Integer,Integer)
[09:55:40:524] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CompanyUserGroupController:
	{GET [/companyUserGroup/list]}: getGroupList(Integer,Integer,String,String)
	{GET [/companyUserGroup/detail]}: getGroupDetail(Integer)
	{POST [/companyUserGroup/update]}: updateGroup(JSONObject)
	{GET [/companyUserGroup/delete]}: deleteGroup(Integer)
	{POST [/companyUserGroup/create]}: createGroup(JSONObject)
[09:55:40:525] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentClassificationInfoController:
	{ [/contentClassification/getPracticeContentOfUnlock]}: getPracticeContentOfUnlock()
	{ [/contentClassification/getUnlockedPracticeContentOfUser]}: getUnlockedPracticeContentOfUser(Integer)
	{ [/contentClassification/getPracticeContentOfSubject]}: getPracticeContentOfSubject(Integer,Integer)
[09:55:40:525] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentProductOfUserController:
	{GET [/contentProductOfUser/getProductListOfUser]}: getProductListOfUser(Integer)
[09:55:40:531] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentSubjectCategoryController:
	{GET [/contentSubjectCategory/getCategoryListOfSubject]}: getCategoryListOfSubject(Integer)
	{GET [/contentSubjectCategory/getSubjectListOfCategory]}: getSubjectListOfCategory(Integer)
	{GET [/contentSubjectCategory/getPathListOfSubject]}: getPathListOfSubject(Integer)
	{GET [/contentSubjectCategory/getNLevelCategoryWithContentNum]}: getLevelCategoriesOfLessThanOrEqualDesignativeLevel(String,Integer,Integer)
	{POST [/contentSubjectCategory/update]}: update(JSONArray)
	{GET [/contentSubjectCategory/delete]}: delete(Integer,Integer)
	{POST [/contentSubjectCategory/save]}: save(JSONArray)
[09:55:40:532] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentSubjectController:
	{POST [/contentSubject/createSubject]}: createSubject(JSONObject)
	{GET [/contentSubject/getContentSubjectByContent]}: getContentSubjectByContent(String,Integer)
	{GET [/contentSubject/getAllContentSubject]}: getSubjectInfoList(String,Integer,Integer,Integer)
	{GET [/contentSubject/getSubjectListOfProduct]}: getSubjectListOfProduct(String)
	{GET [/contentSubject/getAllContentSubjectWithUserSelection]}: getAllContentSubjectWithUserSelection(Integer,String)
	{GET [/contentSubject/getSubjectWrapperBySubjectId]}: getSubjectWrapperBySubjectId(Integer)
	{GET [/contentSubject/getSubjectWrapper]}: getSubjectWrapper(Integer)
	{GET [/contentSubject/getSubjectWrapperWithFavoriteInfo]}: getSubjectWrapperWithFavoriteInfo(Integer,Integer)
	{POST [/contentSubject/modifySubject]}: modifySubject(JSONObject)
	{GET [/contentSubject/delete]}: delete(Integer)
[09:55:40:539] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentSubjectOfUserController:
	{POST [/contentSubjectOfUser/saveContentSubjectsOfUser]}: saveContentSubjectsOfUser(JSONObject)
	{GET [/contentSubjectOfUser/getContentSubjectListOfUser]}: getContentSubjectListOfUser(Integer,String,Integer,Integer,Integer)
[09:55:40:543] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentSubjectSummaryController:
	{GET [/contentSubjectSummary/getSummary]}: getSummary(Integer)
[09:55:40:545] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentUnlockController:
	{POST [/contentUnlock/save]}: save(JSONObject)
[09:55:40:546] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CryptographyController:
	{ [/cryptography/encrypt]}: encrypt(String)
	{ [/cryptography/decrypt]}: decrypt(String)
[09:55:40:547] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CustomerRequirementController:
	{POST [/cs/createRequirement]}: save(JSONObject)
[09:55:40:564] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.DataResolverController:
	{GET [/dataResolver/setSelectionChildType]}: setSelectionChildType(int)
	{GET [/dataResolver/reCalculateQuestionScore]}: reCalculateQuestionScore(int,Integer,Integer,String)
	{GET [/dataResolver/reCalculateExaminationInstanceByQuestionId]}: reCalculateExaminationInstance(int,int)
	{GET [/dataResolver/reCalculateExaminationInstance]}: reCalculateExaminationInstance(int,Integer,String)
	{GET [/dataResolver/reCreateCompletionOptions]}: reCreateCompletionOptions(Integer,Integer,Integer,String)
	{GET [/dataResolver/checkDataMigrationFromFreeVersion]}: checkDataMigrationFromFreeVersion(int)
	{GET [/dataResolver/migrateDataFromFreeVersionToEnterpriseVersion]}: migrateDataFromFreeVersionToEnterpriseVersion(int,int)
	{GET [/dataResolver/deleteExaminationIncludingWrongRandomQuestion]}: deleteExaminationIncludingWrongRandomQuestion()
	{GET [/dataResolver/configUserSysUserRole]}: configUserSysUserRole()
	{GET [/dataResolver/recaculateExaminationInstanceBecauseOfNullScore]}: recaculateExaminationInstanceBecauseOfNullScore()
	{GET [/dataResolver/deleteInValidExamination]}: deleteInValidExamination()
	{GET [/dataResolver/deleteExaminationInstanceAndProcessOfEtea]}: deleteExaminationInstanceAndProcessOfEtea(String,String)
	{GET [/dataResolver/deleteInvalidSubscirbeMessage]}: deleteInvalidSubscirbeMessage(String)
	{GET [/dataResolver/clearJSONErrorStr]}: clearJSONErrorStr(Integer)
	{GET [/dataResolver/recaculateExcerciseBookInfo]}: recaculateExcerciseBookInfo()
	{GET [/dataResolver/deleteInvalidQuestion]}: deleteInvalidQuestion()
	{GET [/dataResolver/deleteInvalidPaper]}: deleteInvalidPaper()
	{GET [/dataResolver/deleteExerciseBookAndRelation]}: deleteInvalidExerciseBookAndRelation()
	{GET [/dataResolver/deleteInvalidSystemMessage]}: deleteInvalidSystemMessage(String,String,String)
	{GET [/dataResolver/deleteExaminationInstance]}: deleteExaminationInstance()
	{GET [/dataResolver/handleDeleteQuestionsEvent]}: handleDeleteQuestionsEvent()
	{GET [/dataResolver/restoreExerciseBookByProcess]}: restoreExerciseBookByProcess()
	{GET [/dataResolver/clearRedisData]}: clearRedisData(String)
	{GET [/dataResolver/clearAccountPermently]}: clearAccountPermently(Integer,Integer)
	{GET [/dataResolver/generateQuestionJsonFileOfExamination]}: generateQuestionJsonFileOfExamination(Integer,Integer,Integer)
	{GET [/dataResolver/generateHotFileListOfCDN]}: generateHotFileListOfCDN(Integer,Integer)
	{GET [/dataResolver/resizeOssPicFile]}: resizeOssPicFile(String)
	{GET [/dataResolver/deleteQuestionAndRelatedRecords]}: deleteQuestionAndRelatedRecords(String,String)
	{GET [/dataResolver/deleteWrongQuestionBook]}: deleteWrongQuestionBook(String,String)
	{GET [/dataResolver/transformCompletionQuestion]}: transformCompletionQuestion(Integer,Integer)
	{GET [/dataResolver/restoreExerciseBook]}: restoreExerciseBook(Integer)
	{GET [/dataResolver/replaceDepartmentId]}: replaceDepartmentId(Integer)
	{GET [/dataResolver/generateAnnualReport]}: generateAnnualReport(Integer,Integer,String,String)
	{GET [/dataResolver/insertCompanyUser]}: insertCompanyUser(Integer)
	{GET [/dataResolver/getUnCompleteQuestion]}: getUnCompleteQuestion(Integer)
	{GET [/dataResolver/deleteDepartments]}: deleteDepartments()
	{POST [/dataResolver/processQuestionAndAnswers], consumes [multipart/form-data]}: processQuestionAndAnswers(HttpServletRequest,HttpServletResponse)
	{GET [/dataResolver/deletedRandomRangeQuestionWithQuestionDeletedState]}: deletedRandomRangeQuestionWithQuestionDeletedState(Integer,Integer)
	{GET [/dataResolver/clearExaminationCache]}: reloadExaminationCache(Integer,Integer)
[09:55:40:568] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.DownloadProxyController:
	{POST [/download/proxy]}: proxyDownload(HttpServletRequest,JSONObject)
[09:55:40:575] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationController:
	{GET [/examination/examination]}: getExaminationById(int)
	{POST [/examination/create]}: createExamination(JSONObject)
	{POST [/examination/createExaminationWithContentCheck]}: createExaminationWithContentCheck(JSONObject)
	{POST [/examination/createIncludingExamineeSelect]}: createExaminationIncludingExamineeSelect(JSONObject)
	{POST [/examination/createFixedExamination]}: createFixedExamination(JSONObject)
	{POST [/examination/createRandomExaminationIncludingExamineeSelect]}: createRandomExaminationIncludingExamineeSelect(JSONObject)
	{POST [/examination/createRandomExamination]}: createRandomExamination(JSONObject)
	{GET [/examination/delete]}: deleteExamination(String)
	{GET [/examination/examinationWrapperListOfCreater]}: getExaminationWrapperListOfCreater(int,int,int,int)
	{GET [/examination/examinationInViewListOfCreater]}: getExaminationInViewListOfCreater(Integer,Integer,Integer,Integer)
	{GET [/examination/getExaminationInViewListOfCreater]}: getExaminationInViewList(Integer,Integer,Integer,int)
	{GET [/examination/getExaminationInViewListAndTotalNumOfCreater]}: getExaminationInViewListAndTotalNumOfCreater(int,int,int,int)
	{GET [/examination/getRecommendExaminations]}: getRecommendExaminations(Integer,Integer,Integer,Integer)
	{GET [/examination/getRecommendExaminationsByProduct]}: getRecommendExaminationsByProduct(Integer,String)
	{POST [/examination/getExaminationInViewListOfCreaterInCompanyByTags]}: getExaminationInViewListOfCreaterInCompanyByTags(JSONObject)
	{POST [/examination/getExaminationInViewListOfCompanyByTags]}: getExaminationInViewListOfCompanyByTags(JSONObject)
	{GET [/examination/ongoingExaminationList]}: getOngoingExaminationList()
	{GET [/examination/getExaminationAndPaperById]}: getExaminationAndPaperById(int)
	{POST [/examination/getExaminationAndPaperByColumns]}: getExaminationAndPaperByColumns(JSONObject)
	{POST [/examination/getExaminationAndPaperByColumnsAfterEncode]}: getExaminationAndPaperByColumnsAfterEncode(JSONObject)
	{POST [/examination/getExaminationDetail]}: getExaminationDetail(JSONObject)
	{GET [/examination/examinationByCode]}: getExaminationByCode(String)
	{GET [/examination/getExaminationListByPaperId]}: getExaminationListByPaperId(Integer)
	{GET [/examination/getExaminationListByKeyword]}: getExaminationListByKeyword(String)
	{GET [/examination/getExaminationListOfCreater]}: getExaminationListOfCreater(Integer,Integer,Integer,Integer)
	{GET [/examination/idByCode]}: getExaminationIdByCode(String)
	{POST [/examination/edit]}: editExamination(JSONObject)
	{GET [/examination/examinationWrapper]}: getExaminationWrapperById(Integer)
	{GET [/examination/getExaminationWrapperSecure]}: getExaminationWrapperSecure(Integer,Integer)
	{GET [/examination/getExaminationWrapperStructure]}: getExaminationWrapperStructure(Integer,Integer)
	{POST [/examination/updateExaminationWrapperStructure]}: updateExaminationWrapperStructure(JSONObject)
	{GET [/examination/getExaminationAndPaperQuestionList]}: getExaminationAndPaperQuestionList(Integer)
	{GET [/examination/getExaminationAndPaperQuestionListAfterEncoded]}: getExaminationAndPaperQuestionListAfterEncoded(Integer)
	{GET [/examination/examinationWrapperWithRedLock]}: examinationWrapperWithRedLock(Integer,String)
	{GET [/examination/suspend]}: suspendExamination(Integer)
	{GET [/examination/resume]}: resumeExamination(Integer)
	{GET [/examination/changeExamineeDisplay]}: changeExamineeDisplay(Integer,Boolean)
	{GET [/examination/rename]}: renameExamination(int,int,String)
	{GET [/examination/renameExamCode]}: renameExamCode(Integer,String)
	{GET [/examination/modifyCode]}: modifyCode(Integer,String)
	{GET [/examination/configAdvancedOptions]}: configAdvancedOptions(String)
	{POST [/examination/saveAdvancedOptions]}: saveAdvancedOptions(JSONObject)
	{GET [/examination/checkIfExceedExaminationTimesLimit]}: checkIfExceedExaminationTimesLimit(Integer,Integer)
	{GET [/examination/copy]}: copyExamination(Integer,Integer,Boolean)
	{GET [/examination/transmit]}: transmitExamination(Integer,Integer,Integer)
	{GET [/examination/getLeftTime]}: getLeftFromBeginTime(Integer)
	{POST [/examination/sendQiyeweixinQkkNotice]}: sendQiyeweixinQkkNotice(JSONObject)
	{POST [/examination/update]}: update(Examination)
	{GET [/examination/reset]}: reset(Integer)
[09:55:40:582] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationExamineeController:
	{GET [/examinationExaminee/getExaminationExaminee]}: getExaminationExaminee(Integer)
	{POST [/examinationExaminee/updateExaminationExaminee]}: updateExaminationExaminee(JSONObject)
[09:55:40:586] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationExamineeSnapshotController:
	{GET [/examinationExamineeSnapshot/insertSnapshot]}: insertSnapshot(Integer,Integer,String,String)
	{GET [/examinationExamineeSnapshot/getUserSnapshotListOfExamination]}: getUserSnapshotListOfExamination(Integer,String,Integer,Integer)
	{GET [/examinationExamineeSnapshot/getUserSnapshotList]}: getUserSnapshotList(Integer,String,Integer,Integer)
	{GET [/examinationExamineeSnapshot/exportSnapshotListOfExamination]}: exportSnapshotListOfExamination(Integer)
	{GET [/examinationExamineeSnapshot/delete]}: delete(String)
[09:55:40:588] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationFavoriteController:
	{GET [/examinationFavorite/add]}: addFavorite(int,int)
	{GET [/examinationFavorite/cancel]}: cancelFavorite(int,int)
	{GET [/examinationFavorite/ifFavorite]}: ifFavorite(int,int)
	{GET [/examinationFavorite/getFavoriteList]}: getFavoriteList(int,int,int)
	{GET [/examinationFavorite/getFavoriteListAndNum]}: getFavoriteListAndNum(int,int,int)
[09:55:40:588] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationGroupExaminationController:
	{ [/examinationGroupExamination/list]}: getExaminationGroupExaminationServiceListById(int)
[09:55:40:597] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationInstanceController:
	{POST [/examinationInstance/getExaminationInstanceList]}: getExaminationInstanceList(JSONObject)
	{[GET, POST] [/examinationInstance/exportRankListOfExaminationInCompany]}: exportRankListOfExaminationInCompany(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/examinationInstance/exportNoRankListOfExaminationInCompany]}: exportNoRankListOfExaminationInCompany(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/examinationInstance/exportDetailRankListOfExamination]}: exportDetailRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{GET [/examinationInstance/getExaminationInstanceWrapperById]}: getExaminationInstanceWrapperById(Integer)
	{POST [/examinationInstance/createExaminationInstance]}: createExaminationInstance(JSONObject,HttpServletRequest)
	{POST [/examinationInstance/createExaminationInstanceWithContentCheck]}: createExaminationInstanceWithContentCheck(JSONObject,HttpServletRequest,HttpServletResponse)
	{POST [/examinationInstance/getExaminationInstanceWrapperListOfExaminee]}: getExaminationInstanceWrapperListOfExaminee(JSONObject)
	{GET [/examinationInstance/examinationInstanceWrapperListOfExaminee]}: getExaminationInstanceWrapperListOfExaminee(int,int,int,int)
	{ [/examinationInstance/getExaminationInstanceInfoById]}: getExaminationInstanceInfoById(Integer)
	{POST [/examinationInstance/getRecentExaminationInstanceListWithAverage]}: getRecentExaminationInstanceListWithAverage(JSONObject)
	{GET [/examinationInstance/getExaminationInstanceListWithUserInfoByExaminationId]}: getExaminationInstanceListWithUserInfoByExaminationId(int,int,int,int)
	{GET [/examinationInstance/examinationInstanceWrapperListOfExamination]}: getExaminationInstanceWrapperListOfExamination(String,int,Integer,Integer)
	{GET [/examinationInstance/rankOfExaminationInstance]}: getRankOfExaminationInstance(int,int,int)
	{GET [/examinationInstance/rankListOfExamination]}: getRankListOfExamination(Integer,Integer,Integer,Integer,Integer)
	{GET [/examinationInstance/getTopTenRankListAndTotalExamineeNumOfExamination]}: getTopTenRankListAndTotalExamineeNumOfExamination(int)
	{GET [/examinationInstance/getLatestTenListAndTotalExamineeNumOfExamination]}: getLatestTenListAndTotalExamineeNumOfExamination(Integer)
	{GET [/examinationInstance/rankListOfExaminationByCode]}: getRankListOfExaminationByCode(String)
	{GET [/examinationInstance/getTotalTimes]}: getTotalTimes(Integer,Integer)
	{GET [/examinationInstance/timesOfExaminationOfExaminee]}: getTimesOfExaminationOfExaminee(int,int,String,String)
	{POST [/examinationInstance/batchDelete]}: batchDeleteByExaminee(JSONArray)
	{POST [/examinationInstance/batchDeleteByAdmin]}: batchDeleteByAdmin(JSONArray)
	{GET [/examinationInstance/examinationInstanceStageSummary]}: getExaminationInstanceStageSummary(int,String,String)
	{POST [/examinationInstance/getExaminationInstanceStageSummaryListByTags]}: getExaminationInstanceStageSummaryList(JSONObject)
	{GET [/examinationInstance/examinationInstanceStageSummaryList]}: getExaminationInstanceStageSummaryList(String,String,String,int,int,int)
	{GET [/examinationInstance/examinationInstanceStageSummaryListExcludingExample]}: getExaminationInstanceStageSummaryListExcludingExample(String,String,String,int,int,int)
	{GET [/examinationInstance/getExaminationInstanceStageSummaryListExcludingCreater]}: getExaminationInstanceStageSummaryListExcludingCreater(String,String,String,int,int,int)
	{GET [/examinationInstance/examinationInstanceStageSummaryDetail]}: getExaminationInstanceStageSummaryDetail(int,String,String,Integer,Integer,String)
	{POST [/examinationInstance/getExaminationStageSummaryDetail]}: getExaminationStageSummaryDetail(JSONObject)
	{GET [/examinationInstance/individualScoreListOfExamination]}: getIndividualScoreListOfExamination(int,int,String,String)
	{GET [/examinationInstance/individualScoreList]}: getIndividualScoreList(int,String,String,int)
	{GET [/examinationInstance/getIndividualRankInfo]}: getIndividualRankInfo(Integer,Integer,Integer)
	{GET [/examinationInstance/getIndividualRankInfoWithDuplicateRemoval]}: getIndividualRankInfoWithDuplicateRemoval(Integer,Integer,Integer)
	{GET [/examinationInstance/getExaminationInstanceWrapperAndProcessById]}: getExaminationInstanceWrapperAndProcessById(Integer)
	{POST [/examinationInstance/getInstanceWrapperAndProcessList]}: getInstanceWrapperAndProcessList(JSONObject)
	{GET [/examinationInstance/notApprovedExaminationInstanceList]}: getNotApprovedExaminationInstanceList(Integer,Integer,Integer,Integer)
	{GET [/examinationInstance/getNotApprovedExaminationInstanceListAndLength]}: getNotApprovedExaminationInstanceListAndLength(Integer,Integer,Integer,Integer)
	{POST [/examinationInstance/approveInstance]}: approveInstance(JSONObject)
	{[GET, POST] [/examinationInstance/exportNoRankListOfExamination]}: exportNoRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/examinationInstance/exportRankListOfExamination]}: exportRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{POST [/examinationInstance/getCountResult]}: getCountResult(JSONObject)
	{[GET, POST] [/examinationInstance/exportDetailRankListOfExaminationInCompany]}: exportDetailRankListOfExaminationInCompany(HttpServletRequest,HttpServletResponse)
	{ [/examinationInstance/exportInstanceOfExaminee]}: exportInstanceOfExaminee(HttpServletRequest,HttpServletResponse)
	{GET [/examinationInstance/exportAbsentList]}: exportAbsentList(HttpServletRequest,HttpServletResponse)
	{GET [/examinationInstance/getAbsentList]}: getAbsentList(Integer)
	{GET [/examinationInstance/getUserListOfExamination]}: getUserListOfExamination(Integer)
	{GET [/examinationInstance/getExaminationAnalysisData]}: getAnalysisData(Integer)
	{GET [/examinationInstance/getDistributionOfScore]}: getDistributionOfScore(Integer)
	{GET [/examinationInstance/getDistributionOfDuration]}: getDistributionOfDuration(Integer)
	{POST [/examinationInstance/getExamineeList]}: getExamineeList(JSONObject)
	{GET [/examinationInstance/delete]}: delete(int,String)
	{POST [/examinationInstance/create]}: create(JSONObject,HttpServletRequest,HttpServletResponse)
[09:55:40:600] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationInstanceEventController:
	{POST [/examinationInstanceEvent/getList]}: getList(JSONObject)
	{POST [/examinationInstanceEvent/getSummaryInfo]}: getSummaryInfo(JSONObject)
	{POST [/examinationInstanceEvent/getExamBehaviorList]}: getExamBehaviorList(JSONObject)
	{POST [/examinationInstanceEvent/insert]}: insert(ExaminationInstanceEvent)
[09:55:40:607] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationInstanceProcessController:
	{GET [/examinationInstanceProcess/examinationInstanceProcessWrapperList]}: getExaminationInstanceProcessWrapperList(int,Integer,Integer,Integer)
	{GET [/examinationInstanceProcess/examinationInstanceProcessWrapperListAndNum]}: examinationInstanceProcessWrapperListAndNum(Integer,Integer,Integer,Integer)
	{GET [/examinationInstanceProcess/wrongQuestionList]}: getWrongQuestionsOfExaminee(int,int,int)
	{GET [/examinationInstanceProcess/rightWrongNumOfExamination]}: getQuestionRightWrongNumOfExamination(int)
	{GET [/examinationInstanceProcess/rightWrongNumOfQuestion]}: getQuestionRightWrongNum(int)
	{GET [/examinationInstanceProcess/getDistributionGroupByAnswerContent]}: getDistributionGroupByAnswerContent(Integer,Integer)
	{GET [/examinationInstanceProcess/getAnalysisOfExamination]}: getAnalysisOfExamination(Integer,Integer,Integer,String)
	{POST [/examinationInstanceProcess/changeQuestionResult]}: changeQuestionResult(JSONObject)
	{GET [/examinationInstanceProcess/exportQuestionAnalysis]}: exportQuestionAnalysis(Integer,HttpServletResponse)
	{POST [/examinationInstanceProcess/performAiScoring]}: performAiScoring(JSONObject)
[09:55:40:613] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationNameListController:
	{GET [/examinationNameList/getList]}: getList(Integer,Integer)
	{POST [/examinationNameList/upInsert]}: upInsert(JSONObject)
	{POST [/examinationNameList/addExaminationNameList]}: addExaminationNameList(JSONObject)
[09:55:40:619] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationStartUpController:
	{GET [/examinationStartUp/getCountGroupByUser]}: getCountGroupByUser(Integer,Integer,Integer)
	{GET [/examinationStartUp/getCount]}: getCount(Integer,Integer)
	{GET [/examinationStartUp/add]}: add(Integer,Integer)
	{GET [/examinationStartUp/delete]}: delete(Integer,Integer)
[09:55:40:621] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationTagsController:
	{GET [/examinationTags/getAllExamTagsOfCreaterIdInCompany]}: getAllExamTagsOfCreaterIdInCompany(Integer,Integer)
	{GET [/examinationTags/getAllExamTagsExcludingCreaterIdInCompany]}: getAllExamTagsExcludingCreaterIdInCompany(int,int)
	{ [/examinationTags/getAllExamTagsOfCompanyId]}: getAllExamTagsOfCompanyId(int,Boolean)
	{ [/examinationTags/getAllExamTagsOfUserInCompany]}: getAllExamTagsOfUserInCompany(Integer,Integer)
	{ [/examinationTags/getTagsOfExam]}: getTagsOfExam(int)
	{POST [/examinationTags/createrExamTags]}: createExamTags(JSONObject)
[09:55:40:623] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationWxGidController:
	{POST [/examinationWxGid/addIfNoExist]}: addEntity(ExaminationWxGid)
	{GET [/examinationWxGid/getList]}: getList(Integer)
	{GET [/examinationWxGid/getEntity]}: getEntity(Integer,String)
	{GET [/examinationWxGid/delete]}: delete(Integer,String)
[09:55:40:631] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookController:
	{POST [/exerciseBook/batchUpdate]}: batchUpdate(JSONArray)
	{POST [/exerciseBook/create]}: createExerciseBook(JSONObject)
	{POST [/exerciseBook/createExerciseBookByQuestionTypeAndCategories]}: createExerciseBookByQuestionTypeAndCategories(JSONObject)
	{GET [/exerciseBook/modifyExerciseBook]}: modifyExerciseBook(String,String)
	{GET [/exerciseBook/delete]}: deleteExerciseBook(int)
	{GET [/exerciseBook/deleteExerciseBookAndRelation]}: deleteExerciseBookAndRelation(Integer)
	{GET [/exerciseBook/getRecommendedKeyWords]}: getRecommendedKeyWords()
	{ [/exerciseBook/exerciseBookListOfCreaterIncludingFavorite]}: getExerciseBookListOfCreaterIncludingFavorite(int,int,int,int,int)
	{ [/exerciseBook/exerciseBook]}: getExerciseBookById(int)
	{GET [/exerciseBook/getExampleQuestions]}: getExampleQuestions(Integer)
	{ [/exerciseBook/exerciseBookBeginInfo]}: getExerciseBookBeginInfoById(Integer,Integer)
	{GET [/exerciseBook/getExerciseBookBeginInfoById]}: getKsiteExerciseBookBeginInfoById(Integer,Integer,Integer)
	{GET [/exerciseBook/getExerciseBookIndexPageInfo]}: getExerciseBookIndexPageInfo(Integer)
	{GET [/exerciseBook/getPracticeSummaryInfo]}: getPracticeSummaryInfo(Integer)
	{ [/exerciseBook/exerciseBookWrapper]}: getExerciseBookWrapperById(String)
	{ [/exerciseBook/switchDisplayToExaminee]}: switchDisplayToExaminee(Integer,Boolean)
	{GET [/exerciseBook/advancedExerciseBookWrapper]}: getAdvancedExerciseBookWrapperById(int)
	{POST [/exerciseBook/doExercise]}: doExercise(JSONObject)
	{POST [/exerciseBook/doExerciseSecure]}: doExerciseSecure(JSONObject)
	{GET [/exerciseBook/rename]}: renameExerciseBook(String,int)
	{POST [/exerciseBook/savePracticeSummary]}: savePracticeSummary(JSONObject)
	{GET [/exerciseBook/getCatagoriesAndNum]}: getCatagoriesAndNum(Integer,Integer,String)
	{GET [/exerciseBook/getTagsGroupByDomain]}: getTagsGroupByDomain(Integer,Integer,String)
	{GET [/exerciseBook/getExerciseBookListByCatagory]}: getExerciseBookListByCatagory(Integer,Integer,String,String,String,Integer,Integer)
	{GET [/exerciseBook/getExerciseBookListAndTotalNum]}: getExerciseBookListAndTotalNum(Integer,Integer,String,String,String,Integer,Integer)
	{GET [/exerciseBook/getDailyPracticeResult]}: getDailyPracticeResult(Integer,Integer,Date,Date)
	{GET [/exerciseBook/getRecommendedList]}: getRecommendedList(Integer,Integer,Integer)
	{GET [/exerciseBook/getFavoriteRankList]}: getFavoriteRankList(String,Integer,Integer,Integer,Integer)
	{POST [/exerciseBook/getExerciseBookListOfCreater]}: getExerciseBookListOfCreater(JSONObject)
	{GET [/exerciseBook/exerciseBookListOfCreater]}: getExerciseBookListOfCreater(Integer,String,String,String,Integer,Integer,Integer)
	{ [/exerciseBook/getExerciseBookList]}: getExerciseBookList(Integer,String,String,String,Integer,Integer,Integer)
	{POST [/exerciseBook/sendQiyeweixinQkkNotice]}: sendQiyeweixinQkkNotice(JSONObject)
	{POST [/exerciseBook/update]}: update(ExerciseBook)
[09:55:40:633] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookExamineeController:
	{GET [/exerciseBookExaminee/getExerciseBookExaminee]}: getExerciseBookExaminee(Integer)
	{POST [/exerciseBookExaminee/updateExerciseBookExaminee]}: updateExerciseBookExaminee(JSONObject)
[09:55:40:634] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookFavorateController:
	{ [/exerciseBookFavorate/addFavorite]}: addInFavoriteList(int,int)
	{ [/exerciseBookFavorate/addBatchFavorite]}: addBatchFavorite(String,int)
	{ [/exerciseBookFavorate/cancelFavorite]}: deleteFavoriteOfUser(Integer,Integer)
	{GET [/exerciseBookFavorate/myFavoriteExerciseBookPage]}: getMyFavoriteExerciseBookPageInfo(Integer,Integer)
	{GET [/exerciseBookFavorate/getMyFavoriteInfo]}: getMyFavoriteInfo(Integer,Integer)
	{ [/exerciseBookFavorate/getSomeoneExerciseBookAndUserPracticeInfo]}: getSomeoneExerciseBookAndUserPracticeInfo(int,int)
	{ [/exerciseBookFavorate/getCompanyExerciseBookAndUserPracticeInfo]}: getCompanyExerciseBookAndUserPracticeInfo(int,int)
	{POST [/exerciseBookFavorate/getCompanyExamineePracticeInfo]}: getCompanyExamineePracticeInfo(JSONObject)
	{GET [/exerciseBookFavorate/getCompanyPracticeSummary]}: getCompanyPracticeSummary(Integer,Integer)
	{ [/exerciseBookFavorate/getEntity]}: getEntity(Integer,Integer)
[09:55:40:635] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookGradeController:
	{POST [/exerciseBookGrade/add]}: add(ExerciseBookGrade)
[09:55:40:635] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookPracticeProcessController:
	{GET [/exerciseBookPracticeProcess/getRankOfInterval]}: getRankOfInterval(Integer,Integer,Integer,String,String,Integer,Integer)
	{GET [/exerciseBookPracticeProcess/getSequenceNoInRankOfInterval]}: getSequenceNoInRankOfInterval(Integer,Integer,Integer,Integer,String,String)
	{GET [/exerciseBookPracticeProcess/getRankInfo]}: getRankInfo(Integer,Integer,Integer,Integer,String,String,Integer,Integer)
[09:55:40:636] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookPracticeSummaryController:
	{GET [/exerciseBookPracticeSummary/getRankOfInterval]}: getRankOfInterval(Integer,Integer,Integer,String,String,Integer,Integer)
	{GET [/exerciseBookPracticeSummary/getSequenceNoInRankOfInterval]}: getSequenceNoInRankOfInterval(Integer,Integer,Integer,Integer,String,String)
	{GET [/exerciseBookPracticeSummary/getRankInfo]}: getRankInfo(Integer,Integer,Integer,Integer,String,String,Integer,Integer)
	{GET [/exerciseBookPracticeSummary/getUserPracticeSummary]}: getUserPracticeSummary(Integer,Integer,String,String)
	{POST [/exerciseBookPracticeSummary/getUserPracticeListWithSummary]}: getUserPracticeListWithSummary(JSONObject)
	{POST [/exerciseBookPracticeSummary/getDepartmentPracticeStatistics]}: getDepartmentPracticeStatistics(JSONObject)
	{GET [/exerciseBookPracticeSummary/exportUserPracticeSummary]}: exportUserPracticeSummary(HttpServletRequest,HttpServletResponse)
[09:55:40:638] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookPraiseController:
	{ [/exerciseBookPraise/add]}: addPraise(int,int)
	{ [/exerciseBookPraise/cancel]}: cancelPraise(int,int)
[09:55:40:639] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookQuestionController:
	{GET [/exerciseBookQuestion/exerciseBookQuestionWrapperList]}: getExerciseBookQuestionsById(int,Integer,Integer)
	{GET [/exerciseBookQuestion/getExerciseBookQuestionWrapperListWithRedLock]}: getExerciseBookQuestionWrapperList(Integer,Integer,Integer)
	{GET [/exerciseBookQuestion/exerciseBookQuestionWrapperListAndTotalNum]}: getExerciseBookQuestionsAndTotalNumById(Integer,Integer,Integer)
	{GET [/exerciseBookQuestion/getDayDayExerciseQuestionList]}: getDayDayExerciseQuestionList(Integer,Integer,Integer,Boolean)
[09:55:40:640] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookSummaryController:
	{GET [/exerciseBookSummary/getSummary]}: getSummary(Integer)
[09:55:40:640] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookUserRecordController:
	{GET [/exerciseBookUserRecord/updateHavePractisedNum]}: updateHavePractisedNum(Integer,Integer)
	{ [/exerciseBookUserRecord/getExerciseProgress]}: getExerciseBookProgressOfUser(int,int)
	{GET [/exerciseBookUserRecord/exportUserPracticeSummary]}: exportUserPracticeSummary(Integer,HttpServletResponse)
[09:55:40:640] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.FileAccessPermissionController:
	{POST [/fileAccessPermission/generateUnlockAdQRCode]}: generateUnlockAdQRCode(JSONObject)
	{POST [/fileAccessPermission/addFileAccessPermission]}: addFileAccessPermission(JSONObject)
	{POST [/fileAccessPermission/getFileAccessPermission]}: getFileAccessPermission(JSONObject)
[09:55:40:640] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.FileController:
	{[GET, POST] [/file/uploadFile]}: uploadFile(HttpServletRequest)
	{[GET, POST] [/file/uploadFileWithCheck]}: uploadFileWithCheck(HttpServletRequest)
	{ [/file/downloadFile]}: downLoad(String,HttpServletResponse,boolean)
	{GET [/file/downloadNetFile]}: downloadNetFile(HttpServletRequest,HttpServletResponse)
	{POST [/file/uploadOssFile]}: uploadOssFile(HttpServletRequest,HttpServletResponse)
[09:55:40:641] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.FreePracticeOfUserController:
	{POST [/freePracticeOfUser/getFreePractice]}: getFreePractice(JSONObject)
	{GET [/freePracticeOfUser/getQuestionListOfFreePractice]}: getQuestionListOfFreePractice(Integer,Integer)
	{GET [/freePracticeOfUser/getSummaryGroupByQuestionType]}: getSummaryGroupByQuestionType(Integer,Integer)
	{GET [/freePracticeOfUser/delete]}: delete(Integer,Integer)
	{POST [/freePracticeOfUser/save]}: save(JSONObject)
[09:55:40:641] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.GameController:
	{GET [/game/getGameWrapper]}: getGameWrapper(Integer)
	{POST [/game/updateInsert]}: updateInsert(JSONObject)
[09:55:40:641] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.GameResultController:
	{GET [/gameResult/getRankInfo]}: getRankInfo(String,Integer,Integer)
[09:55:40:642] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.HomepageUserFollowedController:
	{GET [/homepageUserFollowed/getUserFollowedInfo]}: getUserFollowedInfo(Integer,Integer)
	{GET [/homepageUserFollowed/getUserFollowedList]}: getUserFollowedList(Integer)
	{POST [/homepageUserFollowed/update]}: update(HomepageUserFollowedWrapper)
	{GET [/homepageUserFollowed/delete]}: delete(Integer,Integer)
	{POST [/homepageUserFollowed/save]}: save(HomepageUserFollowedWrapper)
[09:55:40:648] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.InviteRelationController:
	{GET [/inviteRelation/saveInviteRelation]}: saveInviteRelation(int,int,int)
	{GET [/inviteRelation/getRecentInviteRelationAnnouncement]}: getRecentInviteRelationAnnouncement(int)
	{GET [/inviteRelation/getExaminationInstanceListOfInvitee]}: getExaminationInstanceListOfInvitee(int,int,int,int,int)
	{GET [/inviteRelation/getEventResultOfInviter]}: getEventResultOfInviter(int,int,int)
	{GET [/inviteRelation/getResultOfEvent]}: getResultOfEvent(int,int,int,int,int)
	{[GET, POST] [/inviteRelation/exportRankListOfEvent]}: exportRankListOfExamination(HttpServletRequest,HttpServletResponse)
[09:55:40:649] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ManualServiceController:
	{POST [/manualService/addManualImportRecord]}: addManualImportRecord(JSONObject)
	{POST [/manualService/getManualImportRecords]}: getManualImportRecords(JSONObject)
	{POST [/manualService/updateManualImportRecord]}: updateManualImportRecord(ManualServiceRecord)
[09:55:40:650] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MessageController:
	{GET [/message/sendMessage]}: sendMessage(String)
[09:55:40:650] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MessagePushSubscriptionController:
	{POST [/messagePushSubscription/saveIfNotExisted]}: saveIfNotExisted(MessagePushSubscription)
	{POST [/messagePushSubscription/getList]}: getList(JSONObject)
	{GET [/messagePushSubscription/deleteEntity]}: deleteEntity(String,Integer,Integer)
	{POST [/messagePushSubscription/changeBatch]}: changeBatch(JSONObject)
	{GET [/messagePushSubscription/getEntity]}: getEntity(String,Integer,Integer)
[09:55:40:652] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MessageScheduleController:
	{GET [/messageSchedule/sendMessage]}: sendMessage(String)
	{GET [/messageSchedule/getList]}: getList(String,Integer,Integer)
	{POST [/messageSchedule/deleSave]}: deleSave(JSONObject)
	{POST [/messageSchedule/update]}: updateIgnoreNull(MessageSchedule)
[09:55:40:653] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MonitorViewController:
	{GET [/view/{sessionId}]}: viewMonitor(String,Model)
[09:55:40:654] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MultipleMarkingController:
	{GET [/multipleMarking/acceptMultipleMarking]}: acceptMultipleMarking(Integer,Integer)
	{GET [/multipleMarking/getMultipleMarkingExamintionListOfUser]}: getMultipleMarkingExamintionListOfUser(Integer)
	{GET [/multipleMarking/hasMultipleMarkingExaminationInstanceByExaminationInstanceId]}: hasMultipleMarkingExaminationInstanceByExaminationInstanceId(Integer)
	{GET [/multipleMarking/getWorkStateOfMarking]}: getWorkStateOfMarking(Integer,Integer)
	{GET [/multipleMarking/finishMarking]}: finishMarking(Integer)
[09:55:40:656] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.NameListController:
	{GET [/nameList/getList]}: getList(Integer)
	{GET [/nameList/getEntityWrapper]}: getEntityWrapper(Integer)
	{GET [/nameList/getEntity]}: getEntity(Integer)
	{POST [/nameList/update]}: update(JSONObject)
	{GET [/nameList/delete]}: delete(Integer)
	{POST [/nameList/save]}: save(JSONObject)
[09:55:40:656] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.NanXiangController:
	{POST [/nanxiang/getEntity]}: getEntity(NanxiangRegister)
	{POST [/nanxiang/validate]}: validate(NanxiangRegister)
	{GET [/nanxiang/check]}: check(String)
	{POST [/nanxiang/save]}: save(NanxiangRegister)
[09:55:40:661] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.OrderController:
	{GET [/order/getList]}: getList(String,String,Integer,Integer,Integer)
	{POST [/order/createManualOrder]}: createManualOrder(JSONObject)
	{ [/order/create]}: createOrder(JSONObject)
	{ [/order/createCountOrder]}: createCountOrder(JSONObject)
	{POST [/order/createCountOrderByWeChatNative]}: createOrderByWeChatNative(JSONObject)
	{POST [/order/createCountOrderByJSAPI]}: createCountOrderByJSAPI(JSONObject)
	{POST [/order/orderNotify]}: orderNotify(JSONObject)
	{ [/order/getUnconsumedValueOfAccount]}: getUnconsumedValueOfAccount(String,Integer)
	{GET [/order/getOrderById]}: getOrderById(Integer)
[09:55:40:674] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperController:
	{POST [/paper/batchUpdate]}: batchUpdate(JSONArray)
	{GET [/paper/getPaperById]}: getPaperById(Integer)
	{GET [/paper/getPaperWrapperWithRedLock]}: getPaperWrapperWithRedLock(Integer)
	{ [/paper/modifyPaper]}: modifyPaper(String,String)
	{POST [/paper/update]}: updatePaper(Paper)
	{ [/paper/paperListOfCreater]}: getPaperListOfCreater(String,int)
	{GET [/paper/getPaperWithFavoriteInfoAndOrderRelationInfo]}: getPaperWithFavoriteInfoAndOrderRelationInfo(Integer,Integer,Integer)
	{ [/paper/paperWrapper]}: getPaperWrapperById(String)
	{ [/paper/advancedPaperWrapper]}: getAdvancedPaperWrapperById(int)
	{ [/paper/paperListToAdmin]}: getPaperListToAdmin(String,String,int,int)
	{GET [/paper/getCategoriesAndNum]}: getCategoriesAndNum(Integer,Integer,String)
	{GET [/paper/getPaperList]}: getPaperList(Integer,Integer,String,String,String,Integer,Integer)
	{GET [/paper/getPaperListAndTotalNum]}: getPaperListAndTotalNum(Integer,Integer,String,String,String,Integer,Integer)
	{ [/paper/create]}: create(JSONObject)
[09:55:40:675] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperFavoriteController:
	{ [/paperFavorite/add]}: addFavorite(int,int)
	{ [/paperFavorite/cancel]}: cancelFavorite(int,int)
	{ [/paperFavorite/ifFavorite]}: ifFavorite(int,int)
	{ [/paperFavorite/getFavoriteList]}: getFavoriteList(int,int,int)
[09:55:40:675] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperPraiseController:
	{ [/paperPraise/add]}: addPraise(Integer,Integer)
	{ [/paperPraise/cancel]}: cancelPraise(Integer,Integer)
[09:55:40:676] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperQuestionController:
	{ [/paperQuestion/update]}: updateCompletion(BigDecimal,BigDecimal,int,BigDecimal,String,int)
	{ [/paperQuestion/updateSelection]}: updateSelection(BigDecimal,BigDecimal,int,BigDecimal,String,int)
	{GET [/paperQuestion/getQuestionWrapperListAndNum]}: getQuestionWrapperListAndNum(Integer,Integer,Integer)
[09:55:40:676] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperSummaryController:
	{GET [/paperSummary/getSummary]}: getSummary(Integer)
[09:55:40:676] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PassRaceGameController:
	{GET [/passRaceGame/getGameWrapper]}: getGameWrapper(Integer)
	{GET [/passRaceGame/getListAndTotalNum]}: getListAndTotalNum(Integer,Integer,Integer)
	{POST [/passRaceGame/update]}: update(JSONObject)
	{GET [/passRaceGame/delete]}: delete(Integer)
	{POST [/passRaceGame/save]}: save(JSONObject)
[09:55:40:682] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PassRaceResultController:
	{POST [/passRaceResult/save]}: save(JSONObject)
	{GET [/passRaceResult/getGameWrapper]}: getGameWrapper(Integer)
	{GET [/passRaceResult/getListAndTotalNum]}: getListAndTotalNum(Integer,Integer,Integer)
	{POST [/passRaceResult/update]}: update(JSONObject)
	{GET [/passRaceResult/delete]}: delete(Integer)
[09:55:40:686] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PassRaceStageController:
	{GET [/passRaceStage/getStageWrapper]}: getStageWrapper(Integer,Integer)
	{GET [/passRaceStage/getMyPlayedStageList]}: getMyPlayedStageList(Integer,Integer,Boolean)
[09:55:40:688] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PdfFileController:
	{ [/pdfFile/temporaryExaminationReport]}: createFreeExaminationReport(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/pdfFile/examinationReport]}: createExaminationPdfReport(HttpServletRequest,HttpServletResponse)
	{GET [/pdfFile/generatePdfByUser]}: generatePdfByUser(Integer,Integer)
[09:55:40:688] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PermissionAuthorizationController:
	{GET [/permissionAuthorization/getList]}: getList(Integer,Integer)
	{POST [/permissionAuthorization/getAuthorizedDepartmentList]}: getAuthorizedDepartmentList(JSONObject)
	{POST [/permissionAuthorization/getAuthorizedAdminList]}: getAuthorizedAdminList(JSONObject)
	{POST [/permissionAuthorization/getAuthorizedCompanyInfoWith2Grade]}: getAuthorizedCompanyInfoWithRecursionDepartment(JSONObject)
	{GET [/permissionAuthorization/getAuthorizedCompanyPracticeSummary]}: getAuthorizedCompanyPracticeSummary(Integer,Integer)
	{POST [/permissionAuthorization/getAuthorizedCompanyExamineePracticeInfo]}: getAuthorizedCompanyExamineePracticeInfo(JSONObject)
	{POST [/permissionAuthorization/changeBatch]}: changeBatch(JSONObject)
	{GET [/permissionAuthorization/getEntity]}: getEntity(Integer,Integer,Integer)
	{POST [/permissionAuthorization/save]}: save(List)
[09:55:40:689] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PermissionController:
	{GET [/permission/getList]}: getList()
[09:55:40:689] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PointConsumerationController:
	{POST [/pointConsumeration/getUserConsumeList]}: getUserConsumeList(JSONObject)
	{POST [/pointConsumeration/save]}: save(PointConsumeration)
[09:55:40:690] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PointDetailController:
	{GET [/pointDetail/getRankInfo]}: getRankInfo(Integer,Integer,String,String,String,Integer,Integer)
	{GET [/pointDetail/exportPointRankOfCompany]}: exportPointRankOfCompany(HttpServletRequest,HttpServletResponse)
	{POST [/pointDetail/getCompanyRankInfo]}: getCompanyRankInfo(JSONObject)
[09:55:40:690] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PointRuleController:
	{POST [/pointRule/upInsert]}: upInsert(PointRule)
	{GET [/pointRule/getEntity]}: getEntity(Integer)
[09:55:40:691] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PointUserController:
	{GET [/pointUser/getUserPointInfo]}: getUserPointInfo(Integer,Integer)
	{GET [/pointUser/clear]}: clear(Integer,Integer,Integer)
[09:55:40:691] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PracticePushController:
	{GET [/practicePush/list]}: getList(Integer,Integer,String,Integer,Integer,Integer)
	{GET [/practicePush/toggleStatus]}: toggleStatus(Integer,Integer,Integer)
	{GET [/practicePush/detail]}: getDetail(Integer,Integer)
	{GET [/practicePush/checkAndExecutePushTasks]}: checkAndExecutePushTasks()
	{POST [/practicePush/update]}: update(JSONObject)
	{GET [/practicePush/delete]}: delete(Integer,Integer)
	{POST [/practicePush/create]}: create(JSONObject)
[09:55:40:691] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductController:
	{ [/product/getProductList]}: getProductList(String)
	{GET [/product/getListOfProduct]}: getListOfProduct(String)
[09:55:40:692] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductCountTransactionController:
	{POST [/productCountTransaction/getList]}: getList(JSONObject)
[09:55:40:692] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductIntroductionController:
	{ [/productIntroduction/list]}: getProductIntroductionWrapperList()
[09:55:40:692] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductOrderRelationController:
	{GET [/productOrderRelation/getProductOrderRelation]}: getProductOrderRelation(String,Integer)
	{GET [/productOrderRelation/getProductOrderRelationWrapper]}: getProductOrderRelationWrapper(String,Integer)
	{GET [/productOrderRelation/checkIfOverdue]}: checkIfOverdue(String,Integer)
	{POST [/productOrderRelation/updateProductOrderRelation]}: updateProductOrderRelation(ProductOrderRelation)
	{GET [/productOrderRelation/refresh]}: refresh(Integer)
[09:55:40:695] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductOrderRelationCountController:
	{GET [/productOrderRelationCount/consume]}: consume(String,Integer,String,Integer)
	{GET [/productOrderRelationCount/getProductOrderRelationCount]}: getProductOrderRelationCount(String,Integer,String)
	{POST [/productOrderRelationCount/getProductOrderRelationCountList]}: getProductOrderRelationCountList(JSONObject)
	{GET [/productOrderRelationCount/tryToConsumeVADVIP]}: tryToConsumeVADVIP(Integer,Integer)
	{GET [/productOrderRelationCount/consumeVADVIP]}: consumeVADVIP(Integer,Integer)
[09:55:40:695] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QrcodeScanEntryController:
	{GET [/qrcodeScan/checkIfFollowed]}: checkIfFollowed(HttpServletRequest)
[09:55:40:696] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionBankQuestionController:
	{POST [/questionBankQuestion/changeQuestionBank]}: changeQuestionBank(JSONObject)
	{GET [/questionBankQuestion/deleteQuestionBankQuestion]}: deleteQuestionBankQuestion(Integer)
[09:55:40:696] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionCompositionChildController:
	{POST [/compositionChild/getListByIds]}: getListByIds(JSONArray)
	{POST [/compositionChild/getPaperQuestionListMapByIds]}: getListByIds(JSONObject)
	{GET [/compositionChild/getList]}: getList(Integer)
	{POST [/compositionChild/updateChildQuestionEntity]}: updateChildQuestionEntity(QuestionCompositionChild)
[09:55:40:724] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionController:
	{POST [/question/questionListOfCreater]}: getQuestionListOfCreater(JSONObject)
	{GET [/question/exportQuestions]}: exportQuestions(HttpServletRequest,HttpServletResponse)
	{POST [/question/saveBatch]}: saveBatch(JSONObject)
	{GET [/question/getCategoryList]}: getCategoryList(Integer,String,Integer)
	{[GET, POST] [/question/uploadFile]}: uploadFile(HttpServletRequest)
	{[GET, POST] [/question/uploadFileWithCheck]}: uploadFileWithCheck(HttpServletRequest)
	{POST [/question/getQuestionsByGroupConditionAdvanced]}: getQuestionsByGroupConditionAdvanced(JSONObject)
	{POST [/question/updateQuestion]}: updateQuestion(Question)
	{[GET, POST] [/question/modifyQuestion]}: modifyQuestion(HttpServletRequest)
	{POST [/question/deleteQuestionByQuery]}: deleteQuestion(JSONObject)
	{GET [/question/deleteQuestion]}: deleteQuestion(String)
	{GET [/question/deleteQuestionList]}: deleteQuestionList(String)
	{POST [/question/deleteBatchQuestion]}: deleteBatchQuestion(JSONObject)
	{GET [/question/deleteQuestionIncludingExample]}: deleteQuestionIncludingExample(int,int)
	{GET [/question/questionListOfCreaterByCategory]}: getQuestionListOfCreaterByCategory(String,String,int)
	{GET [/question/questionListOfCreaterByType]}: getQuestionListOfCreaterByType(String,String,int,int,int)
	{GET [/question/questionListOfCreaterByTypeIncludingExample]}: getQuestionListOfCreaterByTypeIncludingExample(String,String,int,int,int)
	{GET [/question/questionWrapperListOfCreaterByType]}: getQuestionWrapperListOfCreaterByType(String,String,String,String,int,int,int)
	{[GET, POST] [/question/questionWrapperListOfCreaterByTypeIncludingExample]}: getQuestionWrapperListOfCreaterByTypeIncludingExample(String,String,int,int,int)
	{GET [/question/questionListForPractice]}: getQuestionListOfCreaterForPractice(String,String,String,int,int,int)
	{GET [/question/question]}: getQuestionById(String)
	{GET [/question/questionWrapper]}: getQuestionWrapperById(String)
	{GET [/question/getQuestionWrapper]}: getQuestionWrapper(String)
	{POST [/question/importQuestionUnion], produces [application/json;charset=utf-8]}: importQuestionUnion(HttpServletRequest)
	{POST [/question/importWordQuestion], produces [application/json;charset=utf-8]}: importWordQuestion(HttpServletRequest)
	{[GET, POST] [/question/importExcelQuestion], produces [application/json;charset=utf-8]}: importExcelQuestion(HttpServletRequest)
	{GET [/question/modifyDefaultMark]}: modifyDefaultMarkOfQuestions(String,BigDecimal)
	{GET [/question/modifyCatagory]}: modifyCatagory(String,String)
	{POST [/question/modifyQuestionCatagory]}: modifyCatagory(JSONObject)
	{GET [/question/categoryAndTotalNum]}: getCategoryAndTotalNum(Integer,String,Integer)
	{GET [/question/questionTypeAndCategoryAndTotalNum]}: getQuestionTypeAndCategoryAndTotalNum(Integer,Integer,Integer)
	{POST [/question/getQuestionTypeAndCategoryAndTotalNum]}: getQuestionTypeAndCategoryAndTotalNum(JSONObject)
	{POST [/question/getQuestionTypeAndCategoryAndTotalNumWithPermission]}: getQuestionTypeAndCategoryAndTotalNumWithPermission(JSONObject)
	{GET [/question/getQuestionCatagoryAndTotalNum]}: getQuestionCatagoryAndTotalNum(Integer,int)
	{POST [/question/getQuestionCatagoryAndTotalNumWithPermission]}: getQuestionCatagoryAndTotalNumWithPermission(JSONObject)
	{GET [/question/machineChooseQuestion]}: getMachineChooseQuestion(int,String,int,int)
	{POST [/question/getQuestionsByGroupConditionAdvancedWithPermission]}: getQuestionsByGroupConditionAdvancedWithPermission(JSONObject)
	{GET [/question/getQuestionsByGroupCondition]}: getQuestionsAndLengthByGroupCondition(Integer,String,String,String,int,Integer,Integer)
	{GET [/question/getQuestionsList]}: getQuestionsList(Integer,String,String,String,int,Integer,Integer)
	{POST [/question/checkDuplicates]}: checkDuplicates(DuplicateCheckRequest)
	{[GET, POST] [/question/createQuestion]}: create(HttpServletRequest)
[09:55:40:728] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionFavoriteController:
	{ [/questionFavorite/add]}: addFavorite(int,int)
	{ [/questionFavorite/cancel]}: cancelFavorite(int,int)
	{ [/questionFavorite/ifFavorite]}: ifFavorite(int,int)
[09:55:40:737] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionRandomController:
	{POST [/questionRandom/modify]}: modify(JSONObject)
	{GET [/questionRandom/deleteQuestionList]}: deleteQuestionList(String)
	{POST [/questionRandom/createByPost]}: createByPost(JSONObject)
	{GET [/questionRandom/list]}: getQuestionRandomListByCreaterIdInCompany(Integer,int,int,int)
	{GET [/questionRandom/questionRandomExtend]}: getQuestionRandomExtend(int)
	{GET [/questionRandom/delete]}: delete(int)
	{GET [/questionRandom/create]}: create(String,String)
[09:55:40:738] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionSummaryController:
	{POST [/questionSummary/getWrongQuestionList]}: getWrongQuestionList(JSONObject)
	{POST [/questionSummary/removeWrongQuestion]}: removeWrongQuestion(JSONObject)
[09:55:40:739] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.SMSNoticeController:
	{GET [/notice/sendValidCodeSMS]}: sendValidCodeSMS(String,String)
	{GET [/notice/sendQKKRegisterNotice]}: sendQKKRegisterNotice(String,String)
	{GET [/notice/sendQKKTestOverdueNotice]}: sendQKKTemplateNotice(Integer)
	{GET [/notice/sendQKKTimeingNotice]}: sendQKKTimeingNotice(String)
[09:55:40:739] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.SoftVersionController:
	{GET [/version/reloadVersion]}: reloadVersion(String)
	{GET [/version/setSystemRunningState]}: setSystemRunningState(String)
	{GET [/version/setAdProvider]}: setAdProvider(String)
	{GET [/version/getSystemSettings]}: getSystemSettings(String)
	{GET [/version/setProductSystemRunningState]}: setProductSystemRunningState(String,String)
	{GET [/version/getProductSystemSettings]}: getProductSystemSettings(String)
	{GET [/version/getVersion]}: getVersion(String)
[09:55:40:739] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.SysUserRoleController:
	{GET [/sysUserRole/getSysUserRoleListOfCompany]}: getSysUserRoleListOfCompany(Integer,Integer)
	{POST [/sysUserRole/addSysUserRole]}: addSysUserRole(JSONObject)
	{GET [/sysUserRole/getSysUserRoleListOfUser]}: getSysUserRoleListOfUser(Integer,String,Integer)
	{GET [/sysUserRole/unbindExamineeUserRole]}: unbindExamineeUserRole(Integer,Integer)
	{POST [/sysUserRole/unbindExamineeUserRoleWithPermission]}: unbindExamineeUserRoleWithPermission(JSONObject)
	{GET [/sysUserRole/unbindAdminUserRole]}: unbindAdminUserRole(Integer,Integer)
	{POST [/sysUserRole/unbindAdminUserRoleWithPermission]}: unbindAdminUserRoleWithPermission(JSONObject)
	{POST [/sysUserRole/getSysUserRoleList]}: getSysUserRoleList(JSONObject)
	{GET [/sysUserRole/getSysUserRoleListByUsernameAndPassword]}: getSysUserRoleListByPhoneAndPassword(String,String,String,HttpServletRequest)
	{GET [/sysUserRole/getNewSysUserRoleList]}: getNewSysUserRoleList(String,String,String,HttpServletRequest)
	{GET [/sysUserRole/getSysUserRoleListOfUserInCompany]}: getSysUserRoleListOfUserInCompany(Integer,Integer,String)
	{GET [/sysUserRole/getCompanyInfoByAdminUserId]}: getCompanyInfoByAdminUserId(Integer)
	{GET [/sysUserRole/transmitAdminToOther]}: transmitAdminToOther(Integer,Integer,Integer)
	{POST [/sysUserRole/batchAddChildAdmin]}: batchAddChildAdmin(JSONObject)
[09:55:40:740] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.SystemMessageController:
	{GET [/systemMessage/batchDelete]}: batchDelete(String,String,String)
	{GET [/systemMessage/handleSystemMessage]}: handleSystemMessage()
	{GET [/systemMessage/changeDepartmentId]}: changeDepartmentId(Integer)
	{POST [/systemMessage/insert]}: insert(SystemMessage)
[09:55:40:740] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.TaxIdentityInfoController:
	{ [/taxIdentity/createTaxIdentityInfo]}: createTaxIdentityInfo(String)
	{ [/taxIdentity/modifyTaxIdentityInfo]}: modifyTaxIdentityInfo(String)
	{ [/taxIdentity/taxIdentityInfo]}: getTaxIdentityInfoByUserId(String,String)
[09:55:40:740] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.TaxSheetApplicationController:
	{ [/taxSheetApplication/createTaxSheetApplication]}: createTaxSheetApplication(String)
	{ [/taxSheetApplication/taxSheetApplicationList]}: getTaxSheetApplicationListOfUser(String)
	{ [/taxSheetApplication/taxSheetApplication]}: getTaxSheetApplicationById(String)
[09:55:40:741] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.TemplateController:
	{GET [/template/getMyTemplates]}: getMyTemplates(int,String)
[09:55:40:741] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.TestController:
	{GET [/test/api]}: apiAccess()
	{GET [/test/getKeysByPrefix]}: getKeysByPrefix(String)
	{GET [/test/deleteByPrex]}: deleteByPrex(String)
[09:55:40:741] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UniversalOrderController:
	{GET [/univsersalOrder/getList]}: getList(String,String,Integer,Integer,Integer)
	{ [/univsersalOrder/create]}: createOrder(JSONObject)
	{POST [/univsersalOrder/createOrderByWeChatNative]}: createOrderByWeChatNative(JSONObject)
	{POST [/univsersalOrder/createCountOrderByJSAPI]}: createCountOrderByJSAPI(JSONObject)
	{POST [/univsersalOrder/orderNotify]}: orderNotify(JSONObject)
	{GET [/univsersalOrder/getOrderById]}: getOrderById(Integer)
[09:55:40:742] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UniversalProductOrderRelationController:
	{POST [/universalProductOrderRelation/getProductOrderRelation]}: getProductOrderRelation(JSONObject)
[09:55:40:745] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserCompanyController:
	{GET [/userCompany/exportUserListOfCompany]}: exportUserListOfCompany(Integer,Integer,HttpServletResponse)
	{GET [/userCompany/getExamineeStatisticsOfCompany]}: getExamineeStatisticsOfCompany(Integer)
	{GET [/userCompany/checkIfExceedMemberNumLimit]}: checkIfExceedMemberNumLimit(Integer,Integer)
	{POST [/userCompany/approvedRegist]}: approvedRegist(UserCompany)
	{POST [/userCompany/importUser]}: importUser(HttpServletRequest)
	{GET [/userCompany/getCompanyInfoByUserId]}: getCompanyInfoByUserId(int)
	{GET [/userCompany/deleteUserOfCompany]}: deleteUserOfCompany(int,int)
	{POST [/userCompany/getUserInfoListByIds]}: getUserInfoListByIds(JSONObject)
	{POST [/userCompany/updateUserInfoAndCompanyInfo]}: saveUserInfoAndCompanyInfo(JSONObject)
	{GET [/userCompany/saveUserInfoAndCompanyInfo]}: saveUserInfoAndCompanyInfo(int,int,int,String,String)
	{POST [/userCompany/bindCompany]}: bindCompany(JSONObject)
	{POST [/userCompany/deleteUserOfCompanyWithPermission]}: deleteUserOfCompanyWithPermission(JSONObject)
	{POST [/userCompany/deleteBatch]}: deleteBatchWithPermission(JSONObject)
	{GET [/userCompany/deleteExamineeByDepartmentName]}: deleteExamineeByDepartmentName(Integer,String)
	{POST [/userCompany/deleteExamineeByDepartmentNameWithPermission]}: deleteExamineeByDepartmentNameWithPermission(JSONObject)
	{GET [/userCompany/getUserListByQkkQiyeCorpid]}: getUserListByQkkQiyeCorpid(String,String)
	{GET [/userCompany/getCompanyInfoByUserIdAndCompanyId]}: getUserCompanyInfoByUserIdAndCompanyId(int,int)
	{GET [/userCompany/getUserCompanyByPhone]}: getUserCompanyByPhone(String)
	{GET [/userCompany/getUserInfoWithCompanyInfo]}: getUserInfoWithCompanyInfo(Integer,Integer)
	{GET [/userCompany/getUserListByDepartmentId]}: getUserListByDepartmentId(Integer,Integer,Integer,Integer,Integer,Integer)
	{POST [/userCompany/getCompanyUserListByMap]}: getCompanyUserListByMap(JSONObject)
	{GET [/userCompany/getUserListOfDepartmentsInCludingSelf]}: getUserListOfDepartmentsInCludingSelf(String)
	{GET [/userCompany/getUserListOfDepartmentsExcludingSelf]}: getUserListOfDepartmentsExcludingSelf(String)
	{GET [/userCompany/getUserListByName]}: getUserListByName(String,Integer,Integer,Integer,Integer)
	{POST [/userCompany/getUserList]}: getUserListByName(JSONObject)
	{GET [/userCompany/ifRegisted]}: getIfRegisted(int,int)
	{GET [/userCompany/getUserListOfCompany]}: getUserListOfCompany(Integer,String,String,Integer,Integer)
	{GET [/userCompany/secureGetUserListOfCompany]}: secureGetUserListOfCompany(Integer,String,String,Integer,Integer,Integer,Integer,Integer,Integer)
	{GET [/userCompany/getGrantedUserListOfCompany]}: getGrantedUserListOfCompany(Integer,String,String,Integer,Integer,Integer,Integer,String,Integer,Integer,String,String)
	{POST [/userCompany/getCompanyUserList]}: getCompanyUserList(JSONObject)
[09:55:40:750] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserController:
	{GET [/user/getUserInfoByOpenId]}: getUserByEteaOpenId(String,String)
	{GET [/user/getUserInfoById]}: getUserInfoById(int)
	{GET [/user/getUserInfoListByNickName]}: getUserInfoListByNickName(String)
	{POST [/user/supplyPhoneNo]}: supplyPhoneNo(JSONObject)
	{GET [/user/loginByValidationCode]}: loginByValidationCode(String,String,HttpServletRequest)
	{GET [/user/validateSMSPhoneIfMatched]}: validateSMSPhoneIfMatched(String,String)
	{GET [/user/oneKeyLogin]}: oneKeyLogin(String,HttpServletRequest)
	{GET [/user/login]}: loginByPhoneAndPassword(String,String,HttpServletRequest)
	{GET [/user/loginById]}: loginById(Integer)
	{POST [/user/getUserInfoByUserIdAndCompanyId]}: getUserInfoByUserIdAndCompanyId(JSONObject,HttpServletRequest)
	{POST [/user/loginWithAutoRegist]}: loginWithAutoRegist(JSONObject,HttpServletRequest)
	{GET [/user/changePassword]}: changePassword(Integer,String,String)
	{GET [/user/updateUserInfo]}: updateUserInfo(String)
	{GET [/user/getUserByPhone]}: getUserByPhone(String)
	{POST [/user/getUserInfoListByIds]}: getUserInfoListByIds(List)
	{GET [/user/getUserListByQkkQiyeCorpid]}: getUserListByQkkQiyeCorpid(String,String)
	{GET [/user/getUserInfoWithCompanyInfo]}: getUserInfoWithCompanyInfo(Integer,Integer)
	{POST [/user/getPassword]}: getPassword(JSONObject)
	{POST [/user/register]}: register(JSONObject)
	{POST [/user/update]}: update(User)
[09:55:40:754] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserDeviceBindingController:
	{POST [/userDeviceBinding/getUserDeviceBinding]}: getUserDeviceBinding(JSONObject)
	{POST [/userDeviceBinding/bindDevice]}: bindDevice(JSONObject)
	{POST [/userDeviceBinding/unbindDevice]}: unbindDevice(JSONObject)
[09:55:40:755] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserExaminationPerformanceReportController:
	{ [/examinationPerformanceReport/consume]}: consume(Integer,Integer)
[09:55:40:757] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserFormController:
	{GET [/userForm/deleteForm]}: deleteFormOfUser(Integer,Integer)
	{GET [/userForm/getUserFormListByUserId]}: getUserFormListByUserId(Integer)
	{POST [/userForm/create]}: create(UserForm)
[09:55:40:758] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserFormFlowRecordController:
	{POST [/userFormFlowRecord/upsert]}: upsert(UserFormFlowRecord)
	{GET [/userFormFlowRecord/getFormFlowRecordWrapperById]}: getFormFlowRecordWrapper(Integer,Integer)
[09:55:40:758] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserOfYncxController:
	{ [/userOfYncx/exportDetailRankListOfExamination]}: exportDetailRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/userOfYncx/exportRankListOfExamination]}: exportRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/userOfYncx/examinationReport]}: createExaminationPdfReport(HttpServletRequest,HttpServletResponse)
	{GET [/userOfYncx/exportExaminationInstanceDetailExcelBundle]}: exportExaminationInstanceDetailExcelBundle(Integer,Integer)
	{GET [/userOfYncx/exportExaminationInstancePdfBundle]}: exportExaminationInstancePdfBundle(Integer,Integer)
[09:55:40:758] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserStatisticController:
	{ [/userStatistic/totalItemNum]}: totalNumber(Integer,Integer)
	{ [/userStatistic/getOnlineUserNumberOfExamination]}: getOnlineUserNumberOfExamination(int,int)
[09:55:40:758] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserSummaryController:
	{POST [/userSummary/upInsert]}: upInsert(JSONObject)
	{POST [/userSummary/getEntity]}: getEntity(UserSummary)
[09:55:40:758] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserViewPerformanceWithoutAdController:
	{GET [/userViewPerformanceWithoutAd/insertIfNotExisted]}: insertIfNotExisted(Integer,Integer,Integer,String)
	{GET [/userViewPerformanceWithoutAd/adEndedCallback]}: adEndedCallback(String,String)
	{POST [/userViewPerformanceWithoutAd/deletePermently]}: deletePermently(JSONObject)
	{ [/userViewPerformanceWithoutAd/checkIfAllowUserViewPerformanceWithoutAd]}: checkIfAllowUserViewPerformanceWithoutAd(int,int,int)
	{ [/userViewPerformanceWithoutAd/getUserViewPerformanceWithoutAdTransaction]}: getUserViewPerformanceWithoutAdTransaction(String,int,String,Integer,Integer)
[09:55:40:762] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WeChatMiniProgramController:
	{POST [/weixinAccount/getQRCode]}: getQRCode(JSONObject)
	{POST [/weixinAccount/getGroupId]}: getGroupId(JSONObject)
	{POST [/weixinAccount/msgSecCheck]}: msgSecCheck(JSONObject)
	{POST [/weixinAccount/getProductList]}: getProductList(JSONObject)
	{GET [/weixinAccount/getAccessToken]}: getAccessTokenOfAccount(String)
	{POST [/weixinAccount/registWithCode]}: registWithCode(JSONObject)
	{GET [/weixinAccount/autoLogin]}: autoLogin(String,String,HttpServletRequest)
	{GET [/weixinAccount/loginWithoutUserInfoAndAutoRegist]}: loginWithoutUserInfoAndAutoRegist(String,String,HttpServletRequest)
	{GET [/weixinAccount/login]}: loginWithAutoRegister(String,String,HttpServletRequest)
	{POST [/weixinAccount/loginWithUserAuthNoRegistByPost]}: loginWithUserAuthWithoutAutoRegistByPost(JSONObject,HttpServletRequest)
	{POST [/weixinAccount/getUserInfoByWeixinCode]}: getUserInfoByWeixinCode(JSONObject)
	{POST [/weixinAccount/loginWithUserAuthByPost]}: loginWithUserAuthWithAutoRegistByPost(JSONObject,HttpServletRequest)
	{GET [/weixinAccount/loginWithUserAuth]}: loginWithUserAuthWithAutoRegistByGet(String,String,String,String,String,HttpServletRequest)
	{POST [/weixinAccount/getQRCodeBase64ByLimit]}: getQRCodeBase64ByLimit(JSONObject)
	{POST [/weixinAccount/getQRCodeBase64]}: getQRCodeBase64(JSONObject)
	{POST [/weixinAccount/getPhoneNumByPost]}: getPhoneNumByPost(JSONObject)
	{POST [/weixinAccount/getPhoneNum]}: getPhoneNum(JSONObject)
	{POST [/weixinAccount/getPhoneNumNew]}: getPhoneNumNew(JSONObject)
	{POST [/weixinAccount/getPhoneNumWithoutGetSession]}: getPhoneNumWithoutGetSession(JSONObject)
	{POST [/weixinAccount/searchProduct]}: searchProduct(JSONObject)
	{GET [/weixinAccount/getCouponList]}: getCoupon(String)
	{GET [/weixinAccount/receivedCouple]}: receivedCouple(String,String,String)
	{GET [/weixinAccount/getUserCoupleList]}: getUserCoupleList(String,String,String)
	{GET [/weixinAccount/session]}: getSession(String,String)
	{POST [/weixinAccount/imgSecCheck]}: imgSecCheck(HttpServletRequest)
	{POST [/weixinAccount/imgSecCheckByMultipart], consumes [multipart/form-data]}: imgSecCheck(MultipartFile,String)
	{POST [/weixinAccount/loginWithAuth]}: loginWithAuth(JSONObject,HttpServletRequest)
	{POST [/weixinAccount/bind]}: bind(JSONObject)
[09:55:40:802] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WeChatOfficialAccountController:
	{GET [/wx/checkIfFollowed]}: getQrcodeScanEntry(HttpServletRequest)
	{GET [/wx/loginWithAutoRegister]}: loginWithAutoRegister(String,String)
	{GET [/wx/getJSSDKConfiguration]}: getJSSDKConfiguration(String,String)
	{GET [/wx/getTicket]}: getTicket(HttpServletRequest)
	{GET [/wx/loginByWeChatOfficialAccount]}: loginByWeChatOfficialAccount(String,String)
	{GET [/wx/getPageAuthAccessToken]}: getPageAuthAccessToken(String,String)
	{GET [/wx/getTicketOfProduct]}: getTicketOfProduct(HttpServletRequest)
	{GET [/wx/createAccountMenu]}: createAccountMenu(String)
	{GET [/wx/receiveMessage]}: doGet(HttpServletRequest)
	{POST [/wx/receiveMessage], produces [application/xml;charset=utf-8]}: processRequest(HttpServletRequest)
[09:55:40:802] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WordFileController:
	{GET [/wordFile/exportExamPaper]}: exportExamPaper(HttpServletRequest,HttpServletResponse)
	{GET [/wordFile/exportWrongQuestions]}: exportWrongQuestions(HttpServletRequest,HttpServletResponse)
	{GET [/wordFile/exportExercisePaper]}: exportExercisePaper(HttpServletRequest,HttpServletResponse)
[09:55:40:802] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WrongBookController:
	{GET [/wrongBook/list]}: getWrongBookList(Integer)
	{POST [/wrongBook/create]}: createWrongBook(JSONObject)
	{GET [/wrongBook/deletePermanently]}: deletePermanently(Integer)
	{POST [/wrongBook/getWrongBookQuestionList]}: getWrongBookQuestionList(JSONObject)
	{POST [/wrongBook/removeQuestion]}: removeQuestion(JSONObject)
	{POST [/wrongBook/update]}: updateIgnoreNull(JSONObject)
[09:55:40:803] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WrongQuestionBookController:
	{POST [/wrongQuestionBook/doReview]}: doReview(JSONObject)
	{ [/wrongQuestionBook/wrongQuestionBookPageInfo]}: getWrongQuestionBookPageInfoOfUser(int,int)
	{ [/wrongQuestionBook/getWrongQuestionBookOfUser]}: getWrongQuestionBookOfUser(Integer)
	{POST [/wrongQuestionBook/batchSaveWrongQuestionBook]}: batchSaveWrongQuestionBook(JSONObject)
	{GET [/wrongQuestionBook/delete]}: delete(Integer)
[09:55:40:803] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WrongQuestionBookQuestionController:
	{ [/wrongQuestionBookQuestion/markAsReviewed]}: markAsReviewed(int,int)
	{ [/wrongQuestionBookQuestion/getQuestionWrapperListOfWrongQuestionBook]}: getQuestionWrapperListOfWrongQuestionBook(int)
	{ [/wrongQuestionBookQuestion/getQuestionWrapperListOfBook]}: getQuestionWrapperListOfBook(Integer)
	{GET [/wrongQuestionBookQuestion/getTotalWrongQuestionWrapperList]}: getTotalWrongQuestionWrapperList(Integer,Integer)
	{GET [/wrongQuestionBookQuestion/deleteByQuery]}: deleteByQuery(Integer,Integer)
[09:55:40:803] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WrongQuestionBookReviewSummaryController:
	{POST [/wrongQuestionBookReviewSummary/saveSummary]}: saveSummary(JSONObject)
[09:55:40:803] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WxMiniprogramSubscribeMessageOfEteaController:
	{GET [/wxMiniprogramSubscribeMessageOfEtea/batchSendSubscribedMessage]}: batchSendSubscribedMessage(Integer,Integer,Integer)
	{POST [/wxMiniprogramSubscribeMessageOfEtea/subscribe]}: subscribe(JSONObject)
[09:55:40:804] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.c.p.ExaminationControllerOfPC:
	{POST [/examination/getExaminationInViewListOfCreaterByTags]}: getExaminationInViewListOfCreaterByTags(JSONObject)
	{GET [/examination/examinationInViewListOfCreaterFromPC]}: getExaminationInViewListOfCreaterFromPC(Integer,Integer,Integer,Integer)
	{POST [/examination/getExaminationInViewListAndNum]}: getExaminationInViewListAndNum(JSONObject)
	{POST [/examination/getExaminationInViewListOfCreaterByTagsFromPC]}: getExaminationInViewListOfCreaterByTagsFromPC(JSONObject)
	{POST [/examination/getExaminationInViewListExcludingCreaterByTagsFromPC]}: getExaminationInViewListExcludingCreaterByTagsFromPC(JSONObject)
	{GET [/examination/getExaminationListAndSizeOfWillMark]}: getExaminationListAndSizeOfWillMark(Integer,Integer,Integer,Integer)
	{POST [/examination/getWillMarkExaminationListAndSize]}: getWillMarkExaminationListAndSize(JSONObject)
[09:55:40:804] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.c.p.ExaminationInstanceControllerOfPC:
	{GET [/examinationInstance/examinationInstanceStageSummaryListFromPC]}: getExaminationInstanceStageSummaryListFromPC(String,String,String,int,int,int)
	{GET [/examinationInstance/getExaminationInstanceStageSummaryListAndTotalNumFromPC]}: getExaminationInstanceStageSummaryListAndTotalNumFromPC(String,String,String,int,int,int)
	{POST [/examinationInstance/getExaminationInstanceStageSummaryListAndNum]}: getExaminationInstanceStageSummaryListAndNum(JSONObject)
	{POST [/examinationInstance/getExaminationInstanceStageSummaryListAndLengthByTags]}: getExaminationInstanceStageSummaryListByTagsFromPC(JSONObject)
	{POST [/examinationInstance/getListAndSizeOfExaminationInstanceWithUserInfoByConditions]}: getListAndSizeOfExaminationInstanceWithUserInfoByConditions(JSONObject)
	{GET [/examinationInstance/getExaminationInstanceListAndSizeWithUserInfoByExaminationId]}: getExaminationInstanceListWithUserInfoByExaminationId(Integer,Integer,Integer,String,Integer,Integer)
[09:55:40:804] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.c.p.PcPageDataController:
	{GET [/pageData/getCompanyCoreSummary]}: getCompanyCoreSummary(Integer,Integer)
[09:55:40:804] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.c.PageDataControllerOfCooperation:
	{GET [/pageDateControllerOfCooperation/getRankData]}: getRankData(Integer,Integer,Integer,Integer,String,String,Integer,Integer)
[09:55:40:805] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.e.ExaminationInstanceControllerOfEtea:
	{GET [/examinationInstanceControllerOfEtea/exportTotalScoreGroupByUser]}: exportTotalScoreGroupByUser(String,HttpServletResponse)
	{GET [/examinationInstanceControllerOfEtea/getUserTakenExaminations]}: getUserTakenExaminations(Integer,Integer,Integer)
	{GET [/examinationInstanceControllerOfEtea/getRecentOnGoingExaminations]}: getRecentOnGoingExaminations(Integer)
[09:55:40:805] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.e.PageDataControllerOfEtea:
	{POST [/pageDataOfEtea/getTotalScoreGroupByUser]}: getTotalScoreGroupByUser(JSONObject)
	{GET [/pageDataOfEtea/getHomePageSummaryInfo]}: getHomePageSummaryInfo(Integer)
[09:55:40:805] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.f.FormController:
	{POST [/formSystem/saveFormFlowRecord]}: saveFormFlowRecord(JSONObject)
	{POST [/formSystem/createForm]}: createForm(JSONObject)
	{GET [/formSystem/getFormWrapperById]}: getFormWrapperById(Integer)
	{POST [/formSystem/updateForm]}: updateForm(JSONObject)
[09:55:40:806] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteContentAccessController:
	{POST [/ksiteContentAccess/getContentAccessList]}: getContentAccessList(JSONObject)
	{POST [/ksiteContentAccess/add]}: add(KsiteContentAccess)
[09:55:40:806] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteFavoriteController:
	{POST [/ksiteFavorite/getFavoriteList]}: getFavoriteList(JSONObject)
	{POST [/ksiteFavorite/deleteByColumns]}: deleteByColumns(KsiteFavorite)
	{GET [/ksiteFavorite/getFavorite]}: getFavorite(Integer,String,Integer)
	{POST [/ksiteFavorite/add]}: add(KsiteFavorite)
[09:55:40:806] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteOrderController:
	{ [/ksiteOrder/orderNotify]}: orderNotify(JSONObject)
	{ [/ksiteOrder/createOrder]}: createKsiteOrder(JSONObject)
	{POST [/ksiteOrder/getOrderListWithKsiteInfo]}: getOrderListWithKsiteInfo(JSONObject)
	{POST [/ksiteOrder/getOrderList]}: getOrderListAndNum(JSONObject)
	{GET [/ksiteOrder/getAmountOfIncome]}: getAmountOfIncome(Integer)
	{GET [/ksiteOrder/getSettlementDashboardData]}: getSettlementDashboardData(Integer)
	{POST [/ksiteOrder/getOrderNum]}: getOrderNum(JSONObject)
	{POST [/ksiteOrder/update]}: update(JSONObject)
[09:55:40:806] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteOrderRelationController:
	{POST [/ksiteOrderRelation/checkContentAuth]}: checkContentAuth(KsiteOrderRelation)
[09:55:40:806] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsitePaperInstanceController:
	{GET [/ksitePaperInstance/getPaperInstanceStageSummaryDetail]}: getPaperInstanceStageSummaryDetail(Integer,String,String,Integer,Integer)
	{POST [/ksitePaperInstance/create]}: create(JSONObject)
[09:55:40:807] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsitePaperInstanceProcessController:
	{GET [/ksitePaperInstanceProcess/getPaperInstanceProcessWrapperListAndNum]}: getPaperInstanceProcessWrapperListAndNum(Integer,Boolean,Integer,Integer)
[09:55:40:807] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteUserSignatureController:
	{POST [/ksiteUserSignature/getList]}: getList(JSONArray)
	{POST [/ksiteUserSignature/signKsite]}: sign(KsiteUserSignatureWrapper)
	{GET [/ksiteUserSignature/getUserSignature]}: getUserSignature(Integer,Integer)
	{GET [/ksiteUserSignature/checkKsiteNameExisted]}: checkKsiteNameExisted(String)
	{GET [/ksiteUserSignature/getKsiteInfo]}: getKsiteInfo(String,Integer)
	{POST [/ksiteUserSignature/update]}: update(KsiteUserSignatureWrapper)
[09:55:40:807] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteWithdrawController:
	{POST [/ksiteWithdraw/withdraw]}: withdraw(KsiteWithdraw)
[09:55:40:808] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.m.c.CategoryController:
	{POST [/category/getBatchPathOfCategoryList]}: getBatchPathOfCategoryList(JSONArray)
	{GET [/category/getNLevelCategory]}: getLevelCategoriesOfLessThanOrEqualDesignativeLevel(String,Integer,Integer)
	{POST [/category/addNewCategory]}: addNewCategory(JSONObject)
	{GET [/category/getPathOfCategory]}: getPathOfCategory(Integer)
	{POST [/category/updateCategory]}: updateCategory(JSONObject)
	{POST [/category/updateCategoryList]}: updateCategoryList(JSONArray)
	{GET [/category/copySpecifiedSpaceName]}: copySpecifiedSpaceName(String,Integer,String)
	{POST [/category/add]}: add(JSONObject)
	{POST [/category/update]}: update(JSONObject)
[09:55:40:809] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QiyeweixinAccountController:
	{GET [/qiyeweixinAccount/transferLicense]}: transferLicense(String,List)
	{GET [/qiyeweixinAccount/getMemberActiveInfo]}: getMemberActiveInfo(String,String)
	{GET [/qiyeweixinAccount/queryAutoActiveStatus]}: queryAutoActiveStatus(String)
	{GET [/qiyeweixinAccount/getActivatedAccountList]}: getActivatedAccountList(String,Integer,String)
[09:55:40:811] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QiyeweixinCommunicationController:
	{ [/qiyeweixinCommunication/receiveData]}: receiveData(String,String,String,String,HttpServletRequest)
	{ [/qiyeweixinCommunication/receiveCommand]}: receiveCommand(String,String,String,String,HttpServletRequest)
	{GET [/qiyeweixinCommunication/getPreAuthCode]}: getPreAuthCode(String)
	{GET [/qiyeweixinCommunication/setSessionInfo]}: setSessionInfo(String)
	{GET [/qiyeweixinCommunication/generatePermanentCode]}: generatePermanentCode(String,String)
	{GET [/qiyeweixinCommunication/getAppQrcode]}: getAppQrcode(String)
	{GET [/qiyeweixinCommunication/getAppPermission]}: getAppPermission(String,String)
	{GET [/qiyeweixinCommunication/getAppAdmin]}: getAppAdmin(String,String)
	{GET [/qiyeweixinCommunication/getJsapiTicket]}: getJsapiTicket(String,String,String)
[09:55:40:811] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QiyeweixinSignatureController:
	{POST [/qiyeweixinSignature/getSignature]}: getSignature(JSONObject)
[09:55:40:811] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QiyeweixinUserController:
	{GET [/qiyeweixinUser/login]}: login(HttpServletRequest)
	{GET [/qiyeweixinUser/loginQkkQiyeweixin]}: wechatLogin()
	{GET [/qiyeweixinUser/loginQkkQiyeweixinFromPCWeb]}: wechatLoginFromPCWeb(String)
	{GET [/qiyeweixinUser/syncAllDepartmentNameAndUserNameByOCR]}: syncAllDepartmentNameAndUserNameByOCR(Integer)
	{GET [/qiyeweixinUser/refreshDepartmentNameAndUserNameByOCR]}: refreshDepartmentNameAndUserNameByOCR(Integer)
	{GET [/qiyeweixinUser/getUserInfo]}: getUserInfo(HttpServletRequest)
[09:55:40:812] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.CompanyControllerOfQKK:
	
[09:55:40:812] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.CompanyRegisterFormController:
	{POST [/companyRegisterForm/insertIfNotExisted]}: insertIfNotExisted(CompanyRegisterForm)
	{GET [/companyRegisterForm/getCompanyRegisterForm]}: getCompanyRegisterForm(Integer)
	{GET [/companyRegisterForm/getCompanyRegisterFormFieldStructure]}: getCompanyRegisterFormFieldStructure(Integer)
[09:55:40:812] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.CompanyRegisterFormFlowRecordController:
	{GET [/companyRegisterFormFlowRecord/getRegistrationDetail]}: getRegistrationDetail(Integer,Integer)
[09:55:40:813] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.ExaminationControllerOfQkk:
	{POST [/examination/getExaminationInViewListOfCompanyByTagesAndUserCompany]}: getExaminationInViewListOfCompanyByTagesAndUserCompany(JSONObject)
	{POST [/examination/getExaminationInViewListAndTotalSizeOfCompanyByTagesAndUserCompany]}: getExaminationInViewListAndTotalSizeOfCompanyByTagesAndUserCompany(JSONObject)
	{GET [/examination/getExamList]}: getExamList(Integer)
	{GET [/examination/checkIfAuthorized]}: checkIfAuthorized(Integer,Integer,Integer)
[09:55:40:813] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.ExaminationInstanceControllerOfQkk:
	{GET [/examinationInstanceOfQkk/getExaminationInstanceListWithUserInfoByExaminationId]}: getExaminationInstanceListWithUserInfoByExaminationId(Integer,Integer,Integer,String,Integer,Integer)
	{GET [/examinationInstanceOfQkk/examinationInstanceStageSummaryDetail]}: getExaminationInstanceStageSummaryDetail(Integer,Integer,Integer,String,String,Integer,Integer)
	{POST [/examinationInstanceOfQkk/getSummaryGroupByDepartment]}: getSummaryGroupByDepartment(JSONObject)
	{POST [/examinationInstanceOfQkk/getTotalScoreOfExaminationListGroupByUser]}: getTotalScoreOfExaminationListGroupByUser(JSONObject)
	{GET [/examinationInstanceOfQkk/exportSummaryGroupByDepartment]}: exportSummaryGroupByDepartment(Integer,Integer,Integer,HttpServletResponse)
	{POST [/examinationInstanceOfQkk/getTopNDepartmentDistribution]}: getTopNDepartmentDistribution(JSONObject)
	{GET [/examinationInstanceOfQkk/exportTotalScoreGroupByUser]}: exportTotalScoreGroupByUser(String,HttpServletResponse)
[09:55:40:813] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.PageDataController:
	{POST [/pageData/getTotalScoreGroupByUserInCompany]}: getTotalScoreGroupByUserInCompany(JSONObject)
[09:55:40:813] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QuestionControllerOfQkk:
	{POST [/question/questionListExcludingCreaterInCompany]}: getQuestionListExcludingCreaterInCompany(JSONObject)
[09:55:40:814] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.UserControllerOfQkk:
	{POST [/user/secureAdminRegister]}: secureAdminRegister(HttpServletRequest)
	{POST [/user/registerAdminFromMp]}: registerAdminFromMp(JSONObject,HttpServletRequest)
	{POST [/user/registerAdminFromH5]}: registerAdminFromH5(JSONObject,HttpServletRequest)
	{POST [/user/secureExamineeRegister]}: secureExamineeRegister(HttpServletRequest,HttpServletResponse)
	{POST [/user/registerExamineeFromMp]}: registerExamineeFromMp(JSONObject,HttpServletRequest)
	{POST [/user/registerExamineeFromH5]}: registerExamineeFromH5(JSONObject,HttpServletRequest)
	{POST [/user/secureLogin]}: secureLogin(HttpServletRequest,HttpServletResponse)
	{GET [/user/mergeCompanyUser]}: mergeCompanyUser(Integer)
[09:55:40:814] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoMiniProgramController:
	{POST [/toutiaoMiniProgram/regist]}: regist(JSONObject)
	{POST [/toutiaoMiniProgram/autoLogin]}: autologin(JSONObject)
	{POST [/toutiaoMiniProgram/update]}: update(JSONObject)
[09:55:40:814] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoQuestionStarController:
	{GET [/touTiaoQuestionStar/getQuestionStarWrapper]}: getQuestionStarWrapper(Integer)
[09:55:40:815] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoQuestionStarStageEventController:
	{POST [/toutiaoQuestionStarStageEvent/insertIfNotExisted]}: insertIfNotExisted(ToutiaoQuestionStarStageEvent)
	{GET [/toutiaoQuestionStarStageEvent/getMyPlayedStageList]}: getMyPlayedStageList(Integer,Integer)
[09:55:40:815] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoQuestionStarUserController:
	{GET [/toutiaoQuestionStarUser/getUserInfo]}: getUserInfo(Integer)
[09:55:40:816] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoQuestionStarUserProfileController:
	{POST [/toutiaoQuestionStarUserProfile/getRankInfoList]}: getRankInfoList(JSONObject)
	{POST [/toutiaoQuestionStarUserProfile/markPass]}: markPass(JSONObject)
	{POST [/toutiaoQuestionStarUserProfile/increaseExp]}: increaseExp(JSONObject)
	{GET [/toutiaoQuestionStarUserProfile/getUserInfo]}: getUserInfo(Integer)
[09:55:40:817] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.x.UserSchoolController:
	{POST [/userSchool/bindSchool]}: bindCompany(JSONObject)
[09:55:40:819] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.o.c.OssFileController:
	{POST [/ossFile/update]}: update(OssFile)
[09:55:40:840] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
	{ [/error]}: error(HttpServletRequest)
[09:55:40:903] [DEBUG] - org.springframework.web.servlet.handler.AbstractDetectingUrlHandlerMapping.detectHandlers(AbstractDetectingUrlHandlerMapping.java:86) - 'beanNameHandlerMapping' {}
[09:55:41:431] [DEBUG] - org.springframework.web.servlet.handler.SimpleUrlHandlerMapping.logMappings(SimpleUrlHandlerMapping.java:177) - 'webSocketHandlerMapping' {/wss/pkGameSocket=org.springframework.web.socket.server.support.WebSocketHttpRequestHandler@44a76ac7}
[09:55:41:734] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$MapperScannerRegistrarNotFoundConfiguration.afterPropertiesSet(MybatisAutoConfiguration.java:305) - No org.mybatis.spring.mapper.MapperFactoryBean found.
[09:55:43:184] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[09:55:44:099] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[09:55:44:150] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 47 ms to scan 1 urls, producing 3 keys and 6 values 
[09:55:44:163] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype com.alibaba.nacos.api.remote.request.Request -> com.alibaba.nacos.api.remote.request.ServerRequest
[09:55:44:172] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[09:55:44:209] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 36 ms to scan 1 urls, producing 4 keys and 9 values 
[09:55:44:210] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype com.alibaba.nacos.api.remote.request.Request -> com.alibaba.nacos.api.remote.request.ServerRequest
[09:55:44:214] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[09:55:44:229] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 15 ms to scan 1 urls, producing 3 keys and 10 values 
[09:55:44:234] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
file:/Users/<USER>/Documents/git/taurus-cloud/exam-main-service/target/classes/
file:/private/var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T/cp_bglw5z4cph95pkrips04x9cfi.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/dnsns.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jaccess.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jfxrt.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunec.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/zipfs.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/nashorn.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/cldrdata.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/localedata.jar
[09:55:44:274] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 40 ms to scan 12 urls, producing 0 keys and 0 values 
[09:55:44:276] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[09:55:44:285] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
[09:55:44:288] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[09:55:44:297] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
[09:55:44:302] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[09:55:44:327] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 23 ms to scan 1 urls, producing 2 keys and 8 values 
[09:55:44:329] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype java.lang.Comparable -> java.lang.Enum
[09:55:44:329] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype java.io.Serializable -> java.lang.Enum
[09:55:44:337] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
file:/Users/<USER>/Documents/git/taurus-cloud/exam-main-service/target/classes/
file:/private/var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T/cp_bglw5z4cph95pkrips04x9cfi.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/dnsns.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jaccess.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jfxrt.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunec.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/zipfs.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/nashorn.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/cldrdata.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/localedata.jar
[09:55:44:363] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 25 ms to scan 12 urls, producing 0 keys and 0 values 
[09:55:45:510] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[09:55:45:511] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[09:55:45:512] [DEBUG] - com.taurus.examinationassistant.filter.AdminAuthFilter.init(AdminAuthFilter.java:45) - AdminAuthFilter初始化
[09:55:45:528] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[09:55:45:550] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[09:55:45:565] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[09:55:45:593] [DEBUG] - org.xnio.XnioWorker$Builder.build(XnioWorker.java:1191) - Creating worker:null, pool size:64, max pool size:64, keep alive:60000, io threads:8, stack size:0
[09:55:45:610] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[09:55:45:631] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-6', selector sun.nio.ch.KQueueSelectorImpl@42827ec6
[09:55:45:631] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-1', selector sun.nio.ch.KQueueSelectorImpl@321ef05b
[09:55:45:631] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-4', selector sun.nio.ch.KQueueSelectorImpl@42f7df77
[09:55:45:631] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-5', selector sun.nio.ch.KQueueSelectorImpl@27bd9bb9
[09:55:45:631] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-8', selector sun.nio.ch.KQueueSelectorImpl@48bd7faa
[09:55:45:631] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-7', selector sun.nio.ch.KQueueSelectorImpl@527e19e0
[09:55:45:631] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-2', selector sun.nio.ch.KQueueSelectorImpl@69e69ff1
[09:55:45:631] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-3', selector sun.nio.ch.KQueueSelectorImpl@2251ff7
[09:55:45:631] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 Accept', selector sun.nio.ch.KQueueSelectorImpl@60497e0
[09:55:45:636] [DEBUG] - io.undertow.Undertow.start(Undertow.java:160) - Configuring listener with protocol HTTP for interface 0.0.0.0 and port 8081
[09:55:45:759] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service 192.168.8.183:8081 register finished
[09:55:46:186] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 37.52 seconds (JVM running for 39.443)
[09:55:46:218] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[09:55:46:372] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`account` , t.`app_id` , t.`app_secret` , t.`token` , t.`encoding_aes_key` FROM `weixin_account` t 
[09:55:46:482] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 
[09:55:46:625] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 13
[09:55:46:630] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[09:56:14:204] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /version/getVersion, authentication required: false
[09:56:14:205] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /wx/getTicket, authentication required: false
[09:56:14:217] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /wx/getTicket
[09:56:14:217] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /version/getVersion
[09:56:14:218] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /version/getVersion
[09:56:14:218] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /wx/getTicket
[09:56:14:227] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[09:56:19:142] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: INSERT INTO `qrcode_scan_entry` ( `ticket` , `scene_id` , `create_time` , `scan_time` , `openid` , `creater_id` , `phone` ) VALUES ( ? , ? , ? , ? , ? , ? , ? ) 
[09:56:19:165] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: gQFb7zwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAySURhdjF6aWtjeGwxMWFuVnhFMS0AAgRCEDloAwQIBwAA(String), 86872007(Integer), 2025-05-30 09:56:19.08(Timestamp), null, null, null, null
[09:56:19:261] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[09:56:19:263] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[09:56:20:134] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /user/getUserInfoById, authentication required: false
[09:56:20:135] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /user/getUserInfoById
[09:56:20:135] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /user/getUserInfoById
[09:56:20:227] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[09:56:20:228] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 37377(Integer)
[09:56:20:364] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:20:365] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[09:56:20:422] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /access/getTokenUnlimit, authentication required: false
[09:56:20:424] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /access/getTokenUnlimit
[09:56:20:425] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /access/getTokenUnlimit
[09:56:20:605] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /universalProductOrderRelation/getProductOrderRelation, authentication required: false
[09:56:20:606] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /universalProductOrderRelation/getProductOrderRelation
[09:56:20:606] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /universalProductOrderRelation/getProductOrderRelation
[09:56:20:973] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /productOrderRelationCount/getProductOrderRelationCountList, authentication required: false
[09:56:20:972] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /productOrderRelationCount/getProductOrderRelationCountList, authentication required: false
[09:56:20:988] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /productOrderRelationCount/getProductOrderRelationCountList
[09:56:20:988] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /productOrderRelationCount/getProductOrderRelationCountList
[09:56:20:992] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /productOrderRelationCount/getProductOrderRelationCountList
[09:56:20:992] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /productOrderRelationCount/getProductOrderRelationCountList
[09:56:21:010] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /product/getListOfProduct, authentication required: false
[09:56:21:018] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /product/getListOfProduct
[09:56:21:018] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /product/getProductList, authentication required: false
[09:56:21:022] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /product/getListOfProduct
[09:56:21:027] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /product/getProductList
[09:56:21:031] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /product/getProductList
[09:56:21:028] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /pageData/getCompanyCoreSummary, authentication required: false
[09:56:21:034] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /pageData/getCompanyCoreSummary
[09:56:21:035] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /pageData/getCompanyCoreSummary
[09:56:21:327] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`type` , t.`code` , t.`name` , t.`short_description` , t.`long_description` , t.`max_examinee_num` , t.`valid_days` , t.`price` FROM `product` t WHERE type = ? 
[09:56:21:356] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: P(String)
[09:56:21:372] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`relation_type` , t.`relation_id` , t.`modal_name` , t.`available_times` , t.`end_time` FROM `product_order_relation_count` t WHERE relation_type = ? AND relation_id = ? AND modal_name IN ( ? , ? , ? , ? , ? , ? , ? , ? ) ORDER BY id ASC 
[09:56:21:375] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`relation_type` , t.`relation_id` , t.`modal_name` , t.`available_times` , t.`end_time` FROM `product_order_relation_count` t WHERE relation_type = ? AND relation_id = ? AND modal_name IN ( ? , ? , ? , ? , ? , ? ) ORDER BY id ASC 
[09:56:21:378] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: P(String), 37377(Integer), VPWAD(String), PDDS(String), VADVIPC1(String), VADVIPC2(String), VADVIPC3(String), VADVIPC4(String), VADVIPC5(String), VADVIPC6(String)
[09:56:21:382] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: P(String), 37377(Integer), VADVIPC1(String), VADVIPC2(String), VADVIPC3(String), VADVIPC4(String), VADVIPC5(String), VADVIPC6(String)
[09:56:21:421] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 8
[09:56:21:423] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[09:56:21:429] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 3
[09:56:21:429] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[09:56:21:466] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select type,child_type as childType, count(*) as num from question where enabled=1 and company_id=? and creater_id=? group by type,child_type 
[09:56:21:470] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer)
[09:56:21:470] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:21:471] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[09:56:21:519] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 7
[09:56:21:520] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[09:56:21:569] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select count(distinct(category)) as num from question where enabled=1 and company_id=? and creater_id=? 
[09:56:21:572] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer)
[09:56:21:628] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:21:629] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[09:56:21:633] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select count(*) as totalPracticeTimes, count(distinct t.user_id) as totalUserNum, sum(t.total_right_times) as totalRightTimes,sum(t.total_wrong_times) as totalWrongTimes, sum(t.duration) as totalDuration from exercise_book_practice_summary t left join exercise_book t1 ON t1.id=t.exercise_book_id where t1.enabled=1 and t1.company_id=? and t1.creater_id=? 
[09:56:21:634] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer)
[09:56:21:736] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:21:738] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[09:56:21:774] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select type, count(*) as num from exercise_book where enabled=1 and company_id=? and creater_id in( ? ) group by type 
[09:56:21:778] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer)
[09:56:21:816] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 2
[09:56:21:820] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[09:56:21:841] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select DATE_FORMAT(t.end_date, '%Y-%m-%d') day, count(distinct(t.user_id)) as userNum,count(*) as times from exercise_book_practice_summary t left join exercise_book t1 ON t1.id= t.exercise_book_id where t1.enabled=1 and t1.company_id=? and t1.creater_id in( ? ) group by day order by day desc limit ? 
[09:56:21:843] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer), 20(Integer)
[09:56:21:894] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 6
[09:56:21:895] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[09:56:21:953] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT count(*) FROM `examination` t WHERE enabled = ? AND company_id = ? AND creater_id IN ( ? ) 
[09:56:21:955] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 0(Integer), 37377(Integer)
[09:56:22:000] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:22:000] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[09:56:22:006] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select count(distinct t.user_id) as totalUserNum,count(*) as totalTimes, sum(if(t.if_pass = true, 1, 0)) as totalPassed,sum(if(t.if_pass = false, 1, 0)) as totalFailed from examination_instance t left join examination t1 ON t1.id=t.examination_id where t.deleted_by_admin = 0 and t.enabled=1 and t1.company_id=? and t1.creater_id in( ? ) 
[09:56:22:006] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer)
[09:56:22:073] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:22:074] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[09:56:22:104] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select DATE_FORMAT(t.create_time, '%Y-%m-%d') day, count(distinct(t.user_id)) as userNum,count(*) as times from examination_instance t left join examination t1 ON t1.id=t.examination_id where t.enabled=1 and t.deleted_by_admin=0 and t1.company_id=? and t1.creater_id in( ? ) group by day order by day desc limit ? 
[09:56:22:104] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer), 20(Integer)
[09:56:22:169] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 2
[09:56:22:169] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[09:56:28:942] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /category/getNLevelCategory, authentication required: false
[09:56:28:942] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /question/getQuestionTypeAndCategoryAndTotalNum, authentication required: false
[09:56:28:942] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /category/getNLevelCategory
[09:56:28:942] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /question/getQuestionTypeAndCategoryAndTotalNum
[09:56:28:943] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /question/getQuestionTypeAndCategoryAndTotalNum
[09:56:28:943] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /category/getNLevelCategory
[09:56:28:954] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /questionSummary/getWrongQuestionList, authentication required: false
[09:56:28:955] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /questionSummary/getWrongQuestionList
[09:56:28:955] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /questionSummary/getWrongQuestionList
[09:56:29:021] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`level` , t.`user_id` , t.`space_name` , t.`name` , t.`sequence_num` , t.`parent_id` , t.`enabled` FROM `category` t WHERE space_name = ? AND user_id = ? AND level <= ? AND enabled = ? ORDER BY sequence_num ASC 
[09:56:29:022] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: eteaQB(String), 37377(Integer), 2(Integer), true(Boolean)
[09:56:29:027] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select count(*) from question_summary t left join question t1 ON t1.id=t.question_id left join question_bank_question t2 ON t2.question_id =t.question_id where t1.enabled=true and t1.company_id=? and t1.creater_id=? 
[09:56:29:028] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer)
[09:56:29:028] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t2.name as questionBankName, t1.question_bank_id as questionBankId,t.type,t.child_type as childType,CASE WHEN t.category IS NULL OR t.category = '' THEN '' ELSE t.category END as category,count(*) as totalNum,t.creater_id as createrId from question t left join question_bank_question t1 ON t.id = t1.question_id left join category t2 ON t2.id=t1.question_bank_id where t.enabled=1 and t.creater_id in ( ? ) and t.company_id=? Group by t1.question_bank_id, t.type,t.child_type,CASE WHEN t.category IS NULL OR t.category = '' THEN '' ELSE t.category END 
[09:56:29:029] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 37377(Integer), 0(Integer)
[09:56:29:119] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:29:119] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[09:56:29:121] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 17
[09:56:29:122] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[09:56:29:124] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.question_id as questionId,t.right_times_from_exam as rightTimesFromExam,t.wrong_times_from_exam as wrongTimesFromExam,t.right_times_from_practice as rightTimesFromPractice,t.wrong_times_from_practice as wrongTimesFromPractice,(t.wrong_times_from_exam + t.wrong_times_from_practice) as totalWrongTimes,t1.type,t1.main_content as mainContent,t1.child_type as childType,t1.category from question_summary t left join question t1 ON t1.id=t.question_id left join question_bank_question t2 ON t2.question_id =t.question_id where t1.enabled=true and t1.company_id=? and t1.creater_id=? order by totalWrongTimes desc limit ?,? 
[09:56:29:128] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 2
[09:56:29:128] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer), 0(Integer), 50(Integer)
[09:56:29:134] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[09:56:29:211] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 32
[09:56:29:212] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[09:56:34:416] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /wordFile/exportWrongQuestions, authentication required: false
[09:56:34:422] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /wordFile/exportWrongQuestions
[09:56:34:422] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /wordFile/exportWrongQuestions
[09:56:34:445] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:34:446] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336951(Integer)
[09:56:34:488] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:34:488] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[09:56:34:536] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:34:536] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336951(Integer)
[09:56:34:587] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:34:589] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[09:56:34:602] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:34:603] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333450(Integer)
[09:56:34:664] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:34:669] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:34:669] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333450(Integer)
[09:56:34:703] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:34:704] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:34:705] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336994(Integer)
[09:56:34:741] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:34:746] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:34:747] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336994(Integer)
[09:56:34:791] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:34:792] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:34:792] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333427(Integer)
[09:56:34:861] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:34:862] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:34:863] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333427(Integer)
[09:56:34:903] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:34:904] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:34:904] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336972(Integer)
[09:56:34:938] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:34:944] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:34:944] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336972(Integer)
[09:56:35:054] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:35:057] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:35:061] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336982(Integer)
[09:56:35:097] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:35:100] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:35:101] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336982(Integer)
[09:56:35:150] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:35:151] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:35:151] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336980(Integer)
[09:56:35:223] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:35:235] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:35:236] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336980(Integer)
[09:56:35:293] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:35:294] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:35:295] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333461(Integer)
[09:56:35:351] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:35:361] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:35:361] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333461(Integer)
[09:56:35:438] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:35:454] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:35:455] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333444(Integer)
[09:56:35:511] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:35:514] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:35:516] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333444(Integer)
[09:56:35:608] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:35:613] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:35:613] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333464(Integer)
[09:56:35:660] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:35:670] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:35:670] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333464(Integer)
[09:56:35:742] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:35:747] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:35:748] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336961(Integer)
[09:56:35:780] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:35:788] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:35:788] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336961(Integer)
[09:56:35:827] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:35:831] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:35:831] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336979(Integer)
[09:56:35:891] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:35:896] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:35:897] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336979(Integer)
[09:56:35:957] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:35:961] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:35:961] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336985(Integer)
[09:56:36:007] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:36:010] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:36:010] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336985(Integer)
[09:56:36:066] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:36:067] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:36:067] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336977(Integer)
[09:56:36:108] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:36:113] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:36:114] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336977(Integer)
[09:56:36:168] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:36:169] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:36:169] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333442(Integer)
[09:56:36:204] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:36:209] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:36:210] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333442(Integer)
[09:56:36:273] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:36:276] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:36:277] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333458(Integer)
[09:56:36:328] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:36:339] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:36:339] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333458(Integer)
[09:56:36:407] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:36:415] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:36:416] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336952(Integer)
[09:56:36:465] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:36:474] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:36:475] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336952(Integer)
[09:56:36:538] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:36:540] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:36:541] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336949(Integer)
[09:56:36:587] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:36:597] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:36:598] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336949(Integer)
[09:56:36:642] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:36:646] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:36:647] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336940(Integer)
[09:56:36:699] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:36:705] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:36:705] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336940(Integer)
[09:56:36:754] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:36:758] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:36:759] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336975(Integer)
[09:56:36:800] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:36:815] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:36:815] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336975(Integer)
[09:56:36:867] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:36:874] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:36:875] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336984(Integer)
[09:56:36:924] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:36:931] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:36:931] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336984(Integer)
[09:56:36:996] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:37:001] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:37:001] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336938(Integer)
[09:56:37:059] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:37:070] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:37:071] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336938(Integer)
[09:56:37:151] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:37:152] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:37:152] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336947(Integer)
[09:56:37:224] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:37:227] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:37:227] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336947(Integer)
[09:56:37:282] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:37:287] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:37:288] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336973(Integer)
[09:56:37:334] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:37:337] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:37:337] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336973(Integer)
[09:56:37:386] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:37:387] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:37:388] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336988(Integer)
[09:56:37:444] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:37:450] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:37:451] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336988(Integer)
[09:56:37:508] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:37:510] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:37:511] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336944(Integer)
[09:56:37:565] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:37:568] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:37:569] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336944(Integer)
[09:56:37:611] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:37:612] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:37:613] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333465(Integer)
[09:56:37:753] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:37:761] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:37:762] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333465(Integer)
[09:56:37:835] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:37:838] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:37:838] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336970(Integer)
[09:56:37:891] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:37:906] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:37:907] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336970(Integer)
[09:56:37:954] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:37:955] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:37:956] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336986(Integer)
[09:56:38:015] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:38:027] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:38:028] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336986(Integer)
[09:56:38:088] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:38:090] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:38:090] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336976(Integer)
[09:56:38:138] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:38:141] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:38:141] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336976(Integer)
[09:56:38:193] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:38:194] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:38:195] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333430(Integer)
[09:56:38:250] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:38:252] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:38:252] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333430(Integer)
[09:56:38:304] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:38:305] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:56:38:306] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333446(Integer)
[09:56:38:350] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:56:38:353] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:56:38:353] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333446(Integer)
[09:56:38:410] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:56:39:009] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@5a541e59
[09:56:39:018] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@5a541e59
[09:56:39:023] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@21ceedc7
[09:56:39:024] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@21ceedc7
[09:56:39:029] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@3405f657
[09:56:39:032] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@3405f657
[09:56:39:036] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@3405f657
[09:56:39:039] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@1558b228
[09:56:39:040] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@1558b228
[09:56:39:043] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@72a3da9a
[09:56:39:044] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@72a3da9a
[09:56:39:045] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@72a3da9a
[09:56:39:045] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@7578a6c3
[09:56:39:046] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@7578a6c3
[09:56:39:048] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@7578a6c3
[09:56:39:049] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@ce635a0
[09:56:39:054] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@ce635a0
[09:56:39:067] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@5c7f1fe6
[09:56:39:069] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@5c7f1fe6
[09:56:39:073] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@5c7f1fe6
[09:56:39:078] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@30e479f9
[09:56:39:082] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@30e479f9
[09:56:39:097] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@1a8f98
[09:56:39:098] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@1a8f98
[09:56:39:101] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@1a8f98
[09:56:39:103] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@3dece10e
[09:56:39:105] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@3dece10e
[09:56:39:110] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@3dece10e
[09:56:39:114] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@1948497a
[09:56:39:117] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@1948497a
[09:56:39:123] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@1948497a
[09:56:39:127] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@a70d01b
[09:56:39:135] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@a70d01b
[09:56:39:142] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@5e42f882
[09:56:39:149] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@4fc86d5
[09:56:39:158] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@5fa41ca9
[09:56:39:166] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@53d8ef4e
[09:57:02:291] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /wordFile/exportWrongQuestions, authentication required: false
[09:57:02:300] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /wordFile/exportWrongQuestions
[09:57:02:300] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /wordFile/exportWrongQuestions
[09:57:02:374] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:02:375] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336951(Integer)
[09:57:02:427] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:02:441] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:02:441] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336951(Integer)
[09:57:02:501] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:02:503] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:02:504] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333450(Integer)
[09:57:02:538] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:02:540] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:02:541] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333450(Integer)
[09:57:02:592] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:02:594] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:02:594] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336994(Integer)
[09:57:02:634] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:02:637] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:02:638] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336994(Integer)
[09:57:02:676] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:02:680] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:02:681] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333427(Integer)
[09:57:02:742] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:02:746] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:02:746] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333427(Integer)
[09:57:02:797] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:02:799] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:02:799] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336972(Integer)
[09:57:02:845] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:02:854] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:02:854] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336972(Integer)
[09:57:02:904] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:02:911] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:02:911] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336982(Integer)
[09:57:02:974] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:02:984] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:02:985] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336982(Integer)
[09:57:03:047] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:03:052] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:03:053] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336980(Integer)
[09:57:03:095] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:03:106] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:03:108] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336980(Integer)
[09:57:03:156] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:03:160] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:03:160] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333461(Integer)
[09:57:03:213] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:03:219] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:03:220] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333461(Integer)
[09:57:03:256] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:03:257] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:03:257] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333444(Integer)
[09:57:03:296] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:03:298] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:03:299] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333444(Integer)
[09:57:03:364] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:03:365] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:03:366] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333464(Integer)
[09:57:03:445] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:03:446] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:03:446] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333464(Integer)
[09:57:03:506] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:03:506] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:03:507] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336961(Integer)
[09:57:03:563] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:03:564] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:03:565] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336961(Integer)
[09:57:03:601] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:03:602] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:03:602] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336979(Integer)
[09:57:03:653] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:03:665] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:03:665] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336979(Integer)
[09:57:03:721] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:03:722] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:03:723] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336985(Integer)
[09:57:03:775] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:03:778] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:03:779] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336985(Integer)
[09:57:03:826] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:03:827] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:03:828] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336977(Integer)
[09:57:03:868] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:03:872] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:03:872] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336977(Integer)
[09:57:03:915] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:03:918] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:03:918] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333442(Integer)
[09:57:03:968] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:03:976] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:03:978] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333442(Integer)
[09:57:04:023] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:04:025] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:04:026] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333458(Integer)
[09:57:04:070] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:04:078] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:04:078] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333458(Integer)
[09:57:04:139] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:04:140] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:04:140] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336952(Integer)
[09:57:04:183] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:04:185] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:04:186] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336952(Integer)
[09:57:04:238] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:04:241] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:04:242] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336949(Integer)
[09:57:04:291] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:04:294] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:04:295] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336949(Integer)
[09:57:04:351] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:04:354] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:04:355] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336940(Integer)
[09:57:04:415] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:04:422] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:04:422] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336940(Integer)
[09:57:04:498] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:04:500] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:04:500] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336975(Integer)
[09:57:04:558] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:04:560] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:04:560] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336975(Integer)
[09:57:04:614] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:04:616] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:04:616] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336984(Integer)
[09:57:04:651] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:04:656] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:04:659] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336984(Integer)
[09:57:04:719] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:04:720] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:04:720] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336938(Integer)
[09:57:04:767] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:04:769] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:04:770] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336938(Integer)
[09:57:04:831] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:04:832] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:04:832] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336947(Integer)
[09:57:04:888] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:04:891] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:04:891] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336947(Integer)
[09:57:04:946] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:04:949] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:04:949] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336973(Integer)
[09:57:05:028] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:05:030] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:05:030] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336973(Integer)
[09:57:05:104] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:05:105] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:05:106] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336988(Integer)
[09:57:05:146] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:05:149] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:05:150] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336988(Integer)
[09:57:05:222] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:05:223] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:05:223] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336944(Integer)
[09:57:05:283] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:05:284] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:05:284] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336944(Integer)
[09:57:05:341] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:05:342] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:05:343] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333465(Integer)
[09:57:05:391] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:05:394] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:05:395] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333465(Integer)
[09:57:05:439] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:05:441] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:05:442] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336970(Integer)
[09:57:05:486] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:05:488] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:05:488] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336970(Integer)
[09:57:05:549] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:05:550] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:05:550] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336986(Integer)
[09:57:05:599] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:05:600] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:05:601] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336986(Integer)
[09:57:05:664] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:05:666] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:05:666] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336976(Integer)
[09:57:05:700] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:05:702] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:05:704] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336976(Integer)
[09:57:05:760] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:05:762] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:05:762] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333430(Integer)
[09:57:05:824] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:05:830] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:05:830] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333430(Integer)
[09:57:05:882] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:05:886] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[09:57:05:887] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333446(Integer)
[09:57:05:941] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[09:57:05:946] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[09:57:05:947] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333446(Integer)
[09:57:05:996] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[09:57:06:043] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@64f8a465
[09:57:06:044] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@64f8a465
[09:57:06:044] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@4e14d89d
[09:57:06:045] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@4e14d89d
[09:57:06:053] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@56342e03
[09:57:06:054] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@56342e03
[09:57:06:055] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@56342e03
[09:57:06:056] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@612bb64c
[09:57:06:058] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@612bb64c
[09:57:06:062] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@40d1fd80
[09:57:06:066] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@40d1fd80
[09:57:06:066] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@40d1fd80
[09:57:06:067] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@28690537
[09:57:06:067] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@28690537
[09:57:06:068] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@28690537
[09:57:06:069] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@5f5a620f
[09:57:06:070] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@5f5a620f
[09:57:06:070] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@637f367e
[09:57:06:071] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@637f367e
[09:57:06:072] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@637f367e
[09:57:06:074] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@2c0dfa6c
[09:57:06:075] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@2c0dfa6c
[09:57:06:076] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@4cad0267
[09:57:06:076] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@4cad0267
[09:57:06:077] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@4cad0267
[09:57:06:079] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@40eb4be7
[09:57:06:081] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@40eb4be7
[09:57:06:081] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@40eb4be7
[09:57:06:082] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@5abd5942
[09:57:06:083] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@5abd5942
[09:57:06:088] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@5abd5942
[09:57:06:091] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@192016e3
[09:57:06:091] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@192016e3
[09:57:06:101] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@3d3a919a
[09:57:06:102] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@271441a9
[09:57:06:107] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@17d37b40
[09:57:06:108] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@e5df057
[09:57:29:222] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /192.168.8.183:61535
[09:57:29:242] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /192.168.8.183:61512
[09:57:29:274] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /192.168.8.183:61537
[09:57:39:618] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /192.168.8.183:61511
[09:58:06:266] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /192.168.8.183:61538
[10:00:18:876] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /questionSummary/getWrongQuestionList, authentication required: false
[10:00:18:883] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /questionSummary/getWrongQuestionList
[10:00:18:883] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /questionSummary/getWrongQuestionList
[10:00:18:985] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select count(*) from question_summary t left join question t1 ON t1.id=t.question_id left join question_bank_question t2 ON t2.question_id =t.question_id where t1.enabled=true and t1.company_id=? and t1.creater_id=? 
[10:00:18:988] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer)
[10:00:19:070] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:19:071] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:00:19:072] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.question_id as questionId,t.right_times_from_exam as rightTimesFromExam,t.wrong_times_from_exam as wrongTimesFromExam,t.right_times_from_practice as rightTimesFromPractice,t.wrong_times_from_practice as wrongTimesFromPractice,(t.wrong_times_from_exam + t.wrong_times_from_practice) as totalWrongTimes,t1.type,t1.main_content as mainContent,t1.child_type as childType,t1.category from question_summary t left join question t1 ON t1.id=t.question_id left join question_bank_question t2 ON t2.question_id =t.question_id where t1.enabled=true and t1.company_id=? and t1.creater_id=? order by totalWrongTimes desc limit ?,? 
[10:00:19:072] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer), 0(Integer), 50(Integer)
[10:00:19:159] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 32
[10:00:23:494] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /questionSummary/getWrongQuestionList, authentication required: false
[10:00:23:494] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /questionSummary/getWrongQuestionList
[10:00:23:494] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /questionSummary/getWrongQuestionList
[10:00:23:497] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select count(*) from question_summary t left join question t1 ON t1.id=t.question_id left join question_bank_question t2 ON t2.question_id =t.question_id where t1.enabled=true and t1.company_id=? and t1.creater_id=? 
[10:00:23:498] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer)
[10:00:23:599] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:23:600] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.question_id as questionId,t.right_times_from_exam as rightTimesFromExam,t.wrong_times_from_exam as wrongTimesFromExam,t.right_times_from_practice as rightTimesFromPractice,t.wrong_times_from_practice as wrongTimesFromPractice,(t.wrong_times_from_exam + t.wrong_times_from_practice) as totalWrongTimes,t1.type,t1.main_content as mainContent,t1.child_type as childType,t1.category from question_summary t left join question t1 ON t1.id=t.question_id left join question_bank_question t2 ON t2.question_id =t.question_id where t1.enabled=true and t1.company_id=? and t1.creater_id=? order by totalWrongTimes desc limit ?,? 
[10:00:23:600] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer), 0(Integer), 50(Integer)
[10:00:23:674] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 32
[10:00:28:162] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /product/getProductList, authentication required: false
[10:00:28:167] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /product/getProductList
[10:00:28:167] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /product/getProductList
[10:00:28:188] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /product/getListOfProduct, authentication required: false
[10:00:28:188] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /category/getNLevelCategory, authentication required: false
[10:00:28:193] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /category/getNLevelCategory
[10:00:28:193] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /product/getListOfProduct
[10:00:28:193] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /category/getNLevelCategory
[10:00:28:194] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /product/getListOfProduct
[10:00:28:193] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /productOrderRelationCount/getProductOrderRelationCountList, authentication required: false
[10:00:28:194] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /productOrderRelationCount/getProductOrderRelationCountList
[10:00:28:196] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /productOrderRelationCount/getProductOrderRelationCountList
[10:00:28:202] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /question/getQuestionTypeAndCategoryAndTotalNum, authentication required: false
[10:00:28:202] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /question/getQuestionTypeAndCategoryAndTotalNum
[10:00:28:202] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /question/getQuestionTypeAndCategoryAndTotalNum
[10:00:28:207] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`type` , t.`code` , t.`name` , t.`short_description` , t.`long_description` , t.`max_examinee_num` , t.`valid_days` , t.`price` FROM `product` t WHERE type = ? 
[10:00:28:217] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: P(String)
[10:00:28:209] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /productOrderRelationCount/getProductOrderRelationCountList, authentication required: false
[10:00:28:226] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /productOrderRelationCount/getProductOrderRelationCountList
[10:00:28:228] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /productOrderRelationCount/getProductOrderRelationCountList
[10:00:28:268] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t2.name as questionBankName, t1.question_bank_id as questionBankId,t.type,t.child_type as childType,CASE WHEN t.category IS NULL OR t.category = '' THEN '' ELSE t.category END as category,count(*) as totalNum,t.creater_id as createrId from question t left join question_bank_question t1 ON t.id = t1.question_id left join category t2 ON t2.id=t1.question_bank_id where t.enabled=1 and t.creater_id in ( ? ) and t.company_id=? Group by t1.question_bank_id, t.type,t.child_type,CASE WHEN t.category IS NULL OR t.category = '' THEN '' ELSE t.category END 
[10:00:28:268] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`level` , t.`user_id` , t.`space_name` , t.`name` , t.`sequence_num` , t.`parent_id` , t.`enabled` FROM `category` t WHERE space_name = ? AND user_id = ? AND level <= ? AND enabled = ? ORDER BY sequence_num ASC 
[10:00:28:268] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`relation_type` , t.`relation_id` , t.`modal_name` , t.`available_times` , t.`end_time` FROM `product_order_relation_count` t WHERE relation_type = ? AND relation_id = ? AND modal_name IN ( ? , ? , ? , ? , ? , ? , ? , ? ) ORDER BY id ASC 
[10:00:28:273] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: eteaQB(String), 37377(Integer), 2(Integer), true(Boolean)
[10:00:28:311] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: P(String), 37377(Integer), VPWAD(String), PDDS(String), VADVIPC1(String), VADVIPC2(String), VADVIPC3(String), VADVIPC4(String), VADVIPC5(String), VADVIPC6(String)
[10:00:28:311] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 37377(Integer), 0(Integer)
[10:00:28:331] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`relation_type` , t.`relation_id` , t.`modal_name` , t.`available_times` , t.`end_time` FROM `product_order_relation_count` t WHERE relation_type = ? AND relation_id = ? AND modal_name IN ( ? , ? , ? , ? , ? , ? ) ORDER BY id ASC 
[10:00:28:335] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: P(String), 37377(Integer), VADVIPC1(String), VADVIPC2(String), VADVIPC3(String), VADVIPC4(String), VADVIPC5(String), VADVIPC6(String)
[10:00:28:341] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 8
[10:00:28:343] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:00:28:374] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /questionSummary/getWrongQuestionList, authentication required: false
[10:00:28:375] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /questionSummary/getWrongQuestionList
[10:00:28:375] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /questionSummary/getWrongQuestionList
[10:00:28:387] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select count(*) from question_summary t left join question t1 ON t1.id=t.question_id left join question_bank_question t2 ON t2.question_id =t.question_id where t1.enabled=true and t1.company_id=? and t1.creater_id=? 
[10:00:28:388] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer)
[10:00:28:403] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:28:403] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 3
[10:00:28:405] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:00:28:405] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:00:28:407] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 2
[10:00:28:407] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 17
[10:00:28:408] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:00:28:430] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:28:431] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.question_id as questionId,t.right_times_from_exam as rightTimesFromExam,t.wrong_times_from_exam as wrongTimesFromExam,t.right_times_from_practice as rightTimesFromPractice,t.wrong_times_from_practice as wrongTimesFromPractice,(t.wrong_times_from_exam + t.wrong_times_from_practice) as totalWrongTimes,t1.type,t1.main_content as mainContent,t1.child_type as childType,t1.category from question_summary t left join question t1 ON t1.id=t.question_id left join question_bank_question t2 ON t2.question_id =t.question_id where t1.enabled=true and t1.company_id=? and t1.creater_id=? order by totalWrongTimes desc limit ?,? 
[10:00:28:432] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer), 0(Integer), 50(Integer)
[10:00:28:493] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 32
[10:00:33:617] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /wordFile/exportWrongQuestions, authentication required: false
[10:00:33:617] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /wordFile/exportWrongQuestions
[10:00:33:617] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /wordFile/exportWrongQuestions
[10:00:33:621] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:33:621] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336951(Integer)
[10:00:33:665] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:33:673] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:33:673] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336951(Integer)
[10:00:33:748] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:33:750] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:33:750] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333450(Integer)
[10:00:33:821] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:33:824] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:33:825] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333450(Integer)
[10:00:33:871] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:33:873] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:33:873] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336994(Integer)
[10:00:33:919] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:33:922] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:33:923] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336994(Integer)
[10:00:34:042] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:34:046] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:34:047] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333427(Integer)
[10:00:34:115] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:34:121] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:34:122] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333427(Integer)
[10:00:34:216] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:34:219] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:34:220] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336972(Integer)
[10:00:34:275] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:34:279] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:34:279] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336972(Integer)
[10:00:34:334] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:34:336] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:34:336] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336982(Integer)
[10:00:34:374] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:34:376] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:34:377] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336982(Integer)
[10:00:34:421] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:34:423] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:34:424] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336980(Integer)
[10:00:34:486] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:34:490] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:34:491] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336980(Integer)
[10:00:34:548] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:34:552] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:34:554] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333461(Integer)
[10:00:34:639] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:34:642] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:34:643] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333461(Integer)
[10:00:34:708] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:34:710] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:34:711] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333444(Integer)
[10:00:34:744] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:34:746] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:34:747] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333444(Integer)
[10:00:34:813] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:34:815] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:34:818] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333464(Integer)
[10:00:34:858] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:34:861] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:34:862] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333464(Integer)
[10:00:34:906] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:34:909] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:34:909] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336961(Integer)
[10:00:34:962] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:34:964] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:34:964] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336961(Integer)
[10:00:35:010] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:35:012] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:35:012] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336979(Integer)
[10:00:35:067] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:35:068] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:35:069] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336979(Integer)
[10:00:35:194] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:35:198] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:35:198] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336985(Integer)
[10:00:35:273] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:35:279] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:35:280] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336985(Integer)
[10:00:35:349] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:35:351] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:35:352] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336977(Integer)
[10:00:35:423] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:35:425] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:35:426] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336977(Integer)
[10:00:35:519] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:35:519] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:35:520] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333442(Integer)
[10:00:35:588] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:35:594] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:35:595] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333442(Integer)
[10:00:35:682] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:35:687] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:35:687] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333458(Integer)
[10:00:35:739] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:35:747] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:35:748] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333458(Integer)
[10:00:35:823] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:35:826] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:35:827] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336952(Integer)
[10:00:35:858] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:35:863] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:35:864] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336952(Integer)
[10:00:35:911] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:35:913] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:35:914] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336949(Integer)
[10:00:35:986] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:35:998] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:35:999] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336949(Integer)
[10:00:36:086] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:36:089] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:36:090] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336940(Integer)
[10:00:36:151] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:36:155] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:36:156] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336940(Integer)
[10:00:36:231] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:36:233] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:36:233] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336975(Integer)
[10:00:36:281] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:36:291] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:36:291] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336975(Integer)
[10:00:36:338] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:36:341] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:36:341] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336984(Integer)
[10:00:36:373] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:36:375] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:36:375] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336984(Integer)
[10:00:36:430] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:36:431] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:36:432] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336938(Integer)
[10:00:36:476] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:36:479] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:36:480] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336938(Integer)
[10:00:36:539] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:36:543] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:36:543] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336947(Integer)
[10:00:36:597] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:36:608] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:36:608] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336947(Integer)
[10:00:36:663] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:36:665] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:36:666] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336973(Integer)
[10:00:36:714] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:36:721] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:36:722] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336973(Integer)
[10:00:36:793] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:36:795] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:36:795] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336988(Integer)
[10:00:36:836] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:36:840] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:36:840] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336988(Integer)
[10:00:36:920] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:36:921] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:36:921] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336944(Integer)
[10:00:36:967] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:36:969] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:36:969] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336944(Integer)
[10:00:37:024] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:37:026] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:37:027] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333465(Integer)
[10:00:37:099] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:37:100] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:37:100] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333465(Integer)
[10:00:37:151] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:37:155] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:37:155] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336970(Integer)
[10:00:37:194] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:37:195] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:37:196] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336970(Integer)
[10:00:37:247] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:37:248] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:37:248] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336986(Integer)
[10:00:37:300] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:37:302] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:37:303] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336986(Integer)
[10:00:37:357] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:37:358] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:37:358] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336976(Integer)
[10:00:37:406] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:37:411] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:37:411] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22336976(Integer)
[10:00:37:474] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:37:477] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:37:477] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333430(Integer)
[10:00:37:530] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:37:531] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:37:532] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333430(Integer)
[10:00:37:582] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:37:583] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.*,(t1.wrong_times_from_exam + t1.wrong_times_from_practice) as total_wrong_num,(t1.right_times_from_exam+t1.right_times_from_practice) as total_right_num from question t LEFT JOIN question_summary t1 ON t1.question_id=t.id where t.id=? 
[10:00:37:584] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333446(Integer)
[10:00:37:627] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:00:37:629] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`attachment_id` , t.`attachment_name` , t.`attachment_type` , t.`question_id` , t.`sequence_no` , t.`extra` , t.`purpose` , t.`object_name` , t.`composition_child_id` , t.`uri` FROM `question_attachment` t WHERE question_id = ? AND (composition_child_id IS NULL) 
[10:00:37:629] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 22333446(Integer)
[10:00:37:712] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:00:37:779] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@60a167ab
[10:00:37:779] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@60a167ab
[10:00:37:780] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@68e0c4ab
[10:00:37:780] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@68e0c4ab
[10:00:37:781] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@1c18276e
[10:00:37:781] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@1c18276e
[10:00:37:782] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@1c18276e
[10:00:37:784] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@30961cbe
[10:00:37:786] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@30961cbe
[10:00:37:790] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@5b61b938
[10:00:37:790] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@5b61b938
[10:00:37:790] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@5b61b938
[10:00:37:791] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@354aeb63
[10:00:37:791] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@354aeb63
[10:00:37:794] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@354aeb63
[10:00:37:795] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@683489f3
[10:00:37:795] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@683489f3
[10:00:37:796] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@68c70ba7
[10:00:37:796] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@68c70ba7
[10:00:37:796] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@68c70ba7
[10:00:37:797] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@3e39d7fe
[10:00:37:797] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@3e39d7fe
[10:00:37:797] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@5cca32a9
[10:00:37:798] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@5cca32a9
[10:00:37:798] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@5cca32a9
[10:00:37:798] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@10fd43d2
[10:00:37:799] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@10fd43d2
[10:00:37:799] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@10fd43d2
[10:00:37:799] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@4037f859
[10:00:37:803] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@4037f859
[10:00:37:807] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@4037f859
[10:00:37:809] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@30414186
[10:00:37:809] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@30414186
[10:00:37:810] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@3d93625d
[10:00:37:811] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@470a45ee
[10:00:37:818] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@38809866
[10:00:37:820] [DEBUG] - com.taurus.examinationassistant.service.ExaminationService.exportQuestions(ExaminationService.java:1701) - com.taurus.examinationassistant.entity.business.QuestionWrapper@2f7b381
[10:01:28:465] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /192.168.8.183:61766
[10:01:28:465] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /192.168.8.183:61769
[10:01:28:469] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /192.168.8.183:61767
[10:01:28:472] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /192.168.8.183:61770
[10:01:28:552] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /192.168.8.183:61741
[10:01:37:945] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /192.168.8.183:61768
[10:11:42:269] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /questionSummary/getWrongQuestionList, authentication required: false
[10:11:42:278] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /questionSummary/getWrongQuestionList
[10:11:42:281] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /questionSummary/getWrongQuestionList
[10:11:42:385] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select count(*) from question_summary t left join question t1 ON t1.id=t.question_id left join question_bank_question t2 ON t2.question_id =t.question_id where t1.enabled=true and t1.company_id=? and t1.creater_id=? 
[10:11:42:390] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer)
[10:11:42:537] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:11:42:540] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.question_id as questionId,t.right_times_from_exam as rightTimesFromExam,t.wrong_times_from_exam as wrongTimesFromExam,t.right_times_from_practice as rightTimesFromPractice,t.wrong_times_from_practice as wrongTimesFromPractice,(t.wrong_times_from_exam + t.wrong_times_from_practice) as totalWrongTimes,t1.type,t1.main_content as mainContent,t1.child_type as childType,t1.category from question_summary t left join question t1 ON t1.id=t.question_id left join question_bank_question t2 ON t2.question_id =t.question_id where t1.enabled=true and t1.company_id=? and t1.creater_id=? order by totalWrongTimes desc limit ?,? 
[10:11:42:540] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer), 0(Integer), 50(Integer)
[10:11:42:735] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 32
[10:27:14:754] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x2386bc48, L:/192.168.8.183:61418 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[10:27:14:850] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xadfe7ed0, L:/192.168.8.183:61419 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[10:27:14:851] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@669374998 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2386bc48, L:/192.168.8.183:61418 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[10:27:14:871] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[10:27:14:941] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x2bb82127, L:/192.168.8.183:61421 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[10:27:14:943] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xcda55fd6, L:/192.168.8.183:61422 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[10:27:14:943] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1038102791 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xadfe7ed0, L:/192.168.8.183:61419 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[10:27:14:947] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[10:27:15:002] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@669374998 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2386bc48, L:/192.168.8.183:61418 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[10:27:15:035] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x6b01af27, L:/192.168.8.183:61401 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[10:27:15:035] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xfcfd83b9, L:/192.168.8.183:61400 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[10:27:15:035] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x7a727f55, L:/192.168.8.183:61399 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[10:27:15:035] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x79439fe9, L:/192.168.8.183:61423 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[10:27:15:035] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xa1b4eb5c, L:/192.168.8.183:61424 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[10:27:15:036] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@697999985 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2bb82127, L:/192.168.8.183:61421 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[10:27:15:036] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@372520242 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xcda55fd6, L:/192.168.8.183:61422 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[10:27:15:046] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[10:27:15:047] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[10:27:15:050] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1038102791 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xadfe7ed0, L:/192.168.8.183:61419 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[10:27:15:120] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@372520242 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xcda55fd6, L:/192.168.8.183:61422 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[10:27:15:132] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@697999985 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2bb82127, L:/192.168.8.183:61421 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[10:27:15:136] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xbcda18db, L:/192.168.8.183:61426 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[10:27:15:142] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x2693fdde, L:/192.168.8.183:61403 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[10:27:15:142] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x7c34b00d, L:/192.168.8.183:61402 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[10:27:15:142] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x93fecaa1, L:/192.168.8.183:61427 - R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[10:27:15:142] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1968256351 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xfcfd83b9, L:/192.168.8.183:61400 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[10:27:15:146] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@372390155 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xa1b4eb5c, L:/192.168.8.183:61424 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[10:27:15:147] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1497621981 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6b01af27, L:/192.168.8.183:61401 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[10:27:15:148] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@378468950 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x79439fe9, L:/192.168.8.183:61423 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[10:27:15:149] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisPubSubConnection@419719498 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7a727f55, L:/192.168.8.183:61399 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@681a8f5f[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0] to 47.111.231.172/47.111.231.172:6379 
[10:27:15:170] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[10:27:15:177] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[10:27:15:184] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[10:27:15:184] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[10:27:15:184] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[10:27:15:244] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xccb42c40, L:/192.168.8.183:61405 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[10:27:15:245] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x27dc158c, L:/192.168.8.183:61404 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[10:27:15:245] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@469848639 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xbcda18db, L:/192.168.8.183:61426 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[10:27:15:246] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@593676545 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2693fdde, L:/192.168.8.183:61403 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[10:27:15:247] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@483155522 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7c34b00d, L:/192.168.8.183:61402 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[10:27:15:250] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[10:27:15:254] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1048950339 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x93fecaa1, L:/192.168.8.183:61427 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[10:27:15:271] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1968256351 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xfcfd83b9, L:/192.168.8.183:61400 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[10:27:15:270] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@372390155 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xa1b4eb5c, L:/192.168.8.183:61424 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[10:27:15:272] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1497621981 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x6b01af27, L:/192.168.8.183:61401 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[10:27:15:278] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[10:27:15:280] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[10:27:15:280] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[10:27:15:283] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@378468950 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x79439fe9, L:/192.168.8.183:61423 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[10:27:15:297] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisPubSubConnection@419719498 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7a727f55, L:/192.168.8.183:61399 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@681a8f5f[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: CommandData [promise=java.util.concurrent.CompletableFuture@681a8f5f[Completed exceptionally], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec]
[10:27:15:338] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@469848639 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xbcda18db, L:/192.168.8.183:61426 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[10:27:15:342] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x5f990f89, L:/192.168.8.183:61406 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[10:27:15:344] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x14726fe2, L:/192.168.8.183:61407 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[10:27:15:345] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@477532454 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xccb42c40, L:/192.168.8.183:61405 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[10:27:15:348] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@483155522 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7c34b00d, L:/192.168.8.183:61402 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[10:27:15:348] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1048950339 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x93fecaa1, L:/192.168.8.183:61427 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[10:27:15:349] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1641702055 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x27dc158c, L:/192.168.8.183:61404 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[10:27:15:354] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[10:27:15:355] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[10:27:15:359] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@593676545 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x2693fdde, L:/192.168.8.183:61403 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[10:27:15:433] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1641702055 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x27dc158c, L:/192.168.8.183:61404 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[10:27:15:438] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xc927e4be, L:/192.168.8.183:61408 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[10:27:15:438] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x7e55193c, L:/192.168.8.183:61409 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[10:27:15:438] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@880663859 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x14726fe2, L:/192.168.8.183:61407 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[10:27:15:439] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@385431887 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x5f990f89, L:/192.168.8.183:61406 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[10:27:15:440] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[10:27:15:443] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[10:27:15:446] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@477532454 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xccb42c40, L:/192.168.8.183:61405 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[10:27:15:502] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@385431887 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x5f990f89, L:/192.168.8.183:61406 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[10:27:15:505] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@880663859 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x14726fe2, L:/192.168.8.183:61407 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[10:27:15:541] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x97c2767d, L:/192.168.8.183:61411 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[10:27:15:542] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x766cec9f, L:/192.168.8.183:61410 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[10:27:15:542] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1288582161 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xc927e4be, L:/192.168.8.183:61408 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[10:27:15:542] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@427876348 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7e55193c, L:/192.168.8.183:61409 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[10:27:15:545] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[10:27:15:553] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[10:27:15:622] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1288582161 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xc927e4be, L:/192.168.8.183:61408 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[10:27:15:622] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@427876348 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x7e55193c, L:/192.168.8.183:61409 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[10:27:15:642] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1256262216 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x97c2767d, L:/192.168.8.183:61411 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[10:27:15:643] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1593373923 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x766cec9f, L:/192.168.8.183:61410 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[10:27:15:644] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[10:27:15:646] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[10:27:15:736] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1256262216 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x97c2767d, L:/192.168.8.183:61411 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[10:27:15:736] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1593373923 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x766cec9f, L:/192.168.8.183:61410 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[10:27:15:737] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xcd560f0c, L:/192.168.8.183:61413 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[10:27:15:738] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x1bee8951, L:/192.168.8.183:61414 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[10:27:15:738] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x19a576c7, L:/192.168.8.183:61415 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[10:27:15:738] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x15b77ccc, L:/192.168.8.183:61416 ! R:47.111.231.172/47.111.231.172:6379] closed due to PING response timeout set in 1000 ms
[10:27:15:843] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1747717612 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xcd560f0c, L:/192.168.8.183:61413 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[10:27:15:851] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@612857137 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x1bee8951, L:/192.168.8.183:61414 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[10:27:15:860] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[10:27:15:861] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@383954872 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x19a576c7, L:/192.168.8.183:61415 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[10:27:15:870] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@716193739 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x15b77ccc, L:/192.168.8.183:61416 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] to 47.111.231.172/47.111.231.172:6379 
[10:27:15:879] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[10:27:15:885] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[10:27:15:886] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://47.111.231.172:6379]
[10:27:15:964] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@612857137 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x1bee8951, L:/192.168.8.183:61414 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[10:27:15:963] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@716193739 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x15b77ccc, L:/192.168.8.183:61416 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[10:27:15:964] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1747717612 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0xcd560f0c, L:/192.168.8.183:61413 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[10:27:15:975] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@383954872 [redisClient=[addr=redis://47.111.231.172:6379], channel=[id: 0x19a576c7, L:/192.168.8.183:61415 ! R:47.111.231.172/47.111.231.172:6379], currentCommand=null, usage=0] connected to 47.111.231.172/47.111.231.172:6379, command: null
[10:28:09:576] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /192.168.8.183:62536
[10:28:18:725] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /product/getProductList, authentication required: false
[10:28:18:724] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /productOrderRelationCount/getProductOrderRelationCountList, authentication required: false
[10:28:18:724] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /question/getQuestionTypeAndCategoryAndTotalNum, authentication required: false
[10:28:18:726] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /productOrderRelationCount/getProductOrderRelationCountList
[10:28:18:727] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /question/getQuestionTypeAndCategoryAndTotalNum
[10:28:18:724] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /category/getNLevelCategory, authentication required: false
[10:28:18:727] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /productOrderRelationCount/getProductOrderRelationCountList
[10:28:18:727] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /question/getQuestionTypeAndCategoryAndTotalNum
[10:28:18:724] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /productOrderRelationCount/getProductOrderRelationCountList, authentication required: false
[10:28:18:726] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /product/getListOfProduct, authentication required: false
[10:28:18:727] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /productOrderRelationCount/getProductOrderRelationCountList
[10:28:18:727] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /category/getNLevelCategory
[10:28:18:727] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /productOrderRelationCount/getProductOrderRelationCountList
[10:28:18:727] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /product/getProductList
[10:28:18:727] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /category/getNLevelCategory
[10:28:18:727] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /product/getListOfProduct
[10:28:18:727] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /product/getProductList
[10:28:18:727] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /product/getListOfProduct
[10:28:18:875] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`level` , t.`user_id` , t.`space_name` , t.`name` , t.`sequence_num` , t.`parent_id` , t.`enabled` FROM `category` t WHERE space_name = ? AND user_id = ? AND level <= ? AND enabled = ? ORDER BY sequence_num ASC 
[10:28:18:875] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`relation_type` , t.`relation_id` , t.`modal_name` , t.`available_times` , t.`end_time` FROM `product_order_relation_count` t WHERE relation_type = ? AND relation_id = ? AND modal_name IN ( ? , ? , ? , ? , ? , ? ) ORDER BY id ASC 
[10:28:18:874] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`type` , t.`code` , t.`name` , t.`short_description` , t.`long_description` , t.`max_examinee_num` , t.`valid_days` , t.`price` FROM `product` t WHERE type = ? 
[10:28:18:874] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`relation_type` , t.`relation_id` , t.`modal_name` , t.`available_times` , t.`end_time` FROM `product_order_relation_count` t WHERE relation_type = ? AND relation_id = ? AND modal_name IN ( ? , ? , ? , ? , ? , ? , ? , ? ) ORDER BY id ASC 
[10:28:18:882] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t2.name as questionBankName, t1.question_bank_id as questionBankId,t.type,t.child_type as childType,CASE WHEN t.category IS NULL OR t.category = '' THEN '' ELSE t.category END as category,count(*) as totalNum,t.creater_id as createrId from question t left join question_bank_question t1 ON t.id = t1.question_id left join category t2 ON t2.id=t1.question_bank_id where t.enabled=1 and t.creater_id in ( ? ) and t.company_id=? Group by t1.question_bank_id, t.type,t.child_type,CASE WHEN t.category IS NULL OR t.category = '' THEN '' ELSE t.category END 
[10:28:18:885] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: P(String), 37377(Integer), VADVIPC1(String), VADVIPC2(String), VADVIPC3(String), VADVIPC4(String), VADVIPC5(String), VADVIPC6(String)
[10:28:18:885] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: eteaQB(String), 37377(Integer), 2(Integer), true(Boolean)
[10:28:18:886] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: P(String)
[10:28:18:886] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 37377(Integer), 0(Integer)
[10:28:18:887] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: P(String), 37377(Integer), VPWAD(String), PDDS(String), VADVIPC1(String), VADVIPC2(String), VADVIPC3(String), VADVIPC4(String), VADVIPC5(String), VADVIPC6(String)
[10:28:18:959] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:28:18:959] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 2
[10:28:18:963] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 3
[10:28:18:964] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:28:18:964] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[10:28:18:966] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:28:18:967] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 17
[10:28:18:967] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 8
[10:28:18:967] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:28:19:076] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /questionSummary/getWrongQuestionList, authentication required: false
[10:28:19:080] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@12fb90ce for /questionSummary/getWrongQuestionList
[10:28:19:081] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /questionSummary/getWrongQuestionList
[10:28:19:102] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select count(*) from question_summary t left join question t1 ON t1.id=t.question_id left join question_bank_question t2 ON t2.question_id =t.question_id where t1.enabled=true and t1.company_id=? and t1.creater_id=? 
[10:28:19:104] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer)
[10:28:19:145] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:28:19:147] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select t.question_id as questionId,t.right_times_from_exam as rightTimesFromExam,t.wrong_times_from_exam as wrongTimesFromExam,t.right_times_from_practice as rightTimesFromPractice,t.wrong_times_from_practice as wrongTimesFromPractice,(t.wrong_times_from_exam + t.wrong_times_from_practice) as totalWrongTimes,t1.type,t1.main_content as mainContent,t1.child_type as childType,t1.category from question_summary t left join question t1 ON t1.id=t.question_id left join question_bank_question t2 ON t2.question_id =t.question_id where t1.enabled=true and t1.company_id=? and t1.creater_id=? order by totalWrongTimes desc limit ?,? 
[10:28:19:147] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer), 0(Integer), 50(Integer)
[10:28:19:217] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 32
[10:29:19:058] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /192.168.8.183:62746
[10:29:19:056] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /192.168.8.183:62748
[10:29:19:056] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /192.168.8.183:62751
[10:29:19:053] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /192.168.8.183:62750
[10:29:19:063] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /192.168.8.183:62747
[10:29:19:275] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /192.168.8.183:62749
