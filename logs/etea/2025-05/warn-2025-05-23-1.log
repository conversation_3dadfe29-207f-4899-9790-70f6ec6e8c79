[07:46:30:825] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[07:50:13:915] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[07:51:08:731] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[07:53:59:790] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[08:06:56:995] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[08:08:57:968] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[08:28:53:814] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[08:39:08:854] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[08:42:05:799] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[08:42:28:692] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[08:43:25:737] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[08:44:41:691] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[08:45:56:422] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[08:52:25:108] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[08:53:23:891] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[08:53:38:649] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[08:55:07:534] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[09:14:38:589] [WARN] - redis.clients.jedis.JedisFactory.destroyObject(JedisFactory.java:163) - Error while QUIT
redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: Read timed out
	at redis.clients.jedis.util.RedisInputStream.ensureFill(RedisInputStream.java:205) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.util.RedisInputStream.readByte(RedisInputStream.java:43) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.Protocol.process(Protocol.java:165) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.Protocol.read(Protocol.java:230) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.Connection.readProtocolWithCheckingBroken(Connection.java:352) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.Connection.getStatusCodeReply(Connection.java:270) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.BinaryJedis.quit(BinaryJedis.java:475) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.JedisFactory.destroyObject(JedisFactory.java:160) [jedis-3.8.0.jar:?]
	at org.apache.commons.pool2.PooledObjectFactory.destroyObject(PooledObjectFactory.java:127) [commons-pool2-2.11.1.jar:2.11.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.destroy(GenericObjectPool.java:611) [commons-pool2-2.11.1.jar:2.11.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.evict(GenericObjectPool.java:729) [commons-pool2-2.11.1.jar:2.11.1]
	at org.apache.commons.pool2.impl.BaseGenericObjectPool$Evictor.run(BaseGenericObjectPool.java:160) [commons-pool2-2.11.1.jar:2.11.1]
	at org.apache.commons.pool2.impl.EvictionTimer$WeakRunner.run(EvictionTimer.java:113) [commons-pool2-2.11.1.jar:2.11.1]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_281]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [?:1.8.0_281]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_281]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [?:1.8.0_281]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_281]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_281]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_281]
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method) ~[?:1.8.0_281]
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116) ~[?:1.8.0_281]
	at java.net.SocketInputStream.read(SocketInputStream.java:171) ~[?:1.8.0_281]
	at java.net.SocketInputStream.read(SocketInputStream.java:141) ~[?:1.8.0_281]
	at java.net.SocketInputStream.read(SocketInputStream.java:127) ~[?:1.8.0_281]
	at redis.clients.jedis.util.RedisInputStream.ensureFill(RedisInputStream.java:199) ~[jedis-3.8.0.jar:?]
	... 19 more
[15:18:53:712] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[15:19:16:498] [WARN] - io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:70) - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
