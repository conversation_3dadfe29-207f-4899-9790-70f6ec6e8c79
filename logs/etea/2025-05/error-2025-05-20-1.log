[07:32:09:980] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/wx/getTicket，出现异常:java.lang.NullPointerException；
[07:32:10:020] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"Origin":"http://localhost:8080","content-length":"0","X-Forwarded-Prefix":"/exam","Accept":"application/json, text/plain, */*","Referer":"http://localhost:8080/","X-Forwarded-Host":"127.0.0.1:8000","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","X-Forwarded-Proto":"http","Sec-Fetch-Site":"cross-site","Sec-Fetch-Dest":"empty","Accept-Encoding":"gzip, deflate, br, zstd","userId":"0","X-Forwarded-Port":"8000","token":"","Sec-Fetch-Mode":"cors","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","host":"**************:8081","Forwarded":"proto=http;host=\"127.0.0.1:8000\";for=\"127.0.0.1:64603\"","client":"pc_admin_qkk","X-Forwarded-For":"127.0.0.1","Accept-Language":"zh-CN,zh;q=0.9"}
[07:32:10:022] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{}
[07:32:10:022] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：java.lang.NullPointerException，异常信息：null，
