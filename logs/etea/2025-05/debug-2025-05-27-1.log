[10:21:22:392] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[10:21:30:649] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$AutoConfiguredMapperScannerRegistrar.registerBeanDefinitions(MybatisAutoConfiguration.java:252) - Searching for mappers annotated with @Mapper
[10:21:30:668] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$AutoConfiguredMapperScannerRegistrar.registerBeanDefinitions(MybatisAutoConfiguration.java:264) - Using auto-configuration base package 'com.taurus.examinationassistant'
[10:21:34:299] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[10:21:34:447] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[10:21:34:448] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[10:21:34:449] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[10:21:34:450] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[10:21:34:450] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[10:21:34:450] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[10:21:34:450] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[10:21:34:451] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[10:21:34:452] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[10:21:34:453] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[10:21:34:454] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[10:21:34:454] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[10:21:34:540] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[10:21:35:977] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[10:21:35:986] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[10:21:37:522] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[10:21:37:558] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[10:21:37:570] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[10:21:38:161] [DEBUG] - io.micrometer.core.util.internal.logging.InternalLoggerFactory.newDefaultFactory(InternalLoggerFactory.java:59) - Using SLF4J as the default logging framework
[10:21:38:349] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[10:21:38:362] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[10:21:38:363] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[10:21:38:365] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[10:21:38:367] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[10:21:38:369] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[10:21:38:371] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[10:21:38:377] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[10:21:38:379] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[10:21:39:617] [DEBUG] - io.undertow.server.session.InMemorySessionManager.registerSessionListener(InMemorySessionManager.java:268) - Registered session listener io.undertow.servlet.core.SessionListenerBridge@7b93d25e
[10:21:39:732] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[10:21:46:117] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[10:21:46:131] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[10:21:46:485] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[10:21:46:488] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserFormMapper对应的Mapper
[10:21:46:798] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserOfYncxMapper对应的Mapper
[10:21:46:877] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongQuestionBookQuestionMapper对应的Mapper
[10:21:46:924] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.DiscountMapper对应的Mapper
[10:21:46:962] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PointRuleMapper对应的Mapper
[10:21:47:018] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PermissionAuthorizationMapper对应的Mapper
[10:21:47:058] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongBookMapper对应的Mapper
[10:21:47:073] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionMapper对应的Mapper
[10:21:47:142] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ViewPerformanceWithoutAdTransactionMapper对应的Mapper
[10:21:47:163] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationMapper对应的Mapper
[10:21:47:193] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PermissionMapper对应的Mapper
[10:21:47:217] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarUserProfileMapper对应的Mapper
[10:21:47:258] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UniversalProductOrderRelationMapper对应的Mapper
[10:21:47:296] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongQuestionBookReviewProcessMapper对应的Mapper
[10:21:47:327] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.OrdersMapper对应的Mapper
[10:21:47:359] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationInstanceProcessMapper对应的Mapper
[10:21:47:375] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.FileAccessPermissionMapper对应的Mapper
[10:21:47:390] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PassRaceStageMapper对应的Mapper
[10:21:47:398] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.TaxSheetApplicationMapper对应的Mapper
[10:21:47:410] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyRegisterFormFlowRecordMapper对应的Mapper
[10:21:47:427] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsitePaperInstanceMapper对应的Mapper
[10:21:47:438] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionFavoriteMapper对应的Mapper
[10:21:47:450] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CategoryMapper对应的Mapper
[10:21:47:462] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationInstanceMapper对应的Mapper
[10:21:47:484] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MessagePushSubscriptionMapper对应的Mapper
[10:21:47:500] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserViewPerformanceWithoutAdMapper对应的Mapper
[10:21:47:511] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteContentAccessMapper对应的Mapper
[10:21:47:529] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PointDetailMapper对应的Mapper
[10:21:47:543] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PointUserMapper对应的Mapper
[10:21:47:557] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AppStartNoticeMapper对应的Mapper
[10:21:47:577] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyUserGroupMapper对应的Mapper
[10:21:47:591] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserExaminationPerformanceReportMapper对应的Mapper
[10:21:47:601] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentSubjectCategoryMapper对应的Mapper
[10:21:47:607] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarUserMilestoneMapper对应的Mapper
[10:21:47:613] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AppGroupUserMapper对应的Mapper
[10:21:47:618] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PassRaceResultMapper对应的Mapper
[10:21:47:623] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WxMiniprogramSubscribeMessageOfEteaMapper对应的Mapper
[10:21:47:629] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyGroupUserMapper对应的Mapper
[10:21:47:635] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.OssFileAudioMapper对应的Mapper
[10:21:47:641] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PracticePushScheduleMapper对应的Mapper
[10:21:47:658] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserSummaryMapper对应的Mapper
[10:21:47:666] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.VersionMapper对应的Mapper
[10:21:47:671] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionRandomRangeMapper对应的Mapper
[10:21:47:676] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UniversalOrderMapper对应的Mapper
[10:21:47:683] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationNameListMapper对应的Mapper
[10:21:47:692] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookQuestionMapper对应的Mapper
[10:21:47:698] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperQuestionMapper对应的Mapper
[10:21:47:709] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserFormFlowRecordMapper对应的Mapper
[10:21:47:717] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationStartupMapper对应的Mapper
[10:21:47:723] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AppStartNoticeReadUserMapper对应的Mapper
[10:21:47:731] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentProductOfUserMapper对应的Mapper
[10:21:47:738] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookSummaryMapper对应的Mapper
[10:21:47:745] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.GameMapper对应的Mapper
[10:21:47:755] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CustomerRequirementMapper对应的Mapper
[10:21:47:769] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MessageTemplateMapper对应的Mapper
[10:21:47:776] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyMapper对应的Mapper
[10:21:47:787] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.GameQuestionRandomRuleMapper对应的Mapper
[10:21:47:794] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarMapper对应的Mapper
[10:21:47:806] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.FreePracticeOfUserMapper对应的Mapper
[10:21:47:823] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SysUserRoleMapper对应的Mapper
[10:21:47:832] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentUnlockMapper对应的Mapper
[10:21:47:843] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookUserRecordMapper对应的Mapper
[10:21:47:850] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MultipleMarkingExaminationMapper对应的Mapper
[10:21:47:854] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongQuestionBookMapper对应的Mapper
[10:21:47:859] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationFavoriteMapper对应的Mapper
[10:21:47:868] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionRandomMapper对应的Mapper
[10:21:47:884] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationGroupExaminationMapper对应的Mapper
[10:21:47:894] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionCompositionChildMapper对应的Mapper
[10:21:47:905] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PassRaceGameMapper对应的Mapper
[10:21:47:911] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyRegisterFormMapper对应的Mapper
[10:21:47:918] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperPraiseMapper对应的Mapper
[10:21:47:927] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyDepartmentMapper对应的Mapper
[10:21:47:935] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyJobGradeMapper对应的Mapper
[10:21:47:942] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookTagMapper对应的Mapper
[10:21:47:947] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionSummaryMapper对应的Mapper
[10:21:47:955] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ManualServiceRecordMapper对应的Mapper
[10:21:47:964] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentClassificationInfoMapper对应的Mapper
[10:21:47:976] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.NanxiangRegisterMapper对应的Mapper
[10:21:47:986] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserMapper对应的Mapper
[10:21:47:996] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QiyewxInterfaceLicenseOrderMapper对应的Mapper
[10:21:48:003] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteOrderMapper对应的Mapper
[10:21:48:012] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MultipleMarkingExaminationInstanceMapper对应的Mapper
[10:21:48:021] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.CompanyFunctionRoleMapper对应的Mapper
[10:21:48:027] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentSubjectMapper对应的Mapper
[10:21:48:038] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationTagMapper对应的Mapper
[10:21:48:049] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentSubjectAccessInfoMapper对应的Mapper
[10:21:48:056] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WrongQuestionBookReviewSummaryMapper对应的Mapper
[10:21:48:073] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperTagMapper对应的Mapper
[10:21:48:086] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductMapper对应的Mapper
[10:21:48:096] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionAttachmentMapper对应的Mapper
[10:21:48:102] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionExampleDeletedMapper对应的Mapper
[10:21:48:106] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookExamineeMapper对应的Mapper
[10:21:48:118] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.NameListMapper对应的Mapper
[10:21:48:126] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperFavoriteMapper对应的Mapper
[10:21:48:135] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserReportRelationMapper对应的Mapper
[10:21:48:141] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarStageEventMapper对应的Mapper
[10:21:48:149] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsitePaperInstanceProcessMapper对应的Mapper
[10:21:48:162] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PaperMapper对应的Mapper
[10:21:48:175] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserOfProductMapper对应的Mapper
[10:21:48:185] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ContentSubjectOfUserMapper对应的Mapper
[10:21:48:190] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteFavoriteMapper对应的Mapper
[10:21:48:194] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteUserSignatureMapper对应的Mapper
[10:21:48:199] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductOrderRelationMapper对应的Mapper
[10:21:48:205] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationExamineeSnapshotMapper对应的Mapper
[10:21:48:208] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookFavoriteMapper对应的Mapper
[10:21:48:214] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductOrderRelationCountMapper对应的Mapper
[10:21:48:219] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserExaminationInstanceReportMapper对应的Mapper
[10:21:48:223] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteWithdrawMapper对应的Mapper
[10:21:48:228] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductIntroductionMapper对应的Mapper
[10:21:48:237] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SysRoleMapper对应的Mapper
[10:21:48:243] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SysLogMapper对应的Mapper
[10:21:48:251] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookPracticeProcessMapper对应的Mapper
[10:21:48:295] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.GameResultMapper对应的Mapper
[10:21:48:305] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.InviteRelationMapper对应的Mapper
[10:21:48:314] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QuestionBankQuestionMapper对应的Mapper
[10:21:48:318] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QiyewxUserAccountLicenseMapper对应的Mapper
[10:21:48:330] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.TaxIdentityInfoMapper对应的Mapper
[10:21:48:334] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserCompanyMapper对应的Mapper
[10:21:48:340] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.UserDeviceBindingMapper对应的Mapper
[10:21:48:347] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.HomepageUserFollowedMapper对应的Mapper
[10:21:48:355] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QiyewxOrderMapper对应的Mapper
[10:21:48:362] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MessagePlanMapper对应的Mapper
[10:21:48:367] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.TemplateMapper对应的Mapper
[10:21:48:371] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.MessageScheduleMapper对应的Mapper
[10:21:48:377] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SmsMessageMapper对应的Mapper
[10:21:48:380] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.QrcodeScanEntryMapper对应的Mapper
[10:21:48:384] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AnnualReportMapper对应的Mapper
[10:21:48:390] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.PointConsumerationMapper对应的Mapper
[10:21:48:398] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.AppMapper对应的Mapper
[10:21:48:403] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.KsiteOrderRelationMapper对应的Mapper
[10:21:48:426] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductCountTransactionMapper对应的Mapper
[10:21:48:466] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ToutiaoQuestionStarUserMapper对应的Mapper
[10:21:48:470] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.OssFileMapper对应的Mapper
[10:21:48:488] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationWxGidMapper对应的Mapper
[10:21:48:493] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookMapper对应的Mapper
[10:21:48:509] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.SystemMessageMapper对应的Mapper
[10:21:48:519] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookGradeMapper对应的Mapper
[10:21:48:524] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.WeixinAccountMapper对应的Mapper
[10:21:48:528] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationExamineeMapper对应的Mapper
[10:21:48:533] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ProductIntroductionMediaMapper对应的Mapper
[10:21:48:537] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExaminationInstanceEventMapper对应的Mapper
[10:21:48:544] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.NameListDetailMapper对应的Mapper
[10:21:48:548] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookPraiseMapper对应的Mapper
[10:21:48:556] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.examinationassistant.mapper.ExerciseBookPracticeSummaryMapper对应的Mapper
[10:21:48:561] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：2.072秒
[10:21:55:889] [DEBUG] - io.netty.util.internal.logging.InternalLoggerFactory.useSlf4JLoggerFactory(InternalLoggerFactory.java:63) - Using SLF4J as the default logging framework
[10:21:55:895] [DEBUG] - io.netty.util.concurrent.GlobalEventExecutor.<clinit>(GlobalEventExecutor.java:53) - -Dio.netty.globalEventExecutor.quietPeriodSeconds: 1
[10:21:55:907] [DEBUG] - io.netty.util.internal.InternalThreadLocalMap.<clinit>(InternalThreadLocalMap.java:100) - -Dio.netty.threadLocalMap.stringBuilder.initialSize: 1024
[10:21:55:908] [DEBUG] - io.netty.util.internal.InternalThreadLocalMap.<clinit>(InternalThreadLocalMap.java:101) - -Dio.netty.threadLocalMap.stringBuilder.maxSize: 4096
[10:21:55:941] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[10:21:55:946] [DEBUG] - io.netty.channel.MultithreadEventLoopGroup.<clinit>(MultithreadEventLoopGroup.java:44) - -Dio.netty.eventLoopThreads: 16
[10:21:55:980] [DEBUG] - io.netty.util.internal.PlatformDependent0.explicitNoUnsafeCause0(PlatformDependent0.java:515) - -Dio.netty.noUnsafe: false
[10:21:55:982] [DEBUG] - io.netty.util.internal.PlatformDependent0.javaVersion0(PlatformDependent0.java:1026) - Java version: 8
[10:21:55:984] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:140) - sun.misc.Unsafe.theUnsafe: available
[10:21:55:987] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:164) - sun.misc.Unsafe.copyMemory: available
[10:21:55:989] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:196) - sun.misc.Unsafe.storeFence: available
[10:21:55:989] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:239) - java.nio.Buffer.address: available
[10:21:55:990] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:312) - direct buffer constructor: available
[10:21:55:991] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:403) - java.nio.Bits.unaligned: available, true
[10:21:55:992] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:478) - jdk.internal.misc.Unsafe.allocateUninitializedArray(int): unavailable prior to Java9
[10:21:55:992] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:501) - java.nio.DirectByteBuffer.<init>(long, {int,long}): available
[10:21:55:993] [DEBUG] - io.netty.util.internal.PlatformDependent.unsafeUnavailabilityCause0(PlatformDependent.java:1157) - sun.misc.Unsafe: available
[10:21:55:994] [DEBUG] - io.netty.util.internal.PlatformDependent.tmpdir0(PlatformDependent.java:1303) - -Dio.netty.tmpdir: /var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T (java.io.tmpdir)
[10:21:55:994] [DEBUG] - io.netty.util.internal.PlatformDependent.bitMode0(PlatformDependent.java:1382) - -Dio.netty.bitMode: 64 (sun.arch.data.model)
[10:21:55:998] [DEBUG] - io.netty.util.internal.PlatformDependent.isOsx0(PlatformDependent.java:1125) - Platform: MacOS
[10:21:55:999] [DEBUG] - io.netty.util.internal.PlatformDependent.<clinit>(PlatformDependent.java:176) - -Dio.netty.maxDirectMemory: 3817865216 bytes
[10:21:56:000] [DEBUG] - io.netty.util.internal.PlatformDependent.<clinit>(PlatformDependent.java:183) - -Dio.netty.uninitializedArrayAllocationThreshold: -1
[10:21:56:001] [DEBUG] - io.netty.util.internal.CleanerJava6.<clinit>(CleanerJava6.java:92) - java.nio.ByteBuffer.cleaner(): available
[10:21:56:001] [DEBUG] - io.netty.util.internal.PlatformDependent.<clinit>(PlatformDependent.java:203) - -Dio.netty.noPreferDirect: false
[10:21:56:003] [DEBUG] - io.netty.channel.nio.NioEventLoop.<clinit>(NioEventLoop.java:110) - -Dio.netty.noKeySetOptimization: false
[10:21:56:003] [DEBUG] - io.netty.channel.nio.NioEventLoop.<clinit>(NioEventLoop.java:111) - -Dio.netty.selectorAutoRebuildThreshold: 512
[10:21:56:011] [DEBUG] - io.netty.util.internal.PlatformDependent$Mpsc.<clinit>(PlatformDependent.java:1008) - org.jctools-core.MpscChunkedArrayQueue: available
[10:21:56:028] [WARN] - io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:70) - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
[10:21:56:038] [DEBUG] - io.netty.util.NetUtil.<clinit>(NetUtil.java:148) - -Djava.net.preferIPv4Stack: false
[10:21:56:038] [DEBUG] - io.netty.util.NetUtil.<clinit>(NetUtil.java:149) - -Djava.net.preferIPv6Addresses: false
[10:21:56:041] [DEBUG] - io.netty.util.NetUtilInitializations.determineLoopback(NetUtilInitializations.java:145) - Loopback interface: lo0 (lo0, 0:0:0:0:0:0:0:1%lo0)
[10:21:56:042] [DEBUG] - io.netty.util.NetUtil$SoMaxConnAction.run(NetUtil.java:206) - Failed to get SOMAXCONN from sysctl and file /proc/sys/net/core/somaxconn. Default: 128
[10:21:56:065] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:141) - Default ResolvedAddressTypes: IPV4_PREFERRED
[10:21:56:065] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:142) - Localhost address: localhost/127.0.0.1
[10:21:56:065] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:151) - Windows hostname: null
[10:21:56:068] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:164) - Default search domains: []
[10:21:56:068] [DEBUG] - io.netty.resolver.dns.DnsNameResolver.<clinit>(DnsNameResolver.java:173) - Default UnixResolverOptions{ndots=1, timeout=5, attempts=16}
[10:21:56:080] [DEBUG] - io.netty.resolver.DefaultHostsFileEntriesResolver.<clinit>(DefaultHostsFileEntriesResolver.java:53) - -Dio.netty.hostsFileRefreshInterval: 0
[10:21:56:095] [DEBUG] - io.netty.util.ResourceLeakDetector.<clinit>(ResourceLeakDetector.java:129) - -Dio.netty.leakDetection.level: simple
[10:21:56:096] [DEBUG] - io.netty.util.ResourceLeakDetector.<clinit>(ResourceLeakDetector.java:130) - -Dio.netty.leakDetection.targetRecords: 4
[10:21:56:096] [DEBUG] - io.netty.util.ResourceLeakDetectorFactory$DefaultResourceLeakDetectorFactory.newResourceLeakDetector(ResourceLeakDetectorFactory.java:196) - Loaded default ResourceLeakDetector: io.netty.util.ResourceLeakDetector@7e264fe7
[10:21:56:160] [DEBUG] - io.netty.channel.DefaultChannelId.<clinit>(DefaultChannelId.java:79) - -Dio.netty.processId: 62553 (auto-detected)
[10:21:56:168] [DEBUG] - io.netty.channel.DefaultChannelId.<clinit>(DefaultChannelId.java:101) - -Dio.netty.machineId: 76:f7:10:ff:fe:89:1e:3c (auto-detected)
[10:21:56:195] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:157) - -Dio.netty.allocator.numHeapArenas: 16
[10:21:56:195] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:158) - -Dio.netty.allocator.numDirectArenas: 16
[10:21:56:196] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:160) - -Dio.netty.allocator.pageSize: 8192
[10:21:56:197] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:165) - -Dio.netty.allocator.maxOrder: 9
[10:21:56:198] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:169) - -Dio.netty.allocator.chunkSize: 4194304
[10:21:56:198] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:170) - -Dio.netty.allocator.smallCacheSize: 256
[10:21:56:199] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:171) - -Dio.netty.allocator.normalCacheSize: 64
[10:21:56:199] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:172) - -Dio.netty.allocator.maxCachedBufferCapacity: 32768
[10:21:56:199] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:173) - -Dio.netty.allocator.cacheTrimInterval: 8192
[10:21:56:199] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:174) - -Dio.netty.allocator.cacheTrimIntervalMillis: 0
[10:21:56:200] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:175) - -Dio.netty.allocator.useCacheForAllThreads: false
[10:21:56:200] [DEBUG] - io.netty.buffer.PooledByteBufAllocator.<clinit>(PooledByteBufAllocator.java:176) - -Dio.netty.allocator.maxCachedByteBuffersPerChunk: 1023
[10:21:56:213] [DEBUG] - io.netty.buffer.ByteBufUtil.<clinit>(ByteBufUtil.java:89) - -Dio.netty.allocator.type: pooled
[10:21:56:214] [DEBUG] - io.netty.buffer.ByteBufUtil.<clinit>(ByteBufUtil.java:98) - -Dio.netty.threadLocalDirectBufferSize: 0
[10:21:56:215] [DEBUG] - io.netty.buffer.ByteBufUtil.<clinit>(ByteBufUtil.java:101) - -Dio.netty.maxThreadLocalCharBufferSize: 16384
[10:21:56:226] [DEBUG] - io.netty.bootstrap.ChannelInitializerExtensions.getExtensions(ChannelInitializerExtensions.java:54) - -Dio.netty.bootstrap.extensions: null
[10:21:56:373] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:21:56:373] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:21:56:373] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:21:56:448] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:96) - -Dio.netty.recycler.maxCapacityPerThread: 4096
[10:21:56:451] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:97) - -Dio.netty.recycler.ratio: 8
[10:21:56:452] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:98) - -Dio.netty.recycler.chunkSize: 32
[10:21:56:452] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:99) - -Dio.netty.recycler.blocking: false
[10:21:56:452] [DEBUG] - io.netty.util.Recycler.<clinit>(Recycler.java:100) - -Dio.netty.recycler.batchFastThreadLocalOnly: true
[10:21:56:461] [DEBUG] - io.netty.buffer.AbstractByteBuf.<clinit>(AbstractByteBuf.java:63) - -Dio.netty.buffer.checkAccessible: true
[10:21:56:461] [DEBUG] - io.netty.buffer.AbstractByteBuf.<clinit>(AbstractByteBuf.java:64) - -Dio.netty.buffer.checkBounds: true
[10:21:56:461] [DEBUG] - io.netty.util.ResourceLeakDetectorFactory$DefaultResourceLeakDetectorFactory.newResourceLeakDetector(ResourceLeakDetectorFactory.java:196) - Loaded default ResourceLeakDetector: io.netty.util.ResourceLeakDetector@7fdaf557
[10:21:56:559] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@2071253782 [redisClient=[addr=redis://************72:6379], channel=[id: 0x424565b4, L:/*************:54316 - R:************72/************72:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@71e4e166[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[10:21:56:558] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@961437491 [redisClient=[addr=redis://************72:6379], channel=[id: 0xd996eca5, L:/*************:54317 - R:************72/************72:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@3789a66a[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[10:21:56:593] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connectPubSub$3(ClientConnectionsEntry.java:234) - new pubsub connection created: RedisPubSubConnection@1959645054 [redisClient=[addr=redis://************72:6379], channel=[id: 0x94a909b1, L:/*************:54318 - R:************72/************72:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@7a0a2f97[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[10:21:56:594] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for ************72/************72:6379
[10:21:56:626] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:21:56:626] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:21:56:756] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@108286883 [redisClient=[addr=redis://************72:6379], channel=[id: 0x0b6cc11f, L:/*************:54319 - R:************72/************72:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@8b3ad1d[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[10:21:56:756] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@427876348 [redisClient=[addr=redis://************72:6379], channel=[id: 0xaef9f3a6, L:/*************:54320 - R:************72/************72:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@174732bc[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[10:21:56:782] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:21:56:782] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:21:56:861] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1256262216 [redisClient=[addr=redis://************72:6379], channel=[id: 0xd0d43257, L:/*************:54322 - R:************72/************72:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@4426ed64[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[10:21:56:861] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1593373923 [redisClient=[addr=redis://************72:6379], channel=[id: 0x8f70cec8, L:/*************:54321 - R:************72/************72:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@503f014c[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[10:21:56:869] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:21:56:867] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:21:57:016] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@2117481218 [redisClient=[addr=redis://************72:6379], channel=[id: 0xb63235b2, L:/*************:54323 - R:************72/************72:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@70f1c2b4[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[10:21:57:025] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@851568177 [redisClient=[addr=redis://************72:6379], channel=[id: 0x8fcafad1, L:/*************:54324 - R:************72/************72:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@3c061e19[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[10:21:57:035] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:21:57:036] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:21:57:120] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1797889197 [redisClient=[addr=redis://************72:6379], channel=[id: 0xd0d6a96e, L:/*************:54326 - R:************72/************72:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@65ee6fb8[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[10:21:57:121] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@286485625 [redisClient=[addr=redis://************72:6379], channel=[id: 0xdfa24983, L:/*************:54325 - R:************72/************72:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@1fd4902b[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[10:21:57:126] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:21:57:127] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:21:57:198] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1443558696 [redisClient=[addr=redis://************72:6379], channel=[id: 0xca981be9, L:/*************:54328 - R:************72/************72:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@58cd0199[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[10:21:57:200] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:21:57:203] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@769459922 [redisClient=[addr=redis://************72:6379], channel=[id: 0xbec98172, L:/*************:54327 - R:************72/************72:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@231aed19[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[10:21:57:207] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:21:57:269] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@146870283 [redisClient=[addr=redis://************72:6379], channel=[id: 0x630fb4f4, L:/*************:54330 - R:************72/************72:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@606ff63[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[10:21:57:282] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:21:57:291] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@434606799 [redisClient=[addr=redis://************72:6379], channel=[id: 0xd152a8d6, L:/*************:54329 - R:************72/************72:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@5ce51f97[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[10:21:57:304] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:21:57:379] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1636075003 [redisClient=[addr=redis://************72:6379], channel=[id: 0xe2df1973, L:/*************:54333 - R:************72/************72:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@6ab3ac5e[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[10:21:57:384] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:21:57:403] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@977874088 [redisClient=[addr=redis://************72:6379], channel=[id: 0x101154c8, L:/*************:54334 - R:************72/************72:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@2c6a524c[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[10:21:57:406] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:21:57:477] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1191745862 [redisClient=[addr=redis://************72:6379], channel=[id: 0xbe4c21c3, L:/*************:54335 - R:************72/************72:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@451ea9c8[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[10:21:57:483] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:21:57:493] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1811471201 [redisClient=[addr=redis://************72:6379], channel=[id: 0xa3ccf806, L:/*************:54336 - R:************72/************72:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@32a7e287[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[10:21:57:497] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:21:57:577] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@326677644 [redisClient=[addr=redis://************72:6379], channel=[id: 0x0879dd02, L:/*************:54338 - R:************72/************72:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@567a3887[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[10:21:57:575] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@210516179 [redisClient=[addr=redis://************72:6379], channel=[id: 0x4bfd4f8e, L:/*************:54337 - R:************72/************72:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@498ea726[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[10:21:57:584] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:21:57:584] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:21:57:696] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1075405112 [redisClient=[addr=redis://************72:6379], channel=[id: 0xdfef7603, L:/*************:54339 - R:************72/************72:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@51be75f[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[10:21:57:695] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1409326995 [redisClient=[addr=redis://************72:6379], channel=[id: 0xe1f5891d, L:/*************:54340 - R:************72/************72:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@11020b77[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[10:21:57:711] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:21:57:712] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:21:57:768] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@1959680114 [redisClient=[addr=redis://************72:6379], channel=[id: 0x85f06502, L:/*************:54342 - R:************72/************72:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@31ccc88f[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[10:21:57:787] [DEBUG] - org.redisson.connection.ClientConnectionsEntry.lambda$connect$2(ClientConnectionsEntry.java:199) - new connection created: RedisConnection@943295809 [redisClient=[addr=redis://************72:6379], channel=[id: 0xa3fa0de4, L:/*************:54341 - R:************72/************72:6379], currentCommand=CommandData [promise=java.util.concurrent.CompletableFuture@7d3b1422[Not completed, 1 dependents], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec], usage=0]
[10:21:57:787] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for ************72/************72:6379
[10:22:02:247] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.AccessController:
	{GET [/access/getTokenStr]}: getToken(Integer,HttpServletRequest)
	{GET [/access/getTokenByTime]}: getTokenByTime(Integer,Long,HttpServletRequest)
	{GET [/access/getTokenUnlimit]}: getTokenUnlimit(Integer,HttpServletRequest)
	{GET [/access/heartbeat]}: heartbeat()
[10:22:02:251] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ActivityController:
	{ [/activity/getActiveActivities]}: getActiveActivities(String)
[10:22:02:251] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.AnnualReportController:
	{GET [/annual/getReport]}: getEntity(Integer)
[10:22:02:252] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.AppStartNoticeController:
	{GET [/appStartNotice/getCurrentNoticeOfUser]}: getCurrentNoticeOfUser(String,Integer)
	{GET [/appStartNotice/getCurrentNotice]}: getCurrentNotice(String)
[10:22:02:255] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.AppStartNoticeReadUseController:
	{GET [/appStartNoticeReadUser/save]}: save(Integer,Integer)
[10:22:02:267] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CompanyController:
	{POST [/company/modify]}: modify(Company)
	{POST [/company/createCompany]}: createCompany(JSONObject)
	{GET [/company/getCompanyInfoByUserId]}: getCompanyInfoByUserId(int)
	{POST [/company/createDepartment]}: createDepartment(JSONObject)
	{POST [/company/createJobGrade]}: createJobGrade(JSONObject)
	{POST [/company/createFunctionRole]}: createFunctionRole(JSONObject)
	{GET [/company/deleteCompany]}: deleteCompany(int)
	{GET [/company/deleteDepartment]}: deleteDepartment(int)
	{GET [/company/deleteDepartmentCarefully]}: deleteDepartmentCarefully(Integer,Integer)
	{GET [/company/deleteJobGrade]}: deleteJobGrade(int)
	{GET [/company/deleteJobGradeCarefully]}: deleteJobGradeCarefully(Integer,Integer)
	{GET [/company/deleteFunctionRole]}: deleteFunctionRole(int)
	{GET [/company/deleteFunctionRoleCarefully]}: deleteFunctionRoleCarefully(Integer,Integer)
	{POST [/company/modifyCompany]}: modifyCompany(JSONObject)
	{GET [/company/modifyCompanyName]}: modifyCompanyName(String,Integer)
	{POST [/company/modifyDepartment]}: modifyDepartment(JSONObject)
	{POST [/company/modifyJobGrade]}: modifyJobGrade(JSONObject)
	{POST [/company/modifyFunctionRole]}: modifyFunctionRole(JSONObject)
	{GET [/company/getCompanyInfo]}: getCompanyInfo(Integer)
	{GET [/company/getCompanyInfoById]}: getCompanyInfoWrapperById(int)
	{GET [/company/getCompanyInfoByCodeOrName]}: getCompanyInfoByCodeOrName(String)
	{GET [/company/getCompanyInfoListByName]}: getCompanyInfoListByName(String)
	{GET [/company/getAllDepartmentOfCompany]}: getAllDepartmentOfCompany(Integer,Integer)
	{GET [/company/getNGradeDepartment]}: getLevelDepartmentOfLessThanOrEqualDesignativeGrade(Integer,Integer)
	{GET [/company/getAllDepartmentOfCompanyByUserId]}: getAllDepartmentOfCompanyByUserId(Integer)
	{GET [/company/getSubDepartmentExcludingSelf]}: getSubDepartmentExcludingSelf(String)
	{GET [/company/getSubDepartmentIncludingSelf]}: getSubDepartmentIncludingSelf(String)
	{GET [/company/getAllCompanyJobGradeOfCompany]}: getAllCompanyJobGradeOfCompany(int)
	{GET [/company/getAllCompanyInfo]}: getAllCompanyInfo(int)
	{POST [/company/getQiyeweixinCompanyList]}: getQiyeweixinCompanyList(JSONObject)
	{GET [/company/getAllCompanyInfoWith2Grade]}: getAllCompanyInfoWith2Grade(Integer)
	{GET [/company/getAllCompanyFunctionRoleOfCompany]}: getAllCompanyFunctionRoleOfCompany(int)
	{GET [/company/search]}: searchCompanyByKeyword(String)
	{GET [/company/batchCloseAccount]}: batchCloseAccount(String)
	{GET [/company/closeAccount]}: closeAccount(Integer,Integer)
	{GET [/company/getResidualFlow]}: getResidualFlow(Integer)
	{POST [/company/getOrganizationFrameworkInfoByIds]}: getOrganizationFrameworkInfoByIds(JSONObject)
[10:22:02:272] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CompanyDepartmentController:
	{GET [/companyDepartment/getCompanyDepartmentList]}: getCompanyDepartmentList(Integer,String)
[10:22:02:283] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CompanyGroupUserController:
	{POST [/companyGroupUser/create]}: createCompanyGroupUser(CompanyGroupUser)
	{POST [/companyGroupUser/batchCreate]}: batchCreateCompanyGroupUser(List)
	{POST [/companyGroupUser/update]}: updateCompanyGroupUser(CompanyGroupUser)
	{GET [/companyGroupUser/delete]}: deleteCompanyGroupUser(Integer,String)
	{GET [/companyGroupUser/getGroupsByUserId]}: getGroupsByUserId(Integer)
	{GET [/companyGroupUser/getUsersByGroupId]}: getUsersByGroupId(Integer)
	{GET [/companyGroupUser/checkUserInGroup]}: checkUserInGroup(Integer,Integer)
[10:22:02:284] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CompanyUserGroupController:
	{GET [/companyUserGroup/list]}: getGroupList(Integer,Integer,String,String)
	{POST [/companyUserGroup/update]}: updateGroup(JSONObject)
	{GET [/companyUserGroup/delete]}: deleteGroup(Integer)
	{GET [/companyUserGroup/detail]}: getGroupDetail(Integer)
	{POST [/companyUserGroup/create]}: createGroup(JSONObject)
[10:22:02:285] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentClassificationInfoController:
	{ [/contentClassification/getUnlockedPracticeContentOfUser]}: getUnlockedPracticeContentOfUser(Integer)
	{ [/contentClassification/getPracticeContentOfSubject]}: getPracticeContentOfSubject(Integer,Integer)
	{ [/contentClassification/getPracticeContentOfUnlock]}: getPracticeContentOfUnlock()
[10:22:02:285] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentProductOfUserController:
	{GET [/contentProductOfUser/getProductListOfUser]}: getProductListOfUser(Integer)
[10:22:02:287] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentSubjectCategoryController:
	{GET [/contentSubjectCategory/getCategoryListOfSubject]}: getCategoryListOfSubject(Integer)
	{GET [/contentSubjectCategory/getSubjectListOfCategory]}: getSubjectListOfCategory(Integer)
	{GET [/contentSubjectCategory/getPathListOfSubject]}: getPathListOfSubject(Integer)
	{GET [/contentSubjectCategory/getNLevelCategoryWithContentNum]}: getLevelCategoriesOfLessThanOrEqualDesignativeLevel(String,Integer,Integer)
	{POST [/contentSubjectCategory/update]}: update(JSONArray)
	{GET [/contentSubjectCategory/delete]}: delete(Integer,Integer)
	{POST [/contentSubjectCategory/save]}: save(JSONArray)
[10:22:02:290] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentSubjectController:
	{POST [/contentSubject/modifySubject]}: modifySubject(JSONObject)
	{POST [/contentSubject/createSubject]}: createSubject(JSONObject)
	{GET [/contentSubject/getContentSubjectByContent]}: getContentSubjectByContent(String,Integer)
	{GET [/contentSubject/getAllContentSubject]}: getSubjectInfoList(String,Integer,Integer,Integer)
	{GET [/contentSubject/getSubjectListOfProduct]}: getSubjectListOfProduct(String)
	{GET [/contentSubject/getAllContentSubjectWithUserSelection]}: getAllContentSubjectWithUserSelection(Integer,String)
	{GET [/contentSubject/getSubjectWrapperBySubjectId]}: getSubjectWrapperBySubjectId(Integer)
	{GET [/contentSubject/getSubjectWrapper]}: getSubjectWrapper(Integer)
	{GET [/contentSubject/getSubjectWrapperWithFavoriteInfo]}: getSubjectWrapperWithFavoriteInfo(Integer,Integer)
	{GET [/contentSubject/delete]}: delete(Integer)
[10:22:02:293] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentSubjectOfUserController:
	{POST [/contentSubjectOfUser/saveContentSubjectsOfUser]}: saveContentSubjectsOfUser(JSONObject)
	{GET [/contentSubjectOfUser/getContentSubjectListOfUser]}: getContentSubjectListOfUser(Integer,String,Integer,Integer,Integer)
[10:22:02:294] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentSubjectSummaryController:
	{GET [/contentSubjectSummary/getSummary]}: getSummary(Integer)
[10:22:02:294] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ContentUnlockController:
	{POST [/contentUnlock/save]}: save(JSONObject)
[10:22:02:295] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CryptographyController:
	{ [/cryptography/decrypt]}: decrypt(String)
	{ [/cryptography/encrypt]}: encrypt(String)
[10:22:02:298] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.CustomerRequirementController:
	{POST [/cs/createRequirement]}: save(JSONObject)
[10:22:02:313] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.DataResolverController:
	{GET [/dataResolver/deleteInvalidSubscirbeMessage]}: deleteInvalidSubscirbeMessage(String)
	{GET [/dataResolver/clearJSONErrorStr]}: clearJSONErrorStr(Integer)
	{GET [/dataResolver/recaculateExcerciseBookInfo]}: recaculateExcerciseBookInfo()
	{GET [/dataResolver/deleteInvalidQuestion]}: deleteInvalidQuestion()
	{GET [/dataResolver/deleteInvalidPaper]}: deleteInvalidPaper()
	{GET [/dataResolver/deleteExerciseBookAndRelation]}: deleteInvalidExerciseBookAndRelation()
	{GET [/dataResolver/deleteInvalidSystemMessage]}: deleteInvalidSystemMessage(String,String,String)
	{GET [/dataResolver/deleteExaminationInstance]}: deleteExaminationInstance()
	{GET [/dataResolver/handleDeleteQuestionsEvent]}: handleDeleteQuestionsEvent()
	{GET [/dataResolver/replaceDepartmentId]}: replaceDepartmentId(Integer)
	{GET [/dataResolver/generateAnnualReport]}: generateAnnualReport(Integer,Integer,String,String)
	{GET [/dataResolver/setSelectionChildType]}: setSelectionChildType(int)
	{GET [/dataResolver/reCalculateQuestionScore]}: reCalculateQuestionScore(int,Integer,Integer,String)
	{GET [/dataResolver/reCalculateExaminationInstanceByQuestionId]}: reCalculateExaminationInstance(int,int)
	{GET [/dataResolver/reCalculateExaminationInstance]}: reCalculateExaminationInstance(int,Integer,String)
	{GET [/dataResolver/reCreateCompletionOptions]}: reCreateCompletionOptions(Integer,Integer,Integer,String)
	{GET [/dataResolver/checkDataMigrationFromFreeVersion]}: checkDataMigrationFromFreeVersion(int)
	{GET [/dataResolver/migrateDataFromFreeVersionToEnterpriseVersion]}: migrateDataFromFreeVersionToEnterpriseVersion(int,int)
	{GET [/dataResolver/deleteExaminationIncludingWrongRandomQuestion]}: deleteExaminationIncludingWrongRandomQuestion()
	{GET [/dataResolver/deleteExaminationInstanceAndProcessOfEtea]}: deleteExaminationInstanceAndProcessOfEtea(String,String)
	{GET [/dataResolver/restoreExerciseBookByProcess]}: restoreExerciseBookByProcess()
	{GET [/dataResolver/configUserSysUserRole]}: configUserSysUserRole()
	{GET [/dataResolver/recaculateExaminationInstanceBecauseOfNullScore]}: recaculateExaminationInstanceBecauseOfNullScore()
	{GET [/dataResolver/deleteInValidExamination]}: deleteInValidExamination()
	{GET [/dataResolver/insertCompanyUser]}: insertCompanyUser(Integer)
	{GET [/dataResolver/getUnCompleteQuestion]}: getUnCompleteQuestion(Integer)
	{GET [/dataResolver/deleteDepartments]}: deleteDepartments()
	{POST [/dataResolver/processQuestionAndAnswers], consumes [multipart/form-data]}: processQuestionAndAnswers(HttpServletRequest,HttpServletResponse)
	{GET [/dataResolver/deletedRandomRangeQuestionWithQuestionDeletedState]}: deletedRandomRangeQuestionWithQuestionDeletedState(Integer,Integer)
	{GET [/dataResolver/clearExaminationCache]}: reloadExaminationCache(Integer,Integer)
	{GET [/dataResolver/clearRedisData]}: clearRedisData(String)
	{GET [/dataResolver/clearAccountPermently]}: clearAccountPermently(Integer,Integer)
	{GET [/dataResolver/generateQuestionJsonFileOfExamination]}: generateQuestionJsonFileOfExamination(Integer,Integer,Integer)
	{GET [/dataResolver/generateHotFileListOfCDN]}: generateHotFileListOfCDN(Integer,Integer)
	{GET [/dataResolver/resizeOssPicFile]}: resizeOssPicFile(String)
	{GET [/dataResolver/deleteQuestionAndRelatedRecords]}: deleteQuestionAndRelatedRecords(String,String)
	{GET [/dataResolver/deleteWrongQuestionBook]}: deleteWrongQuestionBook(String,String)
	{GET [/dataResolver/transformCompletionQuestion]}: transformCompletionQuestion(Integer,Integer)
	{GET [/dataResolver/restoreExerciseBook]}: restoreExerciseBook(Integer)
[10:22:02:317] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.DownloadProxyController:
	{POST [/download/proxy]}: proxyDownload(HttpServletRequest,JSONObject)
[10:22:02:334] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationController:
	{GET [/examination/examination]}: getExaminationById(int)
	{POST [/examination/create]}: createExamination(JSONObject)
	{POST [/examination/createExaminationWithContentCheck]}: createExaminationWithContentCheck(JSONObject)
	{POST [/examination/createIncludingExamineeSelect]}: createExaminationIncludingExamineeSelect(JSONObject)
	{POST [/examination/createFixedExamination]}: createFixedExamination(JSONObject)
	{POST [/examination/createRandomExaminationIncludingExamineeSelect]}: createRandomExaminationIncludingExamineeSelect(JSONObject)
	{POST [/examination/createRandomExamination]}: createRandomExamination(JSONObject)
	{GET [/examination/delete]}: deleteExamination(String)
	{GET [/examination/examinationWrapperListOfCreater]}: getExaminationWrapperListOfCreater(int,int,int,int)
	{GET [/examination/examinationInViewListOfCreater]}: getExaminationInViewListOfCreater(Integer,Integer,Integer,Integer)
	{GET [/examination/getExaminationInViewListOfCreater]}: getExaminationInViewList(Integer,Integer,Integer,int)
	{GET [/examination/getExaminationInViewListAndTotalNumOfCreater]}: getExaminationInViewListAndTotalNumOfCreater(int,int,int,int)
	{GET [/examination/getRecommendExaminations]}: getRecommendExaminations(Integer,Integer,Integer,Integer)
	{GET [/examination/getRecommendExaminationsByProduct]}: getRecommendExaminationsByProduct(Integer,String)
	{POST [/examination/getExaminationInViewListOfCreaterInCompanyByTags]}: getExaminationInViewListOfCreaterInCompanyByTags(JSONObject)
	{POST [/examination/getExaminationInViewListOfCompanyByTags]}: getExaminationInViewListOfCompanyByTags(JSONObject)
	{GET [/examination/ongoingExaminationList]}: getOngoingExaminationList()
	{GET [/examination/getExaminationAndPaperById]}: getExaminationAndPaperById(int)
	{POST [/examination/getExaminationAndPaperByColumns]}: getExaminationAndPaperByColumns(JSONObject)
	{POST [/examination/getExaminationAndPaperByColumnsAfterEncode]}: getExaminationAndPaperByColumnsAfterEncode(JSONObject)
	{POST [/examination/getExaminationDetail]}: getExaminationDetail(JSONObject)
	{GET [/examination/examinationByCode]}: getExaminationByCode(String)
	{GET [/examination/getExaminationListByPaperId]}: getExaminationListByPaperId(Integer)
	{GET [/examination/getExaminationListByKeyword]}: getExaminationListByKeyword(String)
	{GET [/examination/getExaminationListOfCreater]}: getExaminationListOfCreater(Integer,Integer,Integer,Integer)
	{GET [/examination/idByCode]}: getExaminationIdByCode(String)
	{POST [/examination/edit]}: editExamination(JSONObject)
	{GET [/examination/examinationWrapper]}: getExaminationWrapperById(Integer)
	{GET [/examination/getExaminationWrapperSecure]}: getExaminationWrapperSecure(Integer,Integer)
	{GET [/examination/getExaminationWrapperStructure]}: getExaminationWrapperStructure(Integer,Integer)
	{POST [/examination/updateExaminationWrapperStructure]}: updateExaminationWrapperStructure(JSONObject)
	{GET [/examination/getExaminationAndPaperQuestionList]}: getExaminationAndPaperQuestionList(Integer)
	{GET [/examination/getExaminationAndPaperQuestionListAfterEncoded]}: getExaminationAndPaperQuestionListAfterEncoded(Integer)
	{GET [/examination/examinationWrapperWithRedLock]}: examinationWrapperWithRedLock(Integer,String)
	{GET [/examination/suspend]}: suspendExamination(Integer)
	{GET [/examination/resume]}: resumeExamination(Integer)
	{GET [/examination/changeExamineeDisplay]}: changeExamineeDisplay(Integer,Boolean)
	{GET [/examination/rename]}: renameExamination(int,int,String)
	{GET [/examination/renameExamCode]}: renameExamCode(Integer,String)
	{GET [/examination/modifyCode]}: modifyCode(Integer,String)
	{GET [/examination/configAdvancedOptions]}: configAdvancedOptions(String)
	{POST [/examination/saveAdvancedOptions]}: saveAdvancedOptions(JSONObject)
	{GET [/examination/checkIfExceedExaminationTimesLimit]}: checkIfExceedExaminationTimesLimit(Integer,Integer)
	{GET [/examination/copy]}: copyExamination(Integer,Integer,Boolean)
	{GET [/examination/transmit]}: transmitExamination(Integer,Integer,Integer)
	{GET [/examination/getLeftTime]}: getLeftFromBeginTime(Integer)
	{POST [/examination/sendQiyeweixinQkkNotice]}: sendQiyeweixinQkkNotice(JSONObject)
	{POST [/examination/update]}: update(Examination)
	{GET [/examination/reset]}: reset(Integer)
[10:22:02:340] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationExamineeController:
	{GET [/examinationExaminee/getExaminationExaminee]}: getExaminationExaminee(Integer)
	{POST [/examinationExaminee/updateExaminationExaminee]}: updateExaminationExaminee(JSONObject)
[10:22:02:344] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationExamineeSnapshotController:
	{GET [/examinationExamineeSnapshot/insertSnapshot]}: insertSnapshot(Integer,Integer,String,String)
	{GET [/examinationExamineeSnapshot/getUserSnapshotListOfExamination]}: getUserSnapshotListOfExamination(Integer,String,Integer,Integer)
	{GET [/examinationExamineeSnapshot/getUserSnapshotList]}: getUserSnapshotList(Integer,String,Integer,Integer)
	{GET [/examinationExamineeSnapshot/exportSnapshotListOfExamination]}: exportSnapshotListOfExamination(Integer)
	{GET [/examinationExamineeSnapshot/delete]}: delete(String)
[10:22:02:345] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationFavoriteController:
	{GET [/examinationFavorite/add]}: addFavorite(int,int)
	{GET [/examinationFavorite/cancel]}: cancelFavorite(int,int)
	{GET [/examinationFavorite/ifFavorite]}: ifFavorite(int,int)
	{GET [/examinationFavorite/getFavoriteList]}: getFavoriteList(int,int,int)
	{GET [/examinationFavorite/getFavoriteListAndNum]}: getFavoriteListAndNum(int,int,int)
[10:22:02:348] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationGroupExaminationController:
	{ [/examinationGroupExamination/list]}: getExaminationGroupExaminationServiceListById(int)
[10:22:02:360] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationInstanceController:
	{POST [/examinationInstance/getExaminationInstanceList]}: getExaminationInstanceList(JSONObject)
	{[GET, POST] [/examinationInstance/exportRankListOfExaminationInCompany]}: exportRankListOfExaminationInCompany(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/examinationInstance/exportNoRankListOfExaminationInCompany]}: exportNoRankListOfExaminationInCompany(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/examinationInstance/exportDetailRankListOfExamination]}: exportDetailRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{GET [/examinationInstance/getExaminationInstanceWrapperById]}: getExaminationInstanceWrapperById(Integer)
	{POST [/examinationInstance/batchDelete]}: batchDeleteByExaminee(JSONArray)
	{POST [/examinationInstance/batchDeleteByAdmin]}: batchDeleteByAdmin(JSONArray)
	{GET [/examinationInstance/examinationInstanceStageSummary]}: getExaminationInstanceStageSummary(int,String,String)
	{POST [/examinationInstance/getExaminationInstanceStageSummaryListByTags]}: getExaminationInstanceStageSummaryList(JSONObject)
	{GET [/examinationInstance/examinationInstanceStageSummaryList]}: getExaminationInstanceStageSummaryList(String,String,String,int,int,int)
	{GET [/examinationInstance/examinationInstanceStageSummaryListExcludingExample]}: getExaminationInstanceStageSummaryListExcludingExample(String,String,String,int,int,int)
	{GET [/examinationInstance/getExaminationInstanceStageSummaryListExcludingCreater]}: getExaminationInstanceStageSummaryListExcludingCreater(String,String,String,int,int,int)
	{GET [/examinationInstance/examinationInstanceStageSummaryDetail]}: getExaminationInstanceStageSummaryDetail(int,String,String,Integer,Integer,String)
	{POST [/examinationInstance/getExaminationStageSummaryDetail]}: getExaminationStageSummaryDetail(JSONObject)
	{GET [/examinationInstance/individualScoreListOfExamination]}: getIndividualScoreListOfExamination(int,int,String,String)
	{GET [/examinationInstance/individualScoreList]}: getIndividualScoreList(int,String,String,int)
	{GET [/examinationInstance/getIndividualRankInfo]}: getIndividualRankInfo(Integer,Integer,Integer)
	{GET [/examinationInstance/getIndividualRankInfoWithDuplicateRemoval]}: getIndividualRankInfoWithDuplicateRemoval(Integer,Integer,Integer)
	{GET [/examinationInstance/getExaminationInstanceWrapperAndProcessById]}: getExaminationInstanceWrapperAndProcessById(Integer)
	{POST [/examinationInstance/getInstanceWrapperAndProcessList]}: getInstanceWrapperAndProcessList(JSONObject)
	{GET [/examinationInstance/notApprovedExaminationInstanceList]}: getNotApprovedExaminationInstanceList(Integer,Integer,Integer,Integer)
	{GET [/examinationInstance/getNotApprovedExaminationInstanceListAndLength]}: getNotApprovedExaminationInstanceListAndLength(Integer,Integer,Integer,Integer)
	{POST [/examinationInstance/approveInstance]}: approveInstance(JSONObject)
	{[GET, POST] [/examinationInstance/exportNoRankListOfExamination]}: exportNoRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/examinationInstance/exportRankListOfExamination]}: exportRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{POST [/examinationInstance/getCountResult]}: getCountResult(JSONObject)
	{[GET, POST] [/examinationInstance/exportDetailRankListOfExaminationInCompany]}: exportDetailRankListOfExaminationInCompany(HttpServletRequest,HttpServletResponse)
	{ [/examinationInstance/exportInstanceOfExaminee]}: exportInstanceOfExaminee(HttpServletRequest,HttpServletResponse)
	{GET [/examinationInstance/exportAbsentList]}: exportAbsentList(HttpServletRequest,HttpServletResponse)
	{GET [/examinationInstance/getAbsentList]}: getAbsentList(Integer)
	{GET [/examinationInstance/getUserListOfExamination]}: getUserListOfExamination(Integer)
	{GET [/examinationInstance/getExaminationAnalysisData]}: getAnalysisData(Integer)
	{GET [/examinationInstance/getDistributionOfScore]}: getDistributionOfScore(Integer)
	{GET [/examinationInstance/getDistributionOfDuration]}: getDistributionOfDuration(Integer)
	{POST [/examinationInstance/getExamineeList]}: getExamineeList(JSONObject)
	{POST [/examinationInstance/getRecentExaminationInstanceListWithAverage]}: getRecentExaminationInstanceListWithAverage(JSONObject)
	{GET [/examinationInstance/getExaminationInstanceListWithUserInfoByExaminationId]}: getExaminationInstanceListWithUserInfoByExaminationId(int,int,int,int)
	{GET [/examinationInstance/examinationInstanceWrapperListOfExamination]}: getExaminationInstanceWrapperListOfExamination(String,int,Integer,Integer)
	{GET [/examinationInstance/rankOfExaminationInstance]}: getRankOfExaminationInstance(int,int,int)
	{GET [/examinationInstance/rankListOfExamination]}: getRankListOfExamination(Integer,Integer,Integer,Integer,Integer)
	{GET [/examinationInstance/getTopTenRankListAndTotalExamineeNumOfExamination]}: getTopTenRankListAndTotalExamineeNumOfExamination(int)
	{GET [/examinationInstance/getLatestTenListAndTotalExamineeNumOfExamination]}: getLatestTenListAndTotalExamineeNumOfExamination(Integer)
	{GET [/examinationInstance/rankListOfExaminationByCode]}: getRankListOfExaminationByCode(String)
	{GET [/examinationInstance/getTotalTimes]}: getTotalTimes(Integer,Integer)
	{GET [/examinationInstance/timesOfExaminationOfExaminee]}: getTimesOfExaminationOfExaminee(int,int,String,String)
	{POST [/examinationInstance/createExaminationInstance]}: createExaminationInstance(JSONObject,HttpServletRequest)
	{POST [/examinationInstance/createExaminationInstanceWithContentCheck]}: createExaminationInstanceWithContentCheck(JSONObject,HttpServletRequest,HttpServletResponse)
	{POST [/examinationInstance/getExaminationInstanceWrapperListOfExaminee]}: getExaminationInstanceWrapperListOfExaminee(JSONObject)
	{GET [/examinationInstance/examinationInstanceWrapperListOfExaminee]}: getExaminationInstanceWrapperListOfExaminee(int,int,int,int)
	{ [/examinationInstance/getExaminationInstanceInfoById]}: getExaminationInstanceInfoById(Integer)
	{GET [/examinationInstance/delete]}: delete(int,String)
	{POST [/examinationInstance/create]}: create(JSONObject,HttpServletRequest,HttpServletResponse)
[10:22:02:365] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationInstanceEventController:
	{POST [/examinationInstanceEvent/getList]}: getList(JSONObject)
	{POST [/examinationInstanceEvent/getExamBehaviorList]}: getExamBehaviorList(JSONObject)
	{POST [/examinationInstanceEvent/getSummaryInfo]}: getSummaryInfo(JSONObject)
	{POST [/examinationInstanceEvent/insert]}: insert(ExaminationInstanceEvent)
[10:22:02:370] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationInstanceProcessController:
	{GET [/examinationInstanceProcess/examinationInstanceProcessWrapperList]}: getExaminationInstanceProcessWrapperList(int,Integer,Integer,Integer)
	{GET [/examinationInstanceProcess/examinationInstanceProcessWrapperListAndNum]}: examinationInstanceProcessWrapperListAndNum(Integer,Integer,Integer,Integer)
	{GET [/examinationInstanceProcess/wrongQuestionList]}: getWrongQuestionsOfExaminee(int,int,int)
	{GET [/examinationInstanceProcess/rightWrongNumOfExamination]}: getQuestionRightWrongNumOfExamination(int)
	{GET [/examinationInstanceProcess/rightWrongNumOfQuestion]}: getQuestionRightWrongNum(int)
	{GET [/examinationInstanceProcess/getDistributionGroupByAnswerContent]}: getDistributionGroupByAnswerContent(Integer,Integer)
	{GET [/examinationInstanceProcess/getAnalysisOfExamination]}: getAnalysisOfExamination(Integer,Integer,Integer,String)
	{POST [/examinationInstanceProcess/changeQuestionResult]}: changeQuestionResult(JSONObject)
	{GET [/examinationInstanceProcess/exportQuestionAnalysis]}: exportQuestionAnalysis(Integer,HttpServletResponse)
	{POST [/examinationInstanceProcess/performAiScoring]}: performAiScoring(JSONObject)
[10:22:02:376] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationNameListController:
	{GET [/examinationNameList/getList]}: getList(Integer,Integer)
	{POST [/examinationNameList/upInsert]}: upInsert(JSONObject)
	{POST [/examinationNameList/addExaminationNameList]}: addExaminationNameList(JSONObject)
[10:22:02:385] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationStartUpController:
	{GET [/examinationStartUp/getCount]}: getCount(Integer,Integer)
	{GET [/examinationStartUp/getCountGroupByUser]}: getCountGroupByUser(Integer,Integer,Integer)
	{GET [/examinationStartUp/add]}: add(Integer,Integer)
	{GET [/examinationStartUp/delete]}: delete(Integer,Integer)
[10:22:02:387] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationTagsController:
	{POST [/examinationTags/createrExamTags]}: createExamTags(JSONObject)
	{GET [/examinationTags/getAllExamTagsOfCreaterIdInCompany]}: getAllExamTagsOfCreaterIdInCompany(Integer,Integer)
	{GET [/examinationTags/getAllExamTagsExcludingCreaterIdInCompany]}: getAllExamTagsExcludingCreaterIdInCompany(int,int)
	{ [/examinationTags/getAllExamTagsOfCompanyId]}: getAllExamTagsOfCompanyId(int,Boolean)
	{ [/examinationTags/getAllExamTagsOfUserInCompany]}: getAllExamTagsOfUserInCompany(Integer,Integer)
	{ [/examinationTags/getTagsOfExam]}: getTagsOfExam(int)
[10:22:02:387] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExaminationWxGidController:
	{GET [/examinationWxGid/getList]}: getList(Integer)
	{POST [/examinationWxGid/addIfNoExist]}: addEntity(ExaminationWxGid)
	{GET [/examinationWxGid/getEntity]}: getEntity(Integer,String)
	{GET [/examinationWxGid/delete]}: delete(Integer,String)
[10:22:02:394] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookController:
	{GET [/exerciseBook/getCatagoriesAndNum]}: getCatagoriesAndNum(Integer,Integer,String)
	{GET [/exerciseBook/getTagsGroupByDomain]}: getTagsGroupByDomain(Integer,Integer,String)
	{GET [/exerciseBook/getExerciseBookListByCatagory]}: getExerciseBookListByCatagory(Integer,Integer,String,String,String,Integer,Integer)
	{GET [/exerciseBook/getExerciseBookListAndTotalNum]}: getExerciseBookListAndTotalNum(Integer,Integer,String,String,String,Integer,Integer)
	{GET [/exerciseBook/getDailyPracticeResult]}: getDailyPracticeResult(Integer,Integer,Date,Date)
	{GET [/exerciseBook/getRecommendedList]}: getRecommendedList(Integer,Integer,Integer)
	{GET [/exerciseBook/getFavoriteRankList]}: getFavoriteRankList(String,Integer,Integer,Integer,Integer)
	{POST [/exerciseBook/getExerciseBookListOfCreater]}: getExerciseBookListOfCreater(JSONObject)
	{GET [/exerciseBook/exerciseBookListOfCreater]}: getExerciseBookListOfCreater(Integer,String,String,String,Integer,Integer,Integer)
	{POST [/exerciseBook/sendQiyeweixinQkkNotice]}: sendQiyeweixinQkkNotice(JSONObject)
	{POST [/exerciseBook/batchUpdate]}: batchUpdate(JSONArray)
	{ [/exerciseBook/getExerciseBookList]}: getExerciseBookList(Integer,String,String,String,Integer,Integer,Integer)
	{POST [/exerciseBook/create]}: createExerciseBook(JSONObject)
	{POST [/exerciseBook/createExerciseBookByQuestionTypeAndCategories]}: createExerciseBookByQuestionTypeAndCategories(JSONObject)
	{GET [/exerciseBook/modifyExerciseBook]}: modifyExerciseBook(String,String)
	{GET [/exerciseBook/delete]}: deleteExerciseBook(int)
	{GET [/exerciseBook/deleteExerciseBookAndRelation]}: deleteExerciseBookAndRelation(Integer)
	{GET [/exerciseBook/getRecommendedKeyWords]}: getRecommendedKeyWords()
	{ [/exerciseBook/exerciseBookListOfCreaterIncludingFavorite]}: getExerciseBookListOfCreaterIncludingFavorite(int,int,int,int,int)
	{ [/exerciseBook/exerciseBook]}: getExerciseBookById(int)
	{GET [/exerciseBook/getExampleQuestions]}: getExampleQuestions(Integer)
	{ [/exerciseBook/exerciseBookBeginInfo]}: getExerciseBookBeginInfoById(Integer,Integer)
	{GET [/exerciseBook/getExerciseBookBeginInfoById]}: getKsiteExerciseBookBeginInfoById(Integer,Integer,Integer)
	{GET [/exerciseBook/getExerciseBookIndexPageInfo]}: getExerciseBookIndexPageInfo(Integer)
	{GET [/exerciseBook/getPracticeSummaryInfo]}: getPracticeSummaryInfo(Integer)
	{ [/exerciseBook/exerciseBookWrapper]}: getExerciseBookWrapperById(String)
	{ [/exerciseBook/switchDisplayToExaminee]}: switchDisplayToExaminee(Integer,Boolean)
	{GET [/exerciseBook/advancedExerciseBookWrapper]}: getAdvancedExerciseBookWrapperById(int)
	{POST [/exerciseBook/doExercise]}: doExercise(JSONObject)
	{POST [/exerciseBook/doExerciseSecure]}: doExerciseSecure(JSONObject)
	{GET [/exerciseBook/rename]}: renameExerciseBook(String,int)
	{POST [/exerciseBook/savePracticeSummary]}: savePracticeSummary(JSONObject)
	{POST [/exerciseBook/update]}: update(ExerciseBook)
[10:22:02:396] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookExamineeController:
	{GET [/exerciseBookExaminee/getExerciseBookExaminee]}: getExerciseBookExaminee(Integer)
	{POST [/exerciseBookExaminee/updateExerciseBookExaminee]}: updateExerciseBookExaminee(JSONObject)
[10:22:02:398] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookFavorateController:
	{ [/exerciseBookFavorate/addFavorite]}: addInFavoriteList(int,int)
	{ [/exerciseBookFavorate/addBatchFavorite]}: addBatchFavorite(String,int)
	{ [/exerciseBookFavorate/cancelFavorite]}: deleteFavoriteOfUser(Integer,Integer)
	{GET [/exerciseBookFavorate/myFavoriteExerciseBookPage]}: getMyFavoriteExerciseBookPageInfo(Integer,Integer)
	{GET [/exerciseBookFavorate/getMyFavoriteInfo]}: getMyFavoriteInfo(Integer,Integer)
	{ [/exerciseBookFavorate/getSomeoneExerciseBookAndUserPracticeInfo]}: getSomeoneExerciseBookAndUserPracticeInfo(int,int)
	{ [/exerciseBookFavorate/getCompanyExerciseBookAndUserPracticeInfo]}: getCompanyExerciseBookAndUserPracticeInfo(int,int)
	{POST [/exerciseBookFavorate/getCompanyExamineePracticeInfo]}: getCompanyExamineePracticeInfo(JSONObject)
	{GET [/exerciseBookFavorate/getCompanyPracticeSummary]}: getCompanyPracticeSummary(Integer,Integer)
	{ [/exerciseBookFavorate/getEntity]}: getEntity(Integer,Integer)
[10:22:02:399] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookGradeController:
	{POST [/exerciseBookGrade/add]}: add(ExerciseBookGrade)
[10:22:02:399] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookPracticeProcessController:
	{GET [/exerciseBookPracticeProcess/getRankOfInterval]}: getRankOfInterval(Integer,Integer,Integer,String,String,Integer,Integer)
	{GET [/exerciseBookPracticeProcess/getSequenceNoInRankOfInterval]}: getSequenceNoInRankOfInterval(Integer,Integer,Integer,Integer,String,String)
	{GET [/exerciseBookPracticeProcess/getRankInfo]}: getRankInfo(Integer,Integer,Integer,Integer,String,String,Integer,Integer)
[10:22:02:402] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookPracticeSummaryController:
	{GET [/exerciseBookPracticeSummary/getUserPracticeSummary]}: getUserPracticeSummary(Integer,Integer,String,String)
	{POST [/exerciseBookPracticeSummary/getUserPracticeListWithSummary]}: getUserPracticeListWithSummary(JSONObject)
	{POST [/exerciseBookPracticeSummary/getDepartmentPracticeStatistics]}: getDepartmentPracticeStatistics(JSONObject)
	{GET [/exerciseBookPracticeSummary/exportUserPracticeSummary]}: exportUserPracticeSummary(HttpServletRequest,HttpServletResponse)
	{GET [/exerciseBookPracticeSummary/getRankOfInterval]}: getRankOfInterval(Integer,Integer,Integer,String,String,Integer,Integer)
	{GET [/exerciseBookPracticeSummary/getSequenceNoInRankOfInterval]}: getSequenceNoInRankOfInterval(Integer,Integer,Integer,Integer,String,String)
	{GET [/exerciseBookPracticeSummary/getRankInfo]}: getRankInfo(Integer,Integer,Integer,Integer,String,String,Integer,Integer)
[10:22:02:403] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookPraiseController:
	{ [/exerciseBookPraise/add]}: addPraise(int,int)
	{ [/exerciseBookPraise/cancel]}: cancelPraise(int,int)
[10:22:02:403] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookQuestionController:
	{GET [/exerciseBookQuestion/exerciseBookQuestionWrapperList]}: getExerciseBookQuestionsById(int,Integer,Integer)
	{GET [/exerciseBookQuestion/getExerciseBookQuestionWrapperListWithRedLock]}: getExerciseBookQuestionWrapperList(Integer,Integer,Integer)
	{GET [/exerciseBookQuestion/exerciseBookQuestionWrapperListAndTotalNum]}: getExerciseBookQuestionsAndTotalNumById(Integer,Integer,Integer)
	{GET [/exerciseBookQuestion/getDayDayExerciseQuestionList]}: getDayDayExerciseQuestionList(Integer,Integer,Integer,Boolean)
[10:22:02:403] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookSummaryController:
	{GET [/exerciseBookSummary/getSummary]}: getSummary(Integer)
[10:22:02:404] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ExerciseBookUserRecordController:
	{GET [/exerciseBookUserRecord/updateHavePractisedNum]}: updateHavePractisedNum(Integer,Integer)
	{ [/exerciseBookUserRecord/getExerciseProgress]}: getExerciseBookProgressOfUser(int,int)
	{GET [/exerciseBookUserRecord/exportUserPracticeSummary]}: exportUserPracticeSummary(Integer,HttpServletResponse)
[10:22:02:406] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.FileAccessPermissionController:
	{POST [/fileAccessPermission/generateUnlockAdQRCode]}: generateUnlockAdQRCode(JSONObject)
	{POST [/fileAccessPermission/addFileAccessPermission]}: addFileAccessPermission(JSONObject)
	{POST [/fileAccessPermission/getFileAccessPermission]}: getFileAccessPermission(JSONObject)
[10:22:02:407] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.FileController:
	{[GET, POST] [/file/uploadFile]}: uploadFile(HttpServletRequest)
	{[GET, POST] [/file/uploadFileWithCheck]}: uploadFileWithCheck(HttpServletRequest)
	{ [/file/downloadFile]}: downLoad(String,HttpServletResponse,boolean)
	{GET [/file/downloadNetFile]}: downloadNetFile(HttpServletRequest,HttpServletResponse)
	{POST [/file/uploadOssFile]}: uploadOssFile(HttpServletRequest,HttpServletResponse)
[10:22:02:407] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.FreePracticeOfUserController:
	{POST [/freePracticeOfUser/getFreePractice]}: getFreePractice(JSONObject)
	{GET [/freePracticeOfUser/getQuestionListOfFreePractice]}: getQuestionListOfFreePractice(Integer,Integer)
	{GET [/freePracticeOfUser/getSummaryGroupByQuestionType]}: getSummaryGroupByQuestionType(Integer,Integer)
	{GET [/freePracticeOfUser/delete]}: delete(Integer,Integer)
	{POST [/freePracticeOfUser/save]}: save(JSONObject)
[10:22:02:407] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.GameController:
	{GET [/game/getGameWrapper]}: getGameWrapper(Integer)
	{POST [/game/updateInsert]}: updateInsert(JSONObject)
[10:22:02:408] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.GameResultController:
	{GET [/gameResult/getRankInfo]}: getRankInfo(String,Integer,Integer)
[10:22:02:412] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.HomepageUserFollowedController:
	{GET [/homepageUserFollowed/getUserFollowedList]}: getUserFollowedList(Integer)
	{GET [/homepageUserFollowed/getUserFollowedInfo]}: getUserFollowedInfo(Integer,Integer)
	{POST [/homepageUserFollowed/update]}: update(HomepageUserFollowedWrapper)
	{GET [/homepageUserFollowed/delete]}: delete(Integer,Integer)
	{POST [/homepageUserFollowed/save]}: save(HomepageUserFollowedWrapper)
[10:22:02:417] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.InviteRelationController:
	{[GET, POST] [/inviteRelation/exportRankListOfEvent]}: exportRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{GET [/inviteRelation/saveInviteRelation]}: saveInviteRelation(int,int,int)
	{GET [/inviteRelation/getRecentInviteRelationAnnouncement]}: getRecentInviteRelationAnnouncement(int)
	{GET [/inviteRelation/getExaminationInstanceListOfInvitee]}: getExaminationInstanceListOfInvitee(int,int,int,int,int)
	{GET [/inviteRelation/getEventResultOfInviter]}: getEventResultOfInviter(int,int,int)
	{GET [/inviteRelation/getResultOfEvent]}: getResultOfEvent(int,int,int,int,int)
[10:22:02:418] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ManualServiceController:
	{POST [/manualService/addManualImportRecord]}: addManualImportRecord(JSONObject)
	{POST [/manualService/getManualImportRecords]}: getManualImportRecords(JSONObject)
	{POST [/manualService/updateManualImportRecord]}: updateManualImportRecord(ManualServiceRecord)
[10:22:02:418] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MessageController:
	{GET [/message/sendMessage]}: sendMessage(String)
[10:22:02:420] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MessagePushSubscriptionController:
	{POST [/messagePushSubscription/getList]}: getList(JSONObject)
	{GET [/messagePushSubscription/deleteEntity]}: deleteEntity(String,Integer,Integer)
	{POST [/messagePushSubscription/changeBatch]}: changeBatch(JSONObject)
	{POST [/messagePushSubscription/saveIfNotExisted]}: saveIfNotExisted(MessagePushSubscription)
	{GET [/messagePushSubscription/getEntity]}: getEntity(String,Integer,Integer)
[10:22:02:421] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MessageScheduleController:
	{GET [/messageSchedule/sendMessage]}: sendMessage(String)
	{POST [/messageSchedule/deleSave]}: deleSave(JSONObject)
	{GET [/messageSchedule/getList]}: getList(String,Integer,Integer)
	{POST [/messageSchedule/update]}: updateIgnoreNull(MessageSchedule)
[10:22:02:422] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MonitorViewController:
	{GET [/view/{sessionId}]}: viewMonitor(String,Model)
[10:22:02:425] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.MultipleMarkingController:
	{GET [/multipleMarking/getWorkStateOfMarking]}: getWorkStateOfMarking(Integer,Integer)
	{GET [/multipleMarking/acceptMultipleMarking]}: acceptMultipleMarking(Integer,Integer)
	{GET [/multipleMarking/getMultipleMarkingExamintionListOfUser]}: getMultipleMarkingExamintionListOfUser(Integer)
	{GET [/multipleMarking/hasMultipleMarkingExaminationInstanceByExaminationInstanceId]}: hasMultipleMarkingExaminationInstanceByExaminationInstanceId(Integer)
	{GET [/multipleMarking/finishMarking]}: finishMarking(Integer)
[10:22:02:441] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.NameListController:
	{GET [/nameList/getList]}: getList(Integer)
	{GET [/nameList/getEntityWrapper]}: getEntityWrapper(Integer)
	{GET [/nameList/getEntity]}: getEntity(Integer)
	{POST [/nameList/update]}: update(JSONObject)
	{GET [/nameList/delete]}: delete(Integer)
	{POST [/nameList/save]}: save(JSONObject)
[10:22:02:451] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.NanXiangController:
	{POST [/nanxiang/getEntity]}: getEntity(NanxiangRegister)
	{GET [/nanxiang/check]}: check(String)
	{POST [/nanxiang/save]}: save(NanxiangRegister)
	{POST [/nanxiang/validate]}: validate(NanxiangRegister)
[10:22:02:452] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.OrderController:
	{GET [/order/getList]}: getList(String,String,Integer,Integer,Integer)
	{ [/order/getUnconsumedValueOfAccount]}: getUnconsumedValueOfAccount(String,Integer)
	{POST [/order/createManualOrder]}: createManualOrder(JSONObject)
	{ [/order/create]}: createOrder(JSONObject)
	{ [/order/createCountOrder]}: createCountOrder(JSONObject)
	{POST [/order/createCountOrderByWeChatNative]}: createOrderByWeChatNative(JSONObject)
	{POST [/order/createCountOrderByJSAPI]}: createCountOrderByJSAPI(JSONObject)
	{POST [/order/orderNotify]}: orderNotify(JSONObject)
	{GET [/order/getOrderById]}: getOrderById(Integer)
[10:22:02:453] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperController:
	{GET [/paper/getPaperById]}: getPaperById(Integer)
	{POST [/paper/batchUpdate]}: batchUpdate(JSONArray)
	{GET [/paper/getPaperWrapperWithRedLock]}: getPaperWrapperWithRedLock(Integer)
	{ [/paper/modifyPaper]}: modifyPaper(String,String)
	{POST [/paper/update]}: updatePaper(Paper)
	{ [/paper/paperListOfCreater]}: getPaperListOfCreater(String,int)
	{GET [/paper/getPaperWithFavoriteInfoAndOrderRelationInfo]}: getPaperWithFavoriteInfoAndOrderRelationInfo(Integer,Integer,Integer)
	{ [/paper/paperWrapper]}: getPaperWrapperById(String)
	{ [/paper/advancedPaperWrapper]}: getAdvancedPaperWrapperById(int)
	{ [/paper/paperListToAdmin]}: getPaperListToAdmin(String,String,int,int)
	{GET [/paper/getCategoriesAndNum]}: getCategoriesAndNum(Integer,Integer,String)
	{GET [/paper/getPaperList]}: getPaperList(Integer,Integer,String,String,String,Integer,Integer)
	{GET [/paper/getPaperListAndTotalNum]}: getPaperListAndTotalNum(Integer,Integer,String,String,String,Integer,Integer)
	{ [/paper/create]}: create(JSONObject)
[10:22:02:454] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperFavoriteController:
	{ [/paperFavorite/add]}: addFavorite(int,int)
	{ [/paperFavorite/cancel]}: cancelFavorite(int,int)
	{ [/paperFavorite/ifFavorite]}: ifFavorite(int,int)
	{ [/paperFavorite/getFavoriteList]}: getFavoriteList(int,int,int)
[10:22:02:454] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperPraiseController:
	{ [/paperPraise/add]}: addPraise(Integer,Integer)
	{ [/paperPraise/cancel]}: cancelPraise(Integer,Integer)
[10:22:02:454] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperQuestionController:
	{ [/paperQuestion/update]}: updateCompletion(BigDecimal,BigDecimal,int,BigDecimal,String,int)
	{ [/paperQuestion/updateSelection]}: updateSelection(BigDecimal,BigDecimal,int,BigDecimal,String,int)
	{GET [/paperQuestion/getQuestionWrapperListAndNum]}: getQuestionWrapperListAndNum(Integer,Integer,Integer)
[10:22:02:455] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PaperSummaryController:
	{GET [/paperSummary/getSummary]}: getSummary(Integer)
[10:22:02:455] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PassRaceGameController:
	{GET [/passRaceGame/getGameWrapper]}: getGameWrapper(Integer)
	{GET [/passRaceGame/getListAndTotalNum]}: getListAndTotalNum(Integer,Integer,Integer)
	{POST [/passRaceGame/update]}: update(JSONObject)
	{GET [/passRaceGame/delete]}: delete(Integer)
	{POST [/passRaceGame/save]}: save(JSONObject)
[10:22:02:461] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PassRaceResultController:
	{POST [/passRaceResult/save]}: save(JSONObject)
	{GET [/passRaceResult/getGameWrapper]}: getGameWrapper(Integer)
	{GET [/passRaceResult/getListAndTotalNum]}: getListAndTotalNum(Integer,Integer,Integer)
	{POST [/passRaceResult/update]}: update(JSONObject)
	{GET [/passRaceResult/delete]}: delete(Integer)
[10:22:02:462] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PassRaceStageController:
	{GET [/passRaceStage/getStageWrapper]}: getStageWrapper(Integer,Integer)
	{GET [/passRaceStage/getMyPlayedStageList]}: getMyPlayedStageList(Integer,Integer,Boolean)
[10:22:02:466] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PdfFileController:
	{ [/pdfFile/temporaryExaminationReport]}: createFreeExaminationReport(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/pdfFile/examinationReport]}: createExaminationPdfReport(HttpServletRequest,HttpServletResponse)
	{GET [/pdfFile/generatePdfByUser]}: generatePdfByUser(Integer,Integer)
[10:22:02:470] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PermissionAuthorizationController:
	{POST [/permissionAuthorization/getAuthorizedDepartmentList]}: getAuthorizedDepartmentList(JSONObject)
	{POST [/permissionAuthorization/getAuthorizedAdminList]}: getAuthorizedAdminList(JSONObject)
	{POST [/permissionAuthorization/getAuthorizedCompanyInfoWith2Grade]}: getAuthorizedCompanyInfoWithRecursionDepartment(JSONObject)
	{GET [/permissionAuthorization/getAuthorizedCompanyPracticeSummary]}: getAuthorizedCompanyPracticeSummary(Integer,Integer)
	{POST [/permissionAuthorization/getAuthorizedCompanyExamineePracticeInfo]}: getAuthorizedCompanyExamineePracticeInfo(JSONObject)
	{GET [/permissionAuthorization/getList]}: getList(Integer,Integer)
	{POST [/permissionAuthorization/changeBatch]}: changeBatch(JSONObject)
	{GET [/permissionAuthorization/getEntity]}: getEntity(Integer,Integer,Integer)
	{POST [/permissionAuthorization/save]}: save(List)
[10:22:02:471] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PermissionController:
	{GET [/permission/getList]}: getList()
[10:22:02:471] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PointConsumerationController:
	{POST [/pointConsumeration/getUserConsumeList]}: getUserConsumeList(JSONObject)
	{POST [/pointConsumeration/save]}: save(PointConsumeration)
[10:22:02:471] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PointDetailController:
	{GET [/pointDetail/exportPointRankOfCompany]}: exportPointRankOfCompany(HttpServletRequest,HttpServletResponse)
	{POST [/pointDetail/getCompanyRankInfo]}: getCompanyRankInfo(JSONObject)
	{GET [/pointDetail/getRankInfo]}: getRankInfo(Integer,Integer,String,String,String,Integer,Integer)
[10:22:02:472] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PointRuleController:
	{POST [/pointRule/upInsert]}: upInsert(PointRule)
	{GET [/pointRule/getEntity]}: getEntity(Integer)
[10:22:02:472] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PointUserController:
	{GET [/pointUser/getUserPointInfo]}: getUserPointInfo(Integer,Integer)
	{GET [/pointUser/clear]}: clear(Integer,Integer,Integer)
[10:22:02:472] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.PracticePushController:
	{GET [/practicePush/list]}: getList(Integer,Integer,String,Integer,Integer,Integer)
	{GET [/practicePush/toggleStatus]}: toggleStatus(Integer,Integer,Integer)
	{GET [/practicePush/detail]}: getDetail(Integer,Integer)
	{GET [/practicePush/checkAndExecutePushTasks]}: checkAndExecutePushTasks()
	{POST [/practicePush/update]}: update(JSONObject)
	{GET [/practicePush/delete]}: delete(Integer,Integer)
	{POST [/practicePush/create]}: create(JSONObject)
[10:22:02:474] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductController:
	{ [/product/getProductList]}: getProductList(String)
	{GET [/product/getListOfProduct]}: getListOfProduct(String)
[10:22:02:478] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductCountTransactionController:
	{POST [/productCountTransaction/getList]}: getList(JSONObject)
[10:22:02:479] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductIntroductionController:
	{ [/productIntroduction/list]}: getProductIntroductionWrapperList()
[10:22:02:480] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductOrderRelationController:
	{GET [/productOrderRelation/getProductOrderRelationWrapper]}: getProductOrderRelationWrapper(String,Integer)
	{GET [/productOrderRelation/checkIfOverdue]}: checkIfOverdue(String,Integer)
	{GET [/productOrderRelation/getProductOrderRelation]}: getProductOrderRelation(String,Integer)
	{POST [/productOrderRelation/updateProductOrderRelation]}: updateProductOrderRelation(ProductOrderRelation)
	{GET [/productOrderRelation/refresh]}: refresh(Integer)
[10:22:02:482] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.ProductOrderRelationCountController:
	{GET [/productOrderRelationCount/getProductOrderRelationCount]}: getProductOrderRelationCount(String,Integer,String)
	{POST [/productOrderRelationCount/getProductOrderRelationCountList]}: getProductOrderRelationCountList(JSONObject)
	{GET [/productOrderRelationCount/tryToConsumeVADVIP]}: tryToConsumeVADVIP(Integer,Integer)
	{GET [/productOrderRelationCount/consumeVADVIP]}: consumeVADVIP(Integer,Integer)
	{GET [/productOrderRelationCount/consume]}: consume(String,Integer,String,Integer)
[10:22:02:483] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QrcodeScanEntryController:
	{GET [/qrcodeScan/checkIfFollowed]}: checkIfFollowed(HttpServletRequest)
[10:22:02:483] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionBankQuestionController:
	{GET [/questionBankQuestion/deleteQuestionBankQuestion]}: deleteQuestionBankQuestion(Integer)
	{POST [/questionBankQuestion/changeQuestionBank]}: changeQuestionBank(JSONObject)
[10:22:02:483] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionCompositionChildController:
	{POST [/compositionChild/updateChildQuestionEntity]}: updateChildQuestionEntity(QuestionCompositionChild)
	{GET [/compositionChild/getList]}: getList(Integer)
	{POST [/compositionChild/getPaperQuestionListMapByIds]}: getListByIds(JSONObject)
	{POST [/compositionChild/getListByIds]}: getListByIds(JSONArray)
[10:22:02:511] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionController:
	{GET [/question/getQuestionWrapper]}: getQuestionWrapper(String)
	{GET [/question/modifyCatagory]}: modifyCatagory(String,String)
	{POST [/question/modifyQuestionCatagory]}: modifyCatagory(JSONObject)
	{GET [/question/categoryAndTotalNum]}: getCategoryAndTotalNum(Integer,String,Integer)
	{POST [/question/getQuestionTypeAndCategoryAndTotalNum]}: getQuestionTypeAndCategoryAndTotalNum(JSONObject)
	{GET [/question/questionTypeAndCategoryAndTotalNum]}: getQuestionTypeAndCategoryAndTotalNum(Integer,Integer,Integer)
	{POST [/question/getQuestionTypeAndCategoryAndTotalNumWithPermission]}: getQuestionTypeAndCategoryAndTotalNumWithPermission(JSONObject)
	{GET [/question/getQuestionCatagoryAndTotalNum]}: getQuestionCatagoryAndTotalNum(Integer,int)
	{POST [/question/getQuestionCatagoryAndTotalNumWithPermission]}: getQuestionCatagoryAndTotalNumWithPermission(JSONObject)
	{GET [/question/machineChooseQuestion]}: getMachineChooseQuestion(int,String,int,int)
	{POST [/question/getQuestionsByGroupConditionAdvancedWithPermission]}: getQuestionsByGroupConditionAdvancedWithPermission(JSONObject)
	{GET [/question/getQuestionsByGroupCondition]}: getQuestionsAndLengthByGroupCondition(Integer,String,String,String,int,Integer,Integer)
	{GET [/question/getQuestionsList]}: getQuestionsList(Integer,String,String,String,int,Integer,Integer)
	{POST [/question/checkDuplicates]}: checkDuplicates(DuplicateCheckRequest)
	{GET [/question/exportQuestions]}: exportQuestions(HttpServletRequest,HttpServletResponse)
	{POST [/question/saveBatch]}: saveBatch(JSONObject)
	{[GET, POST] [/question/uploadFile]}: uploadFile(HttpServletRequest)
	{[GET, POST] [/question/uploadFileWithCheck]}: uploadFileWithCheck(HttpServletRequest)
	{POST [/question/updateQuestion]}: updateQuestion(Question)
	{[GET, POST] [/question/modifyQuestion]}: modifyQuestion(HttpServletRequest)
	{GET [/question/deleteQuestion]}: deleteQuestion(String)
	{POST [/question/deleteQuestionByQuery]}: deleteQuestion(JSONObject)
	{GET [/question/deleteQuestionList]}: deleteQuestionList(String)
	{POST [/question/deleteBatchQuestion]}: deleteBatchQuestion(JSONObject)
	{GET [/question/deleteQuestionIncludingExample]}: deleteQuestionIncludingExample(int,int)
	{GET [/question/questionListOfCreaterByCategory]}: getQuestionListOfCreaterByCategory(String,String,int)
	{GET [/question/questionListOfCreaterByType]}: getQuestionListOfCreaterByType(String,String,int,int,int)
	{GET [/question/questionListOfCreaterByTypeIncludingExample]}: getQuestionListOfCreaterByTypeIncludingExample(String,String,int,int,int)
	{GET [/question/questionWrapperListOfCreaterByType]}: getQuestionWrapperListOfCreaterByType(String,String,String,String,int,int,int)
	{[GET, POST] [/question/questionWrapperListOfCreaterByTypeIncludingExample]}: getQuestionWrapperListOfCreaterByTypeIncludingExample(String,String,int,int,int)
	{GET [/question/questionListForPractice]}: getQuestionListOfCreaterForPractice(String,String,String,int,int,int)
	{GET [/question/question]}: getQuestionById(String)
	{GET [/question/questionWrapper]}: getQuestionWrapperById(String)
	{GET [/question/getCategoryList]}: getCategoryList(Integer,String,Integer)
	{POST [/question/importQuestionUnion], produces [application/json;charset=utf-8]}: importQuestionUnion(HttpServletRequest)
	{POST [/question/importWordQuestion], produces [application/json;charset=utf-8]}: importWordQuestion(HttpServletRequest)
	{[GET, POST] [/question/importExcelQuestion], produces [application/json;charset=utf-8]}: importExcelQuestion(HttpServletRequest)
	{POST [/question/getQuestionsByGroupConditionAdvanced]}: getQuestionsByGroupConditionAdvanced(JSONObject)
	{GET [/question/modifyDefaultMark]}: modifyDefaultMarkOfQuestions(String,BigDecimal)
	{POST [/question/questionListOfCreater]}: getQuestionListOfCreater(JSONObject)
	{[GET, POST] [/question/createQuestion]}: create(HttpServletRequest)
[10:22:02:515] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionFavoriteController:
	{ [/questionFavorite/add]}: addFavorite(int,int)
	{ [/questionFavorite/cancel]}: cancelFavorite(int,int)
	{ [/questionFavorite/ifFavorite]}: ifFavorite(int,int)
[10:22:02:527] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionRandomController:
	{POST [/questionRandom/createByPost]}: createByPost(JSONObject)
	{GET [/questionRandom/list]}: getQuestionRandomListByCreaterIdInCompany(Integer,int,int,int)
	{GET [/questionRandom/questionRandomExtend]}: getQuestionRandomExtend(int)
	{POST [/questionRandom/modify]}: modify(JSONObject)
	{GET [/questionRandom/deleteQuestionList]}: deleteQuestionList(String)
	{GET [/questionRandom/delete]}: delete(int)
	{GET [/questionRandom/create]}: create(String,String)
[10:22:02:529] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.QuestionSummaryController:
	{POST [/questionSummary/getWrongQuestionList]}: getWrongQuestionList(JSONObject)
	{POST [/questionSummary/removeWrongQuestion]}: removeWrongQuestion(JSONObject)
[10:22:02:529] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.SMSNoticeController:
	{GET [/notice/sendValidCodeSMS]}: sendValidCodeSMS(String,String)
	{GET [/notice/sendQKKRegisterNotice]}: sendQKKRegisterNotice(String,String)
	{GET [/notice/sendQKKTestOverdueNotice]}: sendQKKTemplateNotice(Integer)
	{GET [/notice/sendQKKTimeingNotice]}: sendQKKTimeingNotice(String)
[10:22:02:530] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.SoftVersionController:
	{GET [/version/reloadVersion]}: reloadVersion(String)
	{GET [/version/setSystemRunningState]}: setSystemRunningState(String)
	{GET [/version/setAdProvider]}: setAdProvider(String)
	{GET [/version/getSystemSettings]}: getSystemSettings(String)
	{GET [/version/setProductSystemRunningState]}: setProductSystemRunningState(String,String)
	{GET [/version/getProductSystemSettings]}: getProductSystemSettings(String)
	{GET [/version/getVersion]}: getVersion(String)
[10:22:02:531] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.SysUserRoleController:
	{GET [/sysUserRole/unbindExamineeUserRole]}: unbindExamineeUserRole(Integer,Integer)
	{POST [/sysUserRole/unbindExamineeUserRoleWithPermission]}: unbindExamineeUserRoleWithPermission(JSONObject)
	{GET [/sysUserRole/unbindAdminUserRole]}: unbindAdminUserRole(Integer,Integer)
	{POST [/sysUserRole/unbindAdminUserRoleWithPermission]}: unbindAdminUserRoleWithPermission(JSONObject)
	{POST [/sysUserRole/getSysUserRoleList]}: getSysUserRoleList(JSONObject)
	{GET [/sysUserRole/getSysUserRoleListByUsernameAndPassword]}: getSysUserRoleListByPhoneAndPassword(String,String,String,HttpServletRequest)
	{GET [/sysUserRole/getNewSysUserRoleList]}: getNewSysUserRoleList(String,String,String,HttpServletRequest)
	{GET [/sysUserRole/getSysUserRoleListOfUserInCompany]}: getSysUserRoleListOfUserInCompany(Integer,Integer,String)
	{GET [/sysUserRole/getCompanyInfoByAdminUserId]}: getCompanyInfoByAdminUserId(Integer)
	{GET [/sysUserRole/transmitAdminToOther]}: transmitAdminToOther(Integer,Integer,Integer)
	{POST [/sysUserRole/batchAddChildAdmin]}: batchAddChildAdmin(JSONObject)
	{GET [/sysUserRole/getSysUserRoleListOfUser]}: getSysUserRoleListOfUser(Integer,String,Integer)
	{GET [/sysUserRole/getSysUserRoleListOfCompany]}: getSysUserRoleListOfCompany(Integer,Integer)
	{POST [/sysUserRole/addSysUserRole]}: addSysUserRole(JSONObject)
[10:22:02:532] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.SystemMessageController:
	{GET [/systemMessage/handleSystemMessage]}: handleSystemMessage()
	{GET [/systemMessage/changeDepartmentId]}: changeDepartmentId(Integer)
	{GET [/systemMessage/batchDelete]}: batchDelete(String,String,String)
	{POST [/systemMessage/insert]}: insert(SystemMessage)
[10:22:02:532] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.TaxIdentityInfoController:
	{ [/taxIdentity/createTaxIdentityInfo]}: createTaxIdentityInfo(String)
	{ [/taxIdentity/modifyTaxIdentityInfo]}: modifyTaxIdentityInfo(String)
	{ [/taxIdentity/taxIdentityInfo]}: getTaxIdentityInfoByUserId(String,String)
[10:22:02:533] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.TaxSheetApplicationController:
	{ [/taxSheetApplication/createTaxSheetApplication]}: createTaxSheetApplication(String)
	{ [/taxSheetApplication/taxSheetApplicationList]}: getTaxSheetApplicationListOfUser(String)
	{ [/taxSheetApplication/taxSheetApplication]}: getTaxSheetApplicationById(String)
[10:22:02:534] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.TemplateController:
	{GET [/template/getMyTemplates]}: getMyTemplates(int,String)
[10:22:02:534] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.TestController:
	{GET [/test/api]}: apiAccess()
	{GET [/test/getKeysByPrefix]}: getKeysByPrefix(String)
	{GET [/test/deleteByPrex]}: deleteByPrex(String)
[10:22:02:535] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UniversalOrderController:
	{GET [/univsersalOrder/getList]}: getList(String,String,Integer,Integer,Integer)
	{ [/univsersalOrder/create]}: createOrder(JSONObject)
	{POST [/univsersalOrder/createOrderByWeChatNative]}: createOrderByWeChatNative(JSONObject)
	{POST [/univsersalOrder/createCountOrderByJSAPI]}: createCountOrderByJSAPI(JSONObject)
	{POST [/univsersalOrder/orderNotify]}: orderNotify(JSONObject)
	{GET [/univsersalOrder/getOrderById]}: getOrderById(Integer)
[10:22:02:537] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UniversalProductOrderRelationController:
	{POST [/universalProductOrderRelation/getProductOrderRelation]}: getProductOrderRelation(JSONObject)
[10:22:02:539] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserCompanyController:
	{POST [/userCompany/bindCompany]}: bindCompany(JSONObject)
	{POST [/userCompany/deleteUserOfCompanyWithPermission]}: deleteUserOfCompanyWithPermission(JSONObject)
	{POST [/userCompany/deleteBatch]}: deleteBatchWithPermission(JSONObject)
	{GET [/userCompany/deleteExamineeByDepartmentName]}: deleteExamineeByDepartmentName(Integer,String)
	{POST [/userCompany/deleteExamineeByDepartmentNameWithPermission]}: deleteExamineeByDepartmentNameWithPermission(JSONObject)
	{GET [/userCompany/getUserListByQkkQiyeCorpid]}: getUserListByQkkQiyeCorpid(String,String)
	{GET [/userCompany/getCompanyInfoByUserIdAndCompanyId]}: getUserCompanyInfoByUserIdAndCompanyId(int,int)
	{GET [/userCompany/getCompanyInfoByUserId]}: getCompanyInfoByUserId(int)
	{POST [/userCompany/getUserInfoListByIds]}: getUserInfoListByIds(JSONObject)
	{GET [/userCompany/saveUserInfoAndCompanyInfo]}: saveUserInfoAndCompanyInfo(int,int,int,String,String)
	{POST [/userCompany/updateUserInfoAndCompanyInfo]}: saveUserInfoAndCompanyInfo(JSONObject)
	{GET [/userCompany/deleteUserOfCompany]}: deleteUserOfCompany(int,int)
	{GET [/userCompany/getUserCompanyByPhone]}: getUserCompanyByPhone(String)
	{GET [/userCompany/getUserInfoWithCompanyInfo]}: getUserInfoWithCompanyInfo(Integer,Integer)
	{GET [/userCompany/getUserListByDepartmentId]}: getUserListByDepartmentId(Integer,Integer,Integer,Integer,Integer,Integer)
	{POST [/userCompany/getCompanyUserListByMap]}: getCompanyUserListByMap(JSONObject)
	{GET [/userCompany/getUserListOfDepartmentsInCludingSelf]}: getUserListOfDepartmentsInCludingSelf(String)
	{GET [/userCompany/getUserListOfDepartmentsExcludingSelf]}: getUserListOfDepartmentsExcludingSelf(String)
	{POST [/userCompany/getUserList]}: getUserListByName(JSONObject)
	{GET [/userCompany/getUserListByName]}: getUserListByName(String,Integer,Integer,Integer,Integer)
	{GET [/userCompany/ifRegisted]}: getIfRegisted(int,int)
	{GET [/userCompany/getUserListOfCompany]}: getUserListOfCompany(Integer,String,String,Integer,Integer)
	{GET [/userCompany/secureGetUserListOfCompany]}: secureGetUserListOfCompany(Integer,String,String,Integer,Integer,Integer,Integer,Integer,Integer)
	{GET [/userCompany/getGrantedUserListOfCompany]}: getGrantedUserListOfCompany(Integer,String,String,Integer,Integer,Integer,Integer,String,Integer,Integer,String,String)
	{POST [/userCompany/getCompanyUserList]}: getCompanyUserList(JSONObject)
	{POST [/userCompany/approvedRegist]}: approvedRegist(UserCompany)
	{POST [/userCompany/importUser]}: importUser(HttpServletRequest)
	{GET [/userCompany/getExamineeStatisticsOfCompany]}: getExamineeStatisticsOfCompany(Integer)
	{GET [/userCompany/exportUserListOfCompany]}: exportUserListOfCompany(Integer,Integer,HttpServletResponse)
	{GET [/userCompany/checkIfExceedMemberNumLimit]}: checkIfExceedMemberNumLimit(Integer,Integer)
[10:22:02:544] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserController:
	{GET [/user/getUserListByQkkQiyeCorpid]}: getUserListByQkkQiyeCorpid(String,String)
	{GET [/user/updateUserInfo]}: updateUserInfo(String)
	{POST [/user/getUserInfoListByIds]}: getUserInfoListByIds(List)
	{GET [/user/getUserByPhone]}: getUserByPhone(String)
	{GET [/user/getUserInfoWithCompanyInfo]}: getUserInfoWithCompanyInfo(Integer,Integer)
	{GET [/user/getUserInfoByOpenId]}: getUserByEteaOpenId(String,String)
	{GET [/user/getUserInfoById]}: getUserInfoById(int)
	{GET [/user/getUserInfoListByNickName]}: getUserInfoListByNickName(String)
	{POST [/user/supplyPhoneNo]}: supplyPhoneNo(JSONObject)
	{GET [/user/loginByValidationCode]}: loginByValidationCode(String,String,HttpServletRequest)
	{GET [/user/validateSMSPhoneIfMatched]}: validateSMSPhoneIfMatched(String,String)
	{GET [/user/oneKeyLogin]}: oneKeyLogin(String,HttpServletRequest)
	{GET [/user/login]}: loginByPhoneAndPassword(String,String,HttpServletRequest)
	{GET [/user/loginById]}: loginById(Integer)
	{POST [/user/getUserInfoByUserIdAndCompanyId]}: getUserInfoByUserIdAndCompanyId(JSONObject,HttpServletRequest)
	{POST [/user/loginWithAutoRegist]}: loginWithAutoRegist(JSONObject,HttpServletRequest)
	{GET [/user/changePassword]}: changePassword(Integer,String,String)
	{POST [/user/register]}: register(JSONObject)
	{POST [/user/update]}: update(User)
	{POST [/user/getPassword]}: getPassword(JSONObject)
[10:22:02:546] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserDeviceBindingController:
	{POST [/userDeviceBinding/unbindDevice]}: unbindDevice(JSONObject)
	{POST [/userDeviceBinding/getUserDeviceBinding]}: getUserDeviceBinding(JSONObject)
	{POST [/userDeviceBinding/bindDevice]}: bindDevice(JSONObject)
[10:22:02:546] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserExaminationPerformanceReportController:
	{ [/examinationPerformanceReport/consume]}: consume(Integer,Integer)
[10:22:02:546] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserFormController:
	{GET [/userForm/deleteForm]}: deleteFormOfUser(Integer,Integer)
	{GET [/userForm/getUserFormListByUserId]}: getUserFormListByUserId(Integer)
	{POST [/userForm/create]}: create(UserForm)
[10:22:02:547] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserFormFlowRecordController:
	{POST [/userFormFlowRecord/upsert]}: upsert(UserFormFlowRecord)
	{GET [/userFormFlowRecord/getFormFlowRecordWrapperById]}: getFormFlowRecordWrapper(Integer,Integer)
[10:22:02:547] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserOfYncxController:
	{ [/userOfYncx/exportDetailRankListOfExamination]}: exportDetailRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/userOfYncx/exportRankListOfExamination]}: exportRankListOfExamination(HttpServletRequest,HttpServletResponse)
	{[GET, POST] [/userOfYncx/examinationReport]}: createExaminationPdfReport(HttpServletRequest,HttpServletResponse)
	{GET [/userOfYncx/exportExaminationInstanceDetailExcelBundle]}: exportExaminationInstanceDetailExcelBundle(Integer,Integer)
	{GET [/userOfYncx/exportExaminationInstancePdfBundle]}: exportExaminationInstancePdfBundle(Integer,Integer)
[10:22:02:548] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserStatisticController:
	{ [/userStatistic/totalItemNum]}: totalNumber(Integer,Integer)
	{ [/userStatistic/getOnlineUserNumberOfExamination]}: getOnlineUserNumberOfExamination(int,int)
[10:22:02:551] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserSummaryController:
	{POST [/userSummary/upInsert]}: upInsert(JSONObject)
	{POST [/userSummary/getEntity]}: getEntity(UserSummary)
[10:22:02:552] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.UserViewPerformanceWithoutAdController:
	{GET [/userViewPerformanceWithoutAd/adEndedCallback]}: adEndedCallback(String,String)
	{POST [/userViewPerformanceWithoutAd/deletePermently]}: deletePermently(JSONObject)
	{ [/userViewPerformanceWithoutAd/checkIfAllowUserViewPerformanceWithoutAd]}: checkIfAllowUserViewPerformanceWithoutAd(int,int,int)
	{ [/userViewPerformanceWithoutAd/getUserViewPerformanceWithoutAdTransaction]}: getUserViewPerformanceWithoutAdTransaction(String,int,String,Integer,Integer)
	{GET [/userViewPerformanceWithoutAd/insertIfNotExisted]}: insertIfNotExisted(Integer,Integer,Integer,String)
[10:22:02:556] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WeChatMiniProgramController:
	{POST [/weixinAccount/getGroupId]}: getGroupId(JSONObject)
	{POST [/weixinAccount/msgSecCheck]}: msgSecCheck(JSONObject)
	{POST [/weixinAccount/imgSecCheckByMultipart], consumes [multipart/form-data]}: imgSecCheck(MultipartFile,String)
	{POST [/weixinAccount/imgSecCheck]}: imgSecCheck(HttpServletRequest)
	{POST [/weixinAccount/getProductList]}: getProductList(JSONObject)
	{GET [/weixinAccount/getAccessToken]}: getAccessTokenOfAccount(String)
	{POST [/weixinAccount/registWithCode]}: registWithCode(JSONObject)
	{GET [/weixinAccount/autoLogin]}: autoLogin(String,String,HttpServletRequest)
	{GET [/weixinAccount/loginWithoutUserInfoAndAutoRegist]}: loginWithoutUserInfoAndAutoRegist(String,String,HttpServletRequest)
	{GET [/weixinAccount/login]}: loginWithAutoRegister(String,String,HttpServletRequest)
	{POST [/weixinAccount/loginWithUserAuthNoRegistByPost]}: loginWithUserAuthWithoutAutoRegistByPost(JSONObject,HttpServletRequest)
	{POST [/weixinAccount/loginWithAuth]}: loginWithAuth(JSONObject,HttpServletRequest)
	{POST [/weixinAccount/getUserInfoByWeixinCode]}: getUserInfoByWeixinCode(JSONObject)
	{POST [/weixinAccount/loginWithUserAuthByPost]}: loginWithUserAuthWithAutoRegistByPost(JSONObject,HttpServletRequest)
	{GET [/weixinAccount/loginWithUserAuth]}: loginWithUserAuthWithAutoRegistByGet(String,String,String,String,String,HttpServletRequest)
	{POST [/weixinAccount/getQRCodeBase64ByLimit]}: getQRCodeBase64ByLimit(JSONObject)
	{POST [/weixinAccount/getQRCodeBase64]}: getQRCodeBase64(JSONObject)
	{POST [/weixinAccount/getPhoneNumByPost]}: getPhoneNumByPost(JSONObject)
	{POST [/weixinAccount/getPhoneNum]}: getPhoneNum(JSONObject)
	{POST [/weixinAccount/getPhoneNumNew]}: getPhoneNumNew(JSONObject)
	{POST [/weixinAccount/getPhoneNumWithoutGetSession]}: getPhoneNumWithoutGetSession(JSONObject)
	{POST [/weixinAccount/searchProduct]}: searchProduct(JSONObject)
	{GET [/weixinAccount/getCouponList]}: getCoupon(String)
	{GET [/weixinAccount/receivedCouple]}: receivedCouple(String,String,String)
	{GET [/weixinAccount/getUserCoupleList]}: getUserCoupleList(String,String,String)
	{GET [/weixinAccount/session]}: getSession(String,String)
	{POST [/weixinAccount/getQRCode]}: getQRCode(JSONObject)
	{POST [/weixinAccount/bind]}: bind(JSONObject)
[10:22:02:572] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WeChatOfficialAccountController:
	{GET [/wx/receiveMessage]}: doGet(HttpServletRequest)
	{POST [/wx/receiveMessage], produces [application/xml;charset=utf-8]}: processRequest(HttpServletRequest)
	{GET [/wx/getJSSDKConfiguration]}: getJSSDKConfiguration(String,String)
	{GET [/wx/getTicket]}: getTicket(HttpServletRequest)
	{GET [/wx/loginByWeChatOfficialAccount]}: loginByWeChatOfficialAccount(String,String)
	{GET [/wx/getPageAuthAccessToken]}: getPageAuthAccessToken(String,String)
	{GET [/wx/getTicketOfProduct]}: getTicketOfProduct(HttpServletRequest)
	{GET [/wx/createAccountMenu]}: createAccountMenu(String)
	{GET [/wx/checkIfFollowed]}: getQrcodeScanEntry(HttpServletRequest)
	{GET [/wx/loginWithAutoRegister]}: loginWithAutoRegister(String,String)
[10:22:02:573] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WordFileController:
	{GET [/wordFile/exportExamPaper]}: exportExamPaper(HttpServletRequest,HttpServletResponse)
	{GET [/wordFile/exportWrongQuestions]}: exportWrongQuestions(HttpServletRequest,HttpServletResponse)
	{GET [/wordFile/exportExercisePaper]}: exportExercisePaper(HttpServletRequest,HttpServletResponse)
[10:22:02:573] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WrongBookController:
	{POST [/wrongBook/update]}: updateIgnoreNull(JSONObject)
	{GET [/wrongBook/list]}: getWrongBookList(Integer)
	{POST [/wrongBook/create]}: createWrongBook(JSONObject)
	{GET [/wrongBook/deletePermanently]}: deletePermanently(Integer)
	{POST [/wrongBook/getWrongBookQuestionList]}: getWrongBookQuestionList(JSONObject)
	{POST [/wrongBook/removeQuestion]}: removeQuestion(JSONObject)
[10:22:02:574] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WrongQuestionBookController:
	{POST [/wrongQuestionBook/doReview]}: doReview(JSONObject)
	{ [/wrongQuestionBook/wrongQuestionBookPageInfo]}: getWrongQuestionBookPageInfoOfUser(int,int)
	{ [/wrongQuestionBook/getWrongQuestionBookOfUser]}: getWrongQuestionBookOfUser(Integer)
	{POST [/wrongQuestionBook/batchSaveWrongQuestionBook]}: batchSaveWrongQuestionBook(JSONObject)
	{GET [/wrongQuestionBook/delete]}: delete(Integer)
[10:22:02:574] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WrongQuestionBookQuestionController:
	{ [/wrongQuestionBookQuestion/markAsReviewed]}: markAsReviewed(int,int)
	{ [/wrongQuestionBookQuestion/getQuestionWrapperListOfWrongQuestionBook]}: getQuestionWrapperListOfWrongQuestionBook(int)
	{ [/wrongQuestionBookQuestion/getQuestionWrapperListOfBook]}: getQuestionWrapperListOfBook(Integer)
	{GET [/wrongQuestionBookQuestion/getTotalWrongQuestionWrapperList]}: getTotalWrongQuestionWrapperList(Integer,Integer)
	{GET [/wrongQuestionBookQuestion/deleteByQuery]}: deleteByQuery(Integer,Integer)
[10:22:02:574] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WrongQuestionBookReviewSummaryController:
	{POST [/wrongQuestionBookReviewSummary/saveSummary]}: saveSummary(JSONObject)
[10:22:02:574] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.WxMiniprogramSubscribeMessageOfEteaController:
	{GET [/wxMiniprogramSubscribeMessageOfEtea/batchSendSubscribedMessage]}: batchSendSubscribedMessage(Integer,Integer,Integer)
	{POST [/wxMiniprogramSubscribeMessageOfEtea/subscribe]}: subscribe(JSONObject)
[10:22:02:578] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.c.p.ExaminationControllerOfPC:
	{POST [/examination/getExaminationInViewListOfCreaterByTags]}: getExaminationInViewListOfCreaterByTags(JSONObject)
	{GET [/examination/examinationInViewListOfCreaterFromPC]}: getExaminationInViewListOfCreaterFromPC(Integer,Integer,Integer,Integer)
	{POST [/examination/getExaminationInViewListAndNum]}: getExaminationInViewListAndNum(JSONObject)
	{POST [/examination/getExaminationInViewListOfCreaterByTagsFromPC]}: getExaminationInViewListOfCreaterByTagsFromPC(JSONObject)
	{POST [/examination/getExaminationInViewListExcludingCreaterByTagsFromPC]}: getExaminationInViewListExcludingCreaterByTagsFromPC(JSONObject)
	{GET [/examination/getExaminationListAndSizeOfWillMark]}: getExaminationListAndSizeOfWillMark(Integer,Integer,Integer,Integer)
	{POST [/examination/getWillMarkExaminationListAndSize]}: getWillMarkExaminationListAndSize(JSONObject)
[10:22:02:582] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.c.p.ExaminationInstanceControllerOfPC:
	{GET [/examinationInstance/examinationInstanceStageSummaryListFromPC]}: getExaminationInstanceStageSummaryListFromPC(String,String,String,int,int,int)
	{GET [/examinationInstance/getExaminationInstanceStageSummaryListAndTotalNumFromPC]}: getExaminationInstanceStageSummaryListAndTotalNumFromPC(String,String,String,int,int,int)
	{POST [/examinationInstance/getExaminationInstanceStageSummaryListAndNum]}: getExaminationInstanceStageSummaryListAndNum(JSONObject)
	{POST [/examinationInstance/getExaminationInstanceStageSummaryListAndLengthByTags]}: getExaminationInstanceStageSummaryListByTagsFromPC(JSONObject)
	{POST [/examinationInstance/getListAndSizeOfExaminationInstanceWithUserInfoByConditions]}: getListAndSizeOfExaminationInstanceWithUserInfoByConditions(JSONObject)
	{GET [/examinationInstance/getExaminationInstanceListAndSizeWithUserInfoByExaminationId]}: getExaminationInstanceListWithUserInfoByExaminationId(Integer,Integer,Integer,String,Integer,Integer)
[10:22:02:582] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.c.p.PcPageDataController:
	{GET [/pageData/getCompanyCoreSummary]}: getCompanyCoreSummary(Integer,Integer)
[10:22:02:583] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.c.PageDataControllerOfCooperation:
	{GET [/pageDateControllerOfCooperation/getRankData]}: getRankData(Integer,Integer,Integer,Integer,String,String,Integer,Integer)
[10:22:02:583] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.e.ExaminationInstanceControllerOfEtea:
	{GET [/examinationInstanceControllerOfEtea/exportTotalScoreGroupByUser]}: exportTotalScoreGroupByUser(String,HttpServletResponse)
	{GET [/examinationInstanceControllerOfEtea/getUserTakenExaminations]}: getUserTakenExaminations(Integer,Integer,Integer)
	{GET [/examinationInstanceControllerOfEtea/getRecentOnGoingExaminations]}: getRecentOnGoingExaminations(Integer)
[10:22:02:585] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.e.PageDataControllerOfEtea:
	{POST [/pageDataOfEtea/getTotalScoreGroupByUser]}: getTotalScoreGroupByUser(JSONObject)
	{GET [/pageDataOfEtea/getHomePageSummaryInfo]}: getHomePageSummaryInfo(Integer)
[10:22:02:586] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.f.FormController:
	{POST [/formSystem/saveFormFlowRecord]}: saveFormFlowRecord(JSONObject)
	{POST [/formSystem/createForm]}: createForm(JSONObject)
	{GET [/formSystem/getFormWrapperById]}: getFormWrapperById(Integer)
	{POST [/formSystem/updateForm]}: updateForm(JSONObject)
[10:22:02:586] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteContentAccessController:
	{POST [/ksiteContentAccess/getContentAccessList]}: getContentAccessList(JSONObject)
	{POST [/ksiteContentAccess/add]}: add(KsiteContentAccess)
[10:22:02:586] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteFavoriteController:
	{POST [/ksiteFavorite/getFavoriteList]}: getFavoriteList(JSONObject)
	{POST [/ksiteFavorite/deleteByColumns]}: deleteByColumns(KsiteFavorite)
	{GET [/ksiteFavorite/getFavorite]}: getFavorite(Integer,String,Integer)
	{POST [/ksiteFavorite/add]}: add(KsiteFavorite)
[10:22:02:587] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteOrderController:
	{ [/ksiteOrder/orderNotify]}: orderNotify(JSONObject)
	{ [/ksiteOrder/createOrder]}: createKsiteOrder(JSONObject)
	{POST [/ksiteOrder/getOrderListWithKsiteInfo]}: getOrderListWithKsiteInfo(JSONObject)
	{POST [/ksiteOrder/getOrderList]}: getOrderListAndNum(JSONObject)
	{GET [/ksiteOrder/getAmountOfIncome]}: getAmountOfIncome(Integer)
	{GET [/ksiteOrder/getSettlementDashboardData]}: getSettlementDashboardData(Integer)
	{POST [/ksiteOrder/getOrderNum]}: getOrderNum(JSONObject)
	{POST [/ksiteOrder/update]}: update(JSONObject)
[10:22:02:587] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteOrderRelationController:
	{POST [/ksiteOrderRelation/checkContentAuth]}: checkContentAuth(KsiteOrderRelation)
[10:22:02:587] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsitePaperInstanceController:
	{GET [/ksitePaperInstance/getPaperInstanceStageSummaryDetail]}: getPaperInstanceStageSummaryDetail(Integer,String,String,Integer,Integer)
	{POST [/ksitePaperInstance/create]}: create(JSONObject)
[10:22:02:587] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsitePaperInstanceProcessController:
	{GET [/ksitePaperInstanceProcess/getPaperInstanceProcessWrapperListAndNum]}: getPaperInstanceProcessWrapperListAndNum(Integer,Boolean,Integer,Integer)
[10:22:02:588] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteUserSignatureController:
	{POST [/ksiteUserSignature/signKsite]}: sign(KsiteUserSignatureWrapper)
	{GET [/ksiteUserSignature/getUserSignature]}: getUserSignature(Integer,Integer)
	{GET [/ksiteUserSignature/checkKsiteNameExisted]}: checkKsiteNameExisted(String)
	{GET [/ksiteUserSignature/getKsiteInfo]}: getKsiteInfo(String,Integer)
	{POST [/ksiteUserSignature/getList]}: getList(JSONArray)
	{POST [/ksiteUserSignature/update]}: update(KsiteUserSignatureWrapper)
[10:22:02:588] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.k.KsiteWithdrawController:
	{POST [/ksiteWithdraw/withdraw]}: withdraw(KsiteWithdraw)
[10:22:02:588] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.m.c.CategoryController:
	{POST [/category/getBatchPathOfCategoryList]}: getBatchPathOfCategoryList(JSONArray)
	{POST [/category/addNewCategory]}: addNewCategory(JSONObject)
	{GET [/category/getPathOfCategory]}: getPathOfCategory(Integer)
	{POST [/category/updateCategory]}: updateCategory(JSONObject)
	{POST [/category/updateCategoryList]}: updateCategoryList(JSONArray)
	{GET [/category/copySpecifiedSpaceName]}: copySpecifiedSpaceName(String,Integer,String)
	{GET [/category/getNLevelCategory]}: getLevelCategoriesOfLessThanOrEqualDesignativeLevel(String,Integer,Integer)
	{POST [/category/add]}: add(JSONObject)
	{POST [/category/update]}: update(JSONObject)
[10:22:02:589] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QiyeweixinAccountController:
	{GET [/qiyeweixinAccount/transferLicense]}: transferLicense(String,List)
	{GET [/qiyeweixinAccount/getMemberActiveInfo]}: getMemberActiveInfo(String,String)
	{GET [/qiyeweixinAccount/queryAutoActiveStatus]}: queryAutoActiveStatus(String)
	{GET [/qiyeweixinAccount/getActivatedAccountList]}: getActivatedAccountList(String,Integer,String)
[10:22:02:590] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QiyeweixinCommunicationController:
	{ [/qiyeweixinCommunication/receiveData]}: receiveData(String,String,String,String,HttpServletRequest)
	{ [/qiyeweixinCommunication/receiveCommand]}: receiveCommand(String,String,String,String,HttpServletRequest)
	{GET [/qiyeweixinCommunication/getPreAuthCode]}: getPreAuthCode(String)
	{GET [/qiyeweixinCommunication/setSessionInfo]}: setSessionInfo(String)
	{GET [/qiyeweixinCommunication/generatePermanentCode]}: generatePermanentCode(String,String)
	{GET [/qiyeweixinCommunication/getAppQrcode]}: getAppQrcode(String)
	{GET [/qiyeweixinCommunication/getAppPermission]}: getAppPermission(String,String)
	{GET [/qiyeweixinCommunication/getAppAdmin]}: getAppAdmin(String,String)
	{GET [/qiyeweixinCommunication/getJsapiTicket]}: getJsapiTicket(String,String,String)
[10:22:02:590] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QiyeweixinSignatureController:
	{POST [/qiyeweixinSignature/getSignature]}: getSignature(JSONObject)
[10:22:02:591] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QiyeweixinUserController:
	{GET [/qiyeweixinUser/login]}: login(HttpServletRequest)
	{GET [/qiyeweixinUser/loginQkkQiyeweixin]}: wechatLogin()
	{GET [/qiyeweixinUser/loginQkkQiyeweixinFromPCWeb]}: wechatLoginFromPCWeb(String)
	{GET [/qiyeweixinUser/syncAllDepartmentNameAndUserNameByOCR]}: syncAllDepartmentNameAndUserNameByOCR(Integer)
	{GET [/qiyeweixinUser/refreshDepartmentNameAndUserNameByOCR]}: refreshDepartmentNameAndUserNameByOCR(Integer)
	{GET [/qiyeweixinUser/getUserInfo]}: getUserInfo(HttpServletRequest)
[10:22:02:591] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.CompanyControllerOfQKK:
	
[10:22:02:591] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.CompanyRegisterFormController:
	{GET [/companyRegisterForm/getCompanyRegisterForm]}: getCompanyRegisterForm(Integer)
	{GET [/companyRegisterForm/getCompanyRegisterFormFieldStructure]}: getCompanyRegisterFormFieldStructure(Integer)
	{POST [/companyRegisterForm/insertIfNotExisted]}: insertIfNotExisted(CompanyRegisterForm)
[10:22:02:592] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.CompanyRegisterFormFlowRecordController:
	{GET [/companyRegisterFormFlowRecord/getRegistrationDetail]}: getRegistrationDetail(Integer,Integer)
[10:22:02:592] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.ExaminationControllerOfQkk:
	{POST [/examination/getExaminationInViewListOfCompanyByTagesAndUserCompany]}: getExaminationInViewListOfCompanyByTagesAndUserCompany(JSONObject)
	{POST [/examination/getExaminationInViewListAndTotalSizeOfCompanyByTagesAndUserCompany]}: getExaminationInViewListAndTotalSizeOfCompanyByTagesAndUserCompany(JSONObject)
	{GET [/examination/getExamList]}: getExamList(Integer)
	{GET [/examination/checkIfAuthorized]}: checkIfAuthorized(Integer,Integer,Integer)
[10:22:02:593] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.ExaminationInstanceControllerOfQkk:
	{GET [/examinationInstanceOfQkk/exportTotalScoreGroupByUser]}: exportTotalScoreGroupByUser(String,HttpServletResponse)
	{GET [/examinationInstanceOfQkk/examinationInstanceStageSummaryDetail]}: getExaminationInstanceStageSummaryDetail(Integer,Integer,Integer,String,String,Integer,Integer)
	{GET [/examinationInstanceOfQkk/getExaminationInstanceListWithUserInfoByExaminationId]}: getExaminationInstanceListWithUserInfoByExaminationId(Integer,Integer,Integer,String,Integer,Integer)
	{POST [/examinationInstanceOfQkk/getTopNDepartmentDistribution]}: getTopNDepartmentDistribution(JSONObject)
	{POST [/examinationInstanceOfQkk/getTotalScoreOfExaminationListGroupByUser]}: getTotalScoreOfExaminationListGroupByUser(JSONObject)
	{POST [/examinationInstanceOfQkk/getSummaryGroupByDepartment]}: getSummaryGroupByDepartment(JSONObject)
	{GET [/examinationInstanceOfQkk/exportSummaryGroupByDepartment]}: exportSummaryGroupByDepartment(Integer,Integer,Integer,HttpServletResponse)
[10:22:02:593] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.PageDataController:
	{POST [/pageData/getTotalScoreGroupByUserInCompany]}: getTotalScoreGroupByUserInCompany(JSONObject)
[10:22:02:593] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.QuestionControllerOfQkk:
	{POST [/question/questionListExcludingCreaterInCompany]}: getQuestionListExcludingCreaterInCompany(JSONObject)
[10:22:02:594] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.q.UserControllerOfQkk:
	{POST [/user/registerAdminFromH5]}: registerAdminFromH5(JSONObject,HttpServletRequest)
	{POST [/user/secureExamineeRegister]}: secureExamineeRegister(HttpServletRequest,HttpServletResponse)
	{POST [/user/registerExamineeFromMp]}: registerExamineeFromMp(JSONObject,HttpServletRequest)
	{POST [/user/registerExamineeFromH5]}: registerExamineeFromH5(JSONObject,HttpServletRequest)
	{POST [/user/secureLogin]}: secureLogin(HttpServletRequest,HttpServletResponse)
	{GET [/user/mergeCompanyUser]}: mergeCompanyUser(Integer)
	{POST [/user/secureAdminRegister]}: secureAdminRegister(HttpServletRequest)
	{POST [/user/registerAdminFromMp]}: registerAdminFromMp(JSONObject,HttpServletRequest)
[10:22:02:594] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoMiniProgramController:
	{POST [/toutiaoMiniProgram/autoLogin]}: autologin(JSONObject)
	{POST [/toutiaoMiniProgram/regist]}: regist(JSONObject)
	{POST [/toutiaoMiniProgram/update]}: update(JSONObject)
[10:22:02:594] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoQuestionStarController:
	{GET [/touTiaoQuestionStar/getQuestionStarWrapper]}: getQuestionStarWrapper(Integer)
[10:22:02:594] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoQuestionStarStageEventController:
	{GET [/toutiaoQuestionStarStageEvent/getMyPlayedStageList]}: getMyPlayedStageList(Integer,Integer)
	{POST [/toutiaoQuestionStarStageEvent/insertIfNotExisted]}: insertIfNotExisted(ToutiaoQuestionStarStageEvent)
[10:22:02:595] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoQuestionStarUserController:
	{GET [/toutiaoQuestionStarUser/getUserInfo]}: getUserInfo(Integer)
[10:22:02:596] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.t.TouTiaoQuestionStarUserProfileController:
	{POST [/toutiaoQuestionStarUserProfile/markPass]}: markPass(JSONObject)
	{POST [/toutiaoQuestionStarUserProfile/increaseExp]}: increaseExp(JSONObject)
	{POST [/toutiaoQuestionStarUserProfile/getRankInfoList]}: getRankInfoList(JSONObject)
	{GET [/toutiaoQuestionStarUserProfile/getUserInfo]}: getUserInfo(Integer)
[10:22:02:598] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.e.c.x.UserSchoolController:
	{POST [/userSchool/bindSchool]}: bindCompany(JSONObject)
[10:22:02:602] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.o.c.OssFileController:
	{POST [/ossFile/update]}: update(OssFile)
[10:22:02:627] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
	{ [/error]}: error(HttpServletRequest)
[10:22:02:695] [DEBUG] - org.springframework.web.servlet.handler.AbstractDetectingUrlHandlerMapping.detectHandlers(AbstractDetectingUrlHandlerMapping.java:86) - 'beanNameHandlerMapping' {}
[10:22:03:194] [DEBUG] - org.springframework.web.servlet.handler.SimpleUrlHandlerMapping.logMappings(SimpleUrlHandlerMapping.java:177) - 'webSocketHandlerMapping' {/wss/pkGameSocket=org.springframework.web.socket.server.support.WebSocketHttpRequestHandler@729d3cab}
[10:22:03:479] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$MapperScannerRegistrarNotFoundConfiguration.afterPropertiesSet(MybatisAutoConfiguration.java:305) - No org.mybatis.spring.mapper.MapperFactoryBean found.
[10:22:04:970] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[10:22:06:196] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[10:22:06:261] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 63 ms to scan 1 urls, producing 3 keys and 6 values 
[10:22:06:274] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype com.alibaba.nacos.api.remote.request.Request -> com.alibaba.nacos.api.remote.request.ServerRequest
[10:22:06:284] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[10:22:06:314] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 30 ms to scan 1 urls, producing 4 keys and 9 values 
[10:22:06:316] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype com.alibaba.nacos.api.remote.request.Request -> com.alibaba.nacos.api.remote.request.ServerRequest
[10:22:06:319] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[10:22:06:348] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 28 ms to scan 1 urls, producing 3 keys and 10 values 
[10:22:06:351] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
file:/Users/<USER>/Documents/git/taurus-cloud/exam-main-service/target/classes/
file:/private/var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T/cp_bglw5z4cph95pkrips04x9cfi.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/dnsns.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jaccess.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jfxrt.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunec.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/zipfs.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/nashorn.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/cldrdata.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/localedata.jar
[10:22:06:407] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 54 ms to scan 12 urls, producing 0 keys and 0 values 
[10:22:06:412] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[10:22:06:424] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 12 ms to scan 1 urls, producing 1 keys and 5 values 
[10:22:06:430] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[10:22:06:444] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 14 ms to scan 1 urls, producing 1 keys and 7 values 
[10:22:06:466] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[10:22:06:512] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 46 ms to scan 1 urls, producing 2 keys and 8 values 
[10:22:06:513] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype java.lang.Comparable -> java.lang.Enum
[10:22:06:513] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype java.io.Serializable -> java.lang.Enum
[10:22:06:518] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
file:/Users/<USER>/Documents/git/taurus-cloud/exam-main-service/target/classes/
file:/private/var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T/cp_bglw5z4cph95pkrips04x9cfi.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/dnsns.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jaccess.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jfxrt.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunec.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/zipfs.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/nashorn.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/cldrdata.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/localedata.jar
[10:22:06:554] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 35 ms to scan 12 urls, producing 0 keys and 0 values 
[10:22:06:903] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xb63235b2, L:/*************:54323 ! R:************72/************72:6379] closed due to PING response timeout set in 1000 ms
[10:22:06:907] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x8fcafad1, L:/*************:54324 - R:************72/************72:6379] closed due to PING response timeout set in 1000 ms
[10:22:07:246] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xd0d6a96e, L:/*************:54326 ! R:************72/************72:6379] closed due to PING response timeout set in 1000 ms
[10:22:07:299] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@851568177 [redisClient=[addr=redis://************72:6379], channel=[id: 0x8fcafad1, L:/*************:54324 ! R:************72/************72:6379], currentCommand=null, usage=0] to ************72/************72:6379 
[10:22:07:351] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@2117481218 [redisClient=[addr=redis://************72:6379], channel=[id: 0xb63235b2, L:/*************:54323 ! R:************72/************72:6379], currentCommand=null, usage=0] to ************72/************72:6379 
[10:22:07:355] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:22:07:363] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:22:07:368] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1797889197 [redisClient=[addr=redis://************72:6379], channel=[id: 0xd0d6a96e, L:/*************:54326 ! R:************72/************72:6379], currentCommand=null, usage=0] to ************72/************72:6379 
[10:22:07:397] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:22:07:724] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@851568177 [redisClient=[addr=redis://************72:6379], channel=[id: 0x8fcafad1, L:/*************:54324 ! R:************72/************72:6379], currentCommand=null, usage=0] connected to ************72/************72:6379, command: null
[10:22:07:721] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1797889197 [redisClient=[addr=redis://************72:6379], channel=[id: 0xd0d6a96e, L:/*************:54326 ! R:************72/************72:6379], currentCommand=null, usage=0] connected to ************72/************72:6379, command: null
[10:22:07:720] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@2117481218 [redisClient=[addr=redis://************72:6379], channel=[id: 0xb63235b2, L:/*************:54323 ! R:************72/************72:6379], currentCommand=null, usage=0] connected to ************72/************72:6379, command: null
[10:22:08:127] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[10:22:08:129] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[10:22:08:131] [DEBUG] - com.taurus.examinationassistant.filter.AdminAuthFilter.init(AdminAuthFilter.java:45) - AdminAuthFilter初始化
[10:22:08:153] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[10:22:08:173] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[10:22:08:189] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[10:22:08:210] [DEBUG] - org.xnio.XnioWorker$Builder.build(XnioWorker.java:1191) - Creating worker:null, pool size:64, max pool size:64, keep alive:60000, io threads:8, stack size:0
[10:22:08:229] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[10:22:08:242] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 Accept', selector sun.nio.ch.KQueueSelectorImpl@34781b8c
[10:22:08:242] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-7', selector sun.nio.ch.KQueueSelectorImpl@cdf9190
[10:22:08:242] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-8', selector sun.nio.ch.KQueueSelectorImpl@1bc06bf7
[10:22:08:241] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-2', selector sun.nio.ch.KQueueSelectorImpl@2ee11b2c
[10:22:08:241] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-4', selector sun.nio.ch.KQueueSelectorImpl@43e02368
[10:22:08:241] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-3', selector sun.nio.ch.KQueueSelectorImpl@6000aff9
[10:22:08:242] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-5', selector sun.nio.ch.KQueueSelectorImpl@7489546d
[10:22:08:241] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-1', selector sun.nio.ch.KQueueSelectorImpl@68cfb24f
[10:22:08:242] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-6', selector sun.nio.ch.KQueueSelectorImpl@7f06d2f8
[10:22:08:242] [DEBUG] - io.undertow.Undertow.start(Undertow.java:160) - Configuring listener with protocol HTTP for interface 0.0.0.0 and port 8081
[10:22:08:400] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[10:22:08:914] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 49.048 seconds (JVM running for 51.002)
[10:22:08:964] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[10:22:09:159] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`account` , t.`app_id` , t.`app_secret` , t.`token` , t.`encoding_aes_key` FROM `weixin_account` t 
[10:22:09:290] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 
[10:22:09:457] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 13
[10:22:09:461] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:25:12:390] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /wx/getTicket, authentication required: false
[10:25:12:390] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /version/getVersion, authentication required: false
[10:25:12:404] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1b89c3f7 for /wx/getTicket
[10:25:12:404] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1b89c3f7 for /version/getVersion
[10:25:12:404] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /version/getVersion
[10:25:12:404] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /wx/getTicket
[10:25:12:414] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[10:25:14:012] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: INSERT INTO `qrcode_scan_entry` ( `ticket` , `scene_id` , `create_time` , `scan_time` , `openid` , `creater_id` , `phone` ) VALUES ( ? , ? , ? , ? , ? , ? , ? ) 
[10:25:14:031] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: gQE78DwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyVl9KdjBkaWtjeGwxMmhGUmhFMTIAAgSJIjVoAwQIBwAA(String), 35208336(Integer), 2025-05-27 10:25:13.903(Timestamp), null, null, null, null
[10:25:14:117] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:25:14:122] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:25:15:015] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /user/getUserInfoById, authentication required: false
[10:25:15:020] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1b89c3f7 for /user/getUserInfoById
[10:25:15:021] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /user/getUserInfoById
[10:25:15:104] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:25:15:105] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 37377(Integer)
[10:25:15:148] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:25:15:148] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:25:15:194] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /access/getTokenUnlimit, authentication required: false
[10:25:15:196] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1b89c3f7 for /access/getTokenUnlimit
[10:25:15:197] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /access/getTokenUnlimit
[10:25:15:311] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /universalProductOrderRelation/getProductOrderRelation, authentication required: false
[10:25:15:312] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1b89c3f7 for /universalProductOrderRelation/getProductOrderRelation
[10:25:15:312] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /universalProductOrderRelation/getProductOrderRelation
[10:25:15:610] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /productOrderRelationCount/getProductOrderRelationCountList, authentication required: false
[10:25:15:610] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1b89c3f7 for /productOrderRelationCount/getProductOrderRelationCountList
[10:25:15:610] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /productOrderRelationCount/getProductOrderRelationCountList
[10:25:15:636] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /productOrderRelationCount/getProductOrderRelationCountList, authentication required: false
[10:25:15:649] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /pageData/getCompanyCoreSummary, authentication required: false
[10:25:15:651] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /product/getProductList, authentication required: false
[10:25:15:651] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1b89c3f7 for /productOrderRelationCount/getProductOrderRelationCountList
[10:25:15:649] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /product/getListOfProduct, authentication required: false
[10:25:15:653] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /productOrderRelationCount/getProductOrderRelationCountList
[10:25:15:653] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1b89c3f7 for /product/getProductList
[10:25:15:653] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1b89c3f7 for /pageData/getCompanyCoreSummary
[10:25:15:653] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1b89c3f7 for /product/getListOfProduct
[10:25:15:654] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /product/getListOfProduct
[10:25:15:653] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /product/getProductList
[10:25:15:653] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /pageData/getCompanyCoreSummary
[10:25:15:768] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`type` , t.`code` , t.`name` , t.`short_description` , t.`long_description` , t.`max_examinee_num` , t.`valid_days` , t.`price` FROM `product` t WHERE type = ? 
[10:25:15:791] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: P(String)
[10:25:15:859] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`relation_type` , t.`relation_id` , t.`modal_name` , t.`available_times` , t.`end_time` FROM `product_order_relation_count` t WHERE relation_type = ? AND relation_id = ? AND modal_name IN ( ? , ? , ? , ? , ? , ? , ? , ? ) ORDER BY id ASC 
[10:25:15:864] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select type,child_type as childType, count(*) as num from question where enabled=1 and company_id=? and creater_id=? group by type,child_type 
[10:25:15:871] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`relation_type` , t.`relation_id` , t.`modal_name` , t.`available_times` , t.`end_time` FROM `product_order_relation_count` t WHERE relation_type = ? AND relation_id = ? AND modal_name IN ( ? , ? , ? , ? , ? , ? ) ORDER BY id ASC 
[10:25:15:876] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer)
[10:25:15:877] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: P(String), 37377(Integer), VPWAD(String), PDDS(String), VADVIPC1(String), VADVIPC2(String), VADVIPC3(String), VADVIPC4(String), VADVIPC5(String), VADVIPC6(String)
[10:25:15:878] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: P(String), 37377(Integer), VADVIPC1(String), VADVIPC2(String), VADVIPC3(String), VADVIPC4(String), VADVIPC5(String), VADVIPC6(String)
[10:25:15:883] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 8
[10:25:15:885] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:25:15:929] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 3
[10:25:15:930] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:25:15:932] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:25:15:933] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:25:15:967] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 7
[10:25:15:967] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:25:16:027] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select count(distinct(category)) as num from question where enabled=1 and company_id=? and creater_id=? 
[10:25:16:029] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer)
[10:25:16:079] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:25:16:081] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:25:16:085] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select count(*) as totalPracticeTimes, count(distinct t.user_id) as totalUserNum, sum(t.total_right_times) as totalRightTimes,sum(t.total_wrong_times) as totalWrongTimes, sum(t.duration) as totalDuration from exercise_book_practice_summary t left join exercise_book t1 ON t1.id=t.exercise_book_id where t1.enabled=1 and t1.company_id=? and t1.creater_id=? 
[10:25:16:086] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer)
[10:25:16:119] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:25:16:119] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:25:16:154] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select type, count(*) as num from exercise_book where enabled=1 and company_id=? and creater_id in( ? ) group by type 
[10:25:16:155] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer)
[10:25:16:199] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 2
[10:25:16:199] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:25:16:208] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select DATE_FORMAT(t.end_date, '%Y-%m-%d') day, count(distinct(t.user_id)) as userNum,count(*) as times from exercise_book_practice_summary t left join exercise_book t1 ON t1.id= t.exercise_book_id where t1.enabled=1 and t1.company_id=? and t1.creater_id in( ? ) group by day order by day desc limit ? 
[10:25:16:208] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer), 20(Integer)
[10:25:16:250] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 6
[10:25:16:250] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:25:16:273] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT count(*) FROM `examination` t WHERE enabled = ? AND company_id = ? AND creater_id IN ( ? ) 
[10:25:16:274] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 0(Integer), 37377(Integer)
[10:25:16:327] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:25:16:328] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:25:16:330] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select count(distinct t.user_id) as totalUserNum,count(*) as totalTimes, sum(if(t.if_pass = true, 1, 0)) as totalPassed,sum(if(t.if_pass = false, 1, 0)) as totalFailed from examination_instance t left join examination t1 ON t1.id=t.examination_id where t.deleted_by_admin = 0 and t.enabled=1 and t1.company_id=? and t1.creater_id in( ? ) 
[10:25:16:330] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer)
[10:25:16:392] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:25:16:393] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:25:16:417] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: select DATE_FORMAT(t.create_time, '%Y-%m-%d') day, count(distinct(t.user_id)) as userNum,count(*) as times from examination_instance t left join examination t1 ON t1.id=t.examination_id where t.enabled=1 and t.deleted_by_admin=0 and t1.company_id=? and t1.creater_id in( ? ) group by day order by day desc limit ? 
[10:25:16:418] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 0(Integer), 37377(Integer), 20(Integer)
[10:25:16:484] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 2
[10:25:16:485] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:25:18:460] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /company/getQiyeweixinCompanyList, authentication required: false
[10:25:18:461] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1b89c3f7 for /company/getQiyeweixinCompanyList
[10:25:18:461] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /company/getQiyeweixinCompanyList
[10:25:18:509] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`type` , t.`name` , t.`contact` , t.`telephone` , t.`desc` , t.`enable_register_code` , t.`register_code` , t.`address` , t.`create_time` , t.`enabled` , t.`user_id` , t.`code` , t.`first_channel` , t.`sys_config` , t.`storage_capacity` , t.`residual_flow` , t.`qkk_qiye_corpid` , t.`qkk_qiye_permanent_code` , t.`qkk_qiye_agent_id` , t.`qkk_qiye_auth_mode` FROM `company` t WHERE enabled = ? AND (qkk_qiye_corpid IS NOT NULL) ORDER BY id DESC LIMIT ?,? 
[10:25:18:509] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 0(Integer), 20(Integer)
[10:25:18:588] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 20
[10:25:18:589] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:25:18:622] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[10:25:18:623] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6267(Integer), 1(Integer), 3(Integer)
[10:25:18:684] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:25:18:685] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:25:18:688] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[10:25:18:688] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6264(Integer), 1(Integer), 3(Integer)
[10:25:18:766] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:25:18:770] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[10:25:18:770] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6257(Integer), 1(Integer), 3(Integer)
[10:25:18:833] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 3
[10:25:18:874] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[10:25:18:874] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6256(Integer), 1(Integer), 3(Integer)
[10:25:18:947] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:25:18:955] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[10:25:18:955] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6251(Integer), 1(Integer), 3(Integer)
[10:25:19:025] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:25:19:030] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[10:25:19:032] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6184(Integer), 1(Integer), 3(Integer)
[10:25:19:103] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:25:19:112] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[10:25:19:112] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6165(Integer), 1(Integer), 3(Integer)
[10:25:19:182] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:25:19:192] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[10:25:19:193] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6164(Integer), 1(Integer), 3(Integer)
[10:25:19:264] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:25:19:277] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[10:25:19:278] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6162(Integer), 1(Integer), 3(Integer)
[10:25:19:337] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:25:19:340] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[10:25:19:340] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6141(Integer), 1(Integer), 3(Integer)
[10:25:19:413] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:25:19:419] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[10:25:19:423] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6140(Integer), 1(Integer), 3(Integer)
[10:25:19:498] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:25:19:503] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[10:25:19:504] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6131(Integer), 1(Integer), 3(Integer)
[10:25:19:588] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:25:19:596] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[10:25:19:597] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6127(Integer), 1(Integer), 3(Integer)
[10:25:19:708] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:25:19:718] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[10:25:19:721] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6118(Integer), 1(Integer), 3(Integer)
[10:25:19:803] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:25:19:810] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[10:25:19:811] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6112(Integer), 1(Integer), 3(Integer)
[10:25:19:880] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:25:19:887] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[10:25:19:888] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6107(Integer), 1(Integer), 3(Integer)
[10:25:19:960] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:25:19:964] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[10:25:19:965] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6064(Integer), 1(Integer), 3(Integer)
[10:25:20:032] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:25:20:044] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[10:25:20:044] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6060(Integer), 1(Integer), 3(Integer)
[10:25:20:118] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 0
[10:25:20:127] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[10:25:20:127] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6040(Integer), 1(Integer), 3(Integer)
[10:25:20:194] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 6
[10:25:20:201] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.sys_role_id as sysRoleId , t.user_id as userId , t1.name , t1.nickname , t1.phone , t1.qkk_qiye_openid as qkkQiyeOpenid , t1.qkk_qiye_user_id as qkkQiyeUserId FROM `sys_user_role` t left join user t1 ON t1.id=t.user_id WHERE t.enabled = ? AND t.company_id = ? AND t.sys_role_id IN ( ? , ? ) 
[10:25:20:201] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean), 6025(Integer), 1(Integer), 3(Integer)
[10:25:20:277] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 2
[10:25:20:281] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT count(*) FROM `company` t WHERE enabled = ? AND (qkk_qiye_corpid IS NOT NULL) 
[10:25:20:281] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: true(Boolean)
[10:25:20:323] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:25:20:324] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[10:25:20:324] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:25:32:594] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /qiyeweixinAccount/queryAutoActiveStatus, authentication required: false
[10:25:32:602] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1b89c3f7 for /qiyeweixinAccount/queryAutoActiveStatus
[10:25:32:602] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /qiyeweixinAccount/queryAutoActiveStatus
[10:25:32:621] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinAccountService.getAutoActiveStatus(QiyeweixinAccountService.java:603) - 查询企业的许可自动激活状态, corpId:wpfqwcEQAAKhl2yYldZWlyMp4OKKMbNw
[10:25:33:089] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinAccountService.getAutoActiveStatus(QiyeweixinAccountService.java:620) - 查询企业的许可自动激活状态响应: {errcode=0, auto_active_status=1, errmsg=ok}
[10:25:36:040] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /qiyeweixinAccount/getActivatedAccountList, authentication required: false
[10:25:36:045] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1b89c3f7 for /qiyeweixinAccount/getActivatedAccountList
[10:25:36:045] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /qiyeweixinAccount/getActivatedAccountList
[10:25:36:060] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinAccountService.getAccountList(QiyeweixinAccountService.java:352) - 获取企业的账号列表, corpId:wpfqwcEQAAKhl2yYldZWlyMp4OKKMbNw, limit:50, cursor:
[10:25:36:463] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinAccountService.getAccountList(QiyeweixinAccountService.java:375) - 获取企业的账号列表响应: {errcode=0, next_cursor=69mjmzr9QZ-Cl0ny8XY9PQ, errmsg=ok, account_list=[], has_more=0}
[10:25:43:655] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /qiyeweixinUser/refreshDepartmentNameAndUserNameByOCR, authentication required: false
[10:25:43:655] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1b89c3f7 for /qiyeweixinUser/refreshDepartmentNameAndUserNameByOCR
[10:25:43:655] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /qiyeweixinUser/refreshDepartmentNameAndUserNameByOCR
[10:25:43:816] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`type` , t.`name` , t.`contact` , t.`telephone` , t.`desc` , t.`enable_register_code` , t.`register_code` , t.`address` , t.`create_time` , t.`enabled` , t.`user_id` , t.`code` , t.`first_channel` , t.`sys_config` , t.`storage_capacity` , t.`residual_flow` , t.`qkk_qiye_corpid` , t.`qkk_qiye_permanent_code` , t.`qkk_qiye_agent_id` , t.`qkk_qiye_auth_mode` FROM `company` t WHERE t.`id` = ? LIMIT 1 
[10:25:43:821] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 6040(Integer)
[10:25:43:868] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:25:43:869] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[10:25:43:869] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:25:43:870] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`type` , t.`name` , t.`contact` , t.`telephone` , t.`desc` , t.`enable_register_code` , t.`register_code` , t.`address` , t.`create_time` , t.`enabled` , t.`user_id` , t.`code` , t.`first_channel` , t.`sys_config` , t.`storage_capacity` , t.`residual_flow` , t.`qkk_qiye_corpid` , t.`qkk_qiye_permanent_code` , t.`qkk_qiye_agent_id` , t.`qkk_qiye_auth_mode` FROM `company` t WHERE t.`id` = ? LIMIT 1 
[10:25:43:871] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 6040(Integer)
[10:25:43:925] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:25:43:926] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:215) - activeProfile:test-etea
[10:25:43:926] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:222) - targetUrl:https://www.qikaokao.com/qiyeweixin/#/qiyeweixinCompanyInfo?companyId=6040&qkkQiyeCorpid=wpfqwcEQAAKhl2yYldZWlyMp4OKKMbNw&qkkQiyeAgentId=1000088
[10:25:43:927] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:226) - serviceUrl:http://127.0.0.1:8077/api/ocr/recognizeUrl?url=%s&ocr=ali
[10:25:43:927] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:229) - url:http://127.0.0.1:8077/api/ocr/recognizeUrl?url=https%3A%2F%2Fwww.qikaokao.com%2Fqiyeweixin%2F%23%2FqiyeweixinCompanyInfo%3FcompanyId%3D6040%26qkkQiyeCorpid%3DwpfqwcEQAAKhl2yYldZWlyMp4OKKMbNw%26qkkQiyeAgentId%3D1000088&ocr=ali
[10:26:08:300] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:241) - result:{data=[部门列表, $$ 9356 $$ 华东一区 $$ 分隔符 $$ 9357 $$ 车间管理$$ 分隔符, $$9358$$质量与服务部$$分隔符$$9359$$ 华东二区 $$ 分隔符, $$ 9360 $$ 大功率流水线组$$分隔符 $$ 9361 $$ 大功率单工位组$$分隔符, $$ 9362 $$ 负载测试部$$分隔符 $$ 9363 $$ 负载测试组$$分隔符, $$ 9364 $$ 车间管理$$分隔符 $$ 9365 $$ 生产一部$$分隔符$$9366$$ 生产二部 $$分隔符, $$ 9367 $$ 车间管理$$分隔符 $$9368$$小功率变频组 $$分隔符, $$ 9369 $$ 小功率电气组$$分隔符 $$ 9370 $$ 前沿技术部$$分隔符, 人员列表, $$ 1727169 $$ 李帅军$$ 分隔符 $$ 1727170 $$ 王鑫潮 $$分隔符, $$ 1727171 $$ 雷芸菲$$ 分隔符 $$ 1727172 $$ 徐梦影 $$ 分隔符 $$ 1727173 $$ 郑雅$$分隔符, $$ 1727174$$ 史安也 $$ 分隔符 $$ 1727175 $$ 韩永庆 $$ 分隔符, $$ 1727176 $$ 杨赵文 $$分隔符 $$ 1727177 $$ 刘奥 $$ 分隔符 $$ 1727178 $$ 归李辉$$分隔符, $$ 1727179 $$ 詹继杨 $$ 分隔符 $$ 1727180 $$ 娄恩会 $$ 分隔符, $$ 1727181 $$ 乐玉龙 $$ 分隔符 $$ 1727182 $$ 王美伊 $$ 分隔符 $$ 1727183 $$ 丁惠$$分隔符, $$ 1727184 $$ 魏大智$$ 分隔符 $$ 1727185 $$ 熊啟铭 $$ 分隔符 $$ 1727186 $$ 罗星$$分隔符, $$ 1727187 $$ 刘勇杰 $$ 分隔符 $$ 1727188 $$ 靳通 $$ 分隔符 $$ 1727189 $$ 蔡子贤$$分隔符, $$ 1727190 $$ 刘胜男 $$ 分隔符 $$ 1727191 $$ 彭江 $$ 分隔符$$ 1727192 $$ 闫金山 $$ 分隔符, $$ 1727193 $$ 秦焱翔 $$ 分隔符 $$ 1727194 $$ 李小霞 $$ 分隔符 $$ 1727195 $$ 杨平$$分隔符, $$ 1727196 $$ 张瑞利 $$ 分隔符 $$ 1727197 $$ 刘泽文 $$ 分隔符, $$ 1727198 $$ 黄湘成 $$ 分隔符 $$ 1727199 $$ 吕荣祥 $$分隔符, $$ 1727200 $$ 顾佳明$$分隔符 $$ 1727201 $$ 张博宇 $$ 分隔符 $$ 1727202 $$ 符野$$ 分隔符, $$ 1727203 $$ 黄梦辉 $$ 分隔符 $$ 1727204 $$ 戴忠强 $$ 分隔符, $$ 1727205 $$ 彭家贵 $$ 分隔符 $$ 1727206 $$ 曹冻 $$ 分隔符$$ 1727207 $$ 乔佳琦 $$分隔符, $$ 1727208 $$ 张昆 $$ 分隔符 $$ 1727209 $$ 彭少杰 $$ 分隔符 $$ 1727210 $$ 苗磊 $$分隔符, $$ 1727211 $$ 朱永平$$ 分隔符 $$ 1727212 $$ 吴梓鑫 $$分隔符, $$ 1727213$$ 鲁东海 $$ 分隔符 _$$ 1727214 $$ 何海伟 $$ 分隔符$$ 1727215 $$ 赵艺 $$ 分隔符, $$ 1727187 $$ 刘勇杰 $$ 分隔符 $$ 1727188 $$ 靳通 $$ 分隔符$$ 1727189 $$ 蔡子贤 $$分隔符, $$ 1727190 $$ 刘胜男$$ 分隔符 $$ 1727191 $$ 彭江 $$ 分隔符 $$ 1727192 $$ 闫金山$$分隔符, $$ 1727193 $$ 秦焱翔 $$ 分隔符 $$ 1727194 $$ 李小霞 $$ 分隔符 $$ 1727195 $$ 杨平$$分隔符, $$ 1727196 $$ 张瑞利 $$ 分隔符 $$ 1727197 $$ 刘泽文 $$ 分隔符, $$ 1727198 $$ 黄湘成 $$ 分隔符 $$ 1727199 $$ 吕荣祥 $$分隔符, $$ 1727200 $$ 顾佳明$$分隔符 $$ 1727201 $$ 张博宇 $$ 分隔符 $$ 1727202 $$ 符野$$ 分隔符, $$ 1727203 $$ 黄梦辉 $$ 分隔符 $$ 1727204 $$ 戴忠强 $$ 分隔符, $$ 1727205 $$ 彭家贵 $$ 分隔符 $$ 1727206 $$ 曹冻 $$ 分隔符$$ 1727207 $$ 乔佳琦 $$分隔符, $$ 1727208 $$ 张昆 $$ 分隔符 $$ 1727209 $$ 彭少杰 $$ 分隔符$$1727210 $$ 苗磊 $$ 分隔符, $$ 1727211$$ 朱永平$$分隔符$$ 1727212 $$ 吴梓鑫 $$分隔符, $$ 1727213 $$ 鲁东海 $$ 分隔符 $$ 1727214 $$ 何海伟 $$ 分隔符 $$ 1727215 $$ 赵艺$$分隔符, $$ 1727216 $$ 蒋贵盛 $$ 分隔符 $$ 1727217 $$ 陈灶晖 $$ 分隔符, $$ 1727218$$ 於宗海$$分隔符], errorMessage=success, errorCode=0}
[10:26:08:317] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9356, departName=华东一区, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:08:448] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `company_department` SET `depart_name`=? WHERE `id` = ? 
[10:26:08:454] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 华东一区(String), 9356(Integer)
[10:26:08:538] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:08:539] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[10:26:08:540] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:26:08:541] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9357, departName=车间管理, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:08:547] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `company_department` SET `depart_name`=? WHERE `id` = ? 
[10:26:08:547] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 车间管理(String), 9357(Integer)
[10:26:08:646] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:08:648] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9358, departName=质量与服务部, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:08:652] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `company_department` SET `depart_name`=? WHERE `id` = ? 
[10:26:08:653] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 质量与服务部(String), 9358(Integer)
[10:26:08:822] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:08:822] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9359, departName=华东二区, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:08:828] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `company_department` SET `depart_name`=? WHERE `id` = ? 
[10:26:08:829] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 华东二区(String), 9359(Integer)
[10:26:08:933] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:08:935] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9360, departName=大功率流水线组, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:08:939] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `company_department` SET `depart_name`=? WHERE `id` = ? 
[10:26:08:940] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 大功率流水线组(String), 9360(Integer)
[10:26:09:040] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:09:041] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9361, departName=大功率单工位组, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:09:044] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `company_department` SET `depart_name`=? WHERE `id` = ? 
[10:26:09:044] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 大功率单工位组(String), 9361(Integer)
[10:26:09:133] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:09:133] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9362, departName=负载测试部, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:09:135] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `company_department` SET `depart_name`=? WHERE `id` = ? 
[10:26:09:136] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 负载测试部(String), 9362(Integer)
[10:26:09:288] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:09:294] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9363, departName=负载测试组, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:09:298] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `company_department` SET `depart_name`=? WHERE `id` = ? 
[10:26:09:308] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 负载测试组(String), 9363(Integer)
[10:26:09:429] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:09:431] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9364, departName=车间管理, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:09:432] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `company_department` SET `depart_name`=? WHERE `id` = ? 
[10:26:09:432] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 车间管理(String), 9364(Integer)
[10:26:09:622] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:09:626] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9365, departName=生产一部, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:09:635] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `company_department` SET `depart_name`=? WHERE `id` = ? 
[10:26:09:635] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 生产一部(String), 9365(Integer)
[10:26:09:736] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:09:737] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9366, departName=生产二部, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:09:739] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `company_department` SET `depart_name`=? WHERE `id` = ? 
[10:26:09:740] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 生产二部(String), 9366(Integer)
[10:26:09:930] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:09:932] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9367, departName=车间管理, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:09:934] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `company_department` SET `depart_name`=? WHERE `id` = ? 
[10:26:09:935] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 车间管理(String), 9367(Integer)
[10:26:10:098] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:10:101] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9368, departName=小功率变频组, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:10:109] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `company_department` SET `depart_name`=? WHERE `id` = ? 
[10:26:10:109] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 小功率变频组(String), 9368(Integer)
[10:26:10:261] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:10:262] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9369, departName=小功率电气组, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:10:263] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `company_department` SET `depart_name`=? WHERE `id` = ? 
[10:26:10:264] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 小功率电气组(String), 9369(Integer)
[10:26:10:452] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:10:456] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:258) - 保存部门信息: CompanyDepartment [id=9370, departName=前沿技术部, departNo=null, grade=null, companyId=null, higherDepartId=null, qiyewxDepartId=null, qiyewxParentId=null, order=null, enabled=null]
[10:26:10:462] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `company_department` SET `depart_name`=? WHERE `id` = ? 
[10:26:10:463] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 前沿技术部(String), 9370(Integer)
[10:26:10:584] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:10:586] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727169, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=李帅军, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:10:607] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:10:608] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 李帅军(String), 1727169(Integer)
[10:26:10:737] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:10:738] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[10:26:10:739] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:26:10:772] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727170, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=王鑫潮, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:10:777] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:10:779] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 王鑫潮(String), 1727170(Integer)
[10:26:10:945] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:10:947] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727169(Integer)
[10:26:10:961] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:10:963] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727171, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=雷芸菲, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:10:964] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:10:966] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727170(Integer)
[10:26:11:010] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:11:014] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 雷芸菲(String), 1727171(Integer)
[10:26:11:018] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:11:023] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:11:025] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:26:11:025] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.closeRemovedStatement(PreparedStatementPool.java:160) - stmt exit cache
[10:26:11:025] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:26:11:123] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:11:124] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:26:11:125] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727172, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=徐梦影, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:11:126] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:11:127] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727171(Integer)
[10:26:11:127] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:11:127] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 徐梦影(String), 1727172(Integer)
[10:26:11:201] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:11:202] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:26:11:285] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:11:286] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727173, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=郑雅, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:11:286] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:11:287] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727172(Integer)
[10:26:11:288] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:11:288] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 郑雅(String), 1727173(Integer)
[10:26:11:461] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:11:608] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:11:610] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727174, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=史安也, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:11:610] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:11:611] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727173(Integer)
[10:26:11:613] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:11:614] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 史安也(String), 1727174(Integer)
[10:26:11:699] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:11:768] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:11:769] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727175, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=韩永庆, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:11:772] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:11:772] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:11:772] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727174(Integer)
[10:26:11:772] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 韩永庆(String), 1727175(Integer)
[10:26:11:864] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:11:917] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:11:920] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727176, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=杨赵文, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:11:921] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:11:921] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727175(Integer)
[10:26:11:922] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:11:922] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 杨赵文(String), 1727176(Integer)
[10:26:11:961] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:12:017] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:12:019] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727177, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=刘奥, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:12:020] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:12:020] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727176(Integer)
[10:26:12:022] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:12:023] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 刘奥(String), 1727177(Integer)
[10:26:12:089] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:12:108] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:12:109] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727178, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=归李辉, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:12:110] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:12:110] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:12:111] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727177(Integer)
[10:26:12:111] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 归李辉(String), 1727178(Integer)
[10:26:12:174] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:12:217] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:12:218] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727179, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=詹继杨, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:12:218] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:12:219] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:12:219] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727178(Integer)
[10:26:12:220] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 詹继杨(String), 1727179(Integer)
[10:26:12:283] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:12:322] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:12:324] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727180, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=娄恩会, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:12:325] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:12:327] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727179(Integer)
[10:26:12:327] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:12:329] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 娄恩会(String), 1727180(Integer)
[10:26:12:384] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:12:421] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:12:423] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727181, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=乐玉龙, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:12:424] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:12:426] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:12:426] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727180(Integer)
[10:26:12:426] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 乐玉龙(String), 1727181(Integer)
[10:26:12:502] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:12:511] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:12:512] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727182, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=王美伊, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:12:515] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:12:516] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727181(Integer)
[10:26:12:519] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:12:519] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 王美伊(String), 1727182(Integer)
[10:26:12:567] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:12:616] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:12:618] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727183, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=丁惠, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:12:621] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:12:625] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727182(Integer)
[10:26:12:632] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:12:632] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 丁惠(String), 1727183(Integer)
[10:26:12:686] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:12:705] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:12:706] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727184, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=魏大智, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:12:706] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:12:706] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727183(Integer)
[10:26:12:707] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:12:707] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 魏大智(String), 1727184(Integer)
[10:26:12:756] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:12:822] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:12:822] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727185, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=熊啟铭, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:12:823] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:12:823] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727184(Integer)
[10:26:12:824] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:12:824] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 熊啟铭(String), 1727185(Integer)
[10:26:12:883] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:12:985] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:12:987] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727186, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=罗星, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:12:989] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:12:989] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727185(Integer)
[10:26:12:989] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:12:990] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 罗星(String), 1727186(Integer)
[10:26:13:028] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:13:076] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:13:077] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727187, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=刘勇杰, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:13:079] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:13:079] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727186(Integer)
[10:26:13:079] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:13:079] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 刘勇杰(String), 1727187(Integer)
[10:26:13:137] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:13:162] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:13:163] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727188, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=靳通, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:13:164] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:13:164] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727187(Integer)
[10:26:13:164] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:13:165] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 靳通(String), 1727188(Integer)
[10:26:13:205] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:13:237] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:13:238] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727189, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=蔡子贤, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:13:241] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:13:241] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:13:241] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727188(Integer)
[10:26:13:241] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 蔡子贤(String), 1727189(Integer)
[10:26:13:297] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:13:313] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:13:314] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727190, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=刘胜男, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:13:314] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:13:315] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727189(Integer)
[10:26:13:315] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:13:315] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 刘胜男(String), 1727190(Integer)
[10:26:13:386] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:13:422] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:13:422] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727191, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=彭江, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:13:423] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:13:423] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727190(Integer)
[10:26:13:423] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:13:423] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 彭江(String), 1727191(Integer)
[10:26:13:481] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:13:511] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:13:512] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727192, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=闫金山, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:13:512] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:13:513] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727191(Integer)
[10:26:13:513] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:13:513] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 闫金山(String), 1727192(Integer)
[10:26:13:553] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:13:596] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:13:597] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727193, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=秦焱翔, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:13:598] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:13:598] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727192(Integer)
[10:26:13:599] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:13:599] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 秦焱翔(String), 1727193(Integer)
[10:26:13:649] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:13:662] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:13:665] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727194, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=李小霞, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:13:667] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:13:667] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727193(Integer)
[10:26:13:668] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:13:668] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 李小霞(String), 1727194(Integer)
[10:26:13:711] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:13:739] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:13:740] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727195, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=杨平, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:13:741] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:13:741] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727194(Integer)
[10:26:13:741] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:13:742] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 杨平(String), 1727195(Integer)
[10:26:13:809] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:13:823] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:13:824] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727196, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=张瑞利, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:13:824] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:13:825] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727195(Integer)
[10:26:13:825] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:13:826] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 张瑞利(String), 1727196(Integer)
[10:26:13:869] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:13:932] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:13:934] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727197, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=刘泽文, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:13:936] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:13:936] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727196(Integer)
[10:26:13:936] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:13:937] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 刘泽文(String), 1727197(Integer)
[10:26:14:007] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:14:033] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:14:037] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727198, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=黄湘成, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:14:037] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:14:038] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727197(Integer)
[10:26:14:039] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:14:039] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 黄湘成(String), 1727198(Integer)
[10:26:14:094] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:14:124] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:14:126] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727199, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=吕荣祥, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:14:127] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:14:127] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:14:127] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727198(Integer)
[10:26:14:128] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 吕荣祥(String), 1727199(Integer)
[10:26:14:177] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:14:208] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:14:208] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727200, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=顾佳明, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:14:209] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:14:210] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727199(Integer)
[10:26:14:210] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:14:211] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 顾佳明(String), 1727200(Integer)
[10:26:14:257] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:14:291] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:14:292] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727201, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=张博宇, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:14:292] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:14:293] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:14:294] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727200(Integer)
[10:26:14:294] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 张博宇(String), 1727201(Integer)
[10:26:14:338] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:14:347] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:14:347] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727202, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=符野, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:14:348] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:14:348] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727201(Integer)
[10:26:14:348] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:14:349] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 符野(String), 1727202(Integer)
[10:26:14:403] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:14:462] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:14:462] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727203, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=黄梦辉, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:14:464] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:14:464] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:14:464] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727202(Integer)
[10:26:14:465] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 黄梦辉(String), 1727203(Integer)
[10:26:14:505] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:14:532] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:14:533] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727204, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=戴忠强, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:14:540] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:14:540] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:14:540] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727203(Integer)
[10:26:14:541] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 戴忠强(String), 1727204(Integer)
[10:26:14:601] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:14:606] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:14:607] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727205, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=彭家贵, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:14:608] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:14:608] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:14:608] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727204(Integer)
[10:26:14:609] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 彭家贵(String), 1727205(Integer)
[10:26:14:653] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:14:704] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:14:707] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727206, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=曹冻, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:14:708] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:14:709] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727205(Integer)
[10:26:14:710] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:14:710] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 曹冻(String), 1727206(Integer)
[10:26:14:773] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:14:791] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:14:793] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727207, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=乔佳琦, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:14:794] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:14:795] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:14:795] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727206(Integer)
[10:26:14:795] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 乔佳琦(String), 1727207(Integer)
[10:26:14:848] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:14:877] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:14:878] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727208, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=张昆, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:14:879] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:14:880] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727207(Integer)
[10:26:14:880] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:14:880] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 张昆(String), 1727208(Integer)
[10:26:14:939] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:14:947] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:14:948] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727209, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=彭少杰, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:14:948] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:14:948] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727208(Integer)
[10:26:14:949] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:14:949] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 彭少杰(String), 1727209(Integer)
[10:26:15:000] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:15:053] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:15:053] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727210, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=苗磊, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:15:055] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:15:055] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:15:055] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727209(Integer)
[10:26:15:056] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 苗磊(String), 1727210(Integer)
[10:26:15:109] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:15:136] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:15:137] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727211, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=朱永平, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:15:138] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:15:138] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727210(Integer)
[10:26:15:138] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:15:139] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 朱永平(String), 1727211(Integer)
[10:26:15:192] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:15:227] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:15:231] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727212, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=吴梓鑫, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:15:232] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:15:232] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727211(Integer)
[10:26:15:232] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:15:233] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 吴梓鑫(String), 1727212(Integer)
[10:26:15:281] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:15:307] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:15:308] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727213, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=鲁东海, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:15:309] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:15:309] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:15:309] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727212(Integer)
[10:26:15:309] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 鲁东海(String), 1727213(Integer)
[10:26:15:381] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:15:407] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:15:407] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727214, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=何海伟, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:15:408] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:15:408] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727213(Integer)
[10:26:15:408] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:15:408] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 何海伟(String), 1727214(Integer)
[10:26:15:464] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:15:473] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:15:473] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727215, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=赵艺, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:15:474] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:15:474] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727214(Integer)
[10:26:15:475] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:15:475] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 赵艺(String), 1727215(Integer)
[10:26:15:518] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:15:560] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:15:561] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727187, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=刘勇杰, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:15:561] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:15:562] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727215(Integer)
[10:26:15:562] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:15:562] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 刘勇杰(String), 1727187(Integer)
[10:26:15:609] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:15:622] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:15:623] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727188, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=靳通, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:15:623] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:15:623] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727187(Integer)
[10:26:15:624] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:15:624] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 靳通(String), 1727188(Integer)
[10:26:15:682] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:15:731] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:15:732] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727189, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=蔡子贤, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:15:732] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:15:733] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727188(Integer)
[10:26:15:733] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:15:733] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 蔡子贤(String), 1727189(Integer)
[10:26:15:777] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:15:817] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:15:818] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727190, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=刘胜男, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:15:819] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:15:819] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:15:819] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727189(Integer)
[10:26:15:819] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 刘胜男(String), 1727190(Integer)
[10:26:15:862] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:15:917] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:15:918] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727191, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=彭江, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:15:920] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:15:920] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:15:920] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727190(Integer)
[10:26:15:920] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 彭江(String), 1727191(Integer)
[10:26:15:984] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:16:042] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:16:047] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727192, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=闫金山, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:16:049] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:16:050] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:16:050] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727191(Integer)
[10:26:16:050] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 闫金山(String), 1727192(Integer)
[10:26:16:109] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:16:129] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:16:133] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727193, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=秦焱翔, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:16:136] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:16:137] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727192(Integer)
[10:26:16:141] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:16:143] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 秦焱翔(String), 1727193(Integer)
[10:26:16:212] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:16:237] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:16:239] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727194, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=李小霞, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:16:245] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:16:246] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:16:247] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727193(Integer)
[10:26:16:247] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 李小霞(String), 1727194(Integer)
[10:26:16:321] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:16:389] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:16:390] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727195, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=杨平, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:16:390] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:16:391] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727194(Integer)
[10:26:16:391] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:16:391] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 杨平(String), 1727195(Integer)
[10:26:16:448] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:16:546] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /*************:55257
[10:26:16:587] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:16:588] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727196, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=张瑞利, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:16:589] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:16:589] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727195(Integer)
[10:26:16:592] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:16:593] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 张瑞利(String), 1727196(Integer)
[10:26:16:653] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:16:702] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:16:702] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727197, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=刘泽文, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:16:703] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:16:703] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727196(Integer)
[10:26:16:704] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:16:704] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 刘泽文(String), 1727197(Integer)
[10:26:16:762] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:16:786] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:16:787] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727198, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=黄湘成, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:16:788] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:16:788] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727197(Integer)
[10:26:16:788] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:16:789] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 黄湘成(String), 1727198(Integer)
[10:26:16:851] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:16:880] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:16:881] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727199, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=吕荣祥, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:16:882] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:16:882] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727198(Integer)
[10:26:16:883] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:16:884] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 吕荣祥(String), 1727199(Integer)
[10:26:16:929] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:16:980] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:16:981] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727200, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=顾佳明, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:16:982] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:16:982] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:16:982] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727199(Integer)
[10:26:16:982] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 顾佳明(String), 1727200(Integer)
[10:26:17:034] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:17:068] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:17:069] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727201, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=张博宇, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:17:070] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:17:072] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727200(Integer)
[10:26:17:073] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:17:073] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 张博宇(String), 1727201(Integer)
[10:26:17:114] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:17:172] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:17:173] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727202, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=符野, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:17:173] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:17:173] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:17:174] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727201(Integer)
[10:26:17:174] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 符野(String), 1727202(Integer)
[10:26:17:239] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:17:258] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:17:258] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727203, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=黄梦辉, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:17:259] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:17:259] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727202(Integer)
[10:26:17:260] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:17:260] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 黄梦辉(String), 1727203(Integer)
[10:26:17:324] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:17:381] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:17:382] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727204, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=戴忠强, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:17:383] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:17:383] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727203(Integer)
[10:26:17:384] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:17:385] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 戴忠强(String), 1727204(Integer)
[10:26:17:438] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:17:468] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:17:470] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727205, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=彭家贵, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:17:471] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:17:471] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727204(Integer)
[10:26:17:472] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:17:472] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 彭家贵(String), 1727205(Integer)
[10:26:17:511] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:17:568] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:17:569] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727206, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=曹冻, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:17:571] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:17:571] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727205(Integer)
[10:26:17:571] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:17:571] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 曹冻(String), 1727206(Integer)
[10:26:17:658] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:17:658] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:17:659] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:17:660] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727206(Integer)
[10:26:17:660] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727207, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=乔佳琦, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:17:661] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:17:661] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 乔佳琦(String), 1727207(Integer)
[10:26:17:709] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:17:742] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:17:743] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727208, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=张昆, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:17:743] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:17:744] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727207(Integer)
[10:26:17:745] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:17:745] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 张昆(String), 1727208(Integer)
[10:26:17:798] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:17:815] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:17:816] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727209, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=彭少杰, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:17:816] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:17:816] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727208(Integer)
[10:26:17:822] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:17:822] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 彭少杰(String), 1727209(Integer)
[10:26:17:879] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:17:937] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:17:938] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727210, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=苗磊, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:17:939] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:17:939] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:17:939] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727209(Integer)
[10:26:17:939] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 苗磊(String), 1727210(Integer)
[10:26:18:005] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:18:027] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:18:028] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727211, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=朱永平, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:18:029] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:18:029] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727210(Integer)
[10:26:18:030] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:18:030] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 朱永平(String), 1727211(Integer)
[10:26:18:080] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:18:196] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:18:197] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727212, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=吴梓鑫, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:18:197] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:18:197] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727211(Integer)
[10:26:18:198] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:18:198] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 吴梓鑫(String), 1727212(Integer)
[10:26:18:255] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:18:282] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:18:283] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727213, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=鲁东海, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:18:283] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:18:284] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727212(Integer)
[10:26:18:285] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:18:285] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 鲁东海(String), 1727213(Integer)
[10:26:18:323] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:18:388] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:18:389] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727214, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=何海伟, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:18:390] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:18:390] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:18:395] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727213(Integer)
[10:26:18:396] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 何海伟(String), 1727214(Integer)
[10:26:18:450] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:18:482] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:18:482] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727215, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=赵艺, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:18:482] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:18:483] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:18:488] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727214(Integer)
[10:26:18:489] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 赵艺(String), 1727215(Integer)
[10:26:18:531] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:18:580] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:18:581] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727216, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=蒋贵盛, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:18:581] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:18:582] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727215(Integer)
[10:26:18:582] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:18:582] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 蒋贵盛(String), 1727216(Integer)
[10:26:18:645] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:18:668] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:18:669] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727217, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=陈灶晖, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:18:669] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:18:670] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:18:670] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727216(Integer)
[10:26:18:670] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 陈灶晖(String), 1727217(Integer)
[10:26:18:717] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:18:761] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:18:761] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinContactService.syncAllDepartmentNameAndUserNameByOCR(QiyeweixinContactService.java:266) - 保存用户信息: User [id=1727218, channel=null, nickname=null, gender=null, unionid=null, accountOpenid=null, openid=null, qkkOpenid=null, avatarUrl=null, phone=null, password=null, name=於宗海, province=null, city=null, address=null, company=null, department=null, createTime=null, enabled=null, memo=null, area=null, idNum=null, email=null, eteaAndroidOpenid=null]
[10:26:18:763] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: UPDATE `user` SET `name`=? WHERE `id` = ? 
[10:26:18:763] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:18:764] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727217(Integer)
[10:26:18:764] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 於宗海(String), 1727218(Integer)
[10:26:18:829] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:18:842] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==    Updates: 1
[10:26:18:843] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==>  Preparing: SELECT t.`id` , t.`channel` , t.`nickname` , t.`gender` , t.`unionid` , t.`account_openid` , t.`openid` , t.`qkk_openid` , t.`etea_android_openid` , t.`avatar_url` , t.`phone` , t.`password` , t.`name` , t.`province` , t.`city` , t.`address` , t.`company` , t.`department` , t.`create_time` , t.`enabled` , t.`memo` , t.`area` , t.`id_num` , t.`email` , t.`qkk_qiye_user_id` , t.`qkk_qiye_openid` , t.`qkk_qiye_corpid` FROM `user` t WHERE t.`id` = ? LIMIT 1 
[10:26:18:844] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - ==> Parameters: 1727218(Integer)
[10:26:18:906] [DEBUG] - org.apache.ibatis.logging.jdbc.BaseJdbcLogger.debug(BaseJdbcLogger.java:159) - <==      Total: 1
[10:26:20:387] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /*************:55256
[10:26:33:149] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /*************:55255
[10:26:36:559] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /*************:55235
[10:27:18:920] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /*************:55234
[10:38:00:912] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0x85f06502, L:/*************:54342 ! R:************72/************72:6379] closed due to PING response timeout set in 1000 ms
[10:38:01:047] [DEBUG] - org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:89) - channel: [id: 0xe2df1973, L:/*************:54333 - R:************72/************72:6379] closed due to PING response timeout set in 1000 ms
[10:38:01:049] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1959680114 [redisClient=[addr=redis://************72:6379], channel=[id: 0x85f06502, L:/*************:54342 ! R:************72/************72:6379], currentCommand=null, usage=0] to ************72/************72:6379 
[10:38:01:119] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:38:01:227] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog.tryReconnect(ConnectionWatchdog.java:110) - reconnecting RedisConnection@1636075003 [redisClient=[addr=redis://************72:6379], channel=[id: 0xe2df1973, L:/*************:54333 ! R:************72/************72:6379], currentCommand=null, usage=0] to ************72/************72:6379 
[10:38:01:275] [DEBUG] - org.redisson.client.RedisConnection.<init>(RedisConnection.java:72) - Connection created [addr=redis://************72:6379]
[10:38:01:346] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1959680114 [redisClient=[addr=redis://************72:6379], channel=[id: 0x85f06502, L:/*************:54342 ! R:************72/************72:6379], currentCommand=null, usage=0] connected to ************72/************72:6379, command: null
[10:38:01:359] [DEBUG] - org.redisson.client.handler.ConnectionWatchdog$2.lambda$operationComplete$0(ConnectionWatchdog.java:145) - RedisConnection@1636075003 [redisClient=[addr=redis://************72:6379], channel=[id: 0xe2df1973, L:/*************:54333 ! R:************72/************72:6379], currentCommand=null, usage=0] connected to ************72/************72:6379, command: null
[10:38:38:746] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /cryptography/encrypt, authentication required: false
[10:38:38:753] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1b89c3f7 for /cryptography/encrypt
[10:38:38:754] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /cryptography/encrypt
[10:38:38:817] [DEBUG] - com.taurus.examinationassistant.controller.CryptographyController.encrypt(CryptographyController.java:23) - YEudVYacTsPkq%2Br8CbGpOQ%3D%3D
[10:39:38:885] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /*************:56070
[10:40:24:480] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /qiyeweixinAccount/queryAutoActiveStatus, authentication required: false
[10:40:24:485] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@1b89c3f7 for /qiyeweixinAccount/queryAutoActiveStatus
[10:40:24:486] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /qiyeweixinAccount/queryAutoActiveStatus
[10:40:24:505] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinAccountService.getAutoActiveStatus(QiyeweixinAccountService.java:603) - 查询企业的许可自动激活状态, corpId:wpfqwcEQAAWaU9nX3bDFZlntOKTU_rsg
[10:40:25:038] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinAccountService.getAutoActiveStatus(QiyeweixinAccountService.java:620) - 查询企业的许可自动激活状态响应: {errcode=701071, errmsg=never buy any license for corp, hint: [1748313625200120391793910], from ip: ***************, more info at https://open.work.weixin.qq.com/devtool/query?e=701071}
[10:40:25:052] [INFO] - com.taurus.examinationassistant.service.qiyeweixin.QiyeweixinAccountService.getAutoActiveStatus(QiyeweixinAccountService.java:625) - 查询企业的许可自动激活状态失败：errcode:701071,errmsg:never buy any license for corp, hint: [1748313625200120391793910], from ip: ***************, more info at https://open.work.weixin.qq.com/devtool/query?e=701071
