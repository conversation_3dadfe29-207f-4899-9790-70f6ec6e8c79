[07:06:36:274] [WARN] - redis.clients.jedis.JedisFactory.destroyObject(JedisFactory.java:168) - Error while close
redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketException: Broken pipe (Write failed)
	at redis.clients.jedis.Connection.disconnect(Connection.java:256) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.BinaryClient.disconnect(BinaryClient.java:162) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.Connection.close(Connection.java:246) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.BinaryClient.close(BinaryClient.java:168) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.BinaryJedis.close(BinaryJedis.java:340) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.Jedis.close(Jedis.java:4084) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.JedisFactory.destroyObject(JedisFactory.java:166) ~[jedis-3.8.0.jar:?]
	at org.apache.commons.pool2.PooledObjectFactory.destroyObject(PooledObjectFactory.java:127) ~[commons-pool2-2.11.1.jar:2.11.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.destroy(GenericObjectPool.java:611) ~[commons-pool2-2.11.1.jar:2.11.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.invalidateObject(GenericObjectPool.java:941) ~[commons-pool2-2.11.1.jar:2.11.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.invalidateObject(GenericObjectPool.java:914) ~[commons-pool2-2.11.1.jar:2.11.1]
	at redis.clients.jedis.util.Pool.returnBrokenResourceObject(Pool.java:124) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.util.Pool.returnBrokenResource(Pool.java:103) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.Jedis.close(Jedis.java:4079) ~[jedis-3.8.0.jar:?]
	at org.springframework.data.redis.connection.jedis.JedisConnection.close(JedisConnection.java:352) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at org.springframework.data.redis.listener.RedisMessageListenerContainer$Subscriber.closeConnection(RedisMessageListenerContainer.java:1320) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at org.springframework.data.redis.listener.RedisMessageListenerContainer.handleSubscriptionException(RedisMessageListenerContainer.java:890) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at org.springframework.data.redis.listener.RedisMessageListenerContainer$BlockingSubscriber.lambda$eventuallyPerformSubscription$2(RedisMessageListenerContainer.java:1434) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_281]
Caused by: java.net.SocketException: Broken pipe (Write failed)
	at java.net.SocketOutputStream.socketWrite0(Native Method) ~[?:1.8.0_281]
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:111) ~[?:1.8.0_281]
	at java.net.SocketOutputStream.write(SocketOutputStream.java:155) ~[?:1.8.0_281]
	at redis.clients.jedis.util.RedisOutputStream.flushBuffer(RedisOutputStream.java:52) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.util.RedisOutputStream.flush(RedisOutputStream.java:133) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.Connection.disconnect(Connection.java:252) ~[jedis-3.8.0.jar:?]
	... 18 more
[07:19:58:188] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口://version/getSystemSettings，出现异常:org.springframework.data.redis.RedisConnectionFailureException；
[07:19:58:215] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"Accept":"*/*","User-Agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.06.2412050 MicroMessenger/8.0.5 Language/zh_CN webview/","Connection":"close","X-Forwarded-Proto":"http","X-Original-URI":"/exam/version/getSystemSettings","Referer":"https://servicewechat.com/wxa38e954efdceffbe/devtools/page-frame.html","Sec-Fetch-Dest":"empty","Sec-Fetch-Site":"cross-site","Host":"localhost","Accept-Encoding":"gzip, deflate, br","Sec-Fetch-Mode":"cors","X-Forwarded-For":"************","content-type":"application/json","X-Real-IP":"************"}
[07:19:58:217] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{}
[07:19:58:218] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：org.springframework.data.redis.RedisConnectionFailureException: Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool，异常信息：Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool，
[07:19:58:349] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口://examination/getRecommendExaminations，出现异常:org.springframework.data.redis.RedisConnectionFailureException；
[07:19:58:351] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"Accept":"*/*","User-Agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.06.2412050 MicroMessenger/8.0.5 Language/zh_CN webview/","Connection":"close","X-Forwarded-Proto":"http","X-Original-URI":"/exam/examination/getRecommendExaminations?type=public&companyId=0&pageIndex=1&pageSize=6&createrId=5&refresh=true","Referer":"https://servicewechat.com/wxa38e954efdceffbe/devtools/page-frame.html","Sec-Fetch-Dest":"empty","Sec-Fetch-Site":"cross-site","Host":"localhost","Accept-Encoding":"gzip, deflate, br","Sec-Fetch-Mode":"cors","X-Forwarded-For":"************","content-type":"application/json","X-Real-IP":"************"}
[07:19:58:351] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"companyId":"0","pageIndex":"1","pageSize":"6","refresh":"true","createrId":"5","type":"public"}
[07:19:58:351] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：org.springframework.data.redis.RedisConnectionFailureException: Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool，异常信息：Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool，
[07:20:14:734] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口://version/getSystemSettings，出现异常:org.springframework.data.redis.RedisConnectionFailureException；
[07:20:14:738] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"Accept":"*/*","User-Agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.06.2412050 MicroMessenger/8.0.5 Language/zh_CN webview/","Connection":"close","X-Forwarded-Proto":"http","X-Original-URI":"/exam/version/getSystemSettings","Referer":"https://servicewechat.com/wxa38e954efdceffbe/devtools/page-frame.html","Sec-Fetch-Dest":"empty","Sec-Fetch-Site":"cross-site","Host":"localhost","Accept-Encoding":"gzip, deflate, br","Sec-Fetch-Mode":"cors","X-Forwarded-For":"************","content-type":"application/json","X-Real-IP":"************"}
[07:20:14:740] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{}
[07:20:14:740] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：org.springframework.data.redis.RedisConnectionFailureException: Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool，异常信息：Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool，
[07:20:14:882] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口://examination/getRecommendExaminations，出现异常:org.springframework.data.redis.RedisConnectionFailureException；
[07:20:14:885] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"Accept":"*/*","User-Agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.06.2412050 MicroMessenger/8.0.5 Language/zh_CN webview/","Connection":"close","X-Forwarded-Proto":"http","X-Original-URI":"/exam/examination/getRecommendExaminations?type=public&companyId=0&pageIndex=1&pageSize=6&createrId=5&refresh=true","Referer":"https://servicewechat.com/wxa38e954efdceffbe/devtools/page-frame.html","Sec-Fetch-Dest":"empty","Sec-Fetch-Site":"cross-site","Host":"localhost","Accept-Encoding":"gzip, deflate, br","Sec-Fetch-Mode":"cors","X-Forwarded-For":"************","content-type":"application/json","X-Real-IP":"************"}
[07:20:14:885] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{"companyId":"0","pageIndex":"1","pageSize":"6","refresh":"true","createrId":"5","type":"public"}
[07:20:14:885] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：org.springframework.data.redis.RedisConnectionFailureException: Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool，异常信息：Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool，
[07:20:36:136] [ERROR] - com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2699) - create connection SQLException, url: *****************************************************************************************************************************************************************, errorCode 0, state 08001
java.sql.SQLNonTransientConnectionException: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:111) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:98) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:90) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:64) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:74) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:895) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1596) ~[druid-1.1.17.jar:1.1.17]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1662) ~[druid-1.1.17.jar:1.1.17]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2697) [druid-1.1.17.jar:1.1.17]
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_281]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_281]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	... 7 more
Caused by: java.net.ConnectException: Operation timed out (Connection timed out)
	at java.net.PlainSocketImpl.socketConnect(Native Method) ~[?:1.8.0_281]
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476) ~[?:1.8.0_281]
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218) ~[?:1.8.0_281]
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200) ~[?:1.8.0_281]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394) ~[?:1.8.0_281]
	at java.net.Socket.connect(Socket.java:606) ~[?:1.8.0_281]
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	... 7 more
[07:21:18:906] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[07:21:22:654] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[07:21:22:772] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[07:21:22:773] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[07:21:22:773] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[07:21:22:774] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[07:21:22:774] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[07:21:22:774] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[07:21:22:774] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[07:21:22:774] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[07:21:22:774] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[07:21:22:774] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[07:21:22:775] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[07:21:22:775] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[07:21:22:839] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[07:21:23:412] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[07:21:23:427] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[07:21:24:233] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[07:21:24:268] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[07:21:24:605] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[07:21:24:614] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[07:21:24:614] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[07:21:24:615] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[07:21:24:615] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[07:21:24:616] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[07:21:24:617] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[07:21:24:618] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[07:21:24:618] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[07:22:06:295] [ERROR] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:936) - init datasource error, url: *****************************************************************************************************************************************************************
java.sql.SQLNonTransientConnectionException: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:111) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:98) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:90) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:64) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:74) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:895) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1596) ~[druid-1.1.17.jar:1.1.17]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1662) ~[druid-1.1.17.jar:1.1.17]
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:932) ~[druid-1.1.17.jar:1.1.17]
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1369) ~[druid-1.1.17.jar:1.1.17]
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1365) ~[druid-1.1.17.jar:1.1.17]
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:109) ~[druid-1.1.17.jar:1.1.17]
	at com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:94) ~[fastmybatis-core-1.8.4.jar:?]
	at com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.setDataSource(SqlSessionFactoryBeanExt.java:42) ~[fastmybatis-core-1.8.4.jar:?]
	at com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration.sqlSessionFactory(MybatisAutoConfiguration.java:129) ~[fastmybatis-spring-boot-starter-1.8.4.jar:?]
	at com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$$EnhancerBySpringCGLIB$$6a710a5b.CGLIB$sqlSessionFactory$0(<generated>) ~[fastmybatis-spring-boot-starter-1.8.4.jar:?]
	at com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$$EnhancerBySpringCGLIB$$6a710a5b$$FastClassBySpringCGLIB$$ad66b0f6.invoke(<generated>) ~[fastmybatis-spring-boot-starter-1.8.4.jar:?]
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:244) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331) ~[spring-context-5.3.31.jar:5.3.31]
	at com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$$EnhancerBySpringCGLIB$$6a710a5b.sqlSessionFactory(<generated>) ~[fastmybatis-spring-boot-starter-1.8.4.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:648) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:633) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1519) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1417) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.SpringApplicationBuilder.run(SpringApplicationBuilder.java:164) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.examinationassistant.ExaminationAssistantApplication.main(ExaminationAssistantApplication.java:44) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_281]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_281]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	... 114 more
Caused by: java.net.ConnectException: Operation timed out (Connection timed out)
	at java.net.PlainSocketImpl.socketConnect(Native Method) ~[?:1.8.0_281]
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476) ~[?:1.8.0_281]
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218) ~[?:1.8.0_281]
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200) ~[?:1.8.0_281]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394) ~[?:1.8.0_281]
	at java.net.Socket.connect(Socket.java:606) ~[?:1.8.0_281]
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	... 114 more
[07:22:06:341] [ERROR] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:978) - {dataSource-1} init error
java.sql.SQLNonTransientConnectionException: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:111) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:98) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:90) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:64) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:74) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:895) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1596) ~[druid-1.1.17.jar:1.1.17]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1662) ~[druid-1.1.17.jar:1.1.17]
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:932) ~[druid-1.1.17.jar:1.1.17]
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1369) ~[druid-1.1.17.jar:1.1.17]
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1365) ~[druid-1.1.17.jar:1.1.17]
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:109) ~[druid-1.1.17.jar:1.1.17]
	at com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:94) ~[fastmybatis-core-1.8.4.jar:?]
	at com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.setDataSource(SqlSessionFactoryBeanExt.java:42) ~[fastmybatis-core-1.8.4.jar:?]
	at com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration.sqlSessionFactory(MybatisAutoConfiguration.java:129) ~[fastmybatis-spring-boot-starter-1.8.4.jar:?]
	at com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$$EnhancerBySpringCGLIB$$6a710a5b.CGLIB$sqlSessionFactory$0(<generated>) ~[fastmybatis-spring-boot-starter-1.8.4.jar:?]
	at com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$$EnhancerBySpringCGLIB$$6a710a5b$$FastClassBySpringCGLIB$$ad66b0f6.invoke(<generated>) ~[fastmybatis-spring-boot-starter-1.8.4.jar:?]
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:244) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331) ~[spring-context-5.3.31.jar:5.3.31]
	at com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$$EnhancerBySpringCGLIB$$6a710a5b.sqlSessionFactory(<generated>) ~[fastmybatis-spring-boot-starter-1.8.4.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:648) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:633) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1519) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1417) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.SpringApplicationBuilder.run(SpringApplicationBuilder.java:164) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.examinationassistant.ExaminationAssistantApplication.main(ExaminationAssistantApplication.java:44) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_281]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_281]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	... 114 more
Caused by: java.net.ConnectException: Operation timed out (Connection timed out)
	at java.net.PlainSocketImpl.socketConnect(Native Method) ~[?:1.8.0_281]
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476) ~[?:1.8.0_281]
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218) ~[?:1.8.0_281]
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200) ~[?:1.8.0_281]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394) ~[?:1.8.0_281]
	at java.net.Socket.connect(Socket.java:606) ~[?:1.8.0_281]
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	... 114 more
[07:22:06:349] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[07:22:06:350] [ERROR] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:100) - Could not create connection to database server. Attempted reconnect 3 times. Giving up.
java.sql.SQLNonTransientConnectionException: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:111) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:98) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:90) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:64) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:74) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:895) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1596) ~[druid-1.1.17.jar:1.1.17]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1662) ~[druid-1.1.17.jar:1.1.17]
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:932) ~[druid-1.1.17.jar:1.1.17]
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1369) ~[druid-1.1.17.jar:1.1.17]
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1365) ~[druid-1.1.17.jar:1.1.17]
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:109) ~[druid-1.1.17.jar:1.1.17]
	at com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:94) ~[fastmybatis-core-1.8.4.jar:?]
	at com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.setDataSource(SqlSessionFactoryBeanExt.java:42) ~[fastmybatis-core-1.8.4.jar:?]
	at com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration.sqlSessionFactory(MybatisAutoConfiguration.java:129) ~[fastmybatis-spring-boot-starter-1.8.4.jar:?]
	at com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$$EnhancerBySpringCGLIB$$6a710a5b.CGLIB$sqlSessionFactory$0(<generated>) ~[fastmybatis-spring-boot-starter-1.8.4.jar:?]
	at com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$$EnhancerBySpringCGLIB$$6a710a5b$$FastClassBySpringCGLIB$$ad66b0f6.invoke(<generated>) ~[fastmybatis-spring-boot-starter-1.8.4.jar:?]
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:244) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331) ~[spring-context-5.3.31.jar:5.3.31]
	at com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$$EnhancerBySpringCGLIB$$6a710a5b.sqlSessionFactory(<generated>) ~[fastmybatis-spring-boot-starter-1.8.4.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:648) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:633) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1519) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1417) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) [spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.SpringApplicationBuilder.run(SpringApplicationBuilder.java:164) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.examinationassistant.ExaminationAssistantApplication.main(ExaminationAssistantApplication.java:44) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_281]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_281]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	... 114 more
Caused by: java.net.ConnectException: Operation timed out (Connection timed out)
	at java.net.PlainSocketImpl.socketConnect(Native Method) ~[?:1.8.0_281]
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476) ~[?:1.8.0_281]
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218) ~[?:1.8.0_281]
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200) ~[?:1.8.0_281]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394) ~[?:1.8.0_281]
	at java.net.Socket.connect(Socket.java:606) ~[?:1.8.0_281]
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	... 114 more
[07:22:06:369] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:1948) - {dataSource-1} closing ...
[07:22:06:371] [INFO] - com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2020) - {dataSource-1} closed
[07:22:49:039] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[07:22:56:884] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[07:22:57:063] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[07:22:57:068] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[07:22:57:069] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[07:22:57:070] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[07:22:57:070] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[07:22:57:070] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[07:22:57:070] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[07:22:57:071] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[07:22:57:071] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[07:22:57:074] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[07:22:57:076] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[07:22:57:076] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[07:22:57:180] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[07:22:58:243] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[07:22:58:258] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[07:23:00:141] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[07:23:00:254] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[07:23:00:819] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[07:23:00:830] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[07:23:00:831] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[07:23:00:832] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[07:23:00:832] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[07:23:00:833] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[07:23:00:834] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[07:23:00:835] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[07:23:00:836] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[07:23:05:780] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[07:23:05:795] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[07:23:06:107] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[07:23:07:759] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：1.649秒
[07:23:14:275] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[07:23:14:344] [WARN] - io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:70) - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
[07:23:14:810] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[07:23:15:845] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[07:23:21:776] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[07:23:22:636] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
[07:23:22:693] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 35 ms to scan 1 urls, producing 4 keys and 9 values 
[07:23:22:725] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 29 ms to scan 1 urls, producing 3 keys and 10 values 
[07:23:22:761] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 32 ms to scan 12 urls, producing 0 keys and 0 values 
[07:23:22:772] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
[07:23:22:790] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
[07:23:22:809] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
[07:23:22:843] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 29 ms to scan 12 urls, producing 0 keys and 0 values 
[07:23:24:007] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[07:23:24:007] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[07:23:24:025] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[07:23:24:050] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[07:23:24:071] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[07:23:24:148] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[07:23:24:279] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service 192.168.0.100:8081 register finished
[07:23:24:496] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 40.254 seconds (JVM running for 43.285)
[07:23:24:535] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[07:23:29:249] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[15:11:54:613] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "test-etea"
[15:11:58:205] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[15:11:58:297] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[15:11:58:301] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:11:58:301] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:11:58:302] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[15:11:58:302] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[15:11:58:302] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[15:11:58:303] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[15:11:58:303] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:11:58:303] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-test-etea.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:11:58:303] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:11:58:303] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:11:58:303] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:11:58:367] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[15:11:58:904] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[15:11:58:909] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[15:11:59:674] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[15:11:59:697] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[15:12:00:027] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[15:12:00:038] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[15:12:00:039] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[15:12:00:039] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[15:12:00:039] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[15:12:00:041] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[15:12:00:044] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[15:12:00:045] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[15:12:00:047] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[15:12:05:726] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[15:12:05:741] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[15:12:06:009] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[15:12:07:647] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：1.633秒
[15:12:13:901] [INFO] - org.redisson.Version.logVersion(Version.java:41) - Redisson 3.17.3
[15:12:13:976] [WARN] - io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:70) - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
[15:12:14:503] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 1 connections initialized for **************/**************:6379
[15:12:15:804] [INFO] - org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:158) - 24 connections initialized for **************/**************:6379
[15:12:22:043] [INFO] - io.undertow.websockets.jsr.ServerWebSocketContainer.addEndpointInternal(ServerWebSocketContainer.java:639) - UT026003: Adding annotated server endpoint class com.taurus.component.MonitorWebSocketServer for path /monitor/{type}/{sessionId}
[15:12:22:941] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 82 ms to scan 1 urls, producing 3 keys and 6 values 
[15:12:23:039] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 35 ms to scan 1 urls, producing 4 keys and 9 values 
[15:12:23:084] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 25 ms to scan 1 urls, producing 3 keys and 10 values 
[15:12:23:132] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 38 ms to scan 12 urls, producing 0 keys and 0 values 
[15:12:23:150] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 14 ms to scan 1 urls, producing 1 keys and 5 values 
[15:12:23:165] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
[15:12:23:183] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
[15:12:23:211] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 25 ms to scan 12 urls, producing 0 keys and 0 values 
[15:12:24:503] [INFO] - com.taurus.examinationassistant.filter.SqlXssFilter.init(SqlXssFilter.java:85) - SqlXssFilter initialized with exclude patterns: [/access/getTokenStr, /user/updateUserInfo, /examination/configAdvancedOptions]
[15:12:24:504] [INFO] - com.taurus.examinationassistant.filter.ReplaceStreamFilter.init(ReplaceStreamFilter.java:34) - ReplaceStreamFilter初始化...
[15:12:24:522] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[15:12:24:541] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[15:12:24:558] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[15:12:24:599] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[15:12:24:708] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP exam-main-service *************:8081 register finished
[15:12:25:132] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started ExaminationAssistantApplication in 32.863 seconds (JVM running for 34.843)
[15:12:25:164] [INFO] - com.taurus.component.NacosConfig.getTomcatPort(NacosConfig.java:53) - 应用未在web容器内启动（如：tomcat），未获取到容器端口号
[15:41:58:717] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[15:42:04:451] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:55) - 产品：qkk 页面：/pages/examindex/index?
[15:42:04:451] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:56) - 请求接口：/examination/getRecommendExaminationsByProduct
[15:42:04:500] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:57) - 请求参数：{"product":"qkk","createrId":"6","terminal":"qkk","callLocationCode":"/pages/examindex/index?"}
[16:06:01:150] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:55) - 产品：qkk 页面：/pages/examindex/index?
[16:06:01:153] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:56) - 请求接口：/examination/getRecommendExaminationsByProduct
[16:06:01:154] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:57) - 请求参数：{"product":"qkk","createrId":"6","terminal":"qkk","callLocationCode":"/pages/examindex/index?"}
[16:10:13:050] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:55) - 产品：qkk 页面：/pages/examindex/index?
[16:10:13:053] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:56) - 请求接口：/examination/getRecommendExaminationsByProduct
[16:10:13:054] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:57) - 请求参数：{"product":"qkk","createrId":"6","terminal":"qkk","callLocationCode":"/pages/examindex/index?"}
[16:38:18:544] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:55) - 产品：qkk 页面：/pages/examindex/index?
[16:38:18:547] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:56) - 请求接口：/examination/getRecommendExaminationsByProduct
[16:38:18:550] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:57) - 请求参数：{"product":"qkk","createrId":"6","terminal":"qkk","callLocationCode":"/pages/examindex/index?"}
[16:39:07:910] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:55) - 产品：qkk 页面：/pages/exam/examination?
[16:39:07:912] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:56) - 请求接口：/examination/examinationWrapperWithRedLock
[16:39:07:918] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:57) - 请求参数：{"id":"114505","terminal":"qkk","callLocationCode":"/pages/exam/examination?"}
[16:39:14:408] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:55) - 产品：qkk 页面：/pages/exam/examination?
[16:39:14:409] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:56) - 请求接口：/examination/examinationWrapperWithRedLock
[16:39:14:410] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:57) - 请求参数：{"id":"114505","terminal":"qkk","callLocationCode":"/pages/exam/examination?"}
[16:51:07:641] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:55) - 产品：qkk 页面：/pages/examindex/index?
[16:51:07:642] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:56) - 请求接口：/examination/getRecommendExaminationsByProduct
[16:51:07:643] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:57) - 请求参数：{"product":"qkk","createrId":"6","terminal":"qkk","callLocationCode":"/pages/examindex/index?"}
[16:55:25:201] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:55) - 产品：qkk 页面：/pages/exam/examination?
[16:55:25:202] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:56) - 请求接口：/examination/examinationWrapperWithRedLock
[16:55:25:203] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:57) - 请求参数：{"id":"114505","terminal":"qkk","callLocationCode":"/pages/exam/examination?"}
[16:55:31:561] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:55) - 产品：qkk 页面：/pages/exam/examination?
[16:55:31:562] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:56) - 请求接口：/examination/examinationWrapperWithRedLock
[16:55:31:563] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:57) - 请求参数：{"id":"114505","terminal":"qkk","callLocationCode":"/pages/exam/examination?"}
[16:58:07:262] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:55) - 产品：qkk 页面：/pages/exam/examination?
[16:58:07:263] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:56) - 请求接口：/examination/examinationWrapperWithRedLock
[16:58:07:263] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:57) - 请求参数：{"id":"114505","terminal":"qkk","callLocationCode":"/pages/exam/examination?"}
[17:02:06:423] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:55) - 产品：qkk 页面：/pages/exam/examination?
[17:02:06:424] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:56) - 请求接口：/examination/examinationWrapperWithRedLock
[17:02:06:425] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:57) - 请求参数：{"id":"114505","terminal":"qkk","callLocationCode":"/pages/exam/examination?"}
[17:02:12:716] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:55) - 产品：qkk 页面：/pages/exam/examination?
[17:02:12:717] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:56) - 请求接口：/examination/examinationWrapperWithRedLock
[17:02:12:717] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:57) - 请求参数：{"id":"114505","terminal":"qkk","callLocationCode":"/pages/exam/examination?"}
[17:03:18:168] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:55) - 产品：qkk 页面：/pages/examindex/index?
[17:03:18:170] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:56) - 请求接口：/examination/getRecommendExaminationsByProduct
[17:03:18:170] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:57) - 请求参数：{"product":"qkk","createrId":"6","terminal":"qkk","callLocationCode":"/pages/examindex/index?"}
[17:03:39:528] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:55) - 产品：qkk 页面：/pages/examindex/index?
[17:03:39:528] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:56) - 请求接口：/examination/getRecommendExaminationsByProduct
[17:03:39:530] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:57) - 请求参数：{"product":"qkk","createrId":"6","terminal":"qkk","callLocationCode":"/pages/examindex/index?"}
[17:03:40:589] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[17:03:40:739] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[17:03:40:895] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[17:03:41:039] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[17:03:41:188] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[17:03:41:335] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=/] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[17:03:41:473] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[17:03:41:601] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[17:03:41:751] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[17:03:41:891] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[17:03:42:037] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[17:03:42:160] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[17:03:42:279] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[17:03:42:391] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[17:03:42:490] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[17:03:42:589] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[17:03:42:741] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[17:03:42:856] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[17:03:42:969] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[17:03:43:105] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[17:06:11:260] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:55) - 产品：qkk 页面：/pages/examindex/index?
[17:06:11:261] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:56) - 请求接口：/examination/getRecommendExaminationsByProduct
[17:06:11:261] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:57) - 请求参数：{"product":"qkk","createrId":"6","terminal":"qkk","callLocationCode":"/pages/examindex/index?"}
[17:06:17:908] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:25) - 请求接口:/examination/getExaminationInViewListOfCreaterByTags，出现异常:java.io.IOException；
[17:06:17:922] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:250) - 请求头:{"X-Forwarded-Prefix":"/exam","Accept":"*/*","Referer":"https://servicewechat.com/wxfc49b21929e8692f/devtools/page-frame.html","X-Forwarded-Host":"localhost:8000","User-Agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 17_0_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0.1 Mobile/21A360 Safari/604.1 wechatdevtools/1.06.2412050 MicroMessenger/8.0.5 Language/zh_CN webview/","X-Forwarded-Proto":"http","Sec-Fetch-Site":"cross-site","Sec-Fetch-Dest":"empty","Accept-Encoding":"gzip, deflate, br","userId":"37377","X-Forwarded-Port":"8000","token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJ5aWRhbyIsImV4cCI6MTc0NzEyNjkyNSwiaWF0IjoxNzQ2NTIyMTI1LCJ1c2VySWQiOjM3Mzc3fQ.mAom-Ko7hz1vPe4pf7j2yhD03ADcDwZOHqjDt-vrlvE","Sec-Fetch-Mode":"cors","host":"*************:8081","Forwarded":"proto=http;host=\"localhost:8000\";for=\"[0:0:0:0:0:0:0:1]:62290\"","client":"qkkmobile","X-Forwarded-For":"0:0:0:0:0:0:0:1","content-type":"application/json;charset=UTF-8","Content-Length":"150"}
[17:06:17:923] [ERROR] - com.taurus.utils.HttpUtil.printRequestParameters(HttpUtil.java:262) - 请求参数:{}
[17:06:17:923] [ERROR] - com.taurus.examinationassistant.exception.GlobalExceptionHandler.allExceptionHandler(GlobalExceptionHandler.java:27) - 异常类：java.io.IOException: Broken pipe，异常信息：Broken pipe，
[17:07:34:520] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:55) - 产品：qkk 页面：/pages/exam/examination?
[17:07:34:521] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:56) - 请求接口：/examination/examinationWrapperWithRedLock
[17:07:34:521] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:57) - 请求参数：{"id":"114505","terminal":"qkk","callLocationCode":"/pages/exam/examination?"}
[17:07:40:629] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:55) - 产品：qkk 页面：/pages/examindex/index?
[17:07:40:630] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:56) - 请求接口：/examination/getRecommendExaminationsByProduct
[17:07:40:632] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:57) - 请求参数：{"product":"qkk","createrId":"6","terminal":"qkk","callLocationCode":"/pages/examindex/index?"}
[17:08:17:184] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:55) - 产品：qkk 页面：/pages/exam/examination?
[17:08:17:185] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:56) - 请求接口：/examination/examinationWrapperWithRedLock
[17:08:17:187] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:57) - 请求参数：{"id":"114505","terminal":"qkk","callLocationCode":"/pages/exam/examination?"}
[17:08:23:569] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:55) - 产品：qkk 页面：/pages/exam/examination?
[17:08:23:570] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:56) - 请求接口：/examination/examinationWrapperWithRedLock
[17:08:23:570] [INFO] - com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:57) - 请求参数：{"id":"114505","terminal":"qkk","callLocationCode":"/pages/exam/examination?"}
[22:52:11:939] [WARN] - redis.clients.jedis.JedisFactory.destroyObject(JedisFactory.java:168) - Error while close
redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketException: Broken pipe (Write failed)
	at redis.clients.jedis.Connection.disconnect(Connection.java:256) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.BinaryClient.disconnect(BinaryClient.java:162) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.Connection.close(Connection.java:246) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.BinaryClient.close(BinaryClient.java:168) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.BinaryJedis.close(BinaryJedis.java:340) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.Jedis.close(Jedis.java:4084) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.JedisFactory.destroyObject(JedisFactory.java:166) ~[jedis-3.8.0.jar:?]
	at org.apache.commons.pool2.PooledObjectFactory.destroyObject(PooledObjectFactory.java:127) ~[commons-pool2-2.11.1.jar:2.11.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.destroy(GenericObjectPool.java:611) ~[commons-pool2-2.11.1.jar:2.11.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.invalidateObject(GenericObjectPool.java:941) ~[commons-pool2-2.11.1.jar:2.11.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.invalidateObject(GenericObjectPool.java:914) ~[commons-pool2-2.11.1.jar:2.11.1]
	at redis.clients.jedis.util.Pool.returnBrokenResourceObject(Pool.java:124) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.util.Pool.returnBrokenResource(Pool.java:103) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.Jedis.close(Jedis.java:4079) ~[jedis-3.8.0.jar:?]
	at org.springframework.data.redis.connection.jedis.JedisConnection.close(JedisConnection.java:352) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at org.springframework.data.redis.listener.RedisMessageListenerContainer$Subscriber.closeConnection(RedisMessageListenerContainer.java:1320) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at org.springframework.data.redis.listener.RedisMessageListenerContainer.handleSubscriptionException(RedisMessageListenerContainer.java:890) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at org.springframework.data.redis.listener.RedisMessageListenerContainer$BlockingSubscriber.lambda$eventuallyPerformSubscription$2(RedisMessageListenerContainer.java:1434) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_281]
Caused by: java.net.SocketException: Broken pipe (Write failed)
	at java.net.SocketOutputStream.socketWrite0(Native Method) ~[?:1.8.0_281]
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:111) ~[?:1.8.0_281]
	at java.net.SocketOutputStream.write(SocketOutputStream.java:155) ~[?:1.8.0_281]
	at redis.clients.jedis.util.RedisOutputStream.flushBuffer(RedisOutputStream.java:52) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.util.RedisOutputStream.flush(RedisOutputStream.java:133) ~[jedis-3.8.0.jar:?]
	at redis.clients.jedis.Connection.disconnect(Connection.java:252) ~[jedis-3.8.0.jar:?]
	... 18 more
