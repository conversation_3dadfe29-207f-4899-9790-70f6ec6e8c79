[14:52:38:469] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[14:52:38:822] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[14:52:38:976] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[14:52:39:099] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[14:52:39:307] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[14:52:39:459] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[14:52:39:594] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[14:52:39:775] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[14:52:39:925] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[14:52:40:101] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[14:52:40:206] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[14:52:40:360] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[14:52:50:105] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[14:53:38:841] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[14:53:39:009] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[14:53:39:172] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[14:53:39:310] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[14:53:39:443] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[14:53:39:584] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[14:53:39:721] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[14:53:39:856] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[14:53:40:038] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[14:53:40:218] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[14:53:40:375] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[14:53:40:532] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:06:08:295] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:12:35:354] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:12:35:442] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:12:35:539] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:12:35:647] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:12:35:760] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:12:35:861] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:12:35:948] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:12:36:049] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:12:36:146] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:12:36:229] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:12:36:303] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:12:36:390] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:14:22:809] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:14:22:908] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:14:23:009] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:14:23:089] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:14:23:191] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/qkk/qkkExamination/20240821/20240821100755d788.jpg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:14:23:316] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/qkk/qkkExamination/20240821/20240821100755d788.jpg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:14:27:674] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:14:28:026] [ERROR] - com.taurus.examinationassistant.service.QuestionRandomService.getPaperQuestionWrapperListByPaperQuestionRandom(QuestionRandomService.java:275) - 获取随机题对应的PaperQuestionWrapper列表失败
java.lang.ArrayIndexOutOfBoundsException: -1
	at java.util.ArrayList.elementData(ArrayList.java:424) ~[?:1.8.0_281]
	at java.util.ArrayList.get(ArrayList.java:437) ~[?:1.8.0_281]
	at com.taurus.examinationassistant.service.QuestionRandomService.getPaperQuestionWrapperListByPaperQuestionRandom(QuestionRandomService.java:262) [classes/:?]
	at com.taurus.examinationassistant.service.QuestionRandomService$$FastClassBySpringCGLIB$$c5e7570b.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) [spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) [spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) [spring-aop-5.3.31.jar:5.3.31]
	at com.taurus.examinationassistant.service.QuestionRandomService$$EnhancerBySpringCGLIB$$b4dc5c89.getPaperQuestionWrapperListByPaperQuestionRandom(<generated>) [classes/:?]
	at com.taurus.examinationassistant.service.PaperService.getPaperWrapperById(PaperService.java:326) [classes/:?]
	at com.taurus.examinationassistant.service.PaperService$$FastClassBySpringCGLIB$$fa4bbca.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) [spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) [spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) [spring-tx-5.3.31.jar:5.3.31]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) [spring-tx-5.3.31.jar:5.3.31]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) [spring-tx-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.31.jar:5.3.31]
	at com.taurus.examinationassistant.service.PaperService$$EnhancerBySpringCGLIB$$729dfa26.getPaperWrapperById(<generated>) [classes/:?]
	at com.taurus.examinationassistant.service.ExaminationService.getExaminationWrapperById(ExaminationService.java:435) [classes/:?]
	at com.taurus.examinationassistant.service.ExaminationService$$FastClassBySpringCGLIB$$d920f905.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) [spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) [spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) [spring-aop-5.3.31.jar:5.3.31]
	at com.taurus.examinationassistant.service.ExaminationService$$EnhancerBySpringCGLIB$$30f94eb7.getExaminationWrapperById(<generated>) [classes/:?]
	at com.taurus.examinationassistant.controller.ExaminationController.getExaminationWrapperSecure(ExaminationController.java:671) [classes/:?]
	at com.taurus.examinationassistant.controller.ExaminationController$$FastClassBySpringCGLIB$$f2506b03.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) [spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) [spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) [spring-aop-5.3.31.jar:5.3.31]
	at com.taurus.examinationassistant.controller.ExaminationController$$EnhancerBySpringCGLIB$$4d87d371.getExaminationWrapperSecure(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) [spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) [spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:497) [jakarta.servlet-api-4.0.4.jar:4.0.4]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:584) [jakarta.servlet-api-4.0.4.jar:4.0.4]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.websockets.jsr.JsrWebSocketFilter.doFilter(JsrWebSocketFilter.java:173) [undertow-websockets-jsr-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.taurus.examinationassistant.filter.ReplaceStreamFilter.doFilter(ReplaceStreamFilter.java:53) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.taurus.examinationassistant.filter.RejectRequest.doFilter(RejectRequest.java:163) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:64) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.taurus.examinationassistant.filter.SqlXssFilter.doFilter(SqlXssFilter.java:135) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.31.jar:5.3.31]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.31.jar:5.3.31]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SessionRestoringHandler.handleRequest(SessionRestoringHandler.java:119) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_281]
[15:14:32:091] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:14:32:199] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:14:32:288] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:14:32:390] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:14:32:507] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/qkk/qkkExamination/20240821/20240821100755d788.jpg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:14:32:632] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/qkk/qkkExamination/20240821/20240821100755d788.jpg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:14:33:728] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:14:50:311] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:14:50:401] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:14:50:511] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:14:50:613] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:14:50:738] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/qkk/qkkExamination/20240821/20240821100755d788.jpg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:14:50:870] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/qkk/qkkExamination/20240821/20240821100755d788.jpg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:14:53:020] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:14:53:406] [ERROR] - com.taurus.examinationassistant.service.QuestionRandomService.getPaperQuestionWrapperListByPaperQuestionRandom(QuestionRandomService.java:275) - 获取随机题对应的PaperQuestionWrapper列表失败
java.lang.IndexOutOfBoundsException: Index: 0, Size: 0
	at java.util.ArrayList.rangeCheck(ArrayList.java:659) ~[?:1.8.0_281]
	at java.util.ArrayList.get(ArrayList.java:435) ~[?:1.8.0_281]
	at com.taurus.examinationassistant.service.QuestionRandomService.getPaperQuestionWrapperListByPaperQuestionRandom(QuestionRandomService.java:262) [classes/:?]
	at com.taurus.examinationassistant.service.QuestionRandomService$$FastClassBySpringCGLIB$$c5e7570b.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) [spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) [spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) [spring-aop-5.3.31.jar:5.3.31]
	at com.taurus.examinationassistant.service.QuestionRandomService$$EnhancerBySpringCGLIB$$b4dc5c89.getPaperQuestionWrapperListByPaperQuestionRandom(<generated>) [classes/:?]
	at com.taurus.examinationassistant.service.PaperService.getPaperWrapperById(PaperService.java:326) [classes/:?]
	at com.taurus.examinationassistant.service.PaperService$$FastClassBySpringCGLIB$$fa4bbca.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) [spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) [spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) [spring-tx-5.3.31.jar:5.3.31]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) [spring-tx-5.3.31.jar:5.3.31]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) [spring-tx-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.31.jar:5.3.31]
	at com.taurus.examinationassistant.service.PaperService$$EnhancerBySpringCGLIB$$729dfa26.getPaperWrapperById(<generated>) [classes/:?]
	at com.taurus.examinationassistant.service.ExaminationService.getExaminationWrapperById(ExaminationService.java:435) [classes/:?]
	at com.taurus.examinationassistant.service.ExaminationService$$FastClassBySpringCGLIB$$d920f905.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) [spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) [spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) [spring-aop-5.3.31.jar:5.3.31]
	at com.taurus.examinationassistant.service.ExaminationService$$EnhancerBySpringCGLIB$$30f94eb7.getExaminationWrapperById(<generated>) [classes/:?]
	at com.taurus.examinationassistant.controller.ExaminationController.getExaminationWrapperSecure(ExaminationController.java:671) [classes/:?]
	at com.taurus.examinationassistant.controller.ExaminationController$$FastClassBySpringCGLIB$$f2506b03.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) [spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) [spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) [spring-aop-5.3.31.jar:5.3.31]
	at com.taurus.examinationassistant.controller.ExaminationController$$EnhancerBySpringCGLIB$$4d87d371.getExaminationWrapperSecure(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) [spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) [spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:497) [jakarta.servlet-api-4.0.4.jar:4.0.4]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:584) [jakarta.servlet-api-4.0.4.jar:4.0.4]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.websockets.jsr.JsrWebSocketFilter.doFilter(JsrWebSocketFilter.java:173) [undertow-websockets-jsr-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.taurus.examinationassistant.filter.ReplaceStreamFilter.doFilter(ReplaceStreamFilter.java:53) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.taurus.examinationassistant.filter.RejectRequest.doFilter(RejectRequest.java:163) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.taurus.examinationassistant.filter.LocationFilter.doFilter(LocationFilter.java:64) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.taurus.examinationassistant.filter.SqlXssFilter.doFilter(SqlXssFilter.java:135) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.31.jar:5.3.31]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.31.jar:5.3.31]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SessionRestoringHandler.handleRequest(SessionRestoringHandler.java:119) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_281]
[15:15:01:415] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:15:01:509] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:15:01:595] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:15:01:670] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:15:01:775] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/qkk/qkkExamination/20240821/20240821100755d788.jpg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:15:01:868] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/qkk/qkkExamination/20240821/20240821100755d788.jpg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:15:04:169] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/qkk/qkkExamination/20240821/20240821100755d788.jpg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:15:21:502] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:15:21:611] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:15:21:694] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:15:21:802] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:15:21:893] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/qkk/qkkExamination/20240821/20240821100755d788.jpg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:15:21:998] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/qkk/qkkExamination/20240821/20240821100755d788.jpg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:15:58:279] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:15:58:396] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:15:58:493] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:15:58:593] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:15:58:672] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:15:58:750] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:15:58:826] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/qkk/qkkExamination/20240821/20240821100755d788.jpg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:15:58:913] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=examination-etea&objectName=static/qkk/qkkExamination/20240821/20240821100755d788.jpg] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:16:01:495] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:17:05:478] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:17:08:336] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:24:53:518] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:24:53:698] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:24:53:867] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:24:53:987] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:24:54:127] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:24:54:240] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:24:54:386] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:24:54:504] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:24:54:640] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:24:54:835] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:24:54:984] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:24:55:129] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
[15:25:24:362] [ERROR] - com.taurus.utils.oss.OssUtil.callOssFeignClientByW1(OssUtil.java:70) - 微服务：taurus-oss 不可用,[503] during [GET] to [http://taurus-oss/ossObject/getAccessUrl?respositoryName=early-exam&objectName=null/null] [TaurusOssFeignClient#getAccessUrl(String,String)]: [Load balancer does not contain an instance for the service taurus-oss]
