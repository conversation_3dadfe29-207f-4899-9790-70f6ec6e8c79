[07:46:15:921] [INFO] - org.hibernate.validator.internal.util.Version.<clinit>(Version.java:21) - HV000001: Hibernate Validator 6.2.5.Final
[07:46:16:160] [INFO] - org.springframework.boot.logging.DeferredLog.logTo(DeferredLog.java:255) - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
[07:46:20:031] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[07:46:20:052] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[07:46:20:061] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[07:46:20:146] [DEBUG] - io.micrometer.core.util.internal.logging.InternalLoggerFactory.newDefaultFactory(InternalLoggerFactory.java:59) - Using SLF4J as the default logging framework
[07:46:20:325] [DEBUG] - io.undertow.server.session.InMemorySessionManager.registerSessionListener(InMemorySessionManager.java:268) - Registered session listener io.undertow.servlet.core.SessionListenerBridge@503f356d
[07:46:20:354] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[07:46:23:001] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.l.c.Question2JsonController:
	{POST [/question2Json/stream]}: streamWithStreamingResponseBody(HttpServletRequest,HttpServletResponse,MultipartFile)
	{GET [/question2Json/chat]}: stringFlux(String)
	{GET [/question2Json/testInvoke]}: testInvoke()
	{GET [/question2Json/streamtest], produces [text/event-stream]}: stream()
	{POST [/question2Json/generate]}: generate(Integer,Integer,MultipartFile)
[07:46:23:006] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.l.c.QuestionScoringController:
	{POST [/questionScoring/scoring]}: scoring(JSONObject)
[07:46:23:007] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.l.c.Text2QuestionsController:
	{POST [/text2Questions/generate]}: generate(Integer,Integer,Integer,String,MultipartFile)
[07:46:23:013] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
	{ [/error]}: error(HttpServletRequest)
[07:46:23:036] [DEBUG] - org.springframework.web.servlet.handler.AbstractDetectingUrlHandlerMapping.detectHandlers(AbstractDetectingUrlHandlerMapping.java:86) - 'beanNameHandlerMapping' {}
[07:46:23:068] [DEBUG] - org.springframework.web.servlet.handler.SimpleUrlHandlerMapping.logMappings(SimpleUrlHandlerMapping.java:177) - 'resourceHandlerMapping' {/webjars/**=ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]], /**=ResourceHttpRequestHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/], ServletContext [/]]}
[07:46:24:082] [DEBUG] - reactor.util.Loggers$Slf4JLogger.debug(Loggers.java:254) - Using Slf4j logging framework
[07:46:24:120] [DEBUG] - io.netty.util.internal.logging.InternalLoggerFactory.useSlf4JLoggerFactory(InternalLoggerFactory.java:63) - Using SLF4J as the default logging framework
[07:46:24:127] [DEBUG] - io.netty.util.internal.PlatformDependent0.explicitNoUnsafeCause0(PlatformDependent0.java:515) - -Dio.netty.noUnsafe: false
[07:46:24:128] [DEBUG] - io.netty.util.internal.PlatformDependent0.javaVersion0(PlatformDependent0.java:1026) - Java version: 8
[07:46:24:129] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:140) - sun.misc.Unsafe.theUnsafe: available
[07:46:24:129] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:164) - sun.misc.Unsafe.copyMemory: available
[07:46:24:130] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:196) - sun.misc.Unsafe.storeFence: available
[07:46:24:130] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:239) - java.nio.Buffer.address: available
[07:46:24:131] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:312) - direct buffer constructor: available
[07:46:24:132] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:403) - java.nio.Bits.unaligned: available, true
[07:46:24:132] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:478) - jdk.internal.misc.Unsafe.allocateUninitializedArray(int): unavailable prior to Java9
[07:46:24:132] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:501) - java.nio.DirectByteBuffer.<init>(long, {int,long}): available
[07:46:24:132] [DEBUG] - io.netty.util.internal.PlatformDependent.unsafeUnavailabilityCause0(PlatformDependent.java:1157) - sun.misc.Unsafe: available
[07:46:24:133] [DEBUG] - io.netty.util.internal.PlatformDependent.tmpdir0(PlatformDependent.java:1303) - -Dio.netty.tmpdir: /var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T (java.io.tmpdir)
[07:46:24:134] [DEBUG] - io.netty.util.internal.PlatformDependent.bitMode0(PlatformDependent.java:1382) - -Dio.netty.bitMode: 64 (sun.arch.data.model)
[07:46:24:137] [DEBUG] - io.netty.util.internal.PlatformDependent.isOsx0(PlatformDependent.java:1125) - Platform: MacOS
[07:46:24:139] [DEBUG] - io.netty.util.internal.PlatformDependent.<clinit>(PlatformDependent.java:176) - -Dio.netty.maxDirectMemory: 3817865216 bytes
[07:46:24:139] [DEBUG] - io.netty.util.internal.PlatformDependent.<clinit>(PlatformDependent.java:183) - -Dio.netty.uninitializedArrayAllocationThreshold: -1
[07:46:24:141] [DEBUG] - io.netty.util.internal.CleanerJava6.<clinit>(CleanerJava6.java:92) - java.nio.ByteBuffer.cleaner(): available
[07:46:24:141] [DEBUG] - io.netty.util.internal.PlatformDependent.<clinit>(PlatformDependent.java:203) - -Dio.netty.noPreferDirect: false
[07:46:24:147] [DEBUG] - reactor.util.Loggers$Slf4JLogger.debug(Loggers.java:259) - [http] resources will use the default LoopResources: DefaultLoopResources {prefix=reactor-http, daemon=true, selectCount=8, workerCount=8}
[07:46:24:148] [DEBUG] - reactor.util.Loggers$Slf4JLogger.debug(Loggers.java:259) - [http] resources will use the default ConnectionProvider: reactor.netty.resources.DefaultPooledConnectionProvider@4e65c83c
[07:46:25:102] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[07:46:25:161] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 49 ms to scan 1 urls, producing 3 keys and 6 values 
[07:46:25:177] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype com.alibaba.nacos.api.remote.request.Request -> com.alibaba.nacos.api.remote.request.ServerRequest
[07:46:25:204] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[07:46:25:239] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 32 ms to scan 1 urls, producing 4 keys and 9 values 
[07:46:25:240] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype com.alibaba.nacos.api.remote.request.Request -> com.alibaba.nacos.api.remote.request.ServerRequest
[07:46:25:243] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[07:46:25:259] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 13 ms to scan 1 urls, producing 3 keys and 10 values 
[07:46:25:263] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/dnsns.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jaccess.jar
file:/private/var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T/cp_er4b845hrjkcp0ufv1585czg5.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jfxrt.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunec.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/zipfs.jar
file:/Users/<USER>/Documents/git/taurus-cloud/taurus-llm/target/classes/
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/nashorn.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/cldrdata.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/localedata.jar
[07:46:25:287] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 24 ms to scan 12 urls, producing 0 keys and 0 values 
[07:46:25:291] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[07:46:25:302] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
[07:46:25:304] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[07:46:25:313] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
[07:46:25:316] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[07:46:25:330] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
[07:46:25:331] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype java.lang.Comparable -> java.lang.Enum
[07:46:25:331] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype java.io.Serializable -> java.lang.Enum
[07:46:25:333] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/dnsns.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jaccess.jar
file:/private/var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T/cp_er4b845hrjkcp0ufv1585czg5.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jfxrt.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunec.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/zipfs.jar
file:/Users/<USER>/Documents/git/taurus-cloud/taurus-llm/target/classes/
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/nashorn.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/cldrdata.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/localedata.jar
[07:46:25:345] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 12 ms to scan 12 urls, producing 0 keys and 0 values 
[07:46:26:201] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[07:46:26:217] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[07:46:26:229] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[07:46:26:241] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[07:46:26:259] [DEBUG] - org.xnio.XnioWorker$Builder.build(XnioWorker.java:1191) - Creating worker:null, pool size:64, max pool size:64, keep alive:60000, io threads:8, stack size:0
[07:46:26:270] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[07:46:26:281] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-2', selector sun.nio.ch.KQueueSelectorImpl@285adc44
[07:46:26:281] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-5', selector sun.nio.ch.KQueueSelectorImpl@3f1e8012
[07:46:26:281] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-3', selector sun.nio.ch.KQueueSelectorImpl@efbb2ee
[07:46:26:283] [DEBUG] - io.undertow.Undertow.start(Undertow.java:160) - Configuring listener with protocol HTTP for interface 0.0.0.0 and port 8089
[07:46:26:282] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-7', selector sun.nio.ch.KQueueSelectorImpl@7e2ad320
[07:46:26:281] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-4', selector sun.nio.ch.KQueueSelectorImpl@2fa9184
[07:46:26:282] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-6', selector sun.nio.ch.KQueueSelectorImpl@41824627
[07:46:26:281] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-1', selector sun.nio.ch.KQueueSelectorImpl@a784eb1
[07:46:26:282] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-8', selector sun.nio.ch.KQueueSelectorImpl@5c0d1735
[07:46:26:283] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 Accept', selector sun.nio.ch.KQueueSelectorImpl@7f96e6d
[07:46:26:369] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP taurus-llm 192.168.31.136:8089 register finished
[07:46:41:817] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /questionScoring/scoring, authentication required: false
[07:46:41:824] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@c17e68a for /questionScoring/scoring
[07:46:41:824] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /questionScoring/scoring
[07:47:57:819] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /questionScoring/scoring, authentication required: false
[07:47:57:825] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@c17e68a for /questionScoring/scoring
[07:47:57:826] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /questionScoring/scoring
[07:51:38:503] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /questionScoring/scoring, authentication required: false
[07:51:38:514] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@c17e68a for /questionScoring/scoring
[07:51:38:515] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /questionScoring/scoring
[07:58:05:755] [INFO] - io.undertow.Undertow.stop(Undertow.java:259) - stopping server: Undertow - 2.2.28.Final
[07:58:05:834] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Destroying Spring FrameworkServlet 'dispatcherServlet'
[07:58:05:962] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:94) - De-registering from Nacos Server now...
[07:58:05:988] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:114) - De-registration finished.
[07:58:08:885] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[07:58:08:896] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[07:58:08:898] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[07:58:09:027] [DEBUG] - io.undertow.server.session.InMemorySessionManager.registerSessionListener(InMemorySessionManager.java:268) - Registered session listener io.undertow.servlet.core.SessionListenerBridge@635ef10f
[07:58:09:033] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[07:58:09:519] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.l.c.Question2JsonController:
	{POST [/question2Json/stream]}: streamWithStreamingResponseBody(HttpServletRequest,HttpServletResponse,MultipartFile)
	{GET [/question2Json/chat]}: stringFlux(String)
	{GET [/question2Json/testInvoke]}: testInvoke()
	{GET [/question2Json/streamtest], produces [text/event-stream]}: stream()
	{POST [/question2Json/generate]}: generate(Integer,Integer,MultipartFile)
[07:58:09:530] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.l.c.QuestionScoringController:
	{POST [/questionScoring/scoring]}: scoring(JSONObject)
[07:58:09:537] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.l.c.Text2QuestionsController:
	{POST [/text2Questions/generate]}: generate(Integer,Integer,Integer,String,MultipartFile)
[07:58:09:543] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
	{ [/error]}: error(HttpServletRequest)
[07:58:09:562] [DEBUG] - org.springframework.web.servlet.handler.AbstractDetectingUrlHandlerMapping.detectHandlers(AbstractDetectingUrlHandlerMapping.java:86) - 'beanNameHandlerMapping' {}
[07:58:09:571] [DEBUG] - org.springframework.web.servlet.handler.SimpleUrlHandlerMapping.logMappings(SimpleUrlHandlerMapping.java:177) - 'resourceHandlerMapping' {/webjars/**=ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]], /**=ResourceHttpRequestHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/], ServletContext [/]]}
[07:58:09:781] [DEBUG] - reactor.util.Loggers$Slf4JLogger.debug(Loggers.java:259) - [http] resources will use the default LoopResources: DefaultLoopResources {prefix=reactor-http, daemon=true, selectCount=8, workerCount=8}
[07:58:09:783] [DEBUG] - reactor.util.Loggers$Slf4JLogger.debug(Loggers.java:259) - [http] resources will use the default ConnectionProvider: reactor.netty.resources.DefaultPooledConnectionProvider@1d769245
[07:58:10:444] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[07:58:10:457] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[07:58:10:463] [DEBUG] - org.xnio.XnioWorker$Builder.build(XnioWorker.java:1191) - Creating worker:null, pool size:64, max pool size:64, keep alive:60000, io threads:8, stack size:0
[07:58:10:480] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-2 I/O-6', selector sun.nio.ch.KQueueSelectorImpl@4c0d7ed7
[07:58:10:480] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-2 I/O-4', selector sun.nio.ch.KQueueSelectorImpl@448823d9
[07:58:10:482] [DEBUG] - io.undertow.Undertow.start(Undertow.java:160) - Configuring listener with protocol HTTP for interface 0.0.0.0 and port 8089
[07:58:10:481] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-2 I/O-7', selector sun.nio.ch.KQueueSelectorImpl@590edbb7
[07:58:10:481] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-2 I/O-8', selector sun.nio.ch.KQueueSelectorImpl@2e37aada
[07:58:10:481] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-2 Accept', selector sun.nio.ch.KQueueSelectorImpl@247a51b6
[07:58:10:479] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-2 I/O-2', selector sun.nio.ch.KQueueSelectorImpl@7788c9b6
[07:58:10:480] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-2 I/O-5', selector sun.nio.ch.KQueueSelectorImpl@6c56c8cc
[07:58:10:480] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-2 I/O-1', selector sun.nio.ch.KQueueSelectorImpl@772d14b2
[07:58:10:479] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-2 I/O-3', selector sun.nio.ch.KQueueSelectorImpl@229c17e5
[07:58:10:519] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP taurus-llm 192.168.31.136:8089 register finished
