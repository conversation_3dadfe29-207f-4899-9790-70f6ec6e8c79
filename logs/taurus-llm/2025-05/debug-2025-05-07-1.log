[15:13:52:154] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[15:13:52:185] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[15:13:52:195] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[15:13:52:296] [DEBUG] - io.micrometer.core.util.internal.logging.InternalLoggerFactory.newDefaultFactory(InternalLoggerFactory.java:59) - Using SLF4J as the default logging framework
[15:13:52:468] [DEBUG] - io.undertow.server.session.InMemorySessionManager.registerSessionListener(InMemorySessionManager.java:268) - Registered session listener io.undertow.servlet.core.SessionListenerBridge@5e477c09
[15:13:52:504] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[15:13:55:253] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.l.c.Question2JsonController:
	{POST [/question2Json/stream]}: streamWithStreamingResponseBody(HttpServletRequest,HttpServletResponse,MultipartFile)
	{GET [/question2Json/chat]}: stringFlux(String)
	{GET [/question2Json/testInvoke]}: testInvoke()
	{GET [/question2Json/streamtest], produces [text/event-stream]}: stream()
	{POST [/question2Json/generate]}: generate(Integer,Integer,MultipartFile)
[15:13:55:270] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.l.c.QuestionScoringController:
	{POST [/questionScoring/scoring]}: scoring(JSONObject)
[15:13:55:275] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.l.c.Text2QuestionsController:
	{POST [/text2Questions/generate]}: generate(Integer,Integer,Integer,String,MultipartFile)
[15:13:55:299] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
	{ [/error]}: error(HttpServletRequest)
[15:13:55:366] [DEBUG] - org.springframework.web.servlet.handler.AbstractDetectingUrlHandlerMapping.detectHandlers(AbstractDetectingUrlHandlerMapping.java:86) - 'beanNameHandlerMapping' {}
[15:13:55:406] [DEBUG] - org.springframework.web.servlet.handler.SimpleUrlHandlerMapping.logMappings(SimpleUrlHandlerMapping.java:177) - 'resourceHandlerMapping' {/webjars/**=ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]], /**=ResourceHttpRequestHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/], ServletContext [/]]}
[15:13:56:366] [DEBUG] - reactor.util.Loggers$Slf4JLogger.debug(Loggers.java:254) - Using Slf4j logging framework
[15:13:56:407] [DEBUG] - io.netty.util.internal.logging.InternalLoggerFactory.useSlf4JLoggerFactory(InternalLoggerFactory.java:63) - Using SLF4J as the default logging framework
[15:13:56:418] [DEBUG] - io.netty.util.internal.PlatformDependent0.explicitNoUnsafeCause0(PlatformDependent0.java:515) - -Dio.netty.noUnsafe: false
[15:13:56:419] [DEBUG] - io.netty.util.internal.PlatformDependent0.javaVersion0(PlatformDependent0.java:1026) - Java version: 8
[15:13:56:420] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:140) - sun.misc.Unsafe.theUnsafe: available
[15:13:56:422] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:164) - sun.misc.Unsafe.copyMemory: available
[15:13:56:422] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:196) - sun.misc.Unsafe.storeFence: available
[15:13:56:423] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:239) - java.nio.Buffer.address: available
[15:13:56:424] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:312) - direct buffer constructor: available
[15:13:56:425] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:403) - java.nio.Bits.unaligned: available, true
[15:13:56:425] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:478) - jdk.internal.misc.Unsafe.allocateUninitializedArray(int): unavailable prior to Java9
[15:13:56:425] [DEBUG] - io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:501) - java.nio.DirectByteBuffer.<init>(long, {int,long}): available
[15:13:56:426] [DEBUG] - io.netty.util.internal.PlatformDependent.unsafeUnavailabilityCause0(PlatformDependent.java:1157) - sun.misc.Unsafe: available
[15:13:56:428] [DEBUG] - io.netty.util.internal.PlatformDependent.tmpdir0(PlatformDependent.java:1303) - -Dio.netty.tmpdir: /var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T (java.io.tmpdir)
[15:13:56:428] [DEBUG] - io.netty.util.internal.PlatformDependent.bitMode0(PlatformDependent.java:1382) - -Dio.netty.bitMode: 64 (sun.arch.data.model)
[15:13:56:431] [DEBUG] - io.netty.util.internal.PlatformDependent.isOsx0(PlatformDependent.java:1125) - Platform: MacOS
[15:13:56:432] [DEBUG] - io.netty.util.internal.PlatformDependent.<clinit>(PlatformDependent.java:176) - -Dio.netty.maxDirectMemory: 3817865216 bytes
[15:13:56:432] [DEBUG] - io.netty.util.internal.PlatformDependent.<clinit>(PlatformDependent.java:183) - -Dio.netty.uninitializedArrayAllocationThreshold: -1
[15:13:56:434] [DEBUG] - io.netty.util.internal.CleanerJava6.<clinit>(CleanerJava6.java:92) - java.nio.ByteBuffer.cleaner(): available
[15:13:56:434] [DEBUG] - io.netty.util.internal.PlatformDependent.<clinit>(PlatformDependent.java:203) - -Dio.netty.noPreferDirect: false
[15:13:56:440] [DEBUG] - reactor.util.Loggers$Slf4JLogger.debug(Loggers.java:259) - [http] resources will use the default LoopResources: DefaultLoopResources {prefix=reactor-http, daemon=true, selectCount=8, workerCount=8}
[15:13:56:441] [DEBUG] - reactor.util.Loggers$Slf4JLogger.debug(Loggers.java:259) - [http] resources will use the default ConnectionProvider: reactor.netty.resources.DefaultPooledConnectionProvider@6ebfc7d
[15:13:57:276] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[15:13:57:333] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 49 ms to scan 1 urls, producing 3 keys and 6 values 
[15:13:57:344] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype com.alibaba.nacos.api.remote.request.Request -> com.alibaba.nacos.api.remote.request.ServerRequest
[15:13:57:359] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[15:13:57:389] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 27 ms to scan 1 urls, producing 4 keys and 9 values 
[15:13:57:390] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype com.alibaba.nacos.api.remote.request.Request -> com.alibaba.nacos.api.remote.request.ServerRequest
[15:13:57:395] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[15:13:57:408] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 
[15:13:57:411] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/dnsns.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jaccess.jar
file:/private/var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T/cp_er4b845hrjkcp0ufv1585czg5.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jfxrt.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunec.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/zipfs.jar
file:/Users/<USER>/Documents/git/taurus-cloud/taurus-llm/target/classes/
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/nashorn.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/cldrdata.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/localedata.jar
[15:13:57:429] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 17 ms to scan 12 urls, producing 0 keys and 0 values 
[15:13:57:433] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[15:13:57:441] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
[15:13:57:444] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[15:13:57:453] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
[15:13:57:456] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[15:13:57:468] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
[15:13:57:468] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype java.lang.Comparable -> java.lang.Enum
[15:13:57:468] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype java.io.Serializable -> java.lang.Enum
[15:13:57:471] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/dnsns.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jaccess.jar
file:/private/var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T/cp_er4b845hrjkcp0ufv1585czg5.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jfxrt.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunec.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/zipfs.jar
file:/Users/<USER>/Documents/git/taurus-cloud/taurus-llm/target/classes/
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/nashorn.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/cldrdata.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/localedata.jar
[15:13:57:486] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 14 ms to scan 12 urls, producing 0 keys and 0 values 
[15:13:58:274] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[15:13:58:291] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[15:13:58:306] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[15:13:58:320] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[15:13:58:343] [DEBUG] - org.xnio.XnioWorker$Builder.build(XnioWorker.java:1191) - Creating worker:null, pool size:64, max pool size:64, keep alive:60000, io threads:8, stack size:0
[15:13:58:356] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[15:13:58:366] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-2', selector sun.nio.ch.KQueueSelectorImpl@cee977e
[15:13:58:367] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-8', selector sun.nio.ch.KQueueSelectorImpl@7e1f9f47
[15:13:58:367] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-7', selector sun.nio.ch.KQueueSelectorImpl@132f47ac
[15:13:58:366] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-5', selector sun.nio.ch.KQueueSelectorImpl@57383c61
[15:13:58:367] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 Accept', selector sun.nio.ch.KQueueSelectorImpl@31aeeced
[15:13:58:368] [DEBUG] - io.undertow.Undertow.start(Undertow.java:160) - Configuring listener with protocol HTTP for interface 0.0.0.0 and port 8089
[15:13:58:366] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-6', selector sun.nio.ch.KQueueSelectorImpl@6204a820
[15:13:58:366] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-1', selector sun.nio.ch.KQueueSelectorImpl@71bee3d3
[15:13:58:366] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-4', selector sun.nio.ch.KQueueSelectorImpl@3072517a
[15:13:58:367] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-3', selector sun.nio.ch.KQueueSelectorImpl@7349c0c0
[15:13:58:447] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP taurus-llm 192.168.8.151:8089 register finished
[15:19:24:159] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /question2Json/stream, authentication required: false
[15:19:24:168] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@3a8746c5 for /question2Json/stream
[15:19:24:170] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /question2Json/stream
[15:19:25:679] [INFO] - com.taurus.utils.POIUtils.splitContent(POIUtils.java:148) - 分隔后的：(   ×   )研磨时，研具的材料应比工件的材料硬度稍高，这样可使研磨剂的微小磨粒嵌在研具上形成无数刀刃。                           
(  ×    )金属由固态转变为液态时的温度称为沸点，从液态变为固态的过程称为冷凝。     
(   ×   )根据生铁中的含碳量可分为亚共晶生铁，共晶生铁，过共晶生铁。               
(  √  )汽包水清洗装置与分离装置安装不正确或检修质量不良，将使蒸汽品质不合格。   
(    ×  )锅炉范围内管道对接焊缝中心距支架边缘不得小于50mm。     
(   √  )经长期运行，炉管壁厚校核计算达下限时，应更换新管。     
(  ×    )汽包膨胀指示器只是在每次大修检查是否指示零位。   
(   ×   )吊物用的三角架，每根支脚与地面水平的夹角不应小于50°。         
(   √  )气割可用来切割碳钢、合金钢、铸铁等部分金属。                
(  ×    )手工电弧焊接，电弧电压是由焊接电流大小来决定的，焊接电流大，电弧电压高；焊接电流小，电弧电压低。 
[15:19:25:680] [INFO] - com.taurus.utils.POIUtils.splitContent(POIUtils.java:148) - 分隔后的：                        
(   ×   )气焊与电焊比较，气焊时的火焰温度比电焊时电弧温度低，所以气焊接头不易过热。                    
(   √  )气焊在焊接时，保护气氛没有电弧充分，合金元素变为蒸汽而跑掉的较多。       
(  ×    )在焊缝缺陷中，气孔比夹渣的危害性更大些，因为它以空洞的形式出现，削弱了焊缝的截面积，使应力集中，直接影响整个焊缝的强度。              
(   √  )发电机的有功负荷是指把电能转换成其他形式的能量时，在用电设备中消耗的有功功率。                            
(  ×    )气体绝对压力、表压力和真空是气体的三个状态参数。     
(  ×    )现代大型锅炉，大多采用悬吊式，均无膨胀中心死点。    
(×     )煤中的水分可以靠自然干燥除去。           
(   ×   )铰孔是用铰刀对已经粗加工过的孔进行精加工，提高孔和轴的配合精度。         
(   ×   )屏式过热器要求内部蒸汽的重量流速，小于对流过热器的内部
[15:19:25:681] [INFO] - com.taurus.utils.POIUtils.splitContent(POIUtils.java:128) - 最后一个分隔：蒸汽重量流速。     
20.(  √   )安全阀、逆止阀及快速关闭门等主要起某种保护作用。  

[15:19:28:043] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 1, type: result, data: {"output":{"choices":[{"message":{"content":"```","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1185,"output_tokens":1,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:28:089] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 2, type: result, data: {"output":{"choices":[{"message":{"content":"json","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1186,"output_tokens":2,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:28:090] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 3, type: result, data: {"output":{"choices":[{"message":{"content":"\n","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1187,"output_tokens":3,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:28:236] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 4, type: result, data: {"output":{"choices":[{"message":{"content":"[\n  {\n   ","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1191,"output_tokens":7,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:28:464] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 5, type: result, data: {"output":{"choices":[{"message":{"content":" \"mainContent\":","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1195,"output_tokens":11,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:28:553] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 6, type: result, data: {"output":{"choices":[{"message":{"content":" \"蒸汽重量流","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1199,"output_tokens":15,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:28:740] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 7, type: result, data: {"output":{"choices":[{"message":{"content":"速。\",\n    \"","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1203,"output_tokens":19,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:28:890] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 8, type: result, data: {"output":{"choices":[{"message":{"content":"type\": \"COM","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1207,"output_tokens":23,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:29:089] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 9, type: result, data: {"output":{"choices":[{"message":{"content":"\",\n    \"options","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1211,"output_tokens":27,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:29:210] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 10, type: result, data: {"output":{"choices":[{"message":{"content":"\": [\n      {\n","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1215,"output_tokens":31,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:29:406] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 11, type: result, data: {"output":{"choices":[{"message":{"content":"        \"index\":","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1219,"output_tokens":35,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:29:512] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 12, type: result, data: {"output":{"choices":[{"message":{"content":" 0,\n       ","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1223,"output_tokens":39,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:29:695] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 13, type: result, data: {"output":{"choices":[{"message":{"content":" \"option\": \"\"\n","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1227,"output_tokens":43,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:29:880] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 14, type: result, data: {"output":{"choices":[{"message":{"content":"      }\n    ],\n","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1231,"output_tokens":47,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:30:330] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 15, type: result, data: {"output":{"choices":[{"message":{"content":"    \"tipContent","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1235,"output_tokens":51,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:30:456] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 16, type: result, data: {"output":{"choices":[{"message":{"content":"\": \"\"\n  },\n","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1239,"output_tokens":55,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:30:776] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 17, type: result, data: {"output":{"choices":[{"message":{"content":"  {\n    \"","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1243,"output_tokens":59,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:31:015] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 18, type: result, data: {"output":{"choices":[{"message":{"content":"mainContent\": \"","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1247,"output_tokens":63,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:31:137] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 19, type: result, data: {"output":{"choices":[{"message":{"content":"安全阀、逆","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1251,"output_tokens":67,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:31:277] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 20, type: result, data: {"output":{"choices":[{"message":{"content":"止阀及快速","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1255,"output_tokens":71,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:32:120] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 21, type: result, data: {"output":{"choices":[{"message":{"content":"关闭门等主要","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1259,"output_tokens":75,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:32:327] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 22, type: result, data: {"output":{"choices":[{"message":{"content":"起某种保护作用","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1263,"output_tokens":79,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:32:428] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 23, type: result, data: {"output":{"choices":[{"message":{"content":"。\",\n    \"type","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1267,"output_tokens":83,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:32:608] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 24, type: result, data: {"output":{"choices":[{"message":{"content":"\": \"ADJ","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1271,"output_tokens":87,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:32:803] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 25, type: result, data: {"output":{"choices":[{"message":{"content":"\",\n    \"options","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1275,"output_tokens":91,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:32:971] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 26, type: result, data: {"output":{"choices":[{"message":{"content":"\": [\n      {\n","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1279,"output_tokens":95,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:33:204] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 27, type: result, data: {"output":{"choices":[{"message":{"content":"        \"text\":","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1283,"output_tokens":99,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:33:287] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 28, type: result, data: {"output":{"choices":[{"message":{"content":" \"right\",\n       ","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1287,"output_tokens":103,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:33:467] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 29, type: result, data: {"output":{"choices":[{"message":{"content":" \"isSelected\": true","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1291,"output_tokens":107,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:33:597] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 30, type: result, data: {"output":{"choices":[{"message":{"content":"\n      },\n     ","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1295,"output_tokens":111,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:33:786] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 31, type: result, data: {"output":{"choices":[{"message":{"content":" {\n        \"text","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1299,"output_tokens":115,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:33:915] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 32, type: result, data: {"output":{"choices":[{"message":{"content":"\": \"wrong\",\n","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1303,"output_tokens":119,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:34:110] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 33, type: result, data: {"output":{"choices":[{"message":{"content":"        \"isSelected\":","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1307,"output_tokens":123,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:34:290] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 34, type: result, data: {"output":{"choices":[{"message":{"content":" false\n      }\n","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1311,"output_tokens":127,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:34:451] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 35, type: result, data: {"output":{"choices":[{"message":{"content":"    ],\n    \"","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1315,"output_tokens":131,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:34:564] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 36, type: result, data: {"output":{"choices":[{"message":{"content":"tipContent\": \"\"\n","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1319,"output_tokens":135,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:34:746] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 37, type: result, data: {"output":{"choices":[{"message":{"content":"  }\n]\n```","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1323,"output_tokens":139,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:34:765] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 38, type: result, data: {"output":{"choices":[{"message":{"content":"","role":"assistant"},"finish_reason":"null"}]},"usage":{"total_tokens":1323,"output_tokens":139,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:19:34:831] [DEBUG] - com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient.handleSSEEvent(OkHttpHttpClient.java:233) - Event: id 39, type: result, data: {"output":{"choices":[{"message":{"content":"","role":"assistant"},"finish_reason":"stop"}]},"usage":{"total_tokens":1323,"output_tokens":139,"input_tokens":1184,"prompt_tokens_details":{"cached_tokens":0}},"request_id":"b2f75de1-aa84-9712-a04d-d77157f32852"}
[15:20:35:348] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /192.168.8.151:52499
[15:28:57:206] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /question2Json/stream, authentication required: false
[15:28:57:219] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@3a8746c5 for /question2Json/stream
[15:28:57:220] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /question2Json/stream
[15:28:57:502] [INFO] - com.taurus.utils.POIUtils.splitContent(POIUtils.java:148) - 分隔后的：(   ×   )研磨时，研具的材料应比工件的材料硬度稍高，这样可使研磨剂的微小磨粒嵌在研具上形成无数刀刃。                           
(  ×    )金属由固态转变为液态时的温度称为沸点，从液态变为固态的过程称为冷凝。     
(   ×   )根据生铁中的含碳量可分为亚共晶生铁，共晶生铁，过共晶生铁。               
(  √  )汽包水清洗装置与分离装置安装不正确或检修质量不良，将使蒸汽品质不合格。   
(    ×  )锅炉范围内管道对接焊缝中心距支架边缘不得小于50mm。     
(   √  )经长期运行，炉管壁厚校核计算达下限时，应更换新管。     
(  ×    )汽包膨胀指示器只是在每次大修检查是否指示零位。   
(   ×   )吊物用的三角架，每根支脚与地面水平的夹角不应小于50°。         
(   √  )气割可用来切割碳钢、合金钢、铸铁等部分金属。                
(  ×    )手工电弧焊接，电弧电压是由焊接电流大小来决定的，焊接电流大，电弧电压高；焊接电流小，电弧电压低。 
[15:28:57:504] [INFO] - com.taurus.utils.POIUtils.splitContent(POIUtils.java:148) - 分隔后的：                        
(   ×   )气焊与电焊比较，气焊时的火焰温度比电焊时电弧温度低，所以气焊接头不易过热。                    
(   √  )气焊在焊接时，保护气氛没有电弧充分，合金元素变为蒸汽而跑掉的较多。       
(  ×    )在焊缝缺陷中，气孔比夹渣的危害性更大些，因为它以空洞的形式出现，削弱了焊缝的截面积，使应力集中，直接影响整个焊缝的强度。              
(   √  )发电机的有功负荷是指把电能转换成其他形式的能量时，在用电设备中消耗的有功功率。                            
(  ×    )气体绝对压力、表压力和真空是气体的三个状态参数。     
(  ×    )现代大型锅炉，大多采用悬吊式，均无膨胀中心死点。    
(×     )煤中的水分可以靠自然干燥除去。           
(   ×   )铰孔是用铰刀对已经粗加工过的孔进行精加工，提高孔和轴的配合精度。         
(   ×   )屏式过热器要求内部蒸汽的重量流速，小于对流过热器的内部
[15:28:57:504] [INFO] - com.taurus.utils.POIUtils.splitContent(POIUtils.java:128) - 最后一个分隔：蒸汽重量流速。     
20.(  √   )安全阀、逆止阀及快速关闭门等主要起某种保护作用。  

[15:29:57:993] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /192.168.8.151:54973
[15:31:11:872] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /question2Json/stream, authentication required: false
[15:31:11:880] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@3a8746c5 for /question2Json/stream
[15:31:11:880] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /question2Json/stream
[15:31:12:108] [INFO] - com.taurus.utils.POIUtils.splitContent(POIUtils.java:148) - 分隔后的：(   ×   )研磨时，研具的材料应比工件的材料硬度稍高，这样可使研磨剂的微小磨粒嵌在研具上形成无数刀刃。                           
(  ×    )金属由固态转变为液态时的温度称为沸点，从液态变为固态的过程称为冷凝。     
(   ×   )根据生铁中的含碳量可分为亚共晶生铁，共晶生铁，过共晶生铁。               
(  √  )汽包水清洗装置与分离装置安装不正确或检修质量不良，将使蒸汽品质不合格。   
(    ×  )锅炉范围内管道对接焊缝中心距支架边缘不得小于50mm。     
(   √  )经长期运行，炉管壁厚校核计算达下限时，应更换新管。     
(  ×    )汽包膨胀指示器只是在每次大修检查是否指示零位。   
(   ×   )吊物用的三角架，每根支脚与地面水平的夹角不应小于50°。         
(   √  )气割可用来切割碳钢、合金钢、铸铁等部分金属。                
(  ×    )手工电弧焊接，电弧电压是由焊接电流大小来决定的，焊接电流大，电弧电压高；焊接电流小，电弧电压低。 
[15:31:12:110] [INFO] - com.taurus.utils.POIUtils.splitContent(POIUtils.java:148) - 分隔后的：                        
(   ×   )气焊与电焊比较，气焊时的火焰温度比电焊时电弧温度低，所以气焊接头不易过热。                    
(   √  )气焊在焊接时，保护气氛没有电弧充分，合金元素变为蒸汽而跑掉的较多。       
(  ×    )在焊缝缺陷中，气孔比夹渣的危害性更大些，因为它以空洞的形式出现，削弱了焊缝的截面积，使应力集中，直接影响整个焊缝的强度。              
(   √  )发电机的有功负荷是指把电能转换成其他形式的能量时，在用电设备中消耗的有功功率。                            
(  ×    )气体绝对压力、表压力和真空是气体的三个状态参数。     
(  ×    )现代大型锅炉，大多采用悬吊式，均无膨胀中心死点。    
(×     )煤中的水分可以靠自然干燥除去。           
(   ×   )铰孔是用铰刀对已经粗加工过的孔进行精加工，提高孔和轴的配合精度。         
(   ×   )屏式过热器要求内部蒸汽的重量流速，小于对流过热器的内部
[15:31:12:110] [INFO] - com.taurus.utils.POIUtils.splitContent(POIUtils.java:128) - 最后一个分隔：蒸汽重量流速。     
20.(  √   )安全阀、逆止阀及快速关闭门等主要起某种保护作用。  

[15:32:12:466] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /192.168.8.151:55747
[15:38:25:540] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /question2Json/stream, authentication required: false
[15:38:25:551] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@3a8746c5 for /question2Json/stream
[15:38:25:551] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /question2Json/stream
[15:38:25:793] [INFO] - com.taurus.utils.POIUtils.splitContent(POIUtils.java:148) - 分隔后的：(   ×   )研磨时，研具的材料应比工件的材料硬度稍高，这样可使研磨剂的微小磨粒嵌在研具上形成无数刀刃。                           
(  ×    )金属由固态转变为液态时的温度称为沸点，从液态变为固态的过程称为冷凝。     
(   ×   )根据生铁中的含碳量可分为亚共晶生铁，共晶生铁，过共晶生铁。               
(  √  )汽包水清洗装置与分离装置安装不正确或检修质量不良，将使蒸汽品质不合格。   
(    ×  )锅炉范围内管道对接焊缝中心距支架边缘不得小于50mm。     
(   √  )经长期运行，炉管壁厚校核计算达下限时，应更换新管。     
(  ×    )汽包膨胀指示器只是在每次大修检查是否指示零位。   
(   ×   )吊物用的三角架，每根支脚与地面水平的夹角不应小于50°。         
(   √  )气割可用来切割碳钢、合金钢、铸铁等部分金属。                
(  ×    )手工电弧焊接，电弧电压是由焊接电流大小来决定的，焊接电流大，电弧电压高；焊接电流小，电弧电压低。 
[15:38:25:797] [INFO] - com.taurus.utils.POIUtils.splitContent(POIUtils.java:148) - 分隔后的：                        
(   ×   )气焊与电焊比较，气焊时的火焰温度比电焊时电弧温度低，所以气焊接头不易过热。                    
(   √  )气焊在焊接时，保护气氛没有电弧充分，合金元素变为蒸汽而跑掉的较多。       
(  ×    )在焊缝缺陷中，气孔比夹渣的危害性更大些，因为它以空洞的形式出现，削弱了焊缝的截面积，使应力集中，直接影响整个焊缝的强度。              
(   √  )发电机的有功负荷是指把电能转换成其他形式的能量时，在用电设备中消耗的有功功率。                            
(  ×    )气体绝对压力、表压力和真空是气体的三个状态参数。     
(  ×    )现代大型锅炉，大多采用悬吊式，均无膨胀中心死点。    
(×     )煤中的水分可以靠自然干燥除去。           
(   ×   )铰孔是用铰刀对已经粗加工过的孔进行精加工，提高孔和轴的配合精度。         
(   ×   )屏式过热器要求内部蒸汽的重量流速，小于对流过热器的内部
[15:38:25:797] [INFO] - com.taurus.utils.POIUtils.splitContent(POIUtils.java:128) - 最后一个分隔：蒸汽重量流速。     
20.(  √   )安全阀、逆止阀及快速关闭门等主要起某种保护作用。  

[15:39:26:458] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /192.168.8.151:58091
[15:49:32:939] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /question2Json/stream, authentication required: false
[15:49:32:948] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@3a8746c5 for /question2Json/stream
[15:49:32:948] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /question2Json/stream
[15:49:33:278] [INFO] - com.taurus.utils.POIUtils.splitContent(POIUtils.java:148) - 分隔后的：(   ×   )研磨时，研具的材料应比工件的材料硬度稍高，这样可使研磨剂的微小磨粒嵌在研具上形成无数刀刃。                           
(  ×    )金属由固态转变为液态时的温度称为沸点，从液态变为固态的过程称为冷凝。     
(   ×   )根据生铁中的含碳量可分为亚共晶生铁，共晶生铁，过共晶生铁。               
(  √  )汽包水清洗装置与分离装置安装不正确或检修质量不良，将使蒸汽品质不合格。   
(    ×  )锅炉范围内管道对接焊缝中心距支架边缘不得小于50mm。     
(   √  )经长期运行，炉管壁厚校核计算达下限时，应更换新管。     
(  ×    )汽包膨胀指示器只是在每次大修检查是否指示零位。   
(   ×   )吊物用的三角架，每根支脚与地面水平的夹角不应小于50°。         
(   √  )气割可用来切割碳钢、合金钢、铸铁等部分金属。                
(  ×    )手工电弧焊接，电弧电压是由焊接电流大小来决定的，焊接电流大，电弧电压高；焊接电流小，电弧电压低。 
[15:49:33:282] [INFO] - com.taurus.utils.POIUtils.splitContent(POIUtils.java:148) - 分隔后的：                        
(   ×   )气焊与电焊比较，气焊时的火焰温度比电焊时电弧温度低，所以气焊接头不易过热。                    
(   √  )气焊在焊接时，保护气氛没有电弧充分，合金元素变为蒸汽而跑掉的较多。       
(  ×    )在焊缝缺陷中，气孔比夹渣的危害性更大些，因为它以空洞的形式出现，削弱了焊缝的截面积，使应力集中，直接影响整个焊缝的强度。              
(   √  )发电机的有功负荷是指把电能转换成其他形式的能量时，在用电设备中消耗的有功功率。                            
(  ×    )气体绝对压力、表压力和真空是气体的三个状态参数。     
(  ×    )现代大型锅炉，大多采用悬吊式，均无膨胀中心死点。    
(×     )煤中的水分可以靠自然干燥除去。           
(   ×   )铰孔是用铰刀对已经粗加工过的孔进行精加工，提高孔和轴的配合精度。         
(   ×   )屏式过热器要求内部蒸汽的重量流速，小于对流过热器的内部
[15:49:33:282] [INFO] - com.taurus.utils.POIUtils.splitContent(POIUtils.java:128) - 最后一个分隔：蒸汽重量流速。     
20.(  √   )安全阀、逆止阀及快速关闭门等主要起某种保护作用。  

[15:50:33:646] [DEBUG] - io.undertow.server.protocol.ParseTimeoutUpdater.run(ParseTimeoutUpdater.java:151) - Timing out idle connection from /192.168.8.151:61568
