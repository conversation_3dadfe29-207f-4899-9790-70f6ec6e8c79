[07:32:43:774] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[07:32:43:812] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@4fd98416 for /ossObject/getAccessUrl
[07:32:43:813] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[07:32:44:424] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[07:32:44:623] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[07:32:44:624] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@4fd98416 for /ossObject/getAccessUrl
[07:32:44:625] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[07:32:44:734] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[07:32:44:734] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@4fd98416 for /ossObject/getAccessUrl
[07:32:44:734] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[07:32:44:789] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[07:32:44:789] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@4fd98416 for /ossObject/getAccessUrl
[07:32:44:789] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[07:32:44:818] [DEBUG] - io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:160) - Error reading request
java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method) ~[?:1.8.0_281]
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.read(IOUtil.java:192) ~[?:1.8.0_281]
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378) ~[?:1.8.0_281]
	at org.xnio.nio.NioSocketConduit.read(NioSocketConduit.java:289) ~[xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ConduitStreamSourceChannel.read(ConduitStreamSourceChannel.java:127) ~[xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:158) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:136) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:59) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.xnio.ChannelListeners.invokeChannelListener(ChannelListeners.java:92) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ReadReadyHandler$ChannelListenerHandler.readReady(ReadReadyHandler.java:66) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.NioSocketConduit.handleReady(NioSocketConduit.java:89) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.WorkerThread.run(WorkerThread.java:591) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
[07:32:44:853] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[07:32:44:854] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@4fd98416 for /ossObject/getAccessUrl
[07:32:44:854] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[07:32:44:878] [DEBUG] - io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:160) - Error reading request
java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method) ~[?:1.8.0_281]
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.read(IOUtil.java:192) ~[?:1.8.0_281]
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378) ~[?:1.8.0_281]
	at org.xnio.nio.NioSocketConduit.read(NioSocketConduit.java:289) ~[xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ConduitStreamSourceChannel.read(ConduitStreamSourceChannel.java:127) ~[xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:158) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:136) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:59) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.xnio.ChannelListeners.invokeChannelListener(ChannelListeners.java:92) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ReadReadyHandler$ChannelListenerHandler.readReady(ReadReadyHandler.java:66) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.NioSocketConduit.handleReady(NioSocketConduit.java:89) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.WorkerThread.run(WorkerThread.java:591) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
[07:32:44:937] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[07:32:44:937] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@4fd98416 for /ossObject/getAccessUrl
[07:32:44:937] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[07:32:45:006] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[07:32:45:006] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@4fd98416 for /ossObject/getAccessUrl
[07:32:45:006] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[07:32:45:084] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[07:32:45:084] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@4fd98416 for /ossObject/getAccessUrl
[07:32:45:084] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[07:32:45:146] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[07:32:45:146] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@4fd98416 for /ossObject/getAccessUrl
[07:32:45:146] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[07:32:45:274] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[07:32:45:275] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@4fd98416 for /ossObject/getAccessUrl
[07:32:45:275] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[07:32:45:348] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[07:32:45:349] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@4fd98416 for /ossObject/getAccessUrl
[07:32:45:349] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[07:32:45:432] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[07:32:45:432] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@4fd98416 for /ossObject/getAccessUrl
[07:32:45:432] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[07:32:47:596] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[07:32:47:596] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@4fd98416 for /ossObject/getAccessUrl
[07:32:47:597] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[07:32:47:633] [DEBUG] - io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:160) - Error reading request
java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method) ~[?:1.8.0_281]
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.read(IOUtil.java:192) ~[?:1.8.0_281]
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378) ~[?:1.8.0_281]
	at org.xnio.nio.NioSocketConduit.read(NioSocketConduit.java:289) ~[xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ConduitStreamSourceChannel.read(ConduitStreamSourceChannel.java:127) ~[xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:158) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:136) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:59) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.xnio.ChannelListeners.invokeChannelListener(ChannelListeners.java:92) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ReadReadyHandler$ChannelListenerHandler.readReady(ReadReadyHandler.java:66) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.NioSocketConduit.handleReady(NioSocketConduit.java:89) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.WorkerThread.run(WorkerThread.java:591) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
[07:32:53:504] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[07:32:53:506] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@4fd98416 for /ossObject/getAccessUrl
[07:32:53:506] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[07:32:53:540] [DEBUG] - io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:160) - Error reading request
java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method) ~[?:1.8.0_281]
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.read(IOUtil.java:192) ~[?:1.8.0_281]
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378) ~[?:1.8.0_281]
	at org.xnio.nio.NioSocketConduit.read(NioSocketConduit.java:289) ~[xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ConduitStreamSourceChannel.read(ConduitStreamSourceChannel.java:127) ~[xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:158) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:136) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:59) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.xnio.ChannelListeners.invokeChannelListener(ChannelListeners.java:92) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ReadReadyHandler$ChannelListenerHandler.readReady(ReadReadyHandler.java:66) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.NioSocketConduit.handleReady(NioSocketConduit.java:89) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.WorkerThread.run(WorkerThread.java:591) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
[07:32:53:586] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[07:32:53:589] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@4fd98416 for /ossObject/getAccessUrl
[07:32:53:590] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[07:32:53:671] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[07:32:53:672] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@4fd98416 for /ossObject/getAccessUrl
[07:32:53:672] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[07:32:53:737] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[07:32:53:738] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@4fd98416 for /ossObject/getAccessUrl
[07:32:53:738] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[07:32:53:809] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[07:32:53:809] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@4fd98416 for /ossObject/getAccessUrl
[07:32:53:809] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[07:32:53:881] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[07:32:53:881] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@4fd98416 for /ossObject/getAccessUrl
[07:32:53:881] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[07:32:53:943] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[07:32:53:943] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@4fd98416 for /ossObject/getAccessUrl
[07:32:53:943] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[07:32:54:011] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[07:32:54:012] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@4fd98416 for /ossObject/getAccessUrl
[07:32:54:012] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[07:32:54:072] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[07:32:54:073] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@4fd98416 for /ossObject/getAccessUrl
[07:32:54:073] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[07:32:54:141] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[07:32:54:142] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@4fd98416 for /ossObject/getAccessUrl
[07:32:54:142] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[07:32:54:229] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[07:32:54:229] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@4fd98416 for /ossObject/getAccessUrl
[07:32:54:229] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[07:32:54:292] [DEBUG] - io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:160) - Error reading request
java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method) ~[?:1.8.0_281]
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.read(IOUtil.java:192) ~[?:1.8.0_281]
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378) ~[?:1.8.0_281]
	at org.xnio.nio.NioSocketConduit.read(NioSocketConduit.java:289) ~[xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ConduitStreamSourceChannel.read(ConduitStreamSourceChannel.java:127) ~[xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:158) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:136) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:59) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.xnio.ChannelListeners.invokeChannelListener(ChannelListeners.java:92) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ReadReadyHandler$ChannelListenerHandler.readReady(ReadReadyHandler.java:66) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.NioSocketConduit.handleReady(NioSocketConduit.java:89) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.WorkerThread.run(WorkerThread.java:591) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
[07:32:54:355] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[07:32:54:357] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@4fd98416 for /ossObject/getAccessUrl
[07:32:54:358] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[08:57:06:514] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "dev"
[08:57:08:024] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$AutoConfiguredMapperScannerRegistrar.registerBeanDefinitions(MybatisAutoConfiguration.java:252) - Searching for mappers annotated with @Mapper
[08:57:08:029] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$AutoConfiguredMapperScannerRegistrar.registerBeanDefinitions(MybatisAutoConfiguration.java:264) - Using auto-configuration base package 'com.taurus'
[08:57:09:066] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[08:57:09:141] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[08:57:09:142] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[08:57:09:142] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[08:57:09:143] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[08:57:09:143] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[08:57:09:144] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[08:57:09:144] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[08:57:09:145] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[08:57:09:145] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-dev.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[08:57:09:145] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[08:57:09:145] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[08:57:09:145] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[08:57:09:201] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[08:57:09:592] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[08:57:09:596] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[08:57:10:302] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[08:57:10:329] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[08:57:10:339] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[08:57:10:673] [DEBUG] - io.micrometer.core.util.internal.logging.InternalLoggerFactory.newDefaultFactory(InternalLoggerFactory.java:59) - Using SLF4J as the default logging framework
[08:57:10:832] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[08:57:10:846] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[08:57:10:848] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[08:57:10:849] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[08:57:10:849] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[08:57:10:850] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[08:57:10:852] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[08:57:10:853] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[08:57:10:854] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[08:57:11:550] [DEBUG] - io.undertow.server.session.InMemorySessionManager.registerSessionListener(InMemorySessionManager.java:268) - Registered session listener io.undertow.servlet.core.SessionListenerBridge@53340d6b
[08:57:11:615] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[08:57:15:488] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[08:57:16:921] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[08:57:17:031] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[08:57:17:040] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.oss.mapper.OssRespositoryMapper对应的Mapper
[08:57:17:254] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.oss.mapper.OssFileMapper对应的Mapper
[08:57:17:313] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.oss.mapper.CategoryMapper对应的Mapper
[08:57:17:362] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.oss.mapper.CategoryOssFileMapper对应的Mapper
[08:57:17:406] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.oss.mapper.OssFileAudioMapper对应的Mapper
[08:57:17:460] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.oss.mapper.ProductOssInfoMapper对应的Mapper
[08:57:17:492] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.451秒
[08:57:19:414] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.o.c.OssFileController:
	{POST [/ossFile/uploadOssFile]}: uploadOssFile(HttpServletRequest,HttpServletResponse)
	{POST [/ossFile/uploadUrlFile]}: uploadUrlFile(JSONObject,HttpServletRequest)
	{GET [/ossFile/getOssFileById]}: getOssFileById(Integer)
	{POST [/ossFile/getOssFileList]}: getOssFileList(JSONObject)
	{POST [/ossFile/update]}: update(OssFile)
[08:57:19:419] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.o.c.OssObjectController:
	{GET [/ossObject/getAccessUrl]}: getAccessUrl(String,String)
	{GET [/ossObject/resizeOssPicFile]}: resizeOssPicFile(String)
	{GET [/ossObject/delete]}: delete(String,String)
[08:57:19:420] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.o.c.OssRespositoryController:
	{GET [/ossRespository/getAliUploadPolicy]}: getAliUploadPolicy(String,String,String)
	{GET [/ossRespository/getAliSts]}: getAliSts(String,String)
[08:57:19:438] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
	{ [/error]}: error(HttpServletRequest)
[08:57:19:469] [DEBUG] - org.springframework.web.servlet.handler.AbstractDetectingUrlHandlerMapping.detectHandlers(AbstractDetectingUrlHandlerMapping.java:86) - 'beanNameHandlerMapping' {}
[08:57:20:088] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$MapperScannerRegistrarNotFoundConfiguration.afterPropertiesSet(MybatisAutoConfiguration.java:305) - No org.mybatis.spring.mapper.MapperFactoryBean found.
[08:57:22:572] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[08:57:22:638] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 54 ms to scan 1 urls, producing 3 keys and 6 values 
[08:57:22:653] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype com.alibaba.nacos.api.remote.request.Request -> com.alibaba.nacos.api.remote.request.ServerRequest
[08:57:22:664] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[08:57:22:690] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 25 ms to scan 1 urls, producing 4 keys and 9 values 
[08:57:22:690] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype com.alibaba.nacos.api.remote.request.Request -> com.alibaba.nacos.api.remote.request.ServerRequest
[08:57:22:693] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[08:57:22:718] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 24 ms to scan 1 urls, producing 3 keys and 10 values 
[08:57:22:722] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar
file:/Users/<USER>/Documents/git/taurus-cloud/taurus-oss/target/classes/
file:/private/var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T/cp_bcxss9602qzcwyt2vdje35wk0.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/dnsns.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jaccess.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jfxrt.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunec.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/zipfs.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/nashorn.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/cldrdata.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/localedata.jar
[08:57:22:745] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 23 ms to scan 12 urls, producing 0 keys and 0 values 
[08:57:22:747] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[08:57:22:778] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 30 ms to scan 1 urls, producing 1 keys and 5 values 
[08:57:22:781] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[08:57:22:800] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 18 ms to scan 1 urls, producing 1 keys and 7 values 
[08:57:22:802] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[08:57:22:812] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
[08:57:22:813] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype java.lang.Comparable -> java.lang.Enum
[08:57:22:813] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype java.io.Serializable -> java.lang.Enum
[08:57:22:815] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar
file:/Users/<USER>/Documents/git/taurus-cloud/taurus-oss/target/classes/
file:/private/var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T/cp_bcxss9602qzcwyt2vdje35wk0.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/dnsns.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jaccess.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jfxrt.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunec.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/zipfs.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/nashorn.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/cldrdata.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/localedata.jar
[08:57:22:828] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 13 ms to scan 12 urls, producing 0 keys and 0 values 
[08:57:23:712] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[08:57:23:730] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[08:57:23:745] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[08:57:23:757] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[08:57:23:775] [DEBUG] - org.xnio.XnioWorker$Builder.build(XnioWorker.java:1191) - Creating worker:null, pool size:64, max pool size:64, keep alive:60000, io threads:8, stack size:0
[08:57:23:787] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[08:57:23:798] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-4', selector sun.nio.ch.KQueueSelectorImpl@763b2642
[08:57:23:798] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-3', selector sun.nio.ch.KQueueSelectorImpl@4aac5ea1
[08:57:23:799] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-6', selector sun.nio.ch.KQueueSelectorImpl@22c24e86
[08:57:23:798] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-1', selector sun.nio.ch.KQueueSelectorImpl@56d7560
[08:57:23:802] [DEBUG] - io.undertow.Undertow.start(Undertow.java:160) - Configuring listener with protocol HTTP for interface 0.0.0.0 and port 8085
[08:57:23:801] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 Accept', selector sun.nio.ch.KQueueSelectorImpl@7d39f8e
[08:57:23:798] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-5', selector sun.nio.ch.KQueueSelectorImpl@4c08efac
[08:57:23:801] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-8', selector sun.nio.ch.KQueueSelectorImpl@644d95c1
[08:57:23:800] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-7', selector sun.nio.ch.KQueueSelectorImpl@74090d93
[08:57:23:798] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-2', selector sun.nio.ch.KQueueSelectorImpl@3fc87a95
[08:57:23:869] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP taurus-oss 192.168.8.183:8085 register finished
[08:57:23:968] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started MySpringBootApplication in 19.834 seconds (JVM running for 21.441)
