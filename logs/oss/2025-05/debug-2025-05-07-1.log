[09:16:06:255] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "dev"
[09:16:09:603] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$AutoConfiguredMapperScannerRegistrar.registerBeanDefinitions(MybatisAutoConfiguration.java:252) - Searching for mappers annotated with @Mapper
[09:16:09:612] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$AutoConfiguredMapperScannerRegistrar.registerBeanDefinitions(MybatisAutoConfiguration.java:264) - Using auto-configuration base package 'com.taurus'
[09:16:12:253] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[09:16:12:518] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[09:16:12:522] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[09:16:12:522] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[09:16:12:523] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[09:16:12:524] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[09:16:12:527] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[09:16:12:538] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[09:16:12:540] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[09:16:12:540] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-dev.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[09:16:12:541] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[09:16:12:542] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[09:16:12:542] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[09:16:13:027] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[09:16:15:473] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[09:16:15:485] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[09:16:17:886] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[09:16:18:052] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[09:16:18:088] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[09:16:18:968] [DEBUG] - io.micrometer.core.util.internal.logging.InternalLoggerFactory.newDefaultFactory(InternalLoggerFactory.java:59) - Using SLF4J as the default logging framework
[09:16:19:583] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[09:16:19:611] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[09:16:19:618] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[09:16:19:624] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[09:16:19:627] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[09:16:19:637] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[09:16:19:639] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[09:16:19:640] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[09:16:19:646] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[09:16:20:706] [DEBUG] - io.undertow.server.session.InMemorySessionManager.registerSessionListener(InMemorySessionManager.java:268) - Registered session listener io.undertow.servlet.core.SessionListenerBridge@43dea788
[09:16:20:794] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[09:16:25:114] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[09:16:27:057] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[09:16:27:243] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[09:16:27:257] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.oss.mapper.OssFileAudioMapper对应的Mapper
[09:16:27:479] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.oss.mapper.OssFileMapper对应的Mapper
[09:16:27:557] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.oss.mapper.ProductOssInfoMapper对应的Mapper
[09:16:27:608] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.oss.mapper.CategoryMapper对应的Mapper
[09:16:27:668] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.oss.mapper.OssRespositoryMapper对应的Mapper
[09:16:27:710] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.oss.mapper.CategoryOssFileMapper对应的Mapper
[09:16:27:768] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.511秒
[09:16:29:666] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.o.c.OssFileController:
	{POST [/ossFile/uploadUrlFile]}: uploadUrlFile(JSONObject,HttpServletRequest)
	{GET [/ossFile/getOssFileById]}: getOssFileById(Integer)
	{POST [/ossFile/getOssFileList]}: getOssFileList(JSONObject)
	{POST [/ossFile/update]}: update(OssFile)
	{POST [/ossFile/uploadOssFile]}: uploadOssFile(HttpServletRequest,HttpServletResponse)
[09:16:29:673] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.o.c.OssObjectController:
	{GET [/ossObject/getAccessUrl]}: getAccessUrl(String,String)
	{GET [/ossObject/resizeOssPicFile]}: resizeOssPicFile(String)
	{GET [/ossObject/delete]}: delete(String,String)
[09:16:29:675] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.o.c.OssRespositoryController:
	{GET [/ossRespository/getAliUploadPolicy]}: getAliUploadPolicy(String,String,String)
	{GET [/ossRespository/getAliSts]}: getAliSts(String,String)
[09:16:29:704] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
	{ [/error]}: error(HttpServletRequest)
[09:16:29:747] [DEBUG] - org.springframework.web.servlet.handler.AbstractDetectingUrlHandlerMapping.detectHandlers(AbstractDetectingUrlHandlerMapping.java:86) - 'beanNameHandlerMapping' {}
[09:16:30:289] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$MapperScannerRegistrarNotFoundConfiguration.afterPropertiesSet(MybatisAutoConfiguration.java:305) - No org.mybatis.spring.mapper.MapperFactoryBean found.
[09:16:32:521] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[09:16:32:607] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 80 ms to scan 1 urls, producing 3 keys and 6 values 
[09:16:32:623] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype com.alibaba.nacos.api.remote.request.Request -> com.alibaba.nacos.api.remote.request.ServerRequest
[09:16:32:644] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[09:16:32:773] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 123 ms to scan 1 urls, producing 4 keys and 9 values 
[09:16:32:778] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype com.alibaba.nacos.api.remote.request.Request -> com.alibaba.nacos.api.remote.request.ServerRequest
[09:16:32:787] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[09:16:32:817] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 26 ms to scan 1 urls, producing 3 keys and 10 values 
[09:16:32:823] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar
file:/Users/<USER>/Documents/git/taurus-cloud/taurus-oss/target/classes/
file:/private/var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T/cp_bcxss9602qzcwyt2vdje35wk0.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/dnsns.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jaccess.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jfxrt.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunec.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/zipfs.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/nashorn.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/cldrdata.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/localedata.jar
[09:16:32:846] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 23 ms to scan 12 urls, producing 0 keys and 0 values 
[09:16:32:853] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[09:16:32:867] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 14 ms to scan 1 urls, producing 1 keys and 5 values 
[09:16:32:870] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[09:16:32:883] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
[09:16:32:887] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[09:16:32:940] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 51 ms to scan 1 urls, producing 2 keys and 8 values 
[09:16:32:945] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype java.lang.Comparable -> java.lang.Enum
[09:16:32:945] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype java.io.Serializable -> java.lang.Enum
[09:16:32:950] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar
file:/Users/<USER>/Documents/git/taurus-cloud/taurus-oss/target/classes/
file:/private/var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T/cp_bcxss9602qzcwyt2vdje35wk0.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/dnsns.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jaccess.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jfxrt.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunec.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/zipfs.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/nashorn.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/cldrdata.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/localedata.jar
[09:16:32:969] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 18 ms to scan 12 urls, producing 0 keys and 0 values 
[09:16:33:879] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[09:16:33:903] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[09:16:33:922] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[09:16:33:973] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[09:16:34:004] [DEBUG] - org.xnio.XnioWorker$Builder.build(XnioWorker.java:1191) - Creating worker:null, pool size:64, max pool size:64, keep alive:60000, io threads:8, stack size:0
[09:16:34:019] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[09:16:34:039] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-6', selector sun.nio.ch.KQueueSelectorImpl@4642b6f5
[09:16:34:039] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-5', selector sun.nio.ch.KQueueSelectorImpl@1c28dff0
[09:16:34:039] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-1', selector sun.nio.ch.KQueueSelectorImpl@4c6d7be0
[09:16:34:039] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-2', selector sun.nio.ch.KQueueSelectorImpl@52bb8025
[09:16:34:039] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 Accept', selector sun.nio.ch.KQueueSelectorImpl@74cf5ce1
[09:16:34:039] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-4', selector sun.nio.ch.KQueueSelectorImpl@5e7b595c
[09:16:34:039] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-8', selector sun.nio.ch.KQueueSelectorImpl@69b4bd7a
[09:16:34:040] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-3', selector sun.nio.ch.KQueueSelectorImpl@2df30f3a
[09:16:34:051] [DEBUG] - io.undertow.Undertow.start(Undertow.java:160) - Configuring listener with protocol HTTP for interface 0.0.0.0 and port 8085
[09:16:34:051] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-7', selector sun.nio.ch.KQueueSelectorImpl@645d4ad0
[09:16:34:150] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP taurus-oss 192.168.8.151:8085 register finished
[09:16:34:291] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started MySpringBootApplication in 31.28 seconds (JVM running for 33.117)
[09:18:40:622] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:40:630] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:40:630] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:41:085] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[09:18:41:169] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:41:170] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:41:170] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:41:217] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:41:218] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:41:218] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:41:250] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:41:251] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:41:251] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:41:290] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:41:291] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:41:291] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:41:329] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:41:329] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:41:329] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:41:371] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:41:371] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:41:371] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:41:411] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:41:412] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:41:412] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:41:456] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:41:457] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:41:457] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:41:496] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:41:497] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:41:497] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:41:535] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:41:535] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:41:535] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:41:575] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:41:575] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:41:575] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:41:641] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:41:641] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:41:641] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:41:695] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:41:696] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:41:696] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:41:736] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:41:736] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:41:736] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:41:770] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:41:770] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:41:770] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:41:826] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:41:826] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:41:826] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:41:878] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:41:878] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:41:879] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:41:921] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:41:921] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:41:922] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:41:970] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:41:970] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:41:970] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:42:010] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:42:010] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:42:010] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:42:079] [DEBUG] - io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:160) - Error reading request
java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method) ~[?:1.8.0_281]
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.read(IOUtil.java:192) ~[?:1.8.0_281]
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378) ~[?:1.8.0_281]
	at org.xnio.nio.NioSocketConduit.read(NioSocketConduit.java:289) ~[xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ConduitStreamSourceChannel.read(ConduitStreamSourceChannel.java:127) ~[xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:158) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:136) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:59) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.xnio.ChannelListeners.invokeChannelListener(ChannelListeners.java:92) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ReadReadyHandler$ChannelListenerHandler.readReady(ReadReadyHandler.java:66) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.NioSocketConduit.handleReady(NioSocketConduit.java:89) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.WorkerThread.run(WorkerThread.java:591) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
[09:18:42:088] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:42:089] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:42:089] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:42:155] [DEBUG] - io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:160) - Error reading request
java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method) ~[?:1.8.0_281]
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.read(IOUtil.java:192) ~[?:1.8.0_281]
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378) ~[?:1.8.0_281]
	at org.xnio.nio.NioSocketConduit.read(NioSocketConduit.java:289) ~[xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ConduitStreamSourceChannel.read(ConduitStreamSourceChannel.java:127) ~[xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:158) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:136) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:59) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.xnio.ChannelListeners.invokeChannelListener(ChannelListeners.java:92) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ReadReadyHandler$ChannelListenerHandler.readReady(ReadReadyHandler.java:66) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.NioSocketConduit.handleReady(NioSocketConduit.java:89) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.WorkerThread.run(WorkerThread.java:591) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
[09:18:42:157] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:42:159] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:42:159] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:48:241] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:48:241] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:48:242] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:48:341] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:48:341] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:48:342] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:48:393] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:48:393] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:48:394] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:48:433] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:48:434] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:48:434] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:48:507] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:48:508] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:48:508] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:48:550] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:48:550] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:48:551] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:48:612] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:48:613] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:48:613] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:48:656] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:48:656] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:48:656] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:48:705] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:48:706] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:48:707] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:48:753] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:48:753] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:48:753] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:48:801] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:48:801] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:48:801] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:48:851] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:48:851] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:48:852] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:48:891] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:48:892] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:48:892] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:48:939] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:48:940] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:48:940] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:48:993] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:48:994] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:48:994] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:49:033] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:49:033] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:49:033] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:49:072] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:49:073] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:49:073] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:49:157] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:49:159] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:49:159] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:49:215] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:49:216] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:49:216] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:49:271] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:49:272] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:49:272] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:49:405] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:49:407] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:49:408] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:49:483] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:49:483] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:49:484] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:18:49:540] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:18:49:540] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:18:49:541] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:19:00:128] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:19:00:129] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:19:00:129] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:19:00:244] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:19:00:244] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:19:00:244] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:19:00:313] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:19:00:313] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:19:00:314] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:19:00:373] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:19:00:373] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:19:00:373] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:19:00:429] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:19:00:429] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:19:00:429] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:19:00:466] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:19:00:466] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:19:00:466] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:19:00:514] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:19:00:515] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:19:00:516] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:19:00:553] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:19:00:553] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:19:00:553] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:19:00:608] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:19:00:609] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:19:00:609] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:19:00:651] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:19:00:651] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:19:00:652] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:19:00:689] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:19:00:690] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:19:00:690] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:19:00:731] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:19:00:732] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:19:00:732] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:19:00:773] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:19:00:773] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:19:00:773] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:19:00:812] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:19:00:812] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:19:00:815] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:19:00:851] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:19:00:852] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:19:00:852] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:19:00:893] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:19:00:893] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:19:00:893] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:19:00:933] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:19:00:934] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:19:00:934] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:19:00:973] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:19:00:973] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:19:00:973] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:19:01:039] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:19:01:039] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:19:01:039] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:19:01:095] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:19:01:095] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:19:01:096] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:19:01:145] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:19:01:146] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:19:01:146] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:19:01:221] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:19:01:227] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:19:01:227] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[09:19:01:290] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[09:19:01:290] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[09:19:01:291] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:03:21:920] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:03:21:932] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:03:21:933] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:03:22:999] [DEBUG] - com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1449) - skip not validate connection.
[10:03:24:021] [DEBUG] - com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1449) - skip not validate connection.
[10:03:24:130] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:03:24:190] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:03:24:191] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:03:24:191] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:03:24:241] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:03:24:241] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:03:24:242] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:03:24:280] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:03:24:281] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:03:24:281] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:03:24:365] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:03:24:365] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:03:24:366] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:03:24:402] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:03:24:402] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:03:24:402] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:03:24:440] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:03:24:440] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:03:24:440] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:03:24:478] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:03:24:478] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:03:24:479] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:03:24:522] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:03:24:535] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:03:24:537] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:03:24:537] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:03:24:605] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:03:24:605] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:03:24:606] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:36:050] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:36:060] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:36:061] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:36:255] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:36:258] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:36:258] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:36:332] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:36:333] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:36:333] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:36:331] [DEBUG] - io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:160) - Error reading request
java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method) ~[?:1.8.0_281]
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.read(IOUtil.java:192) ~[?:1.8.0_281]
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378) ~[?:1.8.0_281]
	at org.xnio.nio.NioSocketConduit.read(NioSocketConduit.java:289) ~[xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ConduitStreamSourceChannel.read(ConduitStreamSourceChannel.java:127) ~[xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:158) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:136) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:59) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.xnio.ChannelListeners.invokeChannelListener(ChannelListeners.java:92) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ReadReadyHandler$ChannelListenerHandler.readReady(ReadReadyHandler.java:66) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.NioSocketConduit.handleReady(NioSocketConduit.java:89) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.WorkerThread.run(WorkerThread.java:591) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
[10:08:36:399] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:36:400] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:36:400] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:36:452] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:36:452] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:36:452] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:36:535] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:36:535] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:36:535] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:36:605] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:36:606] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:36:606] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:36:691] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:36:692] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:36:692] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:36:771] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:36:772] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:36:772] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:36:841] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:36:841] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:36:841] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:36:921] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:36:922] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:36:923] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:36:960] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:36:960] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:36:961] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:37:006] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:37:006] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:37:006] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:37:088] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:37:089] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:37:089] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:37:125] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:37:125] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:37:125] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:37:168] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:37:168] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:37:168] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:37:253] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:37:254] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:37:254] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:37:326] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:37:327] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:37:327] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:37:367] [DEBUG] - io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:160) - Error reading request
java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method) ~[?:1.8.0_281]
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.read(IOUtil.java:192) ~[?:1.8.0_281]
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378) ~[?:1.8.0_281]
	at org.xnio.nio.NioSocketConduit.read(NioSocketConduit.java:289) ~[xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ConduitStreamSourceChannel.read(ConduitStreamSourceChannel.java:127) ~[xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:158) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:136) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:59) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.xnio.ChannelListeners.invokeChannelListener(ChannelListeners.java:92) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ReadReadyHandler$ChannelListenerHandler.readReady(ReadReadyHandler.java:66) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.NioSocketConduit.handleReady(NioSocketConduit.java:89) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.WorkerThread.run(WorkerThread.java:591) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
[10:08:37:369] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:37:369] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:37:370] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:37:451] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:37:451] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:37:451] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:37:524] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:37:524] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:37:524] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:37:571] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:37:571] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:37:571] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:37:628] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:37:628] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:37:629] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:39:318] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:39:319] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:39:319] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:39:362] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:39:362] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:39:362] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:39:428] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:39:428] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:39:428] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:39:505] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:39:505] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:39:505] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:39:569] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:39:570] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:39:570] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:39:649] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:39:649] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:39:650] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:39:718] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:39:718] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:39:718] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:39:761] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:39:762] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:39:762] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:39:814] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:39:814] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:39:814] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:39:860] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:39:861] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:39:861] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:39:923] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:39:924] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:39:924] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:39:975] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:39:975] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:39:975] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:40:027] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:40:027] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:40:028] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:40:068] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:40:069] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:40:069] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:40:106] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:40:106] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:40:107] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:40:144] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:40:144] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:40:144] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:40:178] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:40:178] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:40:178] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:40:218] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:40:218] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:40:218] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:40:253] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:40:253] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:40:253] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:40:295] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:40:295] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:40:295] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:40:334] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:40:334] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:40:334] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:40:375] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:40:375] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:40:375] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:40:422] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:08:40:422] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:08:40:422] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:08:40:474] [DEBUG] - io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:160) - Error reading request
java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method) ~[?:1.8.0_281]
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.read(IOUtil.java:192) ~[?:1.8.0_281]
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378) ~[?:1.8.0_281]
	at org.xnio.nio.NioSocketConduit.read(NioSocketConduit.java:289) ~[xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ConduitStreamSourceChannel.read(ConduitStreamSourceChannel.java:127) ~[xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:158) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:136) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:59) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.xnio.ChannelListeners.invokeChannelListener(ChannelListeners.java:92) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ReadReadyHandler$ChannelListenerHandler.readReady(ReadReadyHandler.java:66) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.NioSocketConduit.handleReady(NioSocketConduit.java:89) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.WorkerThread.run(WorkerThread.java:591) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
[10:09:02:774] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:02:775] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:02:775] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:02:942] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:02:945] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:02:945] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:03:011] [DEBUG] - io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:160) - Error reading request
java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method) ~[?:1.8.0_281]
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.read(IOUtil.java:192) ~[?:1.8.0_281]
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378) ~[?:1.8.0_281]
	at org.xnio.nio.NioSocketConduit.read(NioSocketConduit.java:289) ~[xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ConduitStreamSourceChannel.read(ConduitStreamSourceChannel.java:127) ~[xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:158) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:136) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:59) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.xnio.ChannelListeners.invokeChannelListener(ChannelListeners.java:92) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ReadReadyHandler$ChannelListenerHandler.readReady(ReadReadyHandler.java:66) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.NioSocketConduit.handleReady(NioSocketConduit.java:89) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.WorkerThread.run(WorkerThread.java:591) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
[10:09:03:013] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:03:014] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:03:014] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:03:071] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:03:073] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:03:073] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:03:124] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:03:125] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:03:125] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:03:158] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:03:159] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:03:159] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:03:204] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:03:205] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:03:205] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:03:204] [DEBUG] - io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:160) - Error reading request
java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method) ~[?:1.8.0_281]
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.read(IOUtil.java:192) ~[?:1.8.0_281]
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378) ~[?:1.8.0_281]
	at org.xnio.nio.NioSocketConduit.read(NioSocketConduit.java:289) ~[xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ConduitStreamSourceChannel.read(ConduitStreamSourceChannel.java:127) ~[xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:158) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:136) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:59) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.xnio.ChannelListeners.invokeChannelListener(ChannelListeners.java:92) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ReadReadyHandler$ChannelListenerHandler.readReady(ReadReadyHandler.java:66) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.NioSocketConduit.handleReady(NioSocketConduit.java:89) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.WorkerThread.run(WorkerThread.java:591) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
[10:09:03:262] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:03:256] [DEBUG] - io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:160) - Error reading request
java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method) ~[?:1.8.0_281]
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.read(IOUtil.java:192) ~[?:1.8.0_281]
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378) ~[?:1.8.0_281]
	at org.xnio.nio.NioSocketConduit.read(NioSocketConduit.java:289) ~[xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ConduitStreamSourceChannel.read(ConduitStreamSourceChannel.java:127) ~[xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:158) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:136) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:59) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.xnio.ChannelListeners.invokeChannelListener(ChannelListeners.java:92) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ReadReadyHandler$ChannelListenerHandler.readReady(ReadReadyHandler.java:66) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.NioSocketConduit.handleReady(NioSocketConduit.java:89) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.WorkerThread.run(WorkerThread.java:591) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
[10:09:03:263] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:03:263] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:03:323] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:03:323] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:03:324] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:03:358] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:03:359] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:03:359] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:03:399] [DEBUG] - io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:160) - Error reading request
java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method) ~[?:1.8.0_281]
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.read(IOUtil.java:192) ~[?:1.8.0_281]
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378) ~[?:1.8.0_281]
	at org.xnio.nio.NioSocketConduit.read(NioSocketConduit.java:289) ~[xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ConduitStreamSourceChannel.read(ConduitStreamSourceChannel.java:127) ~[xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:158) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:136) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:59) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.xnio.ChannelListeners.invokeChannelListener(ChannelListeners.java:92) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ReadReadyHandler$ChannelListenerHandler.readReady(ReadReadyHandler.java:66) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.NioSocketConduit.handleReady(NioSocketConduit.java:89) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.WorkerThread.run(WorkerThread.java:591) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
[10:09:03:401] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:03:401] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:03:401] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:03:481] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:03:481] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:03:481] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:03:554] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:03:554] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:03:554] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:03:609] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:03:609] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:03:609] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:03:664] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:03:665] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:03:665] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:03:697] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:03:697] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:03:697] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:03:767] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:03:768] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:03:768] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:03:838] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:03:838] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:03:838] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:03:877] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:03:877] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:03:877] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:03:923] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:03:923] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:03:923] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:03:958] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:03:958] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:03:958] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:04:023] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:04:023] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:04:024] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:04:070] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:04:070] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:04:071] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:46:071] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:46:077] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:46:078] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:46:239] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:46:239] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:46:239] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:46:278] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:46:278] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:46:278] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:46:317] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:46:317] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:46:317] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:46:359] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:46:360] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:46:360] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:46:433] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:46:433] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:46:433] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:46:477] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:46:478] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:46:478] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:46:517] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:46:517] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:46:517] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:46:557] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:46:557] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:46:557] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:46:602] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:46:602] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:46:602] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:46:642] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:46:643] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:46:643] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:46:684] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:46:685] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:46:685] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:46:751] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:46:753] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:46:754] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:46:799] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:46:799] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:46:799] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:46:838] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:46:838] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:46:838] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:46:883] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:46:883] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:46:883] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:46:921] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:46:922] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:46:922] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:46:961] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:46:961] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:46:961] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:46:996] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:46:997] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:46:997] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:47:036] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:47:036] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:47:037] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:47:078] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:47:078] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:47:079] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:47:116] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:47:116] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:47:116] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:09:47:153] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:09:47:154] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:09:47:154] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:10:17:809] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:10:17:810] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:10:17:810] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:10:17:945] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:10:17:946] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:10:17:946] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:10:17:988] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:10:17:988] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:10:17:988] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:10:18:040] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:10:18:040] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:10:18:040] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:10:18:081] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:10:18:083] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:10:18:083] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:10:18:153] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:10:18:154] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:10:18:154] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:10:18:207] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:10:18:208] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:10:18:208] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:10:18:287] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:10:18:289] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:10:18:289] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:10:18:286] [DEBUG] - io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:160) - Error reading request
java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method) ~[?:1.8.0_281]
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.read(IOUtil.java:192) ~[?:1.8.0_281]
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378) ~[?:1.8.0_281]
	at org.xnio.nio.NioSocketConduit.read(NioSocketConduit.java:289) ~[xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ConduitStreamSourceChannel.read(ConduitStreamSourceChannel.java:127) ~[xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:158) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:136) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:59) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.xnio.ChannelListeners.invokeChannelListener(ChannelListeners.java:92) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ReadReadyHandler$ChannelListenerHandler.readReady(ReadReadyHandler.java:66) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.NioSocketConduit.handleReady(NioSocketConduit.java:89) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.WorkerThread.run(WorkerThread.java:591) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
[10:10:18:356] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:10:18:356] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:10:18:356] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:10:18:414] [DEBUG] - io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:160) - Error reading request
java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method) ~[?:1.8.0_281]
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.read(IOUtil.java:192) ~[?:1.8.0_281]
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378) ~[?:1.8.0_281]
	at org.xnio.nio.NioSocketConduit.read(NioSocketConduit.java:289) ~[xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ConduitStreamSourceChannel.read(ConduitStreamSourceChannel.java:127) ~[xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:158) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:136) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:59) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.xnio.ChannelListeners.invokeChannelListener(ChannelListeners.java:92) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ReadReadyHandler$ChannelListenerHandler.readReady(ReadReadyHandler.java:66) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.NioSocketConduit.handleReady(NioSocketConduit.java:89) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.WorkerThread.run(WorkerThread.java:591) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
[10:10:18:417] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:10:18:417] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:10:18:417] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:10:18:480] [DEBUG] - io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:160) - Error reading request
java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method) ~[?:1.8.0_281]
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.read(IOUtil.java:192) ~[?:1.8.0_281]
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378) ~[?:1.8.0_281]
	at org.xnio.nio.NioSocketConduit.read(NioSocketConduit.java:289) ~[xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ConduitStreamSourceChannel.read(ConduitStreamSourceChannel.java:127) ~[xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:158) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:136) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:59) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.xnio.ChannelListeners.invokeChannelListener(ChannelListeners.java:92) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ReadReadyHandler$ChannelListenerHandler.readReady(ReadReadyHandler.java:66) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.NioSocketConduit.handleReady(NioSocketConduit.java:89) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.WorkerThread.run(WorkerThread.java:591) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
[10:56:50:520] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:56:50:532] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:56:50:533] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:56:51:605] [DEBUG] - com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1449) - skip not validate connection.
[10:56:52:615] [DEBUG] - com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1449) - skip not validate connection.
[10:56:52:685] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[10:56:52:772] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:56:52:773] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:56:52:773] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:56:52:848] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:56:52:849] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:56:52:849] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:56:52:950] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:56:52:950] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:56:52:951] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:56:53:006] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:56:53:006] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:56:53:006] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:56:53:048] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:56:53:048] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:56:53:048] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:56:53:132] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:56:53:132] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:56:53:132] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:56:53:186] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:56:53:186] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:56:53:186] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:56:53:256] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:56:53:256] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:56:53:256] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:56:53:300] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:56:53:300] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:56:53:300] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:56:53:366] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:56:53:367] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:56:53:367] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:56:53:410] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:56:53:411] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:56:53:411] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:56:53:484] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:56:53:484] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:56:53:484] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:56:53:520] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:56:53:520] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:56:53:520] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:56:53:568] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:56:53:568] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:56:53:569] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:56:53:610] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:56:53:611] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:56:53:611] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:56:53:688] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:56:53:688] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:56:53:688] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:56:53:729] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:56:53:729] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:56:53:729] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:56:53:777] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:56:53:777] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:56:53:777] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:56:53:838] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:56:53:838] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:56:53:838] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:56:53:881] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:56:53:882] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:56:53:882] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:56:53:971] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:56:53:971] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:56:53:971] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[10:56:54:026] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[10:56:54:027] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[10:56:54:027] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[11:04:36:392] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[11:04:36:400] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[11:04:36:401] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[11:04:36:630] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[11:04:36:632] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[11:04:36:632] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[11:04:36:726] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[11:04:36:727] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[11:04:36:727] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[11:04:36:804] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[11:04:36:806] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@333a3c79 for /ossObject/getAccessUrl
[11:04:36:806] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[11:04:36:802] [DEBUG] - io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:160) - Error reading request
java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method) ~[?:1.8.0_281]
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223) ~[?:1.8.0_281]
	at sun.nio.ch.IOUtil.read(IOUtil.java:192) ~[?:1.8.0_281]
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378) ~[?:1.8.0_281]
	at org.xnio.nio.NioSocketConduit.read(NioSocketConduit.java:289) ~[xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ConduitStreamSourceChannel.read(ConduitStreamSourceChannel.java:127) ~[xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEventWithNoRunningRequest(HttpReadListener.java:158) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:136) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.protocol.http.HttpReadListener.handleEvent(HttpReadListener.java:59) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.xnio.ChannelListeners.invokeChannelListener(ChannelListeners.java:92) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.conduits.ReadReadyHandler$ChannelListenerHandler.readReady(ReadReadyHandler.java:66) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.NioSocketConduit.handleReady(NioSocketConduit.java:89) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
	at org.xnio.nio.WorkerThread.run(WorkerThread.java:591) [xnio-nio-3.8.7.Final.jar:3.8.7.Final]
[15:10:58:329] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "dev"
[15:11:00:175] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$AutoConfiguredMapperScannerRegistrar.registerBeanDefinitions(MybatisAutoConfiguration.java:252) - Searching for mappers annotated with @Mapper
[15:11:00:180] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$AutoConfiguredMapperScannerRegistrar.registerBeanDefinitions(MybatisAutoConfiguration.java:264) - Using auto-configuration base package 'com.taurus'
[15:11:02:002] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[15:11:02:126] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[15:11:02:127] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:11:02:127] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[15:11:02:128] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[15:11:02:128] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[15:11:02:129] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[15:11:02:138] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[15:11:02:140] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:11:02:140] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-dev.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:11:02:140] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[15:11:02:141] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:11:02:147] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[15:11:02:266] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[15:11:02:759] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[15:11:02:765] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[15:11:04:051] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[15:11:04:079] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[15:11:04:095] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[15:11:04:590] [DEBUG] - io.micrometer.core.util.internal.logging.InternalLoggerFactory.newDefaultFactory(InternalLoggerFactory.java:59) - Using SLF4J as the default logging framework
[15:11:05:100] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[15:11:05:195] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[15:11:05:199] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[15:11:05:230] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[15:11:05:232] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[15:11:05:236] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[15:11:05:237] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[15:11:05:245] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[15:11:05:252] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[15:11:06:511] [DEBUG] - io.undertow.server.session.InMemorySessionManager.registerSessionListener(InMemorySessionManager.java:268) - Registered session listener io.undertow.servlet.core.SessionListenerBridge@7558ebe7
[15:11:06:551] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[15:11:10:407] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[15:11:13:348] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[15:11:13:648] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[15:11:13:678] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.oss.mapper.CategoryMapper对应的Mapper
[15:11:14:157] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.oss.mapper.OssRespositoryMapper对应的Mapper
[15:11:14:397] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.oss.mapper.OssFileAudioMapper对应的Mapper
[15:11:14:507] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.oss.mapper.CategoryOssFileMapper对应的Mapper
[15:11:14:569] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.oss.mapper.OssFileMapper对应的Mapper
[15:11:14:661] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.oss.mapper.ProductOssInfoMapper对应的Mapper
[15:11:14:754] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：1.074秒
[15:11:18:857] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.o.c.OssFileController:
	{POST [/ossFile/uploadOssFile]}: uploadOssFile(HttpServletRequest,HttpServletResponse)
	{POST [/ossFile/uploadUrlFile]}: uploadUrlFile(JSONObject,HttpServletRequest)
	{GET [/ossFile/getOssFileById]}: getOssFileById(Integer)
	{POST [/ossFile/getOssFileList]}: getOssFileList(JSONObject)
	{POST [/ossFile/update]}: update(OssFile)
[15:11:18:864] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.o.c.OssObjectController:
	{GET [/ossObject/getAccessUrl]}: getAccessUrl(String,String)
	{GET [/ossObject/resizeOssPicFile]}: resizeOssPicFile(String)
	{GET [/ossObject/delete]}: delete(String,String)
[15:11:18:866] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.o.c.OssRespositoryController:
	{GET [/ossRespository/getAliUploadPolicy]}: getAliUploadPolicy(String,String,String)
	{GET [/ossRespository/getAliSts]}: getAliSts(String,String)
[15:11:18:905] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
	{ [/error]}: error(HttpServletRequest)
[15:11:19:028] [DEBUG] - org.springframework.web.servlet.handler.AbstractDetectingUrlHandlerMapping.detectHandlers(AbstractDetectingUrlHandlerMapping.java:86) - 'beanNameHandlerMapping' {}
[15:11:20:681] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$MapperScannerRegistrarNotFoundConfiguration.afterPropertiesSet(MybatisAutoConfiguration.java:305) - No org.mybatis.spring.mapper.MapperFactoryBean found.
[15:11:23:857] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[15:11:23:971] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 103 ms to scan 1 urls, producing 3 keys and 6 values 
[15:11:23:995] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype com.alibaba.nacos.api.remote.request.Request -> com.alibaba.nacos.api.remote.request.ServerRequest
[15:11:24:024] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[15:11:24:078] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 45 ms to scan 1 urls, producing 4 keys and 9 values 
[15:11:24:083] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype com.alibaba.nacos.api.remote.request.Request -> com.alibaba.nacos.api.remote.request.ServerRequest
[15:11:24:153] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[15:11:24:185] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 31 ms to scan 1 urls, producing 3 keys and 10 values 
[15:11:24:197] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar
file:/Users/<USER>/Documents/git/taurus-cloud/taurus-oss/target/classes/
file:/private/var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T/cp_bcxss9602qzcwyt2vdje35wk0.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/dnsns.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jaccess.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jfxrt.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunec.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/zipfs.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/nashorn.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/cldrdata.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/localedata.jar
[15:11:24:225] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 28 ms to scan 12 urls, producing 0 keys and 0 values 
[15:11:24:231] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[15:11:24:255] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 24 ms to scan 1 urls, producing 1 keys and 5 values 
[15:11:24:259] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[15:11:24:278] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 19 ms to scan 1 urls, producing 1 keys and 7 values 
[15:11:24:288] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[15:11:24:392] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 102 ms to scan 1 urls, producing 2 keys and 8 values 
[15:11:24:397] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype java.lang.Comparable -> java.lang.Enum
[15:11:24:398] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype java.io.Serializable -> java.lang.Enum
[15:11:24:419] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar
file:/Users/<USER>/Documents/git/taurus-cloud/taurus-oss/target/classes/
file:/private/var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T/cp_bcxss9602qzcwyt2vdje35wk0.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/dnsns.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jaccess.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jfxrt.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunec.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/zipfs.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/nashorn.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/cldrdata.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/localedata.jar
[15:11:24:447] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 26 ms to scan 12 urls, producing 0 keys and 0 values 
[15:11:25:784] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[15:11:25:813] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[15:11:25:836] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[15:11:25:853] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[15:11:25:888] [DEBUG] - org.xnio.XnioWorker$Builder.build(XnioWorker.java:1191) - Creating worker:null, pool size:64, max pool size:64, keep alive:60000, io threads:8, stack size:0
[15:11:25:909] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[15:11:25:925] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-5', selector sun.nio.ch.KQueueSelectorImpl@3e6889f7
[15:11:25:944] [DEBUG] - io.undertow.Undertow.start(Undertow.java:160) - Configuring listener with protocol HTTP for interface 0.0.0.0 and port 8085
[15:11:25:925] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-2', selector sun.nio.ch.KQueueSelectorImpl@76d19e9d
[15:11:25:944] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-1', selector sun.nio.ch.KQueueSelectorImpl@70c7d393
[15:11:25:927] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-3', selector sun.nio.ch.KQueueSelectorImpl@59b80177
[15:11:25:944] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-7', selector sun.nio.ch.KQueueSelectorImpl@516e6e73
[15:11:25:927] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-4', selector sun.nio.ch.KQueueSelectorImpl@5e3d7723
[15:11:25:927] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-6', selector sun.nio.ch.KQueueSelectorImpl@55825586
[15:11:25:927] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 Accept', selector sun.nio.ch.KQueueSelectorImpl@3904769a
[15:11:25:927] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-8', selector sun.nio.ch.KQueueSelectorImpl@726c424a
[15:11:26:079] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP taurus-oss 192.168.8.151:8085 register finished
[15:11:26:351] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started MySpringBootApplication in 31.502 seconds (JVM running for 34.019)
[15:52:16:652] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[15:52:16:662] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@616c273 for /ossObject/getAccessUrl
[15:52:16:662] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[15:52:18:068] [DEBUG] - com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1449) - skip not validate connection.
[15:52:19:103] [DEBUG] - com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1449) - skip not validate connection.
[15:52:19:321] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[15:52:19:751] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[15:52:19:751] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@616c273 for /ossObject/getAccessUrl
[15:52:19:751] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[15:52:20:031] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[15:52:20:031] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@616c273 for /ossObject/getAccessUrl
[15:52:20:031] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[15:52:20:286] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[15:52:20:287] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@616c273 for /ossObject/getAccessUrl
[15:52:20:287] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[15:52:20:557] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[15:52:20:558] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@616c273 for /ossObject/getAccessUrl
[15:52:20:558] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
[15:52:20:777] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /ossObject/getAccessUrl, authentication required: false
[15:52:20:777] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@616c273 for /ossObject/getAccessUrl
[15:52:20:777] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /ossObject/getAccessUrl
