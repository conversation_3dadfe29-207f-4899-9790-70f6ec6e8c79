[11:43:06:303] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "dev"
[11:43:11:126] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[11:43:11:288] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[11:43:11:289] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[11:43:11:293] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[11:43:11:294] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[11:43:11:294] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[11:43:11:294] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[11:43:11:295] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[11:43:11:295] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[11:43:11:296] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-dev.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[11:43:11:300] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[11:43:11:301] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[11:43:11:302] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[11:43:11:362] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[11:43:11:876] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[11:43:11:886] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[11:43:13:949] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[11:43:14:005] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[11:43:14:937] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[11:43:14:956] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[11:43:14:957] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[11:43:14:961] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[11:43:14:962] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[11:43:14:977] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[11:43:14:981] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[11:43:14:986] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[11:43:14:989] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[11:43:20:668] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[11:43:22:752] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[11:43:22:939] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[11:43:23:383] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.437秒
[11:43:29:405] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 81 ms to scan 1 urls, producing 3 keys and 6 values 
[11:43:29:517] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 30 ms to scan 1 urls, producing 4 keys and 9 values 
[11:43:29:549] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 17 ms to scan 1 urls, producing 3 keys and 10 values 
[11:43:29:573] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 19 ms to scan 12 urls, producing 0 keys and 0 values 
[11:43:29:596] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 17 ms to scan 1 urls, producing 1 keys and 5 values 
[11:43:29:611] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
[11:43:29:630] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
[11:43:29:646] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 13 ms to scan 12 urls, producing 0 keys and 0 values 
[11:43:30:762] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[11:43:30:783] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[11:43:30:805] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[11:43:30:821] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[11:43:30:859] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[11:43:31:035] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP taurus-oss 192.168.8.151:8085 register finished
[11:43:31:333] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started MySpringBootApplication in 30.683 seconds (JVM running for 32.93)
[12:01:44:900] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "dev"
[12:01:48:905] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[12:01:49:029] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[12:01:49:030] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[12:01:49:030] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[12:01:49:032] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[12:01:49:034] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[12:01:49:035] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[12:01:49:037] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[12:01:49:038] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[12:01:49:038] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-dev.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[12:01:49:040] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[12:01:49:041] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[12:01:49:042] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[12:01:49:159] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[12:01:49:654] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[12:01:49:664] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[12:01:50:954] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[12:01:50:998] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[12:01:51:685] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[12:01:51:704] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[12:01:51:705] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[12:01:51:708] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[12:01:51:708] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[12:01:51:712] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[12:01:51:713] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[12:01:51:720] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[12:01:51:723] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[12:01:54:598] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[12:01:56:487] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[12:01:56:662] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[12:01:57:262] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.592秒
[12:02:02:402] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
[12:02:02:467] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 33 ms to scan 1 urls, producing 4 keys and 9 values 
[12:02:02:509] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 25 ms to scan 1 urls, producing 3 keys and 10 values 
[12:02:02:541] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 19 ms to scan 12 urls, producing 0 keys and 0 values 
[12:02:02:560] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 13 ms to scan 1 urls, producing 1 keys and 5 values 
[12:02:02:576] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
[12:02:02:601] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 17 ms to scan 1 urls, producing 2 keys and 8 values 
[12:02:02:659] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 54 ms to scan 12 urls, producing 0 keys and 0 values 
[12:02:03:519] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[12:02:03:536] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[12:02:03:550] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[12:02:03:561] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[12:02:03:590] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[12:02:03:706] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP taurus-oss 192.168.31.135:8085 register finished
[12:02:03:827] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started MySpringBootApplication in 22.731 seconds (JVM running for 25.204)
[17:45:54:814] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "dev"
[17:45:59:966] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[17:46:00:084] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[17:46:00:086] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[17:46:00:086] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[17:46:00:086] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[17:46:00:087] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[17:46:00:087] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[17:46:00:088] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[17:46:00:089] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[17:46:00:090] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-dev.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[17:46:00:091] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[17:46:00:091] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[17:46:00:092] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[17:46:00:161] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[17:46:00:678] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[17:46:00:687] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[17:46:01:832] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[17:46:01:871] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[17:46:02:764] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[17:46:02:778] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[17:46:02:779] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[17:46:02:782] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[17:46:02:783] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[17:46:02:785] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[17:46:02:786] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[17:46:02:788] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[17:46:02:794] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[17:46:07:217] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[17:46:08:859] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[17:46:09:030] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[17:46:09:607] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.569秒
[17:46:14:930] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 41 ms to scan 1 urls, producing 3 keys and 6 values 
[17:46:14:964] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
[17:46:14:984] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 15 ms to scan 1 urls, producing 3 keys and 10 values 
[17:46:15:023] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 33 ms to scan 12 urls, producing 0 keys and 0 values 
[17:46:15:055] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 25 ms to scan 1 urls, producing 1 keys and 5 values 
[17:46:15:076] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
[17:46:15:094] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
[17:46:15:114] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 17 ms to scan 12 urls, producing 0 keys and 0 values 
[17:46:16:116] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[17:46:16:136] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[17:46:16:157] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[17:46:16:174] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[17:46:16:209] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[17:46:16:338] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP taurus-oss 192.168.8.151:8085 register finished
[17:46:16:472] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started MySpringBootApplication in 26.388 seconds (JVM running for 28.729)
[20:05:10:173] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "dev"
[20:05:15:346] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[20:05:15:472] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[20:05:15:473] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[20:05:15:473] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[20:05:15:480] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[20:05:15:494] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[20:05:15:495] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[20:05:15:498] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[20:05:15:504] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[20:05:15:508] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-dev.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[20:05:15:509] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[20:05:15:509] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[20:05:15:509] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[20:05:15:627] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[20:05:16:192] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[20:05:16:196] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[20:05:17:623] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[20:05:17:676] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[20:05:18:816] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[20:05:18:866] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[20:05:18:876] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[20:05:18:877] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[20:05:18:879] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[20:05:18:883] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[20:05:18:887] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[20:05:18:890] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[20:05:18:898] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[20:05:21:605] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[20:05:23:580] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[20:05:23:705] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[20:05:24:306] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.593秒
[20:05:29:934] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 64 ms to scan 1 urls, producing 3 keys and 6 values 
[20:05:29:998] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 35 ms to scan 1 urls, producing 4 keys and 9 values 
[20:05:30:026] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 20 ms to scan 1 urls, producing 3 keys and 10 values 
[20:05:30:057] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 25 ms to scan 12 urls, producing 0 keys and 0 values 
[20:05:30:077] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 15 ms to scan 1 urls, producing 1 keys and 5 values 
[20:05:30:094] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
[20:05:30:117] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 17 ms to scan 1 urls, producing 2 keys and 8 values 
[20:05:30:137] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 15 ms to scan 12 urls, producing 0 keys and 0 values 
[20:05:31:124] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[20:05:31:154] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[20:05:31:181] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[20:05:31:198] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[20:05:31:262] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[20:05:31:492] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP taurus-oss 192.168.31.135:8085 register finished
[20:05:31:816] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started MySpringBootApplication in 26.043 seconds (JVM running for 28.156)
