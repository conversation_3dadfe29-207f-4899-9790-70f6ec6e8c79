[08:19:35:065] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "dev"
[08:19:40:289] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[08:19:40:397] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[08:19:40:398] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[08:19:40:398] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[08:19:40:399] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[08:19:40:401] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[08:19:40:401] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[08:19:40:402] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[08:19:40:402] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[08:19:40:402] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-dev.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[08:19:40:406] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[08:19:40:410] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[08:19:40:410] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[08:19:40:510] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[08:19:41:096] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[08:19:41:101] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[08:19:42:408] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[08:19:42:462] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[08:19:42:825] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[08:19:42:885] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[08:19:42:888] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[08:19:42:894] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[08:19:42:921] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[08:19:42:968] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[08:19:42:986] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[08:19:42:999] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[08:19:43:002] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[08:19:48:470] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[08:19:50:391] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[08:19:50:570] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[08:19:51:219] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.645秒
[08:19:56:579] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
[08:19:56:625] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 23 ms to scan 1 urls, producing 4 keys and 9 values 
[08:19:56:647] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 17 ms to scan 1 urls, producing 3 keys and 10 values 
[08:19:56:676] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 24 ms to scan 12 urls, producing 0 keys and 0 values 
[08:19:56:696] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 13 ms to scan 1 urls, producing 1 keys and 5 values 
[08:19:56:745] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 44 ms to scan 1 urls, producing 1 keys and 7 values 
[08:19:56:768] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 18 ms to scan 1 urls, producing 2 keys and 8 values 
[08:19:56:787] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 16 ms to scan 12 urls, producing 0 keys and 0 values 
[08:19:57:653] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[08:19:57:668] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[08:19:57:682] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[08:19:57:693] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[08:19:57:722] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[08:19:57:823] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP taurus-oss 192.168.8.151:8085 register finished
[08:19:57:947] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started MySpringBootApplication in 27.395 seconds (JVM running for 29.112)
