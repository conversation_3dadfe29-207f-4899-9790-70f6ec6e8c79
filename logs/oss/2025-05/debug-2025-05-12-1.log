[09:18:51:565] [INFO] - org.springframework.boot.SpringApplication.logStartupProfileInfo(SpringApplication.java:638) - The following 1 profile is active: "dev"
[09:18:54:050] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$AutoConfiguredMapperScannerRegistrar.registerBeanDefinitions(MybatisAutoConfiguration.java:252) - Searching for mappers annotated with @Mapper
[09:18:54:060] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$AutoConfiguredMapperScannerRegistrar.registerBeanDefinitions(MybatisAutoConfiguration.java:264) - Using auto-configuration base package 'com.taurus'
[09:18:55:303] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[09:18:55:364] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[09:18:55:364] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[09:18:55:365] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[09:18:55:368] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[09:18:55:369] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[09:18:55:371] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[09:18:55:371] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[09:18:55:372] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[09:18:55:372] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-dev.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[09:18:55:372] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[09:18:55:372] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[09:18:55:372] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[09:18:55:423] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[09:18:55:933] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[09:18:55:938] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[09:18:57:537] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[09:18:57:600] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[09:18:57:621] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[09:18:58:140] [DEBUG] - io.micrometer.core.util.internal.logging.InternalLoggerFactory.newDefaultFactory(InternalLoggerFactory.java:59) - Using SLF4J as the default logging framework
[09:18:58:272] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.lambda$null$2(DefaultLazyEncryptor.java:37) - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
[09:18:58:298] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[09:18:58:313] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
[09:18:58:314] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
[09:18:58:314] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
[09:18:58:316] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
[09:18:58:316] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
[09:18:58:320] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.NoIvGenerator
[09:18:58:321] [INFO] - com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor.getProperty(DefaultLazyEncryptor.java:91) - Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
[09:18:59:586] [DEBUG] - io.undertow.server.session.InMemorySessionManager.registerSessionListener(InMemorySessionManager.java:268) - Registered session listener io.undertow.servlet.core.SessionListenerBridge@788f89d8
[09:18:59:638] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[09:19:03:810] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[09:19:06:031] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[09:19:06:308] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[09:19:06:329] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.oss.mapper.OssFileMapper对应的Mapper
[09:19:06:709] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.oss.mapper.CategoryOssFileMapper对应的Mapper
[09:19:06:808] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.oss.mapper.OssFileAudioMapper对应的Mapper
[09:19:06:862] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.oss.mapper.OssRespositoryMapper对应的Mapper
[09:19:06:984] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.oss.mapper.ProductOssInfoMapper对应的Mapper
[09:19:07:023] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.oss.mapper.CategoryMapper对应的Mapper
[09:19:07:092] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.763秒
[09:19:10:377] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.o.c.OssFileController:
	{POST [/ossFile/uploadOssFile]}: uploadOssFile(HttpServletRequest,HttpServletResponse)
	{POST [/ossFile/uploadUrlFile]}: uploadUrlFile(JSONObject,HttpServletRequest)
	{GET [/ossFile/getOssFileById]}: getOssFileById(Integer)
	{POST [/ossFile/getOssFileList]}: getOssFileList(JSONObject)
	{POST [/ossFile/update]}: update(OssFile)
[09:19:10:385] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.o.c.OssObjectController:
	{GET [/ossObject/getAccessUrl]}: getAccessUrl(String,String)
	{GET [/ossObject/resizeOssPicFile]}: resizeOssPicFile(String)
	{GET [/ossObject/delete]}: delete(String,String)
[09:19:10:388] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.o.c.OssRespositoryController:
	{GET [/ossRespository/getAliUploadPolicy]}: getAliUploadPolicy(String,String,String)
	{GET [/ossRespository/getAliSts]}: getAliSts(String,String)
[09:19:10:433] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
	{ [/error]}: error(HttpServletRequest)
[09:19:10:513] [DEBUG] - org.springframework.web.servlet.handler.AbstractDetectingUrlHandlerMapping.detectHandlers(AbstractDetectingUrlHandlerMapping.java:86) - 'beanNameHandlerMapping' {}
[09:19:11:787] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$MapperScannerRegistrarNotFoundConfiguration.afterPropertiesSet(MybatisAutoConfiguration.java:305) - No org.mybatis.spring.mapper.MapperFactoryBean found.
[09:19:14:720] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[09:19:14:855] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 129 ms to scan 1 urls, producing 3 keys and 6 values 
[09:19:14:877] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype com.alibaba.nacos.api.remote.request.Request -> com.alibaba.nacos.api.remote.request.ServerRequest
[09:19:14:894] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[09:19:14:939] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 41 ms to scan 1 urls, producing 4 keys and 9 values 
[09:19:14:945] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype com.alibaba.nacos.api.remote.request.Request -> com.alibaba.nacos.api.remote.request.ServerRequest
[09:19:14:958] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[09:19:14:991] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 31 ms to scan 1 urls, producing 3 keys and 10 values 
[09:19:15:000] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar
file:/Users/<USER>/Documents/git/taurus-cloud/taurus-oss/target/classes/
file:/private/var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T/cp_bcxss9602qzcwyt2vdje35wk0.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/dnsns.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jaccess.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jfxrt.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunec.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/zipfs.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/nashorn.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/cldrdata.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/localedata.jar
[09:19:15:031] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 29 ms to scan 12 urls, producing 0 keys and 0 values 
[09:19:15:039] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[09:19:15:054] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 14 ms to scan 1 urls, producing 1 keys and 5 values 
[09:19:15:057] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[09:19:15:085] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 27 ms to scan 1 urls, producing 1 keys and 7 values 
[09:19:15:091] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[09:19:15:151] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 57 ms to scan 1 urls, producing 2 keys and 8 values 
[09:19:15:153] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype java.lang.Comparable -> java.lang.Enum
[09:19:15:154] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype java.io.Serializable -> java.lang.Enum
[09:19:15:163] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar
file:/Users/<USER>/Documents/git/taurus-cloud/taurus-oss/target/classes/
file:/private/var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T/cp_bcxss9602qzcwyt2vdje35wk0.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/dnsns.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jaccess.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jfxrt.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunec.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/zipfs.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/nashorn.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/cldrdata.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/localedata.jar
[09:19:15:184] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 19 ms to scan 12 urls, producing 0 keys and 0 values 
[09:19:16:660] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[09:19:16:684] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[09:19:16:708] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[09:19:16:725] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[09:19:16:759] [DEBUG] - org.xnio.XnioWorker$Builder.build(XnioWorker.java:1191) - Creating worker:null, pool size:64, max pool size:64, keep alive:60000, io threads:8, stack size:0
[09:19:16:789] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[09:19:16:818] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-1', selector sun.nio.ch.KQueueSelectorImpl@581f41e
[09:19:16:818] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-4', selector sun.nio.ch.KQueueSelectorImpl@3c24eaef
[09:19:16:819] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-6', selector sun.nio.ch.KQueueSelectorImpl@e81d6e5
[09:19:16:824] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-8', selector sun.nio.ch.KQueueSelectorImpl@6cf74b9a
[09:19:16:830] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-7', selector sun.nio.ch.KQueueSelectorImpl@6f83edb1
[09:19:16:819] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-5', selector sun.nio.ch.KQueueSelectorImpl@556fbc25
[09:19:16:834] [DEBUG] - io.undertow.Undertow.start(Undertow.java:160) - Configuring listener with protocol HTTP for interface 0.0.0.0 and port 8085
[09:19:16:816] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-2', selector sun.nio.ch.KQueueSelectorImpl@25634490
[09:19:16:817] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-3', selector sun.nio.ch.KQueueSelectorImpl@607e4470
[09:19:16:836] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 Accept', selector sun.nio.ch.KQueueSelectorImpl@1858fc47
[09:19:17:097] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP taurus-oss 192.168.8.151:8085 register finished
[09:19:17:473] [INFO] - org.springframework.boot.StartupInfoLogger.logStarted(StartupInfoLogger.java:61) - Started MySpringBootApplication in 31.322 seconds (JVM running for 33.209)
