[12:10:32:587] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: config/taurus-pay/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:68) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[12:10:32:602] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: taurus-pay/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:68) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[12:10:32:658] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: config/taurus-comment/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:68) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[12:10:32:669] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: taurus-comment/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:68) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[12:10:32:723] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: config/taurus-form-system/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:68) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[12:10:32:725] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: taurus-form-system/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:68) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[12:10:32:758] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: config/taurus-oss/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:68) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[12:10:32:760] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: taurus-oss/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:68) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[12:12:44:658] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: config/taurus-pay/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:63) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[12:12:44:669] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: taurus-pay/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:63) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[12:12:44:732] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: config/taurus-comment/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:63) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[12:12:44:742] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: taurus-comment/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:63) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[12:12:44:791] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: config/taurus-form-system/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:63) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[12:12:44:793] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: taurus-form-system/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:63) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[12:12:44:836] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: config/taurus-oss/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:63) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[12:12:44:839] [WARN] - com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:353) - 无法加载配置文件: wxinfo.properties
java.io.FileNotFoundException: taurus-oss/wxinfo.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method) ~[?:1.8.0_281]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_281]
	at java.io.FileInputStream.<init>(FileInputStream.java:93) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90) ~[?:1.8.0_281]
	at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188) ~[?:1.8.0_281]
	at org.springframework.core.io.UrlResource.getInputStream(UrlResource.java:187) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.fillProperties(PropertiesLoaderUtils.java:144) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.core.io.support.PropertiesLoaderUtils.loadProperties(PropertiesLoaderUtils.java:133) ~[spring-core-5.3.31.jar:5.3.31]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadResourcesIntoProperties(MicroserviceConfigManager.java:310) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadServiceConfigurations(MicroserviceConfigManager.java:257) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.loadMicroserviceConfigs(MicroserviceConfigManager.java:146) [classes/:?]
	at com.taurus.monolith.config.MicroserviceConfigManager.initialize(MicroserviceConfigManager.java:84) [classes/:?]
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:605) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:374) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:63) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
[15:58:05:421] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:39:49:535] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:40:00:793] [ERROR] - com.taurus.monolith.config.MultiServiceNacosRegistrar.registerService(MultiServiceNacosRegistrar.java:138) - 微服务[taurus-comment]注册失败: Request nacos server failed: 
com.alibaba.nacos.api.exception.NacosException: Request nacos server failed: 
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:290) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doRegisterService(NamingGrpcClientProxy.java:129) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.registerService(NamingGrpcClientProxy.java:115) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.registerService(NamingClientProxyDelegate.java:94) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:145) ~[nacos-client-2.0.4.jar:?]
	at com.taurus.monolith.config.MultiServiceNacosRegistrar.registerService(MultiServiceNacosRegistrar.java:131) ~[classes/:?]
	at com.taurus.monolith.config.MultiServiceNacosRegistrar.start(MultiServiceNacosRegistrar.java:64) ~[classes/:?]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:179) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:357) ~[spring-context-5.3.31.jar:5.3.31]
	at java.lang.Iterable.forEach(Iterable.java:75) [?:1.8.0_281]
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:156) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:124) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:946) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:594) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
Caused by: com.alibaba.nacos.api.exception.NacosException: user not found!
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:282) ~[nacos-client-2.0.4.jar:?]
	... 24 more
[16:40:00:848] [ERROR] - com.taurus.monolith.config.MultiServiceNacosRegistrar.registerService(MultiServiceNacosRegistrar.java:138) - 微服务[taurus-pay]注册失败: Request nacos server failed: 
com.alibaba.nacos.api.exception.NacosException: Request nacos server failed: 
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:290) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doRegisterService(NamingGrpcClientProxy.java:129) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.registerService(NamingGrpcClientProxy.java:115) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.registerService(NamingClientProxyDelegate.java:94) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:145) ~[nacos-client-2.0.4.jar:?]
	at com.taurus.monolith.config.MultiServiceNacosRegistrar.registerService(MultiServiceNacosRegistrar.java:131) ~[classes/:?]
	at com.taurus.monolith.config.MultiServiceNacosRegistrar.start(MultiServiceNacosRegistrar.java:64) ~[classes/:?]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:179) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:357) ~[spring-context-5.3.31.jar:5.3.31]
	at java.lang.Iterable.forEach(Iterable.java:75) [?:1.8.0_281]
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:156) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:124) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:946) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:594) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
Caused by: com.alibaba.nacos.api.exception.NacosException: user not found!
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:282) ~[nacos-client-2.0.4.jar:?]
	... 24 more
[16:40:00:861] [ERROR] - com.taurus.monolith.config.MultiServiceNacosRegistrar.registerService(MultiServiceNacosRegistrar.java:138) - 微服务[taurus-form-system]注册失败: Request nacos server failed: 
com.alibaba.nacos.api.exception.NacosException: Request nacos server failed: 
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:290) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doRegisterService(NamingGrpcClientProxy.java:129) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.registerService(NamingGrpcClientProxy.java:115) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.registerService(NamingClientProxyDelegate.java:94) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:145) ~[nacos-client-2.0.4.jar:?]
	at com.taurus.monolith.config.MultiServiceNacosRegistrar.registerService(MultiServiceNacosRegistrar.java:131) ~[classes/:?]
	at com.taurus.monolith.config.MultiServiceNacosRegistrar.start(MultiServiceNacosRegistrar.java:64) ~[classes/:?]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:179) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:357) ~[spring-context-5.3.31.jar:5.3.31]
	at java.lang.Iterable.forEach(Iterable.java:75) [?:1.8.0_281]
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:156) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:124) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:946) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:594) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
Caused by: com.alibaba.nacos.api.exception.NacosException: user not found!
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:282) ~[nacos-client-2.0.4.jar:?]
	... 24 more
[16:40:00:886] [ERROR] - com.taurus.monolith.config.MultiServiceNacosRegistrar.registerService(MultiServiceNacosRegistrar.java:138) - 微服务[taurus-oss]注册失败: Request nacos server failed: 
com.alibaba.nacos.api.exception.NacosException: Request nacos server failed: 
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:290) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doRegisterService(NamingGrpcClientProxy.java:129) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.registerService(NamingGrpcClientProxy.java:115) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.registerService(NamingClientProxyDelegate.java:94) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:145) ~[nacos-client-2.0.4.jar:?]
	at com.taurus.monolith.config.MultiServiceNacosRegistrar.registerService(MultiServiceNacosRegistrar.java:131) ~[classes/:?]
	at com.taurus.monolith.config.MultiServiceNacosRegistrar.start(MultiServiceNacosRegistrar.java:64) ~[classes/:?]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:179) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:357) ~[spring-context-5.3.31.jar:5.3.31]
	at java.lang.Iterable.forEach(Iterable.java:75) [?:1.8.0_281]
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:156) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:124) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:946) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:594) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
Caused by: com.alibaba.nacos.api.exception.NacosException: user not found!
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:282) ~[nacos-client-2.0.4.jar:?]
	... 24 more
[16:40:02:678] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[16:44:36:577] [ERROR] - com.taurus.monolith.config.MultiServiceNacosRegistrar.stop(MultiServiceNacosRegistrar.java:195) - 注销微服务[taurus-comment]失败: Request nacos server failed: 
[16:44:36:588] [ERROR] - com.taurus.monolith.config.MultiServiceNacosRegistrar.stop(MultiServiceNacosRegistrar.java:195) - 注销微服务[taurus-pay]失败: Request nacos server failed: 
[16:44:36:598] [ERROR] - com.taurus.monolith.config.MultiServiceNacosRegistrar.stop(MultiServiceNacosRegistrar.java:195) - 注销微服务[taurus-form-system]失败: Request nacos server failed: 
[16:44:36:603] [ERROR] - com.taurus.monolith.config.MultiServiceNacosRegistrar.stop(MultiServiceNacosRegistrar.java:195) - 注销微服务[taurus-oss]失败: Request nacos server failed: 
[16:44:40:661] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:44:44:705] [ERROR] - com.taurus.monolith.config.MultiServiceNacosRegistrar.registerService(MultiServiceNacosRegistrar.java:138) - 微服务[taurus-comment]注册失败: Request nacos server failed: 
com.alibaba.nacos.api.exception.NacosException: Request nacos server failed: 
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:290) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doRegisterService(NamingGrpcClientProxy.java:129) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.registerService(NamingGrpcClientProxy.java:115) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.registerService(NamingClientProxyDelegate.java:94) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:145) ~[nacos-client-2.0.4.jar:?]
	at com.taurus.monolith.config.MultiServiceNacosRegistrar.registerService(MultiServiceNacosRegistrar.java:131) ~[classes/:?]
	at com.taurus.monolith.config.MultiServiceNacosRegistrar.start(MultiServiceNacosRegistrar.java:64) ~[classes/:?]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:179) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:357) ~[spring-context-5.3.31.jar:5.3.31]
	at java.lang.Iterable.forEach(Iterable.java:75) [?:1.8.0_281]
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:156) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:124) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:946) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:594) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
Caused by: com.alibaba.nacos.api.exception.NacosException: user not found!
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:282) ~[nacos-client-2.0.4.jar:?]
	... 24 more
[16:44:44:732] [ERROR] - com.taurus.monolith.config.MultiServiceNacosRegistrar.registerService(MultiServiceNacosRegistrar.java:138) - 微服务[taurus-pay]注册失败: Request nacos server failed: 
com.alibaba.nacos.api.exception.NacosException: Request nacos server failed: 
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:290) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doRegisterService(NamingGrpcClientProxy.java:129) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.registerService(NamingGrpcClientProxy.java:115) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.registerService(NamingClientProxyDelegate.java:94) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:145) ~[nacos-client-2.0.4.jar:?]
	at com.taurus.monolith.config.MultiServiceNacosRegistrar.registerService(MultiServiceNacosRegistrar.java:131) ~[classes/:?]
	at com.taurus.monolith.config.MultiServiceNacosRegistrar.start(MultiServiceNacosRegistrar.java:64) ~[classes/:?]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:179) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:357) ~[spring-context-5.3.31.jar:5.3.31]
	at java.lang.Iterable.forEach(Iterable.java:75) [?:1.8.0_281]
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:156) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:124) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:946) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:594) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
Caused by: com.alibaba.nacos.api.exception.NacosException: user not found!
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:282) ~[nacos-client-2.0.4.jar:?]
	... 24 more
[16:44:44:757] [ERROR] - com.taurus.monolith.config.MultiServiceNacosRegistrar.registerService(MultiServiceNacosRegistrar.java:138) - 微服务[taurus-form-system]注册失败: Request nacos server failed: 
com.alibaba.nacos.api.exception.NacosException: Request nacos server failed: 
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:290) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doRegisterService(NamingGrpcClientProxy.java:129) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.registerService(NamingGrpcClientProxy.java:115) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.registerService(NamingClientProxyDelegate.java:94) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:145) ~[nacos-client-2.0.4.jar:?]
	at com.taurus.monolith.config.MultiServiceNacosRegistrar.registerService(MultiServiceNacosRegistrar.java:131) ~[classes/:?]
	at com.taurus.monolith.config.MultiServiceNacosRegistrar.start(MultiServiceNacosRegistrar.java:64) ~[classes/:?]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:179) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:357) ~[spring-context-5.3.31.jar:5.3.31]
	at java.lang.Iterable.forEach(Iterable.java:75) [?:1.8.0_281]
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:156) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:124) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:946) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:594) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
Caused by: com.alibaba.nacos.api.exception.NacosException: user not found!
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:282) ~[nacos-client-2.0.4.jar:?]
	... 24 more
[16:44:44:776] [ERROR] - com.taurus.monolith.config.MultiServiceNacosRegistrar.registerService(MultiServiceNacosRegistrar.java:138) - 微服务[taurus-oss]注册失败: Request nacos server failed: 
com.alibaba.nacos.api.exception.NacosException: Request nacos server failed: 
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:290) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doRegisterService(NamingGrpcClientProxy.java:129) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.registerService(NamingGrpcClientProxy.java:115) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.registerService(NamingClientProxyDelegate.java:94) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:145) ~[nacos-client-2.0.4.jar:?]
	at com.taurus.monolith.config.MultiServiceNacosRegistrar.registerService(MultiServiceNacosRegistrar.java:131) ~[classes/:?]
	at com.taurus.monolith.config.MultiServiceNacosRegistrar.start(MultiServiceNacosRegistrar.java:64) ~[classes/:?]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:179) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:357) ~[spring-context-5.3.31.jar:5.3.31]
	at java.lang.Iterable.forEach(Iterable.java:75) [?:1.8.0_281]
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:156) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:124) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:946) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:594) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
Caused by: com.alibaba.nacos.api.exception.NacosException: user not found!
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:282) ~[nacos-client-2.0.4.jar:?]
	... 24 more
[16:44:46:018] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[16:44:46:597] [ERROR] - com.taurus.monolith.config.MultiServiceNacosRegistrar.stop(MultiServiceNacosRegistrar.java:195) - 注销微服务[taurus-comment]失败: Request nacos server failed: 
[16:44:46:670] [ERROR] - com.taurus.monolith.config.MultiServiceNacosRegistrar.stop(MultiServiceNacosRegistrar.java:195) - 注销微服务[taurus-pay]失败: Request nacos server failed: 
[16:44:46:678] [ERROR] - com.taurus.monolith.config.MultiServiceNacosRegistrar.stop(MultiServiceNacosRegistrar.java:195) - 注销微服务[taurus-form-system]失败: Request nacos server failed: 
[16:44:46:696] [ERROR] - com.taurus.monolith.config.MultiServiceNacosRegistrar.stop(MultiServiceNacosRegistrar.java:195) - 注销微服务[taurus-oss]失败: Request nacos server failed: 
[16:44:50:184] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:44:53:037] [ERROR] - com.taurus.monolith.config.MultiServiceNacosRegistrar.registerService(MultiServiceNacosRegistrar.java:138) - 微服务[taurus-comment]注册失败: Request nacos server failed: 
com.alibaba.nacos.api.exception.NacosException: Request nacos server failed: 
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:290) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doRegisterService(NamingGrpcClientProxy.java:129) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.registerService(NamingGrpcClientProxy.java:115) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.registerService(NamingClientProxyDelegate.java:94) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:145) ~[nacos-client-2.0.4.jar:?]
	at com.taurus.monolith.config.MultiServiceNacosRegistrar.registerService(MultiServiceNacosRegistrar.java:131) ~[classes/:?]
	at com.taurus.monolith.config.MultiServiceNacosRegistrar.start(MultiServiceNacosRegistrar.java:64) ~[classes/:?]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:179) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:357) ~[spring-context-5.3.31.jar:5.3.31]
	at java.lang.Iterable.forEach(Iterable.java:75) [?:1.8.0_281]
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:156) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:124) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:946) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:594) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
Caused by: com.alibaba.nacos.api.exception.NacosException: user not found!
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:282) ~[nacos-client-2.0.4.jar:?]
	... 24 more
[16:44:53:048] [ERROR] - com.taurus.monolith.config.MultiServiceNacosRegistrar.registerService(MultiServiceNacosRegistrar.java:138) - 微服务[taurus-pay]注册失败: Request nacos server failed: 
com.alibaba.nacos.api.exception.NacosException: Request nacos server failed: 
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:290) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doRegisterService(NamingGrpcClientProxy.java:129) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.registerService(NamingGrpcClientProxy.java:115) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.registerService(NamingClientProxyDelegate.java:94) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:145) ~[nacos-client-2.0.4.jar:?]
	at com.taurus.monolith.config.MultiServiceNacosRegistrar.registerService(MultiServiceNacosRegistrar.java:131) ~[classes/:?]
	at com.taurus.monolith.config.MultiServiceNacosRegistrar.start(MultiServiceNacosRegistrar.java:64) ~[classes/:?]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:179) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:357) ~[spring-context-5.3.31.jar:5.3.31]
	at java.lang.Iterable.forEach(Iterable.java:75) [?:1.8.0_281]
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:156) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:124) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:946) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:594) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
Caused by: com.alibaba.nacos.api.exception.NacosException: user not found!
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:282) ~[nacos-client-2.0.4.jar:?]
	... 24 more
[16:44:53:053] [ERROR] - com.taurus.monolith.config.MultiServiceNacosRegistrar.registerService(MultiServiceNacosRegistrar.java:138) - 微服务[taurus-form-system]注册失败: Request nacos server failed: 
com.alibaba.nacos.api.exception.NacosException: Request nacos server failed: 
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:290) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doRegisterService(NamingGrpcClientProxy.java:129) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.registerService(NamingGrpcClientProxy.java:115) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.registerService(NamingClientProxyDelegate.java:94) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:145) ~[nacos-client-2.0.4.jar:?]
	at com.taurus.monolith.config.MultiServiceNacosRegistrar.registerService(MultiServiceNacosRegistrar.java:131) ~[classes/:?]
	at com.taurus.monolith.config.MultiServiceNacosRegistrar.start(MultiServiceNacosRegistrar.java:64) ~[classes/:?]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:179) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:357) ~[spring-context-5.3.31.jar:5.3.31]
	at java.lang.Iterable.forEach(Iterable.java:75) [?:1.8.0_281]
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:156) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:124) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:946) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:594) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
Caused by: com.alibaba.nacos.api.exception.NacosException: user not found!
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:282) ~[nacos-client-2.0.4.jar:?]
	... 24 more
[16:44:53:057] [ERROR] - com.taurus.monolith.config.MultiServiceNacosRegistrar.registerService(MultiServiceNacosRegistrar.java:138) - 微服务[taurus-oss]注册失败: Request nacos server failed: 
com.alibaba.nacos.api.exception.NacosException: Request nacos server failed: 
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:290) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doRegisterService(NamingGrpcClientProxy.java:129) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.registerService(NamingGrpcClientProxy.java:115) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.registerService(NamingClientProxyDelegate.java:94) ~[nacos-client-2.0.4.jar:?]
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:145) ~[nacos-client-2.0.4.jar:?]
	at com.taurus.monolith.config.MultiServiceNacosRegistrar.registerService(MultiServiceNacosRegistrar.java:131) ~[classes/:?]
	at com.taurus.monolith.config.MultiServiceNacosRegistrar.start(MultiServiceNacosRegistrar.java:64) ~[classes/:?]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:179) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:357) ~[spring-context-5.3.31.jar:5.3.31]
	at java.lang.Iterable.forEach(Iterable.java:75) [?:1.8.0_281]
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:156) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:124) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:946) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:594) [spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at com.taurus.monolith.TaurusMonolithApplication.main(TaurusMonolithApplication.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_281]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_281]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_281]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_281]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-2.7.18.jar:2.7.18]
Caused by: com.alibaba.nacos.api.exception.NacosException: user not found!
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:282) ~[nacos-client-2.0.4.jar:?]
	... 24 more
[16:44:54:161] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[16:44:59:988] [ERROR] - com.taurus.monolith.config.MultiServiceNacosRegistrar.stop(MultiServiceNacosRegistrar.java:195) - 注销微服务[taurus-comment]失败: Request nacos server failed: 
[16:44:59:996] [ERROR] - com.taurus.monolith.config.MultiServiceNacosRegistrar.stop(MultiServiceNacosRegistrar.java:195) - 注销微服务[taurus-pay]失败: Request nacos server failed: 
[16:45:00:000] [ERROR] - com.taurus.monolith.config.MultiServiceNacosRegistrar.stop(MultiServiceNacosRegistrar.java:195) - 注销微服务[taurus-form-system]失败: Request nacos server failed: 
[16:45:00:009] [ERROR] - com.taurus.monolith.config.MultiServiceNacosRegistrar.stop(MultiServiceNacosRegistrar.java:195) - 注销微服务[taurus-oss]失败: Request nacos server failed: 
[16:45:02:978] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:45:12:532] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:45:16:030] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[16:45:25:437] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:45:32:108] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:45:35:621] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[16:47:48:745] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:47:52:722] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[16:48:48:705] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:48:52:599] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[16:48:56:265] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:49:00:032] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[16:50:09:174] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:50:12:707] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[16:50:52:175] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:50:55:805] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[16:51:07:507] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:51:11:108] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[16:51:20:127] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:51:23:948] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[16:51:51:335] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:51:54:570] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[16:52:21:324] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:52:24:431] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[16:54:57:337] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:55:00:946] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[16:55:06:573] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:55:09:976] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[16:55:21:229] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:55:24:392] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[16:55:39:999] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:55:43:464] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[16:55:46:538] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:55:50:217] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[16:56:32:852] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:56:38:341] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[16:56:47:981] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:56:52:225] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[16:56:58:250] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[16:57:01:773] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[17:03:05:968] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[17:03:09:622] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[17:03:27:639] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[17:03:31:484] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[17:03:44:830] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[17:03:48:614] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[17:04:15:198] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[17:04:18:665] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[18:33:49:064] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[18:34:18:553] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[18:34:21:803] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[18:34:29:511] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[18:34:33:205] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[18:36:04:964] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[18:36:08:466] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[18:36:38:252] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[18:36:41:478] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[18:37:06:065] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[18:37:09:380] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[18:37:21:606] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[18:37:25:184] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[18:37:35:520] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[18:37:39:425] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[18:37:53:849] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[18:37:58:215] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[18:38:53:219] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[19:28:12:133] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.properties]未找到或加载失败
[19:28:12:135] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.yml]未找到或加载失败
[19:28:12:135] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:227) - 微服务[taurus-form-system]未加载到任何配置
[19:28:12:143] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.properties]未找到或加载失败
[19:28:12:146] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.yml]未找到或加载失败
[19:28:12:146] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:227) - 微服务[taurus-pay]未加载到任何配置
[19:28:12:155] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.properties]未找到或加载失败
[19:28:12:158] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.yml]未找到或加载失败
[19:28:12:158] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:227) - 微服务[taurus-comment]未加载到任何配置
[19:28:12:169] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.properties]未找到或加载失败
[19:28:12:173] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.yml]未找到或加载失败
[19:28:12:174] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:227) - 微服务[taurus-oss]未加载到任何配置
[19:28:17:243] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[19:37:14:515] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.properties]未找到或加载失败
[19:37:14:517] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.yml]未找到或加载失败
[19:37:14:517] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:227) - 微服务[taurus-form-system]未加载到任何配置
[19:37:14:532] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.properties]未找到或加载失败
[19:37:14:533] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.yml]未找到或加载失败
[19:37:14:533] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:227) - 微服务[taurus-pay]未加载到任何配置
[19:37:14:536] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.properties]未找到或加载失败
[19:37:14:541] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.yml]未找到或加载失败
[19:37:14:541] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:227) - 微服务[taurus-comment]未加载到任何配置
[19:37:14:550] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.properties]未找到或加载失败
[19:37:14:551] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.yml]未找到或加载失败
[19:37:14:551] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:227) - 微服务[taurus-oss]未加载到任何配置
[19:37:19:919] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[19:47:22:465] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.properties]未找到或加载失败
[19:47:22:466] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.yml]未找到或加载失败
[19:47:22:466] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:227) - 微服务[taurus-form-system]未加载到任何配置
[19:47:22:477] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.properties]未找到或加载失败
[19:47:22:480] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.yml]未找到或加载失败
[19:47:22:480] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:227) - 微服务[taurus-pay]未加载到任何配置
[19:47:22:485] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.properties]未找到或加载失败
[19:47:22:487] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.yml]未找到或加载失败
[19:47:22:487] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:227) - 微服务[taurus-comment]未加载到任何配置
[19:47:22:497] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.properties]未找到或加载失败
[19:47:22:498] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.yml]未找到或加载失败
[19:47:22:498] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:227) - 微服务[taurus-oss]未加载到任何配置
[19:47:27:902] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[19:52:53:594] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[19:53:02:828] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-pay: 数据源配置可能缺失
[19:53:02:829] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-comment: 数据源配置可能缺失
[19:53:02:829] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-form-system: 数据源配置可能缺失
[19:53:02:829] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-oss: 数据源配置可能缺失
[19:53:09:466] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[19:56:09:238] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[19:56:14:422] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-pay: 数据源配置可能缺失
[19:56:14:423] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-comment: 数据源配置可能缺失
[19:56:14:423] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-form-system: 数据源配置可能缺失
[19:56:14:423] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-oss: 数据源配置可能缺失
[19:56:17:054] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[19:56:34:609] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[19:56:39:352] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-pay: 数据源配置可能缺失
[19:56:39:352] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-comment: 数据源配置可能缺失
[19:56:39:352] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-form-system: 数据源配置可能缺失
[19:56:39:352] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-oss: 数据源配置可能缺失
[19:56:41:857] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[19:57:08:104] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[19:57:12:511] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-pay: 数据源配置可能缺失
[19:57:12:511] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-comment: 数据源配置可能缺失
[19:57:12:511] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-form-system: 数据源配置可能缺失
[19:57:12:511] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-oss: 数据源配置可能缺失
[19:57:14:728] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[19:57:17:831] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[19:57:22:806] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-pay: 数据源配置可能缺失
[19:57:22:806] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-comment: 数据源配置可能缺失
[19:57:22:806] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-form-system: 数据源配置可能缺失
[19:57:22:806] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-oss: 数据源配置可能缺失
[19:57:25:054] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[20:25:19:120] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[20:35:04:080] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[20:36:25:835] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:05:11:328] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:05:11:952] [ERROR] - com.taurus.monolith.config.ConfigurationValidator.validateWeChatPayConfig(ConfigurationValidator.java:59) - ✗ wx.notifyUrl 配置缺失或为空
[21:05:11:954] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-pay: 数据源配置可能缺失
[21:05:11:955] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-comment: 数据源配置可能缺失
[21:05:11:955] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-form-system: 数据源配置可能缺失
[21:05:11:955] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-oss: 数据源配置可能缺失
[21:05:11:958] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateConfigurations(ConfigurationValidator.java:42) - ⚠ 部分配置验证失败，请检查相关配置
[21:05:11:970] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-form-system/src/main/resources/application.yml
[21:05:11:973] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-form-system/src/main/resources/application.properties
[21:05:11:984] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-form-system/src/main/resources/application-dev.properties
[21:05:11:989] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-form-system/src/main/resources/application.properties
[21:05:11:990] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:326) -     配置文件[application.properties]未找到或加载失败
[21:05:11:995] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-form-system/src/main/resources/application.yml
[21:05:11:996] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:326) -     配置文件[application.yml]未找到或加载失败
[21:05:11:996] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:331) - 微服务[taurus-form-system]未加载到任何配置
[21:05:12:008] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-pay/src/main/resources/application.yml
[21:05:12:010] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-pay/src/main/resources/application.properties
[21:05:12:017] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-pay/src/main/resources/application-dev.properties
[21:05:12:021] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-pay/src/main/resources/application.properties
[21:05:12:021] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:326) -     配置文件[application.properties]未找到或加载失败
[21:05:12:026] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-pay/src/main/resources/application.yml
[21:05:12:027] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:326) -     配置文件[application.yml]未找到或加载失败
[21:05:12:028] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:331) - 微服务[taurus-pay]未加载到任何配置
[21:05:12:030] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-comment/src/main/resources/application.yml
[21:05:12:033] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-comment/src/main/resources/application.properties
[21:05:12:036] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-comment/src/main/resources/application-dev.properties
[21:05:12:038] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-comment/src/main/resources/application.properties
[21:05:12:038] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:326) -     配置文件[application.properties]未找到或加载失败
[21:05:12:039] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-comment/src/main/resources/application.yml
[21:05:12:039] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:326) -     配置文件[application.yml]未找到或加载失败
[21:05:12:040] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:331) - 微服务[taurus-comment]未加载到任何配置
[21:05:12:044] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-oss/src/main/resources/application.yml
[21:05:12:048] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-oss/src/main/resources/application.properties
[21:05:12:052] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-oss/src/main/resources/application-dev.properties
[21:05:12:059] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-oss/src/main/resources/application.properties
[21:05:12:059] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:326) -     配置文件[application.properties]未找到或加载失败
[21:05:12:061] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-oss/src/main/resources/application.yml
[21:05:12:061] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:326) -     配置文件[application.yml]未找到或加载失败
[21:05:12:061] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:331) - 微服务[taurus-oss]未加载到任何配置
[21:05:12:063] [ERROR] - com.taurus.monolith.config.DynamicConfigLoader.validateCriticalConfigurations(DynamicConfigLoader.java:177) - ✗ 关键配置wx.notifyUrl未能成功加载！
[21:05:12:063] [ERROR] - com.taurus.monolith.config.DynamicConfigLoader.validateCriticalConfigurations(DynamicConfigLoader.java:178) - 请检查以下可能原因：
[21:05:12:063] [ERROR] - com.taurus.monolith.config.DynamicConfigLoader.validateCriticalConfigurations(DynamicConfigLoader.java:179) -   1. 支付服务配置文件路径是否正确
[21:05:12:063] [ERROR] - com.taurus.monolith.config.DynamicConfigLoader.validateCriticalConfigurations(DynamicConfigLoader.java:180) -   2. 配置文件是否存在且可读
[21:05:12:063] [ERROR] - com.taurus.monolith.config.DynamicConfigLoader.validateCriticalConfigurations(DynamicConfigLoader.java:181) -   3. Profile激活是否正确
[21:05:12:063] [ERROR] - com.taurus.monolith.config.DynamicConfigLoader.validateCriticalConfigurations(DynamicConfigLoader.java:182) -   4. 配置过滤规则是否正确
[21:05:12:063] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.validateCriticalConfigurations(DynamicConfigLoader.java:185) - 启动应急配置加载机制，直接从支付服务配置文件读取wx.notifyUrl...
[21:05:12:065] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-pay/src/main/resources/application-test.properties
[21:05:12:067] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-pay/src/main/resources/application.properties
[21:05:12:068] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-pay/src/main/resources/application-dev.properties
[21:05:12:070] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-pay/src/main/resources/application-prod-172.properties
[21:05:12:070] [ERROR] - com.taurus.monolith.config.DynamicConfigLoader.tryEmergencyLoadWxNotifyUrl(DynamicConfigLoader.java:236) - ✗ 应急配置加载也失败了，请检查支付服务配置文件
[21:05:31:963] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:05:32:647] [ERROR] - com.taurus.monolith.config.ConfigurationValidator.validateWeChatPayConfig(ConfigurationValidator.java:59) - ✗ wx.notifyUrl 配置缺失或为空
[21:05:32:649] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-pay: 数据源配置可能缺失
[21:05:32:650] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-comment: 数据源配置可能缺失
[21:05:32:651] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-form-system: 数据源配置可能缺失
[21:05:32:651] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-oss: 数据源配置可能缺失
[21:05:32:654] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateConfigurations(ConfigurationValidator.java:42) - ⚠ 部分配置验证失败，请检查相关配置
[21:05:32:674] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-form-system/src/main/resources/application.yml
[21:05:32:675] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-form-system/src/main/resources/application.properties
[21:05:32:679] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-form-system/src/main/resources/application-dev.properties
[21:05:32:694] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-form-system/src/main/resources/application.properties
[21:05:32:695] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:326) -     配置文件[application.properties]未找到或加载失败
[21:05:32:697] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-form-system/src/main/resources/application.yml
[21:05:32:697] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:326) -     配置文件[application.yml]未找到或加载失败
[21:05:32:698] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:331) - 微服务[taurus-form-system]未加载到任何配置
[21:05:32:705] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-pay/src/main/resources/application.yml
[21:05:32:708] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-pay/src/main/resources/application.properties
[21:05:32:717] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-pay/src/main/resources/application-dev.properties
[21:05:32:721] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-pay/src/main/resources/application.properties
[21:05:32:722] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:326) -     配置文件[application.properties]未找到或加载失败
[21:05:32:723] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-pay/src/main/resources/application.yml
[21:05:32:724] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:326) -     配置文件[application.yml]未找到或加载失败
[21:05:32:724] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:331) - 微服务[taurus-pay]未加载到任何配置
[21:05:32:725] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-comment/src/main/resources/application.yml
[21:05:32:734] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-comment/src/main/resources/application.properties
[21:05:32:740] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-comment/src/main/resources/application-dev.properties
[21:05:32:743] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-comment/src/main/resources/application.properties
[21:05:32:743] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:326) -     配置文件[application.properties]未找到或加载失败
[21:05:32:744] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-comment/src/main/resources/application.yml
[21:05:32:744] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:326) -     配置文件[application.yml]未找到或加载失败
[21:05:32:744] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:331) - 微服务[taurus-comment]未加载到任何配置
[21:05:32:748] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-oss/src/main/resources/application.yml
[21:05:32:760] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-oss/src/main/resources/application.properties
[21:05:32:763] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-oss/src/main/resources/application-dev.properties
[21:05:32:765] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-oss/src/main/resources/application.properties
[21:05:32:766] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:326) -     配置文件[application.properties]未找到或加载失败
[21:05:32:768] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-oss/src/main/resources/application.yml
[21:05:32:768] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:326) -     配置文件[application.yml]未找到或加载失败
[21:05:32:769] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:331) - 微服务[taurus-oss]未加载到任何配置
[21:05:32:770] [ERROR] - com.taurus.monolith.config.DynamicConfigLoader.validateCriticalConfigurations(DynamicConfigLoader.java:177) - ✗ 关键配置wx.notifyUrl未能成功加载！
[21:05:32:771] [ERROR] - com.taurus.monolith.config.DynamicConfigLoader.validateCriticalConfigurations(DynamicConfigLoader.java:178) - 请检查以下可能原因：
[21:05:32:771] [ERROR] - com.taurus.monolith.config.DynamicConfigLoader.validateCriticalConfigurations(DynamicConfigLoader.java:179) -   1. 支付服务配置文件路径是否正确
[21:05:32:771] [ERROR] - com.taurus.monolith.config.DynamicConfigLoader.validateCriticalConfigurations(DynamicConfigLoader.java:180) -   2. 配置文件是否存在且可读
[21:05:32:771] [ERROR] - com.taurus.monolith.config.DynamicConfigLoader.validateCriticalConfigurations(DynamicConfigLoader.java:181) -   3. Profile激活是否正确
[21:05:32:771] [ERROR] - com.taurus.monolith.config.DynamicConfigLoader.validateCriticalConfigurations(DynamicConfigLoader.java:182) -   4. 配置过滤规则是否正确
[21:05:32:771] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.validateCriticalConfigurations(DynamicConfigLoader.java:185) - 启动应急配置加载机制，直接从支付服务配置文件读取wx.notifyUrl...
[21:05:32:774] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-pay/src/main/resources/application-test.properties
[21:05:32:775] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-pay/src/main/resources/application.properties
[21:05:32:777] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-pay/src/main/resources/application-dev.properties
[21:05:32:778] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.findConfigResource(DynamicConfigLoader.java:423) -       ✗ 所有路径都无法找到配置文件: ../taurus-pay/src/main/resources/application-prod-172.properties
[21:05:32:778] [ERROR] - com.taurus.monolith.config.DynamicConfigLoader.tryEmergencyLoadWxNotifyUrl(DynamicConfigLoader.java:236) - ✗ 应急配置加载也失败了，请检查支付服务配置文件
[21:08:49:647] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:08:59:411] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-pay: 数据源配置可能缺失
[21:08:59:411] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-comment: 数据源配置可能缺失
[21:08:59:411] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-form-system: 数据源配置可能缺失
[21:08:59:411] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-oss: 数据源配置可能缺失
[21:09:06:032] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[21:10:43:862] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:11:19:990] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:11:46:653] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:11:54:529] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:12:23:640] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:12:50:098] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:16:11:931] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:16:48:119] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:18:01:110] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:18:05:385] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-pay: 数据源配置可能缺失
[21:18:05:385] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-comment: 数据源配置可能缺失
[21:18:05:385] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-form-system: 数据源配置可能缺失
[21:18:05:385] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-oss: 数据源配置可能缺失
[21:18:08:214] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[21:20:22:157] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:20:27:055] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-pay: 数据源配置可能缺失
[21:20:27:055] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-comment: 数据源配置可能缺失
[21:20:27:056] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-form-system: 数据源配置可能缺失
[21:20:27:056] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-oss: 数据源配置可能缺失
[21:20:29:147] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[21:24:18:433] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:24:33:324] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:24:37:496] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-pay: 数据源配置可能缺失
[21:24:37:497] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-comment: 数据源配置可能缺失
[21:24:37:497] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-form-system: 数据源配置可能缺失
[21:24:37:497] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-oss: 数据源配置可能缺失
[21:24:39:580] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[21:25:01:506] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:25:10:487] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-pay: 数据源配置可能缺失
[21:25:10:487] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-comment: 数据源配置可能缺失
[21:25:10:488] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-form-system: 数据源配置可能缺失
[21:25:10:488] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-oss: 数据源配置可能缺失
[21:26:04:517] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:26:13:709] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-pay: 数据源配置可能缺失
[21:26:13:709] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-comment: 数据源配置可能缺失
[21:26:13:710] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-form-system: 数据源配置可能缺失
[21:26:13:710] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-oss: 数据源配置可能缺失
[21:26:20:539] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[21:28:27:764] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:28:33:010] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-pay: 数据源配置可能缺失
[21:28:33:010] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-comment: 数据源配置可能缺失
[21:28:33:011] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-form-system: 数据源配置可能缺失
[21:28:33:011] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-oss: 数据源配置可能缺失
[21:28:35:992] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[21:28:38:949] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:28:46:357] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-pay: 数据源配置可能缺失
[21:28:46:358] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-comment: 数据源配置可能缺失
[21:28:46:358] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-form-system: 数据源配置可能缺失
[21:28:46:359] [WARN] - com.taurus.monolith.config.ConfigurationValidator.validateDataSourceConfig(ConfigurationValidator.java:85) - ⚠ taurus-oss: 数据源配置可能缺失
[21:28:48:792] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[21:40:52:903] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.properties]未找到或加载失败
[21:40:52:904] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.yml]未找到或加载失败
[21:40:52:905] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:227) - 微服务[taurus-form-system]未加载到任何配置
[21:40:52:914] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.properties]未找到或加载失败
[21:40:52:916] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.yml]未找到或加载失败
[21:40:52:916] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:227) - 微服务[taurus-pay]未加载到任何配置
[21:40:52:925] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.properties]未找到或加载失败
[21:40:52:929] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.yml]未找到或加载失败
[21:40:52:930] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:227) - 微服务[taurus-comment]未加载到任何配置
[21:40:52:943] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.properties]未找到或加载失败
[21:40:52:944] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.yml]未找到或加载失败
[21:40:52:945] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:227) - 微服务[taurus-oss]未加载到任何配置
[21:40:57:900] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:42:18:422] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.properties]未找到或加载失败
[21:42:18:425] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.yml]未找到或加载失败
[21:42:18:425] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:227) - 微服务[taurus-form-system]未加载到任何配置
[21:42:18:435] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.properties]未找到或加载失败
[21:42:18:438] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.yml]未找到或加载失败
[21:42:18:439] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:227) - 微服务[taurus-pay]未加载到任何配置
[21:42:18:444] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.properties]未找到或加载失败
[21:42:18:445] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.yml]未找到或加载失败
[21:42:18:446] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:227) - 微服务[taurus-comment]未加载到任何配置
[21:42:18:455] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.properties]未找到或加载失败
[21:42:18:456] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.yml]未找到或加载失败
[21:42:18:456] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:227) - 微服务[taurus-oss]未加载到任何配置
[21:42:23:424] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:43:11:339] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.properties]未找到或加载失败
[21:43:11:341] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.yml]未找到或加载失败
[21:43:11:341] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:227) - 微服务[taurus-form-system]未加载到任何配置
[21:43:11:348] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.properties]未找到或加载失败
[21:43:11:351] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.yml]未找到或加载失败
[21:43:11:351] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:227) - 微服务[taurus-pay]未加载到任何配置
[21:43:11:357] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.properties]未找到或加载失败
[21:43:11:359] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.yml]未找到或加载失败
[21:43:11:359] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:227) - 微服务[taurus-comment]未加载到任何配置
[21:43:11:368] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.properties]未找到或加载失败
[21:43:11:369] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:222) -     配置文件[application.yml]未找到或加载失败
[21:43:11:369] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:227) - 微服务[taurus-oss]未加载到任何配置
[21:43:16:779] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:44:28:842] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.properties]未找到或加载失败
[21:44:28:848] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[21:44:28:854] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.properties]未找到或加载失败
[21:44:28:859] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[21:44:28:860] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:184) - 微服务[taurus-form-system]未加载到任何配置
[21:44:28:862] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.properties]未找到或加载失败
[21:44:28:865] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[21:44:28:871] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-test.properties]未找到或加载失败
[21:44:28:872] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-test.yml]未找到或加载失败
[21:44:28:872] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:184) - 微服务[taurus-pay]未加载到任何配置
[21:44:28:879] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.properties]未找到或加载失败
[21:44:28:880] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[21:44:28:882] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.properties]未找到或加载失败
[21:44:28:889] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[21:44:28:890] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:184) - 微服务[taurus-comment]未加载到任何配置
[21:44:28:893] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.properties]未找到或加载失败
[21:44:28:899] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[21:44:28:902] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.properties]未找到或加载失败
[21:44:28:903] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[21:44:28:903] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:184) - 微服务[taurus-oss]未加载到任何配置
[21:44:33:893] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:46:23:490] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.properties]未找到或加载失败
[21:46:23:496] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[21:46:23:502] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.properties]未找到或加载失败
[21:46:23:583] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[21:46:23:584] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:184) - 微服务[taurus-form-system]未加载到任何配置
[21:46:23:588] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.properties]未找到或加载失败
[21:46:23:603] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[21:46:23:610] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-test.properties]未找到或加载失败
[21:46:23:614] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-test.yml]未找到或加载失败
[21:46:23:615] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:184) - 微服务[taurus-pay]未加载到任何配置
[21:46:23:621] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.properties]未找到或加载失败
[21:46:23:623] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[21:46:23:625] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.properties]未找到或加载失败
[21:46:23:630] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[21:46:23:631] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:184) - 微服务[taurus-comment]未加载到任何配置
[21:46:23:636] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.properties]未找到或加载失败
[21:46:23:641] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[21:46:23:642] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.properties]未找到或加载失败
[21:46:23:643] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[21:46:23:643] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:184) - 微服务[taurus-oss]未加载到任何配置
[21:46:28:578] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:54:09:380] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.properties]未找到或加载失败
[21:54:09:388] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[21:54:09:393] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.properties]未找到或加载失败
[21:54:09:396] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[21:54:09:396] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:184) - 微服务[taurus-form-system]未加载到任何配置
[21:54:09:399] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.properties]未找到或加载失败
[21:54:09:402] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[21:54:09:406] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-test.properties]未找到或加载失败
[21:54:09:410] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-test.yml]未找到或加载失败
[21:54:09:411] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:184) - 微服务[taurus-pay]未加载到任何配置
[21:54:09:419] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.properties]未找到或加载失败
[21:54:09:421] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[21:54:09:424] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.properties]未找到或加载失败
[21:54:09:430] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[21:54:09:430] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:184) - 微服务[taurus-comment]未加载到任何配置
[21:54:09:435] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.properties]未找到或加载失败
[21:54:09:445] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[21:54:09:446] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.properties]未找到或加载失败
[21:54:09:447] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[21:54:09:447] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:184) - 微服务[taurus-oss]未加载到任何配置
[21:54:14:198] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[21:56:11:246] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.properties]未找到或加载失败
[21:56:11:252] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[21:56:11:255] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.properties]未找到或加载失败
[21:56:11:262] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[21:56:11:262] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:184) - 微服务[taurus-form-system]未加载到任何配置
[21:56:11:265] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.properties]未找到或加载失败
[21:56:11:266] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[21:56:11:270] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-test.properties]未找到或加载失败
[21:56:11:271] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-test.yml]未找到或加载失败
[21:56:11:271] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:184) - 微服务[taurus-pay]未加载到任何配置
[21:56:11:279] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.properties]未找到或加载失败
[21:56:11:281] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[21:56:11:282] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.properties]未找到或加载失败
[21:56:11:288] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[21:56:11:289] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:184) - 微服务[taurus-comment]未加载到任何配置
[21:56:11:295] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.properties]未找到或加载失败
[21:56:11:303] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[21:56:11:303] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.properties]未找到或加载失败
[21:56:11:305] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[21:56:11:305] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:184) - 微服务[taurus-oss]未加载到任何配置
[21:56:16:400] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[22:05:22:805] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.properties]未找到或加载失败
[22:05:22:810] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:05:22:814] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.properties]未找到或加载失败
[22:05:22:822] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[22:05:22:823] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:184) - 微服务[taurus-form-system]未加载到任何配置
[22:05:22:825] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.properties]未找到或加载失败
[22:05:22:826] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:05:22:831] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-test.properties]未找到或加载失败
[22:05:22:833] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-test.yml]未找到或加载失败
[22:05:22:833] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:184) - 微服务[taurus-pay]未加载到任何配置
[22:05:22:840] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.properties]未找到或加载失败
[22:05:22:842] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:05:22:845] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.properties]未找到或加载失败
[22:05:22:849] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[22:05:22:850] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:184) - 微服务[taurus-comment]未加载到任何配置
[22:05:22:856] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.properties]未找到或加载失败
[22:05:22:861] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:05:22:864] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.properties]未找到或加载失败
[22:05:22:865] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[22:05:22:866] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:184) - 微服务[taurus-oss]未加载到任何配置
[22:05:27:634] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[22:12:34:093] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:12:34:102] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[22:12:34:113] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:12:34:121] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-test.yml]未找到或加载失败
[22:12:34:132] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:12:34:143] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[22:12:34:149] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:12:34:150] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.addReusablePropertiesToEnvironment(DynamicConfigLoader.java:368) - 检测到加密密码配置[spring.datasource.password]，跳过加载，将使用明文密码替代
[22:12:34:159] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[22:12:39:167] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[22:12:52:813] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[22:14:38:728] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:14:38:731] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[22:14:38:733] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:14:38:737] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-test.yml]未找到或加载失败
[22:14:38:742] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:14:38:745] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[22:14:38:748] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:14:38:750] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.addReusablePropertiesToEnvironment(DynamicConfigLoader.java:368) - 检测到加密密码配置[spring.datasource.password]，跳过加载，将使用明文密码替代
[22:14:38:752] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[22:14:41:005] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[22:14:46:812] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[22:15:24:805] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:15:24:809] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[22:15:24:812] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:15:24:815] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-test.yml]未找到或加载失败
[22:15:24:818] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:15:24:820] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[22:15:24:822] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:15:24:822] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.addReusablePropertiesToEnvironment(DynamicConfigLoader.java:382) - 检测到加密密码配置[spring.datasource.password]，跳过加载，将使用明文密码替代
[22:15:24:824] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[22:15:26:558] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[22:15:31:664] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[22:15:33:221] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:15:33:224] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[22:15:33:227] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:15:33:230] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-test.yml]未找到或加载失败
[22:15:33:232] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:15:33:233] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[22:15:33:235] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:15:33:235] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.addReusablePropertiesToEnvironment(DynamicConfigLoader.java:382) - 检测到加密密码配置[spring.datasource.password]，跳过加载，将使用明文密码替代
[22:15:33:237] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[22:15:34:738] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[22:15:39:608] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[22:16:11:336] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:16:11:338] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[22:16:11:341] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:16:11:344] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-test.yml]未找到或加载失败
[22:16:11:347] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:16:11:349] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[22:16:11:351] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:16:11:354] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.addReusablePropertiesToEnvironment(DynamicConfigLoader.java:368) - 检测到加密密码配置[spring.datasource.password]，跳过加载，将使用明文密码替代
[22:16:11:357] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[22:16:12:867] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[22:16:19:500] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:16:19:503] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[22:16:19:505] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:16:19:508] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-test.yml]未找到或加载失败
[22:16:19:510] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:16:19:513] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[22:16:19:516] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:16:19:517] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.addReusablePropertiesToEnvironment(DynamicConfigLoader.java:382) - 检测到加密密码配置[spring.datasource.password]，跳过加载，将使用明文密码替代
[22:16:19:519] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[22:16:20:783] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[22:16:26:264] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
[22:18:14:286] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:18:14:287] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[22:18:14:289] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:18:14:292] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-test.yml]未找到或加载失败
[22:18:14:293] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:18:14:295] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[22:18:14:297] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application.yml]未找到或加载失败
[22:18:14:299] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.addReusablePropertiesToEnvironment(DynamicConfigLoader.java:382) - 检测到加密密码配置[spring.datasource.password]，跳过加载，将使用明文密码替代
[22:18:14:301] [WARN] - com.taurus.monolith.config.DynamicConfigLoader.loadMicroserviceConfigBySpringBootOrder(DynamicConfigLoader.java:179) -     配置文件[application-dev.yml]未找到或加载失败
[22:18:15:269] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[22:18:19:955] [WARN] - com.taurus.monolith.config.ServiceCompatibilityChecker.generateCompatibilityReport(ServiceCompatibilityChecker.java:424) - 发现4个服务有警告，建议关注相关问题
