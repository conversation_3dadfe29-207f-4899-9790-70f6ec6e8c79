[11:45:54:970] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$AutoConfiguredMapperScannerRegistrar.registerBeanDefinitions(MybatisAutoConfiguration.java:252) - Searching for mappers annotated with @Mapper
[11:45:54:977] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$AutoConfiguredMapperScannerRegistrar.registerBeanDefinitions(MybatisAutoConfiguration.java:264) - Using auto-configuration base package 'com.taurus.formSys'
[11:45:56:207] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[11:45:56:260] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[11:45:56:261] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[11:45:56:262] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[11:45:56:262] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[11:45:56:263] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[11:45:56:263] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[11:45:56:264] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[11:45:56:265] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[11:45:56:266] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-dev.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[11:45:56:266] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[11:45:56:266] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[11:45:56:267] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[11:45:56:326] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[11:45:56:810] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[11:45:56:822] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[11:45:57:860] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[11:45:57:883] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[11:45:57:891] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[11:45:58:103] [DEBUG] - io.micrometer.core.util.internal.logging.InternalLoggerFactory.newDefaultFactory(InternalLoggerFactory.java:59) - Using SLF4J as the default logging framework
[11:45:58:621] [DEBUG] - io.undertow.server.session.InMemorySessionManager.registerSessionListener(InMemorySessionManager.java:268) - Registered session listener io.undertow.servlet.core.SessionListenerBridge@4f3dc43
[11:45:58:682] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[11:46:04:367] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[11:46:05:977] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[11:46:06:207] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[11:46:06:228] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.formSys.mapper.FormDefinitionMapper对应的Mapper
[11:46:06:524] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.formSys.mapper.CategoryFormMapper对应的Mapper
[11:46:06:624] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.formSys.mapper.FormFlowRecordDataMapper对应的Mapper
[11:46:06:687] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.formSys.mapper.FormFieldStructureMapper对应的Mapper
[11:46:06:754] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.formSys.mapper.FormFlowRecordMapper对应的Mapper
[11:46:06:801] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.formSys.mapper.CategoryMapper对应的Mapper
[11:46:06:848] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.62秒
[11:46:10:469] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.f.c.CategoryController:
	{POST [/category/addCategory]}: addCategory(JSONObject)
	{GET [/category/getNLevelCategory]}: getLevelCategoriesOfLessThanOrEqualDesignativeLevel(String,String,Integer)
	{GET [/category/getPathOfCategory]}: getPathOfCategory(Integer)
	{POST [/category/getBatchPathOfCategoryList]}: getBatchPathOfCategoryList(JSONArray)
	{POST [/category/updateCategory]}: updateCategory(JSONObject)
	{POST [/category/updateCategoryList]}: updateCategoryList(JSONArray)
[11:46:10:478] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.f.c.CategoryFormController:
	{POST [/categoryForm/getFormWrapperListByColumns]}: getFormListByColumns(JSONObject)
[11:46:10:480] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.f.c.FormDefinitionController:
	{POST [/formDefinition/createForm]}: createForm(JSONObject)
	{GET [/formDefinition/getFormById]}: getFormById(Integer)
	{GET [/formDefinition/getFormWrapperById]}: getFormWrapperById(Integer)
	{POST [/formDefinition/getFormListByIds]}: getFormListByIds(JSONArray)
	{POST [/formDefinition/getFormWrapperListByIds]}: getFormWrapperListByIds(JSONArray)
	{GET [/formDefinition/getFormListOfProduct]}: getFormListOfProduct(String,Integer,Integer,Integer)
	{GET [/formDefinition/getConciseFormListOfProduct]}: getConciseFormListOfProduct(String,Integer,Integer,Integer,Integer)
	{GET [/formDefinition/disableForm]}: disableForm(Integer)
	{POST [/formDefinition/update]}: update(JSONObject)
[11:46:10:485] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.f.c.FormFieldStructureController:
	{GET [/formFieldStructure/getFormFieldStructureListByFormId]}: getFormFieldStructureListByFormId(Integer)
	{GET [/formFieldStructure/getFormEditableFiledStructureListByFormId]}: getFormEditableFiledStructureListByFormId(Integer)
[11:46:10:488] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.f.c.FormFlowRecordController:
	{POST [/formFlowRecord/getUserFormFlowRecordList]}: getUserFormFlowRecordList(JSONObject)
	{GET [/formFlowRecord/getFormFlowRecordWrapperById]}: getFormFlowRecordWrapperById(Integer)
	{POST [/formFlowRecord/getFormFlowRecordWrapperByIds]}: getFormFlowRecordDataByIds(JSONObject)
	{POST [/formFlowRecord/update]}: update(JSONObject)
	{GET [/formFlowRecord/clear]}: clear(Integer)
	{POST [/formFlowRecord/save]}: save(JSONObject)
[11:46:10:492] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.f.c.FormFlowRecordDataController:
	{POST [/formFlowRecordData/getFormFlowRecordDataByIds]}: getFormFlowRecordDataByIds(JSONObject)
	{POST [/formFlowRecordData/updateFormFlowRecordData]}: updateFormFlowRecordData(JSONArray)
	{GET [/formFlowRecordData/getListByFormFlowRecordId]}: getListByFormFlowRecordId(Integer)
	{GET [/formFlowRecordData/getFormFlowRecordDataByFormId]}: getFormFlowRecordDataByFormId(Integer,Date,Date)
	{GET [/formFlowRecordData/getFormFlowRecordMapDataByFormId]}: getFormFlowRecordMapDataByFormId(Integer,Date,Date)
	{GET [/formFlowRecordData/exportFormFlowRecordData]}: exportFormFlowRecordData(Integer,String,String,HttpServletResponse)
	{GET [/formFlowRecordData/getDistrbutionAnalysisData]}: getDistrbutionAnalysisData(Integer)
[11:46:10:495] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.n.c.NacosInstanceController:
	{POST [/actuator/stopService]}: stopNacosService(HttpServletRequest)
[11:46:10:521] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
	{ [/error]}: error(HttpServletRequest)
[11:46:10:573] [DEBUG] - org.springframework.web.servlet.handler.AbstractDetectingUrlHandlerMapping.detectHandlers(AbstractDetectingUrlHandlerMapping.java:86) - 'beanNameHandlerMapping' {}
[11:46:11:031] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$MapperScannerRegistrarNotFoundConfiguration.afterPropertiesSet(MybatisAutoConfiguration.java:305) - No org.mybatis.spring.mapper.MapperFactoryBean found.
[11:46:13:829] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[11:46:13:963] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 110 ms to scan 1 urls, producing 3 keys and 6 values 
[11:46:13:982] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype com.alibaba.nacos.api.remote.request.Request -> com.alibaba.nacos.api.remote.request.ServerRequest
[11:46:13:995] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[11:46:14:031] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 34 ms to scan 1 urls, producing 4 keys and 9 values 
[11:46:14:034] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype com.alibaba.nacos.api.remote.request.Request -> com.alibaba.nacos.api.remote.request.ServerRequest
[11:46:14:042] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[11:46:14:063] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 16 ms to scan 1 urls, producing 3 keys and 10 values 
[11:46:14:070] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
file:/Users/<USER>/Documents/git/taurus-cloud/taurus-form-system/target/classes/
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/dnsns.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jaccess.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jfxrt.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunec.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/zipfs.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/nashorn.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/cldrdata.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/localedata.jar
file:/private/var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T/cp_3ax6zdggjf5hsw1fgggfw21qj.jar
[11:46:14:098] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 28 ms to scan 12 urls, producing 0 keys and 0 values 
[11:46:14:101] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[11:46:14:118] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
[11:46:14:121] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[11:46:14:136] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
[11:46:14:139] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[11:46:14:159] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 19 ms to scan 1 urls, producing 2 keys and 8 values 
[11:46:14:160] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype java.lang.Comparable -> java.lang.Enum
[11:46:14:160] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype java.io.Serializable -> java.lang.Enum
[11:46:14:164] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
file:/Users/<USER>/Documents/git/taurus-cloud/taurus-form-system/target/classes/
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/dnsns.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jaccess.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jfxrt.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunec.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/zipfs.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/nashorn.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/cldrdata.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/localedata.jar
file:/private/var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T/cp_3ax6zdggjf5hsw1fgggfw21qj.jar
[11:46:14:187] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 22 ms to scan 12 urls, producing 0 keys and 0 values 
[11:46:15:138] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[11:46:15:167] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[11:46:15:187] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[11:46:15:207] [DEBUG] - org.xnio.XnioWorker$Builder.build(XnioWorker.java:1191) - Creating worker:null, pool size:64, max pool size:64, keep alive:60000, io threads:8, stack size:0
[11:46:15:222] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[11:46:15:232] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-5', selector sun.nio.ch.KQueueSelectorImpl@14ef5cd9
[11:46:15:232] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-2', selector sun.nio.ch.KQueueSelectorImpl@4af07d63
[11:46:15:232] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-3', selector sun.nio.ch.KQueueSelectorImpl@b4b0aa5
[11:46:15:232] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-1', selector sun.nio.ch.KQueueSelectorImpl@69ea5b3a
[11:46:15:234] [DEBUG] - io.undertow.Undertow.start(Undertow.java:160) - Configuring listener with protocol HTTP for interface 0.0.0.0 and port 8084
[11:46:15:235] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-4', selector sun.nio.ch.KQueueSelectorImpl@5650632a
[11:46:15:235] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-8', selector sun.nio.ch.KQueueSelectorImpl@3ab16ae3
[11:46:15:235] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-7', selector sun.nio.ch.KQueueSelectorImpl@77e55329
[11:46:15:235] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 Accept', selector sun.nio.ch.KQueueSelectorImpl@29a1c0c8
[11:46:15:235] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-6', selector sun.nio.ch.KQueueSelectorImpl@791890fc
[11:46:15:319] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP taurus-formSystem 192.168.8.151:8084 register finished
[11:47:11:403] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFlowRecord/getUserFormFlowRecordList, authentication required: false
[11:47:11:411] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@571286ce for /formFlowRecord/getUserFormFlowRecordList
[11:47:11:412] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFlowRecord/getUserFormFlowRecordList
[11:47:11:417] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[11:47:12:158] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[11:47:12:328] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFlowRecord/getFormFlowRecordWrapperByIds, authentication required: false
[11:47:12:332] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@571286ce for /formFlowRecord/getFormFlowRecordWrapperByIds
[11:47:12:332] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFlowRecord/getFormFlowRecordWrapperByIds
[11:47:12:486] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[11:47:12:571] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[11:47:12:693] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFieldStructure/getFormFieldStructureListByFormId, authentication required: false
[11:47:12:694] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@571286ce for /formFieldStructure/getFormFieldStructureListByFormId
[11:47:12:694] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFieldStructure/getFormFieldStructureListByFormId
[12:01:16:210] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$AutoConfiguredMapperScannerRegistrar.registerBeanDefinitions(MybatisAutoConfiguration.java:252) - Searching for mappers annotated with @Mapper
[12:01:16:217] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$AutoConfiguredMapperScannerRegistrar.registerBeanDefinitions(MybatisAutoConfiguration.java:264) - Using auto-configuration base package 'com.taurus.formSys'
[12:01:17:904] [INFO] - com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory(EnableEncryptablePropertiesBeanFactoryPostProcessor.java:48) - Post-processing PropertySource instances
[12:01:17:994] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[12:01:17:994] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[12:01:17:995] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[12:01:17:995] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[12:01:17:995] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[12:01:17:995] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[12:01:17:995] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[12:01:17:995] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[12:01:17:995] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application-dev.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[12:01:17:996] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[12:01:17:998] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource devtools [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[12:01:17:998] [INFO] - com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable(EncryptablePropertySourceConverter.java:41) - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[12:01:18:076] [INFO] - com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2(DefaultLazyPropertyFilter.java:34) - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[12:01:18:835] [INFO] - com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2(DefaultLazyPropertyResolver.java:35) - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[12:01:18:844] [INFO] - com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2(DefaultLazyPropertyDetector.java:33) - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[12:01:20:615] [WARN] - io.undertow.websockets.jsr.Bootstrap.handleDeployment(Bootstrap.java:68) - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
[12:01:20:643] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[12:01:20:658] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring embedded WebApplicationContext
[12:01:21:181] [DEBUG] - io.micrometer.core.util.internal.logging.InternalLoggerFactory.newDefaultFactory(InternalLoggerFactory.java:59) - Using SLF4J as the default logging framework
[12:01:22:084] [DEBUG] - io.undertow.server.session.InMemorySessionManager.registerSessionListener(InMemorySessionManager.java:268) - Registered session listener io.undertow.servlet.core.SessionListenerBridge@67f70c20
[12:01:22:187] [DEBUG] - org.jboss.logging.DelegatingBasicLogger.debugf(DelegatingBasicLogger.java:384) - Setting default session timeout to 1800
[12:01:23:918] [INFO] - com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:1003) - {dataSource-1} inited
[12:01:25:394] [INFO] - com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt.buildDbName(SqlSessionFactoryBeanExt.java:97) - 数据库名称：MySQL
[12:01:25:573] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:131) - 使用模板:/fastmybatis/tpl/mysql.vm
[12:01:25:580] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.formSys.mapper.FormDefinitionMapper对应的Mapper
[12:01:25:859] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.formSys.mapper.CategoryFormMapper对应的Mapper
[12:01:25:940] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.formSys.mapper.FormFlowRecordMapper对应的Mapper
[12:01:26:000] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.formSys.mapper.FormFlowRecordDataMapper对应的Mapper
[12:01:26:045] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.formSys.mapper.CategoryMapper对应的Mapper
[12:01:26:089] [DEBUG] - com.gitee.fastmybatis.core.ext.code.client.ClassClient.genMybatisXml(ClassClient.java:47) - 开始生成com.taurus.formSys.mapper.FormFieldStructureMapper对应的Mapper
[12:01:26:151] [INFO] - com.gitee.fastmybatis.core.ext.MapperLocationsBuilder.buildMapperResource(MapperLocationsBuilder.java:144) - 生成Mapper内容总耗时：0.57秒
[12:01:30:941] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.f.c.CategoryController:
	{POST [/category/addCategory]}: addCategory(JSONObject)
	{GET [/category/getNLevelCategory]}: getLevelCategoriesOfLessThanOrEqualDesignativeLevel(String,String,Integer)
	{GET [/category/getPathOfCategory]}: getPathOfCategory(Integer)
	{POST [/category/getBatchPathOfCategoryList]}: getBatchPathOfCategoryList(JSONArray)
	{POST [/category/updateCategory]}: updateCategory(JSONObject)
	{POST [/category/updateCategoryList]}: updateCategoryList(JSONArray)
[12:01:30:965] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.f.c.CategoryFormController:
	{POST [/categoryForm/getFormWrapperListByColumns]}: getFormListByColumns(JSONObject)
[12:01:30:980] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.f.c.FormDefinitionController:
	{POST [/formDefinition/update]}: update(JSONObject)
	{POST [/formDefinition/createForm]}: createForm(JSONObject)
	{GET [/formDefinition/getConciseFormListOfProduct]}: getConciseFormListOfProduct(String,Integer,Integer,Integer,Integer)
	{GET [/formDefinition/disableForm]}: disableForm(Integer)
	{GET [/formDefinition/getFormById]}: getFormById(Integer)
	{GET [/formDefinition/getFormWrapperById]}: getFormWrapperById(Integer)
	{POST [/formDefinition/getFormListByIds]}: getFormListByIds(JSONArray)
	{POST [/formDefinition/getFormWrapperListByIds]}: getFormWrapperListByIds(JSONArray)
	{GET [/formDefinition/getFormListOfProduct]}: getFormListOfProduct(String,Integer,Integer,Integer)
[12:01:30:988] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.f.c.FormFieldStructureController:
	{GET [/formFieldStructure/getFormEditableFiledStructureListByFormId]}: getFormEditableFiledStructureListByFormId(Integer)
	{GET [/formFieldStructure/getFormFieldStructureListByFormId]}: getFormFieldStructureListByFormId(Integer)
[12:01:30:992] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.f.c.FormFlowRecordController:
	{POST [/formFlowRecord/getUserFormFlowRecordList]}: getUserFormFlowRecordList(JSONObject)
	{GET [/formFlowRecord/getFormFlowRecordWrapperById]}: getFormFlowRecordWrapperById(Integer)
	{POST [/formFlowRecord/getFormFlowRecordWrapperByIds]}: getFormFlowRecordDataByIds(JSONObject)
	{POST [/formFlowRecord/update]}: update(JSONObject)
	{GET [/formFlowRecord/clear]}: clear(Integer)
	{POST [/formFlowRecord/save]}: save(JSONObject)
[12:01:31:000] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.f.c.FormFlowRecordDataController:
	{POST [/formFlowRecordData/getFormFlowRecordDataByIds]}: getFormFlowRecordDataByIds(JSONObject)
	{POST [/formFlowRecordData/updateFormFlowRecordData]}: updateFormFlowRecordData(JSONArray)
	{GET [/formFlowRecordData/getListByFormFlowRecordId]}: getListByFormFlowRecordId(Integer)
	{GET [/formFlowRecordData/getFormFlowRecordDataByFormId]}: getFormFlowRecordDataByFormId(Integer,Date,Date)
	{GET [/formFlowRecordData/getFormFlowRecordMapDataByFormId]}: getFormFlowRecordMapDataByFormId(Integer,Date,Date)
	{GET [/formFlowRecordData/exportFormFlowRecordData]}: exportFormFlowRecordData(Integer,String,String,HttpServletResponse)
	{GET [/formFlowRecordData/getDistrbutionAnalysisData]}: getDistrbutionAnalysisData(Integer)
[12:01:31:001] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	c.t.n.c.NacosInstanceController:
	{POST [/actuator/stopService]}: stopNacosService(HttpServletRequest)
[12:01:31:097] [DEBUG] - org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:295) - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
	{ [/error]}: error(HttpServletRequest)
[12:01:31:230] [DEBUG] - org.springframework.web.servlet.handler.AbstractDetectingUrlHandlerMapping.detectHandlers(AbstractDetectingUrlHandlerMapping.java:86) - 'beanNameHandlerMapping' {}
[12:01:31:969] [DEBUG] - com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$MapperScannerRegistrarNotFoundConfiguration.afterPropertiesSet(MybatisAutoConfiguration.java:305) - No org.mybatis.spring.mapper.MapperFactoryBean found.
[12:01:35:584] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[12:01:35:709] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 105 ms to scan 1 urls, producing 3 keys and 6 values 
[12:01:35:755] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype com.alibaba.nacos.api.remote.request.Request -> com.alibaba.nacos.api.remote.request.ServerRequest
[12:01:35:777] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[12:01:35:830] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 46 ms to scan 1 urls, producing 4 keys and 9 values 
[12:01:35:844] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype com.alibaba.nacos.api.remote.request.Request -> com.alibaba.nacos.api.remote.request.ServerRequest
[12:01:35:854] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[12:01:35:879] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 23 ms to scan 1 urls, producing 3 keys and 10 values 
[12:01:35:887] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
file:/Users/<USER>/Documents/git/taurus-cloud/taurus-form-system/target/classes/
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/dnsns.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jaccess.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jfxrt.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunec.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/zipfs.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/nashorn.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/cldrdata.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/localedata.jar
file:/private/var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T/cp_3ax6zdggjf5hsw1fgggfw21qj.jar
[12:01:35:911] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 22 ms to scan 12 urls, producing 0 keys and 0 values 
[12:01:35:918] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[12:01:35:941] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 23 ms to scan 1 urls, producing 1 keys and 5 values 
[12:01:35:945] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[12:01:35:982] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 37 ms to scan 1 urls, producing 1 keys and 7 values 
[12:01:35:986] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
jar:file:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar!/
[12:01:36:000] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
[12:01:36:001] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype java.lang.Comparable -> java.lang.Enum
[12:01:36:001] [DEBUG] - org.reflections.Reflections.expandSupertypes(Reflections.java:393) - expanded subtype java.io.Serializable -> java.lang.Enum
[12:01:36:011] [DEBUG] - org.reflections.Reflections.scan(Reflections.java:184) - going to scan these urls:
file:/Users/<USER>/Documents/git/taurus-cloud/taurus-form-system/target/classes/
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/dnsns.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jaccess.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/jfxrt.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunec.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/zipfs.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/nashorn.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/cldrdata.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar
file:/Library/Java/JavaVirtualMachines/jdk1.8.0_281.jdk/Contents/Home/jre/lib/ext/localedata.jar
file:/private/var/folders/1y/rld6l7_55651_ynr9bc7j7th0000gn/T/cp_3ax6zdggjf5hsw1fgggfw21qj.jar
[12:01:36:053] [INFO] - org.reflections.Reflections.scan(Reflections.java:232) - Reflections took 40 ms to scan 12 urls, producing 0 keys and 0 values 
[12:01:37:593] [INFO] - io.undertow.Undertow.start(Undertow.java:120) - starting server: Undertow - 2.2.28.Final
[12:01:37:613] [INFO] - org.xnio.Xnio.<clinit>(Xnio.java:95) - XNIO version 3.8.7.Final
[12:01:37:631] [INFO] - org.xnio.nio.NioXnio.<clinit>(NioXnio.java:58) - XNIO NIO Implementation Version 3.8.7.Final
[12:01:37:664] [DEBUG] - org.xnio.XnioWorker$Builder.build(XnioWorker.java:1191) - Creating worker:null, pool size:64, max pool size:64, keep alive:60000, io threads:8, stack size:0
[12:01:37:683] [INFO] - org.jboss.threads.Version.<clinit>(Version.java:52) - JBoss Threads version 3.1.0.Final
[12:01:37:705] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 Accept', selector sun.nio.ch.KQueueSelectorImpl@318a063b
[12:01:37:704] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-7', selector sun.nio.ch.KQueueSelectorImpl@686fcc02
[12:01:37:696] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-1', selector sun.nio.ch.KQueueSelectorImpl@2ec454e6
[12:01:37:705] [DEBUG] - io.undertow.Undertow.start(Undertow.java:160) - Configuring listener with protocol HTTP for interface 0.0.0.0 and port 8084
[12:01:37:697] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-2', selector sun.nio.ch.KQueueSelectorImpl@66c21ca3
[12:01:37:697] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-3', selector sun.nio.ch.KQueueSelectorImpl@189716df
[12:01:37:697] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-4', selector sun.nio.ch.KQueueSelectorImpl@2fa9e756
[12:01:37:697] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-8', selector sun.nio.ch.KQueueSelectorImpl@2b5426f2
[12:01:37:698] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-6', selector sun.nio.ch.KQueueSelectorImpl@48f23e6
[12:01:37:697] [DEBUG] - org.xnio.nio.WorkerThread.run(WorkerThread.java:444) - Started channel thread 'XNIO-1 I/O-5', selector sun.nio.ch.KQueueSelectorImpl@738da103
[12:01:37:838] [INFO] - com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75) - nacos registry, DEFAULT_GROUP taurus-formSystem 192.168.31.135:8084 register finished
[12:11:45:246] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFlowRecord/getUserFormFlowRecordList, authentication required: false
[12:11:45:257] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFlowRecord/getUserFormFlowRecordList
[12:11:45:257] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFlowRecord/getUserFormFlowRecordList
[12:11:45:263] [INFO] - io.undertow.servlet.spec.ServletContextImpl.log(ServletContextImpl.java:389) - Initializing Spring DispatcherServlet 'dispatcherServlet'
[12:11:45:837] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[12:11:45:975] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFlowRecord/getFormFlowRecordWrapperByIds, authentication required: false
[12:11:45:978] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFlowRecord/getFormFlowRecordWrapperByIds
[12:11:45:978] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFlowRecord/getFormFlowRecordWrapperByIds
[12:11:46:056] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[12:11:46:100] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[12:11:46:222] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFieldStructure/getFormFieldStructureListByFormId, authentication required: false
[12:11:46:222] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFieldStructure/getFormFieldStructureListByFormId
[12:11:46:222] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFieldStructure/getFormFieldStructureListByFormId
[12:12:46:839] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFlowRecord/getUserFormFlowRecordList, authentication required: false
[12:12:46:848] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFlowRecord/getUserFormFlowRecordList
[12:12:46:849] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFlowRecord/getUserFormFlowRecordList
[12:12:47:012] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFlowRecord/getFormFlowRecordWrapperByIds, authentication required: false
[12:12:47:015] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFlowRecord/getFormFlowRecordWrapperByIds
[12:12:47:015] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFlowRecord/getFormFlowRecordWrapperByIds
[12:12:47:141] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFieldStructure/getFormFieldStructureListByFormId, authentication required: false
[12:12:47:142] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFieldStructure/getFormFieldStructureListByFormId
[12:12:47:142] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFieldStructure/getFormFieldStructureListByFormId
[12:14:17:296] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFlowRecord/getUserFormFlowRecordList, authentication required: false
[12:14:17:303] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFlowRecord/getUserFormFlowRecordList
[12:14:17:304] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFlowRecord/getUserFormFlowRecordList
[12:14:17:447] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFlowRecord/getFormFlowRecordWrapperByIds, authentication required: false
[12:14:17:451] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFlowRecord/getFormFlowRecordWrapperByIds
[12:14:17:451] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFlowRecord/getFormFlowRecordWrapperByIds
[12:14:17:581] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFieldStructure/getFormFieldStructureListByFormId, authentication required: false
[12:14:17:581] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFieldStructure/getFormFieldStructureListByFormId
[12:14:17:581] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFieldStructure/getFormFieldStructureListByFormId
[13:08:40:910] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFlowRecord/getUserFormFlowRecordList, authentication required: false
[13:08:40:919] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFlowRecord/getUserFormFlowRecordList
[13:08:40:919] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFlowRecord/getUserFormFlowRecordList
[13:08:42:000] [DEBUG] - com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1449) - skip not validate connection.
[13:08:43:014] [DEBUG] - com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1449) - skip not validate connection.
[13:08:43:089] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:08:43:121] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFlowRecord/getFormFlowRecordWrapperByIds, authentication required: false
[13:08:43:122] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFlowRecord/getFormFlowRecordWrapperByIds
[13:08:43:122] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFlowRecord/getFormFlowRecordWrapperByIds
[13:08:43:174] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:08:43:210] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:08:43:235] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFieldStructure/getFormFieldStructureListByFormId, authentication required: false
[13:08:43:235] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFieldStructure/getFormFieldStructureListByFormId
[13:08:43:235] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFieldStructure/getFormFieldStructureListByFormId
[13:32:13:021] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFlowRecord/getUserFormFlowRecordList, authentication required: false
[13:32:13:031] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFlowRecord/getUserFormFlowRecordList
[13:32:13:032] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFlowRecord/getUserFormFlowRecordList
[13:32:13:157] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:32:13:188] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFlowRecord/getFormFlowRecordWrapperByIds, authentication required: false
[13:32:13:189] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFlowRecord/getFormFlowRecordWrapperByIds
[13:32:13:189] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFlowRecord/getFormFlowRecordWrapperByIds
[13:32:13:248] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:32:13:284] [DEBUG] - com.alibaba.druid.pool.PreparedStatementPool.put(PreparedStatementPool.java:129) - stmt enter cache
[13:32:13:301] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFieldStructure/getFormFieldStructureListByFormId, authentication required: false
[13:32:13:301] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFieldStructure/getFormFieldStructureListByFormId
[13:32:13:301] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFieldStructure/getFormFieldStructureListByFormId
[13:56:34:673] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFlowRecord/getUserFormFlowRecordList, authentication required: false
[13:56:34:683] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFlowRecord/getUserFormFlowRecordList
[13:56:34:683] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFlowRecord/getUserFormFlowRecordList
[13:56:34:855] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFlowRecord/getFormFlowRecordWrapperByIds, authentication required: false
[13:56:34:856] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFlowRecord/getFormFlowRecordWrapperByIds
[13:56:34:856] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFlowRecord/getFormFlowRecordWrapperByIds
[13:56:34:958] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFieldStructure/getFormFieldStructureListByFormId, authentication required: false
[13:56:34:958] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFieldStructure/getFormFieldStructureListByFormId
[13:56:34:959] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFieldStructure/getFormFieldStructureListByFormId
[13:56:48:971] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFlowRecord/getUserFormFlowRecordList, authentication required: false
[13:56:48:972] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFlowRecord/getUserFormFlowRecordList
[13:56:48:972] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFlowRecord/getUserFormFlowRecordList
[13:56:49:039] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFlowRecord/getFormFlowRecordWrapperByIds, authentication required: false
[13:56:49:040] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFlowRecord/getFormFlowRecordWrapperByIds
[13:56:49:040] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFlowRecord/getFormFlowRecordWrapperByIds
[13:56:49:133] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFieldStructure/getFormFieldStructureListByFormId, authentication required: false
[13:56:49:133] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFieldStructure/getFormFieldStructureListByFormId
[13:56:49:133] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFieldStructure/getFormFieldStructureListByFormId
[13:57:08:711] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFlowRecord/getUserFormFlowRecordList, authentication required: false
[13:57:08:712] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFlowRecord/getUserFormFlowRecordList
[13:57:08:712] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFlowRecord/getUserFormFlowRecordList
[13:57:08:801] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFlowRecord/getFormFlowRecordWrapperByIds, authentication required: false
[13:57:08:804] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFlowRecord/getFormFlowRecordWrapperByIds
[13:57:08:805] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFlowRecord/getFormFlowRecordWrapperByIds
[13:57:08:921] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFieldStructure/getFormFieldStructureListByFormId, authentication required: false
[13:57:08:922] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFieldStructure/getFormFieldStructureListByFormId
[13:57:08:922] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFieldStructure/getFormFieldStructureListByFormId
[13:57:49:675] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFlowRecord/getUserFormFlowRecordList, authentication required: false
[13:57:49:675] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFlowRecord/getUserFormFlowRecordList
[13:57:49:676] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFlowRecord/getUserFormFlowRecordList
[13:57:49:777] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFlowRecord/getFormFlowRecordWrapperByIds, authentication required: false
[13:57:49:780] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFlowRecord/getFormFlowRecordWrapperByIds
[13:57:49:780] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFlowRecord/getFormFlowRecordWrapperByIds
[13:57:49:868] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFieldStructure/getFormFieldStructureListByFormId, authentication required: false
[13:57:49:870] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFieldStructure/getFormFieldStructureListByFormId
[13:57:49:871] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFieldStructure/getFormFieldStructureListByFormId
[13:58:53:873] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFlowRecord/getUserFormFlowRecordList, authentication required: false
[13:58:53:877] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFlowRecord/getUserFormFlowRecordList
[13:58:53:877] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFlowRecord/getUserFormFlowRecordList
[13:58:54:021] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFlowRecord/getFormFlowRecordWrapperByIds, authentication required: false
[13:58:54:023] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFlowRecord/getFormFlowRecordWrapperByIds
[13:58:54:023] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFlowRecord/getFormFlowRecordWrapperByIds
[13:58:54:134] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFieldStructure/getFormFieldStructureListByFormId, authentication required: false
[13:58:54:135] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFieldStructure/getFormFieldStructureListByFormId
[13:58:54:135] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFieldStructure/getFormFieldStructureListByFormId
[13:59:13:654] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFlowRecord/getUserFormFlowRecordList, authentication required: false
[13:59:13:656] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFlowRecord/getUserFormFlowRecordList
[13:59:13:656] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFlowRecord/getUserFormFlowRecordList
[13:59:13:752] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFlowRecord/getFormFlowRecordWrapperByIds, authentication required: false
[13:59:13:755] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFlowRecord/getFormFlowRecordWrapperByIds
[13:59:13:755] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFlowRecord/getFormFlowRecordWrapperByIds
[13:59:13:875] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFieldStructure/getFormFieldStructureListByFormId, authentication required: false
[13:59:13:876] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFieldStructure/getFormFieldStructureListByFormId
[13:59:13:877] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFieldStructure/getFormFieldStructureListByFormId
[13:59:48:811] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFlowRecord/getUserFormFlowRecordList, authentication required: false
[13:59:48:813] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFlowRecord/getUserFormFlowRecordList
[13:59:48:813] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFlowRecord/getUserFormFlowRecordList
[13:59:48:899] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFlowRecord/getFormFlowRecordWrapperByIds, authentication required: false
[13:59:48:901] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFlowRecord/getFormFlowRecordWrapperByIds
[13:59:48:901] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFlowRecord/getFormFlowRecordWrapperByIds
[13:59:48:984] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authenticate(SecurityContextImpl.java:86) - Attempting to authenticate /formFieldStructure/getFormFieldStructureListByFormId, authentication required: false
[13:59:48:985] [DEBUG] - io.undertow.security.impl.SecurityContextImpl$AuthAttempter.transition(SecurityContextImpl.java:247) - Authentication outcome was NOT_ATTEMPTED with method io.undertow.security.impl.CachedAuthenticatedSessionMechanism@325e99a3 for /formFieldStructure/getFormFieldStructureListByFormId
[13:59:48:985] [DEBUG] - io.undertow.security.impl.SecurityContextImpl.authTransition(SecurityContextImpl.java:110) - Authentication result was ATTEMPTED for /formFieldStructure/getFormFieldStructureListByFormId
