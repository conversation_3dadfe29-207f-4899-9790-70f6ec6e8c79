# 微服务平台

这是一个基于微服务架构的综合平台，包含多个业务模块，如考试系统、表单系统、支付系统、聊天评论系统和聚会游戏平台等。

## 项目结构

```
.
├── taurus-gateway/           # 网关服务
├── exam-main-service/        # 考试主服务
├── exam-qkk-study-service/   # 考试学习服务
├── taurus-form-system/       # 表单系统服务
├── taurus-oss/              # 对象存储服务
├── taurus-pay/              # 支付服务
├── taurus-comment/          # 评论服务
├── taurus-party-games/      # 聚会游戏服务
├── taurus-monolith/         # 单体化部署聚合模块
└── partyGame/               # 聚会游戏前端项目
```

## 子项目说明

### 1. taurus-gateway

- **功能**: API 网关服务
- **技术栈**: Spring Cloud Gateway
- **主要职责**:
  - 请求路由
  - 负载均衡
  - 安全认证
  - 限流控制

### 2. exam-main-service

- **功能**: 考试系统主服务
- **技术栈**: Spring Boot
- **主要功能**: 考试管理、题库管理等

### 3. exam-qkk-study-service

- **功能**: 考试学习服务
- **技术栈**: Spring Boot
- **主要功能**: 学习资料管理、学习进度跟踪等

### 4. taurus-form-system

- **功能**: 表单系统服务
- **技术栈**: Spring Boot
- **主要功能**:
  - 动态表单管理
  - 表单数据处理
  - 文件上传（支持最大 80MB）

### 5. taurus-oss

- **功能**: 对象存储服务
- **技术栈**: Spring Boot
- **主要功能**: 文件存储和管理

### 6. taurus-pay

- **功能**: 支付服务
- **技术栈**: Spring Boot
- **主要功能**: 支付处理、订单管理

### 7. taurus-comment

- **功能**: 评论服务
- **技术栈**: Spring Boot
- **主要功能**: 评论管理、互动功能

### 8. taurus-party-games (后端服务)

- **技术栈**: Spring Boot、MySQL、MyBatis
- **主要功能**:
  - 房间管理
  - 角色/词语分配
  - 用户管理
  - 游戏状态追踪
- **详细文档**: [taurus-party-games/README.md](taurus-party-games/README.md)

### 9. taurus-monolith (单体化部署模块)

- **功能**: 将多个微服务整合到一个单体应用中部署
- **技术栈**: Spring Boot、Spring Cloud
- **主要特点**:
  - 简化部署和运维
  - 降低资源消耗
  - 支持选择性启用服务
- **详细文档**: [taurus-monolith/README.md](taurus-monolith/README.md)

### 10. partyGame (UniApp 前端)

- **技术栈**: UniApp、Vue.js
- **支持平台**: 微信小程序、H5
- **主要功能**:
  - 狼人杀助手：角色分配、房间管理
  - 谁是卧底：词语分配、房间管理
- **详细文档**: [partyGame/README.md](partyGame/README.md)

## 技术架构

### 微服务架构

- 服务注册与发现: Eureka
- 客户端负载均衡: Ribbon
- 服务监控: Spring Boot Admin

### FastMyBatis配置

所有微服务统一使用FastMyBatis 1.8.4版本，并在启动类中配置了@MapperScan注解：

- taurus-form-system: @MapperScan({"com.taurus.formSys.mapper"})
- taurus-oss: @MapperScan({"com.taurus.oss.mapper"})
- taurus-comment: @MapperScan({"com.taurus.comment.mapper"})
- taurus-pay: @MapperScan({"com.taurus.payment.mapper"})
- taurus-party-games: @MapperScan({"com.taurus.partygames.mapper"})

#### 单体化部署特殊配置

单体化服务(taurus-monolith)使用了特殊的多数据源FastMyBatis配置：

1. **依赖配置**: 在pom.xml中显式添加了FastMyBatis依赖
2. **多数据源支持**: 创建了FastMyBatisMultiDataSourceConfig配置类
3. **SqlSessionFactory**: 为每个微服务创建独立的SqlSessionFactory，并设置basePackage属性
4. **Mapper扫描**: 通过MapperScanConfig配置类，为每个微服务配置独立的MapperScannerConfigurer
5. **自动配置排除**: 排除了FastMyBatis的默认自动配置，使用自定义配置

关键修复点：
- 添加了`sqlSessionFactory.setBasePackage()`调用，解决"属性 'basePackage' 必填"错误
- 使用MapperScannerConfigurer替代@MapperScan注解，确保每个Mapper使用正确的SqlSessionFactory

这完全解决了单体化部署中"Invalid bound statement (not found)"的问题。

### 环境要求

- JDK 17
- Node.js 16+ (前端项目)
- MySQL 5.7+
- Maven 3.6+

### 配置说明

- Eureka 服务地址: http://localhost:7070/eureka
- Admin 监控地址: http://localhost:7070/jj
- 文件上传限制: 80MB

## JVM性能优化配置

### 推荐的JVM启动参数

```bash
# 堆内存配置 (根据服务器内存调整)
-Xms2g -Xmx4g

# 垃圾回收器配置 (推荐G1GC)
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:G1HeapRegionSize=16m

# GC日志配置
-Xlog:gc*:logs/gc.log:time,tags
-XX:+UseGCLogFileRotation
-XX:NumberOfGCLogFiles=5
-XX:GCLogFileSize=10M

# 内存溢出时生成堆转储
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=logs/heapdump.hprof

# JIT编译器优化
-XX:+UseCompressedOops
-XX:+UseCompressedClassPointers

# 监控参数
-Djava.awt.headless=true
-Dfile.encoding=UTF-8
-Duser.timezone=Asia/Shanghai
```

### 不同服务的内存配置建议

```bash
# 网关服务 (高并发)
-Xms1g -Xmx2g

# 业务服务 (中等负载)
-Xms512m -Xmx1g

# 文件服务 (大内存需求)
-Xms1g -Xmx2g

# 轻量级服务
-Xms256m -Xmx512m
```

## 数据库连接池优化配置

### Druid连接池推荐配置

```yaml
spring:
  datasource:
    druid:
      # 核心配置
      initial-size: 5          # 初始连接数
      min-idle: 5              # 最小空闲连接数
      max-active: 50           # 最大连接数 (建议不超过100)
      max-wait: 30000          # 获取连接等待超时时间 (30秒)
      
      # 连接检测配置
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      
      # 连接回收配置
      time-between-eviction-runs-millis: 60000    # 检测间隔60秒
      min-evictable-idle-time-millis: 300000      # 连接空闲5分钟后回收
      
      # 预编译语句缓存
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      
      # 监控配置
      filters: stat,wall,slf4j
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
```

## Redis连接池优化配置

### Jedis连接池推荐配置

```yaml
spring:
  redis:
    # 连接配置
    timeout: 5000
    database: 0
    
    # Jedis连接池配置
    jedis:
      pool:
        max-active: 20         # 最大连接数
        max-idle: 10           # 最大空闲连接数
        min-idle: 2            # 最小空闲连接数
        max-wait: 3000         # 最大等待时间
```

## 性能监控配置

### 应用监控

```yaml
# actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
```

### 日志配置优化

```yaml
# 生产环境日志配置
logging:
  level:
    root: INFO
    com.taurus: INFO
    # 关闭SQL日志输出
    org.apache.ibatis.logging: WARN
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    max-size: 100MB
    max-history: 30
```

## 部署方式

### 方式一：微服务分布式部署

```bash
# 构建所有服务
mvn clean package

# 运行网关服务
java -jar taurus-gateway/target/taurus-gateway.jar

# 运行其他服务
java -jar <service-name>/target/<service-name>.jar
```

### 方式二：单体化部署

```bash
# 仅构建单体化部署模块
mvn clean package -pl taurus-monolith -am

# 运行单体化应用
java -jar taurus-monolith/target/taurus-monolith.jar
```

## 注意事项

1. 服务间通信使用 Nacos 进行服务发现
2. 启用了 Ribbon 的饥饿加载模式
3. 文件上传大小限制为 80MB
4. 分布式部署时确保服务启动顺序正确，网关服务需要最先启动
5. 单体化部署时需注意各服务间可能存在的 Bean 冲突

## 许可证

MIT License
