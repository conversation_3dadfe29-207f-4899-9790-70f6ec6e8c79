#!/bin/bash

# Taurus微服务停止脚本
# 使用方法: ./stop-service.sh <service-name>
# 示例: ./stop-service.sh taurus-oss

SERVICE_NAME=$1

if [ -z "$SERVICE_NAME" ]; then
    echo "错误: 请指定服务名称"
    echo "使用方法: $0 <service-name>"
    echo "示例: $0 taurus-oss"
    exit 1
fi

# 设置基础路径
BASE_DIR=$(cd "$(dirname "$0")/.." && pwd)
LOGS_DIR="$BASE_DIR/logs/$SERVICE_NAME"
PID_FILE="$LOGS_DIR/$SERVICE_NAME.pid"

echo "停止服务: $SERVICE_NAME"

# 检查PID文件是否存在
if [ ! -f "$PID_FILE" ]; then
    echo "警告: PID文件不存在: $PID_FILE"
    echo "尝试通过进程名查找服务..."
    
    # 通过进程名查找
    PID=$(ps aux | grep "$SERVICE_NAME.jar" | grep -v grep | awk '{print $2}')
    if [ -z "$PID" ]; then
        echo "未找到运行中的 $SERVICE_NAME 服务"
        exit 0
    fi
else
    PID=$(cat "$PID_FILE")
fi

# 检查进程是否存在
if ! ps -p $PID > /dev/null 2>&1; then
    echo "进程 $PID 不存在，可能已经停止"
    rm -f "$PID_FILE"
    exit 0
fi

echo "找到进程 PID: $PID"

# 优雅停止
echo "发送 TERM 信号..."
kill -TERM $PID

# 等待进程停止
for i in {1..30}; do
    if ! ps -p $PID > /dev/null 2>&1; then
        echo "服务已优雅停止"
        rm -f "$PID_FILE"
        exit 0
    fi
    echo "等待服务停止... ($i/30)"
    sleep 1
done

# 强制停止
echo "优雅停止超时，强制停止服务..."
kill -KILL $PID

# 再次检查
sleep 2
if ps -p $PID > /dev/null 2>&1; then
    echo "错误: 无法停止服务 (PID: $PID)"
    exit 1
else
    echo "服务已强制停止"
    rm -f "$PID_FILE"
fi 