#!/bin/bash

# 单体化配置验证脚本
# 验证 taurus-comment、taurus-pay、taurus-form-system、taurus-oss 四个微服务的集成配置

echo "=========================================="
echo "Taurus 单体化配置验证工具"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查结果计数
TOTAL_CHECKS=0
PASSED_CHECKS=0
WARNING_CHECKS=0
FAILED_CHECKS=0

# 检查函数
check_item() {
    local description="$1"
    local status="$2"
    local message="$3"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    case $status in
        "PASS")
            echo -e "${GREEN}✓${NC} $description"
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
            ;;
        "WARN")
            echo -e "${YELLOW}⚠${NC} $description - $message"
            WARNING_CHECKS=$((WARNING_CHECKS + 1))
            ;;
        "FAIL")
            echo -e "${RED}✗${NC} $description - $message"
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
            ;;
    esac
}

# 1. 检查微服务模块存在性
echo -e "\n${BLUE}1. 检查微服务模块${NC}"
REQUIRED_MODULES=(
    "taurus-comment"
    "taurus-pay"  
    "taurus-form-system"
    "taurus-oss"
)

for module in "${REQUIRED_MODULES[@]}"; do
    if [ -d "$module" ]; then
        if [ -f "$module/pom.xml" ]; then
            check_item "模块 $module" "PASS" "模块存在且有pom.xml"
        else
            check_item "模块 $module" "WARN" "模块存在但缺少pom.xml"
        fi
    else
        check_item "模块 $module" "FAIL" "模块不存在"
    fi
done

# 2. 检查主配置文件
echo -e "\n${BLUE}2. 检查主配置文件${NC}"
MAIN_CONFIG="taurus-monolith/src/main/resources/application.yml"
if [ -f "$MAIN_CONFIG" ]; then
    check_item "主配置文件" "PASS" "application.yml存在"
    
    # 检查配置内容
    if grep -q "enabled-services:" "$MAIN_CONFIG"; then
        check_item "启用服务配置" "PASS" "找到enabled-services配置"
    else
        check_item "启用服务配置" "FAIL" "未找到enabled-services配置"
    fi
    
    if grep -q "service-mappings:" "$MAIN_CONFIG"; then
        check_item "服务映射配置" "PASS" "找到service-mappings配置"
    else
        check_item "服务映射配置" "FAIL" "未找到service-mappings配置"
    fi
else
    check_item "主配置文件" "FAIL" "application.yml不存在"
fi

# 3. 检查dev环境配置
echo -e "\n${BLUE}3. 检查dev环境配置${NC}"
DEV_CONFIG="taurus-monolith/src/main/resources/application-dev.yml"
if [ -f "$DEV_CONFIG" ]; then
    check_item "dev配置文件" "PASS" "application-dev.yml存在"
    
    # 检查配置路径
    if grep -q "config-paths:" "$DEV_CONFIG"; then
        check_item "配置路径设置" "PASS" "找到config-paths配置"
    else
        check_item "配置路径设置" "PASS" "使用简化配置加载（无需config-paths）"
    fi
    
    # 检查Nacos配置
    if grep -q "nacos:" "$DEV_CONFIG"; then
        check_item "Nacos配置" "PASS" "找到Nacos配置"
    else
        check_item "Nacos配置" "WARN" "未找到Nacos配置"
    fi
else
    check_item "dev配置文件" "FAIL" "application-dev.yml不存在"
fi

# 4. 检查启动类配置
echo -e "\n${BLUE}4. 检查启动类配置${NC}"
MAIN_CLASS="taurus-monolith/src/main/java/com/taurus/monolith/TaurusMonolithApplication.java"
if [ -f "$MAIN_CLASS" ]; then
    check_item "启动类" "PASS" "TaurusMonolithApplication.java存在"
    
    # 检查MapperScan注解
    if grep -q "@MapperScan" "$MAIN_CLASS"; then
        check_item "Mapper扫描配置" "PASS" "找到@MapperScan注解"
    else
        check_item "Mapper扫描配置" "WARN" "未找到@MapperScan注解"
    fi
    
    # 检查ComponentScan注解
    if grep -q "@ComponentScan" "$MAIN_CLASS"; then
        check_item "组件扫描配置" "PASS" "找到@ComponentScan注解"
    else
        check_item "组件扫描配置" "WARN" "未找到@ComponentScan注解"
    fi
else
    check_item "启动类" "FAIL" "TaurusMonolithApplication.java不存在"
fi

# 5. 检查路由配置
echo -e "\n${BLUE}5. 检查路由配置${NC}"
ROUTE_CONFIG="taurus-monolith/src/main/java/com/taurus/monolith/config/RouteConfiguration.java"
if [ -f "$ROUTE_CONFIG" ]; then
    check_item "路由配置类" "PASS" "RouteConfiguration.java存在"
    
    # 检查是否包含4个服务的路由
    ROUTE_SERVICES=("comment" "pay" "form" "oss")
    for service in "${ROUTE_SERVICES[@]}"; do
        if grep -q "/$service/" "$ROUTE_CONFIG"; then
            check_item "路由规则 $service" "PASS" "找到${service}服务路由规则"
        else
            check_item "路由规则 $service" "WARN" "未找到${service}服务路由规则"
        fi
    done
else
    check_item "路由配置类" "WARN" "RouteConfiguration.java不存在"
fi

# 6. 检查pom.xml依赖
echo -e "\n${BLUE}6. 检查Maven依赖${NC}"
MONOLITH_POM="taurus-monolith/pom.xml"
if [ -f "$MONOLITH_POM" ]; then
    check_item "单体模块pom.xml" "PASS" "pom.xml存在"
    
    # 检查是否包含4个服务的依赖
    DEPENDENCY_SERVICES=("taurus-form-system" "taurus-oss" "taurus-pay" "taurus-comment")
    for service in "${DEPENDENCY_SERVICES[@]}"; do
        if grep -q "$service" "$MONOLITH_POM"; then
            check_item "依赖 $service" "PASS" "找到${service}依赖"
        else
            check_item "依赖 $service" "FAIL" "未找到${service}依赖"
        fi
    done
else
    check_item "单体模块pom.xml" "FAIL" "pom.xml不存在"
fi

# 7. 检查各微服务配置文件
echo -e "\n${BLUE}7. 检查微服务配置文件${NC}"
for module in "${REQUIRED_MODULES[@]}"; do
    CONFIG_PATH="$module/src/main/resources"
    if [ -d "$CONFIG_PATH" ]; then
        # 检查是否有application配置文件
        if ls "$CONFIG_PATH"/application*.yml >/dev/null 2>&1 || ls "$CONFIG_PATH"/application*.properties >/dev/null 2>&1; then
            check_item "配置文件 $module" "PASS" "找到配置文件"
        else
            check_item "配置文件 $module" "WARN" "未找到application配置文件"
        fi
    else
        check_item "配置文件 $module" "WARN" "配置目录不存在"
    fi
done

# 生成报告
echo -e "\n=========================================="
echo -e "${BLUE}验证报告汇总${NC}"
echo "=========================================="
echo -e "总检查项: ${TOTAL_CHECKS}"
echo -e "${GREEN}通过: ${PASSED_CHECKS}${NC}"
echo -e "${YELLOW}警告: ${WARNING_CHECKS}${NC}"
echo -e "${RED}失败: ${FAILED_CHECKS}${NC}"

# 给出建议
echo -e "\n${BLUE}建议：${NC}"
if [ $FAILED_CHECKS -eq 0 ]; then
    if [ $WARNING_CHECKS -eq 0 ]; then
        echo -e "${GREEN}✓ 所有检查项都通过！可以启动单体化应用。${NC}"
        echo -e "\n${BLUE}启动命令：${NC}"
        echo "cd taurus-monolith"
        echo "mvn clean package -DskipTests"
        echo "java -jar target/taurus-monolith.jar --spring.profiles.active=dev"
    else
        echo -e "${YELLOW}⚠ 有 $WARNING_CHECKS 个警告项，建议检查后再启动。${NC}"
    fi
else
    echo -e "${RED}✗ 有 $FAILED_CHECKS 个失败项，必须解决后才能启动！${NC}"
fi

echo -e "\n详细说明请参考: taurus-monolith/README.md"
echo "==========================================" 