#!/bin/bash

# Taurus微服务启动脚本
# 使用方法: ./start-service.sh <service-name> <profile> <memory-size>
# 示例: ./start-service.sh taurus-oss dev 1g

SERVICE_NAME=$1
PROFILE=${2:-dev}
MEMORY_SIZE=${3:-512m}

if [ -z "$SERVICE_NAME" ]; then
    echo "错误: 请指定服务名称"
    echo "使用方法: $0 <service-name> [profile] [memory-size]"
    echo "示例: $0 taurus-oss dev 1g"
    exit 1
fi

# 设置基础路径
BASE_DIR=$(cd "$(dirname "$0")/.." && pwd)
SERVICE_DIR="$BASE_DIR/$SERVICE_NAME"
JAR_FILE="$SERVICE_DIR/target/$SERVICE_NAME.jar"

# 检查JAR文件是否存在
if [ ! -f "$JAR_FILE" ]; then
    echo "错误: JAR文件不存在: $JAR_FILE"
    echo "请先执行: mvn clean package"
    exit 1
fi

# 创建日志目录
LOGS_DIR="$BASE_DIR/logs/$SERVICE_NAME"
mkdir -p "$LOGS_DIR"

# JVM参数配置
JVM_OPTS="-server"

# 内存配置
case $MEMORY_SIZE in
    "256m")
        JVM_OPTS="$JVM_OPTS -Xms128m -Xmx256m -XX:MetaspaceSize=64m -XX:MaxMetaspaceSize=128m"
        ;;
    "512m")
        JVM_OPTS="$JVM_OPTS -Xms256m -Xmx512m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m"
        ;;
    "1g")
        JVM_OPTS="$JVM_OPTS -Xms512m -Xmx1g -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m"
        ;;
    "2g")
        JVM_OPTS="$JVM_OPTS -Xms1g -Xmx2g -XX:MetaspaceSize=512m -XX:MaxMetaspaceSize=1g"
        ;;
    *)
        JVM_OPTS="$JVM_OPTS -Xms256m -Xmx512m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m"
        ;;
esac

# 垃圾回收器配置
JVM_OPTS="$JVM_OPTS -XX:+UseG1GC"
JVM_OPTS="$JVM_OPTS -XX:MaxGCPauseMillis=200"
JVM_OPTS="$JVM_OPTS -XX:G1HeapRegionSize=16m"

# GC日志配置
JVM_OPTS="$JVM_OPTS -Xlog:gc*:$LOGS_DIR/gc.log:time,tags"
JVM_OPTS="$JVM_OPTS -XX:+UseGCLogFileRotation"
JVM_OPTS="$JVM_OPTS -XX:NumberOfGCLogFiles=5"
JVM_OPTS="$JVM_OPTS -XX:GCLogFileSize=10M"

# 内存溢出处理
JVM_OPTS="$JVM_OPTS -XX:+HeapDumpOnOutOfMemoryError"
JVM_OPTS="$JVM_OPTS -XX:HeapDumpPath=$LOGS_DIR/heapdump.hprof"

# 性能优化参数
JVM_OPTS="$JVM_OPTS -XX:+UseCompressedOops"
JVM_OPTS="$JVM_OPTS -XX:+UseCompressedClassPointers"
JVM_OPTS="$JVM_OPTS -XX:+OptimizeStringConcat"

# 系统参数
JVM_OPTS="$JVM_OPTS -Djava.awt.headless=true"
JVM_OPTS="$JVM_OPTS -Dfile.encoding=UTF-8"
JVM_OPTS="$JVM_OPTS -Duser.timezone=Asia/Shanghai"

# 应用参数
APP_OPTS="--spring.profiles.active=$PROFILE"
APP_OPTS="$APP_OPTS --logging.file.path=$LOGS_DIR"
APP_OPTS="$APP_OPTS --server.tomcat.basedir=$LOGS_DIR/tomcat"

# 启动命令
JAVA_CMD="java $JVM_OPTS -jar $JAR_FILE $APP_OPTS"

echo "========================================="
echo "启动服务: $SERVICE_NAME"
echo "环境配置: $PROFILE"
echo "内存大小: $MEMORY_SIZE"
echo "日志目录: $LOGS_DIR"
echo "启动命令: $JAVA_CMD"
echo "========================================="

# 检查服务是否已经在运行
PID_FILE="$LOGS_DIR/$SERVICE_NAME.pid"
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if ps -p $PID > /dev/null 2>&1; then
        echo "警告: 服务 $SERVICE_NAME 已经在运行 (PID: $PID)"
        echo "如需重启，请先执行: ./stop-service.sh $SERVICE_NAME"
        exit 1
    else
        rm -f "$PID_FILE"
    fi
fi

# 启动服务
nohup $JAVA_CMD > "$LOGS_DIR/application.log" 2>&1 &
PID=$!

# 保存PID
echo $PID > "$PID_FILE"

echo "服务启动成功!"
echo "PID: $PID"
echo "日志文件: $LOGS_DIR/application.log"
echo "GC日志: $LOGS_DIR/gc.log"

# 等待几秒检查启动状态
sleep 3
if ps -p $PID > /dev/null 2>&1; then
    echo "服务运行正常"
else
    echo "警告: 服务可能启动失败，请检查日志"
    cat "$LOGS_DIR/application.log" | tail -20
fi 