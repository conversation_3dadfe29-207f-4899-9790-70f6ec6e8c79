#!/bin/bash

# Taurus微服务性能检查脚本
# 使用方法: ./performance-check.sh [service-name]

SERVICE_NAME=$1
BASE_DIR=$(cd "$(dirname "$0")/.." && pwd)

echo "========================================="
echo "Taurus微服务性能检查报告"
echo "时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo "========================================="

# 检查系统资源
echo
echo "【系统资源使用情况】"
echo "CPU使用率:"
top -l 1 | grep "CPU usage" | awk '{print $3,$5}' || echo "无法获取CPU信息"

echo "内存使用情况:"
free -h 2>/dev/null || vm_stat | head -5

echo "磁盘使用情况:"
df -h | grep -E "(Filesystem|/dev/)"

# 检查Java进程
echo
echo "【Java进程信息】"
if [ -n "$SERVICE_NAME" ]; then
    echo "检查服务: $SERVICE_NAME"
    PIDS=$(ps aux | grep "$SERVICE_NAME.jar" | grep -v grep | awk '{print $2}')
else
    echo "检查所有Taurus服务:"
    PIDS=$(ps aux | grep "taurus.*\.jar" | grep -v grep | awk '{print $2}')
fi

if [ -z "$PIDS" ]; then
    echo "未找到运行中的服务"
else
    for PID in $PIDS; do
        echo "----------------------------------------"
        PROCESS_INFO=$(ps -p $PID -o pid,ppid,pcpu,pmem,vsz,rss,time,comm --no-headers)
        if [ -n "$PROCESS_INFO" ]; then
            echo "PID: $PID"
            echo "进程信息: $PROCESS_INFO"
            
            # 检查JVM参数
            echo "JVM参数:"
            ps -p $PID -o args --no-headers | tr ' ' '\n' | grep -E "^-X|^-XX" | head -10
            
            # 检查GC信息 (如果有jstat命令)
            if command -v jstat >/dev/null 2>&1; then
                echo "GC信息:"
                jstat -gc $PID 2>/dev/null | tail -1 || echo "无法获取GC信息"
            fi
            
            # 检查线程数
            THREAD_COUNT=$(ps -p $PID -o nlwp --no-headers 2>/dev/null)
            echo "线程数: ${THREAD_COUNT:-未知}"
            
            # 检查文件描述符
            if [ -d "/proc/$PID/fd" ]; then
                FD_COUNT=$(ls /proc/$PID/fd 2>/dev/null | wc -l)
                echo "文件描述符: $FD_COUNT"
            fi
        fi
    done
fi

# 检查端口占用
echo
echo "【端口使用情况】"
echo "Taurus服务端口:"
netstat -tlnp 2>/dev/null | grep -E ":(808[0-9]|7070)" | head -10 || \
lsof -i -P -n | grep -E ":(808[0-9]|7070)" | head -10

# 检查数据库连接
echo
echo "【数据库连接检查】"
DB_CONNECTIONS=$(netstat -an 2>/dev/null | grep ":3306" | grep ESTABLISHED | wc -l)
echo "MySQL连接数: $DB_CONNECTIONS"

# 检查Redis连接
REDIS_CONNECTIONS=$(netstat -an 2>/dev/null | grep ":6379" | grep ESTABLISHED | wc -l)
echo "Redis连接数: $REDIS_CONNECTIONS"

# 检查日志文件大小
echo
echo "【日志文件大小】"
LOGS_DIR="$BASE_DIR/logs"
if [ -d "$LOGS_DIR" ]; then
    echo "日志目录: $LOGS_DIR"
    du -sh "$LOGS_DIR"/* 2>/dev/null | sort -hr | head -10
    
    # 检查错误日志
    echo
    echo "【最近错误日志】"
    find "$LOGS_DIR" -name "*.log" -type f -exec grep -l "ERROR\|Exception\|OutOfMemoryError" {} \; 2>/dev/null | head -5 | while read logfile; do
        echo "文件: $logfile"
        grep -E "ERROR|Exception|OutOfMemoryError" "$logfile" 2>/dev/null | tail -3
        echo "---"
    done
fi

# 检查服务健康状态
echo
echo "【服务健康检查】"
ACTUATOR_PORTS=(8080 8081 8082 8083 8084 8085 8086 8087 8088 8089)

for port in "${ACTUATOR_PORTS[@]}"; do
    HEALTH_URL="http://localhost:$port/actuator/health"
    RESPONSE=$(curl -s -w "%{http_code}" -o /dev/null "$HEALTH_URL" --connect-timeout 3 2>/dev/null)
    if [ "$RESPONSE" = "200" ]; then
        SERVICE_INFO=$(curl -s "$HEALTH_URL" 2>/dev/null | grep -o '"status":"[^"]*"' | head -1)
        echo "端口 $port: 健康 ($SERVICE_INFO)"
    elif [ -n "$RESPONSE" ]; then
        echo "端口 $port: 异常 (HTTP $RESPONSE)"
    fi
done

# 性能建议
echo
echo "【性能优化建议】"

# 检查内存使用
if [ -n "$PIDS" ]; then
    for PID in $PIDS; do
        RSS=$(ps -p $PID -o rss --no-headers 2>/dev/null)
        if [ -n "$RSS" ] && [ "$RSS" -gt 1048576 ]; then  # 1GB
            echo "⚠️  进程 $PID 内存使用过高 (${RSS}KB)"
        fi
    done
fi

# 检查日志文件大小
if [ -d "$LOGS_DIR" ]; then
    LARGE_LOGS=$(find "$LOGS_DIR" -name "*.log" -size +100M 2>/dev/null)
    if [ -n "$LARGE_LOGS" ]; then
        echo "⚠️  发现大日志文件，建议清理:"
        echo "$LARGE_LOGS"
    fi
fi

# 检查GC频率建议
echo "💡 性能优化建议:"
echo "   1. 定期检查GC日志，确保GC停顿时间在合理范围内"
echo "   2. 监控数据库连接池使用情况"
echo "   3. 定期清理过大的日志文件"
echo "   4. 使用APM工具进行深度性能分析"

echo
echo "========================================="
echo "性能检查完成"
echo "=========================================" 