#!/bin/bash

# 微服务集成兼容性检查脚本
# 在进行微服务单体化集成前，运行此脚本进行基础检查

echo "=================================================="
echo "微服务集成兼容性检查工具"
echo "=================================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查结果计数
TOTAL_CHECKS=0
PASSED_CHECKS=0
WARNING_CHECKS=0
FAILED_CHECKS=0

# 检查函数
check_item() {
    local description="$1"
    local status="$2"
    local message="$3"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    case $status in
        "PASS")
            echo -e "${GREEN}✓${NC} $description"
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
            ;;
        "WARN")
            echo -e "${YELLOW}⚠${NC} $description - $message"
            WARNING_CHECKS=$((WARNING_CHECKS + 1))
            ;;
        "FAIL")
            echo -e "${RED}✗${NC} $description - $message"
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
            ;;
    esac
}

# 1. 检查Java版本
echo -e "\n${BLUE}1. 检查Java环境${NC}"
if command -v java >/dev/null 2>&1; then
    JAVA_VERSION=$(java -version 2>&1 | head -1 | cut -d'"' -f2)
    if [[ "$JAVA_VERSION" == 1.8* ]]; then
        check_item "Java版本" "PASS" "Java $JAVA_VERSION"
    else
        check_item "Java版本" "WARN" "建议使用Java 1.8，当前版本: $JAVA_VERSION"
    fi
else
    check_item "Java安装" "FAIL" "未找到Java环境"
fi

# 2. 检查Maven环境
echo -e "\n${BLUE}2. 检查Maven环境${NC}"
if command -v mvn >/dev/null 2>&1; then
    MVN_VERSION=$(mvn -version | head -1 | cut -d' ' -f3)
    check_item "Maven安装" "PASS" "Maven $MVN_VERSION"
else
    check_item "Maven安装" "FAIL" "未找到Maven环境"
fi

# 3. 检查端口占用
echo -e "\n${BLUE}3. 检查端口占用情况${NC}"
PORTS_TO_CHECK=(8080 8081 8082 8083 8084 8085)

for port in "${PORTS_TO_CHECK[@]}"; do
    if lsof -i :$port >/dev/null 2>&1; then
        PROCESS=$(lsof -i :$port | tail -1 | awk '{print $1}')
        check_item "端口 $port" "WARN" "端口被进程 $PROCESS 占用"
    else
        check_item "端口 $port" "PASS" "端口可用"
    fi
done

# 4. 检查内存使用情况
echo -e "\n${BLUE}4. 检查系统资源${NC}"
if command -v free >/dev/null 2>&1; then
    MEMORY_TOTAL=$(free -m | awk 'NR==2{print $2}')
    MEMORY_USED=$(free -m | awk 'NR==2{print $3}')
    MEMORY_USAGE=$((MEMORY_USED * 100 / MEMORY_TOTAL))
    
    if [ $MEMORY_USAGE -lt 70 ]; then
        check_item "内存使用率" "PASS" "使用率 ${MEMORY_USAGE}%"
    elif [ $MEMORY_USAGE -lt 85 ]; then
        check_item "内存使用率" "WARN" "使用率 ${MEMORY_USAGE}%，建议释放内存"
    else
        check_item "内存使用率" "FAIL" "使用率 ${MEMORY_USAGE}%，内存不足"
    fi
    
    if [ $MEMORY_TOTAL -lt 2048 ]; then
        check_item "内存容量" "WARN" "总内存 ${MEMORY_TOTAL}MB，建议4GB以上"
    else
        check_item "内存容量" "PASS" "总内存 ${MEMORY_TOTAL}MB"
    fi
else
    check_item "内存检查" "WARN" "无法检查内存使用情况(非Linux系统)"
fi

# 5. 检查配置文件
echo -e "\n${BLUE}5. 检查配置文件${NC}"
CONFIG_FILES=(
    "taurus-monolith/src/main/resources/application.yml"
    "taurus-monolith/src/main/resources/application-monolith.yml"
)

for config_file in "${CONFIG_FILES[@]}"; do
    if [ -f "$config_file" ]; then
        check_item "配置文件 $(basename $config_file)" "PASS" "文件存在"
    else
        check_item "配置文件 $(basename $config_file)" "FAIL" "文件不存在"
    fi
done

# 6. 检查各微服务模块
echo -e "\n${BLUE}6. 检查微服务模块${NC}"
MODULES=(
    "taurus-pay"
    "taurus-oss"
    "taurus-comment"
    "taurus-form-system"
    "taurus-llm"
    "taurus-sse"
)

for module in "${MODULES[@]}"; do
    if [ -d "$module" ]; then
        if [ -f "$module/pom.xml" ]; then
            check_item "模块 $module" "PASS" "模块存在且有pom.xml"
        else
            check_item "模块 $module" "WARN" "模块存在但缺少pom.xml"
        fi
    else
        check_item "模块 $module" "WARN" "模块不存在或未配置"
    fi
done

# 7. 检查数据库连接
echo -e "\n${BLUE}7. 检查数据库连接${NC}"
if command -v mysql >/dev/null 2>&1; then
    check_item "MySQL客户端" "PASS" "MySQL客户端已安装"
    
    # 尝试连接数据库（需要根据实际配置调整）
    if mysql -h localhost -u root -e "SELECT 1;" >/dev/null 2>&1; then
        check_item "数据库连接" "PASS" "可以连接到MySQL"
    else
        check_item "数据库连接" "WARN" "无法连接到MySQL，请检查配置"
    fi
else
    check_item "MySQL客户端" "WARN" "未安装MySQL客户端"
fi

# 8. 检查Redis连接
echo -e "\n${BLUE}8. 检查Redis连接${NC}"
if command -v redis-cli >/dev/null 2>&1; then
    check_item "Redis客户端" "PASS" "Redis客户端已安装"
    
    if redis-cli ping >/dev/null 2>&1; then
        check_item "Redis连接" "PASS" "可以连接到Redis"
    else
        check_item "Redis连接" "WARN" "无法连接到Redis，请检查配置"
    fi
else
    check_item "Redis客户端" "WARN" "未安装Redis客户端"
fi

# 生成报告
echo -e "\n=================================================="
echo -e "${BLUE}检查报告汇总${NC}"
echo "=================================================="
echo -e "总检查项: ${TOTAL_CHECKS}"
echo -e "${GREEN}通过: ${PASSED_CHECKS}${NC}"
echo -e "${YELLOW}警告: ${WARNING_CHECKS}${NC}"
echo -e "${RED}失败: ${FAILED_CHECKS}${NC}"

# 给出建议
echo -e "\n${BLUE}建议：${NC}"
if [ $FAILED_CHECKS -eq 0 ]; then
    if [ $WARNING_CHECKS -eq 0 ]; then
        echo -e "${GREEN}✓ 所有检查项都通过！可以安全进行微服务集成。${NC}"
    else
        echo -e "${YELLOW}⚠ 有 $WARNING_CHECKS 个警告项，建议解决后再进行集成。${NC}"
    fi
else
    echo -e "${RED}✗ 有 $FAILED_CHECKS 个失败项，必须解决后才能进行集成！${NC}"
fi

echo -e "\n详细的集成要求和规则请参考: taurus-monolith/README.md"
echo "==================================================" 