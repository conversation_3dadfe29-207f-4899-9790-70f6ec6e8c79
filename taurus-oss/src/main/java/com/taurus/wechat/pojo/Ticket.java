package com.taurus.wechat.pojo;

/**
 * 
 * <AUTHOR>
 * 
 * 
 *         {"ticket":"gQH47joAAAAAAAAAASxodHRwOi8vd2VpeGluLnFxLmNvbS9xL2taZ2Z3TVRtNzJXV1Brb3ZhYmJJAAIEZ23sUwMEmm3sUw==","expire_seconds":60,"url":"http:\/\/weixin.qq.com\/q\/kZgfwMTm72WWPkovabbI"}
 */
public class Ticket {

	// ticket是用来进行下一步调用的凭证
	String ticketCode;

	// 二维码的有效期
	int expireTime;

	// url是生成的二维码扫描后要打开的链接，即二维码代表的url值
	String actionUrl;

	/**
	 * @return the ticketCode
	 */
	public String getTicketCode() {
		return ticketCode;
	}

	/**
	 * @param ticketCode the ticketCode to set
	 */
	public void setTicketCode(String ticketCode) {
		this.ticketCode = ticketCode;
	}

	/**
	 * @return the expireTime
	 */
	public int getExpireTime() {
		return expireTime;
	}

	/**
	 * @param string the expireTime to set
	 */
	public void setExpireTime(int string) {
		this.expireTime = string;
	}

	/**
	 * @return the actionUrl
	 */
	public String getActionUrl() {
		return actionUrl;
	}

	/**
	 * @param actionUrl the actionUrl to set
	 */
	public void setActionUrl(String actionUrl) {
		this.actionUrl = actionUrl;
	}

}
