package com.taurus.wechat.pojo;

/**
 * 关注事件推送
 * 
 * <AUTHOR>
 *
 *         ToUserName 开发者微信号 FromUserName 发送者账号（openid） CreateTime 消息创建时间（整型）
 *         MsgType 消息类型 event Event 事件类型（subscribe） EventKey
 *         事件KEY值，qrscene_为前缀，后面为二维码参数值 Ticket 二维码ticke值，可以用来换取二维码图片
 * 
 * 
 *         用户扫描带场景值二维码时，可能推送以下两种事件：
 *         如果用户还未关注公众号，则用户可以关注公众号，关注后微信会将带场景值关注事件推送给开发者。
 *         如果用户已经关注公众号，则微信会将带场景值扫描事件推送给开发者。 1. 用户未关注时，进行关注后的事件推送
 * 
 *         <xml> <ToUserName><![CDATA[gh_45072270791c]]></ToUserName>
 *         <FromUserName><![CDATA[o7Lp5t6n59DeX3U0C7Kric9qEx-Q]]></FromUserName>
 *         <CreateTime>1389684286</CreateTime>
 *         <MsgType><![CDATA[event]]></MsgType>
 *         <Event><![CDATA[subscribe]]></Event>
 *         <EventKey><![CDATA[qrscene_1000]]></EventKey>
 *         <Ticket><![CDATA[gQHi8DoAAAAAAAAAASxodHRwOi8vd2VpeGluLnFxLmNvbS9xL0UweTNxNi1sdlA3RklyRnNKbUFvAAIELdnUUgMEAAAAAA==]]></Ticket>
 *         </xml>
 * 
 *         参数说明
 * 
 *         2. 用户已关注时的事件推送
 * 
 *         <xml> <ToUserName><![CDATA[gh_45072270791c]]></ToUserName>
 *         <FromUserName><![CDATA[o7Lp5t6n59DeX3U0C7Kric9qEx-Q]]></FromUserName>
 *         <CreateTime>1389684184</CreateTime>
 *         <MsgType><![CDATA[event]]></MsgType> <Event><![CDATA[SCAN]]></Event>
 *         <EventKey><![CDATA[1000]]></EventKey>
 *         <Ticket><![CDATA[gQHi8DoAAAAAAAAAASxodHRwOi8vd2VpeGluLnFxLmNvbS9xL0UweTNxNi1sdlA3RklyRnNKbUFvAAIELdnUUgMEAAAAAA==]]></Ticket>
 *         </xml>
 * 
 * 
 *
 */

public class FollowEventPush {

	String toUserName;
	String fromUserName;
	String createTime;
	String msgType;
	String event;
	String eventKey;
	String ticket;

	public String getToUserName() {
		return toUserName;
	}

	public void setToUserName(String toUserName) {
		this.toUserName = toUserName;
	}

	public String getFromUserName() {
		return fromUserName;
	}

	public void setFromUserName(String fromUserName) {
		this.fromUserName = fromUserName;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getMsgType() {
		return msgType;
	}

	public void setMsgType(String msgType) {
		this.msgType = msgType;
	}

	public String getEvent() {
		return event;
	}

	public void setEvent(String event) {
		this.event = event;
	}

	public String getEventKey() {
		return eventKey;
	}

	public void setEventKey(String eventKey) {
		this.eventKey = eventKey;
	}

	public String getTicket() {
		return ticket;
	}

	public void setTicket(String ticket) {
		this.ticket = ticket;
	}

}
