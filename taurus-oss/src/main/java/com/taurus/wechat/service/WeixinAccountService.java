package com.taurus.wechat.service;

import org.springframework.stereotype.Service;

import com.taurus.wechat.pojo.WeixinAccount;

@Service("ossWeixinAccountService")
public class WeixinAccountService {

	public WeixinAccount getWeiXinAccountByName(String accountName) {
		WeixinAccount account = new WeixinAccount();
		if (accountName.equals("punchcard")) {
			account.setAccount("punchcard");
			account.setAppId("wx2bed744d35a9961f");
			account.setAppSecret("e05e8cdbcca819e6f6c0c3b8af3f4b55");
		} else if (accountName.equals("test")) {
			account.setAccount("punchcard");
			account.setAppId("wx2bed744d35a9961f");
			account.setAppSecret("e05e8cdbcca819e6f6c0c3b8af3f4b55");
		} else if (accountName.equals("questionnaire")) {
			account.setAccount("questionnaire");
			account.setAppId("wx57045e83d12ea5c3");
			account.setAppSecret("290b6dc75690686d5a68054e61d253d5");
		} else if (accountName.equals("etea")) {
			account.setAccount("etea");
			account.setAppId("wxa38e954efdceffbe");
			account.setAppSecret("9bd01d2e11ae1595665a823935adb449");
		} else if (accountName.equals("qkk")) {
			account.setAccount("qkk");
			account.setAppId("wxfc49b21929e8692f");
			account.setAppSecret("55d41cfebb76f748cc45c62366607293");
		} else if (accountName.equals("universalForm")) {
			account.setAccount("universalForm");
			account.setAppId("wx44cae5271ed61873");
			account.setAppSecret("bd0dd23fb7fb2715bc6e31b80dd0c272");
		}
		return account;
	}
}
