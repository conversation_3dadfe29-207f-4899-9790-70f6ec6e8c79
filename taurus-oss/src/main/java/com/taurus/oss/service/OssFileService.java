package com.taurus.oss.service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.gitee.fastmybatis.core.query.Query;
import com.gitee.fastmybatis.core.util.MyBeanUtil;
import com.taurus.entity.ResponseObject;
import com.taurus.oss.entity.OssFile;
import com.taurus.oss.entity.OssFileAudio;
import com.taurus.oss.entity.OssRespository;
import com.taurus.oss.mapper.OssFileMapper;

@Service
public class OssFileService {

	@Autowired
	private AliOssObjectService aliOssObjectService;

	@Autowired
	private NativeOssObjectService nativeOssObjectService;

	@Autowired
	private OssFileAudioService ossFileAudioService;
	@Autowired
	@Qualifier("ossOssFileMapper")
	private OssFileMapper ossFileMapper;

	/**
	 * 创建一条成功上传文件的记录
	 * 
	 * @param entity
	 * @return
	 */
	public OssFile create(OssFile entity) {
		ossFileMapper.saveIgnoreNull(entity);
		return entity;
	}

	/**
	 * 更新
	 * 
	 * @param entity
	 */
	public void update(OssFile entity) {
		ossFileMapper.updateIgnoreNull(entity);
	}

	/**
	 * 获取某个ossFile详情
	 * 
	 * @param id
	 * @return
	 */
	public OssFile getOssFileById(Integer id) {
		return ossFileMapper.getById(id);
	}

	/**
	 * 获取文件列表
	 * 
	 * @param productName
	 * @param moduleName
	 * @param fileType
	 * @param fileFormat
	 * @param using
	 * @param pageIndex
	 * @param pageSize
	 * @return
	 */
	public List<OssFile> getOssFileList(String productName, String moduleName, Integer userId, String fileType,
			String fileFormat, Boolean using, Integer pageIndex, Integer pageSize) {
		Query query = new Query().eq("product_name", productName).eq("module_name", moduleName);
		if (userId != null) {
			query.eq("user_id", userId);
		}
		if (fileType != null) {
			query.eq("file_type", fileType);
		}
		if (fileFormat != null) {
			query.eq("file_format", fileFormat);
		}
		if (using != null) {
			query.eq("using", using);
		}
		query.page(pageIndex, pageSize);
		return ossFileMapper.list(query);
	}

	public List<OssFile> getList(String objectName, String fileType) {
		Query query = new Query().like("object_name", objectName).eq("file_type", fileType);
		return ossFileMapper.list(query);
	}

	/**
	 * 向oss仓库上传文件
	 * 
	 * @param request
	 * @param ossFile
	 * @param multipartFile
	 * @param respository
	 */
	public ResponseObject upload(HttpServletRequest request, OssFile ossFile, MultipartFile multipartFile,
			OssRespository respository) {
		String objectName = ossFile.getObjectName();
		String type = respository.getType();
		boolean flag = false;

		if (type.equals("ali")) {
			flag = aliOssObjectService.uploadFile(multipartFile, respository, objectName);
		} else if (type.equals("native")) {
			flag = nativeOssObjectService.uploadFile(multipartFile, respository, objectName);
		} else {
			return ResponseObject.failure("UNRECOGNIZED_OSS_FILE_TYPE");
		}
		if (flag) {
			ossFile = create(ossFile);
			String duration = request.getParameter("duration");
			if (ossFile.getFileType().equals("A") && duration != null) {// 当文件是audio时
				OssFileAudio ossFileAudio = new OssFileAudio();
				ossFileAudio.setOssFileId(ossFile.getId());
				ossFileAudio.setDuration(Integer.parseInt(duration));
				ossFileAudioService.create(ossFileAudio);
			}

			Map<String, Object> res = MyBeanUtil.pojoToMap(ossFile);
			if (respository.getType().equals("ali")) {
				String url = aliOssObjectService.generateAccessUrl(respository, objectName);
				res.put("url", url.toString());
			} else if (respository.getType().equals("native")) {
				String url = nativeOssObjectService.generateAccessUrl(respository, objectName);
				res.put("url", url.toString());
			}
			return ResponseObject.success(res);
		} else {
			return ResponseObject.failure("UPLOAD_FAIL");
		}
	}

	/**
	 * 获取文件类型
	 * 
	 * @param format
	 * @return
	 */
	public String getFileType(String fileName) {
		String format = fileName.substring(fileName.lastIndexOf(".") + 1);
		String[] pic = { "jpg", "png", "jpeg", "gif", "icon" };
		String[] audio = { "mp3", "m4a", "aac", "ogg", "wav" };
		String[] video = { "mp4", "avi", "mkv" };

		if (Arrays.asList(pic).contains(format)) {
			return "P";
		} else if (Arrays.asList(audio).contains(format)) {
			return "A";
		} else if (Arrays.asList(video).contains(format)) {
			return "V";
		} else {
			return "O";
		}
	}
}
