package com.taurus.oss.service;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.oss.model.MatchMode;
import com.aliyun.oss.model.PolicyConditions;
import com.taurus.oss.entity.OssRespository;
import com.taurus.oss.mapper.OssRespositoryMapper;

@Service
public class OssRespositoryService {
	
	@Autowired
	private OssRespositoryMapper ossRespositoryMapper;

	/**
	 * 获取仓库配置信息
	 * 
	 * @param bucketName
	 * @return
	 */
	public OssRespository getEntity(String respositoryName) {
		return ossRespositoryMapper.getByColumn("bucket_name", respositoryName);
	}

	/**
	 * 服务器端签名,用于客户端文件直传
	 * 
	 * @param respository
	 * @return
	 */
	public JSONObject getAliUploadPolicy(OssRespository respository,String dir) {
		
//		OSS client = new OSSClientBuilder().build(respository.getInternetEndpoint(), respository.getAccessKeyId(),
//				respository.getAccessKeySecret());
		//获取一个client
		OSS client =AliOssObjectService.getOssClient(respository);
		
		try {
			long expireTime = 30;
			long expireEndTime = System.currentTimeMillis() + expireTime * 1000;
			Date expiration = new Date(expireEndTime);
			PolicyConditions policyConds = new PolicyConditions();
			policyConds.addConditionItem(PolicyConditions.COND_CONTENT_LENGTH_RANGE, 0, 1048576000);
			policyConds.addConditionItem(MatchMode.StartWith, PolicyConditions.COND_KEY, dir);

			String postPolicy = client.generatePostPolicy(expiration, policyConds);
			byte[] binaryData = postPolicy.getBytes("utf-8");
			String encodedPolicy = BinaryUtil.toBase64String(binaryData);
			String postSignature = client.calculatePostSignature(postPolicy);

			Map<String, String> respMap = new LinkedHashMap<String, String>();
			String accessId = respository.getAccessKeyId();
			String host = respository.getBindedInternetUrl();
			respMap.put("accessid", accessId);
			respMap.put("policy", encodedPolicy);
			respMap.put("signature", postSignature);
			respMap.put("dir", dir);
			respMap.put("host", host);
			respMap.put("expire", String.valueOf(expireEndTime / 1000));

//			JSONObject jasonCallback = new JSONObject();
//			// callbackUrl为 上传回调服务器的URL，请将下面的IP和Port配置为您自己的真实信息。
//			String callbackUrl = "http://***********.:8888";
//			jasonCallback.put("callbackUrl", callbackUrl);
//			jasonCallback.put("callbackBody",
//					"filename=${object}&size=${size}&mimeType=${mimeType}&height=${imageInfo.height}&width=${imageInfo.width}");
//			jasonCallback.put("callbackBodyType", "application/x-www-form-urlencoded");
//			String base64CallbackBody = BinaryUtil.toBase64String(jasonCallback.toString().getBytes());
//			respMap.put("callback", base64CallbackBody);

			JSONObject ja1 = JSONObject.parseObject(JSONObject.toJSONString(respMap));
			return ja1;

		} catch (Exception e) {
			System.out.println(e.getMessage());
		}
		return null;
	}
}
