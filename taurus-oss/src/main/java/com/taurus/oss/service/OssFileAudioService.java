package com.taurus.oss.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.taurus.oss.entity.OssFileAudio;
import com.taurus.oss.mapper.OssFileAudioMapper;

@Service
public class OssFileAudioService {
	@Autowired
	@Qualifier("ossFileAudioMapper")
	private OssFileAudioMapper ossFileAudioMapper;

	public void create(OssFileAudio ossFileAudio) {
		ossFileAudioMapper.saveIgnoreNull(ossFileAudio);
	}

}
