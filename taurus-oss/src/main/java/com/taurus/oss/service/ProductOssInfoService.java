package com.taurus.oss.service;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.gitee.fastmybatis.core.query.Query;
import com.taurus.oss.entity.OssRespository;
import com.taurus.oss.entity.ProductOssInfo;
import com.taurus.oss.mapper.ProductOssInfoMapper;
import com.taurus.service.RedisService;

/**
 * 产品oss配置信息
 * 
 * <AUTHOR>
 *
 */
@Service
public class ProductOssInfoService {

	@Autowired
	private RedisService redisService;

	@Autowired
	private ProductOssInfoMapper productOssInfoMapper;

	/**
	 * 获取产品的oss信息
	 * 
	 * @param productName
	 * @param moduleName
	 */
	public ProductOssInfo getEntity(String productName, String moduleName) {
		Query query = new Query().eq("product_name", productName).eq("module_name", moduleName);
		return productOssInfoMapper.getByQuery(query);
	}

	/**
	 * 获取对应的仓库
	 * 
	 * @param productName
	 * @param moduleName
	 * @return
	 */
	public Map<String, Object> getProductOssInfoAndOssRespository(String productName, String moduleName) {
		StringBuffer keyBuffer = new StringBuffer("ossInfo_").append(productName).append(moduleName);
		String value = redisService.get(keyBuffer.toString());
		if (StringUtils.isEmpty(value)) {
			Map<String, Object> map = productOssInfoMapper.getProductOssInfoAndOssRespository(productName, moduleName);
			ProductOssInfo ossInfo = null;
			OssRespository respository =null;
			if(map!=null) {
				ossInfo=new ProductOssInfo();
				ossInfo.setId((Integer) map.get("id"));
				ossInfo.setProductName(productName);
				ossInfo.setModuleName(moduleName);
				ossInfo.setRespositoryName((String) map.get("respositoryName"));
				ossInfo.setStorageBasePath((String) map.get("storageBasePath"));
				
				respository = new OssRespository();
				respository.setBucketName((String) map.get("bucketName"));
				respository.setInternetEndpoint((String)map.get("internetEndpoint"));
				respository.setInternalEndpoint((String)map.get("internalEndpoint"));
				
				respository.setType((String) map.get("type"));
				respository.setAccessKeyId((String) map.get("accessKeyId"));
				respository.setAccessKeySecret((String) map.get("accessKeySecret"));
				respository.setBindedInternetUrl((String)map.get("bindedInternetUrl"));
				respository.setBindedInternalUrl((String)map.get("bindedInternalUrl"));
			}

			Map<String, Object> result = new HashMap<>();
			if(ossInfo!=null&&respository!=null) {
				result.put("productOssInfo", ossInfo);
				result.put("ossRespository", respository);
				redisService.put(keyBuffer.toString(), result, new Long(365 * 24 * 60));// 保存一年
			}
			return result;
		} else {
			JSONObject resultJson = JSONObject.parseObject(value);
			JSONObject productOssInfoJson = resultJson.getJSONObject("productOssInfo");
			JSONObject ossRespositoryJson = resultJson.getJSONObject("ossRespository");
			
			ProductOssInfo ossInfo = JSONObject.parseObject(productOssInfoJson.toJSONString(), ProductOssInfo.class);
			OssRespository respository=JSONObject.parseObject(ossRespositoryJson.toJSONString(),OssRespository.class);
			Map<String, Object> result = new HashMap<>();
			result.put("productOssInfo", ossInfo);
			result.put("ossRespository", respository);
			return result;
		}
	}

}
