package com.taurus.oss.service;

import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Date;
import java.util.Formatter;
import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.druid.util.StringUtils;
import com.aliyun.oss.ClientException;
import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.oss.common.utils.IOUtils;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.GenericResult;
import com.aliyun.oss.model.ProcessObjectRequest;
import com.aliyun.oss.model.PutObjectRequest;
import com.taurus.oss.entity.OssRespository;

/**
 * oss对象管理 封装了oss对象的多种方式的上传操作
 * 
 * <AUTHOR>
 *
 */
@Service
public class AliOssObjectService {

	@Autowired
	private OssFileService ossFileService;

	// 支持多个bucket连接的缓存
	private volatile static Map<String, OSS> ossClientMap = new HashMap<>();

	
	// 使用单例ossClient，防止OOM
	public static OSS getOssClient(OssRespository repository) {
		String bucket = repository.getBucketName();
		OSS ossClient = ossClientMap.get(bucket);
		if (ossClient == null) {
			synchronized (AliOssObjectService.class) {
				ossClient = new OSSClientBuilder().build(getEndpoint(repository), repository.getAccessKeyId(),
						repository.getAccessKeySecret());
				ossClientMap.put(bucket, ossClient);
			}
		}
		return ossClient;
	}

	/**
	 * 前端multipartFile方式上传文件
	 * 
	 * @param multipartFile
	 * @param repository
	 * @param objectName
	 * @return
	 */
	public boolean uploadFile(MultipartFile multipartFile, OssRespository respository, String objectName) {
		// 创建OSSClient实例
//		OSS ossClient = new OSSClientBuilder().build(getEndpoint(respository), respository.getAccessKeyId(),
//				respository.getAccessKeySecret());
		//获取一个client
		OSS ossClient =getOssClient(respository);
		try {
			InputStream multipartFileInputStream = multipartFile.getInputStream();
			BufferedInputStream bfis = new BufferedInputStream(multipartFileInputStream);
			PutObjectRequest putObjectRequest = new PutObjectRequest(respository.getBucketName(), objectName, bfis);

			// 如果需要上传时设置存储类型与访问权限，请参考以下示例代码。
			// ObjectMetadata metadata = new ObjectMetadata();
			// metadata.setHeader(OSSHeaders.OSS_STORAGE_CLASS,
			// StorageClass.Standard.toString());
			// metadata.setObjectAcl(CannedAccessControlList.Private);
			// putObjectRequest.setMetadata(metadata);
			ossClient.putObject(putObjectRequest);
			// 关闭OSSClient。
			//ossClient.shutdown();
			return true;

		} catch (IOException e) {
			e.printStackTrace();
			return false;
		}

	}

	/**
	 * 通过文件流的方式上传
	 * 
	 * @param filePath
	 * @param objectName
	 */
	public void uploadByFileStream(String filePath, OssRespository respository, String objectName) {
		// 创建OSSClient实例。
//		OSS ossClient = new OSSClientBuilder().build(getEndpoint(respository), respository.getAccessKeyId(),
//				respository.getAccessKeySecret());
		//获取一个client
		OSS ossClient =getOssClient(respository);

		// 上传文件流。
		InputStream inputStream;
		try {
			inputStream = new FileInputStream(filePath);
			ossClient.putObject(respository.getBucketName(), objectName, inputStream);
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		}
		// 关闭OSSClient。
		//ossClient.shutdown();
	}

	/**
	 * 通过文件流的方式上传
	 * 
	 * @param filePath
	 * @param objectName
	 * @throws FileNotFoundException
	 */
	public void uploadByFileStream(File file, OssRespository respository, String objectName)
			throws FileNotFoundException, OSSException, ClientException {
		// 创建OSSClient实例。
//		OSS ossClient = new OSSClientBuilder().build(getEndpoint(respository), respository.getAccessKeyId(),
//				respository.getAccessKeySecret());
		//获取一个client
		OSS ossClient =getOssClient(respository);

		// 上传文件流。
		InputStream inputStream = new FileInputStream(file);
		ossClient.putObject(respository.getBucketName(), objectName, inputStream);

		// 关闭OSSClient。
		//ossClient.shutdown();
	}

	/**
	 * 判断文件是否存在
	 * 
	 * @param respository
	 * @param objectName
	 * @return
	 */
	public boolean doesObjectExist(OssRespository respository, String objectName) {
		// 创建OSSClient实例。
//		OSS ossClient = new OSSClientBuilder().build(getEndpoint(respository), respository.getAccessKeyId(),
//				respository.getAccessKeySecret());
		//获取一个client
		OSS ossClient =getOssClient(respository);

		boolean found = ossClient.doesObjectExist(respository.getBucketName(), objectName);
		// 关闭OSSClient。
		//ossClient.shutdown();
		return found;
	}

	/**
	 * 创建一个指定名称的空文件夹
	 * 
	 * @param repository
	 * @param keySuffixWithSlash 注意结尾一定是/
	 * @throws IOException
	 */
	public void createEmptyFolder(OssRespository respository, String keySuffixWithSlash) {
		/*
		 * Constructs a client instance with your account for accessing OSS
		 */
//		OSS client = new OSSClientBuilder().build(getEndpoint(respository), respository.getAccessKeyId(),
//				respository.getAccessKeySecret());
		//获取一个client
		OSS client =getOssClient(respository);

		try {
			/*
			 * Create an empty folder without request body, note that the key must be
			 * suffixed with a slash
			 */

			client.putObject(respository.getBucketName(), keySuffixWithSlash, new ByteArrayInputStream(new byte[0]));
			System.out.println("Creating an empty folder " + keySuffixWithSlash + "\n");

			/*
			 * Verify whether the size of the empty folder is zero
			 */
//            OSSObject object = client.getObject(respository.getBucketName(), keySuffixWithSlash);
//            System.out.println("Size of the empty folder '" + object.getKey() + "' is " + 
//                    object.getObjectMetadata().getContentLength());
//            object.getObjectContent().close();

		} catch (OSSException oe) {
			System.out.println("Caught an OSSException, which means your request made it to OSS, "
					+ "but was rejected with an error response for some reason.");
			System.out.println("Error Message: " + oe.getErrorMessage());
			System.out.println("Error Code:       " + oe.getErrorCode());
			System.out.println("Request ID:      " + oe.getRequestId());
			System.out.println("Host ID:           " + oe.getHostId());
		} catch (ClientException ce) {
			System.out.println("Caught an ClientException, which means the client encountered "
					+ "a serious internal problem while trying to communicate with OSS, "
					+ "such as not being able to access the network.");
			System.out.println("Error Message: " + ce.getMessage());
//        }catch(IOException ioe) {
//        	 System.out.println("Error Message: " + ioe.getMessage());
		} finally {
			/*
			 * Do not forget to shut down the client finally to release all allocated
			 * resources.
			 */
			//client.shutdown();
		}
	}

	/**
	 * 根据阿里云oss对象名获取该对象的防盗链形式的url
	 * 
	 * @param repository
	 * @param objectName
	 * @return
	 */
	public String generateAccessUrl(OssRespository respository, String objectName) {
		String cdnUrl = respository.getCdnUrl();
		String bucketName = respository.getBucketName();
		// 开启了CDN的仓库的处理流程
		if (!StringUtils.isEmpty(cdnUrl)) {
			StringBuffer cdnUrlBuffer = new StringBuffer(cdnUrl).append(objectName);
			String type = ossFileService.getFileType(objectName);
			if (type.equals("P")) {
				cdnUrlBuffer.append("?x-oss-process=").append("image%2Fresize%2Cm_fixed%2Cw_750");
			}
			return cdnUrlBuffer.toString();
		} else {
			// 创建OSSClient实例。
//			OSS ossClient = new OSSClientBuilder().build(getEndpoint(respository), respository.getAccessKeyId(),
//					respository.getAccessKeySecret());
			//获取一个client
			OSS ossClient =getOssClient(respository);

			// 设置URL过期时间为20天。
			Date expiration = new Date(new Date().getTime() + 20 * 24 * 60 * 60 * 1000);

			URL signedUrl = null;

			String type = ossFileService.getFileType(objectName);
			if (type.equals("P")) {
				GeneratePresignedUrlRequest req = new GeneratePresignedUrlRequest(bucketName, objectName,
						HttpMethod.GET);
				req.setExpiration(expiration);
				req.setProcess("image/resize,m_fixed,w_750");
				signedUrl = ossClient.generatePresignedUrl(req);
			} else {
				signedUrl = ossClient.generatePresignedUrl(bucketName, objectName, expiration);
			}

			//ossClient.shutdown();
			return signedUrl.toString();
		}
	}

	/**
	 *oss对外服务的接入点地址
	 * 
	 * @param respository
	 * @return
	 */
	private static String getEndpoint(OssRespository respository) {
		return respository.getInternetEndpoint();
	}

	/**
	 * 删除仓库中指定的的文件对象
	 * 
	 * @param respository
	 * @param objectName
	 */
	public void delete(OssRespository respository, String objectName) {
		// 创建OSSClient实例。
//		OSS ossClient = new OSSClientBuilder().build(getEndpoint(respository), respository.getAccessKeyId(),
//				respository.getAccessKeySecret());
		
		//获取一个client
		OSS ossClient =getOssClient(respository);
		try {
			// 删除文件或目录。如果要删除目录，目录必须为空。
			ossClient.deleteObject(respository.getBucketName(), objectName);
		} catch (OSSException oe) {
			System.out.println("Caught an OSSException, which means your request made it to OSS, "
					+ "but was rejected with an error response for some reason.");
			System.out.println("Error Message:" + oe.getErrorMessage());
			System.out.println("Error Code:" + oe.getErrorCode());
			System.out.println("Request ID:" + oe.getRequestId());
			System.out.println("Host ID:" + oe.getHostId());
		} catch (ClientException ce) {
			System.out.println("Caught an ClientException, which means the client encountered "
					+ "a serious internal problem while trying to communicate with OSS, "
					+ "such as not being able to access the network.");
			System.out.println("Error Message:" + ce.getMessage());
		} finally {
//			if (ossClient != null) {
//				ossClient.shutdown();
//			}
		}
	}

	/**
	 * 重新裁剪图片的尺寸
	 * 
	 * @param respository
	 * @param objectName
	 */
	public void resizeOssPicFile(OssRespository respository, String objectName, String styleType) {
		// 创建OSSClient实例。
//		OSS ossClient = new OSSClientBuilder().build(getEndpoint(respository), respository.getAccessKeyId(),
//				respository.getAccessKeySecret());
		//获取一个client
		OSS ossClient =getOssClient(respository);
		try {

			StringBuilder sbStyle = new StringBuilder();
			try (Formatter styleFormatter = new Formatter(sbStyle)) {
				styleFormatter.format("%s|sys/saveas,o_%s,b_%s", styleType,
						BinaryUtil.toBase64String(objectName.getBytes()),
						BinaryUtil.toBase64String(respository.getBucketName().getBytes()));
			}

			System.out.println(sbStyle.toString());
			ProcessObjectRequest request = new ProcessObjectRequest(respository.getBucketName(), objectName,
					sbStyle.toString());
			GenericResult processResult = ossClient.processObject(request);
			String json = IOUtils.readStreamAsString(processResult.getResponse().getContent(), "UTF-8");
			processResult.getResponse().getContent().close();
			System.out.println(json);
		} catch (OSSException oe) {
			System.out.println("Caught an OSSException, which means your request made it to OSS, "
					+ "but was rejected with an error response for some reason.");
			System.out.println("Error Message:" + oe.getErrorMessage());
			System.out.println("Error Code:" + oe.getErrorCode());
			System.out.println("Request ID:" + oe.getRequestId());
			System.out.println("Host ID:" + oe.getHostId());
		} catch (ClientException ce) {
			System.out.println("Caught an ClientException, which means the client encountered "
					+ "a serious internal problem while trying to communicate with OSS, "
					+ "such as not being able to access the network.");
			System.out.println("Error Message:" + ce.getMessage());
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
//			if (ossClient != null) {
//				ossClient.shutdown();
//			}
		}
	}

}
