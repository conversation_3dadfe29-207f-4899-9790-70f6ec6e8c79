package com.taurus.oss.service;

import java.io.File;
import java.io.IOException;

import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.taurus.oss.entity.OssRespository;

@Service
public class NativeOssObjectService {

	private static final org.slf4j.Logger logger = LoggerFactory.getLogger(NativeOssObjectService.class);

	/**
	 * 保存文件到本地oss服务器
	 * @param file
	 * @param objectName
	 * @return
	 */
	public Boolean uploadFile(MultipartFile file,OssRespository respository,String objectName) {	
		String respositoryDiskPath = respository.getInternalEndpoint();// 仓库本地磁盘路径
		String filePath = respositoryDiskPath +  objectName;
		try {
			File newFile = new File(filePath);
			file.transferTo(newFile);
			return true;
		} catch (IllegalStateException | IOException e) {
			logger.debug("disk io error");
			return false;
		} catch (NullPointerException e) {
			logger.debug("filePath is null");
			return false;
		}
	}

	/**
	 * 获取可以访问的url
	 * @param respository
	 * @param objectName
	 * @return
	 */
	public String generateAccessUrl(OssRespository respository, String objectName) {
		StringBuffer urlBuffer = new StringBuffer(respository.getBindedInternetUrl()).append("/").append(objectName);
		return urlBuffer.toString();
	}

	public void delete(OssRespository respository, String objectName) {
		String respositoryDiskPath = respository.getInternalEndpoint();// 仓库本地磁盘路径
		String filePath = respositoryDiskPath +  objectName;
		try {
			File newFile = new File(filePath);
			newFile.delete();
		} catch (SecurityException e) {
			logger.debug(e.getMessage());
		} 
		
	}

}
