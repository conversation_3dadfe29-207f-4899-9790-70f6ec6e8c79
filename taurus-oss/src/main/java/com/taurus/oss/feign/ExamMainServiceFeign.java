package com.taurus.oss.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSONObject;
import com.taurus.entity.ResponseObject;

@FeignClient(value = "exam-main-service") // 这里的name对应调用服务的spring.application.name
@Component
public interface ExamMainServiceFeign {
	/**
	 * 检查文字信息是否违规
	 * 
	 * @param body
	 * @return
	 */
	@PostMapping("/weixinAccount/msgSecCheck")
	public ResponseObject msgSecCheck(@RequestBody JSONObject body);

	/**
	 * 检查图片是否违规
	 * 
	 * @param body
	 * @return
	 */
	@PostMapping(value = "/weixinAccount/imgSecCheckByMultipart", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	public ResponseObject imgSecCheck(@RequestPart(value = "file") MultipartFile file,
			@RequestParam("account") String account);

}
