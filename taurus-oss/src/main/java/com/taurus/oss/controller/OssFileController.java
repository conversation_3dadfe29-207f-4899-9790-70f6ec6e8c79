package com.taurus.oss.controller;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URL;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.gitee.fastmybatis.core.util.MyBeanUtil;
import com.taurus.entity.ResponseObject;
import com.taurus.oss.entity.OssFile;
import com.taurus.oss.entity.OssRespository;
import com.taurus.oss.entity.ProductOssInfo;
import com.taurus.oss.entity.bussiness.OssFileWrapper;
import com.taurus.oss.feign.ExamMainServiceFeign;
import com.taurus.oss.service.AliOssObjectService;
import com.taurus.oss.service.NativeOssObjectService;
import com.taurus.oss.service.OssFileService;
import com.taurus.oss.service.OssRespositoryService;
import com.taurus.oss.service.ProductOssInfoService;
import com.taurus.service.RedisService;
import com.taurus.utils.MultipartFileUtil;

@RestController
@RequestMapping("/ossFile")
public class OssFileController {

	@Autowired
	private OssFileService ossFileService;

	@Autowired
	private ExamMainServiceFeign examMainServiceFeign;

	@Autowired
	private ProductOssInfoService productOssInfoService;

	@Autowired
	private AliOssObjectService aliOssObjectService;

	@Autowired
	private NativeOssObjectService nativeOssObjectService;

	@Autowired
	private OssRespositoryService ossRespositoryService;

	@Autowired
	private RedisService redisService;

	private static final Logger log = LoggerFactory.getLogger(OssFileController.class);

	/**
	 * 上传文件到ali云oss
	 * 
	 * @param request
	 * @param response
	 * @return
	 */
	@PostMapping("/uploadOssFile")
	public ResponseObject uploadOssFile(HttpServletRequest request, HttpServletResponse response) {
		MultipartHttpServletRequest params = ((MultipartHttpServletRequest) request);
		List<MultipartFile> files = params.getFiles("file");
		String productName = request.getParameter("productName");// 产品名称:决定选择oss上的哪个bucket
		String moduleName = request.getParameter("moduleName");// 模块名称:决定了选择bucket上的哪个存储位置
		String dynamicPath = request.getParameter("dynamicPath");// 动态路径
		String userId = request.getParameter("userId");// 上传人id
		String description = request.getParameter("description");//
		String ifCheck = params.getParameter("ifCheck");// 是否内容检查
		String using = params.getParameter("using");// 是否被使用，不使用为临时文件，1月后做删除处理
		String width = params.getParameter("width");

		if (files == null || StringUtils.isEmpty(productName) || StringUtils.isEmpty(moduleName)
				|| StringUtils.isEmpty(userId) || StringUtils.isEmpty(ifCheck) || StringUtils.isEmpty(using)
				|| files.size() == 0) {
			return ResponseObject.failure("PARAMETERS_ERROR");
		}

		Map<String, Object> ossInfoAndRespository = productOssInfoService
				.getProductOssInfoAndOssRespository(productName, moduleName);

		if (ossInfoAndRespository == null) {
			return ResponseObject.failure("NOT_EXISTED_PRODUCT_OSS_INFO");
		}

		ProductOssInfo ossInfo = (ProductOssInfo) ossInfoAndRespository.get("productOssInfo");
		if (ossInfo == null) {
			return ResponseObject.failure("NOT_EXISTED_PRODUCT_OSS_INFO");// 不存在的产品oss配置信息
		}

		OssRespository respository = (OssRespository) ossInfoAndRespository.get("ossRespository");
		if (respository == null) {
			return ResponseObject.failure("NOT_EXISTED_RESPOSITORY");// 未找到匹配的仓库
		}

		for (MultipartFile multipartFile : files) {

			// 文件不得超过15M限制
			long size = multipartFile.getSize();
			if (size > 15 * 1024 * 1024) {
				return ResponseObject.failure("EXCEED_MAX_FILE_SIZE");
			}
			String originalFileName = multipartFile.getOriginalFilename();
			String format = originalFileName.substring(originalFileName.lastIndexOf(".") + 1);
			String fileType = ossFileService.getFileType(originalFileName);
			// 当文件是图片时
			if (fileType.equals("P")) {
				if (StringUtils.isEmpty(width) && moduleName.contains("avatar")) {
					width = "120";
				} else if (StringUtils.isEmpty(width)) {
					width = "750";
				}
				multipartFile = MultipartFileUtil.compress(multipartFile, Integer.parseInt(width), null);
				if (!StringUtils.isEmpty(ifCheck) && ifCheck.equals("yes")) {
					// 图片时验证是否违规
					request.setAttribute("account", productName);
					try {

						ResponseObject responseObj = examMainServiceFeign.imgSecCheck(multipartFile, productName);
						if (responseObj.getCode().equals("reject")) {
							return responseObj;
						}
					} catch (Exception ex) {
						ex.printStackTrace();
					}
				}
			}

			OssFile ossFile = new OssFile();
			ossFile.setUserId(Integer.parseInt(userId));
			ossFile.setDescription(description);
			ossFile.setUsing(using.equals("yes") ? true : false);
			ossFile.setInsertTime(new Date());
			ossFile.setProductOssInfoId(ossInfo.getId());
			ossFile.setOriginalFileName(originalFileName);
			ossFile.setFileFormat(format);
			ossFile.setFileSize(new BigDecimal(size / 1024));
			ossFile.setFileType(fileType);
			ossFile.setRespositoryName(respository.getBucketName());

			String objectName = getObjectName(ossInfo, true, false, originalFileName, format, dynamicPath, respository);// 根据存储规则得到objectName
			if (objectName == null) {
				return ResponseObject.failure("OBJECT_NAME_IS_NULL");
			}
			ossFile.setObjectName(objectName);

			return ossFileService.upload(request, ossFile, multipartFile, respository);
		}
		return ResponseObject.failure("NO_FILE_UPLOADED");

	}

	/**
	 * 将一个URL对应的文件上传到ali云oss
	 * 
	 * @param body
	 * @return
	 */
	@PostMapping("/uploadUrlFile")
	public ResponseObject uploadUrlFile(@RequestBody JSONObject body, HttpServletRequest request) {
		String url = body.getString("url");
		String productName = body.getString("productName");
		String moduleName = body.getString("moduleName");
		String dynamicPath = body.getString("dynamicPath");
		String userId = body.getString("userId");
		String description = body.getString("description");
		String ifCheck = body.getString("ifCheck");
		String using = body.getString("using");
		String width = body.getString("width");
		// 允许调用者指定文件名和后缀
		String customFileName = body.getString("fileName");
		String customFileExt = body.getString("fileExt");

		// 验证url格式
		if (StringUtils.isEmpty(url) || StringUtils.isEmpty(productName) || StringUtils.isEmpty(moduleName)
				|| StringUtils.isEmpty(userId) || StringUtils.isEmpty(ifCheck) || StringUtils.isEmpty(using)) {
			return ResponseObject.failure("PARAMETERS_ERROR");
		}

		// 验证url是否有效
		URL urlObj = null;
		try {
			urlObj = new URL(url);
		} catch (MalformedURLException e) {
			return ResponseObject.failure("INVALID_URL");
		}

		// 获取OSS配置信息
		Map<String, Object> ossInfoAndRespository = productOssInfoService
				.getProductOssInfoAndOssRespository(productName, moduleName);

		if (ossInfoAndRespository == null) {
			return ResponseObject.failure("NOT_EXISTED_PRODUCT_OSS_INFO");
		}

		ProductOssInfo ossInfo = (ProductOssInfo) ossInfoAndRespository.get("productOssInfo");
		if (ossInfo == null) {
			return ResponseObject.failure("NOT_EXISTED_PRODUCT_OSS_INFO");
		}

		OssRespository respository = (OssRespository) ossInfoAndRespository.get("ossRespository");
		if (respository == null) {
			return ResponseObject.failure("NOT_EXISTED_RESPOSITORY");
		}

		try {
			// 从URL下载文件
			InputStream inputStream = urlObj.openStream();
			byte[] fileBytes = MultipartFileUtil.getByteArray(inputStream);

			// 获取文件名
			String fileName;
			String fileExt;

			// 如果提供了自定义文件名和后缀，优先使用
			if (!StringUtils.isEmpty(customFileName) && !StringUtils.isEmpty(customFileExt)) {
				fileName = customFileName;
				fileExt = customFileExt;
			} else {
				// 从URL路径中获取文件名
				fileName = urlObj.getPath();
				if (fileName.contains("/")) {
					fileName = fileName.substring(fileName.lastIndexOf("/") + 1);
				}

				// 如果URL中没有文件名，生成默认文件名
				if (StringUtils.isEmpty(fileName)) {
					fileName = "file_" + System.currentTimeMillis();
					fileExt = "bin"; // 默认二进制文件扩展名
				} else {
					// 从文件名中提取后缀
					if (fileName.contains(".")) {
						fileExt = fileName.substring(fileName.lastIndexOf(".") + 1);
					} else {
						// 如果没有后缀，尝试根据内容类型猜测
						fileExt = "bin";
					}
				}

				// 如果提供了自定义后缀，覆盖提取的后缀
				if (!StringUtils.isEmpty(customFileExt)) {
					fileExt = customFileExt;
				}
			}

			// 确保文件名包含后缀
			if (!fileName.endsWith("." + fileExt)) {
				fileName = fileName + "." + fileExt;
			}

			// 根据文件类型设置contentType
			String contentType;
			if (fileExt.equalsIgnoreCase("jpg") || fileExt.equalsIgnoreCase("jpeg")) {
				contentType = "image/jpeg";
			} else if (fileExt.equalsIgnoreCase("png")) {
				contentType = "image/png";
			} else if (fileExt.equalsIgnoreCase("gif")) {
				contentType = "image/gif";
			} else if (fileExt.equalsIgnoreCase("pdf")) {
				contentType = "application/pdf";
			} else if (fileExt.equalsIgnoreCase("doc") || fileExt.equalsIgnoreCase("docx")) {
				contentType = "application/msword";
			} else if (fileExt.equalsIgnoreCase("xls") || fileExt.equalsIgnoreCase("xlsx")) {
				contentType = "application/vnd.ms-excel";
			} else if (fileExt.equalsIgnoreCase("mp3")) {
				contentType = "audio/mpeg";
			} else if (fileExt.equalsIgnoreCase("mp4")) {
				contentType = "video/mp4";
			} else if (fileExt.equalsIgnoreCase("txt")) {
				contentType = "text/plain";
			} else if (fileExt.equalsIgnoreCase("pdf")) {
				contentType = "application/pdf";
			} else if (fileExt.equalsIgnoreCase("ppt") || fileExt.equalsIgnoreCase("pptx")) {
				contentType = "application/vnd.ms-powerpoint";
			} else {
				contentType = "application/octet-stream";
			}

			// 创建MultipartFile
			String base64Str = Base64.getEncoder().encodeToString(fileBytes);
			String dataStr = "data:" + contentType + ";base64," + base64Str;
			MultipartFile multipartFile = MultipartFileUtil.base64ToMultipart(dataStr);

			// 文件不得超过15M限制
			long size = multipartFile.getSize();
			if (size > 200 * 1024 * 1024) {
				log.error("上传文件大小超过200M限制，文件名: {}", fileName);
				return ResponseObject.failure("EXCEED_MAX_FILE_SIZE");
			}

			// 当文件是图片时
			String fileType = ossFileService.getFileType(fileName);
			if (fileType.equals("P")) {
				if (StringUtils.isEmpty(width) && moduleName.contains("avatar")) {
					width = "120";
				} else if (StringUtils.isEmpty(width)) {
					width = "750";
				}
				multipartFile = MultipartFileUtil.compress(multipartFile, Integer.parseInt(width), null);
				if (ifCheck.equals("yes")) {
					// 图片时验证是否违规
					ResponseObject responseObj = examMainServiceFeign.imgSecCheck(multipartFile, productName);
					if (responseObj.getCode().equals("reject")) {
						return responseObj;
					}
				}
			}

			// 创建OssFile对象
			OssFile ossFile = new OssFile();
			ossFile.setUserId(Integer.parseInt(userId));
			ossFile.setDescription(description);
			ossFile.setUsing(using.equals("yes") ? true : false);
			ossFile.setInsertTime(new Date());
			ossFile.setProductOssInfoId(ossInfo.getId());
			ossFile.setOriginalFileName(fileName);
			ossFile.setFileFormat(fileExt);
			ossFile.setFileSize(new BigDecimal(size / 1024));
			ossFile.setFileType(fileType);
			ossFile.setRespositoryName(respository.getBucketName());

			// 生成objectName
			String objectName = getObjectName(ossInfo, true, false, fileName, fileExt, dynamicPath, respository);
			if (objectName == null) {
				return ResponseObject.failure("OBJECT_NAME_IS_NULL");
			}
			ossFile.setObjectName(objectName);
			log.debug("上传文件到OSS，objectName: {}", objectName);

			// 上传文件到OSS
			ResponseObject result = ossFileService.upload(request, ossFile, multipartFile, respository);
			log.debug("上传文件到OSS，result: {}", result);
			return result;

		} catch (IOException e) {
			e.printStackTrace();
			return ResponseObject.failure("DOWNLOAD_FILE_ERROR");
		}
	}

	@PostMapping("/update")
	public ResponseObject update(@RequestBody OssFile entity) {
		ossFileService.update(entity);
		return ResponseObject.success(true);
	}

	/**
	 * 获取某个ossFile的详情
	 * 
	 * @param id
	 * @return
	 */
	@GetMapping("/getOssFileById")
	public ResponseObject getOssFileById(Integer id) {
		OssFile ossFile = ossFileService.getOssFileById(id);
		return ResponseObject.success(ossFile);
	}

	/**
	 * 根据查询条件获取文件
	 * 
	 * @param body
	 * @return
	 */
	@PostMapping("/getOssFileList")
	public ResponseObject getOssFileList(@RequestBody JSONObject body) {
		String productName = body.getString("productName");
		String moduleName = body.getString("moduleName");
		Integer userId = body.getInteger("userId");
		String fileType = body.getString("fileType");
		String fileFormat = body.getString("fileFormat");
		Boolean using = body.getBoolean("using");
		Integer pageIndex = body.getInteger("pageIndex");
		Integer pageSize = body.getInteger("pageSize");

		if (productName == null || moduleName == null || pageIndex == null || pageSize == null) {
			return ResponseObject.failure("PARAMETERS_ERROR");
		}

		List<OssFile> ossFileList = ossFileService.getOssFileList(productName, moduleName, userId, fileType, fileFormat,
				using, pageIndex, pageSize);
		List<OssFileWrapper> ossFileWrapperList = ossFileList.stream().map(n -> {
			return generateOssUrl(n);
		}).collect(Collectors.toList());
		return ResponseObject.success(ossFileWrapperList);
	}

	private OssFileWrapper generateOssUrl(OssFile n) {
		OssFileWrapper wrapper = new OssFileWrapper();
		MyBeanUtil.copyProperties(n, wrapper);
		String objectName = n.getObjectName();
		OssRespository respository = ossRespositoryService.getEntity(n.getRespositoryName());
		if (respository.getType().equals("ali")) {
			String url = aliOssObjectService.generateAccessUrl(respository, objectName);
			wrapper.setUrl(url.toString());
		} else if (respository.getType().equals("native")) {
			String url = nativeOssObjectService.generateAccessUrl(respository, objectName);
			wrapper.setUrl(url.toString());
		}
		return wrapper;
	}

	/**
	 * 根据规则生成ObjectName
	 * 
	 * @param ossInfo
	 * @param keepOriginalFileName
	 * @param originFileName
	 * @param fileFormat
	 * @param dynamicPath
	 * @param respository
	 * @return
	 */
	private String getObjectName(ProductOssInfo ossInfo, Boolean enableDateFolder, Boolean keepOriginalFileName,
			String originFileName, String fileFormat, String dynamicPath, OssRespository respository) {

		String productName = ossInfo.getProductName();
		String moduleName = ossInfo.getModuleName();
		String storagebasePath = ossInfo.getStorageBasePath();
		if (StringUtils.isEmpty(storagebasePath)) {
			storagebasePath = moduleName;
		}

		StringBuffer folderBuffer = new StringBuffer(storagebasePath);
		// 如果存在动态目录，则加上
		if (!StringUtils.isEmpty(dynamicPath)) {
			folderBuffer.append(dynamicPath).append("/");
		}

		StringBuffer keyBuff = new StringBuffer().append(productName).append(moduleName);
		if (enableDateFolder) {
			Date date = new Date();
			SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
			String dateStr = formatter.format(date);
			keyBuff.append(dateStr);
			folderBuffer.append(dateStr).append("/");
		}

		String value = redisService.get(keyBuff.toString());
		// 查看键值为当天日期的路径是否存在，不存在说明没创建过
		if (StringUtils.isEmpty(value)) {
			if (respository.getType().equals("ali")) {
				boolean flag = aliOssObjectService.doesObjectExist(respository, folderBuffer.toString());
				if (!flag) {
					aliOssObjectService.createEmptyFolder(respository, folderBuffer.toString());
				}
			} else if (respository.getType().equals("native")) {
				// nothing to do
			}
			redisService.put(keyBuff.toString(), keyBuff.toString(), new Long(1000 * 60 * 60 * 24));
		}
		// 是否保留原文件名
		if (keepOriginalFileName) {
			folderBuffer.append(originFileName);
		} else {
			// 生成文件名:日期时间14位+uuid后4位+文件后缀
			DateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
			Calendar calendar = Calendar.getInstance();
			StringBuffer fileNameBuff = new StringBuffer().append(df.format(calendar.getTime()))
					.append(UUID.randomUUID().toString().substring(0, 4)).append(".").append(fileFormat);
			folderBuffer.append(fileNameBuff.toString());
		}
		return folderBuffer.toString();
	}
}
