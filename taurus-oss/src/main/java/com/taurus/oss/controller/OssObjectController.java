package com.taurus.oss.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.druid.util.StringUtils;
import com.taurus.entity.ResponseObject;
import com.taurus.oss.entity.OssFile;
import com.taurus.oss.entity.OssRespository;
import com.taurus.oss.service.AliOssObjectService;
import com.taurus.oss.service.NativeOssObjectService;
import com.taurus.oss.service.OssFileService;
import com.taurus.oss.service.OssRespositoryService;

/**
 * 获取ossObject的可访问的Url
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/ossObject")
public class OssObjectController {

	@Autowired
	private OssRespositoryService ossRespositoryService;
	
	@Autowired
	private AliOssObjectService aliOssObjectService;
	
	@Autowired
	private NativeOssObjectService nativeOssObjectService;
	
	@Autowired
	private OssFileService ossFileService;

	/**
	 * 获取某产品objectName对应的访问url
	 * 
	 * @param productName
	 * @param objectName
	 * @return
	 */
	@GetMapping("/getAccessUrl")
	public ResponseObject getAccessUrl(String respositoryName, String objectName) {
		if (StringUtils.isEmpty(respositoryName)|| StringUtils.isEmpty(objectName)) {
			return ResponseObject.failure("PARAMETERS_ERROR");
		}
		OssRespository respository = ossRespositoryService.getEntity(respositoryName);
		if (respository != null) {
			if (respository.getType().equals("ali")) {
				String url = aliOssObjectService.generateAccessUrl(respository, objectName);
				return ResponseObject.success(url);
			} else if (respository.getType().equals("native")) {
				String url = nativeOssObjectService.generateAccessUrl(respository, objectName);
				return ResponseObject.success(url);
			}else {
				return ResponseObject.failure("NOT_EXISTED_TYPE");
			}
		} else {
			return ResponseObject.failure("NOT_EXISTED_RESPOSITORY");
		}
	}
	
	@GetMapping("/delete")
	public ResponseObject delete(String respositoryName,String objectName) {
		if (StringUtils.isEmpty(respositoryName)|| StringUtils.isEmpty(objectName)) {
			return ResponseObject.failure("PARAMETERS_ERROR");
		}
		OssRespository respository = ossRespositoryService.getEntity(respositoryName);
		if (respository != null) {
			if (respository.getType().equals("ali")) {
				aliOssObjectService.delete(respository, objectName);
				return ResponseObject.success(true);
			} else if (respository.getType().equals("native")) {
				nativeOssObjectService.delete(respository, objectName);
				return ResponseObject.success(true);
			}else {
				return ResponseObject.failure("NOT_EXISTED_TYPE");
			}
		} else {
			return ResponseObject.failure("NOT_EXISTED_RESPOSITORY");
		}
	}
	
	/**
	 * examination-etea oss中的头像图片等比例裁剪
	 */
	@GetMapping("/resizeOssPicFile")
	public ResponseObject resizeOssPicFile(String type) {
		if(StringUtils.isEmpty(type)) {
			return ResponseObject.failure("PARAMETERS_ERROR");
		}
		List<OssFile> list = null;
		String styleType = null;
		if(type.equals("avatar")) {
			list=ossFileService.getList("avatar","P");
			styleType="image/resize,m_lfit,w_120";//将图片缩放为固定宽120 px。等比缩放，固定宽度";
		}else if(type.equals("examination")) {
			list=ossFileService.getList("eteaExam","P");
			styleType="image/resize,m_lfit,w_1024";
		}
		
		for(int i=0;list!=null&&i<list.size();i++) {
			OssFile file = list.get(i);
			String objectName = file.getObjectName();
			OssRespository respository = ossRespositoryService.getEntity("examination-etea");
			aliOssObjectService.resizeOssPicFile(respository, objectName,styleType);
		}
		return ResponseObject.success(true);
	}

}
