package com.taurus.oss.controller;


import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.auth.sts.AssumeRoleRequest;
import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.taurus.entity.ResponseObject;
import com.taurus.oss.entity.OssRespository;
import com.taurus.oss.entity.ProductOssInfo;
import com.taurus.oss.service.OssRespositoryService;
import com.taurus.oss.service.ProductOssInfoService;

/**
 * oss仓库
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/ossRespository")
public class OssRespositoryController {

	@Autowired
	private OssRespositoryService ossRespositoryService;
	
	@Autowired
	private ProductOssInfoService productOssInfoService;

	/**
	 * 获取客户端直传需要的policy
	 * 
	 * @param productName
	 * @param moduleName
	 * @param dynamicPath
	 * @return
	 * @throws UnsupportedEncodingException
	 * @throws JSONException
	 */
	@GetMapping("/getAliUploadPolicy")
    public ResponseObject getAliUploadPolicy(String productName,String moduleName,String dynamicPath) throws UnsupportedEncodingException, JSONException {
		if(StringUtils.isEmpty(productName)||StringUtils.isEmpty(moduleName)) {
			return ResponseObject.failure("PARAMETERS_ERROR");
		}
		
		Map<String, Object> ossInfoAndRespository = productOssInfoService
				.getProductOssInfoAndOssRespository(productName, moduleName);

		if (ossInfoAndRespository == null) {
			return ResponseObject.failure("NOT_EXISTED_PRODUCT_OSS_INFO");
		}

		ProductOssInfo ossInfo = (ProductOssInfo) ossInfoAndRespository.get("productOssInfo");
		if (ossInfo == null) {
			return ResponseObject.failure("NOT_EXISTED_PRODUCT_OSS_INFO");// 不存在的产品oss配置信息
		}

		OssRespository respository = (OssRespository) ossInfoAndRespository.get("ossRespository");
		if (respository == null) {
			return ResponseObject.failure("NOT_EXISTED_RESPOSITORY");// 未找到匹配的仓库
		}
		
		StringBuffer dirBuffer = new StringBuffer(ossInfo.getStorageBasePath()).append(dynamicPath).append("/");// 用户上传文件时指定的前缀。
		JSONObject policy =  ossRespositoryService.getAliUploadPolicy(respository,dirBuffer.toString());
		if(policy!=null) {
			return ResponseObject.success(policy);
		}else {
			return ResponseObject.failure("ERROR");
		}
    }
	
	/**
	 * 获取客户端直传阿里oss的临时访问凭证
	 * @param productName
	 * @param moduleName
	 * @return
	 */
	@GetMapping("/getAliSts")
	public ResponseObject getAliSts(String productName, String moduleName) {
		
		if(StringUtils.isEmpty(productName)||StringUtils.isEmpty(moduleName)) {
			return ResponseObject.failure("PARAMETERS_ERROR");
		}
		
		Map<String, Object> ossInfoAndRespository = productOssInfoService
				.getProductOssInfoAndOssRespository(productName, moduleName);

		if (ossInfoAndRespository == null) {
			return ResponseObject.failure("NOT_EXISTED_PRODUCT_OSS_INFO");
		}

		ProductOssInfo ossInfo = (ProductOssInfo) ossInfoAndRespository.get("productOssInfo");
		if (ossInfo == null) {
			return ResponseObject.failure("NOT_EXISTED_PRODUCT_OSS_INFO");// 不存在的产品oss配置信息
		}

		OssRespository respository = (OssRespository) ossInfoAndRespository.get("ossRespository");
		if (respository == null) {
			return ResponseObject.failure("NOT_EXISTED_RESPOSITORY");// 未找到匹配的仓库
		}
		
		// STS接入地址，例如sts.cn-hangzhou.aliyuncs.com。       
        String endpoint = "sts.cn-hangzhou.aliyuncs.com";
        // 填写步骤1生成的RAM用户访问密钥AccessKey ID和AccessKey Secret。
        String accessKeyId = "LTAI4G6geztw5fN5jH8pnW4j";
        String accessKeySecret = "******************************";
        // 填写步骤3获取的角色ARN。
        String roleArn = "acs:ram::28480311:role/ramosstest";
        // 自定义角色会话名称，用来区分不同的令牌，例如可填写为SessionTest。        
        String roleSessionName = "SessionTest";
        // 以下Policy用于限制仅允许使用临时访问凭证向目标存储空间examplebucket上传文件。
        // 临时访问凭证最后获得的权限是步骤4设置的角色权限和该Policy设置权限的交集，即仅允许将文件上传至目标存储空间examplebucket下的exampledir目录。
        // 如果policy为空，则用户将获得该角色下所有权限。
        //String policy = "";
        // 设置临时访问凭证的有效时间为3600秒。
        Long durationSeconds = 3600L;
        try {
            // regionId表示RAM的地域ID。以华东1（杭州）地域为例，regionID填写为cn-hangzhou。也可以保留默认值，默认值为空字符串（""）。
            String regionId = "cn-hangzhou";
            // 添加endpoint。适用于Java SDK 3.12.0及以上版本。
            //DefaultProfile.addEndpoint(regionId, "Sts", endpoint);
            // 添加endpoint。适用于Java SDK 3.12.0以下版本。
            DefaultProfile.addEndpoint("",regionId, "Sts", endpoint);
            // 构造default profile。
            IClientProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
            // 构造client。
            DefaultAcsClient client = new DefaultAcsClient(profile);
            final AssumeRoleRequest request = new AssumeRoleRequest();
            // 适用于Java SDK 3.12.0及以上版本。
            //request.setSysMethod(MethodType.POST);
            // 适用于Java SDK 3.12.0以下版本。
            request.setMethod(MethodType.POST);
            request.setRoleArn(roleArn);
            request.setRoleSessionName(roleSessionName);
            //request.setPolicy(policy); 
            request.setDurationSeconds(durationSeconds); 
            final AssumeRoleResponse response = client.getAcsResponse(request);
            Map<String,Object> map = new HashMap<>();
            map.put("expiration", response.getCredentials().getExpiration());
            map.put("accessKeyId", response.getCredentials().getAccessKeyId());
            map.put("accessKeySecret", response.getCredentials().getAccessKeySecret());
            map.put("securityToken", response.getCredentials().getSecurityToken());
            map.put("bucket", respository.getBucketName());
            map.put("storageBasePath", ossInfo.getStorageBasePath());
           
            return ResponseObject.success(map);
        } catch (ClientException e) {
            ResponseObject response = new ResponseObject();
            response.setCode(e.getErrCode());
            response.setMessage(e.getErrMsg());
            return response;
        }
	}
}
