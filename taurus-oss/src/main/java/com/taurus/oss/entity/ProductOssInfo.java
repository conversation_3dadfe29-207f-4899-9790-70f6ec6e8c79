package com.taurus.oss.entity;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;


/**
 * 表名：product_oss_info
 * 备注：某产品某个模块的文件，存储在仓库中的storage_base_path位置
 *
 * <AUTHOR>
 */
@Table(name = "product_oss_info")
public class ProductOssInfo {
    /**  数据库字段：id */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /** 产品名字, 数据库字段：product_name */
    private String productName;

    /** 模块名字, 数据库字段：module_name */
    private String moduleName;

    /** Oss仓库名称, 数据库字段：respository_name */
    private String respositoryName;

    /** 存储根路径, 数据库字段：storage_base_path */
    private String storageBasePath;

    /**  数据库字段：product_oss_info.id */
    public void setId(Integer id) {
        this.id = id;
    }

    /**  数据库字段：product_oss_info.id */
    public Integer getId() {
        return this.id;
    }

    /** 设置产品名字, 数据库字段：product_oss_info.product_name */
    public void setProductName(String productName) {
        this.productName = productName;
    }

    /** 获取产品名字, 数据库字段：product_oss_info.product_name */
    public String getProductName() {
        return this.productName;
    }

    /** 设置模块名字, 数据库字段：product_oss_info.module_name */
    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    /** 获取模块名字, 数据库字段：product_oss_info.module_name */
    public String getModuleName() {
        return this.moduleName;
    }

    /** 设置Oss仓库名称, 数据库字段：product_oss_info.respository_name */
    public void setRespositoryName(String respositoryName) {
        this.respositoryName = respositoryName;
    }

    /** 获取Oss仓库名称, 数据库字段：product_oss_info.respository_name */
    public String getRespositoryName() {
        return this.respositoryName;
    }

    /** 设置存储根路径, 数据库字段：product_oss_info.storage_base_path */
    public void setStorageBasePath(String storageBasePath) {
        this.storageBasePath = storageBasePath;
    }

    /** 获取存储根路径, 数据库字段：product_oss_info.storage_base_path */
    public String getStorageBasePath() {
        return this.storageBasePath;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = (prime * result) + ((id == null) ? 0 : id.hashCode());

        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        if (obj == null) {
            return false;
        }

        if (getClass() != obj.getClass()) {
            return false;
        }

        ProductOssInfo other = (ProductOssInfo) obj;

        if (id == null) {
            if (other.id != null) {
                return false;
            }
        } else if (!id.equals(other.id)) {
            return false;
        }

        return true;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("ProductOssInfo [");
        sb.append("id=").append(id);
        sb.append(", ");
        sb.append("productName=").append(productName);
        sb.append(", ");
        sb.append("moduleName=").append(moduleName);
        sb.append(", ");
        sb.append("respositoryName=").append(respositoryName);
        sb.append(", ");
        sb.append("storageBasePath=").append(storageBasePath);
        sb.append("]");

        return sb.toString();
    }
}
