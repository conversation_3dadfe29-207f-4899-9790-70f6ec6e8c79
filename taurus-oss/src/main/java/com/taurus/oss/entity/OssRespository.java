package com.taurus.oss.entity;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;


/**
 * 表名：oss_respository
 * 备注：Oss 仓库信息
 *
 * <AUTHOR>
 */
@Table(name = "oss_respository")
public class OssRespository {
    /** 仓库名字, 数据库字段：bucket_name */
    @Id
    @Column(name = "bucket_name")
    @GeneratedValue(strategy = GenerationType.AUTO)
    private String bucketName;

    /** 仓库类型：native本地仓库；Ali阿里云oss, 数据库字段：type */
    private String type;

    /** 可外网访问的oss接入点；仓库为native时表示系统存储的磁盘路径, 数据库字段：internet_endpoint */
    private String internetEndpoint;

    /** 可内网访问的oss接入点；仓库为native时表示系统存储的磁盘路径, 数据库字段：internal_endpoint */
    private String internalEndpoint;

    /** 访问keyid, 数据库字段：access_key_id */
    private String accessKeyId;

    /** 访问keysecret, 数据库字段：access_key_secret */
    private String accessKeySecret;

    /** 绑定的外网访问地址, 数据库字段：binded_internet_url */
    private String bindedInternetUrl;

    /** 绑定的内网访问地址, 数据库字段：binded_internal_url */
    private String bindedInternalUrl;

    /** 绑定的cdn域名, 数据库字段：cdn_url */
    private String cdnUrl;

    /** 备注信息, 数据库字段：memo */
    private String memo;

    /** 设置仓库名字, 数据库字段：oss_respository.bucket_name */
    public void setBucketName(String bucketName) {
        this.bucketName = bucketName;
    }

    /** 获取仓库名字, 数据库字段：oss_respository.bucket_name */
    public String getBucketName() {
        return this.bucketName;
    }

    /** 设置仓库类型：native本地仓库；Ali阿里云oss, 数据库字段：oss_respository.type */
    public void setType(String type) {
        this.type = type;
    }

    /** 获取仓库类型：native本地仓库；Ali阿里云oss, 数据库字段：oss_respository.type */
    public String getType() {
        return this.type;
    }

    /** 设置可外网访问的oss接入点；仓库为native时表示系统存储的磁盘路径, 数据库字段：oss_respository.internet_endpoint */
    public void setInternetEndpoint(String internetEndpoint) {
        this.internetEndpoint = internetEndpoint;
    }

    /** 获取可外网访问的oss接入点；仓库为native时表示系统存储的磁盘路径, 数据库字段：oss_respository.internet_endpoint */
    public String getInternetEndpoint() {
        return this.internetEndpoint;
    }

    /** 设置可内网访问的oss接入点；仓库为native时表示系统存储的磁盘路径, 数据库字段：oss_respository.internal_endpoint */
    public void setInternalEndpoint(String internalEndpoint) {
        this.internalEndpoint = internalEndpoint;
    }

    /** 获取可内网访问的oss接入点；仓库为native时表示系统存储的磁盘路径, 数据库字段：oss_respository.internal_endpoint */
    public String getInternalEndpoint() {
        return this.internalEndpoint;
    }

    /** 设置访问keyid, 数据库字段：oss_respository.access_key_id */
    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }

    /** 获取访问keyid, 数据库字段：oss_respository.access_key_id */
    public String getAccessKeyId() {
        return this.accessKeyId;
    }

    /** 设置访问keysecret, 数据库字段：oss_respository.access_key_secret */
    public void setAccessKeySecret(String accessKeySecret) {
        this.accessKeySecret = accessKeySecret;
    }

    /** 获取访问keysecret, 数据库字段：oss_respository.access_key_secret */
    public String getAccessKeySecret() {
        return this.accessKeySecret;
    }

    /** 设置绑定的外网访问地址, 数据库字段：oss_respository.binded_internet_url */
    public void setBindedInternetUrl(String bindedInternetUrl) {
        this.bindedInternetUrl = bindedInternetUrl;
    }

    /** 获取绑定的外网访问地址, 数据库字段：oss_respository.binded_internet_url */
    public String getBindedInternetUrl() {
        return this.bindedInternetUrl;
    }

    /** 设置绑定的内网访问地址, 数据库字段：oss_respository.binded_internal_url */
    public void setBindedInternalUrl(String bindedInternalUrl) {
        this.bindedInternalUrl = bindedInternalUrl;
    }

    /** 获取绑定的内网访问地址, 数据库字段：oss_respository.binded_internal_url */
    public String getBindedInternalUrl() {
        return this.bindedInternalUrl;
    }

    /** 设置绑定的cdn域名, 数据库字段：oss_respository.cdn_url */
    public void setCdnUrl(String cdnUrl) {
        this.cdnUrl = cdnUrl;
    }

    /** 获取绑定的cdn域名, 数据库字段：oss_respository.cdn_url */
    public String getCdnUrl() {
        return this.cdnUrl;
    }

    /** 设置备注信息, 数据库字段：oss_respository.memo */
    public void setMemo(String memo) {
        this.memo = memo;
    }

    /** 获取备注信息, 数据库字段：oss_respository.memo */
    public String getMemo() {
        return this.memo;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = (prime * result) +
            ((bucketName == null) ? 0 : bucketName.hashCode());

        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        if (obj == null) {
            return false;
        }

        if (getClass() != obj.getClass()) {
            return false;
        }

        OssRespository other = (OssRespository) obj;

        if (bucketName == null) {
            if (other.bucketName != null) {
                return false;
            }
        } else if (!bucketName.equals(other.bucketName)) {
            return false;
        }

        return true;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("OssRespository [");
        sb.append("bucketName=").append(bucketName);
        sb.append(", ");
        sb.append("type=").append(type);
        sb.append(", ");
        sb.append("internetEndpoint=").append(internetEndpoint);
        sb.append(", ");
        sb.append("internalEndpoint=").append(internalEndpoint);
        sb.append(", ");
        sb.append("accessKeyId=").append(accessKeyId);
        sb.append(", ");
        sb.append("accessKeySecret=").append(accessKeySecret);
        sb.append(", ");
        sb.append("bindedInternetUrl=").append(bindedInternetUrl);
        sb.append(", ");
        sb.append("bindedInternalUrl=").append(bindedInternalUrl);
        sb.append(", ");
        sb.append("cdnUrl=").append(cdnUrl);
        sb.append(", ");
        sb.append("memo=").append(memo);
        sb.append("]");

        return sb.toString();
    }
}
