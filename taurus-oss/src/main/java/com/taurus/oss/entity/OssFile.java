package com.taurus.oss.entity;

import java.math.BigDecimal;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;


/**
 * 表名：oss_file
 * 备注：存储在阿里云上的文件
 *
 * <AUTHOR>
 */
@Table(name = "oss_file")
public class OssFile {
    /**  数据库字段：id */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /** 对象名称, 数据库字段：object_name */
    private String objectName;

    /** 上传人id, 数据库字段：user_id */
    private Integer userId;

    /** 原始文件名, 数据库字段：original_file_name */
    private String originalFileName;

    /** 文件标题, 数据库字段：title */
    private String title;

    /** 文件描述, 数据库字段：description */
    private String description;

    /** 文件类型：P代表图片A表示音频V表示视频 O其他文件, 数据库字段：file_type */
    private String fileType;

    /** 文件大小，单位K, 数据库字段：file_size */
    private BigDecimal fileSize;

    /** 文件格式，后缀名, 数据库字段：file_format */
    private String fileFormat;

    /** 对象是否在产品中使用, 数据库字段：using */
    private Boolean using;

    /** 插入时间, 数据库字段：insert_time */
    private Date insertTime;

    /** 产品oss信息记录id, 数据库字段：product_oss_info_id */
    private Integer productOssInfoId;

    /** Oss仓库名称, 数据库字段：respository_name */
    private String respositoryName;

    /**  数据库字段：oss_file.id */
    public void setId(Integer id) {
        this.id = id;
    }

    /**  数据库字段：oss_file.id */
    public Integer getId() {
        return this.id;
    }

    /** 设置对象名称, 数据库字段：oss_file.object_name */
    public void setObjectName(String objectName) {
        this.objectName = objectName;
    }

    /** 获取对象名称, 数据库字段：oss_file.object_name */
    public String getObjectName() {
        return this.objectName;
    }

    /** 设置上传人id, 数据库字段：oss_file.user_id */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /** 获取上传人id, 数据库字段：oss_file.user_id */
    public Integer getUserId() {
        return this.userId;
    }

    /** 设置原始文件名, 数据库字段：oss_file.original_file_name */
    public void setOriginalFileName(String originalFileName) {
        this.originalFileName = originalFileName;
    }

    /** 获取原始文件名, 数据库字段：oss_file.original_file_name */
    public String getOriginalFileName() {
        return this.originalFileName;
    }

    /** 设置文件标题, 数据库字段：oss_file.title */
    public void setTitle(String title) {
        this.title = title;
    }

    /** 获取文件标题, 数据库字段：oss_file.title */
    public String getTitle() {
        return this.title;
    }

    /** 设置文件描述, 数据库字段：oss_file.description */
    public void setDescription(String description) {
        this.description = description;
    }

    /** 获取文件描述, 数据库字段：oss_file.description */
    public String getDescription() {
        return this.description;
    }

    /** 设置文件类型：P代表图片A表示音频V表示视频 O其他文件, 数据库字段：oss_file.file_type */
    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    /** 获取文件类型：P代表图片A表示音频V表示视频 O其他文件, 数据库字段：oss_file.file_type */
    public String getFileType() {
        return this.fileType;
    }

    /** 设置文件大小，单位K, 数据库字段：oss_file.file_size */
    public void setFileSize(BigDecimal fileSize) {
        this.fileSize = fileSize;
    }

    /** 获取文件大小，单位K, 数据库字段：oss_file.file_size */
    public BigDecimal getFileSize() {
        return this.fileSize;
    }

    /** 设置文件格式，后缀名, 数据库字段：oss_file.file_format */
    public void setFileFormat(String fileFormat) {
        this.fileFormat = fileFormat;
    }

    /** 获取文件格式，后缀名, 数据库字段：oss_file.file_format */
    public String getFileFormat() {
        return this.fileFormat;
    }

    /** 设置对象是否在产品中使用, 数据库字段：oss_file.using */
    public void setUsing(Boolean using) {
        this.using = using;
    }

    /** 获取对象是否在产品中使用, 数据库字段：oss_file.using */
    public Boolean getUsing() {
        return this.using;
    }

    /** 设置插入时间, 数据库字段：oss_file.insert_time */
    public void setInsertTime(Date insertTime) {
        this.insertTime = insertTime;
    }

    /** 获取插入时间, 数据库字段：oss_file.insert_time */
    public Date getInsertTime() {
        return this.insertTime;
    }

    /** 设置产品oss信息记录id, 数据库字段：oss_file.product_oss_info_id */
    public void setProductOssInfoId(Integer productOssInfoId) {
        this.productOssInfoId = productOssInfoId;
    }

    /** 获取产品oss信息记录id, 数据库字段：oss_file.product_oss_info_id */
    public Integer getProductOssInfoId() {
        return this.productOssInfoId;
    }

    /** 设置Oss仓库名称, 数据库字段：oss_file.respository_name */
    public void setRespositoryName(String respositoryName) {
        this.respositoryName = respositoryName;
    }

    /** 获取Oss仓库名称, 数据库字段：oss_file.respository_name */
    public String getRespositoryName() {
        return this.respositoryName;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = (prime * result) + ((id == null) ? 0 : id.hashCode());

        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        if (obj == null) {
            return false;
        }

        if (getClass() != obj.getClass()) {
            return false;
        }

        OssFile other = (OssFile) obj;

        if (id == null) {
            if (other.id != null) {
                return false;
            }
        } else if (!id.equals(other.id)) {
            return false;
        }

        return true;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("OssFile [");
        sb.append("id=").append(id);
        sb.append(", ");
        sb.append("objectName=").append(objectName);
        sb.append(", ");
        sb.append("userId=").append(userId);
        sb.append(", ");
        sb.append("originalFileName=").append(originalFileName);
        sb.append(", ");
        sb.append("title=").append(title);
        sb.append(", ");
        sb.append("description=").append(description);
        sb.append(", ");
        sb.append("fileType=").append(fileType);
        sb.append(", ");
        sb.append("fileSize=").append(fileSize);
        sb.append(", ");
        sb.append("fileFormat=").append(fileFormat);
        sb.append(", ");
        sb.append("using=").append(using);
        sb.append(", ");
        sb.append("insertTime=").append(insertTime);
        sb.append(", ");
        sb.append("productOssInfoId=").append(productOssInfoId);
        sb.append(", ");
        sb.append("respositoryName=").append(respositoryName);
        sb.append("]");

        return sb.toString();
    }
}
