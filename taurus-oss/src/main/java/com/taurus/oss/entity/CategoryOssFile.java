package com.taurus.oss.entity;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;


/**
 * 表名：category_oss_file
 * 备注：每个分类下包含哪些oss_file
 *
 * <AUTHOR>
 */
@Table(name = "category_oss_file")
public class CategoryOssFile {
    /**  数据库字段：id */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /** 类别id, 数据库字段：category_id */
    private Integer categoryId;

    /** oss_file_id, 数据库字段：oss_file_id */
    private Integer ossFileId;

    /**  数据库字段：category_oss_file.id */
    public void setId(Integer id) {
        this.id = id;
    }

    /**  数据库字段：category_oss_file.id */
    public Integer getId() {
        return this.id;
    }

    /** 设置类别id, 数据库字段：category_oss_file.category_id */
    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    /** 获取类别id, 数据库字段：category_oss_file.category_id */
    public Integer getCategoryId() {
        return this.categoryId;
    }

    /** 设置oss_file_id, 数据库字段：category_oss_file.oss_file_id */
    public void setOssFileId(Integer ossFileId) {
        this.ossFileId = ossFileId;
    }

    /** 获取oss_file_id, 数据库字段：category_oss_file.oss_file_id */
    public Integer getOssFileId() {
        return this.ossFileId;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = (prime * result) + ((id == null) ? 0 : id.hashCode());

        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        if (obj == null) {
            return false;
        }

        if (getClass() != obj.getClass()) {
            return false;
        }

        CategoryOssFile other = (CategoryOssFile) obj;

        if (id == null) {
            if (other.id != null) {
                return false;
            }
        } else if (!id.equals(other.id)) {
            return false;
        }

        return true;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("CategoryOssFile [");
        sb.append("id=").append(id);
        sb.append(", ");
        sb.append("categoryId=").append(categoryId);
        sb.append(", ");
        sb.append("ossFileId=").append(ossFileId);
        sb.append("]");

        return sb.toString();
    }
}
