package com.taurus.oss.entity;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;


/**
 * 表名：oss_file_audio
 *
 * <AUTHOR>
 */
@Table(name = "oss_file_audio")
public class OssFileAudio {
    /** ossFileId, 数据库字段：oss_file_id */
    @Id
    @Column(name = "oss_file_id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Integer ossFileId;

    /** 时长(毫秒), 数据库字段：duration */
    private Integer duration;

    /** 设置ossFileId, 数据库字段：oss_file_audio.oss_file_id */
    public void setOssFileId(Integer ossFileId) {
        this.ossFileId = ossFileId;
    }

    /** 获取ossFileId, 数据库字段：oss_file_audio.oss_file_id */
    public Integer getOssFileId() {
        return this.ossFileId;
    }

    /** 设置时长(毫秒), 数据库字段：oss_file_audio.duration */
    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    /** 获取时长(毫秒), 数据库字段：oss_file_audio.duration */
    public Integer getDuration() {
        return this.duration;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = (prime * result) +
            ((ossFileId == null) ? 0 : ossFileId.hashCode());

        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        if (obj == null) {
            return false;
        }

        if (getClass() != obj.getClass()) {
            return false;
        }

        OssFileAudio other = (OssFileAudio) obj;

        if (ossFileId == null) {
            if (other.ossFileId != null) {
                return false;
            }
        } else if (!ossFileId.equals(other.ossFileId)) {
            return false;
        }

        return true;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("OssFileAudio [");
        sb.append("ossFileId=").append(ossFileId);
        sb.append(", ");
        sb.append("duration=").append(duration);
        sb.append("]");

        return sb.toString();
    }
}
