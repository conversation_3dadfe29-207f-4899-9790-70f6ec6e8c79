package com.taurus.oss.mapper;

import java.util.Map;

import org.apache.ibatis.annotations.Select;

import com.gitee.fastmybatis.core.mapper.CrudMapper;
import com.taurus.oss.entity.ProductOssInfo;


/**
 * <AUTHOR>
 */
public interface ProductOssInfoMapper extends CrudMapper<ProductOssInfo, String> {

	@Select("select t.id,t.product_name as productName,t.module_name as moduleName,t.respository_name as respositoryName,t.storage_base_path as storageBasePath, t1.bucket_name as bucketName,t1.type,t1.internet_endpoint as internetEndpoint,t1.internal_endpoint as internalEndpoint,t1.access_key_id as accessKeyId,t1.access_key_secret as accessKeySecret,t1.binded_internet_url as bindedInternetUrl,t1.binded_internal_url as bindedInternalUrl,t1.memo from product_oss_info t left join oss_respository t1 ON t.respository_name=t1.bucket_name "
			+ "where t.product_name=#{productName} and t.module_name=#{moduleName}")
	Map<String,Object> getProductOssInfoAndOssRespository(String productName, String moduleName);
}
