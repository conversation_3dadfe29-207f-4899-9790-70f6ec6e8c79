package com.taurus.oss.util;

public class OssUtil {

	// Endpoint以杭州为例，其它Region请按实际情况填写。
	public static final String endpoint = "http://oss-cn-hangzhou.aliyuncs.com";
	// RAM账号。
	public static final String accessKeyId = "LTAI4G6geztw5fN5jH8pnW4j";
	public static final String accessKeySecret = "******************************";
	public static final String bucketName = "ielt";

	// public static void main(String[] args) {
	// if (args.length == 0) {
	// System.out.println("请输入上传文件的路径");
	// return;
	// } else if (args.length == 1) {
	// System.out.println("请输入oss服务器的存储路径");
	// return;
	// }

	// List<String> fileList = FileUtil.getFiles(args[0]);

	// // 创建OSSClient实例。
	// OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId,
	// accessKeySecret);

	// fileList.forEach(item -> {
	// File file = new File(item);
	// String fileName = file.getName();
	// String objectName = args[1] + "/" + fileName;

	// System.out.println("文件名：" + fileName);
	// System.out.println("oss存储对象名：" + objectName);

	// // 上传文件流。
	// InputStream inputStream = null;
	// try {
	// inputStream = new FileInputStream(file);
	// } catch (FileNotFoundException e) {
	// e.printStackTrace();
	// }
	// ossClient.putObject(bucketName, objectName, inputStream);
	// System.out.println("上传完毕");
	// });

	// // 关闭OSSClient。
	// ossClient.shutdown();
	// }
}
