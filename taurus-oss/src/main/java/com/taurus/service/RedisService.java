package com.taurus.service;

import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.taurus.utils.MyStringUtil;

@Service("ossRedisService")
public class RedisService {
	@Autowired
	@Qualifier("ossRedisTemplate")
	private RedisTemplate<String, Object> redisTemplate;

	/**
	 * 获取微信基础accessToken
	 * 
	 * @param account
	 * @return
	 */
	public String getBaseAccessToken(String accountName) {
		StringBuffer buffer = new StringBuffer("baseAccessToken_");
		buffer.append(accountName);
		String value = (String) redisTemplate.opsForValue().get(buffer.toString());
		return value;
	}

	/**
	 * 保存微信accessToken
	 * 
	 * @param token
	 * @param account
	 */
	public void saveBaseAccessToken(String token, String accountName) {
		StringBuffer buffer = new StringBuffer("baseAccessToken_");
		buffer.append(accountName);
		redisTemplate.opsForValue().set(buffer.toString(), token, 1, TimeUnit.HOURS);// 保存1小时
	}

	/**
	 * 获取一个六位的随机码
	 * 
	 * @param requestId
	 * @return
	 */
	public String getRandomValidationCode(String requestId) {
		StringBuffer key = new StringBuffer("random_validation");
		key.append(requestId);
		String value = (String) redisTemplate.opsForValue().get(key.toString());
		return value;
	}

	/**
	 * 保存一个六位的随机码
	 * 
	 * @param requestId
	 */
	public void saveRandomValidationCode(String requestId) {
		StringBuffer key = new StringBuffer("random_validation");
		key.append(requestId);
		String randomStr = MyStringUtil.getRandomString(6);
		redisTemplate.opsForValue().set(key.toString(), randomStr, 3, TimeUnit.MINUTES);// 保存3分钟
	}

	/**
	 * 保存短信验证码到缓存
	 * 
	 * @param openId
	 * @param user
	 */
	public void saveValidationSMS(String phoneNo, String code) {
		StringBuffer key = new StringBuffer("sms_validation");
		key.append(phoneNo);
		redisTemplate.opsForValue().set(key.toString(), code, 3, TimeUnit.MINUTES);// 保存3分钟
	}

	/**
	 * 获取短信验证码
	 * 
	 * @param openId
	 * @return
	 */
	public String getValidationSMS(String phoneNo) {
		StringBuffer key = new StringBuffer("sms_validation");
		key.append(phoneNo);
		String value = (String) redisTemplate.opsForValue().get(key.toString());
		return value;
	}

	/**
	 * 前端请求编号
	 * 
	 * @param requestId
	 */
	public void saveRequestId(String requestId) {
		StringBuffer key = new StringBuffer("request");
		key.append(requestId);
		redisTemplate.opsForValue().set(key.toString(), requestId, 5, TimeUnit.MINUTES);// 保存5分钟
	}

	/**
	 * 获取请求编号
	 * 
	 * @param requestId
	 * @return
	 */
	public String getRequestId(String requestId) {
		StringBuffer key = new StringBuffer("request");
		key.append(requestId);
		String value = (String) redisTemplate.opsForValue().get(key.toString());
		return value;
	}

	/**
	 * 删除请求编号
	 */
	public Boolean deleteRequestId(String requestId) {
		StringBuffer key = new StringBuffer("request");
		key.append(requestId);
		String value = (String) redisTemplate.opsForValue().get(key.toString());
		if (value != null) {
			return redisTemplate.delete(key.toString());
		} else {
			return true;
		}
	}

	/**
	 * 取出一个redis缓存对象
	 * 
	 * @param key
	 * @return
	 */
	public String get(String key) {
		return (String) redisTemplate.opsForValue().get(key);
	}

	/**
	 * 放入一个redis对象
	 * 
	 * @param key
	 * @param value
	 */
	public void put(String key, Object value, Long expire) {
		redisTemplate.opsForValue().set(key, JSONObject.toJSONStringWithDateFormat(value, "yyyy-MM-dd HH:mm:ss",
				SerializerFeature.WriteDateUseDateFormat), 1, TimeUnit.MINUTES);// 保存一年
	}

	/**
	 * 删除一个键值
	 * 
	 * @param key
	 * @return
	 */
	public boolean delete(StringBuffer key) {
		String value = (String) redisTemplate.opsForValue().get(key.toString());
		if (value != null) {
			return redisTemplate.delete(key.toString());
		} else {
			return true;
		}

	}

}
