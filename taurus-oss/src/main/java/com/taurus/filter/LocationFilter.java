package com.taurus.filter;

import java.io.IOException;
import java.util.Enumeration;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSONObject;


/**
 * 前端调用位置解析
 * <AUTHOR>
 *
 */
public class LocationFilter implements Filter {
	
	private static final org.slf4j.Logger logger = LoggerFactory.getLogger(LocationFilter.class);

	@Override
	public void init(FilterConfig filterConfig) throws ServletException {
		

	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, Filter<PERSON>hain chain)
			throws IOException, ServletException {
		request.setCharacterEncoding("utf-8");
		response.setCharacterEncoding("utf-8");
		HttpServletRequest req = (HttpServletRequest) request;
		String uri = req.getRequestURI();
		String terminal = req.getParameter("terminal");
		String callLocationCode = req.getParameter("callLocationCode");
		if(terminal==null||callLocationCode==null) {
			//logger.info("不合规的请求:"+uri);
			//return;	
		}else {
			Enumeration<String> names =  req.getParameterNames();
			JSONObject jsonObject = new JSONObject();
			while(names.hasMoreElements()) {
				String name =names.nextElement();	
				jsonObject.put(name, req.getParameter(name));
			}

			
			logger.info("产品："+terminal+" 页面："+callLocationCode);
			logger.info("请求接口："+uri);
			logger.info("请求参数："+jsonObject.toString());
		}
		
		
		/**
		 * 调用下一个Filter或者在没有下一个Filter的情况下,调用请求终点站——Servlet
		 */
		chain.doFilter(request, response);
		
		
		
	}

	@Override
	public void destroy() {

	}

}
