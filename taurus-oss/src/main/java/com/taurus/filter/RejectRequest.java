package com.taurus.filter;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletContext;
import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.web.context.support.WebApplicationContextUtils;

import com.alibaba.fastjson.JSONObject;
import com.taurus.service.RedisService;


/**
 * 防止重复提交请求导致的数据重复问题
 * <AUTHOR>
 *
 */
public class RejectRequest implements Filter {
	private static final org.slf4j.Logger logger = LoggerFactory.getLogger(RejectRequest.class);
	

	private RedisService redisService;
	
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    	ServletContext context = filterConfig.getServletContext(); 
		ApplicationContext ac = WebApplicationContextUtils .getWebApplicationContext(context); 
		redisService=ac.getBean(RedisService.class);//获取spring容器中 的RedisService
    }

    @Override
    public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) throws IOException, ServletException {
        final HttpServletRequest request = (HttpServletRequest) req;
        final HttpServletResponse response = (HttpServletResponse) res;
       
        //获取 header里的requestId
        final String requestId = request.getHeader("requestId");
        if(requestId!=null) {
        	if(redisService==null) {
        		logger.info("redisService is null");
        	}else {
        		String str = redisService.getRequestId(requestId);
            	if(str!=null) {
            		JSONObject responseJson = new JSONObject();
            		responseJson.put("code", "REPEAT_CALL");//重复请求
            		responseJson.put("message", "请求正在处理,请勿重复提交");
            		ServletOutputStream out = response.getOutputStream();
                    out.write(responseJson.toJSONString().getBytes());
                    out.flush();
            		return;
            	}else {
            		redisService.saveRequestId(requestId);
            	}
        	}
        }
    
      
        chain.doFilter(request, response);
    }
    
   
    
    @Override
    public void destroy() {
    }

}