package com.taurus;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration("ossWebConfig")
@EnableWebMvc
public class WebConfig implements WebMvcConfigurer {

    // @Override
    // public void addCorsMappings(CorsRegistry registry) {
    //
    // registry.addMapping("/**")
    //// .allowedOrigins("http://localhost:8080")
    // .allowedOriginPatterns("*")
    // .allowedMethods("DELETE","POST", "GET", "OPTIONS")
    // .allowedHeaders("enctype","Origin","X-Requested-With","Content-Type","Accept","Authorization","Access-Control-Allow-Credentials","requestid","token","userId")
    // .exposedHeaders("Content-Disposition")
    // .allowCredentials(true).maxAge(3600);
    //
    // }
}
