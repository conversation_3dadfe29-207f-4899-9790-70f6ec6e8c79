package com.taurus.utils;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;

import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSON;
import com.taurus.entity.ResponseObject;

public class IOUtil {
	
	/**
	 * 通过显示设置方式返回response对象
	 * @param response
	 * @param result
	 */
	public static void responseResult(HttpServletResponse response, ResponseObject result) {
        response.setCharacterEncoding("UTF-8");
        response.setStatus(HttpServletResponse.SC_OK);
        response.setContentType("application/json;charset=UTF-8");

        PrintWriter writer=null;
        try {
            writer=response.getWriter();
            writer.write(JSON.toJSONString(result));
            writer.flush();
        } catch(UnsupportedEncodingException ex) {
        	ex.printStackTrace();
        } catch (IOException ex) {
        	ex.printStackTrace();
        } catch(IllegalStateException ex) {
        	ex.printStackTrace();
        } finally {
            if(writer!=null) {
                writer.close();
            }
        }
    }
	
	
	/**
	 * 设置允许跨域头
	 * @param res
	 */
	public static void setCORSHeader(HttpServletResponse res) {
		res.setHeader("Access-Control-Allow-Origin", "*");
		res.setHeader("Access-Control-Allow-Methods", "POST, GET, OPTIONS, DELETE");
		res.setHeader("Access-Control-Max-Age", "3600");
		res.addHeader("Access-Control-Allow-Headers",
				"Origin, X-Requested-With, Content-Type, Accept, Authorization, Access-Control-Allow-Credentials");
		res.addHeader("Access-Control-Allow-Credentials", "true");
	}
}
