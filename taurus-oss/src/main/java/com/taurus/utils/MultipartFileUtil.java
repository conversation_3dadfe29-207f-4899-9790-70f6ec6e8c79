package com.taurus.utils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;
import java.util.Iterator;

import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import net.coobird.thumbnailator.Thumbnails;

/**
 * 获取图片尺寸和截图 工具类
 *
 * <AUTHOR>
 */
public class MultipartFileUtil {
	private static Logger log = LoggerFactory.getLogger(MultipartFileUtil.class);

	/**
	 * 获取图片最长边长度
	 * 
	 * @param params
	 * @return
	 */
	public int getImageLongestSizeLength(MultipartFile file) {
		int lengthSize = 0;
		// 获取图片格式
		String suffixName = getSuffixName(file);
		try {
			Iterator<ImageReader> readers = ImageIO.getImageReadersByFormatName(suffixName);
			ImageReader reader = (ImageReader) readers.next();
			InputStream inputStream = file.getInputStream();
			ImageInputStream iis = ImageIO.createImageInputStream(inputStream);
			reader.setInput(iis, true);
			if (reader.getWidth(0) > reader.getHeight(0)) {
				lengthSize = reader.getWidth(0);
			} else {
				lengthSize = reader.getHeight(0);
			}
			iis.close();
			inputStream.close();
		} catch (IOException e) {
			e.printStackTrace();
		}

		return lengthSize;
	}

	/**
	 * 获取图片后缀名
	 * 
	 * @param params
	 * @return
	 */
	public String getSuffixName(MultipartFile file) {
		String name = "";
		// 图片后缀
		String suffixName = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
		if (suffixName.indexOf("png") > 0) {
			name = "png";
		} else if (suffixName.indexOf("jpg") > 0) {
			name = "jpg";
		} else if (suffixName.indexOf("jpeg") > 0) {
			name = "jpeg";
		}
		return name;
	}

	/**
	 * 压缩图片到指定宽度
	 * 
	 * @param originalImageBytes
	 * @param width
	 * @return
	 */
	public static byte[] compressImage(byte[] originalImageBytes, int width) {
		if (originalImageBytes == null || originalImageBytes.length <= 0) {
			return originalImageBytes;
		}

		byte[] destImageBytes = originalImageBytes;
		try {
			ByteArrayInputStream inputStream = new ByteArrayInputStream(destImageBytes);

			// 先将图像读入内存
			java.awt.image.BufferedImage originalImage = ImageIO.read(inputStream);
			if (originalImage == null) {
				throw new IOException("无法读取图片格式");
			}

			String format = getSuffixName(originalImageBytes);
			boolean hasAlpha = "png".equals(format) || originalImage.getColorModel().hasAlpha();

			// 根据格式选择合适的颜色模型
			java.awt.image.BufferedImage destImage = new java.awt.image.BufferedImage(
					originalImage.getWidth(),
					originalImage.getHeight(),
					hasAlpha ? java.awt.image.BufferedImage.TYPE_INT_ARGB : java.awt.image.BufferedImage.TYPE_INT_RGB);

			// 绘制到新的图像
			java.awt.Graphics2D g = destImage.createGraphics();
			g.drawImage(originalImage, 0, 0, null);
			g.dispose();

			// 对转换后的图像进行压缩
			ByteArrayOutputStream outputStream = new ByteArrayOutputStream(destImageBytes.length);
			Thumbnails.of(destImage).width(width).outputFormat(format).toOutputStream(outputStream);
			destImageBytes = outputStream.toByteArray();

			log.info("【图片压缩】imageId={} | 图片原大小={}kb | 压缩后大小={}kb", "", originalImageBytes.length / 1024,
					destImageBytes.length / 1024);
		} catch (Exception e) {
			log.error("【图片压缩】msg=图片压缩失败!", e);
		}
		return destImageBytes;
	}

	/**
	 * 根据指定大小压缩图片
	 *
	 * @param originalImageBytes 源图片字节数组
	 * @param destImageSize      指定图片大小，单位kb
	 * @return 压缩质量后的图片字节数组
	 */
	public static byte[] compressImage(byte[] originalImageBytes, long destImageSize) {
		if (originalImageBytes == null || originalImageBytes.length <= 0
				|| originalImageBytes.length < destImageSize * 1024) {
			return originalImageBytes;
		}
		long originalImageSize = originalImageBytes.length;
		double accuracy = getExperienceAccuracyValue(originalImageSize);

		byte[] destImageBytes = originalImageBytes;
		try {
			// 先将图像读入内存
			ByteArrayInputStream inputStream = new ByteArrayInputStream(destImageBytes);
			java.awt.image.BufferedImage originalImage = ImageIO.read(inputStream);
			if (originalImage == null) {
				throw new IOException("无法读取图片格式");
			}

			String format = getSuffixName(originalImageBytes);
			boolean hasAlpha = "png".equals(format) || originalImage.getColorModel().hasAlpha();

			// 根据格式选择合适的颜色模型
			java.awt.image.BufferedImage destImage = new java.awt.image.BufferedImage(
					originalImage.getWidth(),
					originalImage.getHeight(),
					hasAlpha ? java.awt.image.BufferedImage.TYPE_INT_ARGB : java.awt.image.BufferedImage.TYPE_INT_RGB);

			// 绘制到新的图像
			java.awt.Graphics2D g = destImage.createGraphics();
			g.drawImage(originalImage, 0, 0, null);
			g.dispose();

			// 对转换后的图像进行压缩
			ByteArrayOutputStream outputStream = new ByteArrayOutputStream(destImageBytes.length);
			Thumbnails.of(destImage).scale(accuracy).outputQuality(accuracy).outputFormat(format)
					.toOutputStream(outputStream);
			destImageBytes = outputStream.toByteArray();

			while (destImageBytes.length > destImageSize * 1024) {
				accuracy = accuracy * 0.9; // 逐步降低质量，直到满足大小要求
				inputStream = new ByteArrayInputStream(destImageBytes);
				originalImage = ImageIO.read(inputStream);

				// 重新检测格式和透明度
				format = getSuffixName(destImageBytes);
				hasAlpha = "png".equals(format) || originalImage.getColorModel().hasAlpha();

				destImage = new java.awt.image.BufferedImage(
						originalImage.getWidth(),
						originalImage.getHeight(),
						hasAlpha ? java.awt.image.BufferedImage.TYPE_INT_ARGB
								: java.awt.image.BufferedImage.TYPE_INT_RGB);

				g = destImage.createGraphics();
				g.drawImage(originalImage, 0, 0, null);
				g.dispose();

				outputStream = new ByteArrayOutputStream(destImageBytes.length);
				Thumbnails.of(destImage).scale(accuracy).outputQuality(accuracy).outputFormat(format)
						.toOutputStream(outputStream);
				destImageBytes = outputStream.toByteArray();
			}

			log.info("【图片压缩】imageId={} | 图片原大小={}kb | 压缩后大小={}kb", "", originalImageSize / 1024,
					destImageBytes.length / 1024);
		} catch (Exception e) {
			log.error("【图片压缩】msg=图片压缩失败!", e);
		}
		return destImageBytes;
	}

	/**
	 * 自动调节精度(经验数值)
	 *
	 * @param size 源图片大小，单位kb
	 * @return 图片压缩质量比
	 */
	private static double getExperienceAccuracyValue(long size) {
		double accuracy;
		if (size < 900) {
			accuracy = 0.85;
		} else if (size < 2047) {
			accuracy = 0.8;
		} else if (size < 3275) {
			accuracy = 0.7;
		} else {
			accuracy = 0.4;
		}
		return accuracy;
	}

	/**
	 * base64 转MultipartFile
	 * 
	 * @param base64
	 * @return
	 */
	public static MultipartFile base64ToMultipart(String base64) {
		// 注意base64是否有头信息，如：data:image/jpeg;base64,。。。
		String[] baseStrs = base64.split(",");
		byte[] b = Base64.getDecoder().decode(baseStrs[1]);
		for (int i = 0; i < b.length; ++i) {
			if (b[i] < 0) {
				b[i] += 256;
			}
		}
		return new BASE64DecodedMultipartFile(b, baseStrs[0]);
	}

	/**
	 * 压缩图片
	 * 
	 * @return
	 */
	public static MultipartFile compress(MultipartFile imageFile, Integer width, Long destSize) {
		MultipartFile result = imageFile;
		// 获取图片最长边
		// int imageLengthSize = getImageLongestSizeLength(fileImg);
		// Long swd = fileImg.getSize();
		try {
			InputStream inputStream = imageFile.getInputStream();
			byte[] imgData = new byte[0];
			if (width != null && destSize == null) {
				imgData = compressImage(getByteArray(inputStream), width);
			} else if (width == null && destSize != null) {
				imgData = compressImage(getByteArray(inputStream), destSize);
			}
			if (imgData.length > 0) {
				String base64Str = Base64.getEncoder().encodeToString(imgData);
				String imgBase64Str = "data:" + imageFile.getContentType() + ";base64," + base64Str;
				MultipartFile def = base64ToMultipart(imgBase64Str);
				result = def;
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		return result;
	}

	/**
	 * InputStream转换为byte[]
	 *
	 * @param inStream
	 * @return
	 * @throws IOException
	 */
	public static final byte[] getByteArray(InputStream inStream) throws IOException {
		ByteArrayOutputStream swapStream = new ByteArrayOutputStream();
		byte[] buff = new byte[100];
		int rc = 0;
		while ((rc = inStream.read(buff, 0, 100)) > 0) {
			swapStream.write(buff, 0, rc);
		}
		byte[] in2b = swapStream.toByteArray();
		return in2b;
	}

	/**
	 * 获取图片后缀名
	 * 
	 * @param imageBytes 图片字节数组
	 * @return 图片格式
	 */
	public static String getSuffixName(byte[] imageBytes) {
		if (imageBytes == null || imageBytes.length < 8) {
			return "jpeg"; // 默认格式
		}

		// 检查PNG文件头
		if (imageBytes[0] == (byte) 0x89 &&
				imageBytes[1] == (byte) 0x50 &&
				imageBytes[2] == (byte) 0x4E &&
				imageBytes[3] == (byte) 0x47) {
			return "png";
		}

		try {
			ByteArrayInputStream bis = new ByteArrayInputStream(imageBytes);
			ImageInputStream iis = ImageIO.createImageInputStream(bis);
			Iterator<ImageReader> readers = ImageIO.getImageReaders(iis);
			if (readers.hasNext()) {
				ImageReader reader = readers.next();
				String formatName = reader.getFormatName().toLowerCase();
				iis.close();
				bis.close();
				return formatName;
			}
		} catch (IOException e) {
			log.error("获取图片格式失败", e);
		}

		return "jpeg"; // 默认格式
	}
}
