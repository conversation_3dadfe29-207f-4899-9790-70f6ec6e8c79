package com.taurus.utils;

 
import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class FileUtil {

    /**
     * 得到文件格式
     * @param path
     * @return
     */
    public static String getFileFormat(String fileName){
        return  fileName.toLowerCase().substring(fileName.toLowerCase().lastIndexOf(".") + 1);
    }
    

    
    
    /**
     * 获取某个目录下所有直接下级文件，不包括目录下的子目录的下的文件
     * 递归循环
     * @param path
     * @return
     */
    public static List<String> getFiles(String path) {
        List<String> files = new ArrayList<String>();
        File file = new File(path);
        File[] tempList = file.listFiles();

        for (int i = 0; i < tempList.length; i++) {
        	File ofile = tempList[i];
            if (ofile.isFile()) {
                files.add(tempList[i].toString());   
            }else if(ofile.isDirectory()) {
            	List<String> subList = getFiles(ofile.getPath());
            	files.addAll(subList);
            }
        }
        return files;
    }
}
