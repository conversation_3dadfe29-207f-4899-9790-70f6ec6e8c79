package com.taurus;

import javax.servlet.Filter;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration("ossWebFilterConfig")
public class WebFilterConfig {

    // @Bean
    // public FilterRegistrationBean<Filter> corsFilterRegistration() {
    // FilterRegistrationBean<Filter> registration = new FilterRegistrationBean<>();
    // registration.setFilter(corsFilter());
    // registration.addUrlPatterns("/*");
    // registration.setName("CorsFilter");
    // registration.setOrder(1);
    // return registration;
    // }

    @Bean("ossLocationFilterRegistration")
    FilterRegistrationBean<Filter> locationFilterRegistration() {
        FilterRegistrationBean<Filter> registration = new FilterRegistrationBean<>();
        registration.setFilter(locationFilter());
        registration.addUrlPatterns("/file/*");
        registration.setName("locationFilter");
        registration.setOrder(3);
        return registration;
    }

    @Bean("ossLocationFilter")
    Filter locationFilter() {
        return new com.taurus.filter.LocationFilter();
    }

}
