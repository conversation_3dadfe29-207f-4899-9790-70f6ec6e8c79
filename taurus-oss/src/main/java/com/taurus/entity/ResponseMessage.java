package com.taurus.entity;

import java.lang.reflect.Field;

public class ResponseMessage {
	
	public final static String CONTENT_EMPTY="内容为空";
	public final static String PARAMETERS_ERROR ="参数不合法或错误";
	
	public final static String NO_USER_SCAN ="用户未扫描登陆二维码";
	public final static String NO_TOKEN = "无TOKEN";
	public final static String INVALID_TOKEN ="无效的token";
	public final static String OVERDUE_TOKEN ="过期的token";
	
	public final static String SUCESS = "成功";
	public final static String NO_USER_ID="无用户Id";
	public final static String NO_EXAMINATION_ID="无考试Id";
	public final static String NO_COMPANY_ID="无公司Id";
	public final static String INVALID_COMPANY_ID="无效的公司id";
	public final static String REPEATED_COMPANY_NAME="公司名已存在";
	
	public final static String NO_PDDS_TIMES="未购买成绩详情报表服务，请联系客服";
	
	public final static String IO_EXCEPTION="发生IO流错误";
	
	public final static String NO_MULTIPARTFILE="未选择文件上传";
	public final static String FILE_PARSE_ERROR="文件解析错误";
	public final static String NOT_EXCEL_FILE="不是excel文件";
	public final static String NOT_WORD_FILE="不是word文件";
	public final static String NO_IMAGE_FILE="没有图片文件";
	public final static String NOT_EXCEL_OR_WORD_FILE="不是excel或word文件";
	public final static String EXCEED_MAX_FILE_SIZE = "文件大小超过15M限制";
	
	//用户独立运营产品
	public final static String NOT_VALID_PRODUCT_CODE="不是有效的产品代码";
	public final static String SUBJECT_NAME_IS_EMPTY="空的栏目名称，请补充";
	
	//转交考试
	public final static String TRANSMIT_OVER="已经转交过了";//
	public final static String TRANSMIT_SELF="这是您自己的考试";//
	
	
	public final static String NO_OPENID="未获取到openid";//
		
	// 通过反射获取属性值
    public static String getValueByFieldName(String fieldName) {
        try {
            Field fieldTag = ResponseMessage.class.getDeclaredField(fieldName);
            return (String) fieldTag.get(ResponseMessage.class);
        } catch (Exception ex) {
            return null;
        }
    }
}
