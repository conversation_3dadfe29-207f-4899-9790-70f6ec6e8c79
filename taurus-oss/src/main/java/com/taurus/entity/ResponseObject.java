package com.taurus.entity;

public class ResponseObject {
	
	/**状态码：1表示成功，其他表示失败**/
	private String code;
	
	/**响应描述信息**/
	private String message;
	
	/**响应对象**/
	private Object data;
	
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public Object getData() {
		return data;
	}
	public void setData(Object data) {
		this.data = data;
	}
	
	public void setSuccess(Object data) {
		this.setCode("1");
		this.setMessage("成功");
		this.setData(data);
	}
	
	/**
	 * 设置接口逻辑失败的标准返回
	 * @param code
	 */
	public void setStandardFailure(String code) {
		this.setCode(code);
		this.setMessage(ResponseMessage.getValueByFieldName(code));
		this.setData("");
	}
	
	/**
	 * 成功返回
	 * @param data
	 * @return
	 */
	public static ResponseObject success(Object data) {
		ResponseObject response = new ResponseObject();
		response.setSuccess(data);
		return response;
	}
	
	/**
	 * 失败返回
	 * @param code
	 * @return
	 */
	public static ResponseObject failure(String code) {
		ResponseObject response = new ResponseObject();
		response.setStandardFailure(code);
		return response;
	}
}
