/**
 * 谁是卧底游戏API工具类
 */

// 服务器基础URL
const BASE_URL = "http://localhost:8080/party-games";

// API URL
const API = {
  WORD_LIBRARY: {
    LIST: `${BASE_URL}/api/word-library/list`,
    WORDS: `${BASE_URL}/api/word-library/words`,
    RANDOM: `${BASE_URL}/api/word-library/random`,
    CONFIG: `${BASE_URL}/api/word-library/config`,
  },
  ROOM: {
    CREATE: `${BASE_URL}/api/room/create`,
    JOIN: `${BASE_URL}/api/room/join`,
    START: `${BASE_URL}/api/room/start`,
    INFO: `${BASE_URL}/api/room/info`,
    PLAYER: `${BASE_URL}/api/room/player`,
    LEAVE: `${BASE_URL}/api/room/leave`,
    KICK: `${BASE_URL}/api/room/kick`,
    DISSOLVE: `${BASE_URL}/api/room/dissolve`,
    STATUS: `${BASE_URL}/api/room/status`,
  },
  GAME: {
    END: `${BASE_URL}/api/game/end`,
  },
  WEBSOCKET: {
    CONNECT: (roomId, playerNo) =>
      `ws://localhost:8080/party-games/ws/game/${roomId}/${playerNo}`,
  },
};

/**
 * 通用请求方法
 * @param {string} url - 请求URL
 * @param {Object} options - 请求选项
 * @returns {Promise} - 请求结果Promise
 */
const request = (url, options = {}) => {
  return new Promise((resolve, reject) => {
    uni.request({
      url,
      ...options,
      success: (res) => {
        if (res.statusCode === 200) {
          // 检查业务状态码
          if (res.data.code === 0) {
            resolve(res.data.data);
          } else {
            uni.showToast({
              title: res.data.message || "请求失败",
              icon: "none",
            });
            reject(res.data);
          }
        } else {
          uni.showToast({
            title: "服务器错误",
            icon: "none",
          });
          reject(res);
        }
      },
      fail: (err) => {
        uni.showToast({
          title: "网络错误",
          icon: "none",
        });
        reject(err);
      },
    });
  });
};

/**
 * 词库相关API
 */
export const wordLibraryApi = {
  /**
   * 获取词库列表
   * @returns {Promise<Array>} 词库列表
   */
  getLibraryList: () => {
    return request(API.WORD_LIBRARY.LIST);
  },

  /**
   * 获取指定词库的词对列表
   * @param {string} libraryName - 词库名称
   * @returns {Promise<Array>} 词对列表
   */
  getWordsByLibrary: (libraryName) => {
    return request(`${API.WORD_LIBRARY.WORDS}?libraryName=${libraryName}`);
  },

  /**
   * 获取随机词对
   * @param {string} libraryName - 词库名称
   * @returns {Promise<Object>} 随机词对
   */
  getRandomWordPair: (libraryName) => {
    return request(`${API.WORD_LIBRARY.RANDOM}?libraryName=${libraryName}`);
  },

  /**
   * 获取词库配置
   * @returns {Promise<Object>} 词库配置
   */
  getConfig: () => {
    return request(API.WORD_LIBRARY.CONFIG);
  },
};

/**
 * 游戏房间相关API
 */
export const roomApi = {
  /**
   * 创建游戏房间
   * @param {Object} roomData - 房间数据
   * @returns {Promise<Object>} 创建结果
   */
  createRoom: (roomData) => {
    return request(API.ROOM.CREATE, {
      method: "POST",
      data: roomData,
      header: {
        "Content-Type": "application/json",
      },
    });
  },

  /**
   * 加入游戏房间
   * @param {Object} joinData - 加入数据
   * @returns {Promise<Object>} 加入结果
   */
  joinRoom: (joinData) => {
    return request(API.ROOM.JOIN, {
      method: "POST",
      data: joinData,
      header: {
        "Content-Type": "application/json",
      },
    });
  },

  /**
   * 开始游戏
   * @param {string} roomId - 房间码
   * @returns {Promise<Object>} 游戏状态
   */
  startGame: (roomId) => {
    return request(`${API.ROOM.START}?roomId=${roomId}`, {
      method: "POST",
    });
  },

  /**
   * 获取房间信息
   * @param {string} roomId - 房间码
   * @returns {Promise<Object>} 房间信息
   */
  getRoomInfo: (roomId) => {
    return request(`${API.ROOM.INFO}?roomId=${roomId}`);
  },

  /**
   * 获取玩家信息
   * @param {string} roomId - 房间码
   * @param {string} playerNo - 玩家编号
   * @returns {Promise<Object>} 玩家信息
   */
  getPlayerInfo: (roomId, playerNo) => {
    return request(`${API.ROOM.PLAYER}?roomId=${roomId}&playerNo=${playerNo}`);
  },

  /**
   * 玩家离开房间
   * @param {string} roomId - 房间码
   * @param {string} playerNo - 玩家编号
   * @returns {Promise<boolean>} 离开结果
   */
  leaveRoom: (roomId, playerNo) => {
    return request(`${API.ROOM.LEAVE}?roomId=${roomId}&playerNo=${playerNo}`, {
      method: "POST",
    });
  },

  /**
   * 房主踢出玩家
   * @param {string} roomId - 房间码
   * @param {string} hostNo - 房主编号
   * @param {string} playerNo - 玩家编号
   * @returns {Promise<boolean>} 踢出结果
   */
  kickPlayer: (roomId, hostNo, playerNo) => {
    return request(
      `${API.ROOM.KICK}?roomId=${roomId}&hostNo=${hostNo}&playerNo=${playerNo}`,
      {
        method: "POST",
      }
    );
  },

  /**
   * 解散房间
   * @param {string} roomId - 房间码
   * @param {string} hostNo - 房主编号
   * @returns {Promise<boolean>} 解散结果
   */
  dissolveRoom: (roomId, hostNo) => {
    return request(`${API.ROOM.DISSOLVE}?roomId=${roomId}&hostNo=${hostNo}`, {
      method: "POST",
    });
  },

  /**
   * 获取房间状态
   * @param {string} roomId - 房间码
   * @returns {Promise<Object>} 房间状态信息
   */
  checkRoomStatus: (roomId) => {
    return request(`${API.ROOM.STATUS}?roomId=${roomId}`);
  },
};

/**
 * 游戏玩法相关API
 */
export const gameApi = {
  /**
   * 结束游戏
   * @param {string} roomId - 房间码
   * @returns {Promise<Object>} 游戏结果
   */
  endGame: (roomId) => {
    return request(`${API.GAME.END}?roomId=${roomId}`, {
      method: "POST",
    });
  },
};

/**
 * WebSocket连接管理
 */
export class GameWebSocket {
  constructor(roomId, playerNo, onMessage) {
    this.roomId = room;
    this.playerNo = playerNo;
    this.onMessage = onMessage;
    this.socket = null;
    this.isConnected = false;
    this.reconnectTimer = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
  }

  /**
   * 连接WebSocket
   */
  connect() {
    if (this.socket) {
      this.close();
    }

    const url = API.WEBSOCKET.CONNECT(this.roomId, this.playerNo);

    this.socket = uni.connectSocket({
      url,
      success: () => {
        console.log(`WebSocket连接成功: ${url}`);
      },
    });

    // 监听WebSocket连接打开
    this.socket.onOpen(() => {
      console.log("WebSocket连接已打开");
      this.isConnected = true;
      this.reconnectAttempts = 0;
    });

    // 监听WebSocket接收到服务器的消息
    this.socket.onMessage((res) => {
      try {
        const data = JSON.parse(res.data);
        if (this.onMessage) {
          this.onMessage(data);
        }
      } catch (e) {
        console.error("解析WebSocket消息失败", e);
      }
    });

    // 监听WebSocket错误
    this.socket.onError((res) => {
      console.error("WebSocket连接错误", res);
      this.isConnected = false;
      this.reconnect();
    });

    // 监听WebSocket关闭
    this.socket.onClose(() => {
      console.log("WebSocket连接已关闭");
      this.isConnected = false;
      this.reconnect();
    });

    return this;
  }

  /**
   * 重连WebSocket
   */
  reconnect() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);

      console.log(`尝试第${this.reconnectAttempts}次重连，延迟${delay}ms`);

      this.reconnectTimer = setTimeout(() => {
        this.connect();
      }, delay);
    } else {
      console.error("WebSocket重连次数已达最大限制，停止重连");
      uni.showToast({
        title: "连接服务器失败，请重新进入游戏",
        icon: "none",
        duration: 3000,
      });
    }
  }

  /**
   * 关闭WebSocket连接
   */
  close() {
    if (this.socket && this.isConnected) {
      this.socket.close();
    }

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    this.isConnected = false;
    this.socket = null;
  }
}

export default {
  wordLibraryApi,
  roomApi,
  gameApi,
  GameWebSocket,
};
