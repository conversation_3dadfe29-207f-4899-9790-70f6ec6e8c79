/**
 * 狼人杀助手API工具类
 */

// 服务器基础URL
const BASE_URL = "http://localhost:8080/api";

// API URL
const API = {
  WECHAT: {
    LOGIN: `${BASE_URL}/wechat/login`,
    QRCODE: `${BASE_URL}/wechat/getQRCodeBase64`,
  },
  WOLF: {
    ROOM: {
      CREATE: `${BASE_URL}/wolf/room/create`,
      JOIN: `${BASE_URL}/wolf/room/join`,
      INFO: `${BASE_URL}/wolf/room/info`,
      INFO_BY_CODE: `${BASE_URL}/wolf/room/infoByCode`,
      ROOM_ROLES: `${BASE_URL}/wolf/room/roomRoles`,
      RESTART: `${BASE_URL}/wolf/room/restart`,
      DELETE: `${BASE_URL}/wolf/room/delete`,
    },
    PLAYER: {
      INFO: `${BASE_URL}/wolf/player/info`,
      RO<PERSON>_PLAYERS: `${BASE_URL}/wolf/player/roomPlayers`,
      JOINED_ROOMS: `${BASE_URL}/wolf/player/joinedRooms`,
      GET_OR_CREATE_USER: `${BASE_URL}/wolf/player/getOrCreateUser`,
      UPDATE: `${BASE_URL}/wolf/player/update`,
    },

    ROLE: {
      ALL: `${BASE_URL}/wolf/role/all`,
      INFO: `${BASE_URL}/wolf/role/info`,
    },
  },
};

/**
 * 通用请求方法
 * @param {string} url - 请求URL
 * @param {Object} options - 请求选项
 * @returns {Promise} - 请求结果Promise
 */
const request = (url, options = {}) => {
  return new Promise((resolve, reject) => {
    uni.request({
      url,
      ...options,
      success: (res) => {
        if (res.statusCode === 200) {
          // 检查业务状态码
          if (res.data.code === 200) {
            resolve(res.data.data);
          } else {
            uni.showToast({
              title: res.data.message || "请求失败",
              icon: "none",
            });
            reject(res.data);
          }
        } else {
          uni.showToast({
            title: "服务器错误",
            icon: "none",
          });
          reject(res);
        }
      },
      fail: (err) => {
        uni.showToast({
          title: "网络错误",
          icon: "none",
        });
        reject(err);
      },
    });
  });
};

/**
 * 微信登录
 */
export const wechatApi = {
  login: (code) => {
    return request(API.WECHAT.LOGIN, {
      method: "GET",
      data: { code: code },
      header: {
        "Content-Type": "application/json",
      },
    });
  },
  getQRCodeBase64: (params) => {
    return request(API.WECHAT.QRCODE, {
      method: "POST",
      data: params,
      header: {
        "Content-Type": "application/json",
      },
    });
  },
};

/**
 * 狼人杀房间相关API
 */
export const wolfRoomApi = {
  /**
   * 创建游戏房间
   * @param {Object} roomData - 房间数据
   * @returns {Promise<Object>} 创建结果
   */
  createRoom: (roomData) => {
    return request(API.WOLF.ROOM.CREATE, {
      method: "POST",
      data: roomData,
      header: {
        "Content-Type": "application/json",
      },
    });
  },

  /**
   * 加入游戏房间
   * @param {Object} joinData - 加入数据
   * @returns {Promise<Object>} 加入结果
   */
  joinRoom: (joinData) => {
    return request(API.WOLF.ROOM.JOIN, {
      method: "POST",
      data: joinData,
      header: {
        "Content-Type": "application/json",
      },
    });
  },

  /**
   * 获取房间信息
   * @param {Long} roomId - 房间ID
   * @returns {Promise<Object>} 房间信息
   */
  getRoomInfo: (roomId) => {
    return request(`${API.WOLF.ROOM.INFO}?roomId=${roomId}`);
  },

  /**
   * 通过房间码获取房间信息
   * @param {String} roomCode - 房间码
   * @returns {Promise<Object>} 房间信息
   */
  getRoomInfoByCode: (roomCode) => {
    return request(`${API.WOLF.ROOM.INFO_BY_CODE}?roomCode=${roomCode}`);
  },

  /**
   * 获取房间角色配置
   * @param {Long} roomId - 房间ID
   * @returns {Promise<Array>} 房间角色配置
   */
  getRoomRoles: (roomId) => {
    return request(`${API.WOLF.ROOM.ROOM_ROLES}?roomId=${roomId}`);
  },

  /**
   * 重新开局
   * @param {Long} roomId - 房间ID
   * @returns {Promise<Object>} 重新开局结果
   */
  restartGame: (roomId) => {
    return request(`${API.WOLF.ROOM.RESTART}?roomId=${roomId}`, {
      method: "POST",
    });
  },

  /**
   * 删除房间
   * @param {Long} roomId - 房间ID
   * @returns {Promise<Object>} 删除结果
   */
  deleteRoom: (roomId) => {
    return request(`${API.WOLF.ROOM.DELETE}?roomId=${roomId}`, {
      method: "POST",
    });
  },
};

/**
 * 狼人杀玩家相关API
 */
export const wolfPlayerApi = {
  /**
   * 获取玩家信息
   * @param {Long} roomId - 房间ID
   * @param {String} playerId - 玩家ID
   * @returns {Promise<Object>} 玩家信息
   */
  getPlayerInfo: (roomId, playerId) => {
    return request(
      `${API.WOLF.PLAYER.INFO}?roomId=${roomId}&playerId=${playerId}`
    );
  },

  /**
   * 获取房间所有玩家
   * @param {Long} roomId - 房间ID
   * @returns {Promise<Array>} 玩家列表
   */
  getRoomPlayers: (roomId) => {
    return request(`${API.WOLF.PLAYER.ROOM_PLAYERS}?roomId=${roomId}`);
  },

  /**
   * 获取玩家参与的所有游戏
   * @param {String} playerId - 玩家ID
   * @returns {Promise<Array>} 参与的游戏列表
   */
  getJoinedRooms: (playerId) => {
    return request(`${API.WOLF.PLAYER.JOINED_ROOMS}?playerId=${playerId}`);
  },

  /**
   * 获取或创建玩家用户
   * @param {Object} playerUser - 玩家用户对象
   * @returns {Promise<Object>} 玩家用户信息
   */
  getOrCreateUser: (playerUser) => {
    return request(API.WOLF.PLAYER.GET_OR_CREATE_USER, {
      method: "POST",
      data: playerUser,
      header: {
        "Content-Type": "application/json",
      },
    });
  },
  /**
   *  更新玩家信息
   * @param {*} playerInfo
   * @returns
   */
  updatePlayerInfo: (playerInfo) => {
    return request(API.WOLF.PLAYER.UPDATE, {
      method: "POST",
      data: playerInfo,
      header: {
        "Content-Type": "application/json",
      },
    });
  },
};

/**
 * 狼人杀角色相关API
 */
export const wolfRoleApi = {
  /**
   * 获取所有角色信息
   * @returns {Promise<Array>} 角色列表
   */
  getAllRoles: () => {
    return request(API.WOLF.ROLE.ALL);
  },

  /**
   * 获取特定角色信息
   * @param {String} roleName - 角色名称
   * @returns {Promise<Object>} 角色信息
   */
  getRoleInfo: (roleName) => {
    return request(`${API.WOLF.ROLE.INFO}?roleName=${roleName}`);
  },
};
