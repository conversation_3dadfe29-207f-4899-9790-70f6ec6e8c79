/**
 * 狼人杀助手通用工具类
 */

/**
 * 页面导航
 * @param {string} pageId - 页面标识
 */
export function navigateToPage(pageId) {
  // 页面映射关系
  const pageMap = {
    "home-page": "/pages/index/index",
    "create-game": "/pages/create-game/create-game",
    "game-created": "/pages/game-created/game-created",
    "join-game": "/pages/join-game/join-game",
    "game-room": "/pages/game-room/game-room",
    "game-rules": "/pages/game-rules/game-rules",
    "game-detail": "/pages/game-detail/game-detail",
    "role-intro": "/pages/role-intro/role-intro",
  };

  // 为狼人杀子包修正路径
  if (pageId.startsWith("wolf-")) {
    const wolfPageMap = {
      "wolf-home": "/wolfkiller/pages/index/index",
      "wolf-create": "/wolfkiller/pages/create-game/create-game",
      "wolf-join": "/wolfkiller/pages/join-game/join-game",
      "wolf-room": "/wolfkiller/pages/game-room/game-room",
      "wolf-rules": "/wolfkiller/pages/game-rules/game-rules",
      "wolf-detail": "/wolfkiller/pages/game-detail/game-detail",
      "wolf-role": "/wolfkiller/pages/role-intro/role-intro",
    };

    const url = wolfPageMap[pageId];
    if (url) {
      navigateWithStrategy(url);
      return;
    }
  }

  const url = pageMap[pageId];
  if (url) {
    navigateWithStrategy(url);
  } else {
    console.error(`未找到ID为 ${pageId} 的页面`);
    uni.showToast({
      title: "页面不存在",
      icon: "none",
    });
  }
}

/**
 * 使用统一的导航策略
 * @param {string} url - 目标页面URL
 */
function navigateWithStrategy(url) {
  try {
    // 先尝试navigateTo
    uni.navigateTo({
      url: url,
      fail: (err) => {
        console.error("navigateTo失败:", err);
        // 尝试switchTab
        uni.switchTab({
          url: url,
          fail: (err1) => {
            console.error("switchTab失败:", err1);
            // 最后尝试reLaunch
            uni.reLaunch({
              url: url,
              fail: (err2) => {
                console.error("reLaunch也失败:", err2);
                uni.showToast({
                  title: "页面跳转失败",
                  icon: "none",
                  duration: 2000,
                });
              },
            });
          },
        });
      },
    });
  } catch (error) {
    console.error("页面导航错误", error);
    uni.showToast({
      title: "页面导航失败，请重试",
      icon: "none",
    });
  }
}

/**
 * 倒计时功能
 * @param {Object} options - 倒计时选项
 * @param {string} options.elementId - 显示倒计时的元素ID
 * @param {number} options.seconds - 倒计时时间（秒）
 * @param {function} options.onFinish - 倒计时结束回调
 * @param {function} options.onTick - 每秒回调
 * @returns {Object} - 倒计时控制器
 */
export function startCountdown(options) {
  const { seconds, onFinish, onTick } = options;
  let remainingSeconds = seconds;

  // 更新倒计时显示
  const updateDisplay = () => {
    const minutes = Math.floor(remainingSeconds / 60);
    const secs = remainingSeconds % 60;
    const formattedTime = `${minutes.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;

    if (onTick) {
      onTick(formattedTime, remainingSeconds);
    }

    return formattedTime;
  };

  // 初始更新
  updateDisplay();

  // 设置定时器
  const intervalId = setInterval(() => {
    remainingSeconds--;

    if (remainingSeconds <= 0) {
      clearInterval(intervalId);
      if (onFinish) {
        onFinish();
      }
    }

    updateDisplay();
  }, 1000);

  // 返回控制器
  return {
    getTime: () => updateDisplay(),
    getRemainingSeconds: () => remainingSeconds,
    stop: () => clearInterval(intervalId),
  };
}

/**
 * 生成6位随机房间号
 * @returns {string} - 6位随机数字
 */
export function generateRoomCode() {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本
 * @param {function} successCallback - 成功回调
 */
export function copyToClipboard(text, successCallback) {
  uni.setClipboardData({
    data: text,
    success: function () {
      if (successCallback) {
        successCallback();
      }
      uni.showToast({
        title: "已复制到剪贴板",
        icon: "none",
      });
    },
  });
}

/**
 * 角色配置管理
 */
export const roleConfig = {
  // 默认角色配置
  default: {
    werewolf: 3,
    villager: 4,
    seer: 1,
    witch: 1,
  },
  extra: {
    hunter: 0,
    guard: 0,
    idiot: 0,
    knight: 0,
    elder: 0,
    crow: 0,
  },

  roleMap: {
    WEREWOLF: "狼人",
    VILLAGER: "村民",
    SEER: "预言家",
    WITCH: "女巫",
    HUNTER: "猎人",
    GUARD: "守卫",
    IDIOT: "愚者",
    KNIGHT: "骑士",
    ELDER: "长老",
    CROW: "乌鸦",
  },

  roleImages: {
    WEREWOLF: "/static/images/werewolf.png",
    VILLAGER: "/static/images/villager.png",
    SEER: "/static/images/seer.png",
    WITCH: "/static/images/witch.png",
    HUNTER: "/static/images/hunter.png",
    GUARD: "/static/images/guard.png",
    IDIOT: "/static/images/idiot.png",
    KNIGHT: "/static/images/knight.png",
    ELDER: "/static/images/elder.png",
    CROW: "/static/images/crow.png",
    CUSTOM: "/static/images/custom.png",
  },
};
