<template>
    <view v-if="visible" class="modal-overlay">
        <view class="profile-modal bg-white bg-opacity-95 rounded-xl shadow-lg p-6">
            <view class="text-center mb-6">
                <text class="text-xl font-bold block">设置个人信息</text>
                <text class="text-sm text-gray-600 block mt-2">请设置您的头像和昵称</text>
            </view>

            <view class="mb-6">
                <text class="text-sm font-medium mb-2 block">选择头像</text>
                <button class="avatar-wrapper" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
                    <image class="avatar" :src="avatarUrl"></image>
                </button>
            </view>

            <view class="mb-6">
                <text class="text-sm font-medium mb-2 block">设置昵称</text>
                <form @submit="saveUserProfile">
                    <input type="nickname" class="nickname-input" placeholder="请输入昵称" @blur="onNicknameChange" />
                    <button form-type="submit"
                        class="btn-primary bg-blue-600 hover-bg-blue-700 text-white font-bold  px-4 rounded-lg transition w-full mt-4">
                        确认
                    </button>
                </form>
            </view>
        </view>
    </view>
</template>

<script>
    export default {
        name: "UserProfileModal",
        props: {
            visible: {
                type: Boolean,
                default: false
            },
            playerId: {
                type: String,
                default: ''
            },
            defaultAvatar: {
                type: String,
                default: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'
            }
        },
        data() {
            return {
                avatarUrl: this.defaultAvatar,
                nickname: ''
            }
        },
        watch: {
            // 监听defaultAvatar变化
            defaultAvatar(newVal) {
                this.avatarUrl = newVal;
            }
        },
        methods: {
            // 处理用户选择头像事件
            onChooseAvatar(e) {
                const { avatarUrl } = e.detail;
                this.avatarUrl = avatarUrl;
            },

            // 处理昵称输入变化事件
            onNicknameChange(e) {
                this.nickname = e.detail.value;
            },

            // 保存用户资料
            async saveUserProfile(e) {
                if (e.detail.value.nickname) {
                    this.nickname = e.detail.value.nickname;
                }

                if (!this.nickname) {
                    uni.showToast({
                        title: '请输入昵称',
                        icon: 'none'
                    });
                    return;
                }

                // 向父组件发送事件
                this.$emit('save', {
                    playerId: this.playerId,
                    nickname: this.nickname,
                    avatarUrl: this.avatarUrl
                });
            }
        }
    }
</script>

<style scoped>
    /* 用户信息设置弹窗样式 */
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.6);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 999;
    }

    .profile-modal {
        width: 80%;
        max-width: 600rpx;
    }

    .avatar-wrapper {
        display: flex;
        justify-content: center;
        margin-bottom: 20px;
    }

    .avatar {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        overflow: hidden;
        border: 2px solid #3b82f6;
    }

    .nickname-input {
        background-color: #f3f4f6;
        border-radius: 16rpx;
        width: 100%;
        height: 88rpx;
        line-height: 88rpx;
        box-sizing: border-box;
        color: #4b5563;
        text-align: center;
    }

    .text-gray-600 {
        color: #4b5563;
    }

    .mt-4 {
        margin-top: 16px;
    }

    /* 按钮样式 */
    .bg-blue-600 {
        background-color: #2563eb;
    }

    .hover-bg-blue-700:hover {
        background-color: #1d4ed8;
    }

    .py-3 {
        padding-top: 12px;
        padding-bottom: 12px;
    }

    .px-4 {
        padding-left: 16px;
        padding-right: 16px;
    }

    .text-white {
        color: white;
    }

    .text-center {
        text-align: center;
    }

    .font-bold {
        font-weight: bold;
    }

    .text-xl {
        font-size: 20px;
    }

    .text-sm {
        font-size: 14px;
    }

    .rounded-lg {
        border-radius: 0.5rem;
    }

    .w-full {
        width: 100%;
    }

    .mb-6 {
        margin-bottom: 24px;
    }

    .mb-2 {
        margin-bottom: 8px;
    }

    .block {
        display: block;
    }

    .mt-2 {
        margin-top: 8px;
    }
</style>