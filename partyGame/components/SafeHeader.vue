<template>
    <view>
        <!-- 安全区域头部 -->
        <view class="safe-header" :style="{ paddingTop: statusBarHeight + 'px' }">
            <view class="safe-header-content">
                <view class="flex items-center">
                    <view v-if="showBack" class="back-button" @click="goBack">
                        <text class="fas fa-arrow-left"></text>
                    </view>
                    <text class="page-title">{{ title }}</text>
                </view>
                <slot name="right"></slot>
            </view>
        </view>
        <!-- 占位符，为固定定位的header腾出空间 -->
        <view class="safe-header-placeholder" :style="{ height: (statusBarHeight) + 'px' }"></view>
    </view>
</template>

<script>
    export default {
        name: 'SafeHeader',
        props: {
            title: {
                type: String,
                default: '狼人杀助手'
            },
            showBack: {
                type: Boolean,
                default: true
            },
            backPage: {
                type: String,
                default: ''
            }
        },
        data() {
            return {
                statusBarHeight: 20 // 默认状态栏高度
            }
        },
        created() {
            // 获取状态栏高度
            const app = getApp();
            if (app && app.globalData) {
                this.statusBarHeight = app.globalData.statusBarHeight || 20;
            } else {
                try {
                    const systemInfo = uni.getSystemInfoSync();
                    this.statusBarHeight = systemInfo.statusBarHeight || 20;
                } catch (e) {
                    console.error('获取状态栏高度失败', e);
                }
            }
        },
        methods: {
            goBack() {
                if (this.backPage) {
                    uni.navigateTo({
                        url: this.backPage,
                        fail: () => {
                            uni.navigateBack({
                                delta: 1
                            });
                        }
                    });
                } else {
                    const pages = getCurrentPages()
                    if (pages.length <= 1) {
                        // 已经在栈顶页面，无法返回，直接跳转到首页
                        uni.reLaunch({
                            url: "/wolfkiller/pages/index/index"
                        })
                    } else {
                        // 非栈顶页面，可以正常返回
                        uni.navigateBack()
                    }
                }
            }
        }
    }
</script>

<style scoped>
    /* 组件样式已在全局CSS中定义 */
</style>