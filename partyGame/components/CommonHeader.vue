<template>
    <view class="header-container">
        <view class="status-bar" :style="{height: statusBarHeight + 'px'}"></view>
        <view class="header">
            <view class="header-left" @tap="handleBack" v-if="showBack">
                <text class="fas fa-chevron-left"></text>
            </view>
            <view class="header-title-container">
                <text class="header-title">{{ title }}</text>
            </view>
            <view class="header-right">
                <slot name="right"></slot>
            </view>
        </view>
    </view>
</template>

<script>
    export default {
        name: 'CommonHeader',
        props: {
            title: {
                type: String,
                default: ''
            },
            showBack: {
                type: Boolean,
                default: true
            }
        },
        data() {
            return {
                statusBarHeight: 20
            }
        },
        created() {
            const app = getApp()
            this.statusBarHeight = app.globalData.statusBarHeight || 20
        },
        methods: {
            handleBack() {
                const pages = getCurrentPages()
                if (pages.length <= 1) {
                    // 已经在栈顶页面，无法返回，直接跳转到首页
                    uni.reLaunch({
                        url: "/partyGame/pages/index/index"
                    })
                } else {
                    // 非栈顶页面，可以正常返回
                    uni.navigateBack()
                }
            }
        }
    }
</script>

<style lang="scss" scoped>
    .header-container {
        width: 100%;
    }

    .status-bar {
        width: 100%;
        background-color: #9333ea;
    }

    .header {
        background-color: #9333ea;
        padding: 0 24rpx;
        display: flex;
        align-items: center;
        height: 100rpx;
        position: relative;
    }

    .header-left {
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .header-title-container {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .header-title {
        color: white;
        font-size: 42rpx;
        font-weight: bold;
        text-align: center;
    }

    .header-right {
        width: 48rpx;
    }

    .fas.fa-chevron-left {
        color: white;
        font-size: 48rpx;
    }
</style>