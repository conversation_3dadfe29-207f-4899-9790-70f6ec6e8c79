<script>
	import { wechatApi } from '@/utils/wolfApi.js'
	export default {
		globalData: {
			playerId: 0,
			nickname: '',
			avatarUrl: '',
			statusBarHeight: 0, // 状态栏高度
			navBarHeight: 0 // 导航栏高度
		},
		onLaunch: function () {
			console.log('App Launch')
			// 获取系统信息并设置状态栏高度
			const systemInfo = uni.getSystemInfoSync()
			this.globalData.statusBarHeight = systemInfo.statusBarHeight || 20
			this.globalData.navBarHeight = systemInfo.navBarHeight || 44

			// 加载字体图标
			this.loadFontAwesome()

			// 应用初始化完成
			console.log('应用初始化完成')
		},
		onShow: function () {
			console.log('App Show')
			this.login()
		},
		onHide: function () {
			console.log('App Hide')
		},
		onPageNotFound() {
			console.log('页面不存在，跳转到首页')
			uni.redirectTo({
				url: '/pages/index/index'
			})
		},
		methods: {

			loadFontAwesome() {
				// 在实际项目中，应该使用本地字体图标文件
				// 这里仅作示例，实际开发时应下载字体图标到本地
				// #ifdef H5
				const fontCss = document.createElement('link')
				fontCss.rel = 'stylesheet'
				fontCss.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css'
				document.head.appendChild(fontCss)
				// #endif

				// 小程序环境中通常使用本地图标字体文件
				console.log('字体图标加载完成')
			},
			// 小程序登录
			login() {

				return new Promise((resolve, reject) => {

					if (!this.globalData.playerId) {
						let that = this
						// 获取微信登录code
						uni.login({
							success: (res) => {
								//用code调用后台接口获取用户信息
								let code = res.code
								wechatApi.login(code).then(res => {
									that.globalData.playerId = res.playerId
									that.globalData.nickname = res.nickname
									that.globalData.avatarUrl = res.avatarUrl
									resolve()
								})
							}
						})
					}
				})
			}

		}
	}
</script>

<style>
	/*每个页面公共css */
	@import "./static/css/font-awesome-6.0.0-beta3-all.min.css";
	@import "./static/css/common.css";

	/* 确保字体图标在按钮中垂直居中对齐 */
	.fas,
	.fab,
	.far {
		vertical-align: middle;
	}

	/* uniapp提供的原生组件会自动加上一些默认样式，我们可以在这里重置它们 */
	button {
		margin: 0;
		padding: 0;
		background-color: transparent;
	}

	button::after {
		border: none;
	}

	/* 全局背景和字体设置 */
	page {
		min-height: 100vh;
		font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
	}

	/* uniapp特定样式调整 */
	.uni-input {
		height: 80rpx;
		padding: 0 20rpx;
	}

	/* 修复flex布局在某些小程序中的兼容性问题 */
	.flex {
		display: flex;
	}
</style>