# 聚会游戏 - UniApp 版

这是一个使用 UniApp 框架开发的聚会游戏应用，目前包含了两款游戏："狼人杀助手"和"谁是卧底"，"狼人杀助手"游戏定位于帮助线下狼人杀游戏进行角色分发；"谁是卧底"游戏定位于帮助线下谁是卧底游戏进行身份和词语的分发。

## 功能特性

### "狼人杀助手"游戏是聚会游戏的一种，代码位于当前 uniapp 项目子包 wolfkiller 目录下。

- 创建游戏：设置游戏名称和角色配置
- 生成房间号：创建游戏后生成 6 位随机房间号
- 角色查看：玩家可以查看自己的角色身份
- 游戏规则：了解狼人杀游戏的基本规则和角色介绍
- 历史记录：查看历史游戏记录
- 创建房间后，游戏已开始，房主作为主持人不参与游戏中的角色；其他玩家只要打开分享的房间链接，就可以随机获取到一个身份角色；
- 玩家打开分享的房间链接后，显示“你的身份”页面，页面中显示随机获取的身份；
- 不需要玩家退出功能，不需要房主踢人的功能；重新开局意味着清空已加入房间的所有游戏人员；
- 不需要记录游戏过程中的多轮对话和每轮的结果，不需要投票流程，也不需要跟踪游戏进行中的状态；
- 作为一款辅助线下游戏的工具，只是为游戏人员的分发身份，需要记录房间里游戏人员随机获取到的身份；
- 不需要用 websocket 接口来实时的传输游戏状态信息；

### "谁是卧底"游戏是聚会游戏的一种，代码位于当前 uniapp 项目子包 partyGame 目录下。

- 创建房间后，游戏已开始，同时房主随机获取了一个身份和词语；其他玩家只要打开分享的房间链接，就可以随机获取到一个身份和词语；
- 玩家打开分享的房间链接后，显示“你的身份”页面，页面中显示随机获取的身份和词语；
- 不需要玩家退出功能，不需要房主踢人的功能；重新开局必须走重新创建房间流程；
- 不需要记录游戏过程中的多轮对话和每轮的结果，也不需要投票；
- 需要记录每个房间里所有游戏人员随机获取到的身份和词语；
- 不需要用 websocket 接口来实时的传输游戏状态信息

## 技术栈

- 基于 uni-app 框架开发
- 支持微信小程序、H5 端
- UI：自定义样式，玻璃态设计
- 图标：FontAwesome

## 页面说明

狼人杀助手游戏包含以下页面：

1. 首页 (index)：应用入口，包含主要功能入口和创建的游戏列表
2. 创建游戏 (create-game)：设置游戏名称和角色配置
3. 游戏创建成功 (game-created)：显示房间号和邀请方式
4. 加入游戏 (join-game)：通过房间号或扫码加入游戏
5. 游戏房间 (game-room)：查看自己的角色和游戏信息
6. 游戏规则 (game-rules)：查看游戏基本规则
7. 游戏详情 (game-detail)：查看历史游戏详情
8. 角色介绍 (role-intro)：了解不同角色的技能和玩法技巧

"谁是卧底"游戏包含以下页面：
所有页面在子包 partyGames 目录下

## 安装与运行

1. 安装依赖：

```bash
npm install
```

2. 开发运行：

```bash
npm run dev:h5     # H5版本
npm run dev:mp-weixin  # 微信小程序版本
npm run dev:app-plus   # App版本
```

3. 打包发布：

```bash
npm run build:h5     # H5版本
npm run build:mp-weixin  # 微信小程序版本
npm run build:app-plus   # App版本
```

## 项目结构

```
├── pages              # 页面文件夹
│   ├── index          # 首页
│   ├── create-game    # 创建游戏
│   ├── game-created   # 游戏创建成功
│   ├── join-game      # 加入游戏
│   ├── game-room      # 游戏房间
│   ├── game-rules     # 游戏规则
│   ├── game-detail    # 游戏详情
│   └── role-intro     # 角色介绍
├── static             # 静态资源
│   └── css            # 样式文件
│       └── common.css # 通用样式
├── utils              # 工具类
│   └── game.js        # 游戏相关工具函数
├── App.vue            # 应用入口
├── main.js            # 主入口文件
├── manifest.json      # 配置文件
├── pages.json         # 页面配置
└── uni.scss           # 全局样式变量
```

## 注意事项

1. 游戏支持多设备联网游戏
2. 请在使用前确保已安装所需的 UniApp 开发环境

# 狼人杀助手

## 项目简介

"狼人杀助手"是一款专为线下狼人杀游戏设计的辅助工具，通过小程序形式实现角色分配、身份查看等功能。这款工具能够创建游戏房间、分享房间号以及随机分配角色，使得传统的狼人杀游戏更加方便和公平。

## 功能特点

- **在线角色分配**：无需纸牌，系统自动分配角色
- **身份保密性**：每名玩家只能查看自己的身份
- **房间共享**：通过房间号或二维码邀请好友加入游戏
- **游戏规则查询**：内置详细的游戏规则和角色说明
- **游戏历史记录**：记录过往游戏，方便重新开局

## 页面详情与功能说明

### 1. 首页 (index)

首页是用户进入小程序后看到的第一个页面，提供了狼人杀助手的基本介绍和主要功能入口。

**按钮功能：**

- **来一局**：创建新的狼人杀游戏，跳转至游戏创建页面
- **加入游戏**：加入已有的游戏房间，跳转至游戏加入页面
- **游戏规则**：查看狼人杀游戏规则详情
- **返回首页**：返回到小程序的主页面
- **游戏记录**：显示之前创建过的游戏记录，点击可进入游戏详情页面

### 2. 创建游戏 (create-game)

用户可以在此页面设置游戏参数，包括游戏名称、玩家角色分配等。

**按钮功能：**

- **增加/减少角色数量**：调整各类角色（狼人、村民、预言家、女巫）的数量
- **添加更多角色**：提示更多角色将在未来版本开放
- **开始游戏**：确认设置并创建游戏房间，跳转至游戏创建成功页面

### 3. 游戏创建成功 (game-created)

显示房间号、二维码等邀请方式，并提供 10 分钟倒计时功能。

**按钮功能：**

- **微信分享**：通过微信分享游戏房间信息给好友
- **游戏详情**：查看当前游戏的详细信息
- **删除游戏**：取消当前创建的游戏，返回首页

### 4. 加入游戏 (join-game)

玩家通过输入房间号或扫描二维码加入已创建的游戏。

**按钮功能：**

- **加入游戏**：输入房间号后加入相应游戏
- **扫码加入**：扫描二维码加入游戏
- **返回**：返回首页

### 5. 游戏房间 (game-room)

显示玩家身份信息和房间内所有玩家列表，是游戏进行中最主要的页面。

**按钮功能：**

- **点击身份卡片**：翻转卡片查看/隐藏个人身份，确保其他玩家看不到
- **游戏规则**：查看游戏规则页面
- **返回首页**：离开当前游戏房间，返回首页

### 6. 游戏规则 (game-rules)

详细介绍狼人杀游戏规则，包括基本规则和各角色介绍。

**按钮功能：**

- **查看更多角色介绍**：跳转至角色详细介绍页面
- **返回**：根据来源页面不同，返回到相应的页面（游戏房间或首页）

### 7. 角色介绍 (role-intro)

详细介绍各个角色的能力、技能和游戏技巧。

**按钮功能：**

- **返回**：返回游戏规则页面

### 8. 游戏详情 (game-detail)

显示特定游戏的详细信息，包括游戏时间、参与玩家等。

**按钮功能：**

- **再来一局**：以相同设置创建新游戏
- **删除游戏**：从历史记录中删除此游戏
- **返回**：返回我的游戏页面

## 使用流程

1. **创建游戏**：房主在首页点击"来一局"创建新游戏
2. **设置角色**：选择游戏名称和各类角色数量
3. **邀请玩家**：房主通过微信分享或展示房间号/二维码邀请其他玩家
4. **加入游戏**：其他玩家通过扫码或输入房间号加入游戏
5. **查看身份**：所有玩家进入游戏房间后，点击卡片查看自己的身份
6. **开始游戏**：线下进行狼人杀游戏，玩家可随时查看自己的角色信息

## 注意事项

- 房间号有效期为 10 分钟，超时房间号失效，玩家无法进入房间领取身份
- 请勿在游戏过程中将手机展示给其他玩家
- 建议在使用前熟悉游戏规则
- 游戏中的角色设置会影响游戏平衡性，建议根据玩家人数合理配置

## 未来计划（现在不做）

- 增加更多特殊角色
- 提供游戏语音提示功能
- 加入自定义角色功能
- 支持自定义游戏规则
