<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>谁是卧底 - 线下游戏助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background-color: #f7f7f7;
        }

        .page {
            width: 100%;
            margin: 0 auto;
            background-color: white;
            min-height: 736px;
            height: calc(100vh - 2rem);
            position: relative;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .page>div {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            scrollbar-width: thin;
            scrollbar-color: rgba(107, 114, 128, 0.3) transparent;
        }

        .page>div::-webkit-scrollbar {
            width: 6px;
        }

        .page>div::-webkit-scrollbar-track {
            background: transparent;
        }

        .page>div::-webkit-scrollbar-thumb {
            background-color: rgba(107, 114, 128, 0.3);
            border-radius: 3px;
        }

        .flip-card-front,
        .flip-card-back {
            backface-visibility: hidden;
            transition: transform 0.6s;
        }

        .flipped .flip-card-front {
            transform: rotateY(180deg);
            display: none;
        }

        .flipped .flip-card-back {
            transform: rotateY(0);
            display: flex;
        }

        .flip-card-back {
            transform: rotateY(180deg);
            display: none;
        }

        /* 开关按钮样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked+.slider {
            background-color: #9333ea;
        }

        input:checked+.slider:before {
            transform: translateX(20px);
        }
    </style>
</head>

<body class="container mx-auto p-4 min-h-screen bg-gray-100">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- 欢迎页 -->
        <div class="page p-0 rounded-xl overflow-hidden">
            <div class="bg-gradient-to-b from-blue-500 to-purple-600 min-h-full flex flex-col">
                <div class="flex-grow flex flex-col items-center justify-center text-white p-4 sm:p-6">
                    <img src="https://images.unsplash.com/photo-1522075469751-3a6694fb2f61?w=600&auto=format&fit=crop&q=60"
                        alt="游戏图标" class="w-32 h-32 rounded-full mb-8 object-cover border-4 border-white">
                    <h1 class="text-4xl font-bold mb-3">谁是卧底</h1>
                    <p class="text-lg mb-12 text-center opacity-90">聚会必备，面对面猜词游戏</p>

                    <button class="w-full bg-white text-purple-600 font-bold py-4 rounded-full shadow-lg mb-6">
                        来一局
                    </button>
                    <button class="w-full bg-white bg-opacity-25 text-white font-bold py-4 rounded-full mb-6">
                        游戏规则
                    </button>
                    <button class="w-full bg-white bg-opacity-25 text-white font-bold py-4 rounded-full mb-6">
                        加入游戏
                    </button>
                </div>
            </div>
        </div>

        <!-- 游戏设置页面修改版 -->
        <div class="page p-0 rounded-xl overflow-hidden">
            <div class="bg-white h-full flex flex-col">
                <div class="bg-purple-600 py-6 px-4 text-white flex items-center">
                    <button class="mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                    </button>
                    <h1 class="text-xl font-bold flex-grow text-center pr-8">游戏设置</h1>
                </div>

                <div class="p-4 sm:p-6 flex flex-col h-full">
                    <div class="mb-6">
                        <label class="block text-gray-700 font-bold mb-2" for="playerCount">
                            玩家人数
                        </label>
                        <div class="flex items-center">
                            <button type="button"
                                class="bg-gray-200 px-4 py-2 rounded-l-lg text-gray-700 font-bold text-xl">-</button>
                            <input
                                class="appearance-none bg-gray-100 text-center w-full py-2 px-4 text-gray-700 leading-tight"
                                id="playerCount" type="number" value="6" readonly>
                            <button type="button"
                                class="bg-gray-200 px-4 py-2 rounded-r-lg text-gray-700 font-bold text-xl">+</button>
                        </div>
                    </div>

                    <div class="mb-6">
                        <label class="block text-gray-700 font-bold mb-2" for="undercoverCount">
                            卧底人数
                        </label>
                        <div class="flex items-center">
                            <button type="button"
                                class="bg-gray-200 px-4 py-2 rounded-l-lg text-gray-700 font-bold text-xl">-</button>
                            <input
                                class="appearance-none bg-gray-100 text-center w-full py-2 px-4 text-gray-700 leading-tight"
                                id="undercoverCount" type="number" value="1" readonly>
                            <button type="button"
                                class="bg-gray-200 px-4 py-2 rounded-r-lg text-gray-700 font-bold text-xl">+</button>
                        </div>
                    </div>

                    <div class="mb-6">
                        <label class="block text-gray-700 font-bold mb-2" for="blankCount">
                            白板人数
                        </label>
                        <div class="flex items-center">
                            <button type="button"
                                class="bg-gray-200 px-4 py-2 rounded-l-lg text-gray-700 font-bold text-xl">-</button>
                            <input
                                class="appearance-none bg-gray-100 text-center w-full py-2 px-4 text-gray-700 leading-tight"
                                id="blankCount" type="number" value="0" readonly>
                            <button type="button"
                                class="bg-gray-200 px-4 py-2 rounded-r-lg text-gray-700 font-bold text-xl">+</button>
                        </div>
                    </div>

                    <div class="mb-6">
                        <label class="block text-gray-700 font-bold mb-2">
                            词库选择
                        </label>
                        <div class="grid grid-cols-2 gap-3">
                            <div
                                class="bg-purple-100 border-2 border-purple-500 rounded-lg p-3 flex items-center justify-center text-purple-700 font-medium">
                                <span>经典词库</span>
                            </div>
                            <div
                                class="bg-white border-2 border-gray-200 rounded-lg p-3 flex items-center justify-center text-gray-700 font-medium">
                                <span>情侣专题</span>
                            </div>
                            <div
                                class="bg-white border-2 border-gray-200 rounded-lg p-3 flex items-center justify-center text-gray-700 font-medium">
                                <span>动漫专题</span>
                            </div>
                            <div
                                class="bg-white border-2 border-gray-200 rounded-lg p-3 flex items-center justify-center text-gray-700 font-medium">
                                <span>电影专题</span>
                            </div>
                            <div
                                class="bg-white border-2 border-gray-200 rounded-lg p-3 flex items-center justify-center text-gray-700 font-medium">
                                <span>自定义词库</span>
                            </div>
                        </div>
                    </div>

                    <!-- 词语选择部分 -->
                    <div class="mb-6">
                        <label class="block text-gray-700 font-bold mb-2">
                            词语选择
                        </label>
                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center mb-4">
                                <label class="switch mr-3">
                                    <input type="checkbox" checked id="randomWordToggle">
                                    <span class="slider"></span>
                                </label>
                                <span class="text-gray-700">随机词语</span>
                            </div>

                            <div class="border-t border-gray-200 pt-4" id="specifyWordArea" style="display: none;">
                                <label class="block text-gray-700 font-medium mb-2">指定词语</label>

                                <!-- 备选词语列表 -->
                                <div class="mb-4">
                                    <p class="text-sm text-gray-600 mb-2">备选词语：</p>
                                    <div class="grid grid-cols-2 gap-2">
                                        <div class="bg-gray-100 p-2 rounded text-sm cursor-pointer hover:bg-purple-100">
                                            苹果 - 梨</div>
                                        <div class="bg-gray-100 p-2 rounded text-sm cursor-pointer hover:bg-purple-100">
                                            西瓜 - 哈密瓜</div>
                                        <div class="bg-gray-100 p-2 rounded text-sm cursor-pointer hover:bg-purple-100">
                                            篮球 - 足球</div>
                                        <div class="bg-gray-100 p-2 rounded text-sm cursor-pointer hover:bg-purple-100">
                                            手机 - 电脑</div>
                                    </div>
                                </div>

                                <div class="grid grid-cols-2 gap-3 mb-3">
                                    <div>
                                        <label class="block text-gray-700 text-sm mb-1">平民词</label>
                                        <input
                                            class="appearance-none border border-gray-300 rounded-lg w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:border-purple-500"
                                            type="text" placeholder="输入平民词语">
                                    </div>
                                    <div>
                                        <label class="block text-gray-700 text-sm mb-1">卧底词</label>
                                        <input
                                            class="appearance-none border border-gray-300 rounded-lg w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:border-purple-500"
                                            type="text" placeholder="输入卧底词语">
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500">*启用随机词语时，指定词语将不生效</p>
                            </div>
                        </div>
                    </div>

                    <div class="mt-auto">
                        <button type="button"
                            class="w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-4 rounded-full shadow-lg transition duration-200">
                            开始游戏
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 简化的分享链接页面 -->
        <div class="page p-0 rounded-xl overflow-hidden">
            <div class="bg-white h-full flex flex-col">
                <div class="bg-purple-600 py-6 px-4 text-white flex items-center">
                    <h1 class="text-xl font-bold flex-grow text-center">分享游戏</h1>
                </div>

                <div class="p-4 sm:p-6 flex flex-col h-full">
                    <div class="bg-blue-50 p-4 rounded-lg mb-6 text-center">
                        <h2 class="text-xl font-bold text-blue-800 mb-2">游戏已准备就绪</h2>
                        <p class="text-gray-700">请将二维码分享给其他玩家</p>
                        <p class="text-gray-700 text-sm mt-2">玩家扫描二维码后将直接获得身份和词语</p>
                    </div>

                    <div class="bg-yellow-50 p-4 rounded-lg mb-6">
                        <h3 class="font-bold text-yellow-800 mb-2">游戏信息</h3>
                        <ul class="text-sm text-yellow-700 space-y-2">
                            <li class="flex justify-between">
                                <span>房间号：</span>
                                <span class="font-medium">273851</span>
                            </li>
                            <li class="flex justify-between">
                                <span>总人数：</span>
                                <span class="font-medium">6人</span>
                            </li>
                            <li class="flex justify-between">
                                <span>卧底：</span>
                                <span class="font-medium">1人</span>
                            </li>
                            <li class="flex justify-between">
                                <span>白板：</span>
                                <span class="font-medium">1人</span>
                            </li>
                            <li class="flex justify-between">
                                <span>词库：</span>
                                <span class="font-medium">经典词库</span>
                            </li>
                        </ul>
                    </div>

                    <div class="flex flex-col items-center justify-center mb-8">
                        <div
                            class="w-48 h-48 bg-white p-2 border border-gray-200 rounded-lg mb-3 flex items-center justify-center">
                            <svg viewBox="0 0 100 100" class="w-full h-full">
                                <!-- 这里放二维码SVG，为了简化，这里只放一个占位图 -->
                                <rect x="10" y="10" width="80" height="80" fill="#4F46E5" opacity="0.1"></rect>
                                <text x="50" y="55" text-anchor="middle" fill="#4F46E5" font-size="10">游戏二维码</text>
                            </svg>
                        </div>
                        <p class="text-sm text-gray-600">扫描二维码加入游戏</p>
                    </div>

                    <div class="mt-auto">
                        <button class="w-full bg-green-600 text-white font-bold py-3 rounded-full shadow mb-3">
                            转发到微信
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 简化的玩家查看身份页面 (直接获取身份) -->
        <div class="page p-0 rounded-xl overflow-hidden">
            <div class="bg-white h-full flex flex-col overflow-y-auto">
                <div class="bg-purple-600 py-6 px-4 text-white flex items-center sticky top-0 z-10">
                    <h1 class="text-xl font-bold flex-grow text-center">你的身份</h1>
                </div>

                <div class="p-4 sm:p-6 flex flex-col">
                    <div class="bg-blue-50 p-4 rounded-lg mb-4 text-center">
                        <h2 class="text-lg font-bold text-blue-800 mb-1">游戏已开始</h2>
                        <p class="text-sm text-gray-700">点击卡片查看你的身份，不要让他人看到</p>
                        <div class="mt-2 text-purple-700 font-medium">房间号：273851</div>
                    </div>

                    <div class="flex items-center justify-center my-4">
                        <div class="relative w-full max-w-sm">
                            <div
                                class="flip-card-front bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl shadow-lg p-8 flex flex-col items-center justify-center min-h-[300px]">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-24 w-24 text-white mb-6" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                                        d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <h3 class="text-2xl font-bold text-white mb-2">点击查看身份</h3>
                                <p class="text-white text-center opacity-80">仅限本人查看</p>
                            </div>

                            <div
                                class="flip-card-back bg-white border-2 border-purple-500 rounded-xl shadow-lg p-8 flex flex-col items-center justify-center min-h-[300px]">
                                <div class="bg-red-100 px-4 py-2 rounded-full text-red-700 font-medium mb-6">卧底</div>
                                <h3 class="text-3xl font-bold text-gray-800 mb-6">香皂</h3>
                                <p class="text-gray-600 text-center mb-8">请根据这个词语进行描述，但不要直接说出这个词</p>
                                <div class="text-sm text-gray-500">点击卡片隐藏信息</div>
                            </div>
                        </div>
                    </div>

                    <div class="pt-6">
                        <div class="bg-yellow-50 p-4 rounded-lg mb-6">
                            <h3 class="font-bold text-yellow-800 mb-2 text-center">游戏提示</h3>
                            <ul class="text-sm text-yellow-700 space-y-2">
                                <li>• 根据你的词语描述，但不要直接说出词语</li>
                                <li>• 仔细听其他玩家的描述，找出可疑的人</li>
                                <li>• 你可以随时点击卡片查看自己的词语</li>
                                <li>• 游戏结束前不要透露自己的身份</li>
                            </ul>
                        </div>

                        <button
                            class="w-full bg-purple-600 text-white font-bold py-4 rounded-full shadow transition duration-200 mb-6">
                            查看游戏规则
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 游戏规则页面 -->
        <div class="page p-0 rounded-xl overflow-hidden">
            <div class="bg-white h-full flex flex-col">
                <div class="bg-purple-600 py-6 px-4 text-white flex items-center">
                    <button class="mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                    </button>
                    <h1 class="text-xl font-bold flex-grow text-center pr-8">游戏规则</h1>
                </div>

                <div class="p-4 sm:p-6 flex flex-col h-full overflow-y-auto">
                    <div class="bg-blue-50 p-4 rounded-lg mb-6">
                        <h2 class="text-lg font-bold text-blue-800 mb-2">什么是谁是卧底？</h2>
                        <p class="text-sm text-gray-700">
                            谁是卧底是一款语言类推理游戏，平民拿到同一个词语，卧底拿到与之相关但不同的词语。通过玩家轮流描述，相互推理找出卧底。
                        </p>
                    </div>

                    <div class="mb-6">
                        <h3 class="font-bold text-gray-800 mb-3">基本规则</h3>
                        <ul class="text-sm text-gray-700 space-y-3 list-disc pl-5">
                            <li>游戏开始前，确定玩家人数和卧底人数</li>
                            <li>每位玩家会分配到一个词语，大部分人（平民）拿到相同词语，卧底拿到不同但相关的词语</li>
                            <li>如有白板角色，他们不会看到任何词语</li>
                            <li>玩家轮流描述自己拿到的词语，不能直接说出词语本身</li>
                            <li>每轮描述后，所有玩家投票选出怀疑的卧底</li>
                            <li>票数最高的玩家出局，并公布身份</li>
                            <li>如果所有卧底都被找出，平民获胜；如果卧底人数大于或等于平民，卧底获胜</li>
                        </ul>
                    </div>

                    <div class="mb-6">
                        <h3 class="font-bold text-gray-800 mb-3">词语示例</h3>
                        <div class="bg-gray-50 p-3 rounded-lg">
                            <div class="flex justify-between mb-2">
                                <span class="font-medium text-gray-700">平民词语：</span>
                                <span class="text-blue-600">牙刷</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium text-gray-700">卧底词语：</span>
                                <span class="text-red-600">牙膏</span>
                            </div>
                        </div>
                    </div>

                    <div class="mb-6">
                        <h3 class="font-bold text-gray-800 mb-3">游戏技巧</h3>
                        <ul class="text-sm text-gray-700 space-y-2 list-disc pl-5">
                            <li>平民：描述要具体但不要太明显，帮助其他平民识别你，同时不要让卧底察觉</li>
                            <li>卧底：仔细听平民描述，尽量模仿平民的描述方式，避免被识别</li>
                            <li>记住其他玩家的描述，寻找不合理或可疑的地方</li>
                            <li>通过肢体语言和表情观察其他玩家的反应</li>
                        </ul>
                    </div>

                    <div class="mt-auto">
                        <button
                            class="w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-4 rounded-full shadow-lg transition duration-200">
                            返回游戏
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加在页面底部的脚本 -->
    <script>
        document.getElementById('randomWordToggle').addEventListener('change', function () {
            const specifyWordArea = document.getElementById('specifyWordArea');
            specifyWordArea.style.display = this.checked ? 'none' : 'block';
        });
    </script>
</body>

</html>