<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D骰子游戏</title>
    <style>
        body {
            margin: 0;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            height: 100vh;
            font-family: Arial, sans-serif;
            background: linear-gradient(to bottom, #87CEFA, #1E90FF);
        }

        #gameContainer {
            position: relative;
            flex-grow: 1;
        }

        #rollButton {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 12px 24px;
            font-size: 18px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            z-index: 100;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        #rollButton:hover {
            background-color: #45a049;
        }

        #rollButton:active {
            transform: translateX(-50%) scale(0.98);
        }

        #diceValue {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 10px 20px;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 5px;
            font-size: 18px;
            font-weight: bold;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            z-index: 100;
        }

        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 24px;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
        }
    </style>
</head>

<body>
    <div id="gameContainer">
        <div id="loading">加载中，请稍候...</div>
        <div id="diceValue">准备掷骰子</div>
        <button id="rollButton">掷骰子</button>
    </div>

    <script type="importmap">
    {
        "imports": {
            "three": "https://unpkg.com/three@0.160.0/build/three.module.js",
            "three/addons/": "https://unpkg.com/three@0.160.0/examples/jsm/"
        }
    }
    </script>

    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';

        // 全局变量
        let scene, camera, renderer, world, dice, bowl;
        let isRolling = false;
        const diceBodys = [];
        let lastTime;
        const diceValues = [];
        let controls;
        let diceMaterial, bowlMaterial; // 添加材质作为全局变量
        let oscillationCounter = 0; // 用于检测骰子摇摆次数
        let lastAngularVelocitySign = null; // 用于检测角速度方向变化

        // 初始化函数
        async function init() {
            try {
                // 创建场景
                scene = new THREE.Scene();
                scene.background = new THREE.Color(0x87CEEB);

                // 创建相机
                camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 0.1, 1000);
                camera.position.set(0, 15, 25);
                camera.lookAt(0, 0, 0);

                // 创建渲染器
                renderer = new THREE.WebGLRenderer({ antialias: true });
                renderer.setSize(window.innerWidth, window.innerHeight);
                renderer.shadowMap.enabled = true;
                renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                document.getElementById('gameContainer').appendChild(renderer.domElement);

                // 添加轨道控制器
                controls = new OrbitControls(camera, renderer.domElement);
                controls.target.set(0, 0, 0);
                controls.update();
                controls.enablePan = false;
                controls.minDistance = 10;
                controls.maxDistance = 30;

                // 添加灯光
                const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
                scene.add(ambientLight);

                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.9);
                directionalLight.position.set(5, 15, 5);
                directionalLight.castShadow = true;

                // 优化阴影设置
                directionalLight.shadow.camera.near = 1;
                directionalLight.shadow.camera.far = 30;
                directionalLight.shadow.camera.left = -15;
                directionalLight.shadow.camera.right = 15;
                directionalLight.shadow.camera.top = 15;
                directionalLight.shadow.camera.bottom = -15;
                directionalLight.shadow.mapSize.width = 2048;
                directionalLight.shadow.mapSize.height = 2048;
                directionalLight.shadow.bias = -0.001;
                directionalLight.shadow.normalBias = 0.05;
                scene.add(directionalLight);

                // 添加辅助灯光改善阴影质量
                const fillLight = new THREE.DirectionalLight(0xffffff, 0.3);
                fillLight.position.set(-5, 10, -5);
                fillLight.castShadow = false;
                scene.add(fillLight);

                // 加载 Cannon.js
                await loadScript('https://cdn.jsdelivr.net/npm/cannon@0.6.2/build/cannon.min.js');

                // 初始化物理世界
                world = new CANNON.World();
                world.gravity.set(0, -98.1, 0); // 标准重力加速度
                world.broadphase = new CANNON.NaiveBroadphase();
                world.solver.iterations = 50; // 大幅增加迭代次数，提高物理精度
                world.defaultContactMaterial.friction = 0.8; // 增加摩擦力
                world.defaultContactMaterial.restitution = 0.3; // 适当的弹性

                // 创建骰子和碗之间的接触材质
                diceMaterial = new CANNON.Material('dice');
                bowlMaterial = new CANNON.Material('bowl');

                // 设置骰子和碗之间的接触参数
                const dice_bowl_cm = new CANNON.ContactMaterial(
                    diceMaterial,
                    bowlMaterial,
                    {
                        friction: 0.8,       // 增加摩擦系数
                        restitution: 0.3,    // 适当的弹性系数
                        contactEquationStiffness: 1e8, // 增加接触刚度
                        contactEquationRelaxation: 3,   // 降低松弛度
                        frictionEquationStiffness: 1e8, // 增加摩擦刚度
                        frictionEquationRegularizationTime: 3 // 降低正则化时间
                    }
                );
                world.addContactMaterial(dice_bowl_cm);

                // 创建碗
                createBowl();

                // 创建骰子
                createDice();

                // 添加窗口大小变化的监听
                window.addEventListener('resize', onWindowResize);

                // 隐藏加载提示
                document.getElementById('loading').style.display = 'none';

                lastTime = performance.now();

                // 开始动画循环
                animate();
            } catch (error) {
                console.error('初始化错误:', error);
                document.getElementById('loading').textContent = '加载失败，请刷新页面重试';
            }
        }

        // 辅助函数：加载外部脚本
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = reject;
                document.body.appendChild(script);
            });
        }

        // 等待页面加载完成后初始化
        window.addEventListener('DOMContentLoaded', () => {
            init();

            // 监听按钮点击事件
            document.getElementById('rollButton').addEventListener('click', () => {
                // 无论当前状态如何，都重新掷骰子
                rollDice();
            });
        });

        // 创建木纹纹理
        function createWoodTexture() {
            const canvas = document.createElement('canvas');
            canvas.width = 512;
            canvas.height = 512;
            const context = canvas.getContext('2d');

            // 填充底色 - 使用更淡的木色
            context.fillStyle = '#D2B48C'; // 使用淡棕褐色
            context.fillRect(0, 0, canvas.width, canvas.height);

            // 添加木纹
            for (let i = 0; i < 60; i++) {
                const x = Math.random() * canvas.width;
                const y = 0;
                const width = 2 + Math.random() * 8;
                const height = canvas.height;

                context.beginPath();
                context.moveTo(x, y);
                context.lineTo(x + width, y);
                context.lineTo(x + width, y + height);
                context.lineTo(x, y + height);
                context.closePath();

                // 随机木纹颜色 - 使用更淡的色调
                const color = Math.floor(180 + Math.random() * 40);
                context.fillStyle = `rgb(${color}, ${color - 20}, ${color - 40})`;
                context.globalAlpha = 0.4; // 降低不透明度使纹理更微妙
                context.fill();
            }

            const texture = new THREE.CanvasTexture(canvas);
            texture.wrapS = THREE.RepeatWrapping;
            texture.wrapT = THREE.RepeatWrapping;
            texture.repeat.set(4, 4);

            return texture;
        }

        // CSG库的简单实现，用于执行布尔运算
        const CSG = {
            fromMesh: function (mesh) {
                return {
                    mesh: mesh.clone()
                };
            },
            subtract: function (other) {
                return this;
            },
            union: function (other) {
                return this;
            },
            toMesh: function (matrix, material) {
                const result = this.mesh;
                result.material = material;
                return result;
            }
        };

        // 创建碗
        function createBowl() {
            // 创建木质桌面的材质
            const tableVisualMaterial = new THREE.MeshStandardMaterial({
                color: 0xD2B48C,  // 淡棕褐色
                roughness: 0.6,    // 适当的粗糙度
                metalness: 0.1,    // 降低金属感
                side: THREE.DoubleSide,
                map: createWoodTexture()  // 添加木纹纹理
            });

            // 桌面的大小参数
            const tableWidth = 30;
            const tableDepth = 30;
            const tableThickness = 1;
            const tableHeight = 0;  // 桌面高度位置

            // 创建桌面（使用盒子几何体）
            const tableGeometry = new THREE.BoxGeometry(tableWidth, tableThickness, tableDepth);
            const table = new THREE.Mesh(tableGeometry, tableVisualMaterial);
            table.position.y = tableHeight - tableThickness / 2;

            // 创建桌子边缘（四个）
            const edgeHeight = 2;
            const edgeThickness = 1;

            // 前边缘
            const frontEdgeGeometry = new THREE.BoxGeometry(tableWidth, edgeHeight, edgeThickness);
            const frontEdge = new THREE.Mesh(frontEdgeGeometry, tableVisualMaterial);
            frontEdge.position.set(0, tableHeight - tableThickness / 2 + edgeHeight / 2, tableDepth / 2 + edgeThickness / 2);

            // 后边缘
            const backEdgeGeometry = new THREE.BoxGeometry(tableWidth, edgeHeight, edgeThickness);
            const backEdge = new THREE.Mesh(backEdgeGeometry, tableVisualMaterial);
            backEdge.position.set(0, tableHeight - tableThickness / 2 + edgeHeight / 2, -tableDepth / 2 - edgeThickness / 2);

            // 左边缘
            const leftEdgeGeometry = new THREE.BoxGeometry(edgeThickness, edgeHeight, tableDepth + 2 * edgeThickness);
            const leftEdge = new THREE.Mesh(leftEdgeGeometry, tableVisualMaterial);
            leftEdge.position.set(-tableWidth / 2 - edgeThickness / 2, tableHeight - tableThickness / 2 + edgeHeight / 2, 0);

            // 右边缘
            const rightEdgeGeometry = new THREE.BoxGeometry(edgeThickness, edgeHeight, tableDepth + 2 * edgeThickness);
            const rightEdge = new THREE.Mesh(rightEdgeGeometry, tableVisualMaterial);
            rightEdge.position.set(tableWidth / 2 + edgeThickness / 2, tableHeight - tableThickness / 2 + edgeHeight / 2, 0);

            // 将所有部分组合在一起
            bowl = new THREE.Group();
            bowl.add(table);
            bowl.add(frontEdge);
            bowl.add(backEdge);
            bowl.add(leftEdge);
            bowl.add(rightEdge);

            // 设置阴影
            table.receiveShadow = true;
            frontEdge.castShadow = true;
            frontEdge.receiveShadow = true;
            backEdge.castShadow = true;
            backEdge.receiveShadow = true;
            leftEdge.castShadow = true;
            leftEdge.receiveShadow = true;
            rightEdge.castShadow = true;
            rightEdge.receiveShadow = true;

            scene.add(bowl);

            // 创建桌面的物理形状
            const tableBody = new CANNON.Body({
                mass: 0, // 质量为0，表示静态物体
                material: bowlMaterial // 使用全局定义的材质
            });

            // 添加桌面的物理形状
            // 1. 桌面（平面）
            const tableShape = new CANNON.Box(new CANNON.Vec3(tableWidth / 2, tableThickness / 2, tableDepth / 2));
            tableBody.addShape(tableShape, new CANNON.Vec3(0, tableHeight - tableThickness / 2, 0));

            // 2. 四个边缘（盒子）
            // 前边缘
            const frontEdgeShape = new CANNON.Box(new CANNON.Vec3(tableWidth / 2, edgeHeight / 2, edgeThickness / 2));
            tableBody.addShape(frontEdgeShape, new CANNON.Vec3(0, tableHeight - tableThickness / 2 + edgeHeight / 2, tableDepth / 2 + edgeThickness / 2));

            // 后边缘
            const backEdgeShape = new CANNON.Box(new CANNON.Vec3(tableWidth / 2, edgeHeight / 2, edgeThickness / 2));
            tableBody.addShape(backEdgeShape, new CANNON.Vec3(0, tableHeight - tableThickness / 2 + edgeHeight / 2, -tableDepth / 2 - edgeThickness / 2));

            // 左边缘
            const leftEdgeShape = new CANNON.Box(new CANNON.Vec3(edgeThickness / 2, edgeHeight / 2, (tableDepth + 2 * edgeThickness) / 2));
            tableBody.addShape(leftEdgeShape, new CANNON.Vec3(-tableWidth / 2 - edgeThickness / 2, tableHeight - tableThickness / 2 + edgeHeight / 2, 0));

            // 右边缘
            const rightEdgeShape = new CANNON.Box(new CANNON.Vec3(edgeThickness / 2, edgeHeight / 2, (tableDepth + 2 * edgeThickness) / 2));
            tableBody.addShape(rightEdgeShape, new CANNON.Vec3(tableWidth / 2 + edgeThickness / 2, tableHeight - tableThickness / 2 + edgeHeight / 2, 0));

            // 设置桌面的位置
            tableBody.position.set(0, 0, 0);
            world.addBody(tableBody);
        }

        // 创建骰子
        function createDice() {
            const diceSize = 1.5;
            const radius = 0.2; // 圆角半径

            // 创建骰子基础材质 - 纯白色磨砂质感
            const material = new THREE.MeshStandardMaterial({
                color: 0xffffff,
                roughness: 0.3,
                metalness: 0.1,
                side: THREE.DoubleSide
            });

            // 使用BoxGeometry创建立方体
            const geometry = new THREE.BoxGeometry(diceSize, diceSize, diceSize);

            // 创建骰子网格
            dice = new THREE.Mesh(geometry, material);
            dice.castShadow = true;
            dice.receiveShadow = true; // 使骰子接收阴影
            dice.position.set(0, 10, 0);

            scene.add(dice);

            // 添加骰子点数
            createDiceDots(dice);

            // 创建骰子物理形状
            const diceBody = new CANNON.Body({
                mass: 300.0, // 显著增加质量，使重力效应更明显
                material: diceMaterial,
                linearDamping: 0.3,    // 增加线性阻尼
                angularDamping: 0.3,   // 增加角度阻尼
                allowSleep: true,
                sleepSpeedLimit: 0.2,  // 提高休眠速度限制
                sleepTimeLimit: 0.1    // 降低休眠时间
            });

            // 主体形状 - 使用更精确的立方体
            const mainShape = new CANNON.Box(new CANNON.Vec3(diceSize / 2, diceSize / 2, diceSize / 2));
            diceBody.addShape(mainShape);

            // 移除所有球形和圆柱体形状，只保留基本立方体
            // 这样可以确保重心始终在中心，且碰撞更准确

            // 设置更合理的惯性张量
            const inertia = diceBody.inertia;
            const scale = 1.0; // 不再放大惯性张量
            diceBody.inertia.set(inertia.x * scale, inertia.y * scale, inertia.z * scale);
            diceBody.updateMassProperties();

            // 将骰子放在空中
            diceBody.position.set(0, 10, 0);
            diceBody.quaternion.setFromAxisAngle(
                new CANNON.Vec3(Math.random(), Math.random(), Math.random()),
                Math.random() * Math.PI * 2
            );

            world.addBody(diceBody);
            diceBodys.push(diceBody);
        }

        // 创建骰子点数
        function createDiceDots(dice) {
            // 点的基本参数
            const dotRadius = 0.15; // 增加点的半径
            const dotDepth = 0.12; // 增加点的深度
            const dotSegments = 36; // 增加点的分段数以使其更圆滑

            // 点的配置（位置和颜色）
            const dotConfigs = [
                { // 1面（正面）
                    positions: [[0, 0]],
                    color: 0xff0000 // 红色
                },
                { // 2面（右面）
                    positions: [[-0.3, -0.3], [0.3, 0.3]],
                    color: 0x000000
                },
                { // 3面（上面）
                    positions: [[-0.3, -0.3], [0, 0], [0.3, 0.3]],
                    color: 0x000000
                },
                { // 4面（左面）
                    positions: [[-0.3, -0.3], [-0.3, 0.3], [0.3, -0.3], [0.3, 0.3]],
                    color: 0x000000
                },
                { // 5面（下面）
                    positions: [[-0.3, -0.3], [-0.3, 0.3], [0, 0], [0.3, -0.3], [0.3, 0.3]],
                    color: 0x000000
                },
                { // 6面（背面）
                    positions: [[-0.3, -0.3], [-0.3, 0], [-0.3, 0.3], [0.3, -0.3], [0.3, 0], [0.3, 0.3]],
                    color: 0xff0000 // 红色
                }
            ];

            // 为每个面添加凹陷的点
            const faces = [
                { dir: [0, 0, 1], up: [0, 1, 0] },    // 正面 1
                { dir: [1, 0, 0], up: [0, 1, 0] },    // 右面 2
                { dir: [0, 1, 0], up: [0, 0, -1] },   // 上面 3
                { dir: [-1, 0, 0], up: [0, 1, 0] },   // 左面 4
                { dir: [0, -1, 0], up: [0, 0, 1] },   // 下面 5
                { dir: [0, 0, -1], up: [0, 1, 0] }    // 背面 6
            ];

            faces.forEach((face, i) => {
                const config = dotConfigs[i];

                config.positions.forEach(pos => {
                    // 创建凹陷效果
                    // 使用圆柱体作为凹陷的基础
                    const dotGeometry = new THREE.CylinderGeometry(
                        dotRadius,           // 顶部半径
                        dotRadius,           // 底部半径
                        dotDepth,            // 高度
                        dotSegments,         // 分段数
                        1,                   // 高度分段
                        false                // 是否开放端
                    );

                    // 创建点的材质（深色哑光）
                    const dotMaterial = new THREE.MeshStandardMaterial({
                        color: config.color,
                        roughness: 0.9,
                        metalness: 0.0
                    });

                    const dot = new THREE.Mesh(dotGeometry, dotMaterial);
                    dot.castShadow = false; // 点不投射阴影
                    dot.receiveShadow = false; // 点不接收阴影

                    // 设置点的位置和方向
                    const dirVec = new THREE.Vector3(...face.dir);
                    const upVec = new THREE.Vector3(...face.up);
                    const rightVec = new THREE.Vector3().crossVectors(dirVec, upVec);

                    // 将点放置在面上并使其凹陷
                    // 调整位置使点凹陷而不是凸起
                    dot.position.copy(dirVec.multiplyScalar(0.75 - dotDepth / 2));
                    dot.position.add(upVec.multiplyScalar(pos[1]));
                    dot.position.add(rightVec.multiplyScalar(pos[0]));

                    // 调整点的旋转，使其垂直于骰子表面
                    dot.lookAt(dot.position.clone().add(dirVec));
                    dot.rotateX(Math.PI / 2);

                    // 添加到骰子
                    dice.add(dot);
                });
            });
        }

        // 掷骰子函数
        function rollDice() {
            // 重置标志和显示
            isRolling = true;
            document.getElementById('diceValue').textContent = "骰子滚动中...";

            // 清除任何现有的计时器
            if (window.checkDiceTimer) {
                clearTimeout(window.checkDiceTimer);
                window.checkDiceTimer = null;
            }
            if (window.showResultTimer) {
                clearTimeout(window.showResultTimer);
                window.showResultTimer = null;
            }
            if (window.forceStableTimer) {
                clearTimeout(window.forceStableTimer);
                window.forceStableTimer = null;
            }

            // 重置骰子状态
            diceBodys.forEach((diceBody, i) => {
                // 获取边缘位置
                const edgePosition = getRandomEdgePosition();

                // 设置初始位置 - 在桌面边缘上方
                diceBody.position.set(edgePosition.x, 12, edgePosition.z);

                // 重置物理状态
                diceBody.velocity.set(0, 0, 0);
                diceBody.angularVelocity.set(0, 0, 0);
                diceBody.force.set(0, 0, 0);
                diceBody.torque.set(0, 0, 0);
                diceBody.wakeUp();

                // 计算从边缘到中心的方向向量
                const directionToCenter = new CANNON.Vec3(-edgePosition.x, 0, -edgePosition.z);
                directionToCenter.normalize();

                // 设置初始速度 - 总是朝向桌面中心
                const speed = 15 + Math.random() * 10;
                const randomAngleOffset = (Math.random() - 0.5) * Math.PI / 4;

                // 应用随机角度偏移
                const cosOffset = Math.cos(randomAngleOffset);
                const sinOffset = Math.sin(randomAngleOffset);
                const adjustedX = directionToCenter.x * cosOffset - directionToCenter.z * sinOffset;
                const adjustedZ = directionToCenter.x * sinOffset + directionToCenter.z * cosOffset;

                // 设置三维速度向量
                diceBody.velocity.set(
                    adjustedX * speed,
                    -3 - Math.random() * 2,
                    adjustedZ * speed
                );

                // 设置旋转
                diceBody.angularVelocity.set(
                    (Math.random() - 0.5) * 30,
                    (Math.random() - 0.5) * 30,
                    (Math.random() - 0.5) * 30
                );
            });

            // 开始监测骰子状态
            startDiceMonitoring();
        }

        // 开始监测骰子状态
        function startDiceMonitoring() {
            // 存储上一帧的位置和旋转，用于比较
            let lastPosition = null;
            let lastRotation = null;
            let stableFrameCount = 0;
            let monitoringStartTime = performance.now();

            // 定义监测函数
            function monitorDice() {
                const body = diceBodys[0];
                if (!body) return;

                // 获取当前位置和旋转
                const currentPosition = new CANNON.Vec3().copy(body.position);
                const currentRotation = new CANNON.Quaternion().copy(body.quaternion);

                // 计算速度和角速度的平方和
                const speedSquared = body.velocity.lengthSquared();
                const angularSpeedSquared = body.angularVelocity.lengthSquared();

                // 如果骰子几乎静止
                if (speedSquared < 0.001 && angularSpeedSquared < 0.001) {
                    // 如果有上一帧的数据，比较变化
                    if (lastPosition && lastRotation) {
                        // 计算位置和旋转的变化量
                        const positionDiff = currentPosition.distanceTo(lastPosition);
                        const rotationDiff = Math.abs(1 - currentRotation.dot(lastRotation));

                        // 使用非常严格的阈值判断是否完全静止
                        if (positionDiff < 0.0001 && rotationDiff < 0.0001) {
                            stableFrameCount++;

                            // 连续5帧静止，认为骰子已完全停止
                            if (stableFrameCount >= 5) {
                                // 检查骰子是否平躺
                                const isFaceUp = checkIfDiceFaceUp();

                                // 立即显示结果
                                if (isFaceUp) {
                                    const diceValue = determineDiceValue();
                                    document.getElementById('diceValue').textContent = `骰子点数: ${diceValue}`;
                                } else {
                                    document.getElementById('diceValue').textContent = "无法读出点数";
                                }

                                // 完全停止骰子的所有运动
                                body.velocity.set(0, 0, 0);
                                body.angularVelocity.set(0, 0, 0);
                                body.force.set(0, 0, 0);
                                body.torque.set(0, 0, 0);
                                body.sleep(); // 让物理引擎将其标记为休眠

                                isRolling = false;
                                return; // 停止监测
                            }
                        } else {
                            // 如果有任何移动，重置计数
                            stableFrameCount = 0;
                        }
                    }
                } else {
                    // 骰子仍在明显移动，重置计数
                    stableFrameCount = 0;
                }

                // 更新上一帧数据
                lastPosition = currentPosition;
                lastRotation = currentRotation;

                // 如果监测时间超过10秒，强制结束
                if (performance.now() - monitoringStartTime > 10000) {
                    // 检查骰子是否平躺
                    const isFaceUp = checkIfDiceFaceUp();

                    // 立即显示结果
                    if (isFaceUp) {
                        const diceValue = determineDiceValue();
                        document.getElementById('diceValue').textContent = `骰子点数: ${diceValue}`;
                    } else {
                        document.getElementById('diceValue').textContent = "无法读出点数";
                    }

                    // 完全停止骰子的所有运动
                    body.velocity.set(0, 0, 0);
                    body.angularVelocity.set(0, 0, 0);
                    body.force.set(0, 0, 0);
                    body.torque.set(0, 0, 0);
                    body.sleep();

                    isRolling = false;
                    return; // 停止监测
                }

                // 继续监测
                window.checkDiceTimer = requestAnimationFrame(monitorDice);
            }

            // 开始监测
            window.checkDiceTimer = requestAnimationFrame(monitorDice);
        }

        // 检查骰子是否有一个面朝上
        function checkIfDiceFaceUp() {
            const directions = [
                new THREE.Vector3(0, 0, 1),   // 1
                new THREE.Vector3(1, 0, 0),   // 2
                new THREE.Vector3(0, 1, 0),   // 3
                new THREE.Vector3(-1, 0, 0),  // 4
                new THREE.Vector3(0, -1, 0),  // 5
                new THREE.Vector3(0, 0, -1)   // 6
            ];

            const diceRotation = new THREE.Quaternion();
            dice.getWorldQuaternion(diceRotation);

            const upVector = new THREE.Vector3(0, 1, 0);
            let maxDot = -Infinity;

            directions.forEach(dir => {
                const rotatedDir = dir.clone().applyQuaternion(diceRotation);
                const dot = rotatedDir.dot(upVector);
                maxDot = Math.max(maxDot, dot);
            });

            // 如果最大点积接近1，说明有一个面几乎垂直朝上
            // 使用0.9作为阈值，允许有小角度偏差
            return maxDot > 0.9;
        }

        // 确定骰子朝上的面
        function determineDiceValue() {
            const directions = [
                new THREE.Vector3(0, 0, 1),   // 1
                new THREE.Vector3(1, 0, 0),   // 2
                new THREE.Vector3(0, 1, 0),   // 3
                new THREE.Vector3(-1, 0, 0),  // 4
                new THREE.Vector3(0, -1, 0),  // 5
                new THREE.Vector3(0, 0, -1)   // 6
            ];

            const diceRotation = new THREE.Quaternion();
            dice.getWorldQuaternion(diceRotation);

            const upVector = new THREE.Vector3(0, 1, 0);
            let maxDot = -Infinity;
            let resultIndex = -1;

            directions.forEach((dir, i) => {
                const rotatedDir = dir.clone().applyQuaternion(diceRotation);
                const dot = rotatedDir.dot(upVector);
                if (dot > maxDot) {
                    maxDot = dot;
                    resultIndex = i;
                }
            });

            return resultIndex + 1; // 返回1-6之间的值
        }

        // 获取桌面边缘的随机位置
        function getRandomEdgePosition() {
            const tableWidth = 30; // 桌面宽度
            const tableDepth = 30; // 桌面深度
            const margin = 2; // 距离边缘的安全距离

            // 随机选择一个边
            const edge = Math.floor(Math.random() * 4);

            let x, z;

            switch (edge) {
                case 0: // 上边
                    x = (Math.random() * 2 - 1) * (tableWidth / 2 - margin);
                    z = -(tableDepth / 2 - margin);
                    break;
                case 1: // 右边
                    x = tableWidth / 2 - margin;
                    z = (Math.random() * 2 - 1) * (tableDepth / 2 - margin);
                    break;
                case 2: // 下边
                    x = (Math.random() * 2 - 1) * (tableWidth / 2 - margin);
                    z = tableDepth / 2 - margin;
                    break;
                case 3: // 左边
                    x = -(tableWidth / 2 - margin);
                    z = (Math.random() * 2 - 1) * (tableDepth / 2 - margin);
                    break;
            }

            return { x, z };
        }

        // 窗口大小变化时调整渲染器和相机
        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }

        // 动画循环
        function animate() {
            requestAnimationFrame(animate);

            const time = performance.now();
            const dt = Math.min((time - lastTime) / 1000, 1 / 60); // 使用更小的时间步长
            lastTime = time;

            // 更细的物理步进
            for (let i = 0; i < 3; i++) { // 每帧计算多次物理步进
                world.step(1 / 180, dt / 3, 10);
            }

            // 更新骰子位置
            diceBodys.forEach((body, i) => {
                // 更新视觉模型位置
                dice.position.copy(body.position);
                dice.quaternion.copy(body.quaternion);

                // 防止骰子下陷到桌面以下
                if (body.position.y < 0.75) {
                    body.position.y = 0.75;

                    // 如果骰子向下运动，应用更真实的碰撞响应
                    if (body.velocity.y < 0) {
                        // 计算碰撞后的速度
                        const restitution = world.defaultContactMaterial.restitution;
                        const friction = world.defaultContactMaterial.friction;

                        // 垂直方向的速度反转并衰减
                        body.velocity.y = -body.velocity.y * restitution;

                        // 水平方向的速度受摩擦力影响衰减
                        const horizontalDamping = 1 - friction * dt;
                        body.velocity.x *= horizontalDamping;
                        body.velocity.z *= horizontalDamping;

                        // 确保角速度也受到影响
                        body.angularVelocity.x *= horizontalDamping;
                        body.angularVelocity.z *= horizontalDamping;
                    }
                }
            });

            renderer.render(scene, camera);
        }
    </script>
</body>

</html>