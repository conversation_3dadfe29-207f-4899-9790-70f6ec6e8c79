<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>狼人杀助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background-color: #f5f5f5;
            background-image: url('https://images.unsplash.com/photo-1478131143081-80f7f84ca84d?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
            background-size: cover;
            background-attachment: fixed;
            background-position: center;
        }

        .page {
            display: none;
            min-height: 100vh;
        }

        .active {
            display: block;
        }

        .role-card {
            transition: transform 0.3s ease;
        }

        .role-card:hover {
            transform: translateY(-5px);
        }

        .flip-card {
            perspective: 1000px;
            height: 300px;
        }

        .flip-card-inner {
            position: relative;
            width: 100%;
            height: 100%;
            transition: transform 0.8s;
            transform-style: preserve-3d;
        }

        .flip-card.flipped .flip-card-inner {
            transform: rotateY(180deg);
        }

        .flip-card-front,
        .flip-card-back {
            position: absolute;
            width: 100%;
            height: 100%;
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            border-radius: 1rem;
        }

        .flip-card-back {
            transform: rotateY(180deg);
        }

        .moon-bg {
            background: linear-gradient(135deg, #2c3e50 0%, #4a69bd 100%);
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.05);
            }

            100% {
                transform: scale(1);
            }
        }

        .pulse-animation {
            animation: pulse 2s infinite;
        }
    </style>
</head>

<body class="bg-gray-100">
    <!-- 首页 -->
    <div id="home-page" class="page active">
        <div class="container mx-auto px-4 py-8">
            <div class="text-center mb-8 mt-8">
                <div class="inline-block p-4 bg-red-600 rounded-full mb-4 shadow-lg pulse-animation">
                    <i class="fas fa-moon text-5xl text-white"></i>
                </div>
                <h1 class="text-4xl font-bold text-white mb-2 text-shadow">狼人杀助手</h1>
                <p class="text-xl text-white text-opacity-90 mb-6">谁是狼人？谁是好人？一切尽在暗夜之中...</p>
            </div>

            <div class="max-w-md mx-auto glass-effect rounded-xl shadow-2xl p-6 mb-8 text-white">
                <div class="relative overflow-hidden rounded-lg mb-6">
                    <img src="https://images.unsplash.com/photo-1568743966689-d37c04538535?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80"
                        alt="狼人杀游戏"
                        class="w-full h-48 object-cover rounded-lg mb-4 transform hover:scale-105 transition duration-500">
                    <div class="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-70"></div>
                    <div class="absolute bottom-0 left-0 p-4">
                        <h2 class="text-2xl font-bold text-white mb-1">夜幕降临</h2>
                        <p class="text-white text-opacity-90">狼人们正在寻找他们的猎物...</p>
                    </div>
                </div>

                <p class="text-white text-opacity-80 mb-6">这是一款帮助你在线下狼人杀游戏中分发角色的小工具，无需主持人，让每个人都能参与游戏！</p>

                <div class="flex flex-col space-y-3">
                    <button onclick="showPage('create-game')"
                        class="bg-red-600 hover:bg-red-700 text-white font-bold py-4 px-4 rounded-lg transition duration-300 flex items-center justify-center shadow-lg transform hover:-translate-y-1">
                        <i class="fas fa-dice mr-2"></i> 来一局
                    </button>
                    <button onclick="showPage('join-game')"
                        class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-4 px-4 rounded-lg transition duration-300 flex items-center justify-center shadow-lg transform hover:-translate-y-1">
                        <i class="fas fa-sign-in-alt mr-2"></i> 加入游戏
                    </button>
                    <button onclick="showPage('game-rules')"
                        class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-4 px-4 rounded-lg transition duration-300 flex items-center justify-center shadow-lg transform hover:-translate-y-1">
                        <i class="fas fa-book mr-2"></i> 游戏规则
                    </button>
                </div>
            </div>

            <div class="max-w-md mx-auto glass-effect rounded-xl shadow-lg p-6 text-white">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold">我创建的游戏</h2>
                    <span class="text-xs bg-red-500 px-2 py-1 rounded-full">热门</span>
                </div>

                <div class="space-y-4">
                    <div class="border border-white border-opacity-20 rounded-lg p-4 hover:bg-white hover:bg-opacity-10 transition duration-300 cursor-pointer"
                        onclick="showPage('game-detail')">
                        <div class="flex justify-between items-center">
                            <div>
                                <h3 class="font-medium">周末聚会</h3>
                                <p class="text-sm text-white text-opacity-70">9人局 · 2023-10-15</p>
                            </div>
                            <i class="fas fa-chevron-right text-white text-opacity-50"></i>
                        </div>
                    </div>

                    <div
                        class="border border-white border-opacity-20 rounded-lg p-4 hover:bg-white hover:bg-opacity-10 transition duration-300 cursor-pointer">
                        <div class="flex justify-between items-center">
                            <div>
                                <h3 class="font-medium">朋友生日派对</h3>
                                <p class="text-sm text-white text-opacity-70">12人局 · 2023-10-08</p>
                            </div>
                            <i class="fas fa-chevron-right text-white text-opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建游戏页面 -->
    <div id="create-game" class="page">
        <div class="container mx-auto px-4 py-8">
            <div class="flex items-center mb-6">
                <button onclick="showPage('home-page')" class="mr-4 text-white">
                    <i class="fas fa-arrow-left text-xl"></i>
                </button>
                <h1 class="text-2xl font-bold text-white">来一局</h1>
            </div>

            <div
                class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl shadow-lg p-6 mb-6 text-white">
                <div class="mb-4">
                    <label for="game-name" class="block font-medium mb-2">游戏名称</label>
                    <input type="text" id="game-name" placeholder="例如：周末聚会"
                        class="w-full px-4 py-2 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 text-white placeholder-white placeholder-opacity-60">
                </div>

                <div class="mb-6">
                    <label class="block font-medium mb-2">角色配置</label>

                    <div class="space-y-3">
                        <div class="flex justify-between items-center p-3 bg-white bg-opacity-10 rounded-lg">
                            <div class="flex items-center">
                                <img src="https://images.unsplash.com/photo-1577975882846-431adc8c2009?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                                    alt="狼人" class="w-10 h-10 rounded-full object-cover mr-3">
                                <span class="font-medium">狼人</span>
                            </div>
                            <div class="flex items-center">
                                <button
                                    class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold">-</button>
                                <span class="mx-3 w-6 text-center">3</span>
                                <button
                                    class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold">+</button>
                            </div>
                        </div>

                        <div class="flex justify-between items-center p-3 bg-white bg-opacity-10 rounded-lg">
                            <div class="flex items-center">
                                <img src="https://images.unsplash.com/photo-1601758125946-6ec2ef64daf8?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                                    alt="村民" class="w-10 h-10 rounded-full object-cover mr-3">
                                <span class="font-medium">村民</span>
                            </div>
                            <div class="flex items-center">
                                <button
                                    class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold">-</button>
                                <span class="mx-3 w-6 text-center">4</span>
                                <button
                                    class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold">+</button>
                            </div>
                        </div>

                        <div class="flex justify-between items-center p-3 bg-white bg-opacity-10 rounded-lg">
                            <div class="flex items-center">
                                <img src="https://images.unsplash.com/photo-1518020382113-a7e8fc38eac9?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                                    alt="预言家" class="w-10 h-10 rounded-full object-cover mr-3">
                                <span class="font-medium">预言家</span>
                            </div>
                            <div class="flex items-center">
                                <button
                                    class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold">-</button>
                                <span class="mx-3 w-6 text-center">1</span>
                                <button
                                    class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold">+</button>
                            </div>
                        </div>

                        <div class="flex justify-between items-center p-3 bg-white bg-opacity-10 rounded-lg">
                            <div class="flex items-center">
                                <img src="https://images.unsplash.com/photo-1511367461989-f85a21fda167?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                                    alt="女巫" class="w-10 h-10 rounded-full object-cover mr-3">
                                <span class="font-medium">女巫</span>
                            </div>
                            <div class="flex items-center">
                                <button
                                    class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold">-</button>
                                <span class="mx-3 w-6 text-center">1</span>
                                <button
                                    class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold">+</button>
                            </div>
                        </div>

                        <button
                            class="w-full py-2 border border-dashed border-white border-opacity-30 rounded-lg text-white hover:bg-white hover:bg-opacity-10 transition duration-300">
                            <i class="fas fa-plus mr-2"></i> 添加更多角色
                        </button>
                    </div>
                </div>

                <button onclick="showPage('game-created')"
                    class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-4 rounded-lg transition duration-300">
                    开始游戏
                </button>
            </div>
        </div>
    </div>

    <!-- 游戏创建成功页面 -->
    <div id="game-created" class="page">
        <div class="container mx-auto px-4 py-8">
            <div class="text-center mb-8">
                <div class="inline-block p-4 bg-green-100 rounded-full mb-4">
                    <i class="fas fa-check text-3xl text-green-500"></i>
                </div>
                <h1 class="text-2xl font-bold text-white">游戏创建成功！</h1>
                <p class="text-white mt-2">游戏房间号: <span class="font-bold">835721</span></p>
                <div class="mt-4 bg-white bg-opacity-10 rounded-lg p-3 inline-block">
                    <span id="countdown-timer" class="font-mono text-xl text-white">10:00</span>
                    <p class="text-sm text-white text-opacity-80 mt-1">房间号有效时间</p>
                </div>
            </div>

            <div
                class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl shadow-lg p-6 mb-6 text-white">
                <h2 class="text-xl font-semibold mb-4">邀请好友</h2>

                <div class="text-center mb-6">
                    <div class="inline-block p-4 bg-white bg-opacity-20 rounded-lg mb-4">
                        <img src="https://via.placeholder.com/200x200" alt="微信二维码" class="w-48 h-48">
                    </div>
                    <p class="text-white text-opacity-80">扫描上方二维码加入游戏</p>
                </div>

                <div class="flex space-x-3 mb-6">
                    <button
                        class="flex-1 bg-green-500 hover:bg-green-600 text-white font-medium py-3 px-4 rounded-lg transition duration-300 flex items-center justify-center">
                        <i class="fab fa-weixin mr-2"></i> 微信分享
                    </button>
                </div>
            </div>

            <div class="flex space-x-3">
                <button onclick="showPage('game-room')"
                    class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg transition duration-300">
                    进入游戏
                </button>
                <button onclick="showPage('home-page')"
                    class="flex-1 bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-4 rounded-lg transition duration-300">
                    取消游戏
                </button>
            </div>
        </div>
    </div>

    <!-- 加入游戏页面 -->
    <div id="join-game" class="page">
        <div class="container mx-auto px-4 py-8">
            <div class="flex items-center mb-6">
                <button onclick="showPage('home-page')" class="mr-4 text-white">
                    <i class="fas fa-arrow-left text-xl"></i>
                </button>
                <h1 class="text-2xl font-bold text-white">加入游戏</h1>
            </div>

            <div
                class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl shadow-lg p-6 mb-6 text-white">
                <div class="mb-6">
                    <label for="player-name" class="block font-medium mb-2">你的昵称</label>
                    <input type="text" id="player-name" placeholder="输入你的昵称"
                        class="w-full px-4 py-2 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-white placeholder-white placeholder-opacity-60">
                </div>

                <div class="mb-6">
                    <label for="room-code" class="block font-medium mb-2">房间号</label>
                    <input type="text" id="room-code" placeholder="输入6位房间号"
                        class="w-full px-4 py-2 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-white placeholder-white placeholder-opacity-60">
                </div>

                <button onclick="showPage('game-room')"
                    class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg transition duration-300">
                    加入游戏
                </button>
            </div>

            <div class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl shadow-lg p-6 text-white">
                <h2 class="text-xl font-semibold mb-4">扫码加入</h2>
                <p class="text-white text-opacity-80 mb-4">如果你收到了游戏邀请二维码，可以直接扫码加入</p>

                <button
                    class="w-full bg-white bg-opacity-20 hover:bg-opacity-30 text-white font-bold py-3 px-4 rounded-lg transition duration-300 flex items-center justify-center">
                    <i class="fas fa-camera mr-2"></i> 扫描二维码
                </button>
            </div>
        </div>
    </div>

    <!-- 游戏房间页面 -->
    <div id="game-room" class="page">
        <div class="container mx-auto px-4 py-8">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-white">周末聚会</h1>
                </div>
                <div class="flex items-center">
                    <span class="bg-red-500 text-white text-xs font-medium px-2.5 py-1 rounded mr-2">房间号: 835721</span>
                    <span id="game-countdown" class="font-mono text-sm text-white">08:32</span>
                </div>
            </div>

            <div
                class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl shadow-lg p-6 mb-6 text-white">
                <div class="flip-card mb-6" id="role-card">
                    <div class="flip-card-inner">
                        <div class="flip-card-front bg-red-600">
                            <img src="https://images.unsplash.com/photo-1518020382113-a7e8fc38eac9?ixlib=rb-1.2.1&auto=format&fit=crop&w=240&q=80"
                                alt="角色卡背面" class="w-24 h-24 rounded-full object-cover mb-4">
                            <h2 class="text-xl font-bold text-white mb-2">点击查看你的身份</h2>
                            <p class="text-white text-opacity-80">请不要让其他人看到</p>
                        </div>
                        <div class="flip-card-back bg-blue-600">
                            <img src="https://images.unsplash.com/photo-1518020382113-a7e8fc38eac9?ixlib=rb-1.2.1&auto=format&fit=crop&w=240&q=80"
                                alt="预言家" class="w-24 h-24 rounded-full object-cover mb-4">
                            <h2 class="text-xl font-bold text-white mb-2">预言家</h2>
                            <p class="text-white text-opacity-80 text-center px-4">每晚可以查验一名玩家的身份</p>
                        </div>
                    </div>
                </div>

                <button id="view-role-btn"
                    class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-4 rounded-lg transition duration-300 mb-4">
                    查看我的身份
                </button>

                <button id="hide-role-btn"
                    class="w-full bg-gray-600 hover:bg-gray-700 text-white font-bold py-3 px-4 rounded-lg transition duration-300 hidden">
                    隐藏我的身份
                </button>
            </div>

            <div
                class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl shadow-lg p-6 mb-6 text-white">
                <h2 class="text-xl font-semibold mb-4">玩家列表</h2>

                <div class="grid grid-cols-3 gap-3">
                    <div class="flex flex-col items-center p-3 bg-white bg-opacity-10 rounded-lg">
                        <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                            alt="玩家头像" class="w-14 h-14 rounded-full object-cover mb-1">
                        <p class="text-sm font-medium truncate w-full text-center">小明</p>
                        <p class="text-xs text-white text-opacity-70">房主</p>
                    </div>

                    <div class="flex flex-col items-center p-3 bg-white bg-opacity-10 rounded-lg">
                        <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                            alt="玩家头像" class="w-14 h-14 rounded-full object-cover mb-1">
                        <p class="text-sm font-medium truncate w-full text-center">小红</p>
                    </div>

                    <div class="flex flex-col items-center p-3 bg-white bg-opacity-10 rounded-lg">
                        <img src="https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                            alt="玩家头像" class="w-14 h-14 rounded-full object-cover mb-1">
                        <p class="text-sm font-medium truncate w-full text-center">小刚</p>
                    </div>

                    <div class="flex flex-col items-center p-3 bg-white bg-opacity-10 rounded-lg">
                        <img src="https://images.unsplash.com/photo-1607746882042-944635dfe10e?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                            alt="玩家头像" class="w-14 h-14 rounded-full object-cover mb-1">
                        <p class="text-sm font-medium truncate w-full text-center">小丽</p>
                    </div>

                    <div class="flex flex-col items-center p-3 bg-white bg-opacity-10 rounded-lg">
                        <div class="w-14 h-14 rounded-full bg-blue-500 flex items-center justify-center mb-1">
                            <span class="text-white font-bold">你</span>
                        </div>
                        <p class="text-sm font-medium truncate w-full text-center">小华</p>
                    </div>
                </div>
            </div>

            <div class="flex space-x-3">
                <button onclick="showPage('game-rules')"
                    class="flex-1 bg-white bg-opacity-20 hover:bg-opacity-30 text-white font-bold py-3 px-4 rounded-lg transition duration-300 flex items-center justify-center">
                    <i class="fas fa-book mr-2"></i> 游戏规则
                </button>
                <button onclick="showPage('home-page')"
                    class="flex-1 bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-4 rounded-lg transition duration-300 flex items-center justify-center">
                    <i class="fas fa-sign-out-alt mr-2"></i> 退出游戏
                </button>
            </div>
        </div>
    </div>

    <!-- 游戏规则页面 -->
    <div id="game-rules" class="page">
        <div class="container mx-auto px-4 py-8">
            <div class="flex items-center mb-6">
                <button onclick="showPage('game-room')" class="mr-4 text-white">
                    <i class="fas fa-arrow-left text-xl"></i>
                </button>
                <h1 class="text-2xl font-bold text-white">游戏规则</h1>
            </div>

            <div
                class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl shadow-lg p-6 mb-6 text-white">
                <h2 class="text-xl font-semibold mb-4">基本规则</h2>

                <div class="space-y-4">
                    <p>狼人杀是一款多人参与的、以语言描述推动的、较量口才和分析判断能力的策略类桌游。</p>

                    <h3 class="font-medium mt-4">游戏目标</h3>
                    <p>狼人阵营：消灭所有神民或村民，或者达到狼人数量等于或多于其他玩家的数量。</p>
                    <p>好人阵营：消灭所有狼人。</p>

                    <h3 class="font-medium mt-4">游戏流程</h3>
                    <ol class="list-decimal pl-5 space-y-2">
                        <li>游戏开始，每位玩家获得一个身份。</li>
                        <li>夜晚阶段：各特殊角色按顺序行使技能。</li>
                        <li>白天阶段：所有玩家讨论，投票处决一名玩家。</li>
                        <li>如此循环，直到一方达成胜利条件。</li>
                    </ol>
                </div>
            </div>

            <div
                class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl shadow-lg p-6 mb-6 text-white">
                <h2 class="text-xl font-semibold mb-4">角色介绍</h2>

                <div class="space-y-4">
                    <div class="p-4 bg-white bg-opacity-10 rounded-lg">
                        <div class="flex items-center mb-2">
                            <img src="https://images.unsplash.com/photo-1577975882846-431adc8c2009?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                                alt="狼人" class="w-10 h-10 rounded-full object-cover mr-3">
                            <h3 class="font-medium">狼人</h3>
                        </div>
                        <p>每晚可以与其他狼人一起选择一名玩家杀死。狼人之间互相认识。</p>
                    </div>

                    <div class="p-4 bg-white bg-opacity-10 rounded-lg">
                        <div class="flex items-center mb-2">
                            <img src="https://images.unsplash.com/photo-1601758125946-6ec2ef64daf8?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                                alt="村民" class="w-10 h-10 rounded-full object-cover mr-3">
                            <h3 class="font-medium">村民</h3>
                        </div>
                        <p>没有特殊技能，只能依靠推理和投票来帮助好人阵营获胜。</p>
                    </div>

                    <div class="p-4 bg-white bg-opacity-10 rounded-lg">
                        <div class="flex items-center mb-2">
                            <img src="https://images.unsplash.com/photo-1518020382113-a7e8fc38eac9?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                                alt="预言家" class="w-10 h-10 rounded-full object-cover mr-3">
                            <h3 class="font-medium">预言家</h3>
                        </div>
                        <p>每晚可以查验一名玩家的身份是好人还是狼人。</p>
                    </div>

                    <div class="p-4 bg-white bg-opacity-10 rounded-lg">
                        <div class="flex items-center mb-2">
                            <img src="https://images.unsplash.com/photo-1511367461989-f85a21fda167?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                                alt="女巫" class="w-10 h-10 rounded-full object-cover mr-3">
                            <h3 class="font-medium">女巫</h3>
                        </div>
                        <p>拥有一瓶解药和一瓶毒药。解药可以救活当晚被狼人杀死的玩家，毒药可以毒死一名玩家。每种药只能使用一次。</p>
                    </div>
                </div>
            </div>

            <button onclick="showPage('role-intro')"
                class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg transition duration-300">
                查看更多角色介绍
            </button>
        </div>
    </div>

    <!-- 游戏详情页面 -->
    <div id="game-detail" class="page">
        <div class="container mx-auto px-4 py-8">
            <div class="flex items-center mb-6">
                <button onclick="showPage('home-page')" class="mr-4 text-white">
                    <i class="fas fa-arrow-left text-xl"></i>
                </button>
                <h1 class="text-2xl font-bold text-white">游戏详情</h1>
            </div>

            <div
                class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl shadow-lg p-6 mb-6 text-white">
                <div class="mb-4">
                    <h2 class="text-xl font-semibold">周末聚会</h2>
                </div>

                <p class="text-white text-opacity-80 mb-4">9人局 · 2023-10-15</p>

                <div class="border-t border-white border-opacity-20 pt-4">
                    <h3 class="font-medium mb-2">玩家角色</h3>

                    <div class="space-y-3">
                        <div class="flex justify-between items-center p-3 bg-white bg-opacity-10 rounded-lg">
                            <div class="flex items-center">
                                <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                                    alt="玩家头像" class="w-10 h-10 rounded-full object-cover mr-3">
                                <div>
                                    <p class="font-medium">小明</p>
                                    <p class="text-xs text-white text-opacity-70">房主</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <img src="https://images.unsplash.com/photo-1518020382113-a7e8fc38eac9?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                                    alt="预言家" class="w-6 h-6 rounded-full object-cover mr-1">
                                <span class="text-sm font-medium">预言家</span>
                            </div>
                        </div>

                        <div class="flex justify-between items-center p-3 bg-white bg-opacity-10 rounded-lg">
                            <div class="flex items-center">
                                <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                                    alt="玩家头像" class="w-10 h-10 rounded-full object-cover mr-3">
                                <div>
                                    <p class="font-medium">小红</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <img src="https://images.unsplash.com/photo-1577975882846-431adc8c2009?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                                    alt="狼人" class="w-6 h-6 rounded-full object-cover mr-1">
                                <span class="text-sm font-medium">狼人</span>
                            </div>
                        </div>

                        <div class="flex justify-between items-center p-3 bg-white bg-opacity-10 rounded-lg">
                            <div class="flex items-center">
                                <img src="https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                                    alt="玩家头像" class="w-10 h-10 rounded-full object-cover mr-3">
                                <div>
                                    <p class="font-medium">小刚</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <img src="https://images.unsplash.com/photo-1601758125946-6ec2ef64daf8?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                                    alt="村民" class="w-6 h-6 rounded-full object-cover mr-1">
                                <span class="text-sm font-medium">村民</span>
                            </div>
                        </div>
                    </div>

                    <button
                        class="w-full mt-4 py-2 border border-white border-opacity-30 rounded-lg text-white hover:bg-white hover:bg-opacity-10 transition duration-300">
                        查看全部玩家
                    </button>
                </div>
            </div>

            <div class="flex space-x-3">
                <button onclick="showPage('create-game')"
                    class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg transition duration-300 flex items-center justify-center">
                    <i class="fas fa-redo mr-2"></i> 再来一局
                </button>
                <button onclick="showPage('home-page')"
                    class="flex-1 bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-4 rounded-lg transition duration-300 flex items-center justify-center">
                    <i class="fas fa-trash mr-2"></i> 删除游戏
                </button>
            </div>
        </div>
    </div>

    <!-- 角色介绍页面 -->
    <div id="role-intro" class="page">
        <div class="container mx-auto px-4 py-8">
            <div class="flex items-center mb-6">
                <button onclick="showPage('game-rules')" class="mr-4 text-white">
                    <i class="fas fa-arrow-left text-xl"></i>
                </button>
                <h1 class="text-2xl font-bold text-white">角色介绍</h1>
            </div>

            <div class="grid grid-cols-1 gap-4 mb-6">
                <div
                    class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl shadow-lg overflow-hidden">
                    <div class="bg-red-600 p-6 flex items-center">
                        <img src="https://images.unsplash.com/photo-1577975882846-431adc8c2009?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                            alt="狼人" class="w-16 h-16 rounded-full object-cover border-2 border-white mr-4">
                        <div>
                            <h2 class="text-xl font-bold text-white">狼人</h2>
                            <p class="text-white text-opacity-80">狼人阵营</p>
                        </div>
                    </div>

                    <div class="p-6">
                        <h3 class="font-medium text-white mb-2">技能介绍</h3>
                        <p class="text-white text-opacity-80 mb-4">每晚可以与其他狼人一起选择一名玩家杀死。狼人之间互相认识。</p>

                        <h3 class="font-medium text-white mb-2">游戏目标</h3>
                        <p class="text-white text-opacity-80 mb-4">消灭所有神民或村民，或者达到狼人数量等于或多于其他玩家的数量。</p>

                        <h3 class="font-medium text-white mb-2">游戏技巧</h3>
                        <ul class="list-disc pl-5 space-y-1 text-white text-opacity-80">
                            <li>尽量隐藏自己的身份，伪装成好人</li>
                            <li>优先击杀有特殊能力的神民</li>
                            <li>与其他狼人配合，互相掩护</li>
                            <li>制造混乱，分散好人的注意力</li>
                        </ul>
                    </div>
                </div>

                <div
                    class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl shadow-lg overflow-hidden">
                    <div class="bg-green-600 p-6 flex items-center">
                        <img src="https://images.unsplash.com/photo-1601758125946-6ec2ef64daf8?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                            alt="村民" class="w-16 h-16 rounded-full object-cover border-2 border-white mr-4">
                        <div>
                            <h2 class="text-xl font-bold text-white">村民</h2>
                            <p class="text-white text-opacity-80">好人阵营</p>
                        </div>
                    </div>

                    <div class="p-6">
                        <h3 class="font-medium text-white mb-2">技能介绍</h3>
                        <p class="text-white text-opacity-80 mb-4">没有特殊技能，只能依靠推理和投票来帮助好人阵营获胜。</p>

                        <h3 class="font-medium text-white mb-2">游戏目标</h3>
                        <p class="text-white text-opacity-80 mb-4">与其他好人一起找出并处决所有狼人。</p>

                        <h3 class="font-medium text-white mb-2">游戏技巧</h3>
                        <ul class="list-disc pl-5 space-y-1 text-white text-opacity-80">
                            <li>仔细观察每个人的发言和行为</li>
                            <li>寻找狼人的破绽和矛盾</li>
                            <li>相信自己的直觉，但也要听取他人的意见</li>
                            <li>保护神民角色，他们是好人阵营的关键</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 gap-4 mb-6">
                <div
                    class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl shadow-lg overflow-hidden">
                    <div class="bg-blue-600 p-6 flex items-center">
                        <img src="https://images.unsplash.com/photo-1518020382113-a7e8fc38eac9?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                            alt="预言家" class="w-16 h-16 rounded-full object-cover border-2 border-white mr-4">
                        <div>
                            <h2 class="text-xl font-bold text-white">预言家</h2>
                            <p class="text-white text-opacity-80">好人阵营</p>
                        </div>
                    </div>

                    <div class="p-6">
                        <h3 class="font-medium text-white mb-2">技能介绍</h3>
                        <p class="text-white text-opacity-80 mb-4">每晚可以查验一名玩家的身份是好人还是狼人。</p>

                        <h3 class="font-medium text-white mb-2">游戏目标</h3>
                        <p class="text-white text-opacity-80 mb-4">利用查验能力帮助好人阵营找出狼人。</p>

                        <h3 class="font-medium text-white mb-2">游戏技巧</h3>
                        <ul class="list-disc pl-5 space-y-1 text-white text-opacity-80">
                            <li>优先查验有嫌疑的玩家</li>
                            <li>谨慎选择是否公开自己的身份</li>
                            <li>可以考虑隐藏部分查验结果，防止被狼人针对</li>
                            <li>在关键时刻站出来指认狼人</li>
                        </ul>
                    </div>
                </div>

                <div
                    class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl shadow-lg overflow-hidden">
                    <div class="bg-purple-600 p-6 flex items-center">
                        <img src="https://images.unsplash.com/photo-1511367461989-f85a21fda167?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                            alt="女巫" class="w-16 h-16 rounded-full object-cover border-2 border-white mr-4">
                        <div>
                            <h2 class="text-xl font-bold text-white">女巫</h2>
                            <p class="text-white text-opacity-80">好人阵营</p>
                        </div>
                    </div>

                    <div class="p-6">
                        <h3 class="font-medium text-white mb-2">技能介绍</h3>
                        <p class="text-white text-opacity-80 mb-4">拥有一瓶解药和一瓶毒药。解药可以救活当晚被狼人杀死的玩家，毒药可以毒死一名玩家。每种药只能使用一次。
                        </p>

                        <h3 class="font-medium text-white mb-2">游戏目标</h3>
                        <p class="text-white text-opacity-80 mb-4">合理使用解药和毒药，帮助好人阵营获胜。</p>

                        <h3 class="font-medium text-white mb-2">游戏技巧</h3>
                        <ul class="list-disc pl-5 space-y-1 text-white text-opacity-80">
                            <li>解药优先救神民角色</li>
                            <li>毒药要留到确定是狼人的玩家身上</li>
                            <li>不要轻易暴露自己的身份</li>
                            <li>可以考虑不使用解药，以隐藏自己的身份</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 分享邀请页面 -->
    <div id="share-invite" class="page">
        <div class="container mx-auto px-4 py-8">
            <div class="flex items-center mb-6">
                <button onclick="showPage('game-created')" class="mr-4 text-white">
                    <i class="fas fa-arrow-left text-xl"></i>
                </button>
                <h1 class="text-2xl font-bold text-white">邀请好友</h1>
            </div>

            <div
                class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl shadow-lg p-6 mb-6 text-white">
                <div class="text-center mb-6">
                    <div class="inline-block p-4 bg-white bg-opacity-20 rounded-lg mb-4">
                        <img src="https://via.placeholder.com/200x200" alt="二维码" class="w-48 h-48">
                    </div>
                    <p class="text-white text-opacity-80">扫描上方二维码加入游戏</p>
                    <div class="mt-2 text-sm text-white text-opacity-60">
                        <span id="qr-countdown" class="font-mono">09:45</span> 后失效
                    </div>
                </div>

                <div class="bg-white bg-opacity-20 p-4 rounded-lg mb-6 relative">
                    <p class="text-white pr-10">https://werewolf-mini.example.com/join/835721</p>
                    <button class="absolute right-4 top-h-2 transform -translate-y-1/2 text-blue-300">
                        <i class="far fa-copy"></i>
                    </button>
                </div>

                <div class="border-t border-white border-opacity-20 pt-4">
                    <h3 class="font-medium mb-2">游戏信息</h3>

                    <div class="space-y-2">
                        <div class="flex justify-between items-center">
                            <span class="text-white text-opacity-80">游戏名称</span>
                            <span class="font-medium">周末聚会</span>
                        </div>

                        <div class="flex justify-between items-center">
                            <span class="text-white text-opacity-80">房间号</span>
                            <span class="font-medium">835721</span>
                        </div>

                        <div class="flex justify-between items-center">
                            <span class="text-white text-opacity-80">玩家人数</span>
                            <span class="font-medium">9人局</span>
                        </div>
                    </div>
                </div>
            </div>

            <div
                class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl shadow-lg p-6 mb-6 text-white">
                <h2 class="text-xl font-semibold mb-4">分享到</h2>

                <div class="grid grid-cols-4 gap-4">
                    <div class="flex flex-col items-center">
                        <div
                            class="w-14 h-14 rounded-full bg-green-500 bg-opacity-30 flex items-center justify-center mb-2">
                            <i class="fab fa-weixin text-2xl text-green-400"></i>
                        </div>
                        <p class="text-sm">微信好友</p>
                    </div>

                    <div class="flex flex-col items-center">
                        <div
                            class="w-14 h-14 rounded-full bg-green-500 bg-opacity-30 flex items-center justify-center mb-2">
                            <i class="fas fa-users text-2xl text-green-400"></i>
                        </div>
                        <p class="text-sm">微信群</p>
                    </div>

                    <div class="flex flex-col items-center">
                        <div
                            class="w-14 h-14 rounded-full bg-red-500 bg-opacity-30 flex items-center justify-center mb-2">
                            <i class="fas fa-comment-dots text-2xl text-red-400"></i>
                        </div>
                        <p class="text-sm">朋友圈</p>
                    </div>

                    <div class="flex flex-col items-center">
                        <div
                            class="w-14 h-14 rounded-full bg-blue-500 bg-opacity-30 flex items-center justify-center mb-2">
                            <i class="fas fa-link text-2xl text-blue-400"></i>
                        </div>
                        <p class="text-sm">复制链接</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加JavaScript功能 -->
    <script>
        // 页面切换功能
        function showPage(pageId) {
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });

            // 显示指定页面
            document.getElementById(pageId).classList.add('active');

            // 如果进入游戏创建成功页面，启动倒计时
            if (pageId === 'game-created') {
                startCountdown('countdown-timer', 10 * 60); // 10分钟倒计时
            }

            // 如果进入游戏房间页面，启动倒计时
            if (pageId === 'game-room') {
                startCountdown('game-countdown', 8 * 60 + 32); // 8分32秒倒计时
            }

            // 如果进入分享邀请页面，启动倒计时
            if (pageId === 'share-invite') {
                startCountdown('qr-countdown', 9 * 60 + 45); // 9分45秒倒计时
            }
        }

        // 角色卡片翻转功能
        document.addEventListener('DOMContentLoaded', function () {
            const roleCard = document.getElementById('role-card');
            const viewRoleBtn = document.getElementById('view-role-btn');
            const hideRoleBtn = document.getElementById('hide-role-btn');

            if (viewRoleBtn && hideRoleBtn && roleCard) {
                viewRoleBtn.addEventListener('click', function () {
                    roleCard.classList.add('flipped');
                    viewRoleBtn.classList.add('hidden');
                    hideRoleBtn.classList.remove('hidden');
                });

                hideRoleBtn.addEventListener('click', function () {
                    roleCard.classList.remove('flipped');
                    hideRoleBtn.classList.add('hidden');
                    viewRoleBtn.classList.remove('hidden');
                });
            }
        });

        // 倒计时功能
        function startCountdown(elementId, seconds) {
            const element = document.getElementById(elementId);
            if (!element) return;

            updateCountdown(element, seconds);

            const interval = setInterval(() => {
                seconds--;
                if (seconds <= 0) {
                    clearInterval(interval);
                    // 倒计时结束后可以执行相应操作
                    if (elementId === 'countdown-timer' || elementId === 'game-countdown' || elementId === 'qr-countdown') {
                        showPage('home-page'); // 时间到自动返回首页
                    }
                }
                updateCountdown(element, seconds);
            }, 1000);
        }

        function updateCountdown(element, seconds) {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            element.textContent = `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
        }
    </script>
</body>

</html>