<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聚会大乱斗 - 微信小游戏</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }

        .page {
            max-width: 414px;
            min-height: 100vh;
            margin: 0 auto;
            border-left: 1px solid #eaeaea;
            border-right: 1px solid #eaeaea;
        }

        .page-title {
            position: relative;
            margin-bottom: 20px;
        }
    </style>
</head>

<body class="bg-gray-100">
    <div class="container mx-auto py-8 px-4">
        <h1 class="text-3xl font-bold text-center mb-8">聚会大乱斗 - 微信小游戏原型</h1>

        <!-- 启动页 -->
        <div class="page bg-gradient-to-b from-purple-600 to-indigo-800 p-6 mb-12 rounded-xl shadow-lg">
            <div class="flex flex-col items-center justify-center h-screen">
                <img src="https://images.unsplash.com/photo-1529156069898-49953e39b3ac?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80"
                    alt="聚会大乱斗" class="w-32 h-32 rounded-full mb-8 border-4 border-white shadow-lg">
                <h1 class="text-4xl font-bold text-white mb-4">聚会大乱斗</h1>
                <p class="text-xl text-white mb-12 text-center">让每一次聚会都充满欢乐</p>
                <button
                    class="bg-white text-purple-700 font-bold py-3 px-8 rounded-full shadow-lg hover:bg-purple-100 transition duration-300 mb-4">
                    开始游戏
                </button>
                <p class="text-white text-sm opacity-80">微信授权登录后开始</p>
            </div>
        </div>

        <!-- 游戏大厅 -->
        <div class="page bg-white p-6 mb-12 rounded-xl shadow-lg">
            <div class="page-title">
                <h2 class="text-2xl font-bold text-center">游戏大厅</h2>
                <div class="absolute right-0 top-1">
                    <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                        alt="用户头像" class="w-8 h-8 rounded-full">
                </div>
            </div>

            <div class="mb-8">
                <div class="flex justify-between mb-4">
                    <button
                        class="bg-purple-600 text-white font-bold py-3 px-6 rounded-lg shadow-md hover:bg-purple-700 transition duration-300 flex-1 mr-2">
                        创建房间
                    </button>
                    <button
                        class="bg-indigo-600 text-white font-bold py-3 px-6 rounded-lg shadow-md hover:bg-indigo-700 transition duration-300 flex-1 ml-2">
                        加入房间
                    </button>
                </div>

                <div class="bg-gray-50 p-4 rounded-lg">
                    <p class="text-gray-600 text-sm mb-2">快速加入</p>
                    <div class="flex items-center">
                        <input type="text" placeholder="输入房间号"
                            class="flex-1 border border-gray-300 rounded-lg py-2 px-4 mr-2">
                        <button class="bg-blue-500 text-white py-2 px-4 rounded-lg">加入</button>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-3">热门游戏</h3>
                <div class="grid grid-cols-2 gap-4">
                    <div class="bg-gradient-to-br from-pink-500 to-red-500 p-4 rounded-lg text-white text-center">
                        <img src="https://images.unsplash.com/photo-1522869635100-9f4c5e86aa37?ixlib=rb-1.2.1&auto=format&fit=crop&w=150&q=80"
                            alt="真心话大冒险" class="w-full h-24 object-cover rounded-lg mb-2">
                        <p class="font-medium">真心话大冒险</p>
                    </div>
                    <div class="bg-gradient-to-br from-green-500 to-teal-500 p-4 rounded-lg text-white text-center">
                        <img src="https://images.unsplash.com/photo-1511379938547-c1f69419868d?ixlib=rb-1.2.1&auto=format&fit=crop&w=150&q=80"
                            alt="你画我猜" class="w-full h-24 object-cover rounded-lg mb-2">
                        <p class="font-medium">你画我猜</p>
                    </div>
                    <div class="bg-gradient-to-br from-yellow-500 to-orange-500 p-4 rounded-lg text-white text-center">
                        <img src="https://images.unsplash.com/photo-1543269865-cbf427effbad?ixlib=rb-1.2.1&auto=format&fit=crop&w=150&q=80"
                            alt="谁是卧底" class="w-full h-24 object-cover rounded-lg mb-2">
                        <p class="font-medium">谁是卧底</p>
                    </div>
                    <div class="bg-gradient-to-br from-blue-500 to-indigo-500 p-4 rounded-lg text-white text-center">
                        <img src="https://images.unsplash.com/photo-1523580494863-6f3031224c94?ixlib=rb-1.2.1&auto=format&fit=crop&w=150&q=80"
                            alt="疯狂猜词" class="w-full h-24 object-cover rounded-lg mb-2">
                        <p class="font-medium">疯狂猜词</p>
                    </div>
                </div>
            </div>

            <div class="mb-4">
                <h3 class="text-lg font-semibold mb-3">最近游戏</h3>
                <div class="space-y-3">
                    <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                        <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                            <span class="text-purple-600 font-bold">真</span>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium">真心话大冒险</p>
                            <p class="text-sm text-gray-500">昨天 20:30 · 5人参与</p>
                        </div>
                        <button class="text-blue-500 text-sm">继续</button>
                    </div>
                    <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-3">
                            <span class="text-green-600 font-bold">你</span>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium">你画我猜</p>
                            <p class="text-sm text-gray-500">3天前 · 4人参与</p>
                        </div>
                        <button class="text-blue-500 text-sm">继续</button>
                    </div>
                </div>
            </div>

            <div class="flex justify-around pt-4 border-t border-gray-200">
                <button class="flex flex-col items-center text-purple-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                    <span class="text-xs mt-1">首页</span>
                </button>
                <button class="flex flex-col items-center text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <span class="text-xs mt-1">我的</span>
                </button>
                <button class="flex flex-col items-center text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <span class="text-xs mt-1">排行</span>
                </button>
                <button class="flex flex-col items-center text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <span class="text-xs mt-1">设置</span>
                </button>
            </div>
        </div>

        <!-- 创建房间 -->
        <div class="page bg-white p-6 mb-12 rounded-xl shadow-lg">
            <div class="page-title">
                <div class="absolute left-0 top-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-600" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                </div>
                <h2 class="text-2xl font-bold text-center">创建房间</h2>
            </div>

            <div class="mb-6">
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">房间名称</label>
                    <input type="text" placeholder="输入房间名称" class="w-full border border-gray-300 rounded-lg py-3 px-4">
                </div>

                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">游戏类型</label>
                    <div class="grid grid-cols-2 gap-3">
                        <div class="border-2 border-purple-500 rounded-lg p-3 text-center bg-purple-50">
                            <p class="font-medium text-purple-700">真心话大冒险</p>
                        </div>
                        <div class="border-2 border-gray-200 rounded-lg p-3 text-center">
                            <p class="font-medium text-gray-600">你画我猜</p>
                        </div>
                        <div class="border-2 border-gray-200 rounded-lg p-3 text-center">
                            <p class="font-medium text-gray-600">谁是卧底</p>
                        </div>
                        <div class="border-2 border-gray-200 rounded-lg p-3 text-center">
                            <p class="font-medium text-gray-600">疯狂猜词</p>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">人数限制</label>
                    <div class="flex items-center">
                        <button class="bg-gray-200 text-gray-600 w-10 h-10 rounded-full">-</button>
                        <span class="mx-4 text-xl font-bold">6</span>
                        <button class="bg-gray-200 text-gray-600 w-10 h-10 rounded-full">+</button>
                    </div>
                </div>

                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-bold mb-2">游戏设置</label>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-700">是否公开房间</span>
                            <div class="relative inline-block w-12 h-6 rounded-full bg-gray-300">
                                <div class="absolute left-1 top-1 w-4 h-4 rounded-full bg-white"></div>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-700">需要密码</span>
                            <div class="relative inline-block w-12 h-6 rounded-full bg-purple-500">
                                <div class="absolute right-1 top-1 w-4 h-4 rounded-full bg-white"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">房间密码</label>
                    <input type="text" placeholder="设置4-6位数字密码"
                        class="w-full border border-gray-300 rounded-lg py-3 px-4">
                </div>
            </div>

            <button
                class="w-full bg-purple-600 text-white font-bold py-3 px-6 rounded-lg shadow-md hover:bg-purple-700 transition duration-300">
                创建并进入
            </button>
        </div>

        <!-- 游戏房间 -->
        <div class="page bg-white p-6 mb-12 rounded-xl shadow-lg">
            <div class="page-title">
                <div class="absolute left-0 top-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-600" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                </div>
                <h2 class="text-2xl font-bold text-center">欢乐聚会</h2>
                <div class="absolute right-0 top-1">
                    <span class="text-sm bg-purple-100 text-purple-700 py-1 px-2 rounded-full">房间号: 8264</span>
                </div>
            </div>

            <div class="mb-6">
                <div class="bg-purple-50 p-4 rounded-lg mb-4">
                    <div class="flex justify-between items-center mb-2">
                        <h3 class="font-bold text-purple-800">真心话大冒险</h3>
                        <span class="text-sm text-purple-600">4/6人</span>
                    </div>
                    <p class="text-sm text-purple-700">等待房主开始游戏...</p>
                </div>

                <div class="grid grid-cols-3 gap-4 mb-6">
                    <div class="flex flex-col items-center">
                        <div class="relative">
                            <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                                alt="用户头像" class="w-16 h-16 rounded-full border-2 border-purple-500">
                            <span
                                class="absolute bottom-0 right-0 bg-purple-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">房</span>
                        </div>
                        <p class="mt-1 text-sm font-medium">小明</p>
                    </div>
                    <div class="flex flex-col items-center">
                        <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                            alt="用户头像" class="w-16 h-16 rounded-full border-2 border-blue-300">
                        <p class="mt-1 text-sm font-medium">小红</p>
                    </div>
                    <div class="flex flex-col items-center">
                        <img src="https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                            alt="用户头像" class="w-16 h-16 rounded-full border-2 border-blue-300">
                        <p class="mt-1 text-sm font-medium">小刚</p>
                    </div>
                    <div class="flex flex-col items-center">
                        <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                            alt="用户头像" class="w-16 h-16 rounded-full border-2 border-blue-300">
                        <p class="mt-1 text-sm font-medium">小丽</p>
                    </div>
                    <div class="flex flex-col items-center">
                        <div
                            class="w-16 h-16 rounded-full border-2 border-dashed border-gray-300 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-400" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                        </div>
                        <p class="mt-1 text-sm font-medium text-gray-400">等待加入</p>
                    </div>
                    <div class="flex flex-col items-center">
                        <div
                            class="w-16 h-16 rounded-full border-2 border-dashed border-gray-300 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-400" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                        </div>
                        <p class="mt-1 text-sm font-medium text-gray-400">等待加入</p>
                    </div>
                </div>

                <div class="bg-gray-50 p-4 rounded-lg mb-6">
                    <h3 class="font-bold mb-2">聊天室</h3>
                    <div class="space-y-3 mb-3 h-32 overflow-y-auto">
                        <div class="flex items-start">
                            <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                                alt="用户头像" class="w-8 h-8 rounded-full mr-2">
                            <div>
                                <p class="text-xs text-gray-500">小明 (房主)</p>
                                <p class="text-sm bg-white p-2 rounded-lg shadow-sm">大家好，准备开始游戏了！</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                                alt="用户头像" class="w-8 h-8 rounded-full mr-2">
                            <div>
                                <p class="text-xs text-gray-500">小红</p>
                                <p class="text-sm bg-white p-2 rounded-lg shadow-sm">好的，我已经准备好了！</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <img src="https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                                alt="用户头像" class="w-8 h-8 rounded-full mr-2">
                            <div>
                                <p class="text-xs text-gray-500">小刚</p>
                                <p class="text-sm bg-white p-2 rounded-lg shadow-sm">我们等一下小丽吧</p>
                            </div>
                        </div>
                    </div>
                    <div class="flex">
                        <input type="text" placeholder="发送消息..."
                            class="flex-1 border border-gray-300 rounded-lg py-2 px-4 mr-2">
                        <button class="bg-blue-500 text-white py-2 px-4 rounded-lg">发送</button>
                    </div>
                </div>
            </div>

            <div class="flex space-x-3">
                <button
                    class="flex-1 bg-purple-600 text-white font-bold py-3 px-6 rounded-lg shadow-md hover:bg-purple-700 transition duration-300">
                    开始游戏
                </button>
                <button
                    class="bg-gray-200 text-gray-700 font-bold py-3 px-6 rounded-lg shadow-md hover:bg-gray-300 transition duration-300">
                    邀请好友
                </button>
            </div>
        </div>

        <!-- 真心话大冒险游戏页 -->
        <div class="page bg-gradient-to-b from-purple-600 to-indigo-800 p-6 mb-12 rounded-xl shadow-lg">
            <div class="page-title">
                <div class="absolute left-0 top-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                </div>
                <h2 class="text-2xl font-bold text-center text-white">真心话大冒险</h2>
                <div class="absolute right-0 top-1">
                    <span class="text-sm bg-white text-purple-700 py-1 px-2 rounded-full">第2轮</span>
                </div>
            </div>

            <div class="flex justify-center my-8">
                <div class="relative">
                    <div
                        class="w-64 h-64 rounded-full border-8 border-white bg-gradient-to-br from-pink-500 to-red-500 flex items-center justify-center shadow-xl">
                        <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=150&q=80"
                            alt="当前玩家" class="w-32 h-32 rounded-full border-4 border-white">
                    </div>
                    <div class="absolute top-0 left-0 w-full h-full flex items-center justify-center">
                        <div class="text-center">
                            <p class="text-white font-bold text-xl mb-1">小红</p>
                            <p class="text-white text-sm">选择真心话还是大冒险？</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex justify-center space-x-4 mb-8">
                <button
                    class="bg-pink-500 text-white font-bold py-3 px-8 rounded-full shadow-lg hover:bg-pink-600 transition duration-300">
                    真心话
                </button>
                <button
                    class="bg-orange-500 text-white font-bold py-3 px-8 rounded-full shadow-lg hover:bg-orange-600 transition duration-300">
                    大冒险
                </button>
            </div>

            <div class="bg-white bg-opacity-20 p-4 rounded-lg mb-6">
                <h3 class="font-bold text-white mb-2">游戏记录</h3>
                <div class="space-y-2">
                    <div class="flex items-center">
                        <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                            alt="用户头像" class="w-8 h-8 rounded-full mr-2">
                        <div>
                            <p class="text-xs text-white opacity-80">小明</p>
                            <p class="text-sm text-white">选择了大冒险：模仿一种动物叫声</p>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <img src="https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                            alt="用户头像" class="w-8 h-8 rounded-full mr-2">
                        <div>
                            <p class="text-xs text-white opacity-80">小刚</p>
                            <p class="text-sm text-white">选择了真心话：你最喜欢的一部电影是什么？</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex justify-center">
                <button
                    class="bg-white text-purple-700 font-bold py-3 px-8 rounded-full shadow-lg hover:bg-purple-100 transition duration-300">
                    跳过 (1/3)
                </button>
            </div>
        </div>

        <!-- 你画我猜游戏页 -->
        <div class="page bg-white p-6 mb-12 rounded-xl shadow-lg">
            <div class="page-title">
                <div class="absolute left-0 top-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-600" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                </div>
                <h2 class="text-2xl font-bold text-center">你画我猜</h2>
                <div class="absolute right-0 top-1">
                    <span class="text-sm bg-green-100 text-green-700 py-1 px-2 rounded-full">剩余时间: 45秒</span>
                </div>
            </div>

            <div class="mb-4 text-center">
                <p class="text-gray-500">小明正在画画，大家猜一猜是什么？</p>
            </div>

            <div class="bg-gray-100 rounded-lg p-2 mb-4">
                <div class="bg-white rounded-lg h-64 flex items-center justify-center border-2 border-gray-200">
                    <img src="https://images.unsplash.com/photo-1579762593175-20226054cad0?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80"
                        alt="画板" class="max-h-full">
                </div>
            </div>

            <div class="grid grid-cols-4 gap-2 mb-6">
                <div class="flex flex-col items-center">
                    <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                        alt="用户头像" class="w-12 h-12 rounded-full border-2 border-green-500">
                    <p class="mt-1 text-xs font-medium">小明</p>
                    <span class="text-xs bg-green-100 text-green-700 py-0.5 px-1 rounded-full">画画</span>
                </div>
                <div class="flex flex-col items-center">
                    <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                        alt="用户头像" class="w-12 h-12 rounded-full">
                    <p class="mt-1 text-xs font-medium">小红</p>
                    <span class="text-xs bg-gray-100 text-gray-700 py-0.5 px-1 rounded-full">0分</span>
                </div>
                <div class="flex flex-col items-center">
                    <img src="https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                        alt="用户头像" class="w-12 h-12 rounded-full">
                    <p class="mt-1 text-xs font-medium">小刚</p>
                    <span class="text-xs bg-blue-100 text-blue-700 py-0.5 px-1 rounded-full">2分</span>
                </div>
                <div class="flex flex-col items-center">
                    <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                        alt="用户头像" class="w-12 h-12 rounded-full">
                    <p class="mt-1 text-xs font-medium">小丽</p>
                    <span class="text-xs bg-gray-100 text-gray-700 py-0.5 px-1 rounded-full">0分</span>
                </div>
            </div>

            <div class="bg-gray-50 p-4 rounded-lg mb-4">
                <div class="space-y-2 mb-3 h-24 overflow-y-auto">
                    <div class="flex items-start">
                        <img src="https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                            alt="用户头像" class="w-6 h-6 rounded-full mr-1">
                        <p class="text-sm">猫？</p>
                    </div>
                    <div class="flex items-start">
                        <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                            alt="用户头像" class="w-6 h-6 rounded-full mr-1">
                        <p class="text-sm">是不是一只狗？</p>
                    </div>
                    <div class="flex items-start">
                        <img src="https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                            alt="用户头像" class="w-6 h-6 rounded-full mr-1">
                        <p class="text-sm">我猜是老虎！</p>
                    </div>
                    <div class="flex items-start text-green-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-1" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <p class="text-sm">小刚猜对了！是老虎</p>
                    </div>
                </div>
                <div class="flex">
                    <input type="text" placeholder="猜一猜..."
                        class="flex-1 border border-gray-300 rounded-lg py-2 px-4 mr-2">
                    <button class="bg-green-500 text-white py-2 px-4 rounded-lg">发送</button>
                </div>
            </div>

            <div class="flex justify-between">
                <div class="text-center">
                    <p class="text-sm text-gray-500">当前轮数</p>
                    <p class="font-bold">2/6</p>
                </div>
                <div class="text-center">
                    <p class="text-sm text-gray-500">你的得分</p>
                    <p class="font-bold">0</p>
                </div>
                <div class="text-center">
                    <p class="text-sm text-gray-500">最高分</p>
                    <p class="font-bold">小刚 2分</p>
                </div>
            </div>
        </div>

        <!-- 谁是卧底游戏页 -->
        <div class="page bg-gradient-to-b from-yellow-500 to-orange-500 p-6 mb-12 rounded-xl shadow-lg">
            <div class="page-title">
                <div class="absolute left-0 top-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                </div>
                <h2 class="text-2xl font-bold text-center text-white">谁是卧底</h2>
                <div class="absolute right-0 top-1">
                    <span class="text-sm bg-white text-orange-700 py-1 px-2 rounded-full">第1轮</span>
                </div>
            </div>

            <div class="bg-white rounded-lg p-4 mb-6 shadow-lg">
                <div class="text-center mb-4">
                    <h3 class="font-bold text-xl text-orange-600">你的词语</h3>
                    <p class="text-3xl font-bold mt-2">手机</p>
                    <p class="text-gray-500 text-sm mt-1">请记住你的词语，不要让其他人看到</p>
                </div>
                <div class="border-t border-gray-200 pt-3">
                    <p class="text-sm text-gray-600">游戏规则：</p>
                    <p class="text-xs text-gray-500">1. 每人轮流描述自己拿到的词语，不能直接说出词语</p>
                    <p class="text-xs text-gray-500">2. 卧底拿到的是与大家不同但相关的词语</p>
                    <p class="text-xs text-gray-500">3. 每轮描述后，所有人投票选出可疑的卧底</p>
                </div>
            </div>

            <div class="bg-white bg-opacity-20 p-4 rounded-lg mb-6">
                <h3 class="font-bold text-white mb-3">玩家状态</h3>
                <div class="grid grid-cols-4 gap-2">
                    <div class="flex flex-col items-center">
                        <div class="relative">
                            <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                                alt="用户头像" class="w-12 h-12 rounded-full border-2 border-white">
                            <span
                                class="absolute -top-1 -right-1 bg-green-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">1</span>
                        </div>
                        <p class="mt-1 text-xs font-medium text-white">小明</p>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="relative">
                            <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                                alt="用户头像" class="w-12 h-12 rounded-full border-2 border-white">
                            <span
                                class="absolute -top-1 -right-1 bg-green-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">2</span>
                        </div>
                        <p class="mt-1 text-xs font-medium text-white">小红</p>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="relative">
                            <img src="https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                                alt="用户头像" class="w-12 h-12 rounded-full border-2 border-white">
                            <span
                                class="absolute -top-1 -right-1 bg-green-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">3</span>
                        </div>
                        <p class="mt-1 text-xs font-medium text-white">小刚</p>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="relative">
                            <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                                alt="用户头像" class="w-12 h-12 rounded-full border-2 border-white">
                            <span
                                class="absolute -top-1 -right-1 bg-green-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">4</span>
                        </div>
                        <p class="mt-1 text-xs font-medium text-white">小丽</p>
                    </div>
                </div>
            </div>

            <div class="bg-white bg-opacity-20 p-4 rounded-lg mb-6">
                <h3 class="font-bold text-white mb-2">描述记录</h3>
                <div class="space-y-2">
                    <div class="flex items-start">
                        <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                            alt="用户头像" class="w-8 h-8 rounded-full mr-2">
                        <div>
                            <p class="text-xs text-white opacity-80">小明</p>
                            <p class="text-sm text-white">这是一个我们每天都会用到的东西</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                            alt="用户头像" class="w-8 h-8 rounded-full mr-2">
                        <div>
                            <p class="text-xs text-white opacity-80">小红</p>
                            <p class="text-sm text-white">它可以用来联系别人</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex justify-center">
                <button
                    class="bg-white text-orange-700 font-bold py-3 px-8 rounded-full shadow-lg hover:bg-orange-100 transition duration-300">
                    轮到你描述
                </button>
            </div>
        </div>

        <!-- 个人中心页 -->
        <div class="page bg-white p-6 mb-12 rounded-xl shadow-lg">
            <div class="page-title">
                <h2 class="text-2xl font-bold text-center">个人中心</h2>
            </div>

            <div class="flex items-center mb-8">
                <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=150&q=80"
                    alt="用户头像" class="w-20 h-20 rounded-full mr-4 border-2 border-purple-500">
                <div>
                    <h3 class="font-bold text-xl">小明</h3>
                    <p class="text-gray-500">游戏达人</p>
                    <div class="flex items-center mt-1">
                        <div class="bg-purple-100 text-purple-700 text-xs py-0.5 px-2 rounded-full mr-2">
                            Lv.5
                        </div>
                        <div class="bg-blue-100 text-blue-700 text-xs py-0.5 px-2 rounded-full">
                            已玩42局
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-3 gap-4 mb-8">
                <div class="bg-purple-50 p-3 rounded-lg text-center">
                    <p class="text-2xl font-bold text-purple-700">42</p>
                    <p class="text-xs text-gray-600">游戏局数</p>
                </div>
                <div class="bg-green-50 p-3 rounded-lg text-center">
                    <p class="text-2xl font-bold text-green-700">28</p>
                    <p class="text-xs text-gray-600">胜利次数</p>
                </div>
                <div class="bg-blue-50 p-3 rounded-lg text-center">
                    <p class="text-2xl font-bold text-blue-700">67%</p>
                    <p class="text-xs text-gray-600">胜率</p>
                </div>
            </div>

            <div class="mb-8">
                <h3 class="font-bold mb-3">游戏统计</h3>
                <div class="space-y-3">
                    <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                        <div class="w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center mr-3">
                            <span class="text-pink-600 font-bold text-xs">真</span>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium">真心话大冒险</p>
                            <div class="flex items-center">
                                <div class="h-2 bg-gray-200 rounded-full flex-1">
                                    <div class="h-2 bg-pink-500 rounded-full" style="width: 75%"></div>
                                </div>
                                <span class="text-sm text-gray-600 ml-2">15局</span>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                        <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                            <span class="text-green-600 font-bold text-xs">你</span>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium">你画我猜</p>
                            <div class="flex items-center">
                                <div class="h-2 bg-gray-200 rounded-full flex-1">
                                    <div class="h-2 bg-green-500 rounded-full" style="width: 60%"></div>
                                </div>
                                <span class="text-sm text-gray-600 ml-2">12局</span>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                        <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                            <span class="text-yellow-600 font-bold text-xs">谁</span>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium">谁是卧底</p>
                            <div class="flex items-center">
                                <div class="h-2 bg-gray-200 rounded-full flex-1">
                                    <div class="h-2 bg-yellow-500 rounded-full" style="width: 45%"></div>
                                </div>
                                <span class="text-sm text-gray-600 ml-2">9局</span>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <span class="text-blue-600 font-bold text-xs">猜</span>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium">疯狂猜词</p>
                            <div class="flex items-center">
                                <div class="h-2 bg-gray-200 rounded-full flex-1">
                                    <div class="h-2 bg-blue-500 rounded-full" style="width: 30%"></div>
                                </div>
                                <span class="text-sm text-gray-600 ml-2">6局</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mb-8">
                <h3 class="font-bold mb-3">好友排行</h3>
                <div class="space-y-3">
                    <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                        <div
                            class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-3 text-yellow-700 font-bold">
                            1
                        </div>
                        <img src="https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                            alt="用户头像" class="w-10 h-10 rounded-full mr-3">
                        <div class="flex-1">
                            <p class="font-medium">小刚</p>
                            <p class="text-xs text-gray-500">游戏达人</p>
                        </div>
                        <p class="font-bold text-yellow-600">56局</p>
                    </div>
                    <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                        <div
                            class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center mr-3 text-gray-700 font-bold">
                            2
                        </div>
                        <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                            alt="用户头像" class="w-10 h-10 rounded-full mr-3">
                        <div class="flex-1">
                            <p class="font-medium">小明 (你)</p>
                            <p class="text-xs text-gray-500">游戏达人</p>
                        </div>
                        <p class="font-bold text-gray-600">42局</p>
                    </div>
                    <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                        <div
                            class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-3 text-orange-700 font-bold">
                            3
                        </div>
                        <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                            alt="用户头像" class="w-10 h-10 rounded-full mr-3">
                        <div class="flex-1">
                            <p class="font-medium">小红</p>
                            <p class="text-xs text-gray-500">游戏新手</p>
                        </div>
                        <p class="font-bold text-gray-600">38局</p>
                    </div>
                </div>
            </div>

            <div class="flex justify-around pt-4 border-t border-gray-200">
                <button class="flex flex-col items-center text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                    <span class="text-xs mt-1">首页</span>
                </button>
                <button class="flex flex-col items-center text-purple-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <span class="text-xs mt-1">我的</span>
                </button>
                <button class="flex flex-col items-center text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <span class="text-xs mt-1">排行</span>
                </button>
                <button class="flex flex-col items-center text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <span class="text-xs mt-1">设置</span>
                </button>
            </div>
        </div>

        <!-- 排行榜页 -->
        <div class="page bg-white p-6 mb-12 rounded-xl shadow-lg">
            <div class="page-title">
                <h2 class="text-2xl font-bold text-center">排行榜</h2>
            </div>

            <div class="flex justify-center mb-8">
                <div class="flex items-end space-x-4">
                    <div class="flex flex-col items-center">
                        <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=150&q=80"
                            alt="第二名" class="w-16 h-16 rounded-full border-2 border-gray-300">
                        <div class="bg-gray-200 rounded-t-lg w-16 h-24 flex items-center justify-center mt-2">
                            <span class="text-gray-700 font-bold">2</span>
                        </div>
                        <p class="text-sm font-medium mt-1">小红</p>
                        <p class="text-xs text-gray-500">1024分</p>
                    </div>
                    <div class="flex flex-col items-center">
                        <img src="https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-1.2.1&auto=format&fit=crop&w=150&q=80"
                            alt="第一名" class="w-20 h-20 rounded-full border-4 border-yellow-400">
                        <div class="bg-yellow-400 rounded-t-lg w-20 h-32 flex items-center justify-center mt-2">
                            <span class="text-white font-bold text-xl">1</span>
                        </div>
                        <p class="text-sm font-bold mt-1">小刚</p>
                        <p class="text-xs text-gray-500">1356分</p>
                    </div>
                    <div class="flex flex-col items-center">
                        <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=150&q=80"
                            alt="第三名" class="w-16 h-16 rounded-full border-2 border-orange-300">
                        <div class="bg-orange-300 rounded-t-lg w-16 h-20 flex items-center justify-center mt-2">
                            <span class="text-white font-bold">3</span>
                        </div>
                        <p class="text-sm font-medium mt-1">小丽</p>
                        <p class="text-xs text-gray-500">876分</p>
                    </div>
                </div>
            </div>

            <div class="mb-4">
                <div class="flex justify-between mb-2">
                    <h3 class="font-bold">好友排行</h3>
                    <div class="flex space-x-2">
                        <button class="bg-purple-600 text-white text-xs py-1 px-3 rounded-full">总榜</button>
                        <button class="bg-gray-200 text-gray-700 text-xs py-1 px-3 rounded-full">周榜</button>
                        <button class="bg-gray-200 text-gray-700 text-xs py-1 px-3 rounded-full">月榜</button>
                    </div>
                </div>

                <div class="space-y-3">
                    <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                        <div
                            class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-3 text-yellow-700 font-bold">
                            1
                        </div>
                        <img src="https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                            alt="用户头像" class="w-10 h-10 rounded-full mr-3">
                        <div class="flex-1">
                            <p class="font-medium">小刚</p>
                            <div class="flex items-center">
                                <div class="h-2 bg-gray-200 rounded-full w-24">
                                    <div class="h-2 bg-yellow-500 rounded-full w-full"></div>
                                </div>
                                <span class="text-xs text-gray-500 ml-2">1356分</span>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                        <div
                            class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center mr-3 text-gray-700 font-bold">
                            2
                        </div>
                        <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                            alt="用户头像" class="w-10 h-10 rounded-full mr-3">
                        <div class="flex-1">
                            <p class="font-medium">小红</p>
                            <div class="flex items-center">
                                <div class="h-2 bg-gray-200 rounded-full w-24">
                                    <div class="h-2 bg-gray-500 rounded-full" style="width: 75%"></div>
                                </div>
                                <span class="text-xs text-gray-500 ml-2">1024分</span>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                        <div
                            class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-3 text-orange-700 font-bold">
                            3
                        </div>
                        <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                            alt="用户头像" class="w-10 h-10 rounded-full mr-3">
                        <div class="flex-1">
                            <p class="font-medium">小丽</p>
                            <div class="flex items-center">
                                <div class="h-2 bg-gray-200 rounded-full w-24">
                                    <div class="h-2 bg-orange-500 rounded-full" style="width: 65%"></div>
                                </div>
                                <span class="text-xs text-gray-500 ml-2">876分</span>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                        <div
                            class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3 text-blue-700 font-bold">
                            4
                        </div>
                        <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                            alt="用户头像" class="w-10 h-10 rounded-full mr-3">
                        <div class="flex-1">
                            <p class="font-medium">小明 (你)</p>
                            <div class="flex items-center">
                                <div class="h-2 bg-gray-200 rounded-full w-24">
                                    <div class="h-2 bg-blue-500 rounded-full" style="width: 60%"></div>
                                </div>
                                <span class="text-xs text-gray-500 ml-2">812分</span>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                        <div
                            class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3 text-gray-700 font-bold">
                            5
                        </div>
                        <img src="https://images.unsplash.com/photo-1527980965255-d3b416303d12?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                            alt="用户头像" class="w-10 h-10 rounded-full mr-3">
                        <div class="flex-1">
                            <p class="font-medium">小华</p>
                            <div class="flex items-center">
                                <div class="h-2 bg-gray-200 rounded-full w-24">
                                    <div class="h-2 bg-gray-500 rounded-full" style="width: 50%"></div>
                                </div>
                                <span class="text-xs text-gray-500 ml-2">678分</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="font-bold mb-3">游戏排行</h3>
                <div class="grid grid-cols-2 gap-3">
                    <div class="bg-pink-50 p-3 rounded-lg">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center mr-2">
                                <span class="text-pink-600 font-bold text-xs">真</span>
                            </div>
                            <p class="font-medium text-sm">真心话大冒险</p>
                        </div>
                        <div class="flex items-center">
                            <img src="https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                                alt="用户头像" class="w-6 h-6 rounded-full mr-1">
                            <p class="text-xs text-gray-600">小刚 - 356分</p>
                        </div>
                    </div>
                    <div class="bg-green-50 p-3 rounded-lg">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-2">
                                <span class="text-green-600 font-bold text-xs">你</span>
                            </div>
                            <p class="font-medium text-sm">你画我猜</p>
                        </div>
                        <div class="flex items-center">
                            <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                                alt="用户头像" class="w-6 h-6 rounded-full mr-1">
                            <p class="text-xs text-gray-600">小红 - 412分</p>
                        </div>
                    </div>
                    <div class="bg-yellow-50 p-3 rounded-lg">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-2">
                                <span class="text-yellow-600 font-bold text-xs">谁</span>
                            </div>
                            <p class="font-medium text-sm">谁是卧底</p>
                        </div>
                        <div class="flex items-center">
                            <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                                alt="用户头像" class="w-6 h-6 rounded-full mr-1">
                            <p class="text-xs text-gray-600">小明 - 328分</p>
                        </div>
                    </div>
                    <div class="bg-blue-50 p-3 rounded-lg">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                                <span class="text-blue-600 font-bold text-xs">猜</span>
                            </div>
                            <p class="font-medium text-sm">疯狂猜词</p>
                        </div>
                        <div class="flex items-center">
                            <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80"
                                alt="用户头像" class="w-6 h-6 rounded-full mr-1">
                            <p class="text-xs text-gray-600">小丽 - 296分</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex justify-around pt-4 border-t border-gray-200">
                <button class="flex flex-col items-center text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                    <span class="text-xs mt-1">首页</span>
                </button>
                <button class="flex flex-col items-center text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <span class="text-xs mt-1">我的</span>
                </button>
                <button class="flex flex-col items-center text-purple-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <span class="text-xs mt-1">排行</span>
                </button>
                <button class="flex flex-col items-center text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <span class="text-xs mt-1">设置</span>
                </button>
            </div>
        </div>

        <!-- 设置页 -->
        <div class="page bg-white p-6 mb-12 rounded-xl shadow-lg">
            <div class="page-title">
                <h2 class="text-2xl font-bold text-center">设置</h2>
            </div>

            <div class="mb-8">
                <h3 class="font-bold mb-3">基本设置</h3>
                <div class="space-y-4">
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 mr-3" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15.536a5 5 0 001.414 1.414m2.828-9.9a9 9 0 012.728-2.728" />
                            </svg>
                            <span>声音效果</span>
                        </div>
                        <div class="relative inline-block w-12 h-6 rounded-full bg-purple-500">
                            <div class="absolute right-1 top-1 w-4 h-4 rounded-full bg-white"></div>
                        </div>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 mr-3" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                            <span>震动反馈</span>
                        </div>
                        <div class="relative inline-block w-12 h-6 rounded-full bg-purple-500">
                            <div class="absolute right-1 top-1 w-4 h-4 rounded-full bg-white"></div>
                        </div>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 mr-3" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                            </svg>
                            <span>游戏通知</span>
                        </div>
                        <div class="relative inline-block w-12 h-6 rounded-full bg-gray-300">
                            <div class="absolute left-1 top-1 w-4 h-4 rounded-full bg-white"></div>
                        </div>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 mr-3" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                            </svg>
                            <span>隐私设置</span>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </div>
                </div>
            </div>

            <div class="mb-8">
                <h3 class="font-bold mb-3">游戏设置</h3>
                <div class="space-y-4">
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 mr-3" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span>游戏时长提醒</span>
                        </div>
                        <div class="relative inline-block w-12 h-6 rounded-full bg-purple-500">
                            <div class="absolute right-1 top-1 w-4 h-4 rounded-full bg-white"></div>
                        </div>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 mr-3" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            <span>自动接受好友邀请</span>
                        </div>
                        <div class="relative inline-block w-12 h-6 rounded-full bg-gray-300">
                            <div class="absolute left-1 top-1 w-4 h-4 rounded-full bg-white"></div>
                        </div>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 mr-3" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                            </svg>
                            <span>游戏难度设置</span>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 mr-3" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <span>自定义游戏背景</span>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </div>
                </div>
            </div>

            <div class="mb-8">
                <h3 class="font-bold mb-3">账号设置</h3>
                <div class="space-y-4">
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 mr-3" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            <span>个人资料</span>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 mr-3" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                            </svg>
                            <span>账号安全</span>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 mr-3" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                            </svg>
                            <span>支付管理</span>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </div>
                </div>
            </div>

            <div class="mb-8">
                <h3 class="font-bold mb-3">其他</h3>
                <div class="space-y-4">
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 mr-3" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span>帮助中心</span>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 mr-3" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                            </svg>
                            <span>用户协议</span>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 mr-3" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span>关于我们</span>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </div>
                </div>
            </div>

            <button
                class="w-full bg-red-500 text-white font-bold py-3 px-6 rounded-lg shadow-md hover:bg-red-600 transition duration-300">
                退出登录
            </button>

            <div class="flex justify-around pt-4 mt-6 border-t border-gray-200">
                <button class="flex flex-col items-center text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                    <span class="text-xs mt-1">首页</span>
                </button>
                <button class="flex flex-col items-center text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <span class="text-xs mt-1">我的</span>
                </button>
                <button class="flex flex-col items-center text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <span class="text-xs mt-1">排行</span>
                </button>
                <button class="flex flex-col items-center text-purple-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <span class="text-xs mt-1">设置</span>
                </button>
            </div>
        </div>
    </div>
</body>

</html>