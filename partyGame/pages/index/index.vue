<template>
    <view class="container">
        <view class="status-bar"></view>

        <!-- 背景动画元素 -->
        <view class="bg-elements">
            <view class="floating-icon moon"></view>
            <view class="floating-icon star star-1"></view>
            <view class="floating-icon star star-2"></view>
            <view class="floating-icon star star-3"></view>
            <view class="floating-icon card card-1"></view>
            <view class="floating-icon card card-2"></view>
        </view>

        <!-- 标题部分 -->
        <view class="header">
            <view class="title-container">
                <view class="logo-container">
                    <view class="logo-circle pulse-animation"></view>
                    <view class="logo-mask">
                        <text class="logo-text">聚</text>
                    </view>
                </view>
                <view class="title-text">
                    <text class="title">聚会游戏</text>
                    <text class="subtitle">让线下游戏更精彩</text>
                </view>
            </view>
        </view>

        <!-- 游戏选择卡片 -->
        <view class="games-container">
            <!-- 狼人杀游戏卡片 -->
            <view class="game-card werewolf" @click="navigateTo('wolf')">
                <view class="card-icon-container">
                    <view class="card-icon wolf-icon">
                        <view class="wolf-eyes">
                            <view class="eye left"></view>
                            <view class="eye right"></view>
                        </view>
                    </view>
                </view>
                <view class="card-content">
                    <view class="card-text">
                        <text class="card-title">狼人杀发牌助手</text>
                        <text class="card-desc">谁是狼人？谁是好人？</text>
                    </view>
                    <view class="card-features">
                        <text class="feature-tag">角色分配</text>
                        <text class="feature-tag">身份查看</text>
                        <text class="feature-tag">游戏规则</text>
                    </view>
                </view>
                <view class="card-action">
                    <text>立即开始</text>
                    <text class="action-arrow">→</text>
                </view>
                <view class="card-decoration wolf-moon"></view>
                <view class="card-decoration wolf-shadow"></view>
            </view>

            <!-- 谁是卧底游戏卡片 -->
            <view class="game-card undercover" @click="navigateTo('undercover')">
                <view class="card-icon-container">
                    <view class="card-icon spy-icon">
                        <view class="spy-glasses">
                            <view class="glass left"></view>
                            <view class="glass right"></view>
                        </view>
                    </view>
                </view>
                <view class="card-content">
                    <view class="card-text">
                        <text class="card-title">谁是卧底领词助手</text>
                        <text class="card-desc">一样的词不一样的命运</text>
                    </view>
                    <view class="card-features">
                        <text class="feature-tag">词语分配</text>
                        <text class="feature-tag">词库选择</text>
                        <text class="feature-tag">游戏记录</text>
                    </view>
                </view>
                <view class="card-action">
                    <text>立即开始</text>
                    <text class="action-arrow">→</text>
                </view>
                <view class="card-decoration spy-spot spy-spot-1"></view>
                <view class="card-decoration spy-spot spy-spot-2"></view>
                <view class="card-decoration spy-spot spy-spot-3"></view>
            </view>
        </view>

        <!-- 版本信息 -->
        <view class="footer">
            <text class="version-text">v1.0.0</text>
        </view>
    </view>
</template>

<script>
    export default {
        data() {
            return {
                title: '聚会游戏助手'
            }
        },
        onLoad() {
            console.log('首页加载')
        },
        methods: {
            navigateTo(gameType) {
                let url = '';

                switch (gameType) {
                    case 'wolf':
                        // 狼人杀页面
                        url = '/wolfkiller/pages/index/index';
                        break;
                    case 'undercover':
                        // 谁是卧底页面
                        url = '/partyGame/pages/index/index';
                        break;
                    default:
                        console.error('未知游戏类型:', gameType);
                        return;
                }

                console.log('navigateTo:', url);

                uni.navigateTo({
                    url: url,
                    fail: (err) => {
                        console.error('导航失败:', err);
                        uni.reLaunch({
                            url: url,
                            fail: (err2) => {
                                console.error('reLaunch也失败:', err2);
                                uni.showToast({
                                    title: '页面跳转失败',
                                    icon: 'none'
                                });
                            }
                        });
                    }
                });
            }
        }
    }
</script>

<style scoped>
    /* 基础容器样式 */
    .container {
        min-height: 100vh;
        background: linear-gradient(135deg, #13172a 0%, #2a314f 100%);
        display: flex;
        flex-direction: column;
        padding-bottom: 60rpx;
        position: relative;
        overflow: hidden;
    }

    /* 状态栏高度 */
    .status-bar {
        height: var(--status-bar-height);
    }

    /* 背景动画元素 */
    .bg-elements {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 0;
    }

    .floating-icon {
        position: absolute;
        opacity: 0.15;
    }

    .moon {
        top: 15%;
        right: 10%;
        width: 100rpx;
        height: 100rpx;
        border-radius: 50%;
        background: #ffd54f;
        box-shadow: 0 0 30rpx #ffd54f;
        animation: float 8s ease-in-out infinite;
    }

    .star {
        background: #ffffff;
        border-radius: 50%;
        box-shadow: 0 0 20rpx #ffffff;
    }

    .star-1 {
        top: 25%;
        left: 15%;
        width: 20rpx;
        height: 20rpx;
        animation: twinkle 4s ease-in-out infinite;
    }

    .star-2 {
        top: 40%;
        right: 20%;
        width: 14rpx;
        height: 14rpx;
        animation: twinkle 6s ease-in-out 1s infinite;
    }

    .star-3 {
        top: 60%;
        left: 25%;
        width: 16rpx;
        height: 16rpx;
        animation: twinkle 5s ease-in-out 2s infinite;
    }

    .card {
        border-radius: 10rpx;
        transform: rotate(15deg);
    }

    .card-1 {
        bottom: 20%;
        left: 8%;
        width: 60rpx;
        height: 80rpx;
        background: rgba(255, 255, 255, 0.2);
        animation: float 7s ease-in-out 1s infinite;
    }

    .card-2 {
        bottom: 15%;
        right: 10%;
        width: 70rpx;
        height: 90rpx;
        background: rgba(255, 255, 255, 0.15);
        animation: float 9s ease-in-out 2s infinite;
    }

    @keyframes float {

        0%,
        100% {
            transform: translateY(0) rotate(15deg);
        }

        50% {
            transform: translateY(-20rpx) rotate(15deg);
        }
    }

    @keyframes twinkle {

        0%,
        100% {
            opacity: 0.15;
            transform: scale(1);
        }

        50% {
            opacity: 0.4;
            transform: scale(1.3);
        }
    }

    /* 标题部分 */
    .header {
        padding: 200rpx 40rpx 50rpx;
        position: relative;
        z-index: 1;
    }

    .title-container {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 40rpx;
    }

    .logo-container {
        position: relative;
        width: 80rpx;
        height: 80rpx;
        margin-right: 20rpx;
    }

    .logo-circle {
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: linear-gradient(135deg, #ff7e5f, #feb47b);
        animation: pulse 2s infinite;
    }

    .logo-mask {
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
    }

    .logo-text {
        color: white;
        font-size: 40rpx;
        font-weight: bold;
    }

    .title-text {
        display: flex;
        flex-direction: column;
    }

    .title {
        font-size: 52rpx;
        color: #ffffff;
        font-weight: bold;
        margin-bottom: 8rpx;
        text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        background: linear-gradient(to right, #ff7e5f, #feb47b);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .subtitle {
        font-size: 28rpx;
        color: rgba(255, 255, 255, 0.8);
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
            opacity: 1;
        }

        50% {
            transform: scale(1.1);
            opacity: 0.8;
        }

        100% {
            transform: scale(1);
            opacity: 1;
        }
    }

    /* 游戏卡片容器 */
    .games-container {
        padding: 0 40rpx;
        display: flex;
        flex-direction: column;
        gap: 50rpx;
        position: relative;
        z-index: 1;
    }

    /* 游戏卡片基础样式 */
    .game-card {
        border-radius: 24rpx;
        overflow: hidden;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
        display: flex;
        flex-direction: column;
        position: relative;
        height: auto;
    }

    /* 游戏卡片图标容器 */
    .card-icon-container {
        position: absolute;
        top: 40rpx;
        right: 40rpx;
        z-index: 2;
    }

    .card-icon {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* 狼人眼睛图标 */
    .wolf-icon {
        background: #d92a2a;
        box-shadow: 0 5px 15px rgba(217, 42, 42, 0.4);
    }

    .wolf-eyes {
        display: flex;
        width: 50rpx;
        justify-content: space-between;
    }

    .eye {
        width: 18rpx;
        height: 18rpx;
        border-radius: 50%;
        background: #ffcc00;
        position: relative;
    }

    .eye:after {
        content: '';
        position: absolute;
        top: 25%;
        left: 25%;
        width: 50%;
        height: 50%;
        border-radius: 50%;
        background: #000;
        animation: eyeMove 5s ease-in-out infinite;
    }

    @keyframes eyeMove {

        0%,
        100% {
            transform: translate(0, 0);
        }

        25% {
            transform: translate(2rpx, 2rpx);
        }

        50% {
            transform: translate(-2rpx, 0);
        }

        75% {
            transform: translate(0, -2rpx);
        }
    }

    /* 卧底眼镜图标 */
    .spy-icon {
        background: #4d7eff;
        box-shadow: 0 5px 15px rgba(77, 126, 255, 0.4);
    }

    .spy-glasses {
        display: flex;
        width: 60rpx;
        justify-content: space-between;
        align-items: center;
    }

    .glass {
        width: 22rpx;
        height: 22rpx;
        border-radius: 50%;
        border: 3rpx solid #fff;
        background: transparent;
    }

    .glass:after {
        content: '';
        position: absolute;
        width: 12rpx;
        height: 3rpx;
        background: #fff;
    }

    /* 卡片装饰元素 */
    .card-decoration {
        position: absolute;
        z-index: 1;
    }

    .wolf-moon {
        top: 40rpx;
        left: 180rpx;
        width: 100rpx;
        height: 100rpx;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
    }

    .wolf-shadow {
        bottom: 40rpx;
        right: 40rpx;
        width: 120rpx;
        height: 120rpx;
        opacity: 0.1;
        background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZmZmZmZmIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgY2xhc3M9ImZlYXRoZXIgZmVhdGhlci1tb29uIj48cGF0aCBkPSJNMjEgMTIuNzlBOSA5IDAgMSAxIDExLjIxIDMgNyA3IDAgMCAwIDIxIDEyLjc5eiI+PC9wYXRoPjwvc3ZnPg==');
        background-size: contain;
        background-repeat: no-repeat;
    }

    .spy-spot {
        width: 40rpx;
        height: 40rpx;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.07);
    }

    .spy-spot-1 {
        top: 60rpx;
        left: 60rpx;
        width: 60rpx;
        height: 60rpx;
    }

    .spy-spot-2 {
        top: 120rpx;
        left: 180rpx;
        width: 30rpx;
        height: 30rpx;
    }

    .spy-spot-3 {
        bottom: 40rpx;
        right: 120rpx;
        width: 50rpx;
        height: 50rpx;
    }

    /* 游戏卡片内容 */
    .card-content {
        flex: 1;
        padding: 40rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        position: relative;
        z-index: 2;
    }

    /* 卡片文字内容 */
    .card-text {
        margin-bottom: 20rpx;
    }

    .card-title {
        font-size: 46rpx;
        color: #ffffff;
        font-weight: bold;
        margin-bottom: 16rpx;
        display: block;
        text-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }

    .card-desc {
        font-size: 28rpx;
        color: rgba(255, 255, 255, 0.9);
        display: block;
    }

    /* 功能标签 */
    .card-features {
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;
        margin-top: 16rpx;
    }

    .feature-tag {
        font-size: 22rpx;
        color: rgba(255, 255, 255, 0.9);
        background-color: rgba(255, 255, 255, 0.15);
        padding: 8rpx 24rpx;
        border-radius: 100rpx;
        backdrop-filter: blur(4px);
    }

    /* 开始游戏按钮 */
    .card-action {
        padding: 24rpx 40rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #ffffff;
        font-weight: bold;
        letter-spacing: 4rpx;
        font-size: 30rpx;
        position: relative;
        z-index: 2;
    }

    .action-arrow {
        font-size: 36rpx;
        transition: transform 0.3s ease;
    }

    .game-card:active .action-arrow {
        transform: translateX(10rpx);
    }

    /* 狼人杀卡片样式 */
    .werewolf {
        background: linear-gradient(135deg, #2a2f45 0%, #8d2e2e 100%);
    }

    .werewolf .card-action {
        background: linear-gradient(90deg, #a61e1e, #d92a2a);
    }

    /* 谁是卧底卡片样式 */
    .undercover {
        background: linear-gradient(135deg, #2a2f45 0%, #2e5b8d 100%);
    }

    .undercover .card-action {
        background: linear-gradient(90deg, #3a56a6, #4d7eff);
    }

    /* 动画效果 */
    .game-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .game-card:active {
        transform: translateY(-5rpx);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    }

    /* 页脚 */
    .footer {
        padding: 40rpx;
        text-align: center;
    }

    .version-text {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.4);
    }
</style>