<template>
    <view class="container">
        <!-- 使用安全头部组件 -->
        <safe-header title="加入游戏" :show-back="true"></safe-header>

        <view class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl shadow-lg p-6 mb-6 text-white">

            <view class="mb-6">
                <text class="block font-medium mb-2">房间码</text>
                <input type="text" v-model="roomCode" placeholder="输入6位房间码"
                    class="w-full px-4 py-2 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg text-white placeholder-white placeholder-opacity-60" />
            </view>

            <button class="w-full bg-red-600 hover-bg-red-700 text-white font-bold py-3 px-4 rounded-lg transition"
                @click="joinGame">
                加入游戏
            </button>
        </view>

        <view class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl shadow-lg p-6 text-white">
            <text class="text-xl font-semibold mb-4 block">扫码加入</text>
            <text class="text-white text-opacity-80 mb-4 block">如果你收到了游戏邀请二维码，可以直接扫码加入</text>

            <button
                class="scan-btn w-full bg-white bg-opacity-20 text-white font-bold py-3 px-4 rounded-lg flex items-center justify-center"
                @click="scanQRCode">
                <text class="fas fa-camera mr-2"></text> 扫描二维码
            </button>
        </view>
    </view>
</template>

<script>
    import { navigateToPage } from '@/utils/game.js';
    import { roomApi } from '@/utils/api.js';
    import SafeHeader from '@/components/SafeHeader.vue';
    import { wolfRoomApi, wolfPlayerApi } from '@/utils/wolfApi.js';

    export default {
        components: {
            SafeHeader
        },
        data() {
            return {
                roomCode: '',
                showPasswordInput: false,
                password: '',
                playerId: '',
                isValid: true
            }
        },
        onLoad(options) {
            // 如果页面传递了房间码，自动填充
            if (options.roomCode) {
                this.roomCode = options.roomCode;
            }
            // 确保玩家ID存在
            if (!getApp().globalData.playerId) {
                getApp().login().then(() => {
                    this.playerId = getApp().globalData.playerId;
                })
            } else {
                this.playerId = getApp().globalData.playerId;
            }
        },
        methods: {
            navigateTo(pageId) {
                uni.navigateTo({
                    url: pageId
                });
            },

            // 加入游戏
            async joinGame() {

                if (!this.roomCode.trim()) {
                    uni.showToast({
                        title: '请输入房间码',
                        icon: 'none'
                    });
                    return;
                }

                // 如果需要密码但没有输入
                if (this.showPasswordInput && !this.password) {
                    uni.showToast({
                        title: '请输入房间密码',
                        icon: 'none'
                    });
                    return;
                }

                try {
                    // 显示加载中
                    uni.showLoading({ title: '加入房间中...' });

                    // 调用API获取房间信息
                    const roomInfo = await wolfRoomApi.getRoomInfoByCode(this.roomCode);

                    if (!roomInfo || !roomInfo.id) {
                        uni.showToast({
                            title: '房间不存在',
                            icon: 'none'
                        });
                        return;
                    }

                    let valid = roomInfo.isValid

                    if (!valid) {
                        uni.showToast({
                            title: '房间号已过期，不能加入',
                            icon: 'none'
                        });
                        return;
                    }


                    // 准备加入房间的数据
                    const joinData = {
                        roomId: roomInfo.id,
                        playerId: this.playerId,
                        password: this.showPasswordInput ? this.password : '',
                    };

                    // 调用API加入房间
                    const joinResult = await wolfRoomApi.joinRoom(joinData);

                    uni.hideLoading();

                    if (joinResult) {
                        // 存储房间信息
                        uni.setStorageSync('wolfRoomInfo', roomInfo);

                        // 跳转到游戏房间页面
                        uni.navigateTo({
                            url: `/wolfkiller/pages/game-room/game-room?roomId=${roomInfo.id}`,
                            fail: (err) => {
                                console.error('页面跳转失败:', err);
                                uni.showToast({
                                    title: '页面跳转失败',
                                    icon: 'none'
                                });
                            }
                        });
                    } else {
                        uni.showToast({
                            title: '房间已满',
                            icon: 'none'
                        });
                    }
                } catch (error) {
                    uni.hideLoading();
                    console.error('加入房间失败:', error);
                    uni.showToast({
                        title: error.message || '加入房间失败',
                        icon: 'none'
                    });
                }
            },

            // 扫描二维码
            scanQRCode() {
                uni.scanCode({
                    scanType: "qrCode",
                    success: (res) => {
                        var path = res.path
                        if (path.startsWith("wolfkiller/game-room")) {
                            var scene = path.split("scene=")[1]
                            uni.navigateTo({
                                url: '/wolfkiller/game-room/game-room?roomId=' + scene
                            })
                        } else {
                            util.showModal("", "此码我不认得呢", false, () => { }, () => { })
                        }
                    },
                    fail: (error) => {
                        console.error('扫描失败:', error);
                        uni.showToast({
                            title: '扫描失败',
                            icon: 'none'
                        });
                    }
                });
            }
        }
    }
</script>

<style>
    .container {
        padding: 32rpx;
        background-color: #2c3e50;
        background-image: url("https://images.unsplash.com/photo-1478131143081-80f7f84ca84d?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80");
        background-size: cover;
        background-attachment: fixed;
        background-position: center;
        min-height: 100vh;
    }

    /* 输入框样式修复 */
    input {
        height: 80rpx;
        box-sizing: border-box;
        color: #fff;
    }

    /* 修复placeholder颜色 */
    .input-field::placeholder {
        color: rgba(255, 255, 255, 0.6);
    }

    /* 输入框背景和边框 */
    .input-field {
        background-color: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    /* 按钮样式 */
    button {
        border: none;
        background-color: transparent;
        padding: 0;
        margin: 0;
        line-height: normal;
    }

    .py-3 {
        padding-top: 12px;
        padding-bottom: 12px;
    }

    /* 修复按钮颜色 */
    .join-btn {
        background-color: #2563eb;
        transition: background-color 0.3s ease;
    }

    .join-btn:active {
        background-color: #1d4ed8;
    }

    .scan-btn {
        background-color: rgba(255, 255, 255, 0.2);
        transition: background-color 0.3s ease;
    }

    .scan-btn:active {
        background-color: rgba(255, 255, 255, 0.3);
    }

    /* 修复flex布局 */
    .flex {
        display: flex;
    }

    /* 修复宽度 */
    .w-full {
        width: 100%;
    }
</style>