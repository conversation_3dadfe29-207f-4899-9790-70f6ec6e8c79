<template>
    <view class="container">
        <view class="text-center mb-8 mt-8">
            <view class="inline-block p-4 bg-red-600 rounded-full mb-4 shadow-lg pulse-animation">
                <text class="fas fa-moon text-white" style="font-size: 40px;"></text>
            </view>
            <text class="text-4xl font-bold text-white mb-2 text-shadow block">狼人杀助手</text>
            <text class="text-xl text-white text-opacity-90 mb-6 block">谁是狼人？谁是好人？一切尽在暗夜之中...</text>
        </view>

        <view class="max-w-md mx-auto glass-effect rounded-xl shadow-2xl p-6 mb-8 text-white">
            <view class="relative overflow-hidden rounded-lg mb-6">
                <image
                    src="https://images.unsplash.com/photo-1568743966689-d37c04538535?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80"
                    alt="狼人杀游戏" class="w-full h-48 object-cover rounded-lg mb-4 hover-scale-105"></image>
                <view class="absolute inset-0 bg-gradient-to-t opacity-70"></view>
                <view class="absolute bottom-0 left-0 p-4">
                    <text class="text-2xl font-bold text-white mb-1 block">夜幕降临</text>
                    <text class="text-white text-opacity-90 block">狼人们正在寻找他们的猎物...</text>
                </view>
            </view>

            <text class="text-white text-opacity-80 mb-6 block">这是一款帮助你在线下狼人杀游戏中分发角色的小工具，无需主持人，让每个人都能参与游戏！</text>

            <view class="flex-col space-y-3">
                <button
                    class="btn-primary bg-red-600 text-white font-bold py-4 px-4 rounded-lg flex items-center justify-center shadow-lg"
                    @click="navigateTo('create-game')">
                    <text class="fas fa-dice mr-2"></text> 来一局
                </button>
                <button
                    class="btn-primary bg-blue-600 text-white font-bold py-4 px-4 rounded-lg flex items-center justify-center shadow-lg"
                    @click="navigateTo('join-game')">
                    <text class="fas fa-sign-in-alt mr-2"></text> 加入游戏
                </button>
                <button
                    class="btn-primary bg-purple-600 text-white font-bold py-4 px-4 rounded-lg flex items-center justify-center shadow-lg"
                    @click="navigateTo('game-rules')">
                    <text class="fas fa-book mr-2"></text> 游戏规则
                </button>
                <button
                    class="btn-primary bg-green-600 text-white font-bold py-4 px-4 rounded-lg flex items-center justify-center shadow-lg"
                    @click="navigateTo('index')">
                    <text class="fas fa-home mr-2"></text> 返回首页
                </button>
            </view>
        </view>

        <view class="max-w-md mx-auto glass-effect rounded-xl shadow-lg p-6 text-white">
            <view class="flex items-center justify-between mb-4">
                <text class="text-xl font-semibold">我的游戏</text>

            </view>

            <view class="space-y-4">
                <!-- 使用v-if显示加载中状态 -->
                <view v-if="isLoading" class="text-center py-4">
                    <text class="text-white">加载中...</text>
                </view>

                <!-- 使用v-else-if显示无数据状态 -->
                <view v-else-if="myGames.length === 0" class="text-center py-4">
                    <text class="text-white">暂无游戏记录</text>
                </view>

                <!-- 使用v-else显示游戏列表 -->
                <view v-else v-for="(game, index) in myGames" :key="index"
                    class="game-record border border-white border-opacity-20 rounded-lg p-4 hover-opacity-10 transition"
                    @click="navigateToGameDetail(game)">
                    <view class="flex justify-between items-center">
                        <view>
                            <text class="text-sm text-white text-opacity-70 block">{{ game.totalPlayers }}人局 · {{
                                formatDate(game.createTime) }}</text>
                        </view>
                        <text class="fas fa-chevron-right text-white text-opacity-50"></text>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    import { wolfPlayerApi } from '@/utils/wolfApi.js';

    export default {
        data() {
            return {
                title: '狼人杀助手',
                isLoading: true,
                myGames: [],
                playerId: ''
            }
        },
        onLoad() {
            // 页面加载时的初始化操作
            console.log('狼人杀首页加载')

            // 确保获取玩家ID
            this.playerId = getApp().globalData.playerId;

            // 加载我的游戏列表
            this.loadMyGames();

            // 检查页面堆栈，如果不是首次加载，确保刷新页面状态
            let pages = getCurrentPages();
            if (pages.length > 1) {
                console.log('从其他页面返回到首页，刷新数据');
                // 刷新页面数据等操作
                this.loadMyGames();
            }
        },
        // 页面显示时触发，用于从其他页面返回时更新数据
        onShow() {
            this.loadMyGames();
        },
        methods: {
            // 加载我的游戏列表
            async loadMyGames() {
                this.isLoading = true;
                try {
                    // 调用API获取用户参与的所有游戏
                    const games = await wolfPlayerApi.getJoinedRooms(this.playerId);
                    this.myGames = games || [];
                    console.log('获取的游戏列表:', this.myGames);
                } catch (error) {
                    console.error('获取游戏列表失败:', error);
                    uni.showToast({
                        title: '获取游戏列表失败',
                        icon: 'none'
                    });
                    this.myGames = [];
                } finally {
                    this.isLoading = false;
                }
            },

            // 跳转到游戏详情页面
            navigateToGameDetail(game) {
                console.log('game', game)
                if (game.creatorId == this.playerId) {
                    uni.navigateTo({
                        url: `/wolfkiller/pages/game-detail/game-detail?roomId=${game.roomId}`,
                        fail: (err) => {
                            console.error('跳转到游戏详情失败:', err);
                            uni.showToast({
                                title: '页面跳转失败',
                                icon: 'none'
                            });
                        }
                    });
                } else {
                    uni.navigateTo({
                        url: `/wolfkiller/pages/game-room/game-room?roomId=${game.roomId}`,
                        fail: (err) => {
                            console.error('跳转到游戏房间失败:', err);
                            uni.showToast({
                                title: '页面跳转失败',
                                icon: 'none'
                            });
                        }
                    });
                }
            },

            // 格式化日期
            formatDate(dateString) {
                if (!dateString) return '';

                const date = new Date(dateString);
                return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
            },

            navigateTo(pageId) {
                // 使用uni原生API导航
                let url = '';

                switch (pageId) {
                    case 'create-game':
                        url = '/wolfkiller/pages/create-game/create-game';
                        break;
                    case 'join-game':
                        url = '/wolfkiller/pages/join-game/join-game';
                        break;
                    case 'game-rules':
                        url = '/wolfkiller/pages/game-rules/game-rules';
                        break;
                    case 'game-detail':
                        url = '/wolfkiller/pages/game-detail/game-detail';
                        break;
                    case 'index':
                        url = '/pages/index/index';
                        uni.reLaunch({
                            url: url
                        });
                        return;
                    default:
                        console.error('未知页面ID:', pageId);
                        return;
                }

                console.log('navigateTo:', url);

                // 使用优化后的导航策略
                uni.navigateTo({
                    url: url,
                    fail: (err) => {
                        console.error('navigateTo失败:', err);
                        // 尝试switchTab
                        uni.switchTab({
                            url: url,
                            fail: (err1) => {
                                console.error('switchTab失败:', err1);
                                // 最后尝试reLaunch
                                uni.reLaunch({
                                    url: url,
                                    fail: (err2) => {
                                        console.error('reLaunch也失败:', err2);
                                        uni.showToast({
                                            title: '页面跳转失败',
                                            icon: 'none'
                                        });
                                    }
                                });
                            }
                        });
                    }
                });
            }
        }
    }
</script>

<style scoped>
    /* 容器样式 */
    .container {
        padding: 32rpx;
        background-color: #2c3e50;
        background-image: url("https://images.unsplash.com/photo-1478131143081-80f7f84ca84d?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80");
        background-size: cover;
        background-attachment: fixed;
        background-position: center;
        min-height: 100vh;
    }

    /* 卡片容器最大宽度 */
    .max-w-md {
        max-width: 90%;
    }

    /* 修复按钮间距 */
    .space-y-3>button:not(:first-child) {
        margin-top: 12px;
    }

    .space-y-4>view:not(:first-child) {
        margin-top: 16px;
    }

    /* 修复uniapp中的flex布局 */
    .flex-col {
        display: flex;
        flex-direction: column;
    }

    /* 修复按钮样式 */
    button {
        background-color: transparent;
        border: none;
        padding: 0;
        margin: 0;
        line-height: normal;
    }

    /* 修复按钮颜色 */
    .btn-primary {
        transition: transform 0.3s ease, background-color 0.3s ease;
    }

    .btn-primary:active {
        transform: translateY(-4px);
    }

    .bg-red-600 {
        background-color: #dc2626;
    }

    .bg-blue-600 {
        background-color: #2563eb;
    }

    .bg-purple-600 {
        background-color: #9333ea;
    }

    .bg-green-600 {
        background-color: #16a34a;
    }

    .py-4 {
        padding-top: 16px;
        padding-bottom: 16px;
    }

    /* 图片尺寸修复 */
    .h-48 {
        height: 192px;
    }

    /* 确保玻璃效果容器可见 */
    .glass-effect {
        background-color: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    /* 确保脉冲动画效果正常 */
    .pulse-animation {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
        }

        50% {
            transform: scale(1.05);
        }

        100% {
            transform: scale(1);
        }
    }

    /* 确保文本阴影效果 */
    .text-shadow {
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    }

    /* 确保hover效果正常 */
    .hover-translate-y-1:active {
        transform: translateY(-4px);
    }

    .hover-scale-105:active {
        transform: scale(1.05);
    }

    /* 游戏记录边框修复 */
    .game-record {
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: background-color 0.3s ease;
    }

    .game-record:active {
        background-color: rgba(255, 255, 255, 0.1);
    }
</style>