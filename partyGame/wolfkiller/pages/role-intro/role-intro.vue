<template>
    <view class="container">
        <!-- 使用安全头部组件 -->
        <safe-header title="角色介绍" :show-back="true"></safe-header>

        <view class="grid grid-cols-1 gap-4 mb-6">
            <view class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl shadow-lg overflow-hidden">
                <view class="bg-red-600 p-6 flex items-center">
                    <image src="/static/images/werewolf.png" alt="狼人"
                        class="w-16 h-16 rounded-full object-cover border-2 border-white mr-4"></image>
                    <view>
                        <text class="text-xl font-bold text-white block">狼人</text>
                        <text class="text-white text-opacity-80 block">狼人阵营</text>
                    </view>
                </view>

                <view class="p-6">
                    <text class="font-medium text-white mb-2 block">技能介绍</text>
                    <text class="text-white text-opacity-80 mb-4 block">每晚可以与其他狼人一起选择一名玩家杀死。狼人之间互相认识。</text>

                    <text class="font-medium text-white mb-2 block">游戏目标</text>
                    <text class="text-white text-opacity-80 mb-4 block">消灭所有神民或村民，或者达到狼人数量等于或多于其他玩家的数量。</text>

                    <text class="font-medium text-white mb-2 block">游戏技巧</text>
                    <view class="list-disc pl-5 space-y-1">
                        <text class="text-white text-opacity-80 block">尽量隐藏自己的身份，伪装成好人</text>
                        <text class="text-white text-opacity-80 block">优先击杀有特殊能力的神民</text>
                        <text class="text-white text-opacity-80 block">与其他狼人配合，互相掩护</text>
                        <text class="text-white text-opacity-80 block">制造混乱，分散好人的注意力</text>
                    </view>
                </view>
            </view>

            <view class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl shadow-lg overflow-hidden">
                <view class="bg-green-600 p-6 flex items-center">
                    <image src="/static/images/villager.png" alt="村民"
                        class="w-16 h-16 rounded-full object-cover border-2 border-white mr-4"></image>
                    <view>
                        <text class="text-xl font-bold text-white block">村民</text>
                        <text class="text-white text-opacity-80 block">好人阵营</text>
                    </view>
                </view>

                <view class="p-6">
                    <text class="font-medium text-white mb-2 block">技能介绍</text>
                    <text class="text-white text-opacity-80 mb-4 block">没有特殊技能，只能依靠推理和投票来帮助好人阵营获胜。</text>

                    <text class="font-medium text-white mb-2 block">游戏目标</text>
                    <text class="text-white text-opacity-80 mb-4 block">与其他好人一起找出并处决所有狼人。</text>

                    <text class="font-medium text-white mb-2 block">游戏技巧</text>
                    <view class="list-disc pl-5 space-y-1">
                        <text class="text-white text-opacity-80 block">仔细观察每个人的发言和行为</text>
                        <text class="text-white text-opacity-80 block">寻找狼人的破绽和矛盾</text>
                        <text class="text-white text-opacity-80 block">相信自己的直觉，但也要听取他人的意见</text>
                        <text class="text-white text-opacity-80 block">保护神民角色，他们是好人阵营的关键</text>
                    </view>
                </view>
            </view>
        </view>

        <view class="grid grid-cols-1 gap-4 mb-6">
            <view class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl shadow-lg overflow-hidden">
                <view class="bg-blue-600 p-6 flex items-center">
                    <image src="/static/images/seer.png" alt="预言家"
                        class="w-16 h-16 rounded-full object-cover border-2 border-white mr-4"></image>
                    <view>
                        <text class="text-xl font-bold text-white block">预言家</text>
                        <text class="text-white text-opacity-80 block">好人阵营</text>
                    </view>
                </view>

                <view class="p-6">
                    <text class="font-medium text-white mb-2 block">技能介绍</text>
                    <text class="text-white text-opacity-80 mb-4 block">每晚可以查验一名玩家的身份是好人还是狼人。</text>

                    <text class="font-medium text-white mb-2 block">游戏目标</text>
                    <text class="text-white text-opacity-80 mb-4 block">利用查验能力帮助好人阵营找出狼人。</text>

                    <text class="font-medium text-white mb-2 block">游戏技巧</text>
                    <view class="list-disc pl-5 space-y-1">
                        <text class="text-white text-opacity-80 block">优先查验有嫌疑的玩家</text>
                        <text class="text-white text-opacity-80 block">谨慎选择是否公开自己的身份</text>
                        <text class="text-white text-opacity-80 block">可以考虑隐藏部分查验结果，防止被狼人针对</text>
                        <text class="text-white text-opacity-80 block">在关键时刻站出来指认狼人</text>
                    </view>
                </view>
            </view>

            <view class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl shadow-lg overflow-hidden">
                <view class="bg-purple-600 p-6 flex items-center">
                    <image src="/static/images/witch.png" alt="女巫"
                        class="w-16 h-16 rounded-full object-cover border-2 border-white mr-4"></image>
                    <view>
                        <text class="text-xl font-bold text-white block">女巫</text>
                        <text class="text-white text-opacity-80 block">好人阵营</text>
                    </view>
                </view>

                <view class="p-6">
                    <text class="font-medium text-white mb-2 block">技能介绍</text>
                    <text
                        class="text-white text-opacity-80 mb-4 block">拥有一瓶解药和一瓶毒药。解药可以救活当晚被狼人杀死的玩家，毒药可以毒死一名玩家。每种药只能使用一次。</text>

                    <text class="font-medium text-white mb-2 block">游戏目标</text>
                    <text class="text-white text-opacity-80 mb-4 block">合理使用解药和毒药，帮助好人阵营获胜。</text>

                    <text class="font-medium text-white mb-2 block">游戏技巧</text>
                    <view class="list-disc pl-5 space-y-1">
                        <text class="text-white text-opacity-80 block">解药优先救神民角色</text>
                        <text class="text-white text-opacity-80 block">毒药要留到确定是狼人的玩家身上</text>
                        <text class="text-white text-opacity-80 block">不要轻易暴露自己的身份</text>
                        <text class="text-white text-opacity-80 block">可以考虑不使用解药，以隐藏自己的身份</text>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    import { navigateToPage } from '@/utils/game.js';
    import SafeHeader from '@/components/SafeHeader.vue';

    export default {
        components: {
            SafeHeader
        },
        data() {
            return {

            }
        },
        methods: {
            navigateTo(pageId) {
                navigateToPage(pageId);
            }
        }
    }
</script>

<style>
    .container {
        padding: 32rpx;
        background-color: #2c3e50;
        background-image: url("https://images.unsplash.com/photo-1478131143081-80f7f84ca84d?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80");
        background-size: cover;
        background-attachment: fixed;
        background-position: center;
        min-height: 100vh;
    }

    /* 网格布局 */
    .grid-cols-1 {
        display: grid;
        grid-template-columns: 1fr;
    }

    .gap-4 {
        grid-gap: 16px;
        gap: 16px;
    }

    /* 角色图标尺寸 */
    .w-16,
    .h-16 {
        width: 64px;
        height: 64px;
    }

    /* 边框样式 */
    .border-2 {
        border-width: 2px;
    }

    /* 溢出控制 */
    .overflow-hidden {
        overflow: hidden;
    }

    /* 列表样式 */
    .list-disc {
        list-style-type: disc;
        padding-left: 20px;
    }

    .space-y-1>text:not(:first-child) {
        margin-top: 4px;
        display: block;
    }

    /* 颜色修复 */
    .bg-red-600 {
        background-color: #dc2626;
    }

    .bg-green-600 {
        background-color: #059669;
    }

    .bg-blue-600 {
        background-color: #2563eb;
    }

    .bg-purple-600 {
        background-color: #9333ea;
    }

    /* 修复透明度 */
    .bg-white.bg-opacity-10 {
        background-color: rgba(255, 255, 255, 0.1);
    }

    /* 修复flex布局 */
    .flex {
        display: flex;
    }

    /* 圆角图片 */
    .rounded-full {
        border-radius: 50%;
    }

    /* 图片显示模式 */
    .object-cover {
        object-fit: cover;
    }

    /* 修复文本不透明度 */
    .text-opacity-80 {
        opacity: 0.8;
    }
</style>