<template>
    <view class="container">
        <!-- 使用安全头部组件 -->
        <safe-header :title="'游戏规则'" :show-back="true"></safe-header>

        <view class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl shadow-lg p-6 mb-6 text-white">
            <text class="text-xl font-semibold mb-4 block">基本规则</text>

            <view class="space-y-4">
                <text class="block">狼人杀是一款多人参与的、以语言描述推动的、较量口才和分析判断能力的策略类桌游。</text>

                <text class="font-medium mt-4 block">游戏目标</text>
                <text class="block">狼人阵营：消灭所有神民或村民，或者达到狼人数量等于或多于其他玩家的数量。</text>
                <text class="block">好人阵营：消灭所有狼人。</text>

                <text class="font-medium mt-4 block">游戏流程</text>
                <view class="list-decimal pl-5 space-y-2">
                    <view class="flex">
                        <text class="mr-2">1.</text>
                        <text>游戏开始，每位玩家获得一个身份。</text>
                    </view>
                    <view class="flex">
                        <text class="mr-2">2.</text>
                        <text>夜晚阶段：各特殊角色按顺序行使技能。</text>
                    </view>
                    <view class="flex">
                        <text class="mr-2">3.</text>
                        <text>白天阶段：所有玩家讨论，投票处决一名玩家。</text>
                    </view>
                    <view class="flex">
                        <text class="mr-2">4.</text>
                        <text>如此循环，直到一方达成胜利条件。</text>
                    </view>
                </view>
            </view>
        </view>

        <view class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl shadow-lg p-6 mb-6 text-white">
            <text class="text-xl font-semibold mb-4 block">角色介绍</text>

            <view class="space-y-4">
                <view class="p-4 bg-white bg-opacity-10 rounded-lg">
                    <view class="flex items-center mb-2">
                        <image src="/static/images/werewolf.png" alt="狼人"
                            class="w-10 h-10 rounded-full object-cover mr-3"></image>
                        <text class="font-medium">狼人</text>
                    </view>
                    <text class="block">每晚可以与其他狼人一起选择一名玩家杀死。狼人之间互相认识。</text>
                </view>

                <view class="p-4 bg-white bg-opacity-10 rounded-lg">
                    <view class="flex items-center mb-2">
                        <image src="/static/images/villager.png" alt="村民"
                            class="w-10 h-10 rounded-full object-cover mr-3"></image>
                        <text class="font-medium">村民</text>
                    </view>
                    <text class="block">没有特殊技能，只能依靠推理和投票来帮助好人阵营获胜。</text>
                </view>

                <view class="p-4 bg-white bg-opacity-10 rounded-lg">
                    <view class="flex items-center mb-2">
                        <image src="/static/images/seer.png" alt="预言家" class="w-10 h-10 rounded-full object-cover mr-3">
                        </image>
                        <text class="font-medium">预言家</text>
                    </view>
                    <text class="block">每晚可以查验一名玩家的身份是好人还是狼人。</text>
                </view>

                <view class="p-4 bg-white bg-opacity-10 rounded-lg">
                    <view class="flex items-center mb-2">
                        <image src="/static/images/witch.png" alt="女巫" class="w-10 h-10 rounded-full object-cover mr-3">
                        </image>
                        <text class="font-medium">女巫</text>
                    </view>
                    <text class="block">拥有一瓶解药和一瓶毒药。解药可以救活当晚被狼人杀死的玩家，毒药可以毒死一名玩家。每种药只能使用一次。</text>
                </view>
            </view>
        </view>

        <button class="more-roles-btn w-full bg-blue-600 text-white font-bold py-3 px-4 rounded-lg"
            @click="navigateTo('wolf-role')">
            查看更多角色介绍
        </button>
    </view>
</template>

<script>
    import { navigateToPage } from '@/utils/game.js';
    import SafeHeader from '@/components/SafeHeader.vue';

    export default {
        components: {
            SafeHeader
        },
        data() {
            return {
                fromPage: 'game-room' // 默认从游戏房间进入
            }
        },
        onLoad(options) {
            // 记录来源页面，便于返回
            if (options.from) {
                this.fromPage = options.from;
            }
        },
        methods: {
            navigateTo(pageId) {
                navigateToPage(pageId);
            },
            goBack() {
                // 返回来源页面
                navigateToPage(this.fromPage);
            }
        }
    }
</script>

<style scoped>
    .container {
        padding: 32rpx;
        background-color: #2c3e50;
        background-image: url("https://images.unsplash.com/photo-1478131143081-80f7f84ca84d?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80");
        background-size: cover;
        background-attachment: fixed;
        background-position: center;
        min-height: 100vh;
    }

    .space-y-2>view:not(:first-child) {
        margin-top: 8px;
    }

    .space-y-4>view:not(:first-child),
    .space-y-4>text:not(:first-child) {
        margin-top: 16px;
    }

    .w-10,
    .h-10 {
        width: 40px;
        height: 40px;
    }

    button {
        border: none;
        background-color: transparent;
        padding: 0;
        margin: 0;
        line-height: normal;
    }

    .py-3 {
        padding-top: 12px;
        padding-bottom: 12px;
    }

    .mt-4 {
        margin-top: 16px;
    }

    /* 列表样式 */
    .list-decimal {
        list-style-type: decimal;
        padding-left: 20px;
    }

    /* 修复更多角色按钮样式 */
    .more-roles-btn {
        background-color: #2563eb;
        transition: background-color 0.3s ease;
    }

    .more-roles-btn:active {
        background-color: #1d4ed8;
    }

    /* 修复flex布局 */
    .flex {
        display: flex;
    }

    /* 修复容器透明度 */
    .bg-white.bg-opacity-10 {
        background-color: rgba(255, 255, 255, 0.1);
    }

    /* 修复圆角图片 */
    .rounded-full {
        border-radius: 50%;
    }

    /* 修复图片显示模式 */
    .object-cover {
        object-fit: cover;
    }
</style>