<template>
    <view class="container">
        <!-- 使用安全头部组件 -->
        <safe-header :title="roomInfo.roomName || '狼人杀房间'" :show-back="true"></safe-header>
        <!-- 房间号 -->
        <view class="text-center text-white text-opacity-80 text-xl font-bold mb-6">房间号：{{ roomInfo.roomCode
            }}</view>
        <!-- 玩家身份卡片 -->
        <view class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl shadow-lg p-6 mb-6 text-white">
            <view class="flip-card mb-6" :class="{ 'flipped': isCardFlipped }" @click="toggleCard">
                <view class="flip-card-inner">
                    <view class="flip-card-front bg-red-600">
                        <text style="font-size: 76rpx; margin-bottom: 40rpx;" class="fas fa-question-circle"></text>
                        <text class="text-xl font-bold text-white mb-2 block">点击查看你的身份</text>
                        <text class="text-white text-opacity-80 block">请不要让其他人看到</text>
                    </view>
                    <view class="flip-card-back bg-blue-600">
                        <image :src="getRoleImage(playerInfo.roleName)" alt="身份"
                            class="w-24 h-24 rounded-full object-cover mb-4">
                        </image>
                        <text class="text-xl font-bold text-white mb-2 block">{{ roleName }}</text>
                        <text class="text-white text-opacity-80 text-center px-4 block">{{ roleDescription }}</text>
                    </view>
                </view>
            </view>
        </view>

        <!-- 玩家列表 -->
        <view class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl shadow-lg p-6 mb-6 text-white">
            <view class="flex justify-between items-center mb-4">
                <text class="text-xl font-semibold block">玩家列表</text>
                <text class="text-sm text-white text-opacity-80 block">{{ players.length }}人已参与</text>
            </view>

            <view v-if="players.length === 0" class="text-center py-8 text-white text-opacity-70">
                <text class="block">暂无玩家加入</text>
                <text class="block mt-2 text-sm">房间号：{{ roomInfo.roomCode }}</text>
            </view>

            <view v-else class="grid grid-cols-3 gap-3">
                <view v-for="(player, index) in players" :key="index"
                    class="flex-col items-center p-3 bg-white bg-opacity-10 rounded-lg">
                    <view class="w-14 h-14 rounded-full flex items-center justify-center mb-1"
                        :class="player.playerId === playerInfo.playerId ? 'bg-blue-500' : 'bg-gray-500'">
                        <text class="text-white font-bold">{{ player.playerName ? player.playerName.substr(0, 1) : '?'
                            }}</text>
                    </view>
                    <text class="text-sm font-medium truncate w-full text-center block">{{ player.playerName || '未知玩家'
                        }}</text>
                    <text v-if="player.isCreator" class="text-xs text-white text-opacity-70 block text-center">房主</text>
                </view>
            </view>
        </view>

        <!-- 底部按钮 -->
        <view class="flex space-x-3">
            <button
                class="btn-primary flex-1 bg-red-600 hover-bg-red-700 text-white font-bold py-4 px-4 rounded-lg transition flex items-center justify-center"
                @click="showGameRules">
                <text class="fas fa-book mr-2"></text> 游戏规则
            </button>

            <button
                class="btn-primary flex-1 bg-blue-600 hover-bg-blue-700 text-white font-bold py-4 px-4 rounded-lg transition flex items-center justify-center"
                @click="navigateToHome">
                <text class="fas fa-home mr-2"></text> 返回首页
            </button>
        </view>

        <!-- 用户信息设置弹窗组件 -->
        <user-profile-modal :visible="showProfileModal" :player-id="playerInfo.playerId" :default-avatar="avatarUrl"
            @save="handleSaveUserProfile"></user-profile-modal>
    </view>
</template>

<script>
    import { wolfRoomApi, wolfPlayerApi } from '@/utils/wolfApi.js';
    import { roleConfig } from '@/utils/game.js';
    import SafeHeader from '@/components/SafeHeader.vue';
    import UserProfileModal from '@/components/UserProfileModal.vue';

    export default {
        components: {
            SafeHeader,
            UserProfileModal
        },
        data() {
            return {
                roomId: null,
                roomInfo: {
                    roomId: null,
                    roomCode: '',
                    roomName: '',
                    creatorId: '',
                    creatorName: '',
                    createdAt: null,
                },
                playerInfo: {
                    playerId: '',
                    playerName: '',
                    roleName: '',
                    playerIndex: 0
                },
                players: [],
                isCardFlipped: false,
                roleName: '未知',
                roleDescription: '正在加载角色信息...',

                loading: false,

                // 个人资料设置相关
                showProfileModal: false,
                avatarUrl: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'
            }
        },
        onLoad(options) {
            console.log('game-room页面参数:', options);

            // 获取房间ID
            if (options.scene) {
                this.roomId = options.scene;
            } else if (options.roomId) {
                this.roomId = options.roomId;
            } else {
                // 尝试从存储中获取房间信息
                const roomInfo = uni.getStorageSync('wolfRoomInfo');
                if (roomInfo && roomInfo.roomId) {
                    this.roomId = roomInfo.roomId;
                } else {
                    uni.showToast({
                        title: '房间信息缺失',
                        icon: 'none'
                    });
                    setTimeout(() => this.navigateToHome(), 1000);
                    return;
                }
            }

            // 确保先获取玩家ID
            let playerId = getApp().globalData.playerId;
            if (!playerId) {
                getApp().login().then(() => {
                    // 确保玩家ID存在
                    this.playerInfo.playerId = getApp().globalData.playerId
                    // 加载房间和玩家信息
                    this.loadRoomInfo();
                })
            } else {
                // 确保玩家ID存在
                this.playerInfo.playerId = playerId;
                // 加载房间和玩家信息
                this.loadRoomInfo();
            }
        },
        onShow() {
            // 页面显示时刷新数据
            if (this.roomId) {
                this.refreshRoomData();
            }
        },
        methods: {
            async loadRoomInfo() {
                try {
                    this.loading = true;
                    uni.showLoading({
                        title: '加载房间信息...'
                    });

                    // 获取房间信息
                    const roomInfo = await wolfRoomApi.getRoomInfo(this.roomId);
                    if (!roomInfo) {
                        throw new Error('房间不存在');
                    }

                    // 更新房间信息
                    this.roomInfo = roomInfo;

                    // 获取玩家信息
                    const joinData = {
                        roomId: this.roomId,
                        playerId: this.playerInfo.playerId,
                        playerName: this.playerInfo.playerName,
                    };
                    const playerInfo = await wolfRoomApi.joinRoom(joinData);
                    if (playerInfo) {
                        this.playerInfo = playerInfo;
                        // 设置角色信息
                        this.setRoleInfo(playerInfo.roleName);
                    } else {
                        uni.showToast({
                            title: '房间已满',
                            icon: 'none'
                        });
                        setTimeout(() => this.navigateToHome(), 2000);
                    }

                    // 获取所有玩家
                    this.loadPlayers();

                    //判断用户是否设置了昵称
                    if (this.playerInfo.playerName) {
                        // 显示个人资料设置弹窗
                        this.showProfileModal = true;
                    }

                    uni.hideLoading();
                    this.loading = false;
                } catch (error) {
                    uni.hideLoading();
                    this.loading = false;
                    console.error('加载房间信息失败:', error);
                    uni.showToast({
                        title: error.message || '加载房间信息失败',
                        icon: 'none'
                    });
                }
            },

            async loadPlayers() {
                try {
                    // 获取所有玩家
                    const players = await wolfPlayerApi.getRoomPlayers(this.roomId);
                    if (players && players.length > 0) {
                        this.players = players;
                    }
                } catch (error) {
                    console.error('获取玩家列表失败:', error);
                    // 使用本地存储的玩家信息作为备用
                    const roomInfo = uni.getStorageSync('wolfRoomInfo');
                    if (roomInfo && roomInfo.players) {
                        this.players = roomInfo.players;
                    }
                }
            },


            // 刷新房间数据
            async refreshRoomData() {
                if (this.loading) return;

                try {
                    await this.loadPlayers();
                } catch (error) {
                    console.error('刷新房间数据失败:', error);
                }
            },

            // 设置角色信息
            setRoleInfo(roleName) {
                if (!roleName) {
                    this.roleName = '等待分配';
                    this.roleDescription = '游戏尚未开始，等待房主分配角色';
                    return;
                }

                // 设置角色名称和描述
                switch (roleName.toUpperCase()) {
                    case 'WEREWOLF':
                        this.roleName = '狼人';
                        this.roleDescription = '你是一名狼人，每晚可以与同伴一起选择一名玩家杀死。请隐藏好自己的身份。';
                        break;
                    case 'SEER':
                        this.roleName = '预言家';
                        this.roleDescription = '你是一名预言家，每晚可以查验一名玩家的身份是好人还是狼人。';
                        break;
                    case 'WITCH':
                        this.roleName = '女巫';
                        this.roleDescription = '你是一名女巫，拥有一瓶解药和一瓶毒药。解药可以救活被狼人杀死的玩家，毒药可以毒死一名玩家。';
                        break;
                    case 'VILLAGER':
                        this.roleName = '村民';
                        this.roleDescription = '你是一名普通村民，没有特殊技能。请在白天仔细观察发言，投票处决出狼人。';
                        break;
                    default:
                        this.roleName = roleName;
                        this.roleDescription = '请参考游戏规则了解该角色的技能和玩法。';
                }
            },

            getRoleImage(roleName) {
                let image = roleConfig.roleImages[roleName.toUpperCase()];
                if (!image) {
                    image = roleConfig.roleImages['CUSTOM'];
                }
                return image;
            },

            // 切换卡片显示状态
            toggleCard() {
                this.isCardFlipped = !this.isCardFlipped;
            },

            // 显示游戏规则
            showGameRules() {
                uni.navigateTo({
                    url: '/wolfkiller/pages/game-rules/game-rules',
                    fail: (err) => {
                        console.error('页面跳转失败:', err);
                        uni.showToast({
                            title: '页面跳转失败',
                            icon: 'none'
                        });
                    }
                });
            },

            // 返回首页
            navigateToHome() {
                uni.reLaunch({
                    url: '/wolfkiller/pages/index/index'
                });
            },

            // 处理用户保存资料
            async handleSaveUserProfile(data) {
                try {
                    uni.showLoading({
                        title: '保存中...'
                    });

                    // 更新玩家信息
                    const updateData = {
                        playerId: data.playerId,
                        nickname: data.nickname,
                        avatarUrl: data.avatarUrl
                    };

                    const result = await wolfPlayerApi.updatePlayerInfo(updateData);
                    if (result) {
                        this.playerInfo.playerName = data.nickname;
                        this.avatarUrl = data.avatarUrl;

                        // 刷新玩家列表
                        await this.loadPlayers();

                        // 隐藏弹窗
                        this.showProfileModal = false;

                        uni.showToast({
                            title: '个人信息设置成功',
                            icon: 'success'
                        });
                    }

                    uni.hideLoading();
                } catch (error) {
                    uni.hideLoading();
                    console.error('保存个人信息失败:', error);
                    uni.showToast({
                        title: '保存失败，请重试',
                        icon: 'none'
                    });
                }
            }
        }
    }
</script>

<style scoped>
    /* 容器样式 */
    .container {
        padding: 32rpx;
        background-color: #2c3e50;
        background-image: url("https://images.unsplash.com/photo-1478131143081-80f7f84ca84d?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80");
        background-size: cover;
        background-attachment: fixed;
        background-position: center;
        min-height: 100vh;
    }

    .space-x-3>view:not(:first-child),
    .space-x-3>button:not(:first-child) {
        margin-left: 12px;
    }

    button {
        border: none;
        background-color: transparent;
        padding: 0;
        margin: 0;
        line-height: normal;
    }

    /* 按钮样式 */
    .bg-red-600 {
        background-color: #dc2626;
    }

    .bg-gray-500 {
        background-color: #6b7280;
    }

    .bg-gray-600 {
        background-color: #4b5563;
    }

    .bg-blue-500 {
        background-color: #3b82f6;
    }

    .bg-blue-600 {
        background-color: #2563eb;
    }

    .bg-green-600 {
        background-color: #059669;
    }

    /* 卡片翻转 */
    .flip-card {
        perspective: 1000px;
        width: 100%;
        height: 230px;
        cursor: pointer;
    }

    .flip-card-inner {
        position: relative;
        width: 100%;
        height: 100%;
        text-align: center;
        transition: transform 0.6s;
        transform-style: preserve-3d;
    }

    .flipped .flip-card-inner {
        transform: rotateY(180deg);
    }

    .flip-card-front,
    .flip-card-back {
        position: absolute;
        width: 100%;
        height: 100%;
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border-radius: 16px;
    }

    .flip-card-back {
        transform: rotateY(180deg);
    }

    /* 修复flex布局 */
    .flex {
        display: flex;
    }

    .justify-between {
        justify-content: space-between;
    }

    .items-center {
        align-items: center;
    }

    .flex-1 {
        flex: 1;
    }

    .space-x-3 {
        display: flex;
    }

    /* 列表样式 */
    .grid {
        display: grid;
    }

    .grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    .gap-3 {
        gap: 12px;
    }

    /* 辅助样式 */
    .truncate {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .text-center {
        text-align: center;
    }

    .w-full {
        width: 100%;
    }

    .py-8 {
        padding-top: 32px;
        padding-bottom: 32px;
    }

    .mt-2 {
        margin-top: 8px;
    }

    /* 文字大小 */
    .text-xs {
        font-size: 12px;
    }

    .text-sm {
        font-size: 14px;
    }

    .text-xl {
        font-size: 20px;
    }

    /* 文字颜色透明度 */
    .text-opacity-70 {
        opacity: 0.7;
    }

    .text-opacity-80 {
        opacity: 0.8;
    }

    /* 圆形样式 */
    .rounded-full {
        border-radius: 50%;
    }

    /* 按钮边缘间距 */
    .px-4 {
        padding-left: 16px;
        padding-right: 16px;
    }

    .hover-bg-blue-700:hover {
        background-color: #1d4ed8;
    }

    .hover-bg-red-700:hover {
        background-color: #b91c1c;
    }

    .hover-bg-green-700:hover {
        background-color: #047857;
    }

    .py-4 {
        padding-top: 16px;
        padding-bottom: 16px;
    }

    .py-3 {
        padding-top: 12px;
        padding-bottom: 12px;
    }

    .flex-col {
        display: flex;
    }
</style>