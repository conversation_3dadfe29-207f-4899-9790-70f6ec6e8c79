<template>
    <view class="container">
        <!-- 使用安全头部组件 -->
        <safe-header title="" :show-back="true"></safe-header>

        <view class="text-center mb-8">
            <view class="inline-block p-4 bg-green-100 rounded-full mb-4">
                <text class="fas fa-check text-3xl text-green-500"></text>
            </view>
            <text class="text-2xl font-bold text-white block">游戏创建成功！</text>
            <text class="text-white mt-2 block">房间号: <text class="font-bold">{{ roomCode }}</text></text>

        </view>

        <view class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl shadow-lg p-6 mb-6 text-white">
            <text class="text-xl font-semibold mb-4 block">邀请好友</text>

            <view class="text-center mb-6">
                <view class="inline-block p-4 bg-white bg-opacity-20 rounded-lg mb-4">
                    <image :src="qrCodeUrl" alt="游戏二维码" class="w-48 h-48"></image>
                </view>
                <text class="text-white text-opacity-80 block">扫描上方二维码加入游戏</text>
            </view>

            <view class="flex space-x-3 mb-6">
                <button open-type="share"
                    class="btn-primary flex-1 bg-green-500 hover-bg-green-600 text-white font-medium py-4 px-4 rounded-lg transition flex items-center justify-center">
                    <text class="fab fa-wechat mr-2"></text> 微信分享
                </button>
            </view>
        </view>

        <view class="flex space-x-3">
            <button
                class="btn-primary flex-1 bg-blue-600 hover-bg-blue-700 text-white font-bold py-4 px-4 rounded-lg transition flex items-center justify-center"
                @click="confirmAttend">
                <text class="fas fa-gamepad mr-2"></text> 进入游戏
            </button>
            <button
                class="btn-primary flex-1 bg-red-600 hover-bg-red-700 text-white font-bold py-4 px-4 rounded-lg transition flex items-center justify-center"
                @click="deleteGame">
                <text class="fas fa-times mr-2"></text> 删除游戏
            </button>
        </view>
    </view>
</template>

<script>

    import { wolfRoomApi, wechatApi } from '@/utils/wolfApi.js';
    import SafeHeader from '@/components/SafeHeader.vue';

    export default {
        data() {
            return {
                roomId: null,
                roomCode: '',
                expireTime: null,

                countdownController: null,
                qrCodeUrl: '', // 二维码URL
            }
        },
        components: {
            SafeHeader
        },
        onLoad(options) {
            console.log('game-created页面参数:', options);

            if (options.roomId && options.roomCode) {
                this.roomId = options.roomId;
                this.roomCode = options.roomCode;
                this.loadRoomDetails();
            } else {
                // 尝试从存储中获取房间信息
                const roomInfo = uni.getStorageSync('wolfRoomInfo');
                if (roomInfo) {
                    this.roomId = roomInfo.roomId;
                    this.roomCode = roomInfo.roomCode;
                    //this.generateQRCode();
                } else {
                    uni.showToast({
                        title: '房间信息缺失',
                        icon: 'none'
                    });
                    setTimeout(() => this.navigateTo('home-page'), 2000);
                }
            }


        },
        onUnload() {
            // 页面卸载时停止倒计时
            if (this.countdownController) {
                this.countdownController.stop();
            }
        },
        onShareAppMessage() {
            return {
                title: '狼人杀助手(房号' + this.roomCode + ')',
                path: '/wolfkiller/pages/game-room/game-room?roomId=' + this.roomId,
                imageUrl: "/wolfkiller/static/images/wolf.jpg"
            }
        },
        methods: {
            async loadRoomDetails() {
                try {
                    // 显示加载中
                    uni.showLoading({
                        title: '加载房间信息...'
                    });

                    // 调用API获取房间详细信息
                    const roomInfo = await wolfRoomApi.getRoomInfo(this.roomId);
                    this.expireTime = roomInfo.expireTime;
                    // 关闭加载提示
                    uni.hideLoading();

                    if (roomInfo) {
                        // 存储完整房间信息
                        uni.setStorageSync('wolfRoomInfo', roomInfo);

                        // 生成二维码
                        //this.generateQRCode();
                    }
                } catch (error) {
                    uni.hideLoading();
                    console.error('获取房间信息失败:', error);
                    uni.showToast({
                        title: '获取房间信息失败',
                        icon: 'none'
                    });
                }
            },


            async generateQRCode() {
                // 在实际项目中，可以使用服务器API生成二维码或使用前端库
                // 这里暂时使用占位图
                var params = {
                    scene: this.roomId,
                    //二维码场景编号
                    page: "wolfkiller/pages/game-room/game-room",
                    //二维码对应的入口url
                    width: 860,
                    account: getApp().globalData.product
                };

                var url = getApp().globalData.serverUrl + "/api/wechat/getQRCodeBase64"

                let res = await wechatApi.getQRCodeBase64(params)

                this.qrCodeUrl = res.data.data
            },
            // 确认参加游戏
            confirmAttend() {
                let that = this
                // 确认参加游戏
                uni.showModal({
                    title: '确认参加游戏',
                    content: '您是房主，确定要加入游戏吗？',
                    confirmText: '确定',
                    cancelText: '取消',
                    success: async (res) => {
                        if (res.confirm) {
                            that.navigateTo('game-room')
                        }
                    }
                })
            },
            navigateTo(pageId) {
                // 导航到指定页面
                let url = '';
                if (pageId === 'game-room') {
                    url = `/wolfkiller/pages/game-room/game-room?roomId=${this.roomId}`;
                } else if (pageId === 'home-page') {
                    url = '/wolfkiller/pages/index/index';
                    uni.reLaunch({
                        url: url,
                        fail: (err) => {
                            console.error('页面跳转失败:', err);
                        }
                    });
                    return
                } else {
                    console.error('未知页面ID:', pageId);
                    return;
                }

                // 使用优化的导航策略
                uni.navigateTo({
                    url: url,
                    fail: (err) => {
                        console.error('页面跳转失败:', err);
                        uni.showToast({
                            title: '页面跳转失败',
                            icon: 'none'
                        });
                    }
                });
            },

            async deleteGame() {
                uni.showModal({
                    title: '确认取消',
                    content: '确定要取消此游戏吗？',
                    success: async (res) => {
                        if (res.confirm) {
                            try {
                                // 实际项目中应调用API删除房间
                                await wolfRoomApi.deleteRoom(this.roomId);

                                // 移除游戏数据
                                uni.removeStorageSync('wolfRoomInfo');

                                // 返回首页
                                uni.reLaunch({
                                    url: '/wolfkiller/pages/index/index'
                                });

                            } catch (error) {
                                console.error('删除房间失败:', error);
                                uni.showToast({
                                    title: '删除房间失败',
                                    icon: 'none'
                                });
                            }
                        }
                    }
                });
            }
        }
    }
</script>

<style scoped>
    /* 容器样式 */
    .container {
        padding: 32rpx;
        background-color: #2c3e50;
        background-image: url("https://images.unsplash.com/photo-1478131143081-80f7f84ca84d?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80");
        background-size: cover;
        background-attachment: fixed;
        background-position: center;
        min-height: 100vh;
    }



    /* 修复inline-block显示 */
    .inline-block {
        display: inline-block;
    }

    /* 修复按钮水平间距 */
    .space-x-3>view:not(:first-child),
    .space-x-3>button:not(:first-child) {
        margin-left: 12px;
    }

    /* 修复按钮样式 */
    button {
        border: none;
        background-color: transparent;
        padding: 0;
        margin: 0;
        line-height: normal;
    }

    .py-3 {
        padding-top: 12px;
        padding-bottom: 12px;
    }

    .py-4 {
        padding-top: 16px;
        padding-bottom: 16px;
    }

    /* 修复二维码尺寸 */
    .w-48,
    .h-48 {
        width: 192px;
        height: 192px;
    }

    /* 修复成功图标背景 */
    .bg-green-100 {
        background-color: #d1fae5;
    }

    .text-green-500 {
        color: #10b981;
    }

    /* 修复玻璃效果容器 */
    .bg-white.bg-opacity-10 {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .bg-white.bg-opacity-20 {
        background-color: rgba(255, 255, 255, 0.2);
    }

    /* 修复按钮背景色 */
    .bg-green-500 {
        background-color: #10b981;
    }

    .bg-blue-600 {
        background-color: #2563eb;
    }

    .bg-red-600 {
        background-color: #dc2626;
    }

    /* 修复hover效果 */
    .hover-bg-green-600:hover {
        background-color: #059669;
    }

    .hover-bg-blue-700:hover {
        background-color: #1d4ed8;
    }

    .hover-bg-red-700:hover {
        background-color: #b91c1c;
    }

    /* 修复flex布局 */
    .flex {
        display: flex;
    }

    .flex-1 {
        flex: 1;
    }

    .space-x-3 {
        display: flex;
    }

    /* 修复倒计时定时器和文本 */
    .font-mono {
        font-family: monospace;
    }

    /* 圆形样式 */
    .rounded-full {
        border-radius: 50%;
    }

    /* 修复按钮边缘间距 */
    .px-4 {
        padding-left: 16px;
        padding-right: 16px;
    }
</style>