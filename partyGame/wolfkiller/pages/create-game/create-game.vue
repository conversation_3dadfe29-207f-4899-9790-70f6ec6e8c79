<template>
    <view class="container">
        <!-- 使用安全头部组件 -->
        <safe-header title="创建游戏" :show-back="true"></safe-header>

        <view class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl shadow-lg p-6 mb-6 text-white">

            <view class="mb-6">
                <view class="flex justify-between items-center mb-2">
                    <text class="block font-medium">角色配置</text>
                    <text class="text-sm text-white text-opacity-70">共 {{ getTotalRoles }} 名玩家</text>
                </view>

                <view class="space-y-3">
                    <view class="flex justify-between items-center p-3 bg-white bg-opacity-10 rounded-lg">
                        <view class="flex items-center">
                            <image :src="getRoleImage('werewolf')" alt="狼人"
                                class="w-10 h-10 rounded-full object-cover mr-3"></image>
                            <text class="font-medium">狼人</text>
                        </view>
                        <view class="flex items-center">
                            <view
                                class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                @click="decreaseRole('werewolf')">
                                <text>-</text>
                            </view>
                            <text class="mx-3 w-6 text-center">{{ roleCount.werewolf }}</text>
                            <view
                                class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                @click="increaseRole('werewolf')">
                                <text>+</text>
                            </view>
                        </view>
                    </view>

                    <view class="flex justify-between items-center p-3 bg-white bg-opacity-10 rounded-lg">
                        <view class="flex items-center">
                            <image :src="getRoleImage('villager')" alt="村民"
                                class="w-10 h-10 rounded-full object-cover mr-3"></image>
                            <text class="font-medium">村民</text>
                        </view>
                        <view class="flex items-center">
                            <view
                                class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                @click="decreaseRole('villager')">
                                <text>-</text>
                            </view>
                            <text class="mx-3 w-6 text-center">{{ roleCount.villager }}</text>
                            <view
                                class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                @click="increaseRole('villager')">
                                <text>+</text>
                            </view>
                        </view>
                    </view>

                    <view class="flex justify-between items-center p-3 bg-white bg-opacity-10 rounded-lg">
                        <view class="flex items-center">
                            <image :src="getRoleImage('seer')" alt="预言家"
                                class="w-10 h-10 rounded-full object-cover mr-3"></image>
                            <text class="font-medium">预言家</text>
                        </view>
                        <view class="flex items-center">
                            <view
                                class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                @click="decreaseRole('seer')">
                                <text>-</text>
                            </view>
                            <text class="mx-3 w-6 text-center">{{ roleCount.seer }}</text>
                            <view
                                class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                @click="increaseRole('seer')">
                                <text>+</text>
                            </view>
                        </view>
                    </view>

                    <view class="flex justify-between items-center p-3 bg-white bg-opacity-10 rounded-lg">
                        <view class="flex items-center">
                            <image :src="getRoleImage('witch')" alt="女巫"
                                class="w-10 h-10 rounded-full object-cover mr-3"></image>
                            <text class="font-medium">女巫</text>
                        </view>
                        <view class="flex items-center">
                            <view
                                class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                @click="decreaseRole('witch')">
                                <text>-</text>
                            </view>
                            <text class="mx-3 w-6 text-center">{{ roleCount.witch }}</text>
                            <view
                                class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                @click="increaseRole('witch')">
                                <text>+</text>
                            </view>
                        </view>
                    </view>

                    <!-- 猎人 -->
                    <view v-if="roleCount.hunter && roleCount.hunter > 0"
                        class="flex justify-between items-center p-3 bg-white bg-opacity-10 rounded-lg">
                        <view class="flex items-center">
                            <image :src="getRoleImage('hunter')" alt="猎人"
                                class="w-10 h-10 rounded-full object-cover mr-3"></image>
                            <view>
                                <text class="font-medium">猎人</text>
                                <text class="text-white text-opacity-60 text-sm block">死亡时可带走一名玩家</text>
                            </view>
                        </view>
                        <view class="flex items-center">
                            <view
                                class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                @click="decreaseRole('hunter')">
                                <text>-</text>
                            </view>
                            <text class="mx-3 w-6 text-center">{{ roleCount.hunter }}</text>
                            <view
                                class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                @click="increaseRole('hunter')">
                                <text>+</text>
                            </view>
                        </view>
                    </view>

                    <!-- 守卫 -->
                    <view v-if="roleCount.guard && roleCount.guard > 0"
                        class="flex justify-between items-center p-3 bg-white bg-opacity-10 rounded-lg">
                        <view class="flex items-center">
                            <image :src="getRoleImage('guard')" alt="守卫"
                                class="w-10 h-10 rounded-full object-cover mr-3"></image>
                            <view>
                                <text class="font-medium">守卫</text>
                                <text class="text-white text-opacity-60 text-sm block">每晚保护一名玩家免受狼人伤害</text>
                            </view>
                        </view>
                        <view class="flex items-center">
                            <view
                                class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                @click="decreaseRole('guard')">
                                <text>-</text>
                            </view>
                            <text class="mx-3 w-6 text-center">{{ roleCount.guard }}</text>
                            <view
                                class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                @click="increaseRole('guard')">
                                <text>+</text>
                            </view>
                        </view>
                    </view>

                    <!-- 白痴 -->
                    <view v-if="roleCount.idiot && roleCount.idiot > 0"
                        class="flex justify-between items-center p-3 bg-white bg-opacity-10 rounded-lg">
                        <view class="flex items-center">
                            <image :src="getRoleImage('idiot')" alt="白痴"
                                class="w-10 h-10 rounded-full object-cover mr-3"></image>
                            <view>
                                <text class="font-medium">白痴</text>
                                <text class="text-white text-opacity-60 text-sm block">被投票处决后不会真正死亡</text>
                            </view>
                        </view>
                        <view class="flex items-center">
                            <view
                                class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                @click="decreaseRole('idiot')">
                                <text>-</text>
                            </view>
                            <text class="mx-3 w-6 text-center">{{ roleCount.idiot }}</text>
                            <view
                                class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                @click="increaseRole('idiot')">
                                <text>+</text>
                            </view>
                        </view>
                    </view>

                    <!-- 骑士 -->
                    <view v-if="roleCount.knight && roleCount.knight > 0"
                        class="flex justify-between items-center p-3 bg-white bg-opacity-10 rounded-lg">
                        <view class="flex items-center">
                            <image :src="getRoleImage('knight')" alt="骑士"
                                class="w-10 h-10 rounded-full object-cover mr-3"></image>
                            <view>
                                <text class="font-medium">骑士</text>
                                <text class="text-white text-opacity-60 text-sm block">可以决斗一名玩家，若为狼人则其死亡</text>
                            </view>
                        </view>
                        <view class="flex items-center">
                            <view
                                class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                @click="decreaseRole('knight')">
                                <text>-</text>
                            </view>
                            <text class="mx-3 w-6 text-center">{{ roleCount.knight }}</text>
                            <view
                                class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                @click="increaseRole('knight')">
                                <text>+</text>
                            </view>
                        </view>
                    </view>

                    <!-- 长老 -->
                    <view v-if="roleCount.elder && roleCount.elder > 0"
                        class="flex justify-between items-center p-3 bg-white bg-opacity-10 rounded-lg">
                        <view class="flex items-center">
                            <image :src="getRoleImage('elder')" alt="长老"
                                class="w-10 h-10 rounded-full object-cover mr-3"></image>
                            <view>
                                <text class="font-medium">长老</text>
                                <text class="text-white text-opacity-60 text-sm block">可以承受一次狼人攻击而不死亡</text>
                            </view>
                        </view>
                        <view class="flex items-center">
                            <view
                                class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                @click="decreaseRole('elder')">
                                <text>-</text>
                            </view>
                            <text class="mx-3 w-6 text-center">{{ roleCount.elder }}</text>
                            <view
                                class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                @click="increaseRole('elder')">
                                <text>+</text>
                            </view>
                        </view>
                    </view>

                    <!-- 乌鸦 -->
                    <view v-if="roleCount.crow && roleCount.crow > 0"
                        class="flex justify-between items-center p-3 bg-white bg-opacity-10 rounded-lg">
                        <view class="flex items-center">
                            <image :src="getRoleImage('crow')" alt="乌鸦"
                                class="w-10 h-10 rounded-full object-cover mr-3"></image>
                            <view>
                                <text class="font-medium">乌鸦</text>
                                <text class="text-white text-opacity-60 text-sm block">每晚可在一名玩家头上做记号</text>
                            </view>
                        </view>
                        <view class="flex items-center">
                            <view
                                class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                @click="decreaseRole('crow')">
                                <text>-</text>
                            </view>
                            <text class="mx-3 w-6 text-center">{{ roleCount.crow }}</text>
                            <view
                                class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                @click="increaseRole('crow')">
                                <text>+</text>
                            </view>
                        </view>
                    </view>

                    <!-- 自定义角色 -->
                    <view v-for="(count, key) in customRolesList" :key="key"
                        class="flex justify-between items-center p-3 bg-white bg-opacity-10 rounded-lg">
                        <view class="flex items-center">
                            <image :src="getRoleImage('custom')" :alt="getCustomRoleName(key)"
                                class="w-10 h-10 rounded-full object-cover mr-3"></image>
                            <view>
                                <text class="font-medium">{{ getCustomRoleName(key) }}</text>
                                <text class="text-white text-opacity-60 text-sm block">自定义角色</text>
                            </view>
                        </view>
                        <view class="flex items-center">
                            <view
                                class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                @click="decreaseRole(key)">
                                <text>-</text>
                            </view>
                            <text class="mx-3 w-6 text-center">{{ count }}</text>
                            <view
                                class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                @click="increaseRole(key)">
                                <text>+</text>
                            </view>
                        </view>
                    </view>

                    <view
                        class="w-full py-2 border border-dashed border-white border-opacity-30 rounded-lg text-white hover-opacity-10 transition flex items-center justify-center"
                        @click="showAddRoleModal">
                        <text class="fas fa-plus mr-2"></text> 添加更多角色
                    </view>
                </view>
            </view>

            <button class="w-full bg-red-600 hover-bg-red-700 text-white font-bold py-3 px-4 rounded-lg transition"
                @click="startGame">
                开始游戏
            </button>
        </view>

        <!-- 角色选择模态框 -->
        <view v-if="showRoleModal" class="modal-backdrop" @click="closeRoleModal">
            <view class="modal-content" @click.stop>
                <view class="modal-header">
                    <text class="text-xl font-bold text-white">选择角色</text>
                    <view class="close-btn" @click="closeRoleModal">×</view>
                </view>
                <view class="modal-body">
                    <scroll-view scroll-y style="max-height: 800rpx;">
                        <view class="space-y-3">
                            <!-- 猎人 -->
                            <view class="flex justify-between items-center p-3 bg-white bg-opacity-10 rounded-lg">
                                <view class="flex items-center">
                                    <image :src="getRoleImage('hunter')" alt="猎人"
                                        class="w-10 h-10 rounded-full object-cover mr-3"></image>
                                    <view>
                                        <text class="font-medium text-white">猎人</text>
                                        <text class="text-white text-opacity-60 text-sm block">死亡时可带走一名玩家</text>
                                    </view>
                                </view>
                                <view class="flex items-center">
                                    <view
                                        class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                        @click="decreaseRole('hunter')">
                                        <text>-</text>
                                    </view>
                                    <text class="mx-3 w-6 text-center">{{ roleCount.hunter || 0 }}</text>
                                    <view
                                        class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                        @click="increaseRole('hunter')">
                                        <text>+</text>
                                    </view>
                                </view>
                            </view>

                            <!-- 守卫 -->
                            <view class="flex justify-between items-center p-3 bg-white bg-opacity-10 rounded-lg">
                                <view class="flex items-center">
                                    <image :src="getRoleImage('guard')" alt="守卫"
                                        class="w-10 h-10 rounded-full object-cover mr-3"></image>
                                    <view>
                                        <text class="font-medium text-white">守卫</text>
                                        <text class="text-white text-opacity-60 text-sm block">每晚保护一名玩家免受狼人伤害</text>
                                    </view>
                                </view>
                                <view class="flex items-center">
                                    <view
                                        class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                        @click="decreaseRole('guard')">
                                        <text>-</text>
                                    </view>
                                    <text class="mx-3 w-6 text-center">{{ roleCount.guard || 0 }}</text>
                                    <view
                                        class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                        @click="increaseRole('guard')">
                                        <text>+</text>
                                    </view>
                                </view>
                            </view>

                            <!-- 白痴 -->
                            <view class="flex justify-between items-center p-3 bg-white bg-opacity-10 rounded-lg">
                                <view class="flex items-center">
                                    <image :src="getRoleImage('idiot')" alt="白痴"
                                        class="w-10 h-10 rounded-full object-cover mr-3"></image>
                                    <view>
                                        <text class="font-medium text-white">白痴</text>
                                        <text class="text-white text-opacity-60 text-sm block">被投票处决后不会真正死亡</text>
                                    </view>
                                </view>
                                <view class="flex items-center">
                                    <view
                                        class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                        @click="decreaseRole('idiot')">
                                        <text>-</text>
                                    </view>
                                    <text class="mx-3 w-6 text-center">{{ roleCount.idiot || 0 }}</text>
                                    <view
                                        class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                        @click="increaseRole('idiot')">
                                        <text>+</text>
                                    </view>
                                </view>
                            </view>

                            <!-- 骑士 -->
                            <view class="flex justify-between items-center p-3 bg-white bg-opacity-10 rounded-lg">
                                <view class="flex items-center">
                                    <image :src="getRoleImage('knight')" alt="骑士"
                                        class="w-10 h-10 rounded-full object-cover mr-3"></image>
                                    <view>
                                        <text class="font-medium text-white">骑士</text>
                                        <text class="text-white text-opacity-60 text-sm block">可以决斗一名玩家，若为狼人则其死亡</text>
                                    </view>
                                </view>
                                <view class="flex items-center">
                                    <view
                                        class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                        @click="decreaseRole('knight')">
                                        <text>-</text>
                                    </view>
                                    <text class="mx-3 w-6 text-center">{{ roleCount.knight || 0 }}</text>
                                    <view
                                        class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                        @click="increaseRole('knight')">
                                        <text>+</text>
                                    </view>
                                </view>
                            </view>

                            <!-- 长老 -->
                            <view class="flex justify-between items-center p-3 bg-white bg-opacity-10 rounded-lg">
                                <view class="flex items-center">
                                    <image :src="getRoleImage('elder')" alt="长老"
                                        class="w-10 h-10 rounded-full object-cover mr-3"></image>
                                    <view>
                                        <text class="font-medium text-white">长老</text>
                                        <text class="text-white text-opacity-60 text-sm block">可以承受一次狼人攻击而不死亡</text>
                                    </view>
                                </view>
                                <view class="flex items-center">
                                    <view
                                        class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                        @click="decreaseRole('elder')">
                                        <text>-</text>
                                    </view>
                                    <text class="mx-3 w-6 text-center">{{ roleCount.elder || 0 }}</text>
                                    <view
                                        class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                        @click="increaseRole('elder')">
                                        <text>+</text>
                                    </view>
                                </view>
                            </view>

                            <!-- 乌鸦 -->
                            <view class="flex justify-between items-center p-3 bg-white bg-opacity-10 rounded-lg">
                                <view class="flex items-center">
                                    <image :src="getRoleImage('crow')" alt="乌鸦"
                                        class="w-10 h-10 rounded-full object-cover mr-3"></image>
                                    <view>
                                        <text class="font-medium text-white">乌鸦</text>
                                        <text class="text-white text-opacity-60 text-sm block">每晚可在一名玩家头上做记号</text>
                                    </view>
                                </view>
                                <view class="flex items-center">
                                    <view
                                        class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                        @click="decreaseRole('crow')">
                                        <text>-</text>
                                    </view>
                                    <text class="mx-3 w-6 text-center">{{ roleCount.crow || 0 }}</text>
                                    <view
                                        class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                        @click="increaseRole('crow')">
                                        <text>+</text>
                                    </view>
                                </view>
                            </view>

                            <!-- 自定义角色 -->
                            <view v-for="(count, key) in customRolesList" :key="key"
                                class="flex justify-between items-center p-3 bg-white bg-opacity-10 rounded-lg">
                                <view class="flex items-center">
                                    <image :src="getRoleImage('custom')" :alt="getCustomRoleName(key)"
                                        class="w-10 h-10 rounded-full object-cover mr-3">
                                    </image>
                                    <view>
                                        <text class="font-medium text-white">{{ getCustomRoleName(key) }}</text>
                                        <text class="text-white text-opacity-60 text-sm block">自定义角色</text>
                                    </view>
                                </view>
                                <view class="flex items-center">
                                    <view
                                        class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                        @click="decreaseRole(key)">
                                        <text>-</text>
                                    </view>
                                    <text class="mx-3 w-6 text-center">{{ count }}</text>
                                    <view
                                        class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                        @click="increaseRole(key)">
                                        <text>+</text>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </scroll-view>

                    <!-- 分隔线 -->
                    <view class="my-4 border-t border-white border-opacity-20"></view>

                    <!-- 自定义角色输入区 -->
                    <view class="mt-4 mb-4">
                        <view class="flex justify-between items-center mb-2 cursor-pointer"
                            @click="toggleCustomRoleForm">
                            <text class="text-white font-medium">添加自定义角色</text>
                            <view class="text-white">
                                <text class="fas"
                                    :class="showCustomRoleForm ? 'fa-chevron-up' : 'fa-chevron-down'"></text>
                            </view>
                        </view>

                        <!-- 展开后的表单 -->
                        <view v-if="showCustomRoleForm" class="mt-2 bg-white bg-opacity-10 p-3 rounded-lg">
                            <view class="flex space-x-2 mb-3">
                                <input v-model="customRoleName" placeholder="输入角色名称" class="flex-grow" />
                                <view class="flex items-center">
                                    <view
                                        class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                        @click="decreaseCustomRoleCount">
                                        <text>-</text>
                                    </view>
                                    <text class="mx-3 w-6 text-center">{{ customRoleCount }}</text>
                                    <view
                                        class="w-8 h-8 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center text-sm font-bold"
                                        @click="increaseCustomRoleCount">
                                        <text>+</text>
                                    </view>
                                </view>
                            </view>
                            <view
                                class="bg-red-600 text-white text-sm rounded-lg py-2 flex items-center justify-center cursor-pointer"
                                @click="addCustomRole">
                                添加角色
                            </view>
                        </view>
                    </view>
                </view>
                <view class="modal-footer">
                    <button class="confirm-btn" @click="confirmRoleSelection">确认选择</button>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    import { roleConfig } from '@/utils/game.js';
    import { wolfRoomApi } from '@/utils/wolfApi.js';
    import SafeHeader from '@/components/SafeHeader.vue';

    export default {
        components: {
            SafeHeader
        },
        data() {
            return {
                roleCount: {
                    werewolf: 3,
                    villager: 4,
                    seer: 1,
                    witch: 1,
                    hunter: 0,
                    guard: 0,
                    idiot: 0,
                    knight: 0,
                    elder: 0,
                    crow: 0
                },
                playerId: '',
                showRoleModal: false,
                customRoleName: '',
                customRoleCount: 1,
                showCustomRoleForm: false
            }
        },
        computed: {
            getTotalRoles() {
                return Object.values(this.roleCount).reduce((sum, count) => sum + count, 0);
            },
            // 获取自定义角色列表
            customRolesList() {
                const customRoles = {};

                // 遍历所有角色，筛选出自定义角色
                for (const [key, value] of Object.entries(this.roleCount)) {
                    if (key.startsWith('custom_') && value > 0) {
                        customRoles[key] = value;
                    }
                }

                return customRoles;
            }
        },
        onLoad() {
            // 初始化角色配置
            this.roleCount = JSON.parse(JSON.stringify(roleConfig.default));
            // 确保默认角色之外的其他角色也有初始值
            this.initializeExtraRoles();
            // 确保玩家ID存在
            this.playerId = getApp().globalData.playerId;
        },
        methods: {
            // 初始化额外角色
            initializeExtraRoles() {
                const extraRoles = Object.keys(roleConfig.extra);
                extraRoles.forEach(role => {
                    if (!this.roleCount[role]) {
                        this.$set(this.roleCount, role, 0);
                    }
                });
            },
            increaseRole(role) {
                if (!this.roleCount[role]) {
                    this.$set(this.roleCount, role, 0);
                }
                this.roleCount[role]++;
            },
            decreaseRole(role) {
                if (this.roleCount[role] && this.roleCount[role] > 0) {
                    this.roleCount[role]--;
                }
            },
            increaseCustomRoleCount() {
                this.customRoleCount++;
            },
            decreaseCustomRoleCount() {
                if (this.customRoleCount > 0) {
                    this.customRoleCount--;
                }
            },
            // 获取自定义角色名称
            getCustomRoleName(key) {
                if (key.startsWith('custom_')) {
                    return key.replace('custom_', '');
                }
                return key;
            },

            getRoleImage(role) {
                return roleConfig.roleImages[role.toUpperCase()];
            },
            // 切换自定义角色表单显示状态
            toggleCustomRoleForm() {
                this.showCustomRoleForm = !this.showCustomRoleForm;
            },
            addCustomRole() {
                if (!this.customRoleName.trim()) {
                    uni.showToast({
                        title: '请输入角色名称',
                        icon: 'none'
                    });
                    return;
                }

                if (this.customRoleCount <= 0) {
                    uni.showToast({
                        title: '角色数量必须大于0',
                        icon: 'none'
                    });
                    return;
                }

                // 使用自定义角色名作为键，确保不会与预定义角色冲突
                const roleKey = 'custom_' + this.customRoleName.trim();

                // 检查是否已经存在同名角色
                if (this.roleCount[roleKey]) {
                    // 如果已存在，增加数量
                    this.roleCount[roleKey] += this.customRoleCount;
                } else {
                    // 如果不存在，添加新角色
                    this.$set(this.roleCount, roleKey, this.customRoleCount);
                }

                // 重置输入
                this.customRoleName = '';
                this.customRoleCount = 1;

                // 显示提示
                uni.showToast({
                    title: '自定义角色添加成功',
                    icon: 'none'
                });
            },
            showAddRoleModal() {
                this.showRoleModal = true;
            },
            closeRoleModal() {
                this.showRoleModal = false;
            },
            confirmRoleSelection() {
                this.closeRoleModal();

                // 计算已选角色总数
                const totalSelectedRoles = Object.values(this.roleCount).reduce((sum, count) => sum + count, 0);

                // 显示提示
                uni.showToast({
                    title: `已添加${totalSelectedRoles}名玩家`,
                    icon: 'none',
                    duration: 1500
                });
            },
            startGame() {
                // 验证至少有一个玩家
                const totalPlayers = Object.values(this.roleCount).reduce((sum, count) => sum + count, 0);
                if (totalPlayers < 3) {
                    uni.showToast({
                        title: '请至少选择3个角色',
                        icon: 'none'
                    });
                    return;
                }

                // 创建房间
                this.createRoom();
            },
            // 创建房间
            async createRoom() {
                try {
                    // 获取玩家名称，如果没有则使用默认值
                    const playerName = getApp().globalData.playerName || '房主';

                    // 准备角色配置数据
                    const roleConfigData = [];
                    for (const [roleName, count] of Object.entries(this.roleCount)) {
                        if (count > 0) {
                            roleConfigData.push({
                                roleName,
                                roleCount: count
                            });
                        }
                    }

                    if (!this.playerId) {
                        await wechatApi.login();
                    }



                    // 准备创建房间的数据
                    const roomData = {
                        creatorName: playerName,
                        creatorId: this.playerId,
                        roleConfigs: roleConfigData,
                        isPublic: true
                    };

                    console.log('roomData', roomData);

                    // 显示加载中
                    uni.showLoading({
                        title: '创建房间中...'
                    });

                    // 调用API创建房间
                    const roomInfo = await wolfRoomApi.createRoom(roomData);
                    console.log('roomInfo', roomInfo);
                    // 关闭加载提示
                    uni.hideLoading();

                    if (roomInfo && roomInfo.id) {
                        // 存储房间信息
                        uni.setStorageSync('wolfRoomInfo', roomInfo);

                        // 保存玩家名称以便后续使用
                        uni.setStorageSync('playerName', playerName);

                        // 跳转到游戏创建成功页面
                        uni.redirectTo({
                            url: `/wolfkiller/pages/game-created/game-created?roomId=${roomInfo.id}&roomCode=${roomInfo.roomCode}`,
                            fail: (err) => {
                                console.error('页面跳转失败:', err);
                                uni.showToast({
                                    title: '页面跳转失败',
                                    icon: 'none'
                                });
                            }
                        });
                    } else {
                        throw new Error('创建房间返回数据异常');
                    }
                } catch (error) {
                    // 关闭加载提示
                    uni.hideLoading();

                    console.error('创建房间失败:', error);
                    uni.showToast({
                        title: error.message || '创建房间失败',
                        icon: 'none'
                    });
                }
            }
        }
    }
</script>

<style scoped>
    /* 容器样式 */
    .container {
        padding: 32rpx;
        background-color: #2c3e50;
        background-image: url("https://images.unsplash.com/photo-1478131143081-80f7f84ca84d?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80");
        background-size: cover;
        background-attachment: fixed;
        background-position: center;
        min-height: 100vh;
    }

    /* 输入框样式修复 */
    input {
        height: 80rpx;
        box-sizing: border-box;
        color: #fff;
        padding: 0 16px;
        background-color: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 8px;
    }

    /* 修复placeholder颜色 */
    input::placeholder {
        color: rgba(255, 255, 255, 0.6);
    }

    /* 角色配置项间距 */
    .space-y-3>view:not(:first-child) {
        margin-top: 12px;
    }

    /* 角色图标尺寸 */
    .w-10,
    .h-10 {
        width: 40px;
        height: 40px;
        min-width: 40px;
        /* 添加最小宽度，防止被挤压 */
        min-height: 40px;
        /* 添加最小高度，防止被挤压 */
    }

    /* 加减按钮尺寸 */
    .w-8,
    .h-8 {
        width: 32px;
        height: 32px;
    }

    /* 确保圆形效果 */
    .rounded-full {
        border-radius: 50%;
        aspect-ratio: 1/1;
        /* 确保宽高比为1:1 */
        overflow: hidden;
        /* 确保内容不会溢出圆形区域 */
    }

    /* 按钮样式 */
    button {
        border: none;
        background-color: transparent;
        padding: 0;
        margin: 0;
        line-height: normal;
    }

    .py-3 {
        padding-top: 12px;
        padding-bottom: 12px;
    }

    /* 修复玻璃效果和背景透明度 */
    .bg-white.bg-opacity-10 {
        background-color: rgba(255, 255, 255, 0.1);
    }

    /* 修复边框样式 */
    .border-white.border-opacity-30 {
        border-color: rgba(255, 255, 255, 0.3);
    }

    /* 确保加减按钮可见 */
    .flex.items-center .bg-white.bg-opacity-20 {
        background-color: rgba(255, 255, 255, 0.2);
        display: flex;
        justify-content: center;
        align-items: center;
    }

    /* 角色列表项背景 */
    .p-3.bg-white.bg-opacity-10 {
        background-color: rgba(255, 255, 255, 0.1);
    }

    /* 修复增加角色按钮样式 */
    .border-dashed.border-white.border-opacity-30 {
        border: 1px dashed rgba(255, 255, 255, 0.3);
    }

    /* 修复启动游戏按钮样式 */
    .bg-red-600 {
        background-color: #dc2626;
    }

    .hover-bg-red-700:hover {
        background-color: #b91c1c;
    }

    /* 修复w-full样式 */
    .w-full {
        width: 100%;
    }

    /* 修复flex样式 */
    .flex {
        display: flex;
    }

    /* 模态框样式 */
    .modal-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    }

    .modal-content {
        width: 90%;
        max-width: 600px;
        background-color: #2c3e50;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        display: flex;
        flex-direction: column;
        max-height: 90vh;
    }

    .modal-header {
        padding: 16px;
        background-color: rgba(255, 255, 255, 0.1);
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-shrink: 0;
    }

    .close-btn {
        font-size: 24px;
        color: white;
        width: 32px;
        height: 32px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.1);
    }

    .modal-body {
        padding: 16px;
        flex-grow: 1;
        overflow: hidden;
    }

    .modal-footer {
        padding: 16px;
        display: flex;
        justify-content: flex-end;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        flex-shrink: 0;
    }

    .confirm-btn {
        background-color: #dc2626;
        color: white;
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: bold;
    }

    .text-sm {
        font-size: 12px;
    }

    .text-opacity-60 {
        opacity: 0.6;
    }
</style>