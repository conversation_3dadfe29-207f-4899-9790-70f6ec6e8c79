<template>
    <view class="container">
        <!-- 使用安全头部组件 -->
        <safe-header title="游戏详情" :show-back="true"></safe-header>

        <view v-if="isLoading" class="loading-container">
            <text class="text-white">加载中...</text>
        </view>

        <view v-else
            class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl shadow-lg p-6 mb-6 text-white">


            <text class="text-white text-opacity-80 mb-4 block">{{ gameInfo.playerCount }}人局 · {{
                formatDate(gameInfo.createTime) }}</text>

            <view class="players-section border-t border-white border-opacity-20 pt-4">
                <text class="font-medium mb-2 block">玩家角色</text>

                <view v-if="players.length === 0" class="text-center py-4">
                    <text class="text-white text-opacity-70">暂无玩家信息</text>
                </view>

                <view v-else class="space-y-3">
                    <view v-for="(player, index) in filterPlayer" :key="index"
                        class="flex justify-between items-center p-3 bg-white bg-opacity-10 rounded-lg">
                        <view class="flex items-center">
                            <image v-if="player.avatarUrl" :src="player.avatarUrl" alt="玩家头像"
                                class="w-10 h-10 rounded-full object-cover mr-3"></image>
                            <view v-else class="w-10 h-10 rounded-full object-cover mr-3 bg-gray-300"></view>
                            <view>
                                <text class="font-medium block">{{ player.playerName }}</text>
                                <text v-if="player.isCreator" class="text-xs text-white text-opacity-70 block">房主</text>
                            </view>
                        </view>
                        <view class="flex items-center">
                            <image
                                :src="creatorInGame&&player.playerId!=player.creatorId?'':getRoleImage(player.roleName)"
                                alt="角色" class="w-6 h-6 rounded-full object-cover mr-1"></image>
                            <text
                                class="text-sm font-medium">{{creatorInGame&&player.playerId!=player.creatorId?"?":getRoleCnName(player.roleName)
                                }}</text>
                        </view>
                    </view>
                </view>

                <button v-if="players&&players.length > 3 && !showAllPlayers"
                    class="view-all-btn w-full mt-4 py-2 border border-white border-opacity-30 rounded-lg text-white hover-opacity-10 transition"
                    @click="toggleShowAllPlayers">
                    查看全部玩家
                </button>
                <button v-else-if="players&&players.length > 3 && showAllPlayers"
                    class="view-all-btn w-full mt-4 py-2 border border-white border-opacity-30 rounded-lg text-white hover-opacity-10 transition"
                    @click="toggleShowAllPlayers">
                    收起玩家列表
                </button>
            </view>
        </view>

        <view class="flex space-x-3 mb-6">
            <button open-type="share"
                class="btn-primary flex-1 bg-green-500 hover-bg-green-600 text-white font-medium py-4 px-4 rounded-lg transition flex items-center justify-center">
                <text class="fas fa-share-alt mr-2"></text> 微信分享
            </button>
        </view>

        <view class="flex space-x-3">
            <button
                class="action-btn flex-1 bg-blue-600 text-white font-bold py-3 px-4 rounded-lg flex items-center justify-center"
                @click="startNewGame">
                <text class="fas fa-redo mr-2"></text> 重新来
            </button>
            <button
                class="action-btn flex-1 bg-red-600 text-white font-bold py-3 px-4 rounded-lg flex items-center justify-center"
                @click="deleteGame">
                <text class="fas fa-trash mr-2"></text> 删除游戏
            </button>
        </view>
    </view>
</template>

<script>
    import { wolfRoomApi, wolfPlayerApi, wolfRoleApi } from '@/utils/wolfApi.js';
    import { roleConfig } from '@/utils/game.js';
    import SafeHeader from '@/components/SafeHeader.vue';

    export default {
        components: {
            SafeHeader
        },
        data() {
            return {
                roomId: '',
                isLoading: true,
                gameInfo: {
                    roomName: '',
                    playerCount: 0,
                    createTime: '',
                    roomCode: ''
                },
                players: [],
                showAllPlayers: false,
                roleMap: roleConfig.roleMap,
                creatorInGame: false,
            }
        },
        onShareAppMessage() {
            return {
                title: '狼人杀助手(房号' + this.gameInfo.roomCode + ')',
                path: '/wolfkiller/pages/game-room/game-room?roomId=' + this.roomId,
                imageUrl: "/wolfkiller/static/images/wolf.jpg"
            }

        },
        onLoad(options) {
            console.log('游戏详情页面接收参数:', options);

            // 获取游戏ID
            if (options.roomId) {
                this.roomId = options.roomId;
                this.loadGameInfo();
            } else {
                this.isLoading = false;
                uni.showToast({
                    title: '未获取到游戏ID',
                    icon: 'none'
                });
            }
        },
        computed: {
            // 过滤后的显示的用户
            filterPlayer() {
                if (this.showAllPlayers) {
                    return this.players;
                } else {
                    return this.players.slice(0, 3);
                }
            }
        },
        methods: {
            // 加载游戏信息
            async loadGameInfo() {
                this.isLoading = true;
                try {
                    // 获取房间信息
                    const roomInfo = await wolfRoomApi.getRoomInfo(this.roomId);
                    console.log('获取到的房间信息:', roomInfo);

                    if (roomInfo) {
                        this.gameInfo = {
                            playerCount: roomInfo.totalPlayers || 0,
                            createTime: roomInfo.createTime,
                            roomCode: roomInfo.roomCode
                        };

                        // 获取房间玩家信息
                        await this.loadRoomPlayers();
                    } else {
                        uni.showToast({
                            title: '房间信息不存在',
                            icon: 'none'
                        });
                    }
                } catch (error) {
                    console.error('加载游戏信息失败:', error);
                    uni.showToast({
                        title: '加载游戏信息失败',
                        icon: 'none'
                    });
                } finally {
                    this.isLoading = false;
                }
            },

            // 加载房间玩家信息
            async loadRoomPlayers() {
                try {
                    const players = await wolfPlayerApi.getRoomPlayers(this.roomId);
                    console.log('获取到的玩家信息:', players);

                    if (players && players.length > 0) {
                        this.players = players

                        this.creatorInGame = players.some(player => player.creatorId == player.playerId);
                        console.log('creatorInGame:', this.creatorInGame);
                    } else {
                        this.players = []
                        uni.showToast({
                            title: '暂无玩家信息',
                            icon: 'none'
                        });
                    }

                } catch (error) {
                    console.error('加载玩家信息失败:', error);
                }
            },

            // 切换显示所有玩家
            toggleShowAllPlayers() {
                this.showAllPlayers = !this.showAllPlayers;
                this.loadRoomPlayers();
            },

            // 获取角色中文名称
            getRoleCnName(roleName) {
                let name = this.roleMap[roleName];
                if (!name) {
                    if (roleName.indexOf('CUSTOM') !== -1) {
                        name = roleName.replace('CUSTOM_', '');
                    } else {
                        name = '自定义';
                    }
                }
                return name;
            },

            // 获取角色图片
            getRoleImage(roleName) {
                let image = roleConfig.roleImages[roleName.toUpperCase()];
                if (!image) {
                    image = roleConfig.roleImages['CUSTOM'];
                }
                return image;
            },

            // 格式化日期
            formatDate(dateString) {
                if (!dateString) return '';

                const date = new Date(dateString);
                return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
            },

            // 开始新游戏
            async startNewGame() {
                const roomInfo = await wolfRoomApi.restartGame(this.roomId);
                if (roomInfo) {
                    // 重新加载玩家信息
                    this.loadRoomPlayers();
                }
            },

            // 删除游戏
            deleteGame() {
                uni.showModal({
                    title: '删除确认',
                    content: '确定要删除此游戏记录吗？删除后将无法恢复。',
                    success: (res) => {
                        if (res.confirm) {
                            this.deleteGameRecord();
                        }
                    }
                });
            },

            // 删除游戏记录
            async deleteGameRecord() {
                this.isLoading = true;
                try {
                    // 这里应该调用删除游戏记录的API
                    // 由于API可能尚未实现，此处只模拟成功操作
                    console.log('删除游戏记录，ID:', this.roomId);

                    await wolfRoomApi.deleteRoom(this.roomId);

                    uni.showToast({
                        title: '游戏已删除',
                        icon: 'success',
                        duration: 1500,
                        success: () => {
                            setTimeout(() => {
                                uni.navigateBack();
                            }, 1500);
                        }
                    });
                } catch (error) {
                    console.error('删除游戏记录失败:', error);
                    uni.showToast({
                        title: '删除失败',
                        icon: 'none'
                    });
                } finally {
                    this.isLoading = false;
                }
            }
        }
    }
</script>

<style>
    .container {
        padding: 32rpx;
        background-color: #2c3e50;
        background-image: url("https://images.unsplash.com/photo-1478131143081-80f7f84ca84d?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80");
        background-size: cover;
        background-attachment: fixed;
        background-position: center;
        min-height: 100vh;
    }

    .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 200px;
    }

    .space-y-3>view:not(:first-child) {
        margin-top: 12px;
    }

    .space-x-3 {
        display: flex;
    }

    .space-x-3>view:not(:first-child),
    .space-x-3>button:not(:first-child) {
        margin-left: 12px;
    }

    button {
        border: none;
        background-color: transparent;
        padding: 0;
        margin: 0;
        line-height: normal;
    }

    .py-2 {
        padding-top: 8px;
        padding-bottom: 8px;
    }

    .py-3 {
        padding-top: 12px;
        padding-bottom: 12px;
    }

    .py-4 {
        padding-top: 16px;
        padding-bottom: 16px;
    }

    .w-6,
    .h-6 {
        width: 24px;
        height: 24px;
    }

    .w-10,
    .h-10 {
        width: 40px;
        height: 40px;
    }

    /* 玩家角色区域上方的边框 */
    .players-section {
        border-top: 1px solid rgba(255, 255, 255, 0.2);
        padding-top: 16px;
    }

    /* 查看全部玩家按钮边框 */
    .view-all-btn {
        border: 1px solid rgba(255, 255, 255, 0.3);
        background-color: transparent;
        transition: background-color 0.3s ease;
    }

    .view-all-btn:active {
        background-color: rgba(255, 255, 255, 0.1);
    }

    /* 修复按钮样式 */
    .action-btn {
        transition: background-color 0.3s ease;
    }

    .bg-red-600 {
        background-color: #dc2626;
    }

    .bg-blue-600 {
        background-color: #2563eb;
    }

    .action-btn:active {
        opacity: 0.9;
    }

    .flex {
        display: flex;
    }

    .items-center {
        align-items: center;
    }

    .justify-center {
        justify-content: center;
    }

    .flex-1 {
        flex: 1;
    }
</style>