/* 通用样式文件，替代Tailwind CSS功能 */

/* 基础样式 */
page {
  background-color: #2c3e50;
  background-image: url("https://images.unsplash.com/photo-1478131143081-80f7f84ca84d?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80");
  background-size: cover;
  background-attachment: fixed;
  background-position: center;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica,
    Arial, sans-serif;
  color: #ffffff;
  min-height: 100vh;
}

/* 容器样式 */
.container {
  padding: 32rpx;
  margin: 0 auto;
  min-height: 100vh;
}

/* 文本样式 */
.text-center {
  text-align: center;
}

.text-white {
  color: #ffffff;
}

.text-opacity-90 {
  opacity: 0.9;
}

.text-opacity-80 {
  opacity: 0.8;
}

.text-opacity-70 {
  opacity: 0.7;
}

.text-opacity-60 {
  opacity: 0.6;
}

.text-opacity-50 {
  opacity: 0.5;
}

.font-bold {
  font-weight: bold;
}

.font-medium {
  font-weight: 500;
}

.font-mono {
  font-family: monospace;
}

.text-4xl {
  font-size: 36px;
}

.text-3xl {
  font-size: 30px;
}

.text-2xl {
  font-size: 24px;
}

.text-xl {
  font-size: 20px;
}

.text-lg {
  font-size: 18px;
}

.text-base {
  font-size: 16px;
}

.text-sm {
  font-size: 14px;
}

.text-xs {
  font-size: 12px;
}

/* 边距样式 */
.m-1 {
  margin: 4px;
}
.m-2 {
  margin: 8px;
}
.m-3 {
  margin: 12px;
}
.m-4 {
  margin: 16px;
}
.m-6 {
  margin: 24px;
}
.m-8 {
  margin: 32px;
}

.mt-1 {
  margin-top: 4px;
}
.mt-2 {
  margin-top: 8px;
}
.mt-4 {
  margin-top: 16px;
}
.mt-6 {
  margin-top: 24px;
}
.mt-8 {
  margin-top: 32px;
}

.mb-1 {
  margin-bottom: 4px;
}
.mb-2 {
  margin-bottom: 8px;
}
.mb-3 {
  margin-bottom: 12px;
}
.mb-4 {
  margin-bottom: 16px;
}
.mb-6 {
  margin-bottom: 24px;
}
.mb-8 {
  margin-bottom: 32px;
}

.mr-1 {
  margin-right: 4px;
}
.mr-2 {
  margin-right: 8px;
}
.mr-3 {
  margin-right: 12px;
}
.mr-4 {
  margin-right: 16px;
}

.ml-1 {
  margin-left: 4px;
}
.ml-2 {
  margin-left: 8px;
}
.ml-3 {
  margin-left: 12px;
}
.ml-4 {
  margin-left: 16px;
}

.mx-1 {
  margin-left: 4px;
  margin-right: 4px;
}
.mx-2 {
  margin-left: 8px;
  margin-right: 8px;
}
.mx-3 {
  margin-left: 12px;
  margin-right: 12px;
}
.mx-4 {
  margin-left: 16px;
  margin-right: 16px;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-1 {
  margin-top: 4px;
  margin-bottom: 4px;
}
.my-2 {
  margin-top: 8px;
  margin-bottom: 8px;
}
.my-4 {
  margin-top: 16px;
  margin-bottom: 16px;
}
.my-6 {
  margin-top: 24px;
  margin-bottom: 24px;
}
.my-8 {
  margin-top: 32px;
  margin-bottom: 32px;
}

/* 内边距样式 */
.p-1 {
  padding: 4px;
}
.p-2 {
  padding: 8px;
}
.p-3 {
  padding: 12px;
}
.p-4 {
  padding: 16px;
}
.p-6 {
  padding: 24px;
}

.pt-1 {
  padding-top: 4px;
}
.pt-2 {
  padding-top: 8px;
}
.pt-4 {
  padding-top: 16px;
}

.pb-1 {
  padding-bottom: 4px;
}
.pb-2 {
  padding-bottom: 8px;
}
.pb-4 {
  padding-bottom: 16px;
}

.pr-1 {
  padding-right: 4px;
}
.pr-2 {
  padding-right: 8px;
}
.pr-4 {
  padding-right: 16px;
}
.pr-10 {
  padding-right: 40px;
}

.pl-1 {
  padding-left: 4px;
}
.pl-2 {
  padding-left: 8px;
}
.pl-4 {
  padding-left: 16px;
}
.pl-5 {
  padding-left: 20px;
}

.px-1 {
  padding-left: 4px;
  padding-right: 4px;
}
.px-2 {
  padding-left: 8px;
  padding-right: 8px;
}
.px-3 {
  padding-left: 12px;
  padding-right: 12px;
}
.px-4 {
  padding-left: 16px;
  padding-right: 16px;
}

.py-1 {
  padding-top: 4px;
  padding-bottom: 4px;
}
.py-2 {
  padding-top: 8px;
  padding-bottom: 8px;
}
.py-3 {
  padding-top: 12px;
  padding-bottom: 12px;
}
.py-4 {
  padding-top: 16px;
  padding-bottom: 16px;
}

/* 圆角样式 */
.rounded-full {
  border-radius: 999px;
}
.rounded-lg {
  border-radius: 8px;
}
.rounded-xl {
  border-radius: 12px;
}
.rounded {
  border-radius: 4px;
}

/* Flex布局 */
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.flex-1 {
  flex: 1;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.space-x-1 > view:not(:first-child) {
  margin-left: 4px;
}
.space-x-2 > view:not(:first-child) {
  margin-left: 8px;
}
.space-x-3 > view:not(:first-child),
.space-x-3 > button:not(:first-child) {
  margin-left: 12px;
}
.space-x-4 > view:not(:first-child) {
  margin-left: 16px;
}
.space-y-1 > view:not(:first-child) {
  margin-top: 4px;
}
.space-y-2 > view:not(:first-child) {
  margin-top: 8px;
}
.space-y-3 > view:not(:first-child),
.space-y-3 > button:not(:first-child) {
  margin-top: 12px;
}
.space-y-4 > view:not(:first-child) {
  margin-top: 16px;
}

/* 显示隐藏 */
.hidden {
  display: none;
}
.block {
  display: block;
}

/* 网格布局 */
.grid {
  display: grid;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.gap-3 {
  gap: 12px;
}
.gap-4 {
  gap: 16px;
}

/* 背景色与文本色 */
.bg-red-500 {
  background-color: #ef4444;
}
.bg-red-600 {
  background-color: #dc2626;
}
.bg-red-700 {
  background-color: #b91c1c;
}
.bg-blue-500 {
  background-color: #3b82f6;
}
.bg-blue-600 {
  background-color: #2563eb;
}
.bg-blue-700 {
  background-color: #1d4ed8;
}
.bg-green-500 {
  background-color: #10b981;
}
.bg-green-600 {
  background-color: #059669;
}
.bg-green-100 {
  background-color: #d1fae5;
}
.bg-purple-600 {
  background-color: #9333ea;
}
.bg-purple-700 {
  background-color: #7e22ce;
}
.bg-gray-600 {
  background-color: #4b5563;
}
.bg-gray-700 {
  background-color: #374151;
}
.bg-yellow-500 {
  background-color: #f59e0b;
}
.bg-white {
  background-color: #ffffff;
}

.text-red-400 {
  color: #f87171;
}
.text-blue-300 {
  color: #93c5fd;
}
.text-blue-400 {
  color: #60a5fa;
}
.text-green-400 {
  color: #34d399;
}
.text-green-500 {
  color: #10b981;
}
.text-white {
  color: #ffffff;
}

/* 透明度 */
.bg-opacity-10 {
  background-color: rgba(255, 255, 255, 0.1);
}
.bg-opacity-20 {
  background-color: rgba(255, 255, 255, 0.2);
}
.bg-opacity-30 {
  background-color: rgba(255, 255, 255, 0.3);
}

/* 边框 */
.border {
  border-width: 1px;
}
.border-t {
  border-top-width: 1px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-l {
  border-left-width: 1px;
}
.border-r {
  border-right-width: 1px;
}
.border-dashed {
  border-style: dashed;
}
.border-white {
  border-color: #ffffff;
}
.border-opacity-20 {
  border-color: rgba(255, 255, 255, 0.2);
}
.border-opacity-30 {
  border-color: rgba(255, 255, 255, 0.3);
}
.border-2 {
  border-width: 2px;
}

/* 尺寸 */
.w-full {
  width: 100%;
}
.w-6 {
  width: 24px;
}
.w-8 {
  width: 32px;
}
.w-10 {
  width: 40px;
}
.w-14 {
  width: 56px;
}
.w-16 {
  width: 64px;
}
.w-24 {
  width: 96px;
}
.w-48 {
  width: 192px;
}

.h-6 {
  height: 24px;
}
.h-8 {
  height: 32px;
}
.h-10 {
  height: 40px;
}
.h-14 {
  height: 56px;
}
.h-16 {
  height: 64px;
}
.h-24 {
  height: 96px;
}
.h-48 {
  height: 192px;
}
.h-48 {
  height: 192px;
}

.min-h-screen {
  min-height: 100vh;
}

/* 定位 */
.relative {
  position: relative;
}
.absolute {
  position: absolute;
}
.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.top-h-2 {
  top: 50%;
}
.bottom-0 {
  bottom: 0;
}
.left-0 {
  left: 0;
}
.right-4 {
  right: 16px;
}
.transform {
  transform: translateY(-50%);
}
.transform-none {
  transform: none;
}

/* 文本样式 */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.list-disc {
  list-style-type: disc;
  padding-left: 20px;
}

/* 阴影 */
.shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
.shadow-2xl {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* 图片 */
.object-cover {
  object-fit: cover;
}

/* 文本阴影 */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* 过渡效果 */
.transition {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}
.duration-300 {
  transition-duration: 300ms;
}
.duration-500 {
  transition-duration: 500ms;
}

.hover-scale {
  transform: scale(1);
  transition: transform 0.3s ease;
}

/* 玻璃效果 */
.glass-effect {
  background-color: rgba(255, 255, 255, 0.15) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 渐变背景 */
.moon-bg {
  background: linear-gradient(135deg, #2c3e50 0%, #4a69bd 100%);
}

.bg-gradient-to-t {
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
}

/* 翻转卡片 */
.flip-card {
  perspective: 1000px;
  height: 300px;
}

.flip-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.8s;
  transform-style: preserve-3d;
}

.flip-card.flipped .flip-card-inner {
  transform: rotateY(180deg);
}

.flip-card-front,
.flip-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  border-radius: 12px;
}

.flip-card-back {
  transform: rotateY(180deg);
}

/* 脉冲动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.pulse-animation {
  animation: pulse 2s infinite;
}

/* hover效果 */
.hover-bg-red-700 {
  transition: background-color 0.3s ease;
}
.hover-bg-red-700:hover {
  background-color: #b91c1c !important;
}

.hover-bg-blue-700 {
  transition: background-color 0.3s ease;
}
.hover-bg-blue-700:hover {
  background-color: #1d4ed8 !important;
}

.hover-bg-green-600 {
  transition: background-color 0.3s ease;
}
.hover-bg-green-600:hover {
  background-color: #059669 !important;
}

.hover-bg-purple-700 {
  transition: background-color 0.3s ease;
}
.hover-bg-purple-700:hover {
  background-color: #7e22ce !important;
}

.hover-bg-gray-700 {
  transition: background-color 0.3s ease;
}
.hover-bg-gray-700:hover {
  background-color: #374151 !important;
}

.hover-opacity-10 {
  transition: background-color 0.3s ease;
}
.hover-opacity-10:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.hover-opacity-30 {
  transition: background-color 0.3s ease;
}
.hover-opacity-30:hover {
  background-color: rgba(255, 255, 255, 0.3) !important;
}

/* 转换效果 */
.hover-translate-y-1 {
  transition: transform 0.3s ease;
}
.hover-translate-y-1:hover {
  transform: translateY(-4px) !important;
}

.hover-scale-105 {
  transition: transform 0.5s ease;
}
.hover-scale-105:hover {
  transform: scale(1.05) !important;
}

/* 光标样式 - uniapp中可能不适用 */
.cursor-pointer {
  cursor: pointer;
}

/* 溢出处理 */
.overflow-hidden {
  overflow: hidden;
}

/* 透明背景色修复 */
.backdrop-filter {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.backdrop-blur-lg {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
}

/* 修复页面背景设置 */
page {
  min-height: 100vh;
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

/* 修复透明度和按钮样式 */
button {
  color: #fff;
}

.bg-red-600,
.bg-blue-600,
.bg-green-500,
.bg-purple-600,
.bg-gray-600 {
  position: relative;
  z-index: 1;
}

.inline-block {
  display: inline-block;
}

/* 修复UniApp中的文本和图标排列 */
text.fas,
text.fab,
text.far {
  font-family: "Font Awesome 6 Free";
}

/* 确保背景容器正确显示 */
.bg-white.bg-opacity-10,
.bg-white.bg-opacity-20,
.bg-white.bg-opacity-30 {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.15);
  position: relative;
  z-index: 1;
}
