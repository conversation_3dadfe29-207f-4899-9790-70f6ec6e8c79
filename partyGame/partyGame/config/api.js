const BASE_URL = "http://localhost:8080/api";

// 统一的请求方法
const request = (url, method = "GET", data = null) => {
  return new Promise((resolve, reject) => {
    uni.request({
      url: `${BASE_URL}${url}`,
      method: method,
      data: data,
      header: {
        "content-type": "application/json",
      },
      success: (res) => {
        if (res.data && res.data.code === 200) {
          resolve(res.data.data);
        } else {
          reject(new Error(res.data?.message || "请求失败"));
        }
      },
      fail: (err) => {
        reject(new Error("网络请求失败"));
      },
    });
  });
};

// API接口定义
export const api = {
  // 房间相关接口
  room: {
    // 创建房间
    create: (data) => request("/room/create", "POST", data),
    // 加入房间
    join: (data) => request("/room/join", "POST", data),
    // 获取房间信息
    getInfo: (roomId) => request(`/room/info?roomId=${roomId}`),
    // 获取玩家信息
    getPlayerInfo: (roomId, playerId) =>
      request(`/roomPlayer/player?roomId=${roomId}&playerId=${playerId}`),
    // 重新开始游戏
    restart: (roomId, hostNo, data) =>
      request(`/room/restart?roomId=${roomId}&hostNo=${hostNo}`, "POST", data),
    // 离开房间
    leave: (roomId, playerId) =>
      request(`/room/leave?roomId=${roomId}&playerId=${playerId}`, "POST"),
    // 踢出玩家
    kick: (roomId, hostNo, playerId) =>
      request(
        `/room/kick?roomId=${roomId}&hostNo=${hostNo}&playerId=${playerId}`,
        "POST"
      ),
    // 获取我的游戏列表
    getMyGames: (playerId) =>
      request(`/roomPlayer/getJoinedRooms?playerId=${playerId}`),
  },
};
