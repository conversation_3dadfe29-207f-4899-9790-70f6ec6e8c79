<template>
    <view class="page">
        <common-header title="分享游戏"></common-header>
        <view class="content">
            <view class="info-box blue">
                <text class="info-title">游戏已准备就绪</text>
                <text class="info-text">请将二维码分享给其他玩家</text>
                <text class="info-hint">玩家扫描二维码后将直接获得身份和词语</text>
            </view>

            <view class="info-box yellow">
                <text class="info-section-title">游戏信息</text>
                <view class="info-list">
                    <view class="info-item">
                        <text>房间号：</text>
                        <text class="info-value">{{ roomCode }}</text>
                    </view>
                    <view class="info-item">
                        <text>总人数：</text>
                        <text class="info-value">{{ totalPlayers }}人</text>
                    </view>
                    <view class="info-item">
                        <text>平民：</text>
                        <text class="info-value">{{ civilianCount }}人</text>
                    </view>
                    <view class="info-item">
                        <text>卧底：</text>
                        <text class="info-value">{{ undercoverCount }}人</text>
                    </view>
                    <view class="info-item">
                        <text>白板：</text>
                        <text class="info-value">{{ whiteboardCount }}人</text>
                    </view>

                </view>
            </view>

            <view class="qrcode-container">
                <view class="qrcode-box">
                    <image class="qrcode" :src="qrCodeUrl" mode="aspectFit"></image>
                </view>
                <text class="qrcode-hint">扫描二维码加入游戏</text>
            </view>

            <view class="action-btns">
                <button open-type="share" class="share-btn">转发到微信</button>
                <button class="test-btn" @click="confirmAttend">查看我的身份</button>
            </view>
        </view>
    </view>
</template>

<script>
    import { api } from '../../config/api.js';
    import { wechatApi } from '@/utils/wolfApi.js'
    import CommonHeader from '@/components/CommonHeader.vue';

    export default {
        components: {
            CommonHeader
        },
        data() {
            return {
                roomCode: '',
                playerId: '',
                playerName: '',
                roomId: '',
                totalPlayers: 0,
                civilianCount: 0,
                undercoverCount: 0,
                whiteboardCount: 0,
                wordPair: '',
                customWord: '',
                qrCodeUrl: '/partyGame/static/qrcode-placeholder.svg',
                identity: '',
                word: ''
            }
        },
        computed: {
            playerCount() {
                return this.civilianCount + this.undercoverCount + this.whiteboardCount;
            }
        },
        onLoad(options) {
            if (options.roomId && options.playerId) {
                this.roomId = options.roomId;
                this.playerId = options.playerId;
                this.getRoomInfo();
            } else {
                uni.showToast({
                    title: '缺少必要参数',
                    icon: 'none'
                });
                setTimeout(() => {
                    uni.redirectTo({
                        url: '/partyGame/pages/index/index'
                    });
                }, 1500);
            }
        },

        onShareAppMessage() {
            return {
                title: '谁是卧底（房号' + this.roomCode + '）',
                path: '/partyGame/pages/identity/index?roomId=' + this.roomId,
                imageUrl: "/partyGame/static/images/undercover.jpg"
            }
        },
        methods: {
            async getRoomInfo() {
                try {
                    uni.showLoading({
                        title: '获取房间信息...'
                    });
                    const roomInfo = await api.room.getInfo(this.roomId);
                    console.log(roomInfo)
                    this.roomId = roomInfo.id;
                    this.roomCode = roomInfo.roomCode;
                    this.civilianCount = roomInfo.civilianCount || 0;
                    this.undercoverCount = roomInfo.undercoverCount || 0;
                    this.whiteboardCount = roomInfo.whiteboardCount || 0;
                    this.totalPlayers = this.civilianCount + this.undercoverCount + this.whiteboardCount;
                    this.wordPair = roomInfo.wordPair;
                    uni.hideLoading();

                    // 生成二维码

                    this.generateQRCode();

                } catch (error) {
                    uni.hideLoading();
                    uni.showToast({
                        title: error.message || '获取房间信息失败',
                        icon: 'none'
                    });
                }
            },


            async generateQRCode() {
                // 在实际项目中，可以使用服务器API生成二维码或使用前端库
                // 这里暂时使用占位图
                var params = {
                    scene: this.roomId,
                    //二维码场景编号
                    page: "partyGame/pages/identity/index",
                    //二维码对应的入口url
                    width: 860,
                };

                var url = getApp().globalData.serverUrl + "/api/wechat/getQRCodeBase64"

                let res = await wechatApi.getQRCodeBase64(params)

                this.qrCodeUrl = res.data.data
            },

            goBack() {
                uni.navigateBack();
            },

            // 确认参加游戏
            confirmAttend() {
                let that = this
                // 确认参加游戏
                uni.showModal({
                    title: '确认参加游戏',
                    content: '您是房主，确定要加入游戏吗？',
                    confirmText: '确定',
                    cancelText: '取消',
                    success: async (res) => {
                        if (res.confirm) {
                            that.navigateToIdentity()
                        }
                    }
                })
            },

            async navigateToIdentity() {
                uni.navigateTo({
                    url: `/partyGame/pages/identity/index?roomId=${this.roomId}`
                });
            }
        }
    }
</script>

<style scoped>
    .page {
        min-height: 100vh;
        background-color: white;
        display: flex;
        flex-direction: column;
    }

    .content {
        flex: 1;
        padding: 24rpx 30rpx;
        display: flex;
        flex-direction: column;
    }

    .info-box {
        border-radius: 16rpx;
        padding: 24rpx;
        margin-bottom: 30rpx;
        text-align: center;
    }

    .info-box.blue {
        background-color: #eff6ff;
        /* blue-50 */
    }

    .info-box.yellow {
        background-color: #fefce8;
        /* yellow-50 */
    }

    .info-title {
        font-size: 42rpx;
        font-weight: bold;
        color: #1e40af;
        /* blue-800 */
        margin-bottom: 12rpx;
        display: block;
    }

    .info-text {
        color: #4b5563;
        /* gray-600 */
        font-size: 32rpx;
        display: block;
    }

    .info-hint {
        color: #4b5563;
        /* gray-600 */
        font-size: 28rpx;
        margin-top: 12rpx;
        display: block;
    }

    .info-section-title {
        font-weight: bold;
        color: #854d0e;
        /* yellow-800 */
        font-size: 32rpx;
        margin-bottom: 12rpx;
        text-align: center;
        display: block;
    }

    .info-list {
        display: flex;
        flex-direction: column;
        gap: 12rpx;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        font-size: 28rpx;
        color: #92400e;
        /* yellow-700 */
    }

    .info-value {
        font-weight: 500;
    }

    .qrcode-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 40rpx;
    }

    .qrcode-box {
        width: 240rpx;
        height: 240rpx;
        background-color: white;
        border: 2rpx solid #e5e5e5;
        border-radius: 16rpx;
        padding: 10rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 15rpx;
    }

    .qrcode {
        width: 100%;
        height: 100%;
    }

    .qrcode-hint {
        font-size: 28rpx;
        color: #6b7280;
        /* gray-500 */
    }

    .action-btns {
        margin-top: auto;
        padding-top: 20rpx;
    }

    .share-btn {
        width: 100%;
        background-color: #16a34a;
        /* green-600 */
        color: white;
        font-weight: bold;
        padding: 20rpx 0;
        font-size: 36rpx;
        border-radius: 100rpx;
        margin-bottom: 15rpx;
        box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
    }

    .test-btn {
        width: 100%;
        background-color: #9333ea;
        /* purple-600 */
        color: white;
        font-weight: bold;
        padding: 20rpx 0;
        font-size: 36rpx;
        border-radius: 100rpx;
        margin-bottom: 15rpx;
        box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
    }
</style>