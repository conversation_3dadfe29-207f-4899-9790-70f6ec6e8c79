<template>
    <view class="page">
        <common-header title="游戏规则"></common-header>
        <view class="content">
            <view class="info-box">
                <text class="info-title">什么是谁是卧底？</text>
                <text class="info-text">
                    谁是卧底是一款语言类推理游戏，平民拿到同一个词语，卧底拿到与之相关但不同的词语。通过玩家轮流描述，相互推理找出卧底。
                </text>
            </view>

            <view class="rule-section">
                <text class="section-title">基本规则</text>
                <view class="rule-list">
                    <text class="rule-item">游戏开始前，确定玩家人数和卧底人数</text>
                    <text class="rule-item">每位玩家会分配到一个词语，大部分人（平民）拿到相同词语，卧底拿到不同但相关的词语</text>
                    <text class="rule-item">如有白板角色，他们不会看到任何词语</text>
                    <text class="rule-item">玩家轮流描述自己拿到的词语，不能直接说出词语本身</text>
                    <text class="rule-item">每轮描述后，所有玩家投票选出怀疑的卧底</text>
                    <text class="rule-item">票数最高的玩家出局，并公布身份</text>
                    <text class="rule-item">如果所有卧底都被找出，平民获胜；如果卧底人数大于或等于平民，卧底获胜</text>
                </view>
            </view>

            <view class="rule-section">
                <text class="section-title">词语示例</text>
                <view class="word-example">
                    <view class="word-row">
                        <text class="word-label">平民词语：</text>
                        <text class="word-civilian">牙刷</text>
                    </view>
                    <view class="word-row">
                        <text class="word-label">卧底词语：</text>
                        <text class="word-undercover">牙膏</text>
                    </view>
                </view>
            </view>

            <view class="rule-section">
                <text class="section-title">游戏技巧</text>
                <view class="rule-list">
                    <text class="rule-item">平民：描述要具体但不要太明显，帮助其他平民识别你，同时不要让卧底察觉</text>
                    <text class="rule-item">卧底：仔细听平民描述，尽量模仿平民的描述方式，避免被识别</text>
                    <text class="rule-item">记住其他玩家的描述，寻找不合理或可疑的地方</text>
                    <text class="rule-item">通过肢体语言和表情观察其他玩家的反应</text>
                </view>
            </view>

            <view class="btn-container">
                <button class="back-btn" @tap="goBack">返回游戏</button>
            </view>
        </view>
    </view>
</template>


<script>
    import CommonHeader from '@/components/CommonHeader.vue';

    export default {
        components: {
            CommonHeader
        },
        data() {
            return {}
        },
        methods: {
            goBack() {
                uni.navigateBack();
            }
        }
    }
</script>

<style scoped>
    .page {
        min-height: 100vh;
        background-color: white;
        display: flex;
        flex-direction: column;
    }

    .content {
        flex: 1;
        padding: 24rpx 30rpx;
        overflow-y: auto;
    }

    .info-box {
        background-color: #eff6ff;
        /* blue-50 */
        border-radius: 16rpx;
        padding: 24rpx;
        margin-bottom: 30rpx;
    }

    .info-title {
        font-size: 36rpx;
        font-weight: bold;
        color: #1e40af;
        /* blue-800 */
        margin-bottom: 12rpx;
        display: block;
    }

    .info-text {
        color: #4b5563;
        /* gray-600 */
        font-size: 28rpx;
        line-height: 1.5;
    }

    .rule-section {
        margin-bottom: 30rpx;
    }

    .section-title {
        font-size: 34rpx;
        font-weight: bold;
        color: #1f2937;
        /* gray-800 */
        margin-bottom: 16rpx;
        display: block;
    }

    .rule-list {
        display: flex;
        flex-direction: column;
        gap: 12rpx;
        padding-left: 30rpx;
    }

    .rule-item {
        font-size: 28rpx;
        color: #4b5563;
        /* gray-600 */
        line-height: 1.5;
        position: relative;
    }

    .rule-item::before {
        content: "•";
        position: absolute;
        left: -30rpx;
    }

    .word-example {
        background-color: #f9fafb;
        /* gray-50 */
        padding: 20rpx;
        border-radius: 12rpx;
    }

    .word-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10rpx;
    }

    .word-row:last-child {
        margin-bottom: 0;
    }

    .word-label {
        font-weight: 500;
        color: #4b5563;
        /* gray-600 */
        font-size: 28rpx;
    }

    .word-civilian {
        color: #2563eb;
        /* blue-600 */
        font-weight: 500;
        font-size: 28rpx;
    }

    .word-undercover {
        color: #dc2626;
        /* red-600 */
        font-weight: 500;
        font-size: 28rpx;
    }

    .btn-container {
        margin-top: auto;
        padding-top: 30rpx;
    }

    .back-btn {
        width: 100%;
        background-color: #9333ea;
        color: white;
        font-weight: bold;
        padding: 24rpx 0;
        font-size: 36rpx;
        border-radius: 100rpx;
        box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
    }
</style>