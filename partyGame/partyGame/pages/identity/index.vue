<template>
	<view class="page">
		<common-header title="你的身份"></common-header>

		<view class="content">
			<view class="info-box">
				<text class="info-title">游戏已开始</text>
				<text class="info-text">点击卡片查看你的身份，不要让他人看到</text>
				<text class="room-code">房间号：{{ roomCode }}</text>
			</view>

			<view class="card-container">
				<view class="identity-card" :class="{ 'flipped': isFlipped }" @tap="toggleCard">
					<view class="card-inner">
						<view class="flip-card-front">
							<view class="icon-wrapper">
								<!-- <text class="fas fa-question-circle"></text> -->
								<text v-if="index > 0" style="font-size: 76rpx; color: white;">您是第{{ index }}号</text>
							</view>
							<text class="card-title">点击查看身份</text>
							<text class="card-subtitle">仅限本人查看</text>
						</view>

						<view class="flip-card-back">
							<view class="role-tag" :class="roleClass">{{ role }}</view>
							<text class="word">{{ word }}</text>
							<text class="hint-text">请根据这个词语进行描述，但不要直接说出这个词</text>
							<text class="card-help">点击卡片隐藏信息</text>
						</view>
					</view>
				</view>
			</view>

			<view class="tips-container">
				<view class="tips-box">
					<text class="tips-title">游戏提示</text>
					<view class="tips-list">
						<text class="tip-item">• 根据你的词语描述，但不要直接说出词语</text>
						<text class="tip-item">• 仔细听其他玩家的描述，找出可疑的人</text>
						<text class="tip-item">• 你可以随时点击卡片查看自己的词语</text>
						<text class="tip-item">• 游戏结束前不要透露自己的身份</text>
					</view>
				</view>

				<button class="rules-btn btn-color" @tap="navToRules">查看游戏规则</button>
				<button class="rules-btn share-color" open-type="share">微信分享</button>
			</view>
		</view>
	</view>
</template>

<script>
	import { api } from '../../config/api.js';
	import CommonHeader from '@/components/CommonHeader.vue';

	export default {
		components: {
			CommonHeader
		},
		data() {
			return {
				roomCode: '',
				roomId: '',
				index: 0,

				playerId: '',
				isFlipped: false,
				identity: '',
				role: '', // 可能值: '平民', '卧底', '白板'
				word: '',
				roleClass: '', // 可能值: 'civilian', 'undercover', 'blank'

				errorMessage: ''
			}
		},
		onLoad(options) {
			// 检查参数
			if (options.roomId) {
				this.roomId = options.roomId;
				// 如果有玩家ID，获取玩家信息
				this.playerId = getApp().globalData.playerId
				if (!this.playerId) {
					getApp().login().then(() => {
						this.playerId = getApp().globalData.playerId
						this.joinRoom();
					})
				} else {
					this.joinRoom();
				}
			} else {
				this.errorMessage = '缺少房间号，无法获取身份信息';
				uni.showToast({
					title: this.errorMessage,
					icon: 'none'
				});

				// 5秒后返回首页
				setTimeout(() => {
					uni.redirectTo({
						url: '/partyGame/pages/index/index'
					});
				}, 5000);
			}
		},
		onShareAppMessage() {
			return {
				title: '谁是卧底(房号' + this.roomCode + ')',
				path: '/partyGame/pages/identity/index?roomId=' + this.roomId,
				imageUrl: "/partyGame/static/images/undercover.jpg"
			}
		},
		methods: {


			// 加入房间
			async joinRoom() {
				try {

					uni.showLoading({
						title: '加入游戏中...'
					});

					const joinData = {
						roomId: this.roomId,
						playerId: this.playerId,
					};

					const playerInfo = await api.room.join(joinData).catch(error => {
						this.roomfull()
					});

					uni.hideLoading();

					if (playerInfo) {
						console.log("playerInfo", playerInfo)
						this.roomCode = playerInfo.roomCode;
						this.index = playerInfo.index;
						this.setRoleAndWord(playerInfo.identity, playerInfo.word);
					} else {
						this.roomfull()
					}

				} catch (error) {
					uni.hideLoading();

					this.errorMessage = error.message || '加入房间失败';
					uni.showToast({
						title: this.errorMessage,
						icon: 'none'
					});
				}
			},

			roomfull: function () {
				this.errorMessage = '房间已满';
				uni.showModal({
					title: '提示',
					content: this.errorMessage,
					showCancel: false,
					success: () => {
						uni.redirectTo({
							url: '/partyGame/pages/index/index'
						});
					}
				});
			},

			// 设置角色和词语
			setRoleAndWord(identity, word) {
				// 根据身份设置角色和CSS类
				if (identity === 'civilian') {
					this.role = '平民';
					this.roleClass = 'civilian';
				} else if (identity === 'undercover') {
					this.role = '卧底';
					this.roleClass = 'undercover';
				} else if (identity === 'whiteboard') {
					this.role = '白板';
					this.roleClass = 'blank';
					this.word = ''; // 白板没有词语
				} else {
					this.role = '未知';
					this.roleClass = '';
				}

				// 如果不是白板，设置词语
				if (identity !== 'whiteboard' && word) {
					this.word = word;
				}
			},

			toggleCard() {
				// 只有成功获取了身份信息才能翻转卡片
				if (!this.errorMessage) {
					this.isFlipped = !this.isFlipped;
				}
			},

			navToRules() {
				uni.navigateTo({
					url: '/partyGame/pages/rules/index'
				});
			}
		}
	}
</script>

<style>
	.page {
		min-height: 100vh;
		background-color: white;
		display: flex;
		flex-direction: column;
	}

	.content {
		flex: 1;
		padding: 24rpx 30rpx;
		display: flex;
		flex-direction: column;
	}

	.info-box {
		background-color: #eff6ff;
		/* blue-50 */
		border-radius: 16rpx;
		padding: 24rpx;
		margin-bottom: 24rpx;
		text-align: center;
	}

	.info-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #1e40af;
		/* blue-800 */
		margin-bottom: 8rpx;
		display: block;
	}

	.info-text {
		color: #4b5563;
		/* gray-600 */
		font-size: 28rpx;
		display: block;
	}

	.room-code {
		margin-top: 12rpx;
		font-weight: 500;
		color: #7e22ce;
		/* purple-700 */
		font-size: 30rpx;
		display: block;
	}

	.card-container {
		display: flex;
		justify-content: center;
		align-items: center;
		margin: 24rpx 0;
	}

	.identity-card {
		width: 100%;
		max-width: 600rpx;
		height: 400rpx;
		perspective: 1000rpx;
		margin: 0 auto;
	}

	.card-inner {
		position: relative;
		width: 100%;
		height: 100%;
		transition: transform 0.8s;
		transform-style: preserve-3d;
		transform-origin: center center;
	}

	.identity-card.flipped .card-inner {
		transform: rotateY(180deg);
	}

	.flip-card-front,
	.flip-card-back {
		position: absolute;
		width: 100%;
		height: 100%;
		-webkit-backface-visibility: hidden;
		backface-visibility: hidden;
		border-radius: 20rpx;
		padding: 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
		left: 0;
		box-sizing: border-box;
	}

	.flip-card-front {
		background: linear-gradient(to bottom right, #9333ea, #4f46e5);
	}

	.flip-card-back {
		background-color: white;
		border: 4rpx solid #9333ea;
		transform: rotateY(180deg);
	}

	.icon-wrapper {
		margin-bottom: 30rpx;
	}

	.fas.fa-question-circle {
		font-size: 96rpx;
		color: white;
	}

	.card-title {
		font-size: 42rpx;
		font-weight: bold;
		color: white;
		margin-bottom: 12rpx;
	}

	.card-subtitle {
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
	}

	.role-tag {
		padding: 8rpx 20rpx;
		border-radius: 100rpx;
		font-weight: 500;
		font-size: 30rpx;
		margin-bottom: 30rpx;
	}

	.role-tag.civilian {
		background-color: #e0f2fe;
		/* light blue-100 */
		color: #0369a1;
		/* blue-700 */
	}

	.role-tag.undercover {
		background-color: #fee2e2;
		/* red-100 */
		color: #b91c1c;
		/* red-700 */
	}

	.role-tag.blank {
		background-color: #f3f4f6;
		/* gray-100 */
		color: #4b5563;
		/* gray-600 */
	}

	.word {
		font-size: 60rpx;
		font-weight: bold;
		color: #1f2937;
		/* gray-800 */
		margin-bottom: 30rpx;
	}

	.hint-text {
		color: #6b7280;
		/* gray-500 */
		text-align: center;
		margin-bottom: 40rpx;
		font-size: 28rpx;
	}

	.card-help {
		font-size: 24rpx;
		color: #9ca3af;
		/* gray-400 */
	}

	.tips-container {
		padding-top: 60rpx;
	}

	.tips-box {
		background-color: #fefce8;
		/* yellow-50 */
		border-radius: 16rpx;
		padding: 24rpx;
		margin-bottom: 30rpx;
	}

	.tips-title {
		font-weight: bold;
		color: #854d0e;
		/* yellow-800 */
		font-size: 32rpx;
		margin-bottom: 12rpx;
		text-align: center;
		display: block;
	}

	.tips-list {
		display: flex;
		flex-direction: column;
		gap: 12rpx;
	}

	.tip-item {
		font-size: 28rpx;
		color: #92400e;
		/* yellow-700 */
		line-height: 1.5;
	}

	.rules-btn {
		width: 100%;

		color: white;
		font-weight: bold;
		padding: 24rpx 0;
		font-size: 36rpx;
		border-radius: 100rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.btn-color {
		background-color: #9333ea;
	}

	.share-color {
		background-color: #16a34a;
	}
</style>