<template>
    <view class="page">
        <view class="form-container">
            <common-header title="游戏设置"></common-header>

            <view class="content">
                <view class="setting-group">
                    <text class="setting-label">平民人数</text>
                    <view class="counter">
                        <view class="counter-btn" @tap="decreaseCivilianCount">-</view>
                        <input class="counter-input" type="number" v-model="civilianCount" disabled />
                        <view class="counter-btn" @tap="increaseCivilianCount">+</view>
                    </view>
                </view>

                <view class="setting-group">
                    <text class="setting-label">卧底人数</text>
                    <view class="counter">
                        <view class="counter-btn" @tap="decreaseUndercoverCount">-</view>
                        <input class="counter-input" type="number" v-model="undercoverCount" disabled />
                        <view class="counter-btn" @tap="increaseUndercoverCount">+</view>
                    </view>
                </view>

                <view class="setting-group">
                    <text class="setting-label">白板人数</text>
                    <view class="counter">
                        <view class="counter-btn" @tap="decreaseBlankCount">-</view>
                        <input class="counter-input" type="number" v-model="blankCount" disabled />
                        <view class="counter-btn" @tap="increaseBlankCount">+</view>
                    </view>
                </view>

                <view class="setting-group">
                    <text class="setting-label">词库选择</text>
                    <view class="word-library-grid">
                        <view v-for="(library, index) in wordLibraries" :key="index"
                            :class="['word-library-item', { active: selectedLibrary === index }] "
                            @tap="selectLibrary(index)">
                            <text>{{ library }}</text>
                        </view>
                    </view>
                </view>

                <view class="setting-group">
                    <text class="setting-label">{{wordLibraries[selectedLibrary] !== '自定义词库' ? '词语选择' : '指定词语'}}</text>
                    <view class="word-select-box">
                        <view v-if="wordLibraries[selectedLibrary] !== '自定义词库'" class="switch-row">
                            <view class="switch">
                                <switch :checked="randomWord" @change="onToggleRandomWord" color="#9333ea"
                                    style="transform:scale(0.8);" :disabled="isCustomLibrarySelected" />
                            </view>
                            <text class="switch-label">随机词语</text>
                        </view>

                        <view class="divider" v-if="!randomWord&&wordLibraries[selectedLibrary] !== '自定义词库'"></view>

                        <view class="specify-word-area" v-if="!randomWord">
                            <text v-if="wordLibraries[selectedLibrary] !== '自定义词库'" class="specify-label">指定词语</text>

                            <view v-if="wordLibraries[selectedLibrary] !== '自定义词库'" class="word-suggestions">
                                <view class="suggestions-header">
                                    <text class="small-label">备选词语：</text>
                                    <button class="refresh-btn" size="mini" @click="refreshSuggestedWords">刷新</button>
                                </view>
                                <view class="suggestions-grid">
                                    <view class="suggestion-item" v-for="(pair, index) in wordSuggestions" :key="index"
                                        @tap="selectWordPair(pair)">
                                        {{ pair.civilian }} - {{ pair.undercover }}
                                    </view>
                                </view>
                            </view>

                            <view class="word-input-grid">
                                <view class="word-input-wrap">
                                    <text class="small-label">平民词</text>
                                    <input class="word-input" v-model="civilianWord" placeholder="输入平民词语" />
                                </view>
                                <view class="word-input-wrap">
                                    <text class="small-label">卧底词</text>
                                    <input class="word-input" v-model="undercoverWord" placeholder="输入卧底词语" />
                                </view>
                            </view>
                            <text class="word-hint">*启用随机词语时，指定词语将不生效</text>
                        </view>
                    </view>
                </view>

                <view class="btn-container">
                    <button class="start-btn" @tap="startGame">开始游戏</button>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    import { api } from '../../config/api.js';
    import CommonHeader from '@/components/CommonHeader.vue';

    // Mock word pairs for different libraries
    const mockWordPairs = {
        '经典词库': [
            { civilian: '香蕉', undercover: '芒果' },
            { civilian: '太阳', undercover: '月亮' },
            { civilian: '米饭', undercover: '面条' },
            { civilian: '肥皂', undercover: '香皂' }
        ],
        '情侣专题': [
            { civilian: '玫瑰', undercover: '月季' },
            { civilian: '电影院', undercover: '咖啡厅' },
            { civilian: '戒指', undercover: '项链' },
            { civilian: '拥抱', undercover: '牵手' }
        ],
        '动漫专题': [
            { civilian: '路飞', undercover: '鸣人' },
            { civilian: '柯南', undercover: '基德' },
            { civilian: '皮卡丘', undercover: '数码宝贝' },
            { civilian: '高达', undercover: '变形金刚' }
        ],
        '电影专题': [
            { civilian: '泰坦尼克号', undercover: '阿凡达' },
            { civilian: '钢铁侠', undercover: '蜘蛛侠' },
            { civilian: '哈利波特', undercover: '指环王' },
            { civilian: '盗梦空间', undercover: '星际穿越' }
        ],
        // Custom library might have different logic or no suggestions
        '自定义词库': []
    };

    export default {
        components: {
            CommonHeader
        },
        data() {
            return {
                civilianCount: 3,
                undercoverCount: 1,
                blankCount: 1,
                randomWord: true,
                wordLibraries: ['经典词库', '情侣专题', '动漫专题', '电影专题', '自定义词库'],
                selectedLibrary: 0,
                civilianWord: '',
                undercoverWord: '',
                wordSuggestions: mockWordPairs['经典词库'] // Initial suggestions
            };
        },
        computed: {
            isCustomLibrarySelected() {
                return this.wordLibraries[this.selectedLibrary] === '自定义词库';
            },
			playerCount(){
				return this.civilianCount+this.undercoverCount+this.blankCount
			}
        },
        watch: {
            selectedLibrary(newIndex) {
                if (this.wordLibraries[newIndex] === '自定义词库') {
                    this.randomWord = false;
                }
                if (!this.randomWord) {
                    this.refreshSuggestedWords();
                }
            }
        },
        methods: {
            goBack() {
                uni.navigateBack();
            },
            increaseCivilianCount() {
                if (this.civilianCount < 12) {
                    this.civilianCount++;
                }
            },
            decreaseCivilianCount() {
                if (this.civilianCount > 3) {
                    this.civilianCount--;
                }
            },
            increaseUndercoverCount() {
                if (this.undercoverCount < Math.floor(this.playerCount / 2) && this.undercoverCount + this.blankCount < this.playerCount - 1) {
                    this.undercoverCount++;
                }
            },
            decreaseUndercoverCount() {
                if (this.undercoverCount > 0) {
                    this.undercoverCount--;
                }
            },
            increaseBlankCount() {
                if (this.blankCount < 2 && this.undercoverCount + this.blankCount < this.playerCount - 1) {
                    this.blankCount++;
                }
            },
            decreaseBlankCount() {
                if (this.blankCount > 0) {
                    this.blankCount--;
                }
            },
            selectLibrary(index) {
                this.selectedLibrary = index;
            },
            onToggleRandomWord(e) {
                if (this.isCustomLibrarySelected) {
                    this.randomWord = false;
                    uni.showToast({ title: '自定义词库只能指定词语', icon: 'none' });
                    return;
                }
                this.randomWord = e.detail.value;
                if (!this.randomWord) {
                    this.refreshSuggestedWords();
                }
            },
            refreshSuggestedWords() {
                const currentLibrary = this.wordLibraries[this.selectedLibrary];
                if (mockWordPairs[currentLibrary]) {
                    this.wordSuggestions = [...mockWordPairs[currentLibrary]].sort(() => 0.5 - Math.random());
                } else {
                    this.wordSuggestions = [];
                }
                console.log(`Refreshing suggestions for: ${currentLibrary}`);
            },
            selectWordPair(pair) {
                this.civilianWord = pair.civilian;
                this.undercoverWord = pair.undercover;
            },
            async startGame() {
				
				let playerId = getApp().globalData.playerId
				
				if(!playerId){
					await getApp().login()
				}
				
                try {
                    uni.showLoading({
                        title: '创建游戏中...'
                    });

                    // 创建游戏参数
                    const gameParams = {
                        creatorId: getApp().globalData.playerId,
                        civilianCount: this.civilianCount,
                        undercoverCount: this.undercoverCount,
                        whiteboardCount: this.blankCount,
                        libraryName: this.wordLibraries[this.selectedLibrary],
                        wordRandom: this.randomWord,
                    };

                    // 如果不是随机词语，添加自定义词语
                    if (!this.randomWord && this.civilianWord && this.undercoverWord) {
                        gameParams.wordPair = `${this.civilianWord},${this.undercoverWord}`;
                    }

                    // 调用创建房间API
                    const roomInfo = await api.room.create(gameParams);
					
					console.log(roomInfo)

                    // 保存玩家信息到本地存储
                    uni.setStorageSync('playerInfo', {
                        playerId: gameParams.playerId,
                        playerName: gameParams.playerName
                    });

                    // 导航到分享页面
                    uni.navigateTo({
                        url: `/partyGame/pages/share/index?roomId=${roomInfo.id}&playerId=${gameParams.playerId}`
                    });
                } catch (error) {
                    uni.hideLoading();
                    uni.showToast({
                        title: error.message || '创建游戏失败',
                        icon: 'none'
                    });
                }
            }
        }
    };
</script>

<style scoped>
    .page {
        min-height: 100vh;
        background-color: white;
        display: flex;
        flex-direction: column;
    }

    .content {
        flex: 1;
        padding: 24rpx 30rpx;
        overflow-y: auto;
    }

    .setting-group {
        margin-bottom: 40rpx;
    }

    .setting-label {
        display: block;
        font-weight: bold;
        color: #333;
        font-size: 32rpx;
        margin-bottom: 16rpx;
    }

    .counter {
        display: flex;
        align-items: center;
    }

    .counter-btn {
        background-color: #f0f0f0;
        padding: 16rpx 24rpx;
        font-size: 42rpx;
        font-weight: bold;
        color: #333;
    }

    .counter-btn:first-child {
        border-top-left-radius: 16rpx;
        border-bottom-left-radius: 16rpx;
    }

    .counter-btn:last-child {
        border-top-right-radius: 16rpx;
        border-bottom-right-radius: 16rpx;
    }

    .counter-input {
        flex: 1;
        height: 80rpx;
        background-color: #f7f7f7;
        text-align: center;
        color: #333;
    }

    .word-library-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20rpx;
    }

    .word-library-item {
        background-color: white;
        border: 4rpx solid #e5e5e5;
        border-radius: 16rpx;
        padding: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 30rpx;
        color: #555;
        font-weight: 500;
    }

    .word-library-item.active {
        background-color: #f3e8ff;
        border-color: #9333ea;
        color: #9333ea;
    }

    .word-select-box {
        background-color: white;
        border: 2rpx solid #e5e5e5;
        border-radius: 16rpx;
        padding: 24rpx;
    }

    .switch-row {
        display: flex;
        align-items: center;
        margin-bottom: 24rpx;
    }

    .switch-label {
        color: #333;
        font-size: 30rpx;
        margin-left: 16rpx;
    }

    .divider {
        height: 2rpx;
        background-color: #e5e5e5;
        margin: 24rpx 0;
    }

    .specify-word-area {
        padding-top: 24rpx;
    }

    .specify-label {
        font-weight: 500;
        color: #333;
        font-size: 30rpx;
        margin-bottom: 16rpx;
        display: block;
    }

    .small-label {
        color: #666;
        font-size: 28rpx;
        margin-bottom: 12rpx;
        display: block;
    }

    .word-suggestions {
        margin-top: 20rpx;
        padding: 20rpx;
        background-color: #f9f9f9;
        border-radius: 8rpx;
    }

    .suggestions-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10rpx;
    }

    .refresh-btn {
        background-color: #9333ea;
        color: white;
        border: none;
        padding: 4rpx 16rpx;
        font-size: 24rpx;
        line-height: 1.5;
        height: auto;
        min-height: unset;
    }

    .suggestions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200rpx, 1fr));
        gap: 10rpx;
        margin-top: 10rpx;
    }

    .suggestion-item {
        background-color: white;
        padding: 10rpx 15rpx;
        border-radius: 8rpx;
        text-align: center;
        font-size: 26rpx;
        color: #555;
        cursor: pointer;
        border: 1px solid #eee;
    }

    .suggestion-item:hover {
        background-color: #e9d5ff;
    }

    .word-input-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20rpx;
        margin-bottom: 12rpx;
    }

    .word-input {
        border: 2rpx solid #e0e0e0;
        border-radius: 12rpx;
        padding: 12rpx 16rpx;
        font-size: 28rpx;
        color: #333;
    }

    .word-hint {
        font-size: 24rpx;
        color: #888;
    }

    .btn-container {
        margin-top: 40rpx;
    }

    .start-btn {
        width: 100%;
        background-color: #9333ea;
        color: white;
        font-weight: bold;
        padding: 24rpx 0;
        font-size: 36rpx;
        border-radius: 100rpx;
        box-shadow: 0 8rpx 16rpx rgba(147, 51, 234, 0.2);
    }

    .switch {
        display: inline-block;
        vertical-align: middle;
        position: relative;
    }

    .switch-row .switch uni-switch[disabled]+.wx-switch-input {
        background-color: #cccccc !important;
        border-color: #bbbbbb !important;
    }

    .switch-row .switch uni-switch[disabled] .wx-switch-input::before {
        background-color: #eeeeee !important;
    }
</style>