<template>
    <view class="page">
        <view class="bg-gradient">
            <view class="content">
                <image
                    src="https://images.unsplash.com/photo-1522075469751-3a6694fb2f61?w=600&auto=format&fit=crop&q=60"
                    class="avatar" mode="aspectFill"></image>
                <text class="title">谁是卧底</text>
                <text class="subtitle">聚会必备，面对面猜词游戏</text>

                <button class="primary-btn" @tap="navToSetup">来一局</button>
                <button class="secondary-btn" @tap="navToRules">游戏规则</button>
                <button class="secondary-btn" @click="navigateToMyGames">我的游戏</button>
				 <button class="secondary-btn" @click="navigateToIndex">返回首页</button>
            </view>
        </view>
    </view>
</template>

<script>
    export default {
        data() {
            return {}
        },
        methods: {
            navToSetup() {
                uni.navigateTo({
                    url: '/partyGame/pages/setup/index'
                });
            },
            navToRules() {
                uni.navigateTo({
                    url: '/partyGame/pages/rules/index'
                });
            },
            navigateToMyGames() {
                uni.navigateTo({
                    url: '/partyGame/pages/myGames/index'
                });
            },
			navigateToIndex() {
			    uni.reLaunch({
			        url: '/pages/index/index'
			    });
			}
        }
    }
</script>

<style>
    .page {
        width: 100%;
        background-color: #f7f7f7;
    }

    .bg-gradient {
        background: linear-gradient(to bottom, #3b82f6, #8b5cf6);
        min-height: 100vh;
        display: flex;
        flex-direction: column;
    }

    .content {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40rpx;
    }

    .avatar {
        width: 160rpx;
        height: 160rpx;
        border-radius: 80rpx;
        margin-bottom: 40rpx;
        border: 8rpx solid #FFFFFF;
    }

    .title {
        font-size: 80rpx;
        font-weight: bold;
        color: #FFFFFF;
        margin-bottom: 20rpx;
    }

    .subtitle {
        font-size: 36rpx;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 120rpx;
        text-align: center;
    }

    .primary-btn {
        width: 100%;
        background-color: #FFFFFF;
        color: #8b5cf6;
        font-weight: bold;
        font-size: 36rpx;
        padding: 24rpx 0;
        border-radius: 100rpx;
        margin-bottom: 30rpx;
        box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
    }

    .secondary-btn {
        width: 100%;
        background-color: rgba(255, 255, 255, 0.25);
        color: #FFFFFF;
        font-weight: bold;
        font-size: 36rpx;
        padding: 24rpx 0;
        border-radius: 100rpx;
        margin-bottom: 30rpx;
    }
</style>