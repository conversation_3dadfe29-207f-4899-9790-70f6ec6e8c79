<template>
    <view class="page">
        <common-header title="我的游戏"></common-header>

        <view class="content">
            <view class="game-list">
                <view class="game-item" v-for="(game, index) in gameList" :key="index" @click="navigateToGame(game)"
                    :class="{'owner': game.isCreator, 'player': !game.isCreator}">
                    <view class="game-info">
                        <text class="game-name">游戏 #{{game.roomCode}}</text>
                        <text class="game-date">{{formatDate(game.joinTime)}}</text>
                    </view>
                    <view class="game-role">
                        <text class="role-text">{{game.isCreator ? '房主' : '玩家'}}</text>
                    </view>
                    <text class="arrow">></text>
                </view>
            </view>

            <view class="empty-tip" v-if="gameList.length === 0">
                <text class="empty-text">暂无游戏记录</text>
                <button class="create-btn" @click="navigateToCreate">创建新游戏</button>
            </view>
        </view>
    </view>
</template>

<script>
    import { api } from '../../config/api.js';
    import CommonHeader from '@/components/CommonHeader.vue';

    export default {
        components: {
            CommonHeader
        },
        data() {
            return {
                gameList: [],
                playerId: 0
            }
        },
        // 在页面的onLoad方法中
        onLoad() {
            const app = getApp()
            this.statusBarHeight = app.globalData.statusBarHeight
            this.navBarHeight = app.globalData.navBarHeight
            this.playerId = app.globalData.playerId
            console.log('playerId:', this.playerId)

            this.loadMyGames();
        },
        methods: {


            async loadMyGames() {
                try {
                    uni.showLoading({
                        title: '加载游戏记录...'
                    });

                    const games = await api.room.getMyGames(this.playerId);
                    this.gameList = games.map(game => ({
                        id: game.id,
                        roomCode: game.roomCode,
                        roomId: game.roomId,
                        joinTime: game.joinTime,
                        isCreator: game.isCreator
                    }));
					console.log(this.gameList)

                   
                } catch (error) {
                    console.error('获取游戏历史记录失败', error);
                    this.showMockData();
                } finally {
                    uni.hideLoading();
                }
            },

           
            formatDate(date) {
                if (!date) return '';

                // 如果date是字符串，尝试转换为日期对象
                if (typeof date === 'string') {
                    date = new Date(date);
                }

                // 如果是timestamp，转换为Date
                if (typeof date === 'number') {
                    date = new Date(date);
                }

                return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
            },
            navigateToGame(game) {
                if (game.isCreator) {
                    // 房主跳转到分享游戏页面
                    uni.navigateTo({
                        url: `/partyGame/pages/share/index?roomId=${game.roomId}&playerId=${this.playerId}`
                    });
                } else {
                    // 普通玩家跳转到身份页面
                    uni.navigateTo({
                        url: `/partyGame/pages/identity/index?roomId=${game.roomId}&playerId=${this.playerId}`
                    });
                }
            },
            navigateToCreate() {
                uni.navigateTo({
                    url: '/partyGame/pages/setup/index'
                });
            },
            goBack() {
                uni.navigateBack();
            }
        }
    }
</script>

<style lang="scss" scoped>
    .page {
        min-height: 100vh;
        background-color: white;
        display: flex;
        flex-direction: column;
    }

    /* 添加状态栏高度，适配手机摄像头区域 */
    .status-bar {
        width: 100%;
        background-color: #9333ea;
    }

    .header {
        background-color: #9333ea;
        padding: 0 24rpx;
        display: flex;
        align-items: center;
        height: auto;
        position: relative;
        height: 100rpx;
    }

    .header-left {
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
    }


    /* 调整标题容器，确保标题居中 */
    .header-title-container {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .header-title {
        color: white;
        font-size: 42rpx;
        font-weight: bold;
        text-align: center;
    }

    .header-right {
        width: 48rpx;
    }

    .fas.fa-chevron-left {
        color: white;
        font-size: 48rpx;
    }

    .content {
        flex: 1;
        padding: 24rpx 30rpx;
        display: flex;
        flex-direction: column;
    }

    .game-list {
        display: flex;
        flex-direction: column;
        gap: 20rpx;
    }

    .game-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 30rpx;
        border-radius: 16rpx;
        box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
    }

    .game-item.owner {
        background-color: #eff6ff;
        /* 蓝色背景表示房主 */
    }

    .game-item.player {
        background-color: #fefce8;
        /* 黄色背景表示玩家 */
    }

    .game-info {
        flex: 1;
    }

    .game-name {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 10rpx;
        display: block;
    }

    .game-date {
        font-size: 24rpx;
        color: #666;
    }

    .game-role {
        margin-right: 20rpx;
    }

    .role-text {
        font-size: 28rpx;
        font-weight: 500;
        color: #4b5563;
    }

    .arrow {
        color: #9333ea;
        font-size: 32rpx;
        font-weight: bold;
    }

    .empty-tip {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 100rpx 0;
    }

    .empty-text {
        color: #6b7280;
        font-size: 32rpx;
        margin-bottom: 30rpx;
    }

    .create-btn {
        background-color: #9333ea;
        color: white;
        font-weight: bold;
        padding: 20rpx 60rpx;
        font-size: 36rpx;
        border-radius: 100rpx;
        box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
    }
</style>