import uni from "@dcloudio/vite-plugin-uni";
import { defineConfig } from "vite";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [uni()],
  server: {
    port: 5173,
    host: "0.0.0.0",
    open: true,
  },
  build: {
    // 确保正确处理路由
    assetsInlineLimit: 0,
    chunkSizeWarningLimit: 2000,
    // 生成sourcemap用于调试
    sourcemap: process.env.NODE_ENV === "development",
  },
});
