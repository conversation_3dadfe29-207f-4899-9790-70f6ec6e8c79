package com.taurus.partygames.exception;

import lombok.Getter;

@Getter
public class GameException extends RuntimeException {
    private final int code;
    private final String message;

    public GameException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public static GameException roomNotFound() {
        return new GameException(1001, "房间不存在");
    }

    public static GameException roomFull() {
        return new GameException(1002, "房间已满");
    }

    public static GameException playerNotFound() {
        return new GameException(1003, "玩家不存在");
    }

    public static GameException notHost() {
        return new GameException(1004, "不是房主");
    }

    public static GameException gameAlreadyStarted() {
        return new GameException(1005, "游戏已开始");
    }

    public static GameException gameNotStarted() {
        return new GameException(1006, "游戏未开始");
    }

    public static GameException gameAlreadyEnded() {
        return new GameException(1007, "游戏已结束");
    }

    public static GameException identityAlreadyClaimed() {
        return new GameException(1008, "身份已领取");
    }

    public static GameException invalidOperation() {
        return new GameException(1009, "无效操作");
    }

    public static GameException wordLibraryNotFound() {
        return new GameException(1010, "词语库不存在");
    }
}