package com.taurus.partygames.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import lombok.Data;

/**
 * 词对实体类
 */
@Data
@Entity
@Table(name = "word_pair")
public class WordPair {

    /**
     * 词对ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 词库名称
     */
    @Column(name = "library_name")
    private String libraryName;

    /**
     * 平民词语
     */
    @Column(name = "civilian_word")
    private String civilianWord;

    /**
     * 卧底词语
     */
    @Column(name = "undercover_word")
    private String undercoverWord;

    /**
     * 创建时间
     */
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "update_time")
    private Date updateTime;
}