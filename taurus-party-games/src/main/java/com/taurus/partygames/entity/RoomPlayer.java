package com.taurus.partygames.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

/**
 * 房间玩家实体类
 */
@Data
@Entity
@Table(name = "room_player")
public class RoomPlayer {

    /**
     * ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 房间ID
     */

    @Column(name = "room_id") // 需要删除这个显式列映射
    private Long roomId;

    /**
     * 玩家ID
     */
    @Column(name = "player_id")
    private String playerId;

    /**
     * 玩家在房间中的位置
     */
    @Column(name = "position")
    private Integer index;

    /**
     * 是否是房主
     */
    @Column(name = "is_creator")
    private Boolean isCreator;

    /**
     * civilian(平民), UNDERCOVER(卧底)
     */
    @Column(name = "identity")
    private String identity;

    /**
     * 玩家词语
     */
    @Column(name = "word")
    private String word;

    /**
     * 加入时间
     */
    @Column(name = "join_time")
    private Long joinTime;

    /**
     * 最后活动时间
     */
    @Column(name = "last_active_time")
    private Long lastActiveTime;
}