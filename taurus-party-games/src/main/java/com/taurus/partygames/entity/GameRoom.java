/**
 * Entity (实体类)
 * 作用：与数据库表结构一一对应，是数据持久化的基本单位
 * 特点：包含JPA注解(@Entity, @Table等)，用于ORM映射
 * 生命周期：整个应用程序生命周期，主要在持久层使用
 * 示例：WordPair, Room, RoomPlayer类
 */
package com.taurus.partygames.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import lombok.Data;

/**
 * 游戏房间实体
 */
@Data
@Entity
@Table(name = "game_room")
public class GameRoom implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 房间ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 房间编码
     */
    @Column(name = "room_code", unique = true)
    private String roomCode;

    /**
     * 房主ID
     */
    @Column(name = "creator_id")
    private String creatorId;

    /**
     * 平民数量
     */
    @Column(name = "civilian_count")
    private Integer civilianCount;

    /**
     * 卧底数量
     */
    @Column(name = "undercover_count")
    private Integer undercoverCount;

    /**
     * 白板数量
     */
    @Column(name = "whiteboard_count")
    private Integer whiteboardCount;

    /**
     * 剩余卧底数量
     */
    @Column(name = "remaining_undercover_count")
    private Integer remainingUndercoverCount;

    /**
     * 剩余平民数量
     */
    @Column(name = "remaining_civilian_count")
    private Integer remainingCivilianCount;

    /**
     * 剩余白板数量
     */
    @Column(name = "remaining_whiteboard_count")
    private Integer remainingWhiteboardCount;

    /**
     * 词语对ID
     */
    @Column(name = "word_pair")
    private String wordPair;

    /**
     * 创建时间
     */
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "update_time")
    private Date updateTime;
}