package com.taurus.partygames.utils;

/**
 * 系统常量
 */
public class Constants {

    // 房间状态
    public static final String ROOM_STATUS_WAITING = "waiting"; // 等待中
    public static final String ROOM_STATUS_PLAYING = "playing"; // 游戏中
    public static final String ROOM_STATUS_FINISHED = "finished"; // 已结束

    // 轮次状态
    public static final String ROUND_STATUS_DESCRIBING = "describing"; // 描述阶段
    public static final String ROUND_STATUS_VOTING = "voting"; // 投票阶段
    public static final String ROUND_STATUS_FINISHED = "finished"; // 已结束

    // 轮次阶段
    public static final String ROUND_PHASE_DESCRIBE = "describe"; // 描述阶段
    public static final String ROUND_PHASE_VOTE = "vote"; // 投票阶段
    public static final String ROUND_PHASE_RESULT = "result"; // 结果阶段

    // 玩家角色
    public static final String PLAYER_ROLE_HOST = "host"; // 房主
    public static final String PLAYER_ROLE_PLAYER = "player"; // 普通玩家

    // 玩家状态
    public static final String PLAYER_STATUS_JOINED = "joined"; // 已加入
    public static final String PLAYER_STATUS_PLAYING = "playing"; // 游戏中
    public static final String PLAYER_STATUS_LEFT = "left"; // 已离开

    // 玩家身份
    public static final String IDENTITY_CIVILIAN = "civilian"; // 平民
    public static final String IDENTITY_UNDERCOVER = "undercover"; // 卧底
    public static final String IDENTITY_WHITEBOARD = "whiteboard"; // 白板

    // API结果码
    public static final Integer CODE_SUCCESS = 0; // 成功
    public static final Integer CODE_ERROR = -1; // 失败
    public static final Integer CODE_UNAUTHORIZED = 401; // 未授权
    public static final Integer CODE_FORBIDDEN = 403; // 禁止访问
    public static final Integer CODE_NOT_FOUND = 404; // 资源不存在

    // 默认配置
    public static final Integer DEFAULT_MAX_PLAYERS = 10; // 默认最大玩家数
    public static final Integer DEFAULT_ROUNDS = 3; // 默认游戏轮数
    public static final String DEFAULT_WORD_LIBRARY = "default"; // 默认词库
}