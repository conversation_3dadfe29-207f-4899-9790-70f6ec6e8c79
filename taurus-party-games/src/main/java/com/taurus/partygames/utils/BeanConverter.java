package com.taurus.partygames.utils;

import com.taurus.partygames.entity.RoomPlayer;
import com.taurus.partygames.vo.RoomPlayerVO;

public class BeanConverter {

    public static RoomPlayerVO convertToRoomPlayerVO(RoomPlayer roomPlayer) {

        RoomPlayerVO roomPlayerVO = new RoomPlayerVO();
        roomPlayerVO.setPlayerId(roomPlayer.getPlayerId());
        roomPlayerVO.setIndex(roomPlayer.getIndex());
        roomPlayerVO.setRoomId(roomPlayer.getRoomId());
        roomPlayerVO.setWord(roomPlayer.getWord());
        roomPlayerVO.setIdentity(roomPlayer.getIdentity());
        roomPlayerVO.setIsCreator(roomPlayer.getIsCreator());
        roomPlayerVO.setJoinTime(roomPlayer.getJoinTime());

        return roomPlayerVO;
    }

}
