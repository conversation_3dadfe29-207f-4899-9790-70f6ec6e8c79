package com.taurus.partygames.vo;

import java.util.List;

import lombok.Data;

/**
 * 房间信息VO
 */
@Data

public class RoomInfoVO {

    /**
     * 房间ID
     */
    private Long id;

    /**
     * 房间码
     */
    private String roomCode;

    /**
     * 房间名称
     */
    private String roomName;

    /**
     * 创建者ID
     */
    private String creatorId;

    /**
     * 创建者昵称
     */
    private String creatorName;

    /**
     * 卧底人数
     */
    private Integer undercoverCount;

    /**
     * 平民人数
     */
    private Integer civilianCount;

    /**
     * 白板人数
     */
    private Integer whiteboardCount;

    /**
     * 词语对ID
     */
    private Long wordPairId;

    /**
     * 词语对名称
     */
    private String wordPair;

    /**
     * 房间玩家列表
     */
    private List<RoomPlayerVO> players;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 最后活动时间
     */
    private Long lastActiveTime;
}