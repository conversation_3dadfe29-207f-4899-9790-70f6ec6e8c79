package com.taurus.partygames.vo;

import lombok.Data;

@Data
public class WebSocketMessage {
    private String type;
    private Object data;
    private String roomCode;

    public static WebSocketMessage of(String type, Object data, String roomCode) {
        WebSocketMessage message = new WebSocketMessage();
        message.setType(type);
        message.setData(data);
        message.setRoomCode(roomCode);
        return message;
    }

    public static WebSocketMessage playerJoined(String roomCode, RoomPlayerVO player) {
        return of("PLAYER_JOINED", player, roomCode);
    }

    public static WebSocketMessage playerLeft(String roomCode, Integer playerNo) {
        return of("PLAYER_LEFT", playerNo, roomCode);
    }

    public static WebSocketMessage gameStarted(String roomCode, RoomInfoVO roomInfo) {
        return of("GAME_STARTED", roomInfo, roomCode);
    }

    public static WebSocketMessage gameEnded(String roomCode, RoomInfoVO roomInfo) {
        return of("GAME_ENDED", roomInfo, roomCode);
    }

    public static WebSocketMessage roomReset(String roomCode, RoomInfoVO roomInfo) {
        return of("ROOM_RESET", roomInfo, roomCode);
    }

    public static WebSocketMessage error(String roomCode, String message) {
        return of("ERROR", message, roomCode);
    }
}