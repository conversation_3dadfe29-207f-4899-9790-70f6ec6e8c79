package com.taurus.partygames.vo;

import lombok.Data;

/**
 * API统一响应对象
 *
 * @param <T> 响应数据类型
 */
@Data
public class Result<T> {

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 状态信息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 成功响应
     *
     * @param data 响应数据
     * @param <T>  数据类型
     * @return 成功响应
     */
    public static <T> Result<T> success(T data) {
        Result<T> result = new Result<>();
        result.setCode(200);
        result.setMessage("成功");
        result.setData(data);
        return result;
    }

    /**
     * 失败响应
     *
     * @param code    状态码
     * @param message 错误信息
     * @param <T>     数据类型
     * @return 失败响应
     */
    public static <T> Result<T> error(Integer code, String message) {
        Result<T> result = new Result<>();
        result.setCode(code);
        result.setMessage(message);
        return result;
    }

    /**
     * 参数错误响应
     *
     * @param message 错误信息
     * @param <T>     数据类型
     * @return 参数错误响应
     */
    public static <T> Result<T> paramError(String message) {
        return error(400, message);
    }

    /**
     * 未授权响应
     *
     * @param <T> 数据类型
     * @return 未授权响应
     */
    public static <T> Result<T> unauthorized() {
        return error(401, "未授权");
    }

    /**
     * 禁止访问响应
     *
     * @param <T> 数据类型
     * @return 禁止访问响应
     */
    public static <T> Result<T> forbidden() {
        return error(403, "禁止访问");
    }

    /**
     * 资源不存在响应
     *
     * @param <T> 数据类型
     * @return 资源不存在响应
     */
    public static <T> Result<T> notFound() {
        return error(404, "资源不存在");
    }

    /**
     * 服务器错误响应
     *
     * @param <T> 数据类型
     * @return 服务器错误响应
     */
    public static <T> Result<T> serverError() {
        return error(500, "服务器错误");
    }
}