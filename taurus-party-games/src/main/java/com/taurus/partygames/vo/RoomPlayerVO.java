package com.taurus.partygames.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

/**
 * 玩家VO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RoomPlayerVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 房间ID
     */
    private Long roomId;

    /**
     * 房间号
     */
    private String roomCode;

    /**
     * 玩家ID
     */
    private String playerId;

    /**
     * 玩家在房间中的位置
     */
    private Integer index;

    /**
     * 是否是房主
     */
    private Boolean isCreator;

    /**
     * 玩家身份: civilian-平民, undercover-卧底，whiteboard-白板
     * 只有游戏开始后当前玩家才能看到自己的身份
     */
    private String identity;

    /**
     * 玩家词语
     * 只有游戏开始后当前玩家才能看到自己的词语
     */
    private String word;

    /**
     * 加入时间
     */
    private Long joinTime;
}