package com.taurus.partygames.websocket;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.taurus.partygames.service.GameRoomService;

import lombok.extern.slf4j.Slf4j;

/**
 * 游戏WebSocket服务
 */
@Slf4j
@Component
@ServerEndpoint("/ws/game/{roomCode}/{playerNo}")
public class GameWebSocket {

    // 使用静态注入的方式
    private static GameRoomService gameRoomService;

    @Autowired
    public void setGameRoomService(GameRoomService gameRoomService) {
        GameWebSocket.gameRoomService = gameRoomService;
    }

    private static final ObjectMapper objectMapper = new ObjectMapper();

    // 存储房间号与对应的所有连接（房间Code -> (玩家编号 -> WebSocket连接)）
    private static final Map<String, Map<String, GameWebSocket>> ROOM_CONNECTIONS = new ConcurrentHashMap<>();

    // 本次连接的房间号
    private String roomCode;

    // 本次连接的玩家编号
    private String playerNo;

    // WebSocket连接会话
    private Session session;

    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("roomCode") String roomCode,
            @PathParam("playerNo") String playerNo) {
        this.roomCode = roomCode;
        this.playerNo = playerNo;
        this.session = session;

        // 将连接加入到房间连接映射
        ROOM_CONNECTIONS.computeIfAbsent(roomCode, k -> new ConcurrentHashMap<>())
                .put(playerNo, this);

        log.info("玩家[{}]加入房间[{}]的WebSocket连接，当前房间连接数: {}",
                playerNo, roomCode, ROOM_CONNECTIONS.get(roomCode).size());

    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        if (roomCode != null && playerNo != null) {
            // 从房间连接映射中移除
            Map<String, GameWebSocket> roomConnections = ROOM_CONNECTIONS.get(roomCode);
            if (roomConnections != null) {
                roomConnections.remove(playerNo);
                log.info("玩家[{}]退出房间[{}]的WebSocket连接，当前房间连接数: {}",
                        playerNo, roomCode, roomConnections.size());

                // 如果房间没有连接了，则移除房间
                if (roomConnections.isEmpty()) {
                    ROOM_CONNECTIONS.remove(roomCode);
                    log.info("房间[{}]已无连接，移除房间", roomCode);
                }
            }
        }
    }

    /**
     * 收到客户端消息后调用的方法
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        log.info("收到来自玩家[{}]的消息: {}", playerNo, message);
        // 暂不处理客户端消息，游戏状态更新由服务调用广播
    }

    /**
     * 发生错误时调用
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("玩家[{}]的WebSocket连接发生错误: {}", playerNo, error.getMessage());
        error.printStackTrace();
    }

    /**
     * 向指定房间的所有连接发送消息
     */
    public static void sendMessageToRoom(String roomCode, Object message) {
        Map<String, GameWebSocket> connections = ROOM_CONNECTIONS.get(roomCode);
        if (connections != null && !connections.isEmpty()) {
            String messageStr;
            try {
                messageStr = objectMapper.writeValueAsString(message);
                for (GameWebSocket webSocket : connections.values()) {
                    webSocket.sendMessage(messageStr);
                }
                log.info("向房间[{}]的{}个玩家广播消息: {}", roomCode, connections.size(), messageStr);
            } catch (Exception e) {
                log.error("消息广播失败", e);
            }
        }
    }

    /**
     * 向单个WebSocket客户端发送消息
     */
    private void sendMessage(String message) {
        try {
            this.session.getBasicRemote().sendText(message);
        } catch (IOException e) {
            log.error("消息发送失败", e);
        }
    }
}