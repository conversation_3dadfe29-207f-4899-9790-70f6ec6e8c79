/**
 * DTO (Data Transfer Object)
 * 作用：在系统不同层之间传递数据，特别是客户端请求参数封装
 * 特点：包含数据验证注解(@NotNull, @Min等)，无业务逻辑
 * 生命周期：请求处理过程中，完成参数校验后转换为领域对象
 * 示例：CreateRoomDTO, JoinRoomDTO类
 * 创建房间DTO
 */
package com.taurus.partygames.dto;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 创建房间DTO
 */
@Data
public class RoomCreateDTO {

    /**
     * 创建者ID
     */
    @NotBlank(message = "创建者ID不能为空")
    private String creatorId;

    /**
     * 词库名称
     */
    @NotBlank(message = "词库名称不能为空")
    private String libraryName;

    /**
     * 词语是否随机
     */
    @NotNull(message = "词语是否随机不能为空")
    private Boolean wordRandom;

    /**
     * 平民卧底词语对
     */
    private String wordPair;

    /**
     * 卧底人数
     */
    @NotNull(message = "卧底人数不能为空")
    @Min(value = 1, message = "卧底人数不能小于1")
    private Integer undercoverCount;

    /**
     * 平民人数
     */
    @NotNull(message = "平民人数不能为空")
    @Min(value = 1, message = "平民人数不能小于1")
    private Integer civilianCount;

    /**
     * 白板人数
     */
    @NotNull(message = "白板人数不能为空")
    @Min(value = 0, message = "白板人数不能小于0")
    private Integer whiteboardCount;

}