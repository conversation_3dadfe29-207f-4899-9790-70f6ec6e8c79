package com.taurus.partygames.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.taurus.partygames.dto.RoomCreateDTO;
import com.taurus.partygames.dto.RoomJoinDTO;
import com.taurus.partygames.entity.GameRoom;
import com.taurus.partygames.entity.RoomPlayer;
import com.taurus.partygames.exception.GameException;
import com.taurus.partygames.mapper.GameRoomMapper;
import com.taurus.partygames.repository.GameRoomRepository;
import com.taurus.partygames.repository.RoomPlayerRepository;
import com.taurus.partygames.service.GameRoomService;
import com.taurus.partygames.service.WordPairService;
import com.taurus.partygames.utils.Constants;
import com.taurus.partygames.vo.RoomInfoVO;
import com.taurus.partygames.vo.RoomPlayerVO;
import com.taurus.partygames.websocket.GameWebSocket;

import lombok.extern.slf4j.Slf4j;

/**
 * 游戏房间服务实现类
 */
@Slf4j
@Service
public class GameRoomServiceImpl implements GameRoomService {

    @Autowired
    private WordPairService wordPairService;

    @Autowired
    private GameRoomRepository gameRoomRepository;

    @Autowired
    private GameRoomMapper gameRoomMapper;

    @Autowired
    private RoomPlayerRepository roomPlayerRepository;

    private final ConcurrentHashMap<Long, ReentrantLock> roomLocks = new ConcurrentHashMap<>();

    private ReentrantLock getLockForRoom(Long roomId) {
        return roomLocks.computeIfAbsent(roomId, k -> new ReentrantLock());
    }

    /**
     * 创建游戏房间
     * 
     * @param roomCreateDTO 房间创建DTO
     * @return 房间信息VO
     * @throws Exception
     */
    @Override
    @Transactional
    public RoomInfoVO createRoom(RoomCreateDTO roomCreateDTO) {
        // 生成6位随机房间码
        String roomCode = generateRoomCode();
        // 确定词语对
        Boolean random = roomCreateDTO.getWordRandom();
        String wordPair = null;
        if (random) {
            // 随机获取词语对
            String wordLibrary = roomCreateDTO.getLibraryName();
            try {
                wordPair = wordPairService.getRandomWordPair(wordLibrary);
            } catch (Exception e) {
                log.error("随机获取词语对失败", e);
            }
        } else {
            // 指定词语对
            wordPair = roomCreateDTO.getWordPair();
        }

        // 持久化GameRoom
        GameRoom gameRoom = new GameRoom();
        gameRoom.setRoomCode(roomCode);
        gameRoom.setCreatorId(roomCreateDTO.getCreatorId());
        gameRoom.setWordPair(wordPair);
        gameRoom.setCreateTime(new Date());
        gameRoom.setUpdateTime(new Date());

        // 设置玩家类型数量
        gameRoom.setCivilianCount(roomCreateDTO.getCivilianCount());
        gameRoom.setUndercoverCount(roomCreateDTO.getUndercoverCount());
        gameRoom.setWhiteboardCount(roomCreateDTO.getWhiteboardCount());

        // 设置剩余玩家类型数量
        gameRoom.setRemainingCivilianCount(roomCreateDTO.getCivilianCount());
        gameRoom.setRemainingUndercoverCount(roomCreateDTO.getUndercoverCount());
        gameRoom.setRemainingWhiteboardCount(roomCreateDTO.getWhiteboardCount());
        gameRoomRepository.save(gameRoom);

        Map<String, String> identityAndWord = null;

        // 创建房间的同时，房主获取词语，房主参加游戏
        // 调用随机获取身份和词语的方法
        // identityAndWord = getIdentityAndWord(gameRoom, roomCreateDTO.getCreatorId(),
        // true);

        // if (identityAndWord == null) {
        // log.error("没有可分配的身份");
        // return null;
        // }

        // 构建返回对象
        RoomInfoVO roomInfoVO = new RoomInfoVO();
        roomInfoVO.setId(gameRoom.getId());
        roomInfoVO.setRoomCode(roomCode);
        roomInfoVO.setCreatorId(roomCreateDTO.getCreatorId());
        roomInfoVO.setCreateTime(new Date().getTime());
        roomInfoVO.setLastActiveTime(new Date().getTime());
        roomInfoVO.setPlayers(new ArrayList<>());

        // RoomPlayerVO hostPlayerVO = new RoomPlayerVO();
        // hostPlayerVO.setPlayerId(roomCreateDTO.getCreatorId());
        // hostPlayerVO.setIsCreator(true);
        // hostPlayerVO.setJoinTime(new Date().getTime());
        // hostPlayerVO.setIdentity(identityAndWord.get("identity"));
        // hostPlayerVO.setWord(identityAndWord.get("word"));

        // List<RoomPlayerVO> players = new ArrayList<>();
        // players.add(hostPlayerVO);
        // roomInfoVO.setPlayers(players);

        return roomInfoVO;
    }

    /**
     * 获取身份和词语
     * 
     * @param roomCreateDTO 房间创建DTO
     * @param wordPair      词语对
     * @return 身份和词语
     */
    private Map<String, String> getIdentityAndWord(GameRoom gameRoom, String playerId, Boolean isCreator) {
        // 随机获取身份和词语
        Integer remainingWhiteboardCount = gameRoom.getRemainingWhiteboardCount();
        Integer remainingCivilianCount = gameRoom.getRemainingCivilianCount();
        Integer remainingUndercoverCount = gameRoom.getRemainingUndercoverCount();

        Integer whiteboardCount = gameRoom.getWhiteboardCount();
        Integer civilianCount = gameRoom.getCivilianCount();
        Integer undercoverCount = gameRoom.getUndercoverCount();

        Random random = new Random();
        int totalCount = whiteboardCount + civilianCount + undercoverCount;
        int remainingCount = remainingWhiteboardCount + remainingCivilianCount + remainingUndercoverCount;
        Integer index = totalCount + 1 - remainingCount;

        // 检查是否还有可分配的身份
        if (remainingCount <= 0) {
            log.error("没有可分配的身份");
            return null;
        }

        String identity = null;
        int randomIndex = random.nextInt(remainingCount);
        if (remainingWhiteboardCount > 0 && randomIndex < remainingWhiteboardCount) {
            identity = Constants.IDENTITY_WHITEBOARD;
        } else if (remainingCivilianCount > 0 && randomIndex < remainingWhiteboardCount + remainingCivilianCount) {
            identity = Constants.IDENTITY_CIVILIAN;
        } else if (remainingUndercoverCount > 0) {
            identity = Constants.IDENTITY_UNDERCOVER;
        }

        if (identity == null) {
            log.error("没有可分配的身份");
            return null;
        }

        Map<String, String> map = new HashMap<>();
        map.put("index", index.toString());
        map.put("identity", identity);

        // 拆分词语对
        String wordPair = gameRoom.getWordPair();
        if (wordPair == null || wordPair.isEmpty()) {
            log.error("词语对为空");

            throw new GameException(1010, "词语对为空");

        } else {
            String[] words = wordPair.split(",");
            // 根据身份分配词语，平民和卧底各一个词语，白板没有词语
            if (identity.equals(Constants.IDENTITY_CIVILIAN)) {
                map.put("word", words[0]);
            } else if (identity.equals(Constants.IDENTITY_UNDERCOVER)) {
                map.put("word", words[1]);
            } else {
                map.put("word", "");
            }
        }

        // 更新剩余身份数
        if (identity.equals(Constants.IDENTITY_WHITEBOARD)
                && gameRoom.getRemainingWhiteboardCount() > 0) {
            gameRoom.setRemainingWhiteboardCount(gameRoom.getRemainingWhiteboardCount() - 1);
        } else if (identity.equals(Constants.IDENTITY_CIVILIAN)
                && gameRoom.getRemainingCivilianCount() > 0) {
            gameRoom.setRemainingCivilianCount(gameRoom.getRemainingCivilianCount() - 1);
        } else if (identity.equals(Constants.IDENTITY_UNDERCOVER)
                && gameRoom.getRemainingUndercoverCount() > 0) {
            gameRoom.setRemainingUndercoverCount(gameRoom.getRemainingUndercoverCount() - 1);
        }
        gameRoomRepository.save(gameRoom);

        // 添加房主到玩家列表,持久化RoomPlayer
        RoomPlayer roomPlayer = new RoomPlayer();
        roomPlayer.setRoomId(gameRoom.getId());
        roomPlayer.setPlayerId(playerId);
        // 获取玩家在房间中的位置
        roomPlayer.setIndex(index);
        roomPlayer.setIsCreator(isCreator);
        roomPlayer.setIdentity(identity);
        roomPlayer.setWord(map.get("word"));
        roomPlayer.setJoinTime(new Date().getTime());
        roomPlayer.setLastActiveTime(new Date().getTime());
        roomPlayerRepository.save(roomPlayer);

        return map;
    }

    /**
     * 加入游戏房间
     * 
     * @param roomJoinDTO 房间加入DTO
     * @return 玩家信息VO
     */
    @Override
    public RoomPlayerVO joinRoom(RoomJoinDTO roomJoinDTO) {
        Long roomId = roomJoinDTO.getRoomId();
        ReentrantLock lock = getLockForRoom(roomId);

        lock.lock();
        try {
            return joinRoomInternal(roomJoinDTO);
        } finally {
            lock.unlock();
        }
    }

    @Transactional
    private RoomPlayerVO joinRoomInternal(RoomJoinDTO roomJoinDTO) {
        Long roomId = roomJoinDTO.getRoomId();
        ReentrantLock lock = getLockForRoom(roomId);

        lock.lock();
        try {
            GameRoom gameRoom = null;
            Map<String, String> identityAndWord = null;

            // 检查房间是否存在
            gameRoom = gameRoomMapper.findById(roomId);

            if (gameRoom == null) {
                log.error("房间不存在");
                throw new GameException(1010, "房间不存在");
            }

            // 检查玩家是否已加入房间
            RoomPlayer roomPlayer = roomPlayerRepository.findByRoomIdAndPlayerId(gameRoom.getId(),
                    roomJoinDTO.getPlayerId());
            if (roomPlayer != null) {
                log.info("玩家已加入房间");
                // 返回已获取的身份和词语
                RoomPlayerVO playerVO = com.taurus.partygames.utils.BeanConverter.convertToRoomPlayerVO(roomPlayer);
                playerVO.setRoomCode(gameRoom.getRoomCode());
                return playerVO;

            }

            // 检查房间是否满员
            if (gameRoom.getRemainingWhiteboardCount() <= 0 && gameRoom.getRemainingCivilianCount() <= 0
                    && gameRoom.getRemainingUndercoverCount() <= 0) {
                log.error("房间满员");
                throw new GameException(1010, "房间满员");
            }

            // 调用随机获取身份和词语的方法
            identityAndWord = getIdentityAndWord(gameRoom, roomJoinDTO.getPlayerId(), false);

            if (identityAndWord == null) {
                log.error("没有可分配的身份");
                return null;
            }

            // 构建返回对象
            RoomPlayerVO playerVO = new RoomPlayerVO();
            playerVO.setPlayerId(roomJoinDTO.getPlayerId());
            playerVO.setIndex(Integer.parseInt(identityAndWord.get("index")));
            playerVO.setRoomId(gameRoom.getId());
            playerVO.setRoomCode(gameRoom.getRoomCode());
            playerVO.setIsCreator(false);
            playerVO.setIdentity(identityAndWord.get("identity"));
            playerVO.setWord(identityAndWord.get("word"));
            playerVO.setJoinTime(new Date().getTime());

            return playerVO;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 获取房间信息
     * 
     * @param roomCode 房间码
     * @return 房间信息VO
     */

    @Override
    public GameRoom findByRoomCode(String roomCode) {
        GameRoom gameRoom = gameRoomRepository.findByRoomCode(roomCode);
        return gameRoom;
    }

    /**
     * 获取房间信息
     *
     * @param roomCode 房间码
     * @return 房间信息VO
     */
    @Override
    public GameRoom findByRoomId(Long roomId) {
        // 检查房间是否存在
        GameRoom gameRoom = gameRoomMapper.findById(roomId);
        return gameRoom;
    }

    /**
     * 获取房间信息
     * 
     * @param roomCode 房间码
     * @return 房间信息VO
     */
    @Override
    public RoomInfoVO getRoomInfo(String roomCode) {

        // 构建房间信息VO
        RoomInfoVO roomInfo = new RoomInfoVO();
        roomInfo.setId(1L); // 示例ID
        roomInfo.setRoomCode(roomCode);
        roomInfo.setRoomName("示例房间");
        roomInfo.setCreatorId("creator1");
        roomInfo.setCreateTime(new Date().getTime());
        roomInfo.setLastActiveTime(new Date().getTime());

        // 模拟房间玩家列表
        List<RoomPlayerVO> roomPlayers = new ArrayList<>();
        RoomPlayerVO playerVO = new RoomPlayerVO();
        playerVO.setJoinTime(new Date().getTime());
        roomPlayers.add(playerVO);
        roomInfo.setPlayers(roomPlayers);

        return roomInfo;
    }

    /**
     * 获取单个玩家信息
     * 
     * @param roomCode 房间码
     * @param playerNo 玩家编号
     * @return 玩家信息 VO
     */
    @Override
    public RoomPlayerVO getPlayerInfo(String roomCode, String playerNo) {
        RoomPlayerVO playerVO = new RoomPlayerVO();
        playerVO.setPlayerId(playerNo);

        return playerVO;
    }

    /**
     * 玩家离开房间
     * 
     * @param roomCode 房间码
     * @param playerNo 玩家编号
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean leaveRoom(String roomCode, String playerNo) {
        // 通过WebSocket通知房间内其他玩家
        RoomInfoVO roomInfo = getRoomInfo(roomCode);
        GameWebSocket.sendMessageToRoom(roomCode, roomInfo);

        return true;
    }

    /**
     * 房主踢出玩家
     * 
     * @param roomCode 房间码
     * @param hostNo   房主编号
     * @param playerNo 玩家编号
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean kickPlayer(String roomCode, String hostNo, String playerNo) {
        // 通过WebSocket通知房间内其他玩家
        RoomInfoVO roomInfo = getRoomInfo(roomCode);
        GameWebSocket.sendMessageToRoom(roomCode, roomInfo);

        return true;
    }

    /**
     * 生成6位随机房间码
     * 
     * @return 房间码
     */
    private String generateRoomCode() {
        StringBuilder sb = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 6; i++) {
            sb.append(random.nextInt(10));
        }
        return sb.toString();
    }

    /**
     * 重新开始游戏
     * 
     * @param roomCode      房间码
     * @param hostNo        房主编号
     * @param roomCreateDTO 房间创建DTO
     * @return 新房间信息
     */
    @Override
    @Transactional
    public RoomInfoVO restartGame(String roomCode, String hostNo, RoomCreateDTO roomCreateDTO) {
        // 检查房间是否存在
        GameRoom gameRoom = gameRoomRepository.findByRoomCode(roomCode);
        if (gameRoom == null) {
            log.error("房间不存在");
            throw new GameException(1010, "房间不存在");
        }

        // 检查是否是房主
        RoomPlayer creator = roomPlayerRepository.findByRoomIdAndPlayerId(gameRoom.getId(), hostNo);
        if (creator == null || !creator.getIsCreator()) {
            log.error("只有房主才能重新开始游戏");
            throw new GameException(1010, "只有房主才能重新开始游戏");
        }

        // 删除当前房间的所有玩家
        roomPlayerRepository.deleteByRoomId(gameRoom.getId());

        // 创建新的游戏房间
        return createRoom(roomCreateDTO);
    }
}