package com.taurus.partygames.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.taurus.partygames.mapper.RoomPlayerMapper;
import com.taurus.partygames.service.RoomPlayerService;
import com.taurus.partygames.vo.JoinedGameRoomVO;
import com.taurus.partygames.vo.RoomPlayerVO;

@Service
public class RoomPlayerServiceImpl implements RoomPlayerService {

    private final RoomPlayerMapper roomPlayerMapper;

    public RoomPlayerServiceImpl(RoomPlayerMapper roomPlayerMapper) {
        this.roomPlayerMapper = roomPlayerMapper;
    }

    @Override
    public List<JoinedGameRoomVO> getJoinedRooms(Long playerId) {
        // 转换为String类型传递参数
        return roomPlayerMapper.selectJoinedRoomsByPlayerId(playerId.toString());
    }

    /**
     * 获取玩家加入的房间
     *
     * @param roomId
     * @param playerId
     * @return
     */
    @Override
    public RoomPlayerVO getJoinedplayer(Long roomId, Long playerId) {

        // 查询玩家在指定房间的信息
        RoomPlayerVO result = roomPlayerMapper.selectJoinedplayerByRoomIdAndPlayerId(
                roomId.toString(),
                playerId.toString());

        return result;
    }
}