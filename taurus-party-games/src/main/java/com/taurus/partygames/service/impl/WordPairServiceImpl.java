package com.taurus.partygames.service.impl;

import java.util.List;
import java.util.Random;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.taurus.partygames.entity.WordPair;
import com.taurus.partygames.exception.GameException;
import com.taurus.partygames.mapper.WordPairMapper;
import com.taurus.partygames.service.WordPairService;

@Service
public class WordPairServiceImpl implements WordPairService {

    @Autowired
    private WordPairMapper wordPairMapper;

    /**
     * 随机获取词语对
     * 
     * @param wordLibrary 词语库名称
     * @return 词语对
     * @throws Exception
     */
    @Override
    public String getRandomWordPair(String wordLibrary) throws Exception {
        // 从数据库中获取词语对
        List<WordPair> wordPairs = wordPairMapper.selectByLibraryName(wordLibrary);
        if (wordPairs.isEmpty()) {
            throw GameException.wordLibraryNotFound();
        }
        // 随机获取一个词语对
        int randomIndex = new Random().nextInt(wordPairs.size());
        StringBuilder wordPair = new StringBuilder();
        wordPair.append(wordPairs.get(randomIndex).getCivilianWord());
        wordPair.append(",");
        wordPair.append(wordPairs.get(randomIndex).getUndercoverWord());
        return wordPair.toString();
    }

}
