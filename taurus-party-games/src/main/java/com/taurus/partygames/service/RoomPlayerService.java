package com.taurus.partygames.service;

import java.util.List;

import com.taurus.partygames.vo.JoinedGameRoomVO;
import com.taurus.partygames.vo.RoomPlayerVO;

public interface RoomPlayerService {

    /**
     * 获取玩家加入的房间
     * 
     * @param playerId
     * @return
     */
    List<JoinedGameRoomVO> getJoinedRooms(Long playerId);

    /**
     * 获取玩家加入的房间
     *
     * @param roomId
     * @param playerId
     * @return
     */
    RoomPlayerVO getJoinedplayer(Long roomId, Long playerId);

}
