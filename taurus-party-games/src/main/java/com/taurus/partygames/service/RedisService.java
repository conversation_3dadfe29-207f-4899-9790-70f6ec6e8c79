package com.taurus.partygames.service;

import java.util.Iterator;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.RedisConnectionFailureException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

/**
 * Redis服务类，提供缓存操作功能
 * 当Redis服务不可用时，会自动降级为内存缓存模式
 */
@Service("partyGamesRedisService")
public class RedisService {
    private static final Logger logger = LoggerFactory.getLogger(RedisService.class);
    @Autowired
    @Qualifier("partyGamesRedisTemplate")
    private RedisTemplate<String, Object> redisTemplate;

    // 内存缓存，当Redis不可用时使用
    private final ConcurrentHashMap<String, Object> memoryCache = new ConcurrentHashMap<>();
    // 内存缓存过期时间记录
    private final ConcurrentHashMap<String, Long> expirations = new ConcurrentHashMap<>();

    /**
     * 从缓存中获取值
     * 
     * @param key 键
     * @return 值，如果键不存在或已过期则返回null
     */
    public Object get(String key) {
        try {
            // 尝试从Redis获取
            return redisTemplate.opsForValue().get(key);
        } catch (RedisConnectionFailureException e) {
            logger.warn("Redis连接失败，使用内存缓存: {}", e.getMessage());
            // 降级为内存缓存
            return getFromMemory(key);
        } catch (Exception e) {
            logger.error("Redis获取值异常: {}", e.getMessage());
            // 降级为内存缓存
            return getFromMemory(key);
        }
    }

    /**
     * 从内存缓存中获取值
     */
    private Object getFromMemory(String key) {
        // 检查是否过期
        if (isExpired(key)) {
            memoryCache.remove(key);
            expirations.remove(key);
            return null;
        }
        return memoryCache.get(key);
    }

    /**
     * 检查键是否已过期
     */
    private boolean isExpired(String key) {
        Long expiration = expirations.get(key);
        return expiration != null && expiration < System.currentTimeMillis();
    }

    /**
     * 设置缓存
     * 
     * @param key   键
     * @param value 值
     */
    public void set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
        } catch (RedisConnectionFailureException e) {
            logger.warn("Redis连接失败，使用内存缓存: {}", e.getMessage());
            // 降级为内存缓存
            memoryCache.put(key, value);
        } catch (Exception e) {
            logger.error("Redis设置值异常: {}", e.getMessage());
            // 降级为内存缓存
            memoryCache.put(key, value);
        }
    }

    /**
     * 设置缓存并设置过期时间
     * 
     * @param key     键
     * @param value   值
     * @param timeout 过期时间
     * @param unit    时间单位
     */
    public void set(String key, Object value, long timeout, TimeUnit unit) {
        try {
            redisTemplate.opsForValue().set(key, value, timeout, unit);
        } catch (RedisConnectionFailureException e) {
            logger.warn("Redis连接失败，使用内存缓存: {}", e.getMessage());
            // 降级为内存缓存
            memoryCache.put(key, value);
            // 计算过期时间点
            long expireTime = System.currentTimeMillis() + unit.toMillis(timeout);
            expirations.put(key, expireTime);
        } catch (Exception e) {
            logger.error("Redis设置值异常: {}", e.getMessage());
            // 降级为内存缓存
            memoryCache.put(key, value);
            // 计算过期时间点
            long expireTime = System.currentTimeMillis() + unit.toMillis(timeout);
            expirations.put(key, expireTime);
        }
    }

    /**
     * 删除缓存
     * 
     * @param key 键
     */
    public void delete(String key) {
        try {
            redisTemplate.delete(key);
        } catch (RedisConnectionFailureException e) {
            logger.warn("Redis连接失败，使用内存缓存: {}", e.getMessage());
            // 从内存缓存中删除
            memoryCache.remove(key);
            expirations.remove(key);
        } catch (Exception e) {
            logger.error("Redis删除值异常: {}", e.getMessage());
            // 从内存缓存中删除
            memoryCache.remove(key);
            expirations.remove(key);
        }
    }

    /**
     * 设置过期时间
     * 
     * @param key     键
     * @param timeout 过期时间
     * @param unit    时间单位
     * @return 设置成功返回true，否则返回false
     */
    public boolean expire(String key, long timeout, TimeUnit unit) {
        try {
            return Boolean.TRUE.equals(redisTemplate.expire(key, timeout, unit));
        } catch (RedisConnectionFailureException e) {
            logger.warn("Redis连接失败，使用内存缓存: {}", e.getMessage());
            // 计算过期时间点
            long expireTime = System.currentTimeMillis() + unit.toMillis(timeout);
            expirations.put(key, expireTime);
            return true;
        } catch (Exception e) {
            logger.error("Redis设置过期时间异常: {}", e.getMessage());
            // 计算过期时间点
            long expireTime = System.currentTimeMillis() + unit.toMillis(timeout);
            expirations.put(key, expireTime);
            return true;
        }
    }

    /**
     * 判断键是否存在
     * 
     * @param key 键
     * @return 如果键存在且未过期返回true，否则返回false
     */
    public boolean hasKey(String key) {
        try {
            return Boolean.TRUE.equals(redisTemplate.hasKey(key));
        } catch (RedisConnectionFailureException e) {
            logger.warn("Redis连接失败，使用内存缓存: {}", e.getMessage());
            // 检查内存缓存
            return memoryCache.containsKey(key) && !isExpired(key);
        } catch (Exception e) {
            logger.error("Redis判断键是否存在异常: {}", e.getMessage());
            // 检查内存缓存
            return memoryCache.containsKey(key) && !isExpired(key);
        }
    }

    /**
     * 直接传入一个pojo对象 放入一个String对象，字符串会加上一对双引号！！！
     * 
     * @param key
     * @param value
     */
    public void put(String key, Object value, Long expire, TimeUnit timeUnit) {
        redisTemplate.opsForValue().set(key, JSONObject.toJSONStringWithDateFormat(value, "yyyy-MM-dd HH:mm:ss",
                SerializerFeature.WriteDateUseDateFormat), expire, timeUnit);// 保存一年
    }

    /**
     * 直接存储字符串值，不进行JSON序列化
     * 
     * @param key     键
     * @param value   值
     * @param timeout 过期时间
     * @param unit    时间单位
     */
    public void putNoJson(String key, String value, Long timeout, TimeUnit unit) {
        try {
            redisTemplate.opsForValue().set(key, value, timeout, unit);
        } catch (RedisConnectionFailureException e) {
            logger.warn("Redis连接失败，使用内存缓存: {}", e.getMessage());
            // 降级为内存缓存
            memoryCache.put(key, value);
            // 计算过期时间点
            long expireTime = System.currentTimeMillis() + unit.toMillis(timeout);
            expirations.put(key, expireTime);
        } catch (Exception e) {
            logger.error("Redis设置值异常: {}", e.getMessage());
            // 降级为内存缓存
            memoryCache.put(key, value);
            // 计算过期时间点
            long expireTime = System.currentTimeMillis() + unit.toMillis(timeout);
            expirations.put(key, expireTime);
        }
    }

    /**
     * 删除一个键值
     * 
     * @param key
     * @return
     */
    public boolean delete(StringBuffer key) {
        String value = (String) redisTemplate.opsForValue().get(key.toString());
        if (value != null) {
            return redisTemplate.delete(key.toString());
        } else {
            return true;
        }
    }

    /**
     * 模糊匹配多个并删除
     * 
     * @param prex
     */
    public void deleteByPrex(String prex) {
        Set<String> keys = redisTemplate.keys(prex + "*");
        if (keys != null && keys.size() > 0) {
            Iterator<String> iterator = keys.iterator();
            while (iterator.hasNext()) {
                String key = iterator.next();
                redisTemplate.delete(key);
            }
        }
    }
}
