package com.taurus.partygames.service;

import com.taurus.partygames.dto.RoomCreateDTO;
import com.taurus.partygames.dto.RoomJoinDTO;
import com.taurus.partygames.entity.GameRoom;
import com.taurus.partygames.vo.RoomInfoVO;
import com.taurus.partygames.vo.RoomPlayerVO;

/**
 * 游戏房间服务接口
 */
public interface GameRoomService {

    /**
     * 创建游戏房间
     * 
     * @param roomCreateDTO 房间创建DTO
     * @return 房间信息VO
     */
    RoomInfoVO createRoom(RoomCreateDTO roomCreateDTO);

    /**
     * 加入游戏房间
     * 
     * @param roomJoinDTO 房间加入DTO
     * @return 玩家VO
     */
    RoomPlayerVO joinRoom(RoomJoinDTO roomJoinDTO);

    /**
     * 获取房间信息
     * 
     * @param roomCode 房间码
     * @return 房间信息VO
     */
    RoomInfoVO getRoomInfo(String roomCode);

    /**
     * 获取单个玩家信息
     * 
     * @param roomCode 房间码
     * @param playerNo 玩家编号
     * @return 玩家VO
     */
    RoomPlayerVO getPlayerInfo(String roomCode, String playerNo);

    /**
     * 玩家离开房间
     * 
     * @param roomCode 房间码
     * @param playerNo 玩家编号
     * @return 是否成功
     */
    boolean leaveRoom(String roomCode, String playerNo);

    /**
     * 房主踢出玩家
     * 
     * @param roomCode 房间码
     * @param hostNo   房主编号
     * @param playerNo 玩家编号
     * @return 是否成功
     */
    boolean kickPlayer(String roomCode, String hostNo, String playerNo);

    /**
     * 获取房间信息
     * 
     * @param roomCode 房间码
     * @return 房间信息VO
     */
    GameRoom findByRoomCode(String roomCode);

    /**
     * 获取房间信息
     * 
     * @param roomId
     * @return
     */
    GameRoom findByRoomId(Long roomId);

    /**
     * @param roomCode      房间码
     * @param hostNo        房主编号
     * @param roomCreateDTO 房间创建DTO
     * @return 新房间信息
     */
    RoomInfoVO restartGame(String roomCode, String hostNo, RoomCreateDTO roomCreateDTO);

}