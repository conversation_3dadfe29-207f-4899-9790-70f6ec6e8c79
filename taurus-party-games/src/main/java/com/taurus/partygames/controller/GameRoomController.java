package com.taurus.partygames.controller;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.taurus.partygames.dto.RoomCreateDTO;
import com.taurus.partygames.dto.RoomJoinDTO;
import com.taurus.partygames.entity.GameRoom;
import com.taurus.partygames.service.GameRoomService;
import com.taurus.partygames.vo.Result;
import com.taurus.partygames.vo.RoomInfoVO;
import com.taurus.partygames.vo.RoomPlayerVO;

import lombok.extern.slf4j.Slf4j;

/**
 * 游戏房间控制器
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/room")
public class GameRoomController {

    @Autowired
    private GameRoomService gameRoomService;

    /**
     * 创建游戏房间
     * 
     * @param roomCreateDTO 房间创建DTO
     * @return 房间VO
     */
    @PostMapping("/create")
    public Result<RoomInfoVO> createRoom(@Valid @RequestBody RoomCreateDTO roomCreateDTO) {
        log.info("创建房间请求: {}", roomCreateDTO);
        RoomInfoVO roomInfoVO = gameRoomService.createRoom(roomCreateDTO);
        return Result.success(roomInfoVO);
    }

    /**
     * 加入游戏房间
     * 
     * @param roomJoinDTO 房间加入DTO
     * @return 玩家VO
     */
    @PostMapping("/join")
    public Result<RoomPlayerVO> joinRoom(@Valid @RequestBody RoomJoinDTO roomJoinDTO) {

        if (roomJoinDTO.getPlayerId() == null || roomJoinDTO.getRoomId() == null) {
            return Result.paramError("玩家ID和房间ID不能为空");
        }

        log.info("加入房间请求: {}", roomJoinDTO);
        RoomPlayerVO playerVO = gameRoomService.joinRoom(roomJoinDTO);
        return Result.success(playerVO);
    }

    /**
     * 获取房间信息
     * 
     * @param roomCode 房间码
     * @return 房间信息VO
     */
    @GetMapping("/info")
    public Result<GameRoom> getRoomInfo(@RequestParam @NotNull(message = "房间id不能为空") Long roomId) {
        log.info("获取房间信息, roomId: {}", roomId);
        GameRoom room = gameRoomService.findByRoomId(roomId);
        return Result.success(room);
    }

    /**
     * 重新开始游戏（新游戏）
     * 
     * @param roomCode 房间码
     * @param hostNo   房主编号
     * @return 新房间信息
     */
    @PostMapping("/restart")
    public Result<RoomInfoVO> restartGame(
            @RequestParam @NotBlank(message = "房间码不能为空") String roomCode,
            @RequestParam @NotBlank(message = "房主编号不能为空") String hostNo,
            @Valid @RequestBody RoomCreateDTO roomCreateDTO) {
        log.info("重新开始游戏, roomCode: {}, hostNo: {}", roomCode, hostNo);
        RoomInfoVO roomInfoVO = gameRoomService.restartGame(roomCode, hostNo, roomCreateDTO);
        return Result.success(roomInfoVO);
    }

    /**
     * 玩家离开房间
     * 
     * @param roomCode 房间码
     * @param playerNo 玩家编号
     * @return 成功状态
     */
    @PostMapping("/leave")
    public Result<Boolean> leaveRoom(
            @RequestParam @NotBlank(message = "房间码不能为空") String roomCode,
            @RequestParam @NotBlank(message = "玩家编号不能为空") String playerNo) {
        boolean result = gameRoomService.leaveRoom(roomCode, playerNo);
        return Result.success(result);
    }

    /**
     * 房主踢出玩家
     * 
     * @param roomCode 房间码
     * @param hostNo   房主编号
     * @param playerNo 玩家编号
     * @return 成功状态
     */
    @PostMapping("/kick")
    public Result<Boolean> kickPlayer(
            @RequestParam @NotBlank(message = "房间码不能为空") String roomCode,
            @RequestParam @NotBlank(message = "房主编号不能为空") String hostNo,
            @RequestParam @NotBlank(message = "玩家编号不能为空") String playerNo) {
        boolean result = gameRoomService.kickPlayer(roomCode, hostNo, playerNo);
        return Result.success(result);
    }
}