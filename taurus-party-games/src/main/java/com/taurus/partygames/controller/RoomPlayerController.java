package com.taurus.partygames.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.taurus.partygames.service.RoomPlayerService;
import com.taurus.partygames.vo.JoinedGameRoomVO;
import com.taurus.partygames.vo.Result;
import com.taurus.partygames.vo.RoomPlayerVO;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/roomPlayer")
public class RoomPlayerController {

    @Autowired
    private RoomPlayerService roomPlayerService;

    /**
     * 
     * 获取参加的游戏房间列表
     *
     * @param playerId 用户ID
     * @return 房间列表
     */

    @RequestMapping("/getJoinedRooms")
    public Result<List<JoinedGameRoomVO>> getJoinedRooms(@RequestParam("playerId") Long playerId) {
        // 实现获取参加的游戏房间列表的逻辑
        // 可以调用RoomPlayerService的方法来获取房间列表
        List<JoinedGameRoomVO> roomList = roomPlayerService.getJoinedRooms(playerId);
        // 返回房间列表
        return Result.success(roomList);
    }

    /**
     * 
     */
    @RequestMapping("/player")
    public Result<RoomPlayerVO> getJoinedplayer(@RequestParam("roomId") Long roomId,
            @RequestParam("playerId") Long playerId) {
        // 实现获取参加的游戏房间列表的逻辑
        RoomPlayerVO room = roomPlayerService.getJoinedplayer(roomId, playerId);
        // 返回房间列表
        return Result.success(room);

    }

}
