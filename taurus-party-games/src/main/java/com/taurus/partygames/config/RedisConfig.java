package com.taurus.partygames.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

@Configuration("partyGamesRedisConfig")
public class RedisConfig {
    private static final Logger logger = LoggerFactory.getLogger(RedisConfig.class);

    @Value("${spring.redis.host}")
    private String redisHost;

    @Value("${spring.redis.port}")
    private int redisPort;

    @Value("${spring.redis.password}")
    private String redisPassword;

    @Value("${spring.redis.database}")
    private int redisDatabase;

    @Bean
    public RedisConnectionFactory redisConnectionFactory() {
        try {
            RedisStandaloneConfiguration redisConfig = new RedisStandaloneConfiguration();
            redisConfig.setHostName(redisHost);
            redisConfig.setPort(redisPort);
            redisConfig.setPassword(redisPassword);
            redisConfig.setDatabase(redisDatabase);

            JedisConnectionFactory factory = new JedisConnectionFactory(redisConfig);
            factory.afterPropertiesSet();

            // 测试连接
            factory.getConnection().ping();

            logger.info("成功连接到Redis服务器: {}:{}", redisHost, redisPort);
            return factory;
        } catch (Exception e) {
            logger.error("无法连接到Redis服务器: {}:{}, 错误: {}", redisHost, redisPort, e.getMessage());
            logger.info("将使用内存模式运行，所有Redis数据仅在内存中临时存储");

            // 创建一个本地Redis配置，使用本地主机和端口
            RedisStandaloneConfiguration localConfig = new RedisStandaloneConfiguration();
            localConfig.setHostName("localhost");
            localConfig.setPort(6379);

            JedisConnectionFactory localFactory = new JedisConnectionFactory(localConfig);
            localFactory.afterPropertiesSet();
            return localFactory;
        }
    }

    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(redisConnectionFactory);

        // 设置key的序列化方式
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());

        // 设置value的序列化方式
        GenericJackson2JsonRedisSerializer jsonRedisSerializer = new GenericJackson2JsonRedisSerializer();
        redisTemplate.setValueSerializer(jsonRedisSerializer);
        redisTemplate.setHashValueSerializer(jsonRedisSerializer);

        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }
}