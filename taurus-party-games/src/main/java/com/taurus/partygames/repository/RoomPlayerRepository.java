package com.taurus.partygames.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.taurus.partygames.entity.RoomPlayer;

/**
 * 房间玩家仓库接口
 */
@Repository
public interface RoomPlayerRepository extends JpaRepository<RoomPlayer, Long> {

    /**
     * 根据房间ID查找所有玩家
     * 
     * @param roomId 房间ID
     * @return 玩家列表
     */
    List<RoomPlayer> findByRoomId(Long roomId);

    /**
     * 检查房间中是否存在该玩家
     * 
     * @param roomId   房间ID
     * @param playerId 玩家ID
     * @return 是否存在
     */
    boolean existsByRoomIdAndPlayerId(Long roomId, String playerId);

    /**
     * 根据房间码删除所有玩家
     * 
     * @param roomId 房间ID
     */
    void deleteByRoomId(Long roomId);

    /**
     * 根据房间ID和玩家ID查找玩家
     * 
     * @param roomId   房间ID
     * @param playerId 玩家ID
     * @return 玩家对象
     */
    RoomPlayer findByRoomIdAndPlayerId(Long roomId, String playerId);
}