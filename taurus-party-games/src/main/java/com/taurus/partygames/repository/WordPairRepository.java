package com.taurus.partygames.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.taurus.partygames.entity.WordPair;

/**
 * 词对仓库接口
 */
@Repository
public interface WordPairRepository extends JpaRepository<WordPair, Long> {

    /**
     * 根据词库名称查找词对
     * 
     * @param libraryName 词库名称
     * @return 词对列表
     */
    List<WordPair> findByLibraryName(String libraryName);

    /**
     * 随机获取一个词对
     * 
     * @param libraryName 词库名称
     * @return 随机词对
     */
    @Query(value = "SELECT * FROM wordPair WHERE libraryName = :libraryName ORDER BY RAND() LIMIT 1", nativeQuery = true)
    WordPair findRandomWordPair(@Param("libraryName") String libraryName);

    /**
     * 获取所有词库名称
     */
    @Query("SELECT DISTINCT w.libraryName FROM WordPair w")
    List<String> findAllLibraryNames();
}