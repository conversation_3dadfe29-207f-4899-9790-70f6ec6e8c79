package com.taurus.partygames.mapper;

import com.taurus.partygames.entity.WordPair;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 词对Mapper接口
 */
@Repository
public interface WordPairMapper {
    
    /**
     * 插入词对记录
     * @param wordPair 词对对象
     * @return 影响行数
     */
    int insert(WordPair wordPair);
    
    /**
     * 更新词对信息
     * @param wordPair 词对对象
     * @return 影响行数
     */
    int update(WordPair wordPair);
    
    /**
     * 根据词库名称查询词对列表
     * @param libraryName 词库名称
     * @return 词对列表
     */
    List<WordPair> selectByLibraryName(@Param("libraryName") String libraryName);
    
    /**
     * 获取随机词对
     * @param libraryName 词库名称
     * @return 随机词对
     */
    WordPair getRandomWordPair(@Param("libraryName") String libraryName);
    
    /**
     * 查询所有词对
     * @return 词对列表
     */
    List<WordPair> selectAll();
    
    /**
     * 删除词对
     * @param id 词对ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 查询所有词库名称
     * @return 词库名称列表
     */
    List<String> selectAllLibraryNames();
} 