package com.taurus.partygames.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.taurus.partygames.entity.GameRoom;

/**
 * 游戏房间Mapper接口
 */
@Repository
public interface GameRoomMapper {

    /**
     * 插入房间记录
     * 
     * @param gameRoom 房间对象
     * @return 影响行数
     */
    int insert(GameRoom gameRoom);

    /**
     * 更新房间信息
     * 
     * @param gameRoom 房间对象
     * @return 影响行数
     */
    int update(GameRoom gameRoom);

    /**
     * 根据房间编码查询房间
     * 
     * @param roomCode 房间编码
     * @return 房间对象
     */
    GameRoom findByRoomCode(@Param("roomCode") String roomCode);

    /**
     * 根据ID查询房间
     *
     * @param id 房间ID
     * @return 房间对象
     */
    GameRoom findById(@Param("id") Long id);

    /**
     * 查询所有房间
     * 
     * @return 房间列表
     */
    List<GameRoom> findAll();

    /**
     * 根据状态查询房间列表
     * 
     * @param status 房间状态
     * @return 房间列表
     */
    List<GameRoom> findByStatus(@Param("status") String status);

    /**
     * 删除过期房间
     * 
     * @param beforeTime 过期时间点
     * @return 影响行数
     */
    int deleteExpiredRooms(@Param("beforeTime") String beforeTime);
}