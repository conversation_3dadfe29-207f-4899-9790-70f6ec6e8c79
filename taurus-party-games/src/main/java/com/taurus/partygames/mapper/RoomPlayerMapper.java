package com.taurus.partygames.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.taurus.partygames.vo.JoinedGameRoomVO;
import com.taurus.partygames.vo.RoomPlayerVO;

@Mapper
public interface RoomPlayerMapper {
    /**
     * 获取玩家加入的游戏角色列表
     * 
     * @param playerId
     * @return
     */
    List<JoinedGameRoomVO> selectJoinedRoomsByPlayerId(String playerId); // 改为String类型更通用

    /**
     * 获取指定房间的玩家游戏角色
     * 
     * @param roomId
     * @param playerId
     * @return
     */
    RoomPlayerVO selectJoinedplayerByRoomIdAndPlayerId(String roomId, String playerId);
}