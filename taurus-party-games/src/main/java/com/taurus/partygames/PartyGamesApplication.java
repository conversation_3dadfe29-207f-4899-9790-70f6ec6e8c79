package com.taurus.partygames;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 派对游戏服务启动类
 */
@SpringBootApplication
@EnableTransactionManagement
@ComponentScan(basePackages = { "com.taurus.partygames" })
@MapperScan({ "com.taurus.partygames.mapper" })
public class PartyGamesApplication {

    public static void main(String[] args) {
        SpringApplication.run(PartyGamesApplication.class, args);
    }
}