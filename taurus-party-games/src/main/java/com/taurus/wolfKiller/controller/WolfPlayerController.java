package com.taurus.wolfKiller.controller;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.taurus.wolfKiller.VO.WolfRoomGameVO;
import com.taurus.wolfKiller.VO.WolfRoomPlayerVO;
import com.taurus.wolfKiller.common.CommonResult;
import com.taurus.wolfKiller.entity.Player;
import com.taurus.wolfKiller.entity.WolfRoomPlayer;
import com.taurus.wolfKiller.service.PlayerService;
import com.taurus.wolfKiller.service.WolfRoomPlayerService;

/**
 * 狼人杀玩家控制器
 */
@RestController
@RequestMapping("/api/wolf/player")
public class WolfPlayerController {

    @Autowired
    private WolfRoomPlayerService wolfRoomPlayerService;

    @Autowired
    private PlayerService wolfPlayerUserService;

    /**
     * 获取玩家信息
     */
    @GetMapping("/info")
    public CommonResult<WolfRoomPlayer> getPlayerInfo(@RequestParam("roomId") Long roomId,
            @RequestParam("playerId") String playerId) {
        WolfRoomPlayer player = wolfRoomPlayerService.getPlayerInfo(roomId, playerId);
        return CommonResult.success(player);
    }

    /**
     * 获取房间所有玩家
     */
    @GetMapping("/roomPlayers")
    public CommonResult<List<WolfRoomPlayerVO>> getRoomPlayers(@RequestParam("roomId") Long roomId) {
        List<WolfRoomPlayerVO> players = wolfRoomPlayerService.getPlayersByRoomId(roomId);
        return CommonResult.success(players);
    }

    /**
     * 获取玩家参与的所有游戏
     */
    @GetMapping("/joinedRooms")
    public CommonResult<List<WolfRoomGameVO>> getJoinedRooms(@RequestParam("playerId") String playerId) {
        List<WolfRoomGameVO> players = wolfRoomPlayerService.getPlayersByPlayerId(playerId);
        return CommonResult.success(players);
    }

    /**
     * 获取或创建玩家用户
     */
    @PostMapping("/createUser")
    public CommonResult<Player> createUser(@RequestBody Player playerUser) {
        Player user = wolfPlayerUserService.createUser(playerUser);
        return CommonResult.success(user);
    }

    @PostMapping("/update")
    public CommonResult<Player> update(@RequestBody Player playerUser) {
        playerUser.setLastLoginTime(LocalDateTime.now());
        Player user = wolfPlayerUserService.updateUser(playerUser);
        return CommonResult.success(user);
    }
}