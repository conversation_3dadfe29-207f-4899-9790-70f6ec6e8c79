package com.taurus.wolfKiller.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.taurus.wolfKiller.common.CommonResult;
import com.taurus.wolfKiller.entity.WolfRole;
import com.taurus.wolfKiller.service.WolfRoleService;

/**
 * 狼人杀角色控制器
 */
@RestController
@RequestMapping("/api/wolf/role")
public class WolfRoleController {

    @Autowired
    private WolfRoleService wolfRoleService;

    /**
     * 获取所有角色信息
     */
    @GetMapping("/all")
    public CommonResult<List<WolfRole>> getAllRoles() {
        List<WolfRole> roles = wolfRoleService.getAllRoles();
        return CommonResult.success(roles);
    }

    /**
     * 获取特定角色信息
     */
    @GetMapping("/info")
    public CommonResult<WolfRole> getRoleInfo(@RequestParam("roleName") String roleName) {
        WolfRole role = wolfRoleService.getRoleByName(roleName);
        return CommonResult.success(role);
    }
}