package com.taurus.wolfKiller.controller;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.taurus.wolfKiller.VO.WolfRoomVO;
import com.taurus.wolfKiller.common.CommonResult;
import com.taurus.wolfKiller.dto.WolfPlayerJoinRequestDTO;
import com.taurus.wolfKiller.dto.WolfRoomCreateRequest;
import com.taurus.wolfKiller.dto.WolfRoomCreateRequestDTO;
import com.taurus.wolfKiller.entity.WolfRoom;
import com.taurus.wolfKiller.entity.WolfRoomPlayer;
import com.taurus.wolfKiller.entity.WolfRoomRole;
import com.taurus.wolfKiller.service.WolfRoomPlayerService;
import com.taurus.wolfKiller.service.WolfRoomService;

import lombok.extern.slf4j.Slf4j;

/**
 * 狼人杀房间控制器
 */
@RestController
@RequestMapping("/api/wolf/room")
@Slf4j
public class WolfRoomController {

    @Autowired
    private WolfRoomService wolfRoomService;

    @Autowired
    private WolfRoomPlayerService wolfRoomPlayerService;

    /**
     * 创建房间
     */
    @PostMapping("/create")
    public CommonResult<WolfRoom> createRoom(@RequestBody WolfRoomCreateRequestDTO dto) {
        log.info("创建房间请求, dto={}", dto);

        // 将前端DTO转换为后端请求对象
        WolfRoomCreateRequest request = new WolfRoomCreateRequest();
        request.setCreatorId(dto.getCreatorId());
        request.setCreatorName(dto.getCreatorName());
        request.setRoleConfigs(dto.getRoleConfigs());

        WolfRoom room = wolfRoomService.createRoom(request);
        return CommonResult.success(room);
    }

    /**
     * 加入房间
     */
    @PostMapping("/join")
    public CommonResult<WolfRoomPlayer> joinRoom(@RequestBody WolfPlayerJoinRequestDTO request) {
        WolfRoomPlayer player = wolfRoomPlayerService.joinRoom(request);
        return CommonResult.success(player);
    }

    /**
     * 获取房间信息
     */
    @GetMapping("/info")
    public CommonResult<WolfRoom> getRoomInfo(@RequestParam("roomId") Long roomId) {
        WolfRoom room = wolfRoomService.getRoomById(roomId);
        return CommonResult.success(room);
    }

    /**
     * 根据房间码获取房间信息
     */
    @GetMapping("/infoByCode")
    public CommonResult<WolfRoomVO> getRoomInfoByCode(@RequestParam("roomCode") String roomCode) {
        WolfRoom room = wolfRoomService.getRoomByCode(roomCode);
        if (room == null) {
            return CommonResult.success(null);
        }
        WolfRoomVO roomVO = new WolfRoomVO(room);
        roomVO.setIsValid(room.getExpireTime().isAfter(LocalDateTime.now()));
        return CommonResult.success(roomVO);
    }

    /**
     * 获取房间角色配置
     */
    @GetMapping("/roomRoles")
    public CommonResult<List<WolfRoomRole>> getRoomRoles(@RequestParam("roomId") Long roomId) {
        List<WolfRoomRole> roles = wolfRoomService.getRoomRoles(roomId);
        return CommonResult.success(roles);
    }

    /**
     * 重新开局
     */
    @PostMapping("/restart")
    public CommonResult<WolfRoom> restartGame(@RequestParam("roomId") Long roomId) {
        WolfRoom room = wolfRoomService.restartGame(roomId);
        return CommonResult.success(room);
    }

    /**
     * 删除房间
     */
    @PostMapping("/delete")
    public CommonResult<Integer> deleteRoom(@RequestParam("roomId") Long roomId) {
        int result = wolfRoomService.deleteRoom(roomId);
        return CommonResult.success(result);
    }
}
