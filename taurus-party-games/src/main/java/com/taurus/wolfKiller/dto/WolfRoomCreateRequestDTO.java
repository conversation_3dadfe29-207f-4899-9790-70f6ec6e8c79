package com.taurus.wolfKiller.dto;

import java.util.List;

import lombok.Data;

/**
 * 狼人杀创建房间请求DTO（前端参数格式）
 */
@Data
public class WolfRoomCreateRequestDTO {
    /**
     * 创建者ID
     */
    private String creatorId;

    /**
     * 创建者名称
     */
    private String creatorName;

    /**
     * 角色配置列表
     */
    private List<RoleConfig> roleConfigs;

    /**
     * 是否公开
     */
    private Boolean isPublic;

    /**
     * 角色配置
     */
    @Data
    public static class RoleConfig {
        /**
         * 角色名称
         */
        private String roleName;

        /**
         * 角色数量
         */
        private Integer roleCount;
    }
}