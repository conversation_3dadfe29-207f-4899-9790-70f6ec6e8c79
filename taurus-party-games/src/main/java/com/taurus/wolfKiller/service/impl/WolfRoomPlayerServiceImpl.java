package com.taurus.wolfKiller.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.taurus.wolfKiller.VO.WolfRoomGameVO;
import com.taurus.wolfKiller.VO.WolfRoomPlayerVO;
import com.taurus.wolfKiller.dto.WolfPlayerJoinRequestDTO;
import com.taurus.wolfKiller.entity.WolfRoomPlayer;
import com.taurus.wolfKiller.entity.WolfRoomRole;
import com.taurus.wolfKiller.mapper.WolfRoomPlayerMapper;
import com.taurus.wolfKiller.service.WolfRoomPlayerService;
import com.taurus.wolfKiller.service.WolfRoomRoleService;

import lombok.extern.slf4j.Slf4j;

/**
 * 狼人杀房间玩家服务实现类
 */
@Service
@Slf4j
public class WolfRoomPlayerServiceImpl implements WolfRoomPlayerService {

    @Autowired
    private WolfRoomPlayerMapper wolfRoomPlayerMapper;

    @Autowired
    private WolfRoomRoleService wolfRoomRoleService;

    private final ConcurrentHashMap<Long, ReentrantLock> roomLocks = new ConcurrentHashMap<>();

    private ReentrantLock getLockForRoom(Long roomId) {
        return roomLocks.computeIfAbsent(roomId, k -> new ReentrantLock());
    }

    @Override
    public WolfRoomPlayer joinRoom(WolfPlayerJoinRequestDTO request) {
        // 检查玩家是否已经在房间中
        WolfRoomPlayer existPlayer = wolfRoomPlayerMapper.selectByRoomIdAndPlayerId(request.getRoomId(),
                request.getPlayerId());
        if (existPlayer != null) {
            log.info("玩家已在房间中，直接返回玩家信息, roomId={}, playerId={}", request.getRoomId(), request.getPlayerId());
            return existPlayer;
        }

        WolfRoomPlayer player = null;
        ReentrantLock lock = getLockForRoom(request.getRoomId());

        lock.lock();
        try {
            player = this.getRandomPlayerRole(request.getRoomId(), request.getPlayerId());
        } finally {
            lock.unlock();
        }

        if (player == null) {
            log.warn("获取玩家角色失败, roomId={}, playerId={}", request.getRoomId(), request.getPlayerId());
            return null;
        }

        log.info("玩家成功加入房间, roomId={}, playerId={}, playerIndex={}",
                request.getRoomId(), request.getPlayerId(), player.getPlayerIndex());

        return player;
    }

    /**
     * 随机获取一个玩家角色
     * 
     * @param roomId
     * @param playerId
     * @return
     */
    public WolfRoomPlayer getRandomPlayerRole(Long roomId, String playerId) {
        // 获取各角色数量
        List<WolfRoomRole> roles = wolfRoomRoleService.getRoomRoles(roomId);
        // 获取已分配的角色数量
        List<WolfRoomPlayerVO> players = wolfRoomPlayerMapper.selectByRoomId(roomId);

        // 计算每个角色已分配的数量
        Map<String, Long> assignedRoleCounts = players.stream()
                .collect(Collectors.groupingBy(WolfRoomPlayerVO::getRoleName, Collectors.counting()));

        // 剩余可分配的角色（考虑roleCount限制）
        List<WolfRoomRole> availableRoles = roles.stream()
                .filter(role -> {
                    // 获取该角色已分配的数量，默认为0
                    Long assignedCount = assignedRoleCounts.getOrDefault(role.getRoleName(), 0L);
                    // 只有当已分配数量小于允许的最大数量时，该角色才可用
                    return assignedCount < role.getRoleCount();
                })
                .collect(Collectors.toList());

        // 如果剩余可用角色为空，则返回null
        if (availableRoles == null || availableRoles.isEmpty()) {
            log.warn("没有可用角色, roomId={}", roomId);
            return null;
        }

        // 从可用角色中随机获取一个
        WolfRoomRole selectedRole = availableRoles.get(new Random().nextInt(availableRoles.size()));

        // 创建玩家记录
        WolfRoomPlayer roomPlayer = WolfRoomPlayer.builder()
                .roomId(roomId)
                .playerId(playerId)
                .playerIndex(players.size() + 1)
                .joinTime(LocalDateTime.now())
                .roleName(selectedRole.getRoleName())
                .build();

        wolfRoomPlayerMapper.insert(roomPlayer);

        return roomPlayer;
    }

    @Override
    public WolfRoomPlayer getPlayerInfo(Long roomId, String playerId) {
        return wolfRoomPlayerMapper.selectByRoomIdAndPlayerId(roomId, playerId);
    }

    @Override
    public List<WolfRoomPlayerVO> getPlayersByRoomId(Long roomId) {
        return wolfRoomPlayerMapper.selectByRoomId(roomId);
    }

    @Override
    public List<WolfRoomGameVO> getPlayersByPlayerId(String playerId) {
        return wolfRoomPlayerMapper.selectByPlayerId(playerId);
    }
}