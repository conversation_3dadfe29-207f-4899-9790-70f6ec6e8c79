package com.taurus.wolfKiller.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.taurus.wolfKiller.entity.WolfRoomRole;
import com.taurus.wolfKiller.mapper.WolfRoomRoleMapper;
import com.taurus.wolfKiller.service.WolfRoomRoleService;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class WolfRoomRoleServiceImpl implements WolfRoomRoleService {

    @Autowired
    private WolfRoomRoleMapper wolfRoomRoleMapper;

    /**
     * 获取房间角色配置
     * 
     * @param roomId 房间ID
     * @return 角色配置列表
     */
    @Override
    public List<WolfRoomRole> getRoomRoles(Long roomId) {
        return wolfRoomRoleMapper.selectByRoomId(roomId);
    }

}
