package com.taurus.wolfKiller.service.impl;

import java.time.LocalDateTime;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.taurus.wolfKiller.entity.Player;
import com.taurus.wolfKiller.mapper.PlayerMapper;
import com.taurus.wolfKiller.service.PlayerService;

import lombok.extern.slf4j.Slf4j;

/**
 * 狼人杀玩家用户服务实现类
 */
@Service
@Slf4j
public class PlayerServiceImpl implements PlayerService {

    @Autowired
    private PlayerMapper playerMapper;

    @Override
    public Player createUser(Player playerUser) {

        // 设置默认值
        if (playerUser.getNickname() == null || playerUser.getNickname().isEmpty()) {
            playerUser.setNickname("");
        }
        if (playerUser.getAvatarUrl() == null || playerUser.getAvatarUrl().isEmpty()) {
            playerUser.setAvatarUrl("");
        }

        // 设置最后登录时间
        playerUser.setLastLoginTime(LocalDateTime.now());

        // 创建新用户
        playerMapper.insert(playerUser);
        log.info("创建新玩家用户, playerId={}, nickname={}", playerUser.getPlayerId(), playerUser.getNickname());

        return playerUser;
    }

    @Override
    public Player getUserByPlayerId(String playerId) {
        return playerMapper.selectByPlayerId(playerId);
    }

    @Override
    public Player updateUser(Player playerUser) {
        // 验证用户是否存在
        Player existUser = playerMapper.selectByPlayerId(playerUser.getPlayerId());
        if (existUser == null) {
            log.warn("尝试更新不存在的用户, playerId={}", playerUser.getPlayerId());
            return null;
        }

        // 设置ID
        playerUser.setPlayerId(existUser.getPlayerId());

        // 更新用户
        int result = playerMapper.update(playerUser);
        if (result > 0) {
            log.info("更新玩家用户成功, playerId={}", playerUser.getPlayerId());
            return playerUser;
        } else {
            log.warn("更新玩家用户失败, playerId={}", playerUser.getPlayerId());
            return null;
        }
    }

    @Override
    public Player getPlayerByOpenid(String openid) {
        return playerMapper.selectByOpenid(openid);
    }

}