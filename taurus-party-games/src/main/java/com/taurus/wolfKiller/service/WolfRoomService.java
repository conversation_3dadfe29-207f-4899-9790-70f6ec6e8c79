package com.taurus.wolfKiller.service;

import java.util.List;

import com.taurus.wolfKiller.dto.WolfRoomCreateRequest;
import com.taurus.wolfKiller.entity.WolfRoom;
import com.taurus.wolfKiller.entity.WolfRoomRole;

/**
 * 狼人杀房间服务接口
 */
public interface WolfRoomService {

    /**
     * 创建房间
     * 
     * @param request 创建房间请求
     * @return 房间信息
     */
    WolfRoom createRoom(WolfRoomCreateRequest request);

    /**
     * 根据ID获取房间
     * 
     * @param roomId 房间ID
     * @return 房间信息
     */
    WolfRoom getRoomById(Long roomId);

    /**
     * 根据房间码获取房间
     * 
     * @param roomCode 房间码
     * @return 房间信息
     */
    WolfRoom getRoomByCode(String roomCode);

    /**
     * 获取房间角色配置
     * 
     * @param roomId 房间ID
     * @return 角色配置列表
     */
    List<WolfRoomRole> getRoomRoles(Long roomId);

    /**
     * 重新开局
     * 
     * @param roomId 房间ID
     * @return 更新后的房间信息
     */
    WolfRoom restartGame(Long roomId);

    /**
     * 删除房间
     * 
     * @param roomId 房间ID
     * @return 影响行数
     */
    int deleteRoom(Long roomId);
}