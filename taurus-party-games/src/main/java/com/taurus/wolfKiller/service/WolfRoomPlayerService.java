package com.taurus.wolfKiller.service;

import java.util.List;

import com.taurus.wolfKiller.VO.WolfRoomGameVO;
import com.taurus.wolfKiller.VO.WolfRoomPlayerVO;
import com.taurus.wolfKiller.dto.WolfPlayerJoinRequestDTO;
import com.taurus.wolfKiller.entity.WolfRoomPlayer;

/**
 * 狼人杀房间玩家服务接口
 */
public interface WolfRoomPlayerService {

    /**
     * 加入房间
     * 
     * @param request 加入房间请求
     * @return 玩家信息
     */
    WolfRoomPlayer joinRoom(WolfPlayerJoinRequestDTO request);

    /**
     * 获取玩家信息
     * 
     * @param roomId   房间ID
     * @param playerId 玩家标识
     * @return 玩家信息
     */
    WolfRoomPlayer getPlayerInfo(Long roomId, String playerId);

    /**
     * 根据房间ID获取所有玩家
     * 
     * @param roomId 房间ID
     * @return 玩家列表
     */
    List<WolfRoomPlayerVO> getPlayersByRoomId(Long roomId);

    /**
     * 根据玩家ID获取参与的所有房间的玩家信息
     * 
     * @param playerId 玩家标识
     * @return 玩家参与的房间列表
     */
    List<WolfRoomGameVO> getPlayersByPlayerId(String playerId);
}