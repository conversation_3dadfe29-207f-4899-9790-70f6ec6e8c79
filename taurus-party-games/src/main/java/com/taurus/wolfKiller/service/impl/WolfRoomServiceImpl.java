package com.taurus.wolfKiller.service.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.taurus.wolfKiller.VO.WolfRoomPlayerVO;
import com.taurus.wolfKiller.dto.WolfRoomCreateRequest;
import com.taurus.wolfKiller.entity.WolfRoom;
import com.taurus.wolfKiller.entity.WolfRoomRole;
import com.taurus.wolfKiller.mapper.WolfRoomMapper;
import com.taurus.wolfKiller.mapper.WolfRoomPlayerMapper;
import com.taurus.wolfKiller.mapper.WolfRoomRoleMapper;
import com.taurus.wolfKiller.service.WolfRoomService;

import lombok.extern.slf4j.Slf4j;

/**
 * 狼人杀房间服务实现类
 */
@Service
@Slf4j
public class WolfRoomServiceImpl implements WolfRoomService {

    @Autowired
    private WolfRoomMapper wolfRoomMapper;

    @Autowired
    private WolfRoomRoleMapper wolfRoomRoleMapper;

    @Autowired
    private WolfRoomPlayerMapper wolfRoomPlayerMapper;

    /**
     * 创建房间
     */
    @Override
    @Transactional
    public WolfRoom createRoom(WolfRoomCreateRequest request) {
        // 生成唯一的6位房间码
        String roomCode = generateRoomCode();

        // 计算总玩家数
        int totalPlayers = request.getRoleConfigs().stream().mapToInt(config -> config.getRoleCount()).sum();

        // 设置过期时间为10分钟后
        LocalDateTime expireTime = LocalDateTime.now().plusMinutes(10);

        // 创建房间
        WolfRoom room = WolfRoom.builder()
                .roomCode(roomCode)
                .creatorId(request.getCreatorId())
                .creatorName(request.getCreatorName())
                .totalPlayers(totalPlayers)
                .expireTime(expireTime)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        wolfRoomMapper.insert(room);
        log.info("创建狼人杀房间成功, roomId={}, roomCode={}, totalPlayers={}", room.getId(), room.getRoomCode(),
                room.getTotalPlayers());

        // 保存角色配置
        this.saveRoleConfig(room.getId(), request);

        return room;
    }

    @Override
    public WolfRoom getRoomById(Long roomId) {
        return wolfRoomMapper.selectById(roomId);
    }

    /**
     * 根据房间码获取房间
     */
    @Override
    public WolfRoom getRoomByCode(String roomCode) {
        return wolfRoomMapper.selectByRoomCode(roomCode);
    }

    /**
     * 根据房间ID获取角色配置
     */
    @Override
    public List<WolfRoomRole> getRoomRoles(Long roomId) {
        return wolfRoomRoleMapper.selectByRoomId(roomId);
    }

    /**
     * 重新开局
     */
    @Override
    @Transactional
    public WolfRoom restartGame(Long roomId) {
        // 获取当前房间信息
        WolfRoom room = wolfRoomMapper.selectById(roomId);
        if (room == null) {
            log.warn("房间不存在, roomId={}", roomId);
            return null;
        }

        // 更新过期时间
        room.setExpireTime(LocalDateTime.now().plusMinutes(10));
        wolfRoomMapper.updateExpireTime(room);

        // 清空已分配的玩家
        wolfRoomPlayerMapper.deleteByRoomId(roomId);

        log.info("重新开局成功, roomId={}", roomId);

        return room;
    }

    /**
     * 保存角色配置
     */
    private void saveRoleConfig(Long roomId, WolfRoomCreateRequest request) {

        for (com.taurus.wolfKiller.dto.WolfRoomCreateRequestDTO.RoleConfig config : request.getRoleConfigs()) {
            WolfRoomRole role = WolfRoomRole.builder()
                    .roomId(roomId)
                    .roleName(config.getRoleName().toUpperCase())
                    .roleCount(config.getRoleCount())
                    .build();
            wolfRoomRoleMapper.insert(role);
        }
    }

    /**
     * 随机分配角色给玩家
     */
    public void assignRolesToPlayers(Long roomId) {
        // 获取角色配置
        List<WolfRoomRole> roles = wolfRoomRoleMapper.selectByRoomId(roomId);

        // 获取所有玩家
        List<WolfRoomPlayerVO> players = wolfRoomPlayerMapper.selectByRoomId(roomId);
        // 创建角色池
        List<String> rolePool = new ArrayList<>();

        // 将每种角色按数量加入角色池
        for (WolfRoomRole role : roles) {
            for (int i = 0; i < role.getRoleCount(); i++) {
                rolePool.add(role.getRoleName());
            }
        }

        // 随机打乱角色池
        Random random = new Random();
        for (int i = rolePool.size() - 1; i > 0; i--) {
            int index = random.nextInt(i + 1);
            String temp = rolePool.get(index);
            rolePool.set(index, rolePool.get(i));
            rolePool.set(i, temp);
        }

        // 分配角色给玩家
        for (int i = 0; i < players.size() && i < rolePool.size(); i++) {
            WolfRoomPlayerVO player = players.get(i);
            player.setRoleName(rolePool.get(i));
            // 更新玩家角色
            wolfRoomPlayerMapper.updateRoleName(player);
            log.info("分配角色, roomId={}, playerId={}, roleName={}", player.getRoomId(), player.getPlayerId(),
                    player.getRoleName());
        }
    }

    /**
     * 生成6位随机房间码
     */
    private String generateRoomCode() {
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        String chars = "1234567890"; // 去掉了容易混淆的字符

        for (int i = 0; i < 6; i++) {
            int index = random.nextInt(chars.length());
            sb.append(chars.charAt(index));
        }

        return sb.toString();
    }

    /**
     * 删除房间
     */
    @Override
    @Transactional
    public int deleteRoom(Long roomId) {
        // 删除房间
        wolfRoomMapper.deleteById(roomId);

        // 删除房间角色
        wolfRoomRoleMapper.deleteByRoomId(roomId);

        // 删除房间玩家
        wolfRoomPlayerMapper.deleteByRoomId(roomId);
        return 1;
    }
}