package com.taurus.wolfKiller.service;

import com.taurus.wolfKiller.entity.Player;

/**
 * 狼人杀玩家用户服务接口
 */
public interface PlayerService {

    /**
     * 获取或创建玩家用户
     * 
     * @param playerUser 玩家用户信息
     * @return 玩家用户
     */
    Player createUser(Player playerUser);

    /**
     * 根据玩家ID获取用户信息
     * 
     * @param playerId 玩家标识
     * @return 玩家用户
     */
    Player getUserByPlayerId(String playerId);

    /**
     * 更新玩家信息
     * 
     * @param playerUser 玩家用户信息
     * @return 更新后的玩家用户
     */
    Player updateUser(Player playerUser);

    /**
     * 根据微信openid获取玩家信息
     * 
     * @param openid 微信openid
     * @return 玩家信息
     */
    Player getPlayerByOpenid(String openid);
}