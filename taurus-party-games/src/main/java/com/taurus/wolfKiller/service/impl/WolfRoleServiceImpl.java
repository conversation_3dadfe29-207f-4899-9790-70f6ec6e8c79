package com.taurus.wolfKiller.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.taurus.wolfKiller.entity.WolfRole;
import com.taurus.wolfKiller.mapper.WolfRoleMapper;
import com.taurus.wolfKiller.service.WolfRoleService;

import lombok.extern.slf4j.Slf4j;

/**
 * 狼人杀角色服务实现类
 */
@Service
@Slf4j
public class WolfRoleServiceImpl implements WolfRoleService {

    @Autowired
    private WolfRoleMapper wolfRoleMapper;

    @Override
    public List<WolfRole> getAllRoles() {
        log.info("获取所有角色信息");
        return wolfRoleMapper.selectAll();
    }

    @Override
    public WolfRole getRoleByName(String roleName) {
        log.info("根据名称获取角色信息, roleName={}", roleName);
        return wolfRoleMapper.selectByRoleName(roleName);
    }
}