package com.taurus.wolfKiller.VO;

import java.time.LocalDateTime;

import com.taurus.wolfKiller.entity.WolfRoomPlayer;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class WolfRoomPlayerVO extends WolfRoomPlayer {

    /**
     * 房间ID
     */
    private Long roomId;

    /**
     * 房间码
     */
    private String roomCode;

    /**
     * 玩家ID
     */
    private String playerId;

    /**
     * 玩家名称
     */
    private String playerName;

    /**
     * 头像
     * 
     */
    private String avatarUrl;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 总玩家数量
     */
    private Integer totalPlayers;

    /**
     * 创建者ID
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
