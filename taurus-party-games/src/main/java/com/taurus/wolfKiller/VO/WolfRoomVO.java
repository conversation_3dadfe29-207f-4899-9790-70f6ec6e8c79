package com.taurus.wolfKiller.VO;

import java.time.LocalDateTime;

import com.taurus.wolfKiller.entity.WolfRoom;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class WolfRoomVO extends WolfRoom {
    /**
     * 房间ID
     */
    private Long id;

    /**
     * 房间码
     */
    private String roomCode;

    /**
     * 创建者ID
     */
    private String creatorId;

    /**
     * 创建者名称
     */
    private String creatorName;

    /**
     * 总玩家数量
     */
    private Integer totalPlayers;

    /**
     * 房间过期时间
     */
    private LocalDateTime expireTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 房间是否有效
     */
    private Boolean isValid;

    public WolfRoomVO(WolfRoom room) {
        this.id = room.getId();
        this.roomCode = room.getRoomCode();
        this.creatorId = room.getCreatorId();
        this.creatorName = room.getCreatorName();
        this.totalPlayers = room.getTotalPlayers();
        this.expireTime = room.getExpireTime();
        this.createTime = room.getCreateTime();
    }

}