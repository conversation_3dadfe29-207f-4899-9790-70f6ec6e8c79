package com.taurus.wolfKiller.entity;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 狼人杀房间玩家实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WolfRoomPlayer {
    /**
     * 玩家标识（主键）
     */
    private String playerId;

    /**
     * 房间ID
     */
    private Long roomId;

    /**
     * 玩家序号
     */
    private Integer playerIndex;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 加入时间
     */
    private LocalDateTime joinTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}