package com.taurus.wolfKiller.entity;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 狼人杀房间角色实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WolfRoomRole {
    /**
     * ID
     */
    private Long id;

    /**
     * 房间ID
     */
    private Long roomId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色数量
     */
    private Integer roleCount;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}