package com.taurus.wolfKiller.entity;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 狼人杀房间实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WolfRoom {
    /**
     * 房间ID
     */
    private Long id;

    /**
     * 房间码
     */
    private String roomCode;

    /**
     * 创建者ID
     */
    private String creatorId;

    /**
     * 创建者名称
     */
    private String creatorName;

    /**
     * 总玩家数量
     */
    private Integer totalPlayers;

    /**
     * 房间过期时间
     */
    private LocalDateTime expireTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}