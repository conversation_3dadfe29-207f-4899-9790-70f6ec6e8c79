package com.taurus.wolfKiller.entity;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 狼人杀角色实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WolfRole {
    /**
     * 角色ID
     */
    private Long id;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色描述
     */
    private String roleDescription;

    /**
     * 角色能力
     */
    private String roleAbilities;

    /**
     * 游戏技巧
     */
    private String roleTips;

    /**
     * 角色图片地址
     */
    private String roleImage;

    /**
     * 角色阵营: werewolf-狼人，villager-村民，third-第三方
     */
    private String roleType;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}