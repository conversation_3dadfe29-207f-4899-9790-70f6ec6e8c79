package com.taurus.wolfKiller.entity;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 狼人杀玩家用户实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Player {
    /**
     * 玩家标识
     */
    private String playerId;

    /**
     * 玩家昵称
     */
    private String nickname;

    /**
     * 头像URL
     */
    private String avatarUrl;

    /**
     * 微信openid
     */
    private String openid;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}