package com.taurus.wolfKiller.mapper;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.taurus.wolfKiller.entity.WolfRoom;

/**
 * 狼人杀房间Mapper接口
 */
@Mapper
public interface WolfRoomMapper {

    /**
     * 添加房间
     * 
     * @param room 房间信息
     * @return 影响行数
     */
    @Insert("INSERT INTO wolf_room(room_code, creator_id, creator_name, total_players, expire_time, create_time, update_time) "
            +
            "VALUES(#{roomCode}, #{creatorId}, #{creatorName}, #{totalPlayers}, #{expireTime}, now(), now())")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(WolfRoom room);

    /**
     * 根据ID获取房间
     * 
     * @param id 房间ID
     * @return 房间信息
     */
    @Select("SELECT * FROM wolf_room WHERE id = #{id}")
    WolfRoom selectById(Long id);

    /**
     * 根据房间码获取房间
     * 
     * @param roomCode 房间码
     * @return 房间信息
     */
    @Select("SELECT * FROM wolf_room  WHERE room_code = #{roomCode} order by id desc limit 1 ")
    WolfRoom selectByRoomCode(String roomCode);

    /**
     * 更新房间过期时间
     * 
     * @param room 房间信息
     * @return 影响行数
     */
    @Update("UPDATE wolf_room SET expire_time = #{expireTime}, update_time = now() WHERE id = #{id}")
    int updateExpireTime(WolfRoom room);

    /**
     * 删除房间
     * 
     * @param id 房间ID
     * @return 影响行数
     */
    @Delete("DELETE FROM wolf_room WHERE id = #{id}")
    int deleteById(Long id);
}