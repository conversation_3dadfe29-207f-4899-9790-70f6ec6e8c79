package com.taurus.wolfKiller.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import com.taurus.wolfKiller.entity.WolfRole;

/**
 * 狼人杀角色Mapper接口
 */
@Mapper
public interface WolfRoleMapper {

    /**
     * 获取所有角色信息
     * 
     * @return 角色列表
     */
    @Select("SELECT * FROM wolf_role ORDER BY sort_order ASC")
    List<WolfRole> selectAll();

    /**
     * 根据角色名称获取角色信息
     * 
     * @param roleName 角色名称
     * @return 角色信息
     */
    @Select("SELECT * FROM wolf_role WHERE role_name = #{roleName}")
    WolfRole selectByRoleName(String roleName);
}