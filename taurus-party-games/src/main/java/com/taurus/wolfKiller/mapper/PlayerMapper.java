package com.taurus.wolfKiller.mapper;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.taurus.wolfKiller.entity.Player;

/**
 * 狼人杀玩家用户Mapper接口
 */
@Mapper
public interface PlayerMapper {

        /**
         * 添加玩家用户
         * 
         * @param user 用户信息
         * @return 影响行数
         */
        @Insert("INSERT INTO player(player_id, nickname,openid, avatar_url, last_login_time, create_time, update_time) "
                        +
                        "VALUES(#{playerId}, #{nickname}, #{openid}, #{avatarUrl}, #{lastLoginTime}, now(), now())")
        @Options(useGeneratedKeys = true, keyProperty = "playerId")
        int insert(Player user);

        /**
         * 根据玩家ID获取用户
         * 
         * @param playerId 玩家ID
         * @return 用户信息
         */
        @Select("SELECT * FROM player WHERE player_id = #{playerId}")
        Player selectByPlayerId(String playerId);

        /**
         * 更新用户信息
         * 
         * @param user 用户信息
         * @return 影响行数
         */
        @Update("UPDATE player SET nickname = #{nickname}, avatar_url = #{avatarUrl}, " +
                        "last_login_time = #{lastLoginTime}, update_time = now() WHERE player_id = #{playerId}")
        int update(Player user);

        /**
         * 根据微信openid获取玩家信息
         * 
         * @param openid 微信openid
         * @return 玩家信息
         */
        @Select("SELECT * FROM player WHERE openid = #{openid} limit 1")
        Player selectByOpenid(String openid);
}