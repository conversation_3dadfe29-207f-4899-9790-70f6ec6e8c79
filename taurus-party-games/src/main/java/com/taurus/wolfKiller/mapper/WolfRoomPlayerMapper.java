package com.taurus.wolfKiller.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.taurus.wolfKiller.VO.WolfRoomGameVO;
import com.taurus.wolfKiller.VO.WolfRoomPlayerVO;
import com.taurus.wolfKiller.entity.WolfRoomPlayer;

/**
 * 狼人杀房间玩家Mapper接口
 */
@Mapper
public interface WolfRoomPlayerMapper {

        /**
         * 添加房间玩家
         * 
         * @param player 玩家信息
         * @return 影响行数
         */
        @Insert("INSERT INTO wolf_room_player(room_id, player_id, player_index, role_name, join_time, create_time, update_time) "
                        +
                        "VALUES(#{roomId}, #{playerId}, #{playerIndex}, #{roleName}, #{joinTime}, now(), now())")
        int insert(WolfRoomPlayer player);

        /**
         * 根据房间ID和玩家ID获取玩家信息
         * 
         * @param roomId   房间ID
         * @param playerId 玩家ID
         * @return 玩家信息
         */
        @Select("SELECT *,t1.nickname as playerName FROM wolf_room_player t left join player t1 ON t.player_id=t1.player_id WHERE t.room_id = #{roomId} AND t.player_id = #{playerId}")
        WolfRoomPlayer selectByRoomIdAndPlayerId(@Param("roomId") Long roomId, @Param("playerId") String playerId);

        /**
         * 根据房间ID获取所有玩家
         * 
         * @param roomId 房间ID
         * @return 玩家列表
         */
        @Select("SELECT t.*,r.creator_id,p.avatar_url,p.nickname as playerName FROM wolf_room_player t LEFT JOIN wolf_room r ON t.room_id = r.id left join player p ON p.player_id=t.player_id WHERE t.room_id = #{roomId} ORDER BY r.id desc")
        List<WolfRoomPlayerVO> selectByRoomId(Long roomId);

        /**
         * 根据玩家ID获取参与的所有房间的玩家信息
         * 
         * @param playerId 玩家ID
         * @return 玩家列表
         */
        @Select("SELECT t.player_id,t.role_name,r.room_code,r.total_players,r.create_time,r.creator_id,r.id as room_id,p.nickname as playerName FROM wolf_room_player t right join wolf_room r on t.room_id = r.id and  t.player_id=#{playerId} left join player p ON p.player_id=t.player_id WHERE t.player_id = #{playerId} or  r.creator_id = #{playerId} order by r.id desc limit 5")
        List<WolfRoomGameVO> selectByPlayerId(String playerId);

        /**
         * 更新玩家角色
         * 
         * @param player 玩家信息
         * @return 影响行数
         */
        @Insert("UPDATE wolf_room_player SET role_name = #{roleName}, update_time = now() " +
                        "WHERE room_id = #{roomId} AND player_id = #{playerId}")
        int updateRoleName(WolfRoomPlayer player);

        /**
         * 根据房间ID删除玩家
         * 
         * @param roomId 房间ID
         * @return 影响行数
         */
        @Delete("DELETE FROM wolf_room_player WHERE room_id = #{roomId}")
        int deleteByRoomId(Long roomId);
}