package com.taurus.wolfKiller.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;

import com.taurus.wolfKiller.entity.WolfRoomRole;

/**
 * 狼人杀房间角色Mapper接口
 */
@Mapper
public interface WolfRoomRoleMapper {

    /**
     * 添加房间角色
     * 
     * @param roomRole 房间角色信息
     * @return 影响行数
     */
    @Insert("INSERT INTO wolf_room_role(room_id, role_name, role_count, create_time, update_time) " +
            "VALUES(#{roomId}, #{roleName}, #{roleCount}, now(), now())")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(WolfRoomRole roomRole);

    /**
     * 根据房间ID获取角色配置
     * 
     * @param roomId 房间ID
     * @return 角色配置列表
     */
    @Select("SELECT * FROM wolf_room_role WHERE room_id = #{roomId}")
    List<WolfRoomRole> selectByRoomId(Long roomId);

    /**
     * 根据房间ID删除角色配置
     * 
     * @param roomId 房间ID
     * @return 影响行数
     */
    @Delete("DELETE FROM wolf_room_role WHERE room_id = #{roomId}")
    int deleteByRoomId(Long roomId);
}