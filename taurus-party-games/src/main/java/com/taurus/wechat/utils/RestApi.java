package com.taurus.wechat.utils;

/**
 * 微信api列表
 * 
 * <AUTHOR>
 *
 */
public class RestApi {

	// code换取session
	public static final String jscode2sessionUrl = "https://api.weixin.qq.com/sns/jscode2session";

	// 获取accessToken
	public static final String accessTokenUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=APPID&secret=APPSECRET";

	// 获取小程序码（无数量限制）
	public static final String qrCodeUnlimitedUrl = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=ACCESS_TOKEN";

	// 获取小程序码（数量有限）
	public static final String qrCodeLimitedUrl = "https://api.weixin.qq.com/wxa/getwxacode?access_token=ACCESS_TOKEN";

	// 违规文字检测接口
	public static final String msgSecCheckUrl = "https://api.weixin.qq.com/wxa/msg_sec_check?access_token=ACCESS_TOKEN";

	// 图片检测接口
	public static final String imgSecCheckUrl = "https://api.weixin.qq.com/wxa/img_sec_check?access_token=ACCESS_TOKEN";

	// 网页授权accessToken
	public static final String OAUTH2_ACCESS_TOKEN = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=APPID&secret=APPSECRET&code=CODE&grant_type=authorization_code";

	public static final String JSAPI_TICKET_URL = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=ACCESS_TOKEN&type=jsapi";
	public static final String WECHAT_PUBLIC_GET_TEMPORARY_TICKET = "https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=##TOKEN##";
	public static final String USER_INFO_URL = "https://api.weixin.qq.com/cgi-bin/user/info?access_token=##ACCESS_TOKEN##&openid=##OPENID##&lang=zh_CN";
	public static final String CREATE_MENU_URL = "https://api.weixin.qq.com/cgi-bin/menu/create?access_token=##ACCESS_TOKEN##";
	public static final String SEND_TEMPLATE_MESSAGE = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=##ACCESS_TOKEN##";
}
