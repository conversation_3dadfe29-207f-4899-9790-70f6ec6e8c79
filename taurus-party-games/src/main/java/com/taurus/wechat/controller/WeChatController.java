package com.taurus.wechat.controller;

import java.io.IOException;
import java.time.LocalDateTime;

import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.taurus.partygames.vo.Result;
import com.taurus.wechat.DTO.QrCodeDTO;
import com.taurus.wechat.pojo.Session;
import com.taurus.wechat.service.WeChatService;
import com.taurus.wolfKiller.entity.Player;
import com.taurus.wolfKiller.service.PlayerService;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/api/wechat")
@Slf4j
public class WeChatController {

    @Autowired
    private PlayerService playerService;

    @Autowired
    private WeChatService weChatService;

    /**
     * 微信登录
     * 
     * @param code
     * @param request
     * @return
     * @throws IOException
     */
    @GetMapping("/login")
    public Result<Player> loginWithCode(
            @RequestParam(required = true) String code) throws IOException {
        if (StringUtils.isEmpty(code)) {
            return Result.error(1, "code不能为空");
        }
        Session session = weChatService.getSession(code);

        if (session == null) {
            return Result.error(1, "session为空");
        }

        Player player = playerService.getPlayerByOpenid(session.getOpenid());
        if (player == null) {
            Player playerUser = new Player();
            playerUser.setOpenid(session.getOpenid());
            player = playerService.createUser(playerUser);
        } else {
            // 更新登录时间
            player.setLastLoginTime(LocalDateTime.now());
            // 如果提供了新的昵称或头像URL则更新
            if (player.getNickname() != null && !player.getNickname().isEmpty()) {
                player.setNickname(player.getNickname());
            }
            if (player.getAvatarUrl() != null && !player.getAvatarUrl().isEmpty()) {
                player.setAvatarUrl(player.getAvatarUrl());
            }
            playerService.updateUser(player);
        }

        return Result.success(player);
    }

    /**
     * 获取二维码
     * 
     * @param dto
     * @return
     */
    @PostMapping("/getQRCodeBase64")
    public Result<String> getQRCodeBase64(@RequestBody QrCodeDTO dto) {

        String scene = dto.getScene();// urlencode 处理，请使用其他编码方式）
        String page = dto.getPage();
        Integer width = dto.getWidth();

        if (StringUtils.isEmpty(scene) || StringUtils.isEmpty(page) || width == null) {
            return Result.error(1, "参数不能为空");
        }

        String result = weChatService.getBase64QRCode(dto);

        if (StringUtils.isEmpty(result)) {
            return Result.error(1, "获取二维码失败");
        }

        String resStr = null;
        try {
            log.debug(result);
            JSONObject.parseObject(result);
        } catch (JSONException e) {
            byte[] array = result.getBytes();
            String base64Str = Base64.encodeBase64String(array);
            resStr = "data:image/png;base64," + base64Str;
        }

        return Result.success(resStr);

    }

}
