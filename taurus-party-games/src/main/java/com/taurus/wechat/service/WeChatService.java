package com.taurus.wechat.service;

import java.io.IOException;
import java.util.Collections;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.gitee.fastmybatis.core.util.MyBeanUtil;
import com.taurus.partygames.service.RedisService;
import com.taurus.wechat.DTO.QrCodeDTO;
import com.taurus.wechat.pojo.Session;
import com.taurus.wechat.utils.RestApi;

import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class WeChatService {

    private static final String appId = "wx8e0cdf5683fd6b16";
    private static final String appSecret = "58ebf014a35413936a835c4c293fa120";

    @Autowired
    private RedisService redisService;

    /**
     * 获取Session对象
     * 
     * @param code
     * @param account
     * @return
     */
    public Session getSession(String code) {

        String url = RestApi.jscode2sessionUrl + "?appid=" + appId + "&secret=" + appSecret + "&js_code=" + code
                + "&grant_type=authorization_code";

        String result = HttpUtil.get(url, Collections.emptyMap());

        JSONObject sessionJson = JSON.parseObject(result, JSONObject.class);
        String session_key = sessionJson.getString("session_key");// 会话密钥
        String openid = sessionJson.getString("openid");// openid
        String unionid = sessionJson.getString("unionid");

        Session session = new Session();
        session.setSession_key(session_key);
        session.setOpenid(openid);
        session.setUnionid(unionid);

        return session;

    }

    /**
     * 获取小程序码
     * 
     * @param dto
     * @return
     * @throws IOException
     */

    public String getBase64QRCode(QrCodeDTO dto) {
        String accessToken = null;
        try {
            accessToken = getAccessToken();
        } catch (Exception e) {
            log.error("获取小程序码失败", e);
            return null;
        }

        String url = RestApi.qrCodeUnlimitedUrl.replace("ACCESS_TOKEN", accessToken);
        Map<String, Object> paramMap = MyBeanUtil.pojoToMap(dto);
        String result = HttpUtil.post(url, paramMap.toString());

        try {
            // 如果返回错误码，则返回null
            JSONObject jsonObject = JSONObject.parseObject(result);
            String errorCode = jsonObject.getString("errorcode");
            if (errorCode != null) {
                log.debug("获取小程序码数据错误：{}", result);
                return null;
            }
            return null;
        } catch (JSONException exception) {
            return result;
        }

    }

    /**
     * 根据account获取accessToken
     * 
     * @return
     * @throws IOException
     */
    public String getAccessToken() throws IOException {
        // 查询redis缓存
        String redisKey = "accessToken";
        Object existedAccessToken = redisService.get(redisKey);
        if (existedAccessToken != null) {
            if (existedAccessToken instanceof String) {

                return existedAccessToken.toString();

            } else {
                // 直接返回字符串值
                return existedAccessToken.toString();
            }
        } else {
            // 从微信服务器获取accessToken
            String url = RestApi.accessTokenUrl.replace("APPID", appId).replace("APPSECRET", appSecret);
            String str = HttpUtil.get(url);
            try {
                JSONObject jsonObject = JSONObject.parseObject(str);
                String accessToken = jsonObject.getString("access_token");
                if (!StringUtils.isEmpty(accessToken)) {
                    // 将微信返回的accessToken放到redis缓存中
                    redisService.putNoJson(redisKey, accessToken, 3600L, TimeUnit.SECONDS);
                    return accessToken;
                } else {
                    log.error("从微信获取accessToken失败: {}", str);
                    return null;
                }
            } catch (Exception e) {
                log.error("解析微信返回的accessToken失败", e);
                return null;
            }
        }
    }
}