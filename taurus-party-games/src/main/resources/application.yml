server:
  port: 8080
  undertow:
    io-threads: 4
    worker-threads: 32
    buffer-size: 1024
    direct-buffers: true

spring:
  application:
    name: taurus-competition
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************************************
    username: root
    password: 9945xqyg
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      # 核心连接池配置
      initial-size: 5
      min-idle: 5
      # 降低最大连接数，避免数据库压力
      max-active: 50
      # 降低等待时间到30秒
      max-wait: 30000

      # 连接检测配置
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false

      # 预编译语句缓存
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20

      # 监控和安全配置
      filters: stat,wall,slf4j

      # 监控页面配置
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"

      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: admin
        login-password: admin123
  redis:
    host: 127.0.0.1
    port: 6379
    password: jfksio&^%^2fjlsfdh217^%02
    database: 0
    # 优化Redis超时时间
    timeout: 5000
    jedis:
      pool:
        # 优化Redis连接池配置
        max-active: 20
        max-wait: 3000
        max-idle: 10
        min-idle: 2
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        service: taurus-party-games
        username: nacos
        password: 9945xqyg

  # 禁用 JPA Open-in-View
  jpa:
    open-in-view: false

# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.taurus.partygames.entity
    com.taurus.partygames.vo
    com.taurus.wolfKiller.entity
    com.taurus.wolfKiller.dto
  configuration:
    map-underscore-to-camel-case: true
    # 生产环境关闭SQL日志输出
    # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 日志配置
logging:
  level:
    root: info
    com.taurus.partygames: info
    # 关闭SQL日志输出以提升性能
    org.apache.ibatis.logging: warn
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/party-games.log
    max-size: 100MB
    max-history: 30
