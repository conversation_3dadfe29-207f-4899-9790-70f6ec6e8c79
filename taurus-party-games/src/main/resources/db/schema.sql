-- 游戏房间表
CREATE TABLE IF NOT EXISTS game_room (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    room_code VARCHAR(6) NOT NULL COMMENT '房间码',
    room_name VARCHAR(50) NOT NULL COMMENT '房间名称',
    word_pair VARCHAR(100) COMMENT '词语对',
    remaining_whiteboard_count INT DEFAULT 0 COMMENT '剩余白板数',
    remaining_civilian_count INT DEFAULT 0 COMMENT '剩余平民数',
    remaining_undercover_count INT DEFAULT 0 COMMENT '剩余卧底数',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    update_time DATETIME NOT NULL COMMENT '更新时间',
    UNIQUE KEY uk_room_code (room_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='游戏房间表';

-- 房间玩家表
CREATE TABLE IF NOT EXISTS room_player (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    room_id BIGINT NOT NULL COMMENT '房间ID',
    player_id VARCHAR(50) NOT NULL COMMENT '玩家ID',
    is_creator BOOLEAN DEFAULT FALSE COMMENT '是否房主',
    identity VARCHAR(20) COMMENT '身份',
    word VARCHAR(50) COMMENT '词语',
    join_time BIGINT NOT NULL COMMENT '加入时间',
    UNIQUE KEY uk_room_player (room_id, player_id),
    FOREIGN KEY (room_id) REFERENCES game_room(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='房间玩家表';

-- 玩家表
CREATE TABLE IF NOT EXISTS player (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    player_id VARCHAR(50) NOT NULL COMMENT '玩家ID',
    player_name VARCHAR(50) NOT NULL COMMENT '玩家昵称',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    update_time DATETIME NOT NULL COMMENT '更新时间',
    UNIQUE KEY uk_player_id (player_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='玩家表';

-- 词语对表
CREATE TABLE IF NOT EXISTS word_pair (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    civilian_word VARCHAR(50) NOT NULL COMMENT '平民词语',
    undercover_word VARCHAR(50) NOT NULL COMMENT '卧底词语',
    library_name VARCHAR(50) NOT NULL COMMENT '词库名称',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    update_time DATETIME NOT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='词语对表';

-- 创建索引
CREATE INDEX idx_room_code ON game_room(room_code);
CREATE INDEX idx_room_id ON room_player(room_id);
CREATE INDEX idx_player_id ON player(player_id);
CREATE INDEX idx_library_name ON word_pair(library_name); 