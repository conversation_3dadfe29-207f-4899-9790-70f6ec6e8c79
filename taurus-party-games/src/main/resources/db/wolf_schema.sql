-- 创建狼人杀角色表
CREATE TABLE IF NOT EXISTS `wolf_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `role_name` varchar(64) NOT NULL COMMENT '角色名称',
  `role_description` text COMMENT '角色描述',
  `role_abilities` text COMMENT '角色能力',
  `role_tips` text COMMENT '游戏技巧',
  `role_image` varchar(512) DEFAULT NULL COMMENT '角色图片地址',
  `role_type` varchar(32) NOT NULL DEFAULT 'villager' COMMENT '角色阵营:werewolf-狼人,villager-村民,third-第三方',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_name` (`role_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='狼人杀角色表';

-- 创建狼人杀玩家用户表
CREATE TABLE IF NOT EXISTS `wolf_player_user` (
  `player_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '玩家ID',
  `nickname` varchar(255) NOT NULL COMMENT '玩家昵称',
  `avatar_url` varchar(512) DEFAULT NULL COMMENT '头像URL',
  `last_login_time` datetime NOT NULL COMMENT '最后登录时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_player_id` (`player_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='狼人杀玩家用户表'; 


-- 创建狼人杀房间表
CREATE TABLE IF NOT EXISTS `wolf_room` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '房间ID',
  `room_code` varchar(20) NOT NULL COMMENT '房间码',
  `creator_id` varchar(64) NOT NULL COMMENT '创建者ID',
  `creator_name` varchar(255) NOT NULL COMMENT '创建者名称',
  `total_players` int(11) NOT NULL DEFAULT '1' COMMENT '总玩家数量',
  `expire_time` datetime NOT NULL COMMENT '房间过期时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_room_code` (`room_code`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='狼人杀房间表';

-- 创建狼人杀房间角色表
CREATE TABLE IF NOT EXISTS `wolf_room_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `room_id` bigint(20) NOT NULL COMMENT '房间ID',
  `role_name` varchar(64) NOT NULL COMMENT '角色名称',
  `role_count` int(11) NOT NULL DEFAULT '0' COMMENT '角色数量',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_room_id` (`room_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='狼人杀房间角色表'; 

-- 创建狼人杀房间玩家表
CREATE TABLE IF NOT EXISTS `wolf_room_player` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `room_id` bigint(20) NOT NULL COMMENT '房间ID',
  `player_id` varchar(64) NOT NULL COMMENT '玩家ID',
  `player_name` varchar(255) NOT NULL COMMENT '玩家名称',
  `player_index` int(11) NOT NULL COMMENT '玩家序号',
  `role_name` varchar(64) DEFAULT NULL COMMENT '角色名称',
  `is_creator` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否创建者:0否1是',
  `join_time` datetime NOT NULL COMMENT '加入时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_room_player` (`room_id`, `player_id`),
  KEY `idx_room_id` (`room_id`),
  KEY `idx_player_id` (`player_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='狼人杀房间玩家表'; 


-- 插入初始角色数据
INSERT INTO `wolf_role` (`role_name`, `role_description`, `role_abilities`, `role_tips`, `role_image`, `role_type`, `sort_order`)
VALUES 
('WEREWOLF', '每晚可以杀死一名玩家，目标是杀光所有好人。', '每晚与其他狼人一起选择一名玩家杀死。', '白天尽量隐藏身份，混淆视听，伪装成好人。', 'https://via.placeholder.com/150?text=狼人', 'werewolf', 1),
('VILLAGER', '没有特殊能力的村民，靠推理找出狼人。', '无特殊能力，只能通过投票放逐可疑的人。', '仔细观察每个人的发言，寻找破绽和可疑点。', 'https://via.placeholder.com/150?text=村民', 'villager', 2),
('SEER', '预言家每晚可以查验一名玩家的身份。', '每晚可以选择一名玩家查看是否为狼人。', '不要过早暴露身份，积累信息后再发言。', 'https://via.placeholder.com/150?text=预言家', 'villager', 3),
('WITCH', '女巫拥有一瓶解药和一瓶毒药。', '可以使用解药救活被狼人杀死的人，或用毒药杀死一个人。', '解药和毒药都是一次性的，要谨慎使用。', 'https://via.placeholder.com/150?text=女巫', 'villager', 4),
('HUNTER', '猎人被杀或投票出局时可以开枪射杀一人。', '死亡时可以"带走"一名玩家。', '可以利用开枪威胁来保护自己。', 'https://via.placeholder.com/150?text=猎人', 'villager', 5),
('GUARD', '守卫可以在夜晚保护一名玩家免受狼人攻击。', '每晚可以选择一名玩家进行保护，但不能连续两晚保护同一个人。', '尽量不要让狼人知道谁被保护了。', 'https://via.placeholder.com/150?text=守卫', 'villager', 6),
('IDIOT', '白痴被投票出局后可以翻牌亮明身份，继续存活但失去投票权。', '被投票放逐后不会出局，但失去发言权和投票权。', '可以作为一个确认的好人身份存在。', 'https://via.placeholder.com/150?text=白痴', 'villager', 7); 


