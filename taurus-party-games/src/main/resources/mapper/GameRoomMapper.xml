<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.taurus.partygames.mapper.GameRoomMapper">
    
    <!-- 房间结果映射 -->
    <resultMap id="gameRoomMap" type="com.taurus.partygames.entity.GameRoom">
        <id property="id" column="id"/>
        <result property="roomCode" column="room_code"/>
        <result property="roomName" column="room_name"/>
        <result property="hostName" column="host_name"/>
        <result property="maxPlayers" column="max_players"/>
        <result property="wordLibrary" column="word_library"/>
        <result property="rounds" column="rounds"/>
        <result property="currentRound" column="current_round"/>
        <result property="status" column="status"/>
        <result property="roundStatus" column="round_status"/>
        <result property="winner" column="winner"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    
    <!-- 插入房间记录 -->
    <insert id="insert" parameterType="com.taurus.partygames.entity.GameRoom" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO game_room (
            room_code, room_name, host_name, max_players, word_library, 
            rounds, current_round, status, round_status, winner, create_time, update_time
        ) VALUES (
            #{roomCode}, #{roomName}, #{hostName}, #{maxPlayers}, #{wordLibrary}, 
            #{rounds}, #{currentRound}, #{status}, #{roundStatus}, #{winner}, #{createTime}, #{updateTime}
        )
    </insert>
    
    <!-- 更新房间信息 -->
    <update id="update" parameterType="com.taurus.partygames.entity.GameRoom">
        UPDATE game_room
        <set>
            <if test="roomName != null">room_name = #{roomName},</if>
            <if test="hostName != null">host_name = #{hostName},</if>
            <if test="maxPlayers != null">max_players = #{maxPlayers},</if>
            <if test="wordLibrary != null">word_library = #{wordLibrary},</if>
            <if test="rounds != null">rounds = #{rounds},</if>
            <if test="currentRound != null">current_round = #{currentRound},</if>
            <if test="status != null">status = #{status},</if>
            <if test="roundStatus != null">round_status = #{roundStatus},</if>
            <if test="winner != null">winner = #{winner},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>
    
    <!-- 根据房间编码查询房间 -->
    <select id="findByRoomCode" resultMap="gameRoomMap">
        SELECT * FROM game_room WHERE room_code = #{roomCode}
    </select>

    <!-- 根据房间id查询房间 -->
    <select id="findById" resultMap="gameRoomMap">
        SELECT * FROM game_room WHERE id = #{id}
    </select>
    
    <!-- 查询所有房间 -->
    <select id="findAll" resultMap="gameRoomMap">
        SELECT * FROM game_room ORDER BY create_time DESC
    </select>
    
    <!-- 根据状态查询房间列表 -->
    <select id="findByStatus" resultMap="gameRoomMap">
        SELECT * FROM game_room WHERE status = #{status} ORDER BY create_time DESC
    </select>
    
    <!-- 删除过期房间 -->
    <delete id="deleteExpiredRooms">
        DELETE FROM game_room WHERE create_time &lt; #{beforeTime}
    </delete>
</mapper> 