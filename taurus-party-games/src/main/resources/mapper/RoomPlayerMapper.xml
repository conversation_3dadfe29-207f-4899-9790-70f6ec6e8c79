<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.taurus.partygames.mapper.RoomPlayerMapper">
    
    <select id="selectJoinedRoomsByPlayerId" resultType="com.taurus.partygames.vo.JoinedGameRoomVO">
        SELECT 
            r.id as roomId,        <!-- 确保别名与VO属性名一致 -->
            r.room_code as roomCode,    <!-- 确保别名与VO属性名一致 -->
            rp.is_creator as isCreator,   
            rp.join_time as joinTime <!-- 根据实际VO属性添加 -->
        FROM 
            room_player rp
        JOIN 
            game_room r ON rp.room_id = r.id
        WHERE 
            rp.player_id = #{playerId}
        ORDER BY rp.join_time DESC
    </select>
    
    <select id="selectJoinedplayerByRoomIdAndPlayerId" resultType="com.taurus.partygames.vo.RoomPlayerVO">
        SELECT 
            r.id as roomId,
             r.room_code as roomCode,
             rp.player_id as playerId,
             rp.word as word,
             rp.identity,
             rp.index,
            rp.is_creator as isCreator,   
            rp.join_time as joinTime
        FROM 
            room_player rp
        JOIN 
            game_room r ON rp.room_id = r.id
        WHERE 
            rp.room_id = #{roomId}
            AND rp.player_id = #{playerId}
        LIMIT 1
    </select>
</mapper>