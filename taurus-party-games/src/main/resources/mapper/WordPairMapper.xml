<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.taurus.partygames.mapper.WordPairMapper">
    
    <!-- 词对结果映射 -->
    <resultMap id="wordPairMap" type="com.taurus.partygames.entity.WordPair">
        <id property="id" column="id"/>
        <result property="libraryName" column="library_name"/>
        <result property="civilianWord" column="civilian_word"/>
        <result property="undercoverWord" column="undercover_word"/>
        <result property="difficulty" column="difficulty"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    
    <!-- 插入词对记录 -->
    <insert id="insert" parameterType="com.taurus.partygames.entity.WordPair" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO word_pair (
            library_name, civilian_word, undercover_word, difficulty, create_time, update_time
        ) VALUES (
            #{libraryName}, #{civilianWord}, #{undercoverWord}, #{difficulty}, NOW(), NOW()
        )
    </insert>
    
    <!-- 更新词对信息 -->
    <update id="update" parameterType="com.taurus.partygames.entity.WordPair">
        UPDATE word_pair
        <set>
            <if test="libraryName != null">library_name = #{libraryName},</if>
            <if test="civilianWord != null">civilian_word = #{civilianWord},</if>
            <if test="undercoverWord != null">undercover_word = #{undercoverWord},</if>
            <if test="difficulty != null">difficulty = #{difficulty},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>
    
    <!-- 根据词库名称查询词对列表 -->
    <select id="selectByLibraryName" resultMap="wordPairMap">
        SELECT * FROM word_pair WHERE library_name = #{libraryName}
    </select>
    
    <!-- 获取随机词对 -->
    <select id="getRandomWordPair" resultMap="wordPairMap">
        SELECT * FROM word_pair WHERE library_name = #{libraryName} ORDER BY RAND() LIMIT 1
    </select>
    
    <!-- 查询所有词对 -->
    <select id="selectAll" resultMap="wordPairMap">
        SELECT * FROM word_pair ORDER BY library_name, id
    </select>
    
    <!-- 删除词对 -->
    <delete id="deleteById">
        DELETE FROM word_pair WHERE id = #{id}
    </delete>
    
    <!-- 查询所有词库名称 -->
    <select id="selectAllLibraryNames" resultType="java.lang.String">
        SELECT DISTINCT library_name FROM word_pair
    </select>
</mapper>