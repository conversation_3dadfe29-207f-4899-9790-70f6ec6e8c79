# 一定要记住以下几个方面：

## 编码原则

- 代码要复用，写之前一定要看有没有现成的可以用；
- UI、UE、代码设计编写保持一种风格，具有一致性和连贯性；
- 从前端功能推理出前端请求和后端接口如何实现和连接；
- 编写真实功能代码，不要模拟代码；
- 后端代码设计时一定要以实体类为基础，不要随意增加更改实体类，如一定要增加或修改，需要我同意！
- 编写前后端代码时，如果前后端字段不一样时，以后端提供的字段为准，按意义一一替换；
- 验证代码的正确性可靠性，是否能满足需求；
- 如果遇到问题，应试图自行分析、搜索、发现、找到解决问题的方法；

## 业务需求

- 创建房间后，游戏已开始，同时房主随机获取了一个身份和词语；其他玩家只要打开分享的房间链接，就可以随机获取到一个身份和词语；
- 玩家打开分享的房间链接后，显示“你的身份”页面，页面中显示随机获取的身份和词语；
- 不需要玩家退出功能，不需要房主踢人的功能；重新开局必须走
- 不需要记录游戏过程中的多轮对话和每轮的结果，也不需要投票；
- 需要记录每个房间里所有游戏人员随机获取到的身份和词语；
- 不需要用 websocket 接口来实时的传输游戏状态信息
