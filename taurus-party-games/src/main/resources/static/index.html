<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>派对游戏测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        h1 {
            color: #333;
            text-align: center;
        }

        .card {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        form {
            display: grid;
            grid-gap: 10px;
        }

        label {
            font-weight: bold;
        }

        input,
        button {
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            padding: 10px;
            font-weight: bold;
        }

        button:hover {
            background-color: #45a049;
        }

        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>

<body>
    <h1>派对游戏 API 测试</h1>

    <div class="card">
        <h2>创建游戏房间</h2>
        <form id="createRoomForm">
            <label for="creatorId">创建者ID:</label>
            <input type="text" id="creatorId" value="player123" required>

            <label for="libraryName">词库名称:</label>
            <input type="text" id="libraryName" value="默认词库" required>

            <label for="wordRandom">词语是否随机:</label>
            <input type="checkbox" id="wordRandom" checked>

            <label for="wordPair">自定义词语对 (非随机时使用):</label>
            <input type="text" id="wordPair" value="苹果,梨子">

            <label for="civilianCount">平民人数 (不少于1):</label>
            <input type="number" id="civilianCount" value="3" min="1" required>

            <label for="undercoverCount">卧底人数 (不少于1):</label>
            <input type="number" id="undercoverCount" value="1" min="1" required>

            <label for="whiteboardCount">白板人数 (不少于0):</label>
            <input type="number" id="whiteboardCount" value="1" min="0" required>

            <button type="submit">创建房间</button>
        </form>
        <div>
            <h3>创建结果:</h3>
            <pre id="createResult">等待提交...</pre>
        </div>
    </div>

    <div class="card">
        <h2>加入游戏房间</h2>
        <form id="joinRoomForm">
            <label for="roomCode">房间码:</label>
            <input type="text" id="roomCode" required>

            <label for="playerId">玩家ID:</label>
            <input type="text" id="playerId" value="player456" required>

            <label for="playerName">玩家昵称:</label>
            <input type="text" id="playerName" value="玩家2" required>

            <button type="submit">加入房间</button>
        </form>
        <div>
            <h3>加入结果:</h3>
            <pre id="joinResult">等待提交...</pre>
        </div>
    </div>

    <div class="card">
        <h2>获取房间信息</h2>
        <form id="getRoomForm">
            <label for="getRoomCode">房间码:</label>
            <input type="text" id="getRoomCode" required>

            <button type="submit">获取房间信息</button>
        </form>
        <div>
            <h3>房间信息:</h3>
            <pre id="roomResult">等待提交...</pre>
        </div>
    </div>

    <script>
        // 创建房间
        document.getElementById('createRoomForm').addEventListener('submit', function (e) {
            e.preventDefault();

            const data = {
                creatorId: document.getElementById('creatorId').value,
                libraryName: document.getElementById('libraryName').value,
                wordRandom: document.getElementById('wordRandom').checked,
                wordPair: document.getElementById('wordPair').value,
                civilianCount: parseInt(document.getElementById('civilianCount').value),
                undercoverCount: parseInt(document.getElementById('undercoverCount').value),
                whiteboardCount: parseInt(document.getElementById('whiteboardCount').value)
            };

            fetch('http://localhost:8080/api/room/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
                .then(response => response.json())
                .then(result => {
                    document.getElementById('createResult').textContent = JSON.stringify(result, null, 2);
                    // 如果创建成功，自动填充房间码到其他表单
                    if (result.code === 200 && result.data && result.data.roomCode) {
                        document.getElementById('roomCode').value = result.data.roomCode;
                        document.getElementById('getRoomCode').value = result.data.roomCode;
                    }
                })
                .catch(error => {
                    document.getElementById('createResult').textContent = 'Error: ' + error;
                });
        });

        // 加入房间
        document.getElementById('joinRoomForm').addEventListener('submit', function (e) {
            e.preventDefault();

            const data = {
                roomCode: document.getElementById('roomCode').value,
                playerId: document.getElementById('playerId').value,
                playerName: document.getElementById('playerName').value
            };

            fetch('http://localhost:8080/api/room/join', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
                .then(response => response.json())
                .then(result => {
                    document.getElementById('joinResult').textContent = JSON.stringify(result, null, 2);
                })
                .catch(error => {
                    document.getElementById('joinResult').textContent = 'Error: ' + error;
                });
        });

        // 获取房间信息
        document.getElementById('getRoomForm').addEventListener('submit', function (e) {
            e.preventDefault();

            const roomCode = document.getElementById('getRoomCode').value;

            fetch(`http://localhost:8080/api/room/info?roomCode=${roomCode}`)
                .then(response => response.json())
                .then(result => {
                    document.getElementById('roomResult').textContent = JSON.stringify(result, null, 2);
                })
                .catch(error => {
                    document.getElementById('roomResult').textContent = 'Error: ' + error;
                });
        });
    </script>
</body>

</html>