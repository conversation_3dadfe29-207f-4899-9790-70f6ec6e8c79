# 派对游戏服务 (Party Games Service)

这是一个基于 Spring Boot 的多人派对游戏平台后端服务，目前支持"谁是卧底"和"狼人杀助手"两款游戏。

## 项目架构

### 技术栈

- 后端框架：Spring Boot
- 数据库：MySQL
- ORM 框架：MyBatis
- 工具库：Lombok
- 接口文档：Swagger

### 项目结构

```
taurus-party-games/
├── src/main/java/com/taurus/wolfKiller/
│   ├── controller/    # 控制器层，处理HTTP请求
│   ├── service/       # 服务层，处理业务逻辑
│   ├── entity/        # 实体类
│   ├── dto/          # 数据传输对象
│   ├── vo/           # 视图对象
│   ├── mapper/       # MyBatis映射接口
│   ├── common/       # 公共组件
│   └── config/       # 配置类
└── src/main/resources/
    ├── mapper/       # MyBatis映射文件
    └── application.yml # 应用配置文件
```

## 核心功能

### 狼人杀助手

#### 1. 房间管理

- 创建游戏房间
- 生成唯一房间码
- 设置房间有效期（10 分钟）
- 支持重新开局
- 房间状态管理

#### 2. 玩家管理

- 玩家加入房间
- 随机分配角色
- 玩家信息管理
- 玩家状态跟踪

#### 3. 角色系统

- 支持多种角色配置
  - 狼人
  - 平民
  - 预言家
  - 女巫
  - 猎人
  - 守卫
  - 白痴
- 角色能力说明
- 角色技巧提示

#### 4. 游戏流程

1. 房主创建房间并设置角色配置
2. 其他玩家通过房间码加入
3. 系统随机分配角色
4. 玩家查看自己的角色
5. 房主可以随时重新开局

### 谁是卧底

#### 1. 房间管理

- 创建游戏房间
- 生成唯一房间码
- 设置房间有效期
- 支持重新开局

#### 2. 玩家管理

- 玩家加入房间
- 随机分配词语
- 玩家信息管理

#### 3. 词库管理

- 默认词库
- 自定义词语对
- 随机分配机制

#### 4. 游戏流程

1. 房主创建房间并选择词库
2. 其他玩家通过房间码加入
3. 系统随机分配词语
4. 玩家查看自己的词语
5. 房主可以随时重新开局

## 开发指南

### 环境要求

- JDK 1.8+
- Maven 3.6+
- MySQL 5.7+

### 本地开发

1. 克隆项目

```bash
git clone [项目地址]
```

2. 配置数据库

- 创建数据库
- 修改 `application.yml` 中的数据库配置

3. 启动项目

```bash
mvn spring-boot:run
```

### 部署说明

1. 打包

```bash
mvn clean package
```

2. 运行

```bash
java -jar target/taurus-party-games.jar
```

## 更新历史

### 2024-03-31

1. 优化了狼人杀房间创建接口，支持更灵活的角色配置
2. 改进了玩家角色分配算法，确保角色分配更加随机
3. 增加了房间状态检查，防止过期房间被使用
4. 优化了错误处理机制，提供更友好的错误提示

### 2024-03-30

1. 新增"谁是卧底"游戏支持
2. 实现了词库管理和随机分配功能
3. 优化了房间管理逻辑
4. 完善了 API 文档

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 跨域配置

本项目支持前端跨域访问，具体配置如下：

1. `WebConfig.java`: 通过实现`WebMvcConfigurer`接口，配置全局跨域支持
2. `CorsConfig.java`: 提供`CorsFilter`实现，确保预检请求(OPTIONS)能被正确处理

当前配置允许以下前端源访问 API:

- http://localhost:5173

如需添加其他前端源，请修改这两个配置文件中的源地址。

## API 接口

### 创建游戏房间

POST 请求 `/api/room/create`

**请求示例：**

```javascript
// 前端调用示例 - 使用 fetch API
fetch("http://localhost:8080/api/room/create", {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
  },
  body: JSON.stringify({
    creatorId: "player123", // 必填：创建者ID
    libraryName: "默认词库", // 必填：词库名称
    wordRandom: true, // 可选：词语是否随机
    wordPair: "苹果,梨子", // 可选：自定义词语对，当wordRandom为false时使用
    undercoverCount: 1, // 必填：卧底人数（不能小于1）
    civilianCount: 3, // 必填：平民人数（不能小于1）
    whiteboardCount: 1, // 必填：白板人数（不能小于0）
  }),
})
  .then((response) => response.json())
  .then((data) => console.log(data))
  .catch((error) => console.error("Error:", error));
```

### 加入游戏房间

POST 请求 `/api/room/join`

**请求示例：**

```javascript
// 前端调用示例
fetch("http://localhost:8080/api/room/join", {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
  },
  body: JSON.stringify({
    roomCode: "ABC123", // 必填：房间码
    playerId: "player456", // 必填：玩家ID
    playerName: "玩家2", // 必填：玩家昵称
  }),
})
  .then((response) => response.json())
  .then((data) => console.log(data))
  .catch((error) => console.error("Error:", error));
```

### 获取房间信息

GET 请求 `/api/room/info?roomCode={roomCode}`

**请求示例：**

```javascript
// 前端调用示例
fetch("http://localhost:8080/api/room/info?roomCode=ABC123")
  .then((response) => response.json())
  .then((data) => console.log(data))
  .catch((error) => console.error("Error:", error));
```

# 狼人杀助手项目 API 接口文档

## 项目介绍

"狼人杀助手"游戏定位于帮助线下狼人杀游戏进行角色分发，属于派对游戏 web 服务（Party Games Service）的一部分。本文档提供 API 接口的详细说明，方便前端开发人员进行开发和调试。

## 1. 基础信息

- 基础 URL: `http://localhost:8080/api`
- 请求方式: GET/POST，详见各接口说明
- 响应格式: 统一 JSON 格式
- 通用响应结构:
  ```json
  {
    "code": 200, // 状态码，200表示成功
    "message": "成功", // 响应消息
    "data": {} // 响应数据，具体结构见各接口说明
  }
  ```

## 2. 房间相关接口

### 2.1 创建房间

- **URL**: `/wolf/room/create`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "creatorId": "string", // 创建者ID，必填
    "creatorName": "string", // 创建者名称，必填
    "roomName": "string", // 房间名称，必填
    "roleConfigs": [
      // 角色配置列表，必填
      {
        "roleName": "werewolf", // 角色名称
        "roleCount": 2 // 角色数量
      },
      {
        "roleName": "villager", // 角色名称
        "roleCount": 4 // 角色数量
      }
    ],
    "isPublic": true // 是否公开，选填
  }
  ```
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "id": 1,
      "roomCode": "A8B7C6",
      "creatorId": "creator_12345",
      "creatorName": "房主",
      "totalPlayers": 8,
      "expireTime": "2023-04-01T00:30:00",
      "createTime": "2023-04-01T00:00:00",
      "updateTime": "2023-04-01T00:00:00"
    }
  }
  ```

### 2.2 获取房间信息

- **URL**: `/wolf/room/info`
- **方法**: GET
- **请求参数**:
  - `roomId`: 房间 ID，必填
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "id": 1,
      "roomCode": "A8B7C6",
      "creatorId": "creator_12345",
      "creatorName": "房主",
      "totalPlayers": 8,
      "expireTime": "2023-04-01T00:30:00",
      "createTime": "2023-04-01T00:00:00",
      "updateTime": "2023-04-01T00:00:00"
    }
  }
  ```

### 2.3 根据房间码获取房间信息

- **URL**: `/wolf/room/infoByCode`
- **方法**: GET
- **请求参数**:
  - `roomCode`: 房间码，必填
- **响应示例**: 与 2.2 相同

### 2.4 获取房间角色配置

- **URL**: `/wolf/room/roomRoles`
- **方法**: GET
- **请求参数**:
  - `roomId`: 房间 ID，必填
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": [
      {
        "id": 1,
        "roomId": 1,
        "roleName": "狼人",
        "roleCount": 2,
        "createTime": "2023-04-01T00:00:00",
        "updateTime": "2023-04-01T00:00:00"
      },
      {
        "id": 2,
        "roomId": 1,
        "roleName": "平民",
        "roleCount": 4,
        "createTime": "2023-04-01T00:00:00",
        "updateTime": "2023-04-01T00:00:00"
      },
      {
        "id": 3,
        "roomId": 1,
        "roleName": "预言家",
        "roleCount": 1,
        "createTime": "2023-04-01T00:00:00",
        "updateTime": "2023-04-01T00:00:00"
      },
      {
        "id": 4,
        "roomId": 1,
        "roleName": "女巫",
        "roleCount": 1,
        "createTime": "2023-04-01T00:00:00",
        "updateTime": "2023-04-01T00:00:00"
      }
    ]
  }
  ```

### 2.5 重新开局

- **URL**: `/wolf/room/restart`
- **方法**: POST
- **请求参数**:
  - `roomId`: 房间 ID，必填
- **响应示例**: 与 2.2 相同

## 3. 玩家相关接口

### 3.1 加入房间

- **URL**: `/wolf/room/join`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "roomId": 1, // 房间ID，必填
    "playerId": "string", // 玩家标识，必填
    "playerName": "string" // 玩家名称，必填
  }
  ```
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "playerId": "player_12345",
      "roomId": 1,
      "playerName": "玩家1",
      "playerIndex": 1,
      "roleName": "狼人",
      "isCreator": 0,
      "joinTime": "2023-04-01T00:05:00",
      "createTime": "2023-04-01T00:05:00",
      "updateTime": "2023-04-01T00:05:00"
    }
  }
  ```

### 3.2 获取玩家信息

- **URL**: `/wolf/player/info`
- **方法**: GET
- **请求参数**:
  - `roomId`: 房间 ID，必填
  - `playerId`: 玩家标识，必填
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "playerId": "player_12345",
      "roomId": 1,
      "playerName": "玩家1",
      "playerIndex": 1,
      "roleName": "狼人",
      "isCreator": 0,
      "joinTime": "2023-04-01T00:05:00",
      "createTime": "2023-04-01T00:05:00",
      "updateTime": "2023-04-01T00:05:00"
    }
  }
  ```

### 3.3 获取房间所有玩家

- **URL**: `/wolf/player/roomPlayers`
- **方法**: GET
- **请求参数**:
  - `roomId`: 房间 ID，必填
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": [
      {
        "playerId": "creator_12345",
        "roomId": 1,
        "playerName": "房主",
        "playerIndex": 0,
        "roleName": "预言家",
        "isCreator": 1,
        "joinTime": "2023-04-01T00:00:00",
        "createTime": "2023-04-01T00:00:00",
        "updateTime": "2023-04-01T00:00:00"
      },
      {
        "playerId": "player_12345",
        "roomId": 1,
        "playerName": "玩家1",
        "playerIndex": 1,
        "roleName": "狼人",
        "isCreator": 0,
        "joinTime": "2023-04-01T00:05:00",
        "createTime": "2023-04-01T00:05:00",
        "updateTime": "2023-04-01T00:05:00"
      }
    ]
  }
  ```

### 3.4 获取玩家参与的所有游戏

- **URL**: `/wolf/player/joinedRooms`
- **方法**: GET
- **请求参数**:
  - `playerId`: 玩家标识，必填
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": [
      {
        "playerId": "player_12345",
        "roomId": 1,
        "playerName": "玩家1",
        "playerIndex": 1,
        "roleName": "狼人",
        "isCreator": 0,
        "joinTime": "2023-04-01T00:05:00",
        "createTime": "2023-04-01T00:05:00",
        "updateTime": "2023-04-01T00:05:00"
      },
      {
        "playerId": "player_12345",
        "roomId": 2,
        "playerName": "玩家1",
        "playerIndex": 1,
        "roleName": "平民",
        "isCreator": 0,
        "joinTime": "2023-04-02T10:15:00",
        "createTime": "2023-04-02T10:15:00",
        "updateTime": "2023-04-02T10:15:00"
      }
    ]
  }
  ```

### 3.5 获取或创建玩家用户

- **URL**: `/wolf/player/getOrCreateUser`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "playerId": "string", // 玩家标识，必填
    "nickname": "string", // 玩家昵称，可选
    "avatarUrl": "string" // 头像URL，可选
  }
  ```
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "id": 1,
      "playerId": "player_12345",
      "nickname": "狼人杀新手",
      "avatarUrl": "https://example.com/avatar.jpg",
      "lastLoginTime": "2023-04-01T00:00:00",
      "createTime": "2023-04-01T00:00:00",
      "updateTime": "2023-04-01T00:00:00"
    }
  }
  ```

## 4. 角色相关接口

### 4.1 获取所有角色信息

- **URL**: `/wolf/role/all`
- **方法**: GET
- **请求参数**: 无
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": [
      {
        "id": 1,
        "roleName": "狼人",
        "roleDescription": "每晚可以杀死一名玩家，目标是杀光所有好人。",
        "roleAbilities": "每晚与其他狼人一起选择一名玩家杀死。",
        "roleTips": "白天尽量隐藏身份，混淆视听，伪装成好人。",
        "roleImage": "https://example.com/werewolf.jpg",
        "roleType": "werewolf",
        "sortOrder": 1,
        "createTime": "2023-04-01T00:00:00",
        "updateTime": "2023-04-01T00:00:00"
      },
      {
        "id": 2,
        "roleName": "平民",
        "roleDescription": "没有特殊能力的村民，靠推理找出狼人。",
        "roleAbilities": "无特殊能力，只能通过投票放逐可疑的人。",
        "roleTips": "仔细观察每个人的发言，寻找破绽和可疑点。",
        "roleImage": "https://example.com/villager.jpg",
        "roleType": "villager",
        "sortOrder": 2,
        "createTime": "2023-04-01T00:00:00",
        "updateTime": "2023-04-01T00:00:00"
      }
    ]
  }
  ```

### 4.2 获取特定角色信息

- **URL**: `/wolf/role/info`
- **方法**: GET
- **请求参数**:
  - `roleName`: 角色名称，必填
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "id": 1,
      "roleName": "狼人",
      "roleDescription": "每晚可以杀死一名玩家，目标是杀光所有好人。",
      "roleAbilities": "每晚与其他狼人一起选择一名玩家杀死。",
      "roleTips": "白天尽量隐藏身份，混淆视听，伪装成好人。",
      "roleImage": "https://example.com/werewolf.jpg",
      "roleType": "werewolf",
      "sortOrder": 1,
      "createTime": "2023-04-01T00:00:00",
      "updateTime": "2023-04-01T00:00:00"
    }
  }
  ```

## 5. 错误码说明

| 错误码 | 说明                  |
| ------ | --------------------- |
| 200    | 成功                  |
| 400    | 参数验证失败          |
| 401    | 未登录或 token 已过期 |
| 403    | 没有相关权限          |
| 404    | 资源不存在            |
| 500    | 服务器内部错误        |

## 6. 注意事项

1. 房间有效期为 10 分钟，超时后玩家无法加入。
2. 创建房间时，总角色数量应该与`totalPlayers`一致。
3. 每个玩家加入房间后会随机获得一个身份。
4. 房主作为主持人，不需要参与游戏中的角色。
5. 重新开局会重置所有玩家的角色分配。
6. **数据结构更新**: WolfRoomPlayer 实体类已移除`id`字段，现使用`playerId`作为主键。所有 API 响应结构中，不再返回`id`字段，请使用`playerId`作为玩家的唯一标识。
7. **API 格式更新**: 创建房间 API 接口格式已更新，现接受角色配置列表格式，详见[创建房间](#21-创建房间)API 说明。

## 7. 调试工具

推荐使用 Postman 或类似工具进行 API 接口调试，可以通过以下步骤进行：

1. 设置请求 URL 和方法
2. 添加请求参数（GET 方法使用查询参数，POST 方法使用 Body）
3. 设置 Content-Type 为 application/json
4. 发送请求并查看响应

## 8. 示例流程

### 创建游戏流程

1. 房主创建角色 → 创建房间 → 获取房间码
2. 其他玩家通过房间码加入房间
3. 每个玩家查看自己的身份
4. 房主可以随时进行根据线下游戏情况重新开局

## 9. 更新历史

### 2025-03-31

1. 修改了`WolfRoomPlayer`实体类结构，移除`id`字段，改用`playerId`作为主键。
2. 更新了 CreateRoom API 接口，支持前端传入角色配置列表格式。
3. 所有返回玩家信息的 API 响应结构已更新，不再包含`id`字段。
