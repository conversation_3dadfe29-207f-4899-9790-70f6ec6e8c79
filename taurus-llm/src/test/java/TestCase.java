import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class TestCase {
	private static final Logger logger = LoggerFactory.getLogger(TestCase.class);

	public static void main(String[] args) {
		String str = "```json\n[\n  {\n    \"mainContent\": \"我公司PE给水管材所执行标准号为\",\n    \"options\": [\n      {\"text\": \"GB/T 18742.3-2017\", \"isSelected\": false},\n      {\"text\": \"GB/T 13663.2-2018\", \"isSelected\": true},\n      {\"text\": \"GB/T 28799.2-2020\", \"isSelected\": false},\n      {\"text\": \"GB/T 18742.2-2017\", \"isSelected\": false}\n    ]\n  },\n  {\n    \"mainContent\": \"我公司生产的常规HDPE双壁波纹管材属于{}径系列\",\n    \"options\": [\n      {\"text\": \"内\", \"isSelected\": false},\n      {\"text\": \"外\", \"isSelected\": true},\n      {\"text\": \"内径和外径\", \"isSelected\": false},\n      {\"text\": \"以上都不对\", \"isSelected\": false}\n    ]\n  },\n  {\n    \"mainContent\": \"我公司生产的HDPE同层排水管属于{}级\",\n    \"options\": [\n      {\"text\": \"PE63\", \"isSelected\": false},\n      {\"text\": \"PE80\", \"isSelected\": false},\n      {\"text\": \"PE100\", \"isSelected\": true},\n      {\"text\": \"以上都不对\", \"isSelected\": false}\n    ]\n  },\n  {\n    \"mainContent\": \"dn20×1.6MPa PE黑色直管每包数量为{}根\",\n    \"options\": [\n      {\"text\": \"10\", \"isSelected\": true},\n      {\"text\": \"15\", \"isSelected\": false},\n      {\"text\": \"20\", \"isSelected\": false},\n      {\"text\": \"25\", \"isSelected\": false}\n    ]\n  },\n  {\n    \"mainContent\": \"我司生产的HDPE同层排水管有{}条压线\",\n    \"options\": [\n      {\"text\": \"1\", \"isSelected\": true},\n      {\"text\": \"2\", \"isSelected\": false},\n      {\"text\": \"3\", \"isSelected\": false},\n      {\"text\": \"4\", \"isSelected\": false}\n    ]\n  },\n  {\n    \"mainContent\": \"我司生产的PE给水管有{}条色线\",\n    \"options\": [\n      {\"text\": \"3\", \"isSelected\": true},\n      {\"text\": \"4\", \"isSelected\": false},\n      {\"text\": \"5\", \"isSelected\": false},\n      {\"text\": \"6\", \"isSelected\": false}\n    ]\n  },\n  {\n    \"mainContent\": \"DN/ID 300 SN8 HDPE双壁波纹管中，“ID”指的是{}\",\n    \"options\": [\n      {\"text\": \"公称直径\", \"isSelected\": false},\n      {\"text\": \"外径\", \"isSelected\": false},\n      {\"text\": \"内径\", \"isSelected\": true},\n      {\"text\": \"以上说法都正确\", \"isSelected\": false}\n    ]\n  },\n  {\n    \"mainContent\": \"以下管材检验项目中，不能通过目测监测的是{}\",\n    \"options\": [\n      {\"text\": \"外观\", \"isSelected\": false},\n      {\"text\": \"颜色\", \"isSelected\": false},\n      {\"text\": \"壁厚\", \"isSelected\": true},\n      {\"text\": \"外径\", \"isSelected\": true}\n    ]\n  },\n  {\n    \"mainContent\": \"机台停机后有哪些工作要做？{}\",\n    \"options\": [\n      {\"text\": \"机筒、模体降温\", \"isSelected\": true},\n      {\"text\": \"产品拉完后停止各段设备运转\", \"isSelected\": true},\n      {\"text\": \"关闭机台水、电、气\", \"isSelected\": true},\n      {\"text\": \"检查设备有无问题并打扫卫生\", \"isSelected\": true}\n    ]\n  },\n  {\n    \"mainContent\": \"常规设备铭牌上包含的信息有{}\",\n    \"options\": [\n      {\"text\": \"生产厂家\", \"isSelected\": true},\n      {\"text\": \"规格型号\", \"isSelected\": true},\n      {\"text\": \"出厂编号\", \"isSelected\": true},\n      {\"text\": \"价格\", \"isSelected\": false}\n    ]\n  }\n]\n```";
		str = str.replace("```json", "");
		str = str.replace("\n", "");
		str = str.replace(" ", "");
		
		logger.info(str);
		

	}

}
