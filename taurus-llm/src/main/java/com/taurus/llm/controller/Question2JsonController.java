package com.taurus.llm.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import com.alibaba.dashscope.aigc.generation.GenerationResult;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.taurus.entity.ResponseObject;
import com.taurus.llm.feign.SSEClient;
import com.taurus.llm.model.ModelTask_Qwen;
import com.taurus.llm.service.LLMService;
import com.taurus.llm.service.LLM_ZhiPuService;
import com.taurus.llm.service.PromptTemplateService;
import com.taurus.utils.POIUtils;

import io.netty.util.internal.StringUtil;
import io.reactivex.Flowable;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.netty.http.client.HttpClient;
import reactor.netty.transport.ProxyProvider;

@Slf4j
@RestController
@RequestMapping("/question2Json")
public class Question2JsonController {
	@Autowired
	private LLMService llmService;

	@Autowired
	private LLM_ZhiPuService llmZhiPuService;

	@Autowired
	private PromptTemplateService promptTemplateService;

	@Autowired
	private SSEClient sseClient;

	/**
	 * 生成响应实体，从上传的文件中读取内容并进行处理。
	 * 
	 * @param file 用户上传的文件，预期为Word文档。
	 * @return ResponseEntity<String>
	 *         包含处理结果的HTTP响应实体。如果上传的文件为空，返回状态为BAD_REQUEST的响应体，包含错误信息。如果上传的文件格式不正确，同样返回状态为BAD_REQUEST的响应体。在处理过程中如果发生IO异常，返回状态为INTERNAL_SERVER_ERROR的响应体，包含错误信息。正常情况下，返回状态为OK的响应体，但当前实现中内容为空。
	 * @throws InputRequiredException
	 * @throws NoApiKeyException
	 * @throws ApiException
	 * @throws IOException            如果读取文件时发生错误。
	 */
	@PostMapping("/generate")
	public ResponseObject generate(@RequestParam("companyId") Integer companyId, @RequestParam("userId") Integer userId,
			@RequestParam("file") MultipartFile file) throws ApiException, NoApiKeyException, InputRequiredException {
		// 检查上传的文件是否为空
		if (file.isEmpty()) {
			return ResponseObject.failure("NO_FILE");
		}

		// 检查文件是否为Word文档
		String originalFilename = file.getOriginalFilename();
		if (!originalFilename.endsWith(".doc") && !originalFilename.endsWith(".docx")) {
			return ResponseObject.failure("NOT_WORD_FILE");
		}
		// 超过5Mb
		long size = file.getSize();
		if (size > 2 * 1024 * 1024) {
			return ResponseObject.failure("EXCEED_MAX_FILE_SIZE");
		}

		// 尝试读取并处理Word文件内容
		List<String> uContentList = POIUtils.readWordContent(file);

		// 字符数超过10000
		int contentLength = uContentList.stream().mapToInt(item -> item.length()).sum();

		if (contentLength > 10000) {
			return ResponseObject.failure("EXCEED_MAX_CONTENT_LENGTH");
		}

		JSONArray objArray = new JSONArray();
		for (int i = 0; i < uContentList.size(); i++) {
			String uContent = uContentList.get(i);
			String promptContent = promptTemplateService.constructQ2jsonPrompt(uContent);

			GenerationResult res = llmService.callWithMessage(promptContent);
			String jsonText = res.getOutput().getChoices().get(0).getMessage().getContent();

			jsonText = jsonText.replace("```json", "");
			jsonText = jsonText.replace("```", "");
			jsonText = jsonText.replace("\n", "");
			jsonText = jsonText.replace(" ", "");
			log.info("jsonText:{}", jsonText);
			JSONArray obj = JSONArray.parseArray(jsonText);
			objArray.add(obj);
		}

		return ResponseObject.success(objArray);
	}

	@PostMapping(value = "/stream")
	public ResponseObject streamWithStreamingResponseBody(HttpServletRequest request, HttpServletResponse response,
			@RequestParam("file") MultipartFile file) throws ApiException, NoApiKeyException, InputRequiredException {
		String userId = request.getParameter("userId");
		if (StringUtil.isNullOrEmpty(userId))
			return ResponseObject.failure("PARAMETERS_ERROR");

		// 首先向SSE发送开始标记
		JSONObject beginReq = new JSONObject();
		beginReq.put("clientId", userId);
		beginReq.put("msg", "BEGIN_LLM_CONVERSATION");
		sseClient.send(beginReq);

		int corePoolSize = 4; // 可根据实际情况调整
		int maximumPoolSize = 8;
		long keepAliveTime = 0L;

		BlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<>();
		ExecutorService executorService = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, keepAliveTime,
				TimeUnit.MILLISECONDS, workQueue);

		// 线程安全的集合，用于存储线程的执行结果

		List<Future<StringBuffer>> futures = new ArrayList<>();

		// 尝试读取并处理Word文件内容
		List<String> uContentList = POIUtils.readWordContent(file);

		for (int i = 0; i < uContentList.size(); i++) {
			String uContent = uContentList.get(i);
			String promptContent = promptTemplateService.constructQ2jsonPrompt(uContent);

			Future<StringBuffer> future = executorService.submit(new ModelTask_Qwen().setPromptContent(promptContent));
			// Future<StringBuffer> future = executorService.submit(new
			// ModelTask_ZhiPu().setPromptContent(promptContent));

			futures.add(future);

			// 每三个线程执行完，等待所有线程执行完毕
			if (i > 0 && i % 3 == 0) {
				this.handleThreadResult(futures, userId);
				futures = new ArrayList<>();
			} else if (i == uContentList.size() - 1) {
				this.handleThreadResult(futures, userId);
				futures = new ArrayList<>();
			}
		}

		// 关闭线程池
		executorService.shutdown();

		// 最后向SSE发送结束标记
		JSONObject endReq = new JSONObject();
		endReq.put("clientId", userId);
		endReq.put("msg", "END_LLM_CONVERSATION");
		sseClient.send(endReq);

		return ResponseObject.success("true");
	}

	// 处理线程结果
	private void handleThreadResult(List<Future<StringBuffer>> futures, String userId) {
		// 获取所有线程结果，将所有结果合并成一个列表
		StringBuffer combinedResults = new StringBuffer();
		for (Future<StringBuffer> ifuture : futures) {
			try {
				combinedResults.append(ifuture.get());
			} catch (InterruptedException | ExecutionException e) {
				e.printStackTrace();
			}
		}
		futures.clear();

		// 向SSE发送结果
		JSONObject req = new JSONObject();
		req.put("clientId", userId);
		req.put("msg", combinedResults.toString());
		sseClient.send(req);

	}

	@GetMapping(value = "/streamtest", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
	public Flowable<String> stream() {
		return Flowable.just("Hello", "World!", "这是流式响应。").delay(1, TimeUnit.SECONDS);// 每秒发送一个元素
	}

	@GetMapping("/chat")
	public Flux<String> stringFlux(String c) {
		return webClient().post().accept(MediaType.TEXT_EVENT_STREAM) // 接收text/event-stream流的数据
				.body(BodyInserters.fromValue(jsonObject(c))) // 参数
				.retrieve().bodyToFlux(String.class) // 输出格式
				.map(s -> {
					if (!Objects.equals(s, "[DONE]")) {
						JSONObject jo = JSON.parseObject(s).getJSONArray("choices").getJSONObject(0)
								.getJSONObject("delta");
						String content = jo.getString("content");
						if (content != null) {
							return content;
						}
					}
					return "";
				}).onErrorResume(WebClientResponseException.class, ex -> Flux.just(ex.getResponseBodyAsString())) // 请求失败
				.doFinally(signalType -> log.info("完成")); // 请求完成后
	}

	// 参数
	private JSONObject jsonObject(String content) {

		JSONObject userMessage = new JSONObject();
		userMessage.put("role", "user");
		userMessage.put("content", content);

		JSONArray jsonArray = new JSONArray();
		jsonArray.add(userMessage);

		JSONObject jsonObject = new JSONObject();
		jsonObject.put("model", "gpt-3.5-turbo-16k-0613"); // 速度快，价格高
		jsonObject.put("messages", jsonArray);
		jsonObject.put("stream", true);
		return jsonObject;
	}

	private WebClient webClient() {
		return WebClient.builder().clientConnector(new ReactorClientHttpConnector(
				HttpClient.create().proxy(proxy -> proxy.type(ProxyProvider.Proxy.HTTP).host("127.0.0.1").port(1080)) // 代理
		)).defaultHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
				.defaultHeader("Authorization", "Bearer token") // 令牌
				.baseUrl("https://api.openai.com/v1/chat/completions") // 请求地址
				.build();
	}

	@GetMapping("/testInvoke")
	public void testInvoke() {
		llmZhiPuService.testInvoke();
	}
}
