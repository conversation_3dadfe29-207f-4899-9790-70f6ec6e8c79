package com.taurus.llm.controller;

import java.math.BigDecimal;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.dashscope.aigc.generation.GenerationResult;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.taurus.entity.ResponseObject;
import com.taurus.llm.service.LLMService;
import com.taurus.llm.service.LLM_ZhiPuService;
import com.taurus.llm.service.PromptTemplateService;

import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/questionScoring")
public class QuestionScoringController {

	@Autowired
	private LLMService llmService;

	@Autowired
	private LLM_ZhiPuService llmZhiPuService;

	@Autowired
	private PromptTemplateService promptTemplateService;

	/**
	 * 给题目打分
	 * 
	 * @param body
	 * @return
	 * @throws InputRequiredException
	 * @throws NoApiKeyException
	 * @throws ApiException
	 */
	@PostMapping("/scoring")
	public ResponseObject scoring(@RequestBody JSONObject body)
			throws ApiException, NoApiKeyException, InputRequiredException {
		String mainContent = body.getString("mainContent");
		String tipContent = body.getString("tipContent");

		BigDecimal questionMark = body.getBigDecimal("questionMark");
		if (StringUtil.isNullOrEmpty(mainContent) || questionMark == null || questionMark.intValue() == 0)
			return ResponseObject.failure("PARAMETERS_ERROR");

		if (!StringUtil.isNullOrEmpty(tipContent)) {
			tipContent = tipContent.trim();
		}

		if (StringUtil.isNullOrEmpty(tipContent))
			return ResponseObject.failure("EMPTY_TIPCONTENT");

		String answer = body.getString("answer");
		if (StringUtil.isNullOrEmpty(answer)) {
			JSONObject result = new JSONObject();
			result.put("score", new BigDecimal(0));
			result.put("comment", "未填写内容");
			return ResponseObject.success(result);
		}

		String promptContent = promptTemplateService.constructScoringPrompt(mainContent, tipContent, answer,
				questionMark);
		log.info("prompt:{}", promptContent);

		GenerationResult res = llmService.callWithMessage(promptContent);
		String jsonText = res.getOutput().getChoices().get(0).getMessage().getContent();

		jsonText = jsonText.replace("```json", "");
		jsonText = jsonText.replace("```", "");
		jsonText = jsonText.replace("\n", "");
		jsonText = jsonText.replace(" ", "");
		log.info("LLM response jsonText:{}", jsonText);
		// 通过正则表达式的方式取出json字符串
		Pattern pattern = Pattern.compile("\\{.*?\\}");
		Matcher matcher = pattern.matcher(jsonText);
		if (matcher.find()) {
			jsonText = matcher.group();
		}
		log.info("get jsonText by REGEX :{}", jsonText);
		JSONObject obj = null;
		try {
			obj = JSONArray.parseObject(jsonText);
		} catch (Exception e) {
			log.error("parse jsonText error:{}", jsonText);
			return ResponseObject.failure("PARSE_JSON_TEXT_ERROR");
		}
		Object score = obj.get("score");
		if (score instanceof Integer) {
			obj.put("score", new BigDecimal((Integer) score));
		} else if (score instanceof Double) {
			obj.put("score", new BigDecimal((Double) score));
		}

		return ResponseObject.success(obj);

	}

}
