package com.taurus.llm.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.taurus.entity.ResponseObject;
import com.taurus.llm.service.LLMService;
import com.taurus.llm.service.PromptTemplateService;
import com.taurus.utils.POIUtils;

/**
 * 文生题
 */
@RestController
@RequestMapping("/text2Questions")
public class Text2QuestionsController {
	@Autowired
	private LLMService llmService;

	@Autowired
	private PromptTemplateService promptTemplateService;

	/**
	 * 生成响应实体，从上传的文件中读取内容并进行处理。
	 * 
	 * @param file 用户上传的文件，预期为Word文档。
	 * @return ResponseEntity<String>
	 *         包含处理结果的HTTP响应实体。如果上传的文件为空，返回状态为BAD_REQUEST的响应体，包含错误信息。如果上传的文件格式不正确，同样返回状态为BAD_REQUEST的响应体。在处理过程中如果发生IO异常，返回状态为INTERNAL_SERVER_ERROR的响应体，包含错误信息。正常情况下，返回状态为OK的响应体，但当前实现中内容为空。
	 * @throws IOException 如果读取文件时发生错误。
	 */
	@PostMapping("/generate")
	public ResponseObject generate(@RequestParam("companyId") Integer companyId, @RequestParam("userId") Integer userId,
			@RequestParam("number") Integer number, @RequestParam("questionType") String questionType,
			@RequestParam("file") MultipartFile file) {
		List<String> questionTypes = Arrays.asList("SEL", "COM", "ADJ");

		if (!questionTypes.contains(questionType)) {
			return ResponseObject.failure("PARAMETERS_ERROR");
		}

		// 检查上传的文件是否为空
		if (file.isEmpty()) {
			return ResponseObject.failure("NO_FILE");
		}

		// 检查文件是否为Word文档
		String originalFilename = file.getOriginalFilename();
		if (!originalFilename.endsWith(".doc") && !originalFilename.endsWith(".docx")) {
			return ResponseObject.failure("NOT_WORD_FILE");
		}
		// 超过5Mb
		long size = file.getSize();
		if (size > 5 * 1024 * 1024) {
			return ResponseObject.failure("EXCEED_MAX_FILE_SIZE");
		}

		// 尝试读取并处理Word文件内容
		List<String> uContentList = POIUtils.readWordContent(file);

		// 字符数超过10000
		int contentLength = uContentList.stream().mapToInt(item -> item.length()).sum();

		if (contentLength > 10000) {
			return ResponseObject.failure("EXCEED_MAX_CONTENT_LENGTH");
		}

		String questionTypeName = "", rule = "";

		if (questionType.equals("SEL")) {
			questionTypeName = "选择题";
			rule = promptTemplateService.getPromptTemplate("prompt-json-sel", "dymatic");
		} else if (questionType.equals("COM")) {
			questionTypeName = "填空题";
			rule = promptTemplateService.getPromptTemplate("prompt-json-com", "dymatic");
		} else if (questionType.equals("ADJ")) {
			questionTypeName = "判断题";
			rule = promptTemplateService.getPromptTemplate("prompt-json-adj", "dymatic");
		}

		List<String> replyList = new ArrayList<>();
		for (int i = 0; i < uContentList.size(); i++) {
			String uContent = uContentList.get(i);
			// 读取模板内容
			String promptContent = promptTemplateService.getPromptTemplate("prompt-t2q", "dymatic");
			promptContent = promptContent.replace("{{{number}}}", String.valueOf(number));
			promptContent = promptContent.replace("{{{questionTypeName}}}", questionTypeName);
			promptContent = promptContent.replace("{{{uContent}}}", uContent);
			promptContent = promptContent.replace("{{{rule}}}", rule);

			String replyContent = llmService.getConversationResult(promptContent);
			replyList.add(replyContent);
		}

		return ResponseObject.success(replyList);

	}

}
