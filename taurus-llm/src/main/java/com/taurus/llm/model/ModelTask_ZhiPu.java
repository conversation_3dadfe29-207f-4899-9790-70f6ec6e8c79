package com.taurus.llm.model;

import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.taurus.llm.service.LLM_ZhiPuService;
import com.taurus.llm.service.RedisService;
import com.taurus.utils.TextToMD5;
import com.zhipu.oapi.service.v4.model.ModelData;

import io.netty.util.internal.StringUtil;
import io.reactivex.Flowable;
import lombok.extern.slf4j.Slf4j;

/**
 * 质谱清言大模型任务
 */
@Slf4j
@Component
public class ModelTask_ZhiPu implements Callable<StringBuffer> {

	private String promptContent;
	
	private static RedisService redisService;
	
	private static LLM_ZhiPuService llmZhiPuService;
	
	@Autowired
	public void setRedisService(RedisService redisService) {
		ModelTask_ZhiPu.redisService=redisService;
	}
	
	@Autowired
	public void setLLM_ZhiPuService(LLM_ZhiPuService llmZhiPuService) {
		ModelTask_ZhiPu.llmZhiPuService=llmZhiPuService;
	}
	

	public ModelTask_ZhiPu setPromptContent(String promptContent) {
		this.promptContent = promptContent;
		return this;
	}

	@Override
	public StringBuffer call() throws Exception {
		// 为实现断点续传效果，将已解析的部分短期保存在redis中，第二次解析时能快速找回
		String md5Value = TextToMD5.getMD5(promptContent);
		log.info("md5:{}", md5Value);
		String value = redisService.get(md5Value);
		//String value = null;
		if (value != null) {
			StringBuffer contentBuffer = JSON.parseObject(value, StringBuffer.class);
			return contentBuffer;
		} else {
			// 在这里执行大模型的流式调用
			Flowable<ModelData> result = llmZhiPuService.streamCallWithMessage(promptContent);

			StringBuffer contentBuffer = new StringBuffer();
			if (result != null) {
				result.blockingForEach(message -> {
					String content = message.getChoices().get(0).getDelta().getContent();
					contentBuffer.append(content);
					// logger.info("LLM流式返回：{}", content);
				});
			}

			log.info("LLM单次会话返回内容：{}", contentBuffer.toString());

			if (!StringUtil.isNullOrEmpty(contentBuffer.toString())) {
				redisService.put(md5Value, contentBuffer, 1L, TimeUnit.HOURS);
			}

			return contentBuffer;
		}
	}
}