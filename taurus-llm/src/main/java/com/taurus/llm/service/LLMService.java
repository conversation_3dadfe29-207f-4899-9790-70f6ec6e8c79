package com.taurus.llm.service;

import java.util.Arrays;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.alibaba.dashscope.aigc.conversation.Conversation;
import com.alibaba.dashscope.aigc.conversation.ConversationParam;
import com.alibaba.dashscope.aigc.conversation.ConversationResult;
import com.alibaba.dashscope.aigc.generation.Generation;
import com.alibaba.dashscope.aigc.generation.GenerationParam;
import com.alibaba.dashscope.aigc.generation.GenerationResult;
import com.alibaba.dashscope.aigc.generation.models.QwenParam;
import com.alibaba.dashscope.common.Message;
import com.alibaba.dashscope.common.MessageManager;
import com.alibaba.dashscope.common.Role;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.alibaba.dashscope.utils.Constants;
import com.alibaba.dashscope.utils.JsonUtils;

import io.reactivex.Flowable;

@Service
public class LLMService {
	
	private static final Logger logger = LoggerFactory.getLogger(LLMService.class);

	/**
	 * 获取对话结果的方法。
	 * 
	 * @param prompt 用户的输入提示语。
	 * @return 返回对话接口的结果，以JSON字符串形式呈现。如果过程中发生异常，则返回null。
	 */
	public String getConversationResult(String prompt) {
		// 设置API密钥
		Constants.apiKey = "sk-376a5bb346e541119536c9ae027905c6";
		// 创建对话实例
		Conversation conversation = new Conversation();
		// 构建对话参数
		ConversationParam param = ConversationParam.builder().model(Conversation.Models.QWEN_MAX).prompt(prompt)
				.build();
		ConversationResult result;
		try {
			// 调用对话接口
			result = conversation.call(param);
			String replyContent = JsonUtils.toJson(result);
			// 打印并返回对话结果
			logger.info("大模型返回内容：{}", replyContent);

			return replyContent;
		} catch (ApiException e) {
			// 处理API异常
			e.printStackTrace();
			logger.error("发生错误：{}", "ApiException");
		} catch (NoApiKeyException e) {
			// 处理无API密钥异常
			e.printStackTrace();
			logger.error("发生错误：{}", "NoApiKeyException");
		} catch (InputRequiredException e) {
			// 处理输入缺失异常
			e.printStackTrace();
			logger.error("发生错误：{}", "InputRequiredException");
		}
		// 如果发生异常则返回null
		return null;
	}

	/**
	 * 调用大模型
	 * 
	 * @param prompt
	 * @return
	 * @throws NoApiKeyException
	 * @throws ApiException
	 * @throws InputRequiredException
	 */
	public GenerationResult callWithMessage(String prompt)
			throws NoApiKeyException, ApiException, InputRequiredException {
		logger.info("prompt:{}", prompt);
		// 设置API密钥
		Constants.apiKey = "sk-376a5bb346e541119536c9ae027905c6";
		Generation gen = new Generation();
		MessageManager msgManager = new MessageManager(10);
		Message systemMsg = Message.builder().role(Role.SYSTEM.getValue()).content("You are a helpful assistant.")
				.build();
		Message userMsg = Message.builder().role(Role.USER.getValue()).content(prompt).build();
		msgManager.add(systemMsg);
		msgManager.add(userMsg);
		QwenParam param = QwenParam.builder().model(Generation.Models.QWEN_MAX).messages(msgManager.get())
				.resultFormat(QwenParam.ResultFormat.MESSAGE).build();
		GenerationResult result = gen.call(param);

		logger.info("result:{}" , result.toString());
		return result;
	}

	/**
	 * 流式输出
	 * 
	 * @param prompt
	 * @return
	 * @throws NoApiKeyException
	 * @throws ApiException
	 * @throws InputRequiredException
	 */
	public Flowable<GenerationResult> streamCallWithMessage(String prompt)
			throws NoApiKeyException, ApiException, InputRequiredException {
		logger.info("提示词：prompt{}" , prompt);

		Constants.apiKey = "sk-376a5bb346e541119536c9ae027905c6";
		Generation gen = new Generation();
		Message systemMsg = Message.builder().role(Role.SYSTEM.getValue()).content("You are a helpful assistant.")
				.build();
		Message userMsg = Message.builder().role(Role.USER.getValue()).content(prompt).build();
		GenerationParam param = GenerationParam.builder()
				.model("qwen-plus")
				.messages(Arrays.asList(systemMsg, userMsg))
				.resultFormat(GenerationParam.ResultFormat.MESSAGE) // the result if message format.
				.topP(0.8).enableSearch(true) // set streaming output
				.incrementalOutput(true) // get streaming output incrementally
				.build();
		Flowable<GenerationResult> result = gen.streamCall(param);
		return result;
	}
	
	
}
