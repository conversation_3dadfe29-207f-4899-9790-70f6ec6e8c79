package com.taurus.llm.service;

import java.util.Iterator;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;




@Service
public class RedisService {
	@Autowired
	private RedisTemplate<String, Object> redisTemplate;


	/**
	 * 取出一个redis缓存对象
	 * 
	 * @param key
	 * @return
	 */
	public String get(String key) {
		return (String) redisTemplate.opsForValue().get(key);
	}

	/**
	 * 直接传入一个pojo对象 放入一个String对象，字符串会加上一对双引号！！！
	 * 
	 * @param key
	 * @param value
	 */
	public void put(String key, Object value, Long expire, TimeUnit timeUnit) {
		redisTemplate.opsForValue().set(key, JSONObject.toJSONString(value), expire, timeUnit);
	}

	/**
	 * 放入一个String对象
	 * 
	 * @param key
	 * @param value
	 * @param expire
	 * @param timeUnit
	 */
	public void putNoJson(String key, Object value, Long expire, TimeUnit timeUnit) {
		redisTemplate.opsForValue().set(key, value, expire, timeUnit);// 保存一年
	}

	/**
	 * 删除一个键值
	 * 
	 * @param key
	 * @return
	 */
	public boolean delete(StringBuffer key) {
		String value = (String) redisTemplate.opsForValue().get(key.toString());
		if (value != null) {
			return redisTemplate.delete(key.toString());
		} else {
			return true;
		}
	}

	/**
	 * 模糊匹配多个并删除
	 * 
	 * @param prex
	 */
	public void deleteByPrex(String prex) {
		Set<String> keys = redisTemplate.keys(prex + "*");
		if (keys != null && keys.size() > 0) {
			Iterator<String> iterator = keys.iterator();
			while (iterator.hasNext()) {
				String key = iterator.next();
				redisTemplate.delete(key);
			}
		}
	}

}
