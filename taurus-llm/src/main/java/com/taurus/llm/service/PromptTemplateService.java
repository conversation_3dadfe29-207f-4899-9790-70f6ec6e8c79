package com.taurus.llm.service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import com.taurus.llm.pojo.PromptTemplate;
 
@Service
public class PromptTemplateService {
 
    @Autowired
    private ResourceLoader resourceLoader;
    
    
	/**
	 * 获取模版内容
	 * @param templateName
	 * @param type
	 * @return
	 */
	public String getPromptTemplate(String templateName,String type) {
		if(type.equals("static")){
			HashMap<String,Object> map = PromptTemplate.templateMap;
			return (String) map.get(templateName);
		}else if(type.equals("dymatic")) {
			return this.readTemplate(templateName);
		}else {
			return null;
		}
	}
 
	/**
	 * 读取磁盘上的模版
	 * @param templateName
	 * @return
	 */
    public String readTemplate(String templateName) {
    	StringBuffer path = new StringBuffer("classpath:templates/").append(templateName).append(".txt");
        Resource resource = resourceLoader.getResource(path.toString());
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8))) {
            return reader.lines().collect(Collectors.joining(System.lineSeparator()));
        } catch (IOException e) {
			e.printStackTrace();
			return null;
		}
    }
    
	/**
	 * 构建Q2json prompt模版
	 * 
	 * @param uContent
	 * @return
	 */
	public String constructQ2jsonPrompt(String uContent) {
		StringBuffer ruleBuff = new StringBuffer();
		String rule1 = getPromptTemplate("prompt-json-sel", "dymatic");
		String rule2 = getPromptTemplate("prompt-json-com", "dymatic");
		String rule3 = getPromptTemplate("prompt-json-adj", "dymatic");
		String rule4 = getPromptTemplate("prompt-json-dis", "dymatic");
		// String rule5 = promptTemplateService.getPromptTemplate("prompt-json-cop",
		// "dymatic");

		ruleBuff.append(rule1).append(rule2).append(rule3).append(rule4);
		// ruleBuff.append(rule5);

		// 读取模板内容
		String promptContent = (String) getPromptTemplate("prompt-q2json", "dymatic");
		promptContent = promptContent.replace("{{{uContent}}}", uContent);
		promptContent = promptContent.replace("{{{rule}}}", ruleBuff.toString());
		return promptContent;
	}

	/**
	 * 构建ai打分prompt
	 * @param tipContent
	 * @param questionMark
	 * @param answer
	 * @return
	 */
	public String constructScoringPrompt(String mainContent,String tipContent, String answer,BigDecimal questionMark) {
		String promptContent = getPromptTemplate("prompt-scoring", "dymatic");
		promptContent = promptContent.replace("{{mainContent}}", mainContent);
		promptContent = promptContent.replace("{{tipContent}}", tipContent);
		promptContent = promptContent.replace("{{answer}}", answer);
		promptContent = promptContent.replace("{{questionMark}}", questionMark.toString());
		return promptContent;
	}
}
