package com.taurus.llm.service;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Service;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.zhipu.oapi.ClientV4;
import com.zhipu.oapi.Constants;
import com.zhipu.oapi.service.v4.model.ChatCompletionRequest;
import com.zhipu.oapi.service.v4.model.ChatCompletionRequestMixIn;
import com.zhipu.oapi.service.v4.model.ChatFunction;
import com.zhipu.oapi.service.v4.model.ChatFunctionCall;
import com.zhipu.oapi.service.v4.model.ChatFunctionCallMixIn;
import com.zhipu.oapi.service.v4.model.ChatFunctionMixIn;
import com.zhipu.oapi.service.v4.model.ChatMessage;
import com.zhipu.oapi.service.v4.model.ChatMessageRole;
import com.zhipu.oapi.service.v4.model.ModelApiResponse;
import com.zhipu.oapi.service.v4.model.ModelData;

import io.reactivex.Flowable;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class LLM_ZhiPuService {
	// 请自定义自己的业务id
	private static final String requestIdTemplate = "mycompany-%d";

	private static final String API_KEY = "56a5914f0ce1406000dd4de467f93cdb.xw1Yuka8lvvRw7Ef";
	
	private ClientV4 client = new ClientV4.Builder(API_KEY).build();
	
	private final ObjectMapper mapper = defaultObjectMapper();

	private ObjectMapper defaultObjectMapper() {
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
		mapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
		mapper.addMixIn(ChatFunction.class, ChatFunctionMixIn.class);
		mapper.addMixIn(ChatCompletionRequest.class, ChatCompletionRequestMixIn.class);
		mapper.addMixIn(ChatFunctionCall.class, ChatFunctionCallMixIn.class);
		return mapper;
	}
	

	/**
	 * 流式调用
	 */
	public Flowable<ModelData> streamCallWithMessage(String prompt) {
		log.info("提示词：prompt{}" , prompt);
		
		List<ChatMessage> messages = new ArrayList<>();

		ChatMessage chatMessage = new ChatMessage(ChatMessageRole.USER.value(), prompt);
		messages.add(chatMessage);

		//Constants.ModelChatGLM4代表GLM4大模型
		ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder().model("GLM-4-Plus")
				.stream(Boolean.TRUE).messages(messages)
				.requestId(String.format(requestIdTemplate, System.currentTimeMillis())).build();

		ModelApiResponse sseModelApiResp = client.invokeModelApi(chatCompletionRequest);
		if (sseModelApiResp.isSuccess()) {
			Flowable<ModelData> result =sseModelApiResp.getFlowable();
			return result;
		}else {
			return null;
		}
		
	}
	
	
	/**
	* 同步调用
	*/
	public void testInvoke() {
	   List<ChatMessage> messages = new ArrayList<>();
	   ChatMessage chatMessage = new ChatMessage(ChatMessageRole.USER.value(), "作为一名营销专家，请为智谱开放平台创作一个吸引人的slogan");
	   messages.add(chatMessage);
	   String requestId = String.format(requestIdTemplate, System.currentTimeMillis());
	   
	   ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
	           .model(Constants.ModelChatGLM4)
	           .stream(Boolean.FALSE)
	           .invokeMethod(Constants.invokeMethod)
	           .messages(messages)
	           .requestId(requestId)
	           .build();
	   ModelApiResponse invokeModelApiResp = client.invokeModelApi(chatCompletionRequest);
	   
	   try {
	       System.out.println("model output:" + mapper.writeValueAsString(invokeModelApiResp));
	   } catch (JsonProcessingException e) {
	       e.printStackTrace();
	   }
	}

}
