package com.taurus.llm.component;

/**
 * 加载模版列表到缓存
 */

import java.util.HashMap;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;

import com.taurus.llm.pojo.PromptTemplate;
import com.taurus.llm.service.PromptTemplateService;

public class PromptTemplateMapInitiation implements ApplicationRunner {
	
	@Autowired
	private PromptTemplateService promptTemplateService;

	@Override
	public void run(ApplicationArguments args) throws Exception {
		HashMap<String,Object> templateMap = new HashMap<>();
		
		//text to questions
		String promptContent = promptTemplateService.readTemplate("prompt-t2q.txt");
		templateMap.put("prompt-t2q", promptContent);
		
		//
		promptContent = promptTemplateService.readTemplate("prompt-q2json.txt");
		templateMap.put("prompt-q2json", promptContent);
		
		promptContent = promptTemplateService.readTemplate("prompt-json-sel.txt");
		templateMap.put("prompt-json-sel", promptContent);
		
		promptContent = promptTemplateService.readTemplate("prompt-json-com.txt");
		templateMap.put("prompt-json-com", promptContent);
		
		promptContent = promptTemplateService.readTemplate("prompt-json-adj.txt");
		templateMap.put("prompt-json-adj", promptContent);
		
		promptContent = promptTemplateService.readTemplate("prompt-json-dis.txt");
		templateMap.put("prompt-json-dis", promptContent);
		
		promptContent = promptTemplateService.readTemplate("prompt-json-cop.txt");
		templateMap.put("prompt-json-cop", promptContent);
		
		PromptTemplate.templateMap=templateMap;
	}

}
