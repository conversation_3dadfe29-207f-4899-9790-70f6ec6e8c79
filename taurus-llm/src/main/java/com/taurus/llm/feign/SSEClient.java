package com.taurus.llm.feign;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.alibaba.fastjson.JSONObject;
import com.taurus.entity.ResponseObject;

@FeignClient(value = "taurus-sse") //这里的name对应调用服务的spring.application.name
@Component
public interface SSEClient {

	/**
	 * 获取objectName对应的url
	 * @param formId
	 * @return
	 */
    @PostMapping("/sseTest/send")
    public ResponseObject send(@RequestBody JSONObject body);
}
