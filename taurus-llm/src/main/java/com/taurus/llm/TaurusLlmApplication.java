package com.taurus.llm;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

@EnableDiscoveryClient
@EnableFeignClients
@ComponentScan("com.taurus")
@SpringBootApplication(exclude={DataSourceAutoConfiguration.class})
public class TaurusLlmApplication {
	

	public static void main(String[] args) {
		SpringApplication.run(TaurusLlmApplication.class, args);
	}

}
