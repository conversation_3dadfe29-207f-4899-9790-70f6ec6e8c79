package com.taurus.utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.nio.charset.StandardCharsets;

public class TextToMD5 {
    /**
     * 获取字符串的MD5值
     * @param text 需要转换为MD5的文本
     * @return 计算出的MD5值，16进制字符串表示
     */
    public static String getMD5(String text) {
        try {
            // 创建MessageDigest实例，指定算法为MD5
            MessageDigest md = MessageDigest.getInstance("MD5");
            
            // 将字符串转换为字节数组
            byte[] inputBytes = text.getBytes(StandardCharsets.UTF_8);
            
            // 计算MD5哈希值
            byte[] digestBytes = md.digest(inputBytes);
            
            // 将字节数组转换为16进制表示的字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : digestBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5 algorithm not found", e);
        }
    }

    public static void main(String[] args) {
        String text = "Hello, World!";
        String md5Value = getMD5(text);
        System.out.println("The MD5 value of the text \"" + text + "\" is: " + md5Value);
    }
}