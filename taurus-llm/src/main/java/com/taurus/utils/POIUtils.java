package com.taurus.utils;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.usermodel.Range;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

public class POIUtils {

	private static final Logger logger = LoggerFactory.getLogger(POIUtils.class);

	private static String MainContentPatternString = "([0-9]+[.、：:．）\\)])|(^第[0-9]+题[.、：:．]?)";

	/**
	 * 接收一个上传的word文档，并用poi包解析这个word文档，获取这个文档的所有内容
	 * 
	 * @param uploadedFile
	 * @return
	 */

	public static List<String> readWordContent(MultipartFile file) {
		String content = "";
		InputStream in = null;

		try {
			in = file.getInputStream();
			String fileName = file.getOriginalFilename();

			// 首先尝试按文件扩展名处理
			if (fileName != null && fileName.endsWith("docx")) {
				try {
					// 尝试作为OOXML格式（docx）处理
					XWPFDocument doc2007 = new XWPFDocument(in);
					try (XWPFWordExtractor extractor = new XWPFWordExtractor(doc2007)) {
						content = extractor.getText();
					} finally {
						doc2007.close();
					}
				} catch (org.apache.poi.openxml4j.exceptions.OLE2NotOfficeXmlFileException e) {
					// 如果是OLE2格式，则重新打开流并使用HWPFDocument处理
					logger.info("文件扩展名为docx但实际格式为doc，尝试使用HWPFDocument处理");
					closeQuietly(in);
					in = file.getInputStream(); // 重新获取输入流
					POIFSFileSystem fs = new POIFSFileSystem(in);
					HWPFDocument doc2003 = new HWPFDocument(fs);
					Range range = doc2003.getRange();
					content = range.text();
					doc2003.close();
				}
			} else {
				// 假设为doc格式
				try {
					// 尝试作为OLE2格式（doc）处理
					POIFSFileSystem fs = new POIFSFileSystem(in);
					HWPFDocument doc2003 = new HWPFDocument(fs);
					Range range = doc2003.getRange();
					content = range.text();
					doc2003.close();
				} catch (Exception e) {
					// 如果不是OLE2格式，可能是OOXML格式，重新打开流处理
					logger.info("文件扩展名为doc但可能是其他格式，尝试使用XWPFDocument处理");
					closeQuietly(in);
					in = file.getInputStream(); // 重新获取输入流
					XWPFDocument doc2007 = new XWPFDocument(in);
					try (XWPFWordExtractor extractor = new XWPFWordExtractor(doc2007)) {
						content = extractor.getText();
					} finally {
						doc2007.close();
					}
				}
			}
		} catch (Exception e) {
			logger.error("处理Word文档时出现异常", e);
		} finally {
			closeQuietly(in);
		}

		return splitContent(content);
	}

	/**
	 * 安全关闭输入流
	 */
	private static void closeQuietly(InputStream is) {
		if (is != null) {
			try {
				is.close();
			} catch (IOException e) {
				logger.error("关闭输入流时出现错误", e);
			}
		}
	}

	/**
	 * 分隔内容
	 * 
	 * @param content
	 * @return
	 */
	private static List<String> splitContent(String content) {
		List<String> contentList = new ArrayList<>();
		int totalLength = content.length();
		boolean flag = true;
		int beginIndex = 0;
		int step = 500;
		int endIndex = 500;

		while (flag) {
			if (beginIndex >= totalLength) {
				flag = false;
				break;
			}

			if (endIndex >= totalLength) {
				endIndex = totalLength;
				String subContent = content.substring(beginIndex, endIndex);
				logger.info("最后一个分隔：{}", subContent);
				contentList.add(subContent);
				flag = false;
				break;
			} else {
				String subContent = content.substring(beginIndex, endIndex);
				Pattern pattern = Pattern.compile(MainContentPatternString); // 包含这个模式
				Matcher matcher = pattern.matcher(subContent);

				Integer lastStartIndex = subContent.length();

				while (matcher.find()) {
					lastStartIndex = matcher.start();
				}

				// 调整后的endIndex
				if (lastStartIndex != 0)
					endIndex = beginIndex + lastStartIndex;

				subContent = content.substring(beginIndex, endIndex);
				logger.info("分隔后的：{}", subContent);
				contentList.add(subContent);

				// 构想下一次subString区间
				beginIndex = endIndex;
				endIndex = beginIndex + step;
			}
		}

		return contentList;
	}
}
