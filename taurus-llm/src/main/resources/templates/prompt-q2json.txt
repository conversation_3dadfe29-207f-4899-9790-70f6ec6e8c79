下面内容要求改写成json格式：{{{uContent}}},这段内容中包含了一些题目，可能的题型有选择题，填空题，判断题，问答题。请仔细分析这段内容，首先分割题目，
如何分割题目：
1.根据语义去分割题目，有依赖关系的内容，同属一道题；
2.严禁将选项拆分为题目；
分割好后，再去分辨题型：
如果一个题目要求在题干中补充缺失的内容，缺失的内容通常是有意义的字词，而不是英文字母，那么该题是填空题；
如果一个题目包含多个选项，选项内容和题干内容分离，选项用来填充题干缺失的内容或者解释题干或者回答题干的提问，并且选项数量大于题干中需要补充内容的位置数量时，那么该题为选择题；
如果一个题目，它的正确答案为：正确、对、√、错误、错、×️这些表示正确错误的词语或符号，那么该题是判断题；
如果一个题目，题干内容提出一个疑问，等待答题者发表自己的认知，那么该题为问答题；
还可以根据文字提示，得知文字后的题目题型；
各题型具体的JSON改写规则如下：{{{rule}}}。
一个题目用一个JSON对象字符串表示,如果多个题目，一定要用JSON数组表示。
最后再反思以下几点：
1. JSON字符串必须以```json开始，以```结束。
2. JSON字符串格式一定要符合标准，键值内容没有找到用空代替，不要添加注释注解提示。
3. 键名type的可能值有：SEL、COM、ADJ、DIS，不要出现其他值。
4. 所有键值内容均来自提供给你的题目内容里，不要编造新的内容。
5. 填空题的mainContent中至少包含一组{},并且{}里面包含题目要填写的内容，如果没有不符合要求。
6. 如果未发现任何题目，返回空JSON。
7. 不要在json中用//增加任何注释提示文字。
8. 只出现json，不要其他提示文字。
9. 不要编造内容。
10. 无法分辨题型时，优先选择题。
仔细分析下题型判断规则，开始吧