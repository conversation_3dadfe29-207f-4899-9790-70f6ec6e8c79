{{{uContent}}},这段内容中包含了若干考试题，可能的题型有选择题，填空题，判断题，问答题和组合题，不要出现其他题型。请仔细分析这段内容，首先分隔题目，不要搞错每道题目的界限。
题型判断依据如下：
如果一个题目有多个选项组成，选项一般以英文字母开头，那么该题为选择题；
如果一个题目没有可供答题人选择的英文字母开头的选项，而是需要答题人填写内容，那么该题为填空题；
如果一个题目，题干内容描述某种事实后让你判断正确错误，那么该题为判断题；
如果一个题目，题干内容提出一个疑问，等待答题者发表自己的认知，那么该题为问答题；
如果一个题目，先有一段为子题服务的公共材料内容，然后再划分了若干的子题来提问，子题是独立的选择题、填空题、判断题、问答题之一，这种包含了若干个子题的题型叫组合题；
还可以根据文字标记提示，得知标记位置后面的题目的题型；
各题型具体的JSON规则如下：{{{rule}}} 。
为每个题目用一个独立的JSON字符串表示，请输出 JSON 字符串,不要把多个题目放在JSON数组里，不要输出其他内容，如markdown中的```。