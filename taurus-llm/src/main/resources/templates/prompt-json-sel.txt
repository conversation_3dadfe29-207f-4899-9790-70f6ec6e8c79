如果是选择题，JSON字符串包含以下元素：
- mainContent:题目的主要内容，即题干，不能包含代表正确答案的选项字母。
- type:SEL。
- options:一个数组，每个对象代表一个选项。
 - text:表示选项内容，选项开头不要出现表示序号的字母。
 - isSelected:一个布尔值，表示该选项是否为正确答案。
- tipContent:答题解析内容。
- childType:单选题为S，多选题为M。

注意：保留题目表示答案位置的符号，如（）、()等等


以下是两个例子：
业务训练前热身运动，时间不少于（)
A.5分钟
B.10分钟
C.15分钟
D.30分钟
答案：D
用json表示为:{mainContent:"业务训练前热身运动，时间不少于（)",options:[{text:"5分钟",isSelected:false},{text:"10分钟",isSelected:false},{text:"30分钟",isSelected:false},{text:"10",isSelected:true}],tipContent:"",childType:"S"}。

水域下潜时经常观察压力表和浮力表，保持中性浮力，每下潜3米均需观察周边情况，气瓶压力低于（）MPa时必须上浮出水，速度不得大于18米/分钟。
A.3
B.5
C.7
D.10
答案：B
用json表示为:{mainContent:"水域下潜时经常观察压力表和浮力表，保持中性浮力，每下潜3米均需观察周边情况，气瓶压力低于（）MPa时必须上浮出水，速度不得大于18米/分钟。",options:[{text:"3",isSelected:false},{text:"5",isSelected:true},{text:"7",isSelected:false},{text:"10",isSelected:false}],tipContent:"",childType:"S"}

