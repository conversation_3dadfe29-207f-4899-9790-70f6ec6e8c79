spring.profiles.active=prod
spring.application.name=taurus-llm

server.port=8089
server.servlet.context-path:/


spring.cloud.nacos.discovery.service=taurus-llm

# actuator 监控配置
management.endpoints.enabled-by-default=false
management.endpoints.web.base-path: /activeEgoolanw98sjHpK


spring.main.allow-circular-references=true

server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true

#服务端禁用ribbon的hystrix
feign.hystrix.enabled=false
#当值为0或者大于0时，表示容器在应用启动时就加载并初始化这个servlet
spring.mvc.servlet.load-on-startup=100

spring.jmx.default-domain=taurus-llm

#spring.jpa.properties.hibernate.hbm2ddl.auto=update
#spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL5InnoDBDialect
#spring.jpa.show-sql=true
#Date对象的格式化
spring.jackson.date-format=yyyy/MM/dd HH:mm:ss
spring.jackson.time-zone=GMT+8

#log文件配置
logging.config=classpath:log4j2.xml


####redis####
spring.redis.database=1
spring.redis.host=*************
spring.redis.port=6379

spring.redis.password=jfksio&^%^2fjlsfdh217^%02
# 连接池最大连接数（使用负值表示没有限制）
spring.redis.jedis.pool.max-active=1000
# 连接池最大阻塞等待时间（使用负值表示没有限制）
spring.redis.jedis.pool.max-wait=-1
# 连接池中的最大空闲连接
spring.redis.jedis.pool.max-idle=10
# 连接池中的最小空闲连接
spring.redis.jedis.pool.min-idle=0
# 连接超时时间（毫秒）
spring.redis.timeout=5000
