spring.cloud.nacos.discovery.server-addr=localhost:8848
spring.cloud.nacos.discovery.enabled=true
spring.cloud.nacos.discovery.username=nacos
spring.cloud.nacos.discovery.password=9945xqyg

#饥饿加载模式
ribbon.eager-load.enabled=true
ribbon.eager-load.clients=taurus-formSystem

#服务端禁用ribbon的hystrix
feign.hystrix.enabled=false
#当值为0或者大于0时，表示容器在应用启动时就加载并初始化这个servlet
spring.mvc.servlet.load-on-startup=100

spring.jmx.default-domain=taurus-llm

#spring.jpa.properties.hibernate.hbm2ddl.auto=update
#spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL5InnoDBDialect
#spring.jpa.show-sql=true
#Date对象的格式化
spring.jackson.date-format=yyyy/MM/dd HH:mm:ss
spring.jackson.time-zone=GMT+8

#log文件配置
logging.config=classpath:log4j2-dev.xml

####redis####
spring.redis.database=1
spring.redis.host=**************
#spring.redis.host=127.0.0.1
spring.redis.port=6379
spring.redis.password=jfksio&^%^2fjlsfdh217^%02
