package com.taurus.utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import com.alibaba.fastjson.JSONObject;

public class MapUtil {
	/**
	 * 将Map中的key由下划线转换为驼峰
	 *
	 * @param map
	 * @return
	 */
	public static Map<String, Object> formatHumpName(Map<String, Object> map) {
		Map<String, Object> newMap = new HashMap<String, Object>();
		Iterator<Map.Entry<String, Object>> it = map.entrySet().iterator();
		while (it.hasNext()) {
			Map.Entry<String, Object> entry = it.next();
			String key = entry.getKey();
			String newKey = toFormatCol(key);
			newMap.put(newKey, entry.getValue());
		}
		return newMap;
	}

	public static String toFormatCol(String colName) {
		StringBuilder sb = new StringBuilder();
		String[] str = colName.toLowerCase().split("_");
		int i = 0;
		for (String s : str) {
			if (s.length() == 1) {
				s = s.toUpperCase();
			}
			i++;
			if (i == 1) {
				sb.append(s);
				continue;
			}
			if (s.length() > 0) {
				sb.append(s.substring(0, 1).toUpperCase());
				sb.append(s.substring(1));
			}
		}
		return sb.toString();
	}

	/**
	 * 将List中map的key值命名方式格式化为驼峰
	 *
	 * @param
	 * @return
	 */
	public static List<Map<String, Object>> formatHumpNameForList(List<Map<String, Object>> list) {
		List<Map<String, Object>> newList = new ArrayList<Map<String, Object>>();
		for (Map<String, Object> o : list) {
			newList.add(formatHumpName(o));
		}
		return newList;
	}
	
	/**
	 * JSONObject 转 Map<String,Object>
	 * @return
	 */
	public static Map<String,Object> jsonObjectToMap(JSONObject obj){
		//map对象
		Map<String, Object> map =new HashMap<>();
		//循环转换
		 Iterator<Entry<String, Object>> it =obj.entrySet().iterator();
		 while (it.hasNext()) {
		       Map.Entry<String, Object> entry = (Entry<String, Object>) it.next();
		       map.put(entry.getKey(), entry.getValue());
		 }
		 return map;
	}
}
