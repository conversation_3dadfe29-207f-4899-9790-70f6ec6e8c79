package com.taurus.comment.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Select;

import com.gitee.fastmybatis.core.mapper.CrudMapper;
import com.taurus.comment.entity.LeaveWord;


/**
 * <AUTHOR>
 */
public interface LeaveWordMapper extends <PERSON>rudMapper<LeaveWord, Integer> {

	/**
	 * 获取留言列表
	 * @param projectId
	 * @param moduleId
	 * @param sourceId
	 * @param userId
	 * @param userIdPraised
	 * @param level
	 * @param display
	 * @return
	 */
	@Select("<script>"
			+"(select t.id,t.project_id as projectId,t.module_id as moduleId,t.source_id as sourceId,t.level,t.insert_time as insertTime,t.user_id as userId,t.reply_to_id as replyToId,t.text,t1.total_praised_num as totalPraisedNum "
			+ "<if test='userIdPraised!=null'>"
			+ ",!ISNULL(t2.insert_time) as praised "
			+ "</if>"
			+ "from leave_word t "
			+ "LEFT JOIN leave_word_summary t1 ON t1.leave_word_id=t.id "
			+ "<if test='userIdPraised!=null'>"
			+ "LEFT JOIN leave_word_praised t2 ON t.id= t2.leave_word_id and t2.user_id=#{userIdPraised} "
			+ "</if>"
			+ "where t.project_id=#{projectId} and t.module_id =#{moduleId} and t.level&lt;=#{level} "
			+ "<if test='sourceId!=null'>"
			+ "and t.source_id=#{sourceId} "
			+ "</if>"
			+ "<if test='userId!=null'>"
			+ "and t.user_id=#{userId} "
			+ "</if>"
			+ "<if test='display!=null'>"
			+ "and t.display=#{display} "
			+ "</if>"
			+ ")"
			//某个用户可以看到所有display为true的留言外，还可以看到自己的所有留言，无论diaplay状态值是true 或者false
			+ "<if test='userIdPraised!=null'>"
			+ " union ("
			+"select t.id,t.project_id as projectId,t.module_id as moduleId,t.source_id as sourceId,t.level,t.insert_time as insertTime,t.user_id as userId,t.reply_to_id as replyToId,t.text,t1.total_praised_num as totalPraisedNum"
			+ ",!ISNULL(t2.insert_time) as praised "
			+ " from leave_word t "
			+ "LEFT JOIN leave_word_summary t1 ON t1.leave_word_id=t.id "
			+ "LEFT JOIN leave_word_praised t2 ON t.id= t2.leave_word_id and t2.user_id=#{userIdPraised} "
			+ "where t.project_id=#{projectId} and t.module_id =#{moduleId} and t.level&lt;=#{level} "
			+ "<if test='sourceId!=null'>"
			+ "and t.source_id=#{sourceId} "
			+ "</if>"
			+ "and t.user_id=#{userIdPraised} "
			+ ") "
			+ "</if>"
			+ " order by insertTime desc"
			+ "</script>")
	List<Map<String, Object>> getLevelLeaveWordOfLessThanOrEqualDesignativeLevel(Integer projectId, Integer moduleId,
			Integer sourceId, Integer userId, Integer userIdPraised, Integer level, Boolean display);
}
