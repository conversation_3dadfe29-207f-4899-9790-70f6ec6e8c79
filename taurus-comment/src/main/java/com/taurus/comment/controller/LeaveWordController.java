package com.taurus.comment.controller;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.druid.util.StringUtils;
import com.taurus.comment.entity.LeaveWord;
import com.taurus.comment.service.LeaveWordService;
import com.taurus.comment.service.WeChatMiniProgramService;
import com.taurus.entity.ResponseObject;

@RestController
@RequestMapping("/leaveWord")
public class LeaveWordController {
	private static Logger log = LoggerFactory.getLogger(LeaveWordController.class);
	
	@Autowired
	private LeaveWordService leaveWordService;
	
	@Autowired
	private WeChatMiniProgramService weChatMiniProgramService;
	
	@PostMapping("/save")
	public ResponseObject save(@RequestBody LeaveWord word) {
		Integer level = word.getLevel();
		Integer projectId = word.getProjectId();
		Integer moduleId = word.getModuleId();
		Integer sourceId = word.getSourceId();
		String text = word.getText();
		Integer userId = word.getUserId();
		if(level==null||level==0||projectId==null||projectId==0||moduleId==null||moduleId==0||sourceId==null||sourceId==0||StringUtils.isEmpty(text)||userId==null||userId==0) {
			return ResponseObject.failure("PARAMETERS_ERROR");
		}
		Boolean display = word.getDisplay();
		if(display==null) word.setDisplay(true);
		
		ResponseObject res= weChatMiniProgramService.msgSecCheck("etea",text);
		if(!res.getCode().equals("1")) {
			log.debug(res.getMessage());
			return res;
		}else{
			word.setInsertTime(new Date());
			leaveWordService.save(word);
			return ResponseObject.success(word);
		}
	}
	
	/**
	 * 获取N层递归结构留言数据
	 * @param projectId
	 * @param moduleId
	 * @param sourceId(非必要)
	 * @param userId
	 * @param userIdPraised
	 * @param level
	 * @return
	 */
	@GetMapping("/getRecursionLevelLeaveWord")
	public ResponseObject getRecursionLevelLeaveWordOfLessThanOrEqualDesignativeLevel(Integer projectId,Integer moduleId,@RequestParam(required=false)Integer sourceId,@RequestParam(required=false)Integer userId,@RequestParam(required=false)Integer userIdPraised, Integer level,@RequestParam(required=false)Boolean display) {
		if(level==null||level==0||projectId==null||projectId==0||moduleId==null||moduleId==0||sourceId==0) {
			return ResponseObject.failure("PARAMETERS_ERROR");
		}
		
		List<Map<String, Object>> list = leaveWordService.getRecursionLevelLeaveWordOfLessThanOrEqualDesignativeLevel(projectId,moduleId,sourceId,userId,userIdPraised,level,display);
		return ResponseObject.success(list);
	}
	
	/**
	 * 删除某条留言
	 * @param id
	 * @return
	 */
	@GetMapping("/deleteById")
	public ResponseObject deleteById(Integer id) {
		if(id==null||id==0) {
			return ResponseObject.failure("PARAMETERS_ERROR");
		}
		leaveWordService.deleteById(id);
		return ResponseObject.success(true);
	}
}
