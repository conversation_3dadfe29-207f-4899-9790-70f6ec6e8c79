package com.taurus.comment.controller;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.taurus.comment.entity.LeaveWordPraised;
import com.taurus.comment.service.LeaveWordPraisedService;
import com.taurus.comment.service.LeaveWordSummaryService;
import com.taurus.entity.ResponseObject;

@RestController
@RequestMapping("/leaveWordPraised")
public class LeaveWordPraisedController {
	@Autowired
	private LeaveWordPraisedService leaveWordPraisedService;
	
	@Autowired
	private LeaveWordSummaryService leaveWordSummaryService;
	
	/**
	 * 点赞
	 * @param userId
	 * @param leaveWordId
	 * @return
	 */
	@GetMapping("/addPraise")
	@Transactional
	public ResponseObject addPraise(Integer userId,Integer leaveWordId) {
		if(userId==null||userId==0||leaveWordId==null||leaveWordId==0) {
			return ResponseObject.failure("PARAMETERS_ERROR");
		}
		LeaveWordPraised praised = leaveWordPraisedService.getEntity(userId,leaveWordId);
		if(praised==null) {
			praised = new LeaveWordPraised();
			praised.setLeaveWordId(leaveWordId);
			praised.setUserId(userId);
			praised.setInsertTime(new Date());
			leaveWordPraisedService.save(praised);
			leaveWordSummaryService.increasePraisedNumber(leaveWordId);
		}
		return ResponseObject.success(true);
	}
	
	/**
	 * 取消点赞
	 * @param userId
	 * @param leaveWordId
	 * @return
	 */
	@GetMapping("/cancelPraise")
	@Transactional
	public ResponseObject cancelPraise(Integer userId,Integer leaveWordId) {
		if(userId==null||userId==0||leaveWordId==null||leaveWordId==0) {
			return ResponseObject.failure("PARAMETERS_ERROR");
		}
		LeaveWordPraised praised = leaveWordPraisedService.getEntity(userId,leaveWordId);
		if(praised!=null) {
			leaveWordPraisedService.delete(userId,leaveWordId);
			leaveWordSummaryService.decreasePraisedNumber(leaveWordId);
		}
		return ResponseObject.success(true);
	}
}
