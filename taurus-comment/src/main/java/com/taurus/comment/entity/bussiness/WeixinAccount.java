package com.taurus.comment.entity.bussiness;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;


/**
 * 表名：weixin_account
 */
@Table(name = "weixin_account")
public class WeixinAccount {

	@Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    /**  数据库字段：id */
    private Integer id;

    /** 微信账户名, 数据库字段：account */
    private String account;

    /** appid, 数据库字段：app_id */
    private String appId;

    /** 密钥, 数据库字段：app_secret */
    private String appSecret;

    /** 令牌, 数据库字段：token */
    private String token;

    /**  数据库字段：weixin_account.id */
    public void setId(Integer id) {
        this.id = id;
    }

    /**  数据库字段：weixin_account.id */
    public Integer getId() {
        return this.id;
    }

    /** 设置微信账户名, 数据库字段：weixin_account.account */
    public void setAccount(String account) {
        this.account = account;
    }

    /** 获取微信账户名, 数据库字段：weixin_account.account */
    public String getAccount() {
        return this.account;
    }

    /** 设置appid, 数据库字段：weixin_account.app_id */
    public void setAppId(String appId) {
        this.appId = appId;
    }

    /** 获取appid, 数据库字段：weixin_account.app_id */
    public String getAppId() {
        return this.appId;
    }

    /** 设置密钥, 数据库字段：weixin_account.app_secret */
    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    /** 获取密钥, 数据库字段：weixin_account.app_secret */
    public String getAppSecret() {
        return this.appSecret;
    }

    /** 设置令牌, 数据库字段：weixin_account.token */
    public void setToken(String token) {
        this.token = token;
    }

    /** 获取令牌, 数据库字段：weixin_account.token */
    public String getToken() {
        return this.token;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = (prime * result) + ((id == null) ? 0 : id.hashCode());

        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        if (obj == null) {
            return false;
        }

        if (getClass() != obj.getClass()) {
            return false;
        }

        WeixinAccount other = (WeixinAccount) obj;

        if (id == null) {
            if (other.id != null) {
                return false;
            }
        } else if (!id.equals(other.id)) {
            return false;
        }

        return true;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("WeixinAccount [");
        sb.append("id=").append(id);
        sb.append(", ");
        sb.append("account=").append(account);
        sb.append(", ");
        sb.append("appId=").append(appId);
        sb.append(", ");
        sb.append("appSecret=").append(appSecret);
        sb.append(", ");
        sb.append("token=").append(token);
        sb.append("]");

        return sb.toString();
    }
}
