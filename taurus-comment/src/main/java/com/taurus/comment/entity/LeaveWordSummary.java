package com.taurus.comment.entity;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;


/**
 * 表名：leave_word_summary
 * 备注：留言数据汇总表
 *
 * <AUTHOR>
 */
@Table(name = "leave_word_summary")
public class LeaveWordSummary {
    /** 留言id, 数据库字段：leave_word_id */
    @Id
    @Column(name = "leave_word_id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Integer leaveWordId;

    /** 获赞总数, 数据库字段：total_praised_num */
    private Integer totalPraisedNum;

    /** 设置留言id, 数据库字段：leave_word_summary.leave_word_id */
    public void setLeaveWordId(Integer leaveWordId) {
        this.leaveWordId = leaveWordId;
    }

    /** 获取留言id, 数据库字段：leave_word_summary.leave_word_id */
    public Integer getLeaveWordId() {
        return this.leaveWordId;
    }

    /** 设置获赞总数, 数据库字段：leave_word_summary.total_praised_num */
    public void setTotalPraisedNum(Integer totalPraisedNum) {
        this.totalPraisedNum = totalPraisedNum;
    }

    /** 获取获赞总数, 数据库字段：leave_word_summary.total_praised_num */
    public Integer getTotalPraisedNum() {
        return this.totalPraisedNum;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = (prime * result) +
            ((leaveWordId == null) ? 0 : leaveWordId.hashCode());

        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        if (obj == null) {
            return false;
        }

        if (getClass() != obj.getClass()) {
            return false;
        }

        LeaveWordSummary other = (LeaveWordSummary) obj;

        if (leaveWordId == null) {
            if (other.leaveWordId != null) {
                return false;
            }
        } else if (!leaveWordId.equals(other.leaveWordId)) {
            return false;
        }

        return true;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("LeaveWordSummary [");
        sb.append("leaveWordId=").append(leaveWordId);
        sb.append(", ");
        sb.append("totalPraisedNum=").append(totalPraisedNum);
        sb.append("]");

        return sb.toString();
    }
}
