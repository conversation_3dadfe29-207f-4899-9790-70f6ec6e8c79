package com.taurus.comment.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;


/**
 * 表名：leave_word_praised
 * 备注：留言获赞记录表
 *
 * <AUTHOR>
 */
@Table(name = "leave_word_praised")
public class LeaveWordPraised {
    /** 用户id, 数据库字段：user_id */
    @Id
    @Column(name = "user_id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Integer userId;

    /** 留言id, 数据库字段：leave_word_id */
    @Id
    @Column(name = "leave_word_id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Integer leaveWordId;

    /** 记录时间, 数据库字段：insert_time */
    private Date insertTime;

    /** 设置用户id, 数据库字段：leave_word_praised.user_id */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /** 获取用户id, 数据库字段：leave_word_praised.user_id */
    public Integer getUserId() {
        return this.userId;
    }

    /** 设置留言id, 数据库字段：leave_word_praised.leave_word_id */
    public void setLeaveWordId(Integer leaveWordId) {
        this.leaveWordId = leaveWordId;
    }

    /** 获取留言id, 数据库字段：leave_word_praised.leave_word_id */
    public Integer getLeaveWordId() {
        return this.leaveWordId;
    }

    /** 设置记录时间, 数据库字段：leave_word_praised.insert_time */
    public void setInsertTime(Date insertTime) {
        this.insertTime = insertTime;
    }

    /** 获取记录时间, 数据库字段：leave_word_praised.insert_time */
    public Date getInsertTime() {
        return this.insertTime;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = (prime * result) + ((userId == null) ? 0 : userId.hashCode());

        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        if (obj == null) {
            return false;
        }

        if (getClass() != obj.getClass()) {
            return false;
        }

        LeaveWordPraised other = (LeaveWordPraised) obj;

        if (userId == null) {
            if (other.userId != null) {
                return false;
            }
        } else if (!userId.equals(other.userId)) {
            return false;
        }

        return true;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("LeaveWordPraised [");
        sb.append("userId=").append(userId);
        sb.append(", ");
        sb.append("leaveWordId=").append(leaveWordId);
        sb.append(", ");
        sb.append("insertTime=").append(insertTime);
        sb.append("]");

        return sb.toString();
    }
}
