package com.taurus.comment.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;


/**
 * 表名：leave_word
 * 备注：留言内容表
 *
 * <AUTHOR>
 */
@Table(name = "leave_word")
public class LeaveWord {
    /** 留言ID, 数据库字段：id */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /** 项目id, 数据库字段：project_id */
    private Integer projectId;

    /** 模块id, 数据库字段：module_id */
    private Integer moduleId;

    /** 模块下的资源id, 数据库字段：source_id */
    private Integer sourceId;

    /** 留言用户ID, 数据库字段：user_id */
    private Integer userId;

    /** 父留言id, 数据库字段：reply_to_id */
    private Integer replyToId;
    
    /** 层级, 数据库字段：level */
    private Integer level;

    /** 回复内容, 数据库字段：text */
    private String text;

    /**  留言时间, 数据库字段：insert_time */
    private Date insertTime;
    
    /** 是否显示，数据库字段：display */
    private Boolean display;

    /** 设置留言ID, 数据库字段：leave_word.id */
    public void setId(Integer id) {
        this.id = id;
    }

    /** 获取留言ID, 数据库字段：leave_word.id */
    public Integer getId() {
        return this.id;
    }

    /** 设置项目id, 数据库字段：leave_word.project_id */
    public void setProjectId(Integer projectId) {
        this.projectId = projectId;
    }

    /** 获取项目id, 数据库字段：leave_word.project_id */
    public Integer getProjectId() {
        return this.projectId;
    }

    /** 设置模块id, 数据库字段：leave_word.module_id */
    public void setModuleId(Integer moduleId) {
        this.moduleId = moduleId;
    }

    /** 获取模块id, 数据库字段：leave_word.module_id */
    public Integer getModuleId() {
        return this.moduleId;
    }

    /** 设置模块下的资源id, 数据库字段：leave_word.source_id */
    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    /** 获取模块下的资源id, 数据库字段：leave_word.source_id */
    public Integer getSourceId() {
        return this.sourceId;
    }

    /** 设置留言用户ID, 数据库字段：leave_word.user_id */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /** 获取留言用户ID, 数据库字段：leave_word.user_id */
    public Integer getUserId() {
        return this.userId;
    }

    /** 设置父留言id, 数据库字段：leave_word.reply_to_id */
    public void setReplyToId(Integer replyToId) {
        this.replyToId = replyToId;
    }

    /** 获取父留言id, 数据库字段：leave_word.reply_to_id */
    public Integer getReplyToId() {
        return this.replyToId;
    }

    /** 设置回复内容, 数据库字段：leave_word.text */
    public void setText(String text) {
        this.text = text;
    }

    /** 获取回复内容, 数据库字段：leave_word.text */
    public String getText() {
        return this.text;
    }

    /** 设置 留言时间, 数据库字段：leave_word.insert_time */
    public void setInsertTime(Date insertTime) {
        this.insertTime = insertTime;
    }
    


    /** 获取 留言时间, 数据库字段：leave_word.insert_time */
    public Date getInsertTime() {
        return this.insertTime;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = (prime * result) + ((id == null) ? 0 : id.hashCode());

        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        if (obj == null) {
            return false;
        }

        if (getClass() != obj.getClass()) {
            return false;
        }

        LeaveWord other = (LeaveWord) obj;

        if (id == null) {
            if (other.id != null) {
                return false;
            }
        } else if (!id.equals(other.id)) {
            return false;
        }

        return true;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("LeaveWord [");
        sb.append("id=").append(id);
        sb.append(", ");
        sb.append("projectId=").append(projectId);
        sb.append(", ");
        sb.append("moduleId=").append(moduleId);
        sb.append(", ");
        sb.append("sourceId=").append(sourceId);
        sb.append(", ");
        sb.append("userId=").append(userId);
        sb.append(", ");
        sb.append("replyToId=").append(replyToId);
        sb.append(", ");
        sb.append("level=").append(level);
        sb.append(", ");
        sb.append("text=").append(text);
        sb.append(", ");
        sb.append("insertTime=").append(insertTime);
        sb.append(", ");
        sb.append("display=").append(display);
        sb.append("]");

        return sb.toString();
    }

	public Integer getLevel() {
		return level;
	}

	public void setLevel(Integer level) {
		this.level = level;
	}

	public Boolean getDisplay() {
		return display;
	}

	public void setDisplay(Boolean display) {
		this.display = display;
	}
}
