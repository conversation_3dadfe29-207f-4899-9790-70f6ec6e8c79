package com.taurus.comment.entity.bussiness;

import com.taurus.comment.entity.LeaveWord;


public class LeaveWordWrapper extends LeaveWord{
	
	private String avatarUrl;//用户头像地址
	private String userName;//用户姓名
	private String nickname;//用户昵称
	
	private Boolean praised;//是否被赞
	
	private Integer totalPraisedNum;//留言被点赞总次数

	public String getAvatarUrl() {
		return avatarUrl;
	}

	public void setAvatarUrl(String avatarUrl) {
		this.avatarUrl = avatarUrl;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getNickname() {
		return nickname;
	}

	public void setNickname(String nickname) {
		this.nickname = nickname;
	}

	public Integer getTotalPraisedNum() {
		return totalPraisedNum;
	}

	public void setTotalPraisedNum(Integer totalPraisedNum) {
		this.totalPraisedNum = totalPraisedNum;
	}

	public Boolean getPraised() {
		return praised;
	}

	public void setPraised(<PERSON><PERSON><PERSON> praised) {
		this.praised = praised;
	}
	
}
