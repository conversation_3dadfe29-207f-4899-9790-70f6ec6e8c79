package com.taurus.comment.service;

import java.io.IOException;
import java.io.InputStream;
import java.security.AlgorithmParameters;
import java.security.Security;
import java.util.Arrays;
import java.util.HashMap;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.ContentType;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.util.encoders.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSONObject;
import com.taurus.comment.entity.bussiness.WeixinAccount;
import com.taurus.entity.ResponseObject;
import com.taurus.redis.service.RedisService;
import com.taurus.utils.HttpUtil;
import com.taurus.wechat.pojo.BaseAccessToken;


@Service
public class WeChatMiniProgramService {

	private static Logger log = LoggerFactory.getLogger(WeChatMiniProgramService.class);
	
	private static HashMap<String,WeixinAccount>  weixinAccountMap =getWeixinAccount();
	
	//装载微信账号参数数据，此处使用了全民考试助手小程序的账号数据
	private static HashMap<String, WeixinAccount> getWeixinAccount() {
		HashMap<String, WeixinAccount> map = new HashMap<>();
		WeixinAccount account = new WeixinAccount();
		account.setId(1);
		account.setAccount("etea");
		account.setAppId("wxa38e954efdceffbe");
		account.setAppSecret("9bd01d2e11ae1595665a823935adb449");
		map.put("etea", account);
		return map;
	}

	@Value("${wx.grantType}")
	private String grantType;

	@Value("${wx.requestUrl}")
	private String requestUrl;

	@Value("${wx.accessTokenUrl}")
	private String accessTokenUrl;
	
	@Value("${wx.qrCodeUnlimitedUrl}")
	private String qrCodeUnlimitedUrl;
	
	@Value("${wx.msgSecCheckUrl}")
	private String msgSecCheckUrl;

	@Value("${wx.imgSecCheckUrl}")
	private String imgSecCheckUrl;
	
	
	@Autowired
	private RedisService redisService;
	
	@Autowired
	private OkHttpService okHttpService;
	
	/**
	 * 获取Session对象
	 * @param code
	 * @param account
	 * @return
	 */
	public String getSession(String account,String code) {
		WeixinAccount weixinAccount = weixinAccountMap.get(account);
		String appId = weixinAccount.getAppId();
		String appSecret = weixinAccount.getAppSecret();
		String url = this.requestUrl + "?appid=" + appId + "&secret=" + appSecret + "&js_code=" + code + "&grant_type="
				+ grantType;
		return HttpUtil.get(url);
	}
	
	
	


	/**
	 * 根据account获取accessToken
	 * 
	 * @param account
	 * @return
	 * @throws IOException
	 */
	public String getAccessTokenByAccount(String accountName) throws IOException {
		
		// 查询redis缓存
		String existedAccessToken = redisService.getBaseAccessToken(accountName);
		if (existedAccessToken != null) {
			BaseAccessToken baseToken = JSONObject.parseObject(existedAccessToken,BaseAccessToken.class);
			return baseToken.getAccessToken();
		}else {
			WeixinAccount weixinAccount = weixinAccountMap.get(accountName);
			String appId = weixinAccount.getAppId();
			String appSecret = weixinAccount.getAppSecret();
			String url = accessTokenUrl.replace("APPID", appId).replace("APPSECRET", appSecret);
			String str = okHttpService.doGet(url);
			JSONObject jsonObject = JSONObject.parseObject(str);
			String accessToken = jsonObject.getString("access_token");
			// 将微信返回的accessToken放到redis缓存中
			redisService.saveBaseAccessToken(str, accountName);
			return accessToken;
		}
		
	}

	/**
	 * 检查是否包含违法违规内容
	 * 
	 * @param account
	 * @param content
	 * @return
	 */
	public ResponseObject msgSecCheck(String account, String content) {
		ResponseObject responseObject = new ResponseObject();
		try {
			String accessToken = getAccessTokenByAccount(account);
			String url = msgSecCheckUrl.replace("ACCESS_TOKEN", accessToken);
			JSONObject json = new JSONObject();
			json.put("content", content);
			log.debug(content);
			String response = okHttpService.doPostJson(url, json.toJSONString());
			JSONObject responseJson = JSONObject.parseObject(response);
			Integer errcode = (Integer) responseJson.get("errcode");
			String errMsg = (String)responseJson.get("errmsg");
			if (errcode.intValue() == 0) {
				responseObject.setSuccess("");
				return responseObject;
			} else if (errcode.intValue() == 87014) {
				responseObject.setCode("reject");
				responseObject.setMessage("以下内容涉嫌违法违规，请检查后提交("+errMsg+")");
				return responseObject;
			} else {
				responseObject.setSuccess("");
				return responseObject;
			}
		} catch (IOException e) {
			return ResponseObject.failure("IO_EXCEPTION");
		}
	}
	
	
	/**
	 * 恶意图片过滤
	 * 
	 * @param account
	 * @param multipartFile
	 * @return
	 */
	public ResponseObject imgSecCheck(String account, MultipartFile multipartFile) {
		ResponseObject responseObject = new ResponseObject();
		try {

			CloseableHttpClient httpclient = HttpClients.createDefault();
			CloseableHttpResponse response = null;

			String accessToken = getAccessTokenByAccount(account);
			String url = imgSecCheckUrl.replace("ACCESS_TOKEN", accessToken);
			HttpPost request = new HttpPost(url);
			request.addHeader("Content-Type", "application/octet-stream");

			InputStream inputStream = multipartFile.getInputStream();

			byte[] byt = new byte[inputStream.available()];
			inputStream.read(byt);
			request.setEntity(new ByteArrayEntity(byt, ContentType.create("image/jpg")));

			response = httpclient.execute(request);
			HttpEntity httpEntity = response.getEntity();
			String result = EntityUtils.toString(httpEntity, "UTF-8");// 转成string
			JSONObject jso = JSONObject.parseObject(result);

			Object errcode = jso.get("errcode");
			int errCode = (int) errcode;
			if (errCode == 0) {
				responseObject.setSuccess("");
				return responseObject;
			} else if (errCode == 87014) {
				responseObject.setCode("reject");
				responseObject.setMessage("含有违法违规内容，请检查后提交");
				return responseObject;
			}
			responseObject.setSuccess("");
			return responseObject;
		} catch (IOException e) {
			log.info("腾讯图片检查接口无法连接");
			
			return ResponseObject.failure("IO_EXCEPTION");
		}
	}
	
	
	/**
	 * 小程序解密用户数据
	 * 
	 * @param encryptedData
	 * @param sessionKey
	 * @param iv
	 * @return
	 */
	public JSONObject decryptionEncryptInfo(String encryptedData, String sessionKey, String iv) {
		// 被加密的数据
		byte[] dataByte = Base64.decode(encryptedData);
		// 加密秘钥
		byte[] keyByte = Base64.decode(sessionKey);
		// 偏移量
		byte[] ivByte = Base64.decode(iv);

		try {
			// 如果密钥不足16位，那么就补足. 这个if 中的内容很重要
			int base = 16;
			if (keyByte.length % base != 0) {
				int groups = keyByte.length / base + (keyByte.length % base != 0 ? 1 : 0);
				byte[] temp = new byte[groups * base];
				Arrays.fill(temp, (byte) 0);
				System.arraycopy(keyByte, 0, temp, 0, keyByte.length);
				keyByte = temp;
			}
			// 初始化
			Security.addProvider(new BouncyCastleProvider());
			Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding", "BC");
			SecretKeySpec spec = new SecretKeySpec(keyByte, "AES");
			AlgorithmParameters parameters = AlgorithmParameters.getInstance("AES");
			parameters.init(new IvParameterSpec(ivByte));
			cipher.init(Cipher.DECRYPT_MODE, spec, parameters);// 初始化
			byte[] resultByte = cipher.doFinal(dataByte);
			if (null != resultByte && resultByte.length > 0) {
				String result = new String(resultByte, "UTF-8");
				return JSONObject.parseObject(result);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	

}
