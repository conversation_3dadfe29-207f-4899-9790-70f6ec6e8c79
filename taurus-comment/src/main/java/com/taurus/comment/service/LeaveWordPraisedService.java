package com.taurus.comment.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.gitee.fastmybatis.core.query.Query;
import com.taurus.comment.entity.LeaveWordPraised;
import com.taurus.comment.mapper.LeaveWordPraisedMapper;

@Service
public class LeaveWordPraisedService {
	@Autowired
	private LeaveWordPraisedMapper leaveWordPraisedMapper;
	
	public void save(LeaveWordPraised praised) {
		leaveWordPraisedMapper.saveIgnoreNull(praised);
	}
	
	/**
	 * 获取一条记录
	 * @param userId
	 * @param leaveWordId
	 * @return
	 */
	public LeaveWordPraised getEntity(Integer userId, Integer leaveWordId) {
		Query query = new Query().eq("user_id", userId).eq("leave_word_id", leaveWordId);
		return leaveWordPraisedMapper.getByQuery(query);
		
	}

	/**
	 * 物理删除
	 * @param userId
	 * @param leaveWordId
	 */
	public void delete(Integer userId, Integer leaveWordId) {
		Query query = new Query();
		if(userId!=null&&userId!=0) {
			query.eq("user_id", userId);
		}
		if(leaveWordId!=null&&leaveWordId!=0) {
			query.eq("leave_word_id", leaveWordId);
		}
		
		leaveWordPraisedMapper.deleteByQuery(query);
	}
}
