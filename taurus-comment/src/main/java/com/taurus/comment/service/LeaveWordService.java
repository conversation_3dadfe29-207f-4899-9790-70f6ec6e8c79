package com.taurus.comment.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.gitee.fastmybatis.core.util.MyBeanUtil;
import com.taurus.comment.entity.LeaveWord;
import com.taurus.comment.entity.bussiness.LeaveWordWrapper;
import com.taurus.comment.feignClient.EteaFeignClient;
import com.taurus.comment.mapper.LeaveWordMapper;
import com.taurus.entity.ResponseObject;

@Service
public class LeaveWordService {

	@Autowired
	private LeaveWordMapper leaveWordMapper;
	
	@Autowired
	private LeaveWordPraisedService leaveWordPraisedService;
	
	@Autowired
	private LeaveWordSummaryService leaveWordSummaryService;
	

	@Autowired
	private EteaFeignClient eteaFeignClient;

	public void save(LeaveWord word) {
		leaveWordMapper.saveIgnoreNull(word);
	}

	/**
	 * 获取N层递归结构留言数据
	 * 
	 * @param projectId
	 * @param moduleId
	 * @param sourceId
	 * @param userId
	 * @param userIdPraised(如果需要查询某用户是否已点击praise，需要给userIdPraised赋值)
	 * @param level
	 * @return
	 */
	public List<Map<String, Object>> getRecursionLevelLeaveWordOfLessThanOrEqualDesignativeLevel(Integer projectId,
			Integer moduleId, Integer sourceId,Integer userId,Integer userIdPraised, Integer level,Boolean display) {

		List<Map<String, Object>> comparelevelList = new ArrayList<>();
		List<LeaveWordWrapper> list = getLevelLeaveWordOfLessThanOrEqualDesignativeLevel(projectId, moduleId, sourceId,
				userId,userIdPraised,level,display);
		if (list != null && list.size() > 0) {

			List<Integer> userIdList = list.stream().map(n -> n.getUserId()).distinct().collect(Collectors.toList());
			ResponseObject response = eteaFeignClient.getUserInfoListByIds(userIdList);
			Object res = response.getData();
			@SuppressWarnings("rawtypes")
			List<Map> userInfoList = JSONArray.parseArray(JSONObject.toJSONString(res), Map.class);
			// list转map
			Map<String, Map> idUserMapMap = userInfoList.stream()
					.collect(Collectors.toMap(n -> String.valueOf(n.get("id")), Function.identity(), (key1, key2) -> key2));
			// level次循环，分别找出
			for (int i = level; i >= 1; i--) {
				List<Map<String, Object>> currentlevelList = new ArrayList<>();
				for (LeaveWordWrapper item : list) {
					if (i == item.getLevel()) {
						Integer euserId = item.getUserId();
						
						@SuppressWarnings("unchecked")
						Map<String, Object> userInfoMap = idUserMapMap.get(String.valueOf(euserId));
						String avatarUrl = (String) userInfoMap.get("avatarUrl");
						String name = (String) userInfoMap.get("name");
						String nickname = (String) userInfoMap.get("nickname");
						item.setAvatarUrl(avatarUrl);
						item.setNickname(nickname);
						item.setUserName(name);

						List<Map<String, Object>> containLeaveWordList = new ArrayList<>();

						for (Map<String, Object> compareMap : comparelevelList) {
							LeaveWord downCategory = (LeaveWord) compareMap.get("leaveWordWrapper");
							Integer replyToId = downCategory.getReplyToId();
							if (replyToId != null && replyToId.intValue() == item.getId().intValue()) {
								containLeaveWordList.add(compareMap);
							}
						}
						Map<String, Object> currentMap = new HashMap<>();
						currentMap.put("leaveWordWrapper", item);
						currentMap.put("containLeaveWordList", containLeaveWordList);
						currentlevelList.add(currentMap);// 放入当前级别的
					}
				}
				comparelevelList = currentlevelList;
			}
		}
		return comparelevelList;
	}

	/**
	 * 获取N层留言并携带留言点赞总数、自己是否点赞的信息
	 * 
	 * @param projectId
	 * @param moduleId
	 * @param sourceId
	 * @param userIdPraised
	 * @param level
	 * @return
	 */
	private List<LeaveWordWrapper> getLevelLeaveWordOfLessThanOrEqualDesignativeLevel(Integer projectId,
			Integer moduleId, Integer sourceId,Integer userId,Integer userIdPraised, Integer level,Boolean display) {
		
		List<Map<String, Object>> mapList = leaveWordMapper.getLevelLeaveWordOfLessThanOrEqualDesignativeLevel(projectId,
			moduleId,sourceId,userId,userIdPraised,level,display);
		
		return MyBeanUtil.mapListToObjList(mapList, LeaveWordWrapper.class);
	}

	/**
	 * 删除
	 * @param id
	 */
	@Transactional
	public void deleteById(Integer id) {
		leaveWordMapper.deleteById(id);
		leaveWordPraisedService.delete(null, id);
		leaveWordSummaryService.delete(id);
	}

}
