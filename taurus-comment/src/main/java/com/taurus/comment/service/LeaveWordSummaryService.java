package com.taurus.comment.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.gitee.fastmybatis.core.query.Query;
import com.taurus.comment.entity.LeaveWordSummary;
import com.taurus.comment.mapper.LeaveWordSummaryMapper;

@Service
public class LeaveWordSummaryService {

	@Autowired
	private LeaveWordSummaryMapper leaveWordSummaryMapper;
	
	public void save(LeaveWordSummary summary) {
		leaveWordSummaryMapper.save(summary);
	}
	
	
	/**
	 * 增加一次点赞
	 * @param leaveWordId
	 */
	public void increasePraisedNumber(Integer leaveWordId) {
		LeaveWordSummary summary =  getEntity(leaveWordId);
		if(summary!=null) {
			summary.setTotalPraisedNum(summary.getTotalPraisedNum()+1);
			leaveWordSummaryMapper.updateIgnoreNull(summary);
		}else {
			summary= new LeaveWordSummary();
			summary.setLeaveWordId(leaveWordId);
			summary.setTotalPraisedNum(1);
			save(summary);
		}
	}
	
	/**
	 * 取消一次点赞
	 * @param leaveWordId
	 */
	public void decreasePraisedNumber(Integer leaveWordId) {
		LeaveWordSummary summary =  getEntity(leaveWordId);
		if(summary!=null) {
			summary.setTotalPraisedNum(summary.getTotalPraisedNum()-1);
			leaveWordSummaryMapper.updateIgnoreNull(summary);
		}
	}
	
	
	
	public LeaveWordSummary getEntity(Integer leaveWordId) {
		return leaveWordSummaryMapper.getById(leaveWordId);
	}


	public void delete(Integer id) {
		Query query = new Query();
		query.eq("leave_word_id", id);
		leaveWordSummaryMapper.deleteByQuery(query);
	}


}
