package com.taurus.comment.feignClient;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSONObject;
import com.taurus.entity.ResponseObject;

@FeignClient(value = "exam-main-service") //这里的name对应调用服务的spring.application.name
@Component
public interface EteaFeignClient {
	
	/**
	 * 获取用户信息列表
	 * @param ids
	 * @return
	 */
	@PostMapping("/user/getUserInfoListByIds")
    public ResponseObject getUserInfoListByIds(@RequestBody List<Integer> ids);
	
	/**
	 * 检查文字信息是否违规
	 * 
	 * @param body
	 * @return
	 */
	@PostMapping("/weixinAccount/msgSecCheck")
	public ResponseObject msgSecCheck(@RequestBody JSONObject body);
	
	/**
	 * 检查图片是否违规
	 * @param body
	 * @return
	 */
	@PostMapping("/weixinAccount/imgSecCheckByMultipart")
	public ResponseObject imgSecCheck(@RequestPart MultipartFile file,@RequestParam("account")String account);
}
