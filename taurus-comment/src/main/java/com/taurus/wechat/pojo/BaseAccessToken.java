package com.taurus.wechat.pojo;


/**
 * 访问令牌
 * 
 * <AUTHOR>
 * 
 *tokenId 接口调用凭证 expiresTime 有效时长 最大值7200秒
 *
 */

public class BaseAccessToken {
	private String accessToken;
	private Long expiresIn;
	public String getAccessToken() {
		return accessToken;
	}
	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}
	public Long getExpiresIn() {
		return expiresIn;
	}
	public void setExpiresIn(Long expiresIn) {
		this.expiresIn = expiresIn;
	}
}
