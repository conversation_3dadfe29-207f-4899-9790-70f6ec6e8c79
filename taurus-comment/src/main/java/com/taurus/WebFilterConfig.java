package com.taurus;

import javax.servlet.Filter;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class WebFilterConfig {

    
    @Bean
    FilterRegistrationBean<Filter> locationFilterRegistration() {
        FilterRegistrationBean<Filter> registration = new FilterRegistrationBean<>();
        registration.setFilter(locationFilter());
        registration.addUrlPatterns("/file/*");
        registration.setName("locationFilter");
        registration.setOrder(3);
        return registration;
    }

    @Bean
    Filter locationFilter() {
        return new com.taurus.filter.LocationFilter();
    }

}
