<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
	<settings>
		<!-- 全局映射器启用缓存 -->
		<setting name="cacheEnabled" value="true" />
		<!-- 查询时，关闭关联对象即时加载以提高性能 -->
		<setting name="lazyLoadingEnabled" value="true" />
		<!-- 对于未知的SQL查询，允许返回不同的结果集以达到通用的效果 -->
		<setting name="multipleResultSetsEnabled" value="true" />
		<!-- 允许使用列标签代替列名 -->
		<setting name="useColumnLabel" value="true" />
		<!-- 允许使用自定义的主键值(比如由程序生成的UUID 32位编码作为键值)，数据表的PK生成策略将被覆盖 -->
		<setting name="useGeneratedKeys" value="false" />
		<!-- 对于批量更新操作缓存SQL以提高性能:BATCH -->
		<setting name="defaultExecutorType" value="SIMPLE" />
		<!-- 超时设置 -->
		<setting name="defaultStatementTimeout" value="25000" />
		<setting name="logImpl" value="LOG4J" />
	</settings>
</configuration>