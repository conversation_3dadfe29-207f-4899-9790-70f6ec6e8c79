spring.profiles.active=prod-172
spring.application.name=taurus-comment
server.port=8083

spring.jmx.default-domain=taurus-comment
spring.cloud.nacos.discovery.server-addr=localhost:8848

#文件上传配置
server.servlet.context-path:/
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=80MB
spring.servlet.multipart.max-request-size=80MB

#spring.jpa.properties.hibernate.hbm2ddl.auto=update
#spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL5InnoDBDialect
#spring.jpa.show-sql=true
#Date对象的格式化
spring.jackson.date-format=yyyy/MM/dd HH:mm:ss
spring.jackson.time-zone=GMT+8


# mybatis xml classpath
mybatis.mapper-locations=classpath:mybatis/mapper/*.xml
#将查询数据库得到的map键值转为驼峰形式
#mybatis.configuration.map-underscore-to-camel-case=true

# 连接池最大连接数（使用负值表示没有限制）
spring.redis.jedis.pool.max-active=1000
# 连接池最大阻塞等待时间（使用负值表示没有限制）
spring.redis.jedis.pool.max-wait=-1
# 连接池中的最大空闲连接
spring.redis.jedis.pool.max-idle=10
# 连接池中的最小空闲连接
spring.redis.jedis.pool.min-idle=0
# 连接超时时间（毫秒）
spring.redis.timeout=5000


eureka.client.serviceUrl.defaultZone=http://localhost:7070/eureka
eureka.client.registerWithEureka=true
eureka.client.fetch-registry=true

#jasypt properties属性文件属性加密
jasypt.encryptor.password=EDsPO|sdfa2812&823&&^dlfFdKdajdingss

#此配置建议只试用开发和测试环境
#心跳间隔时间,默认是30秒
eureka.instance.leaseRenewalIntervalInSeconds=2
#最后一次心跳时间后leaseExpirationDurationInSeconds秒就认为是下线了，默认是90秒
eureka.instance.leaseExpirationDurationInSeconds=6
eureka.instance.instance-id=${spring.application.name}:${spring.cloud.client.ip-address}:${server.port}
eureka.instance.prefer-ip-address=true

#饥饿加载模式
ribbon.eager-load.enabled=true
ribbon.eager-load.clients=taurus-formSystem

#服务端禁用ribbon的hystrix
feign.hystrix.enabled=false
#当值为0或者大于0时，表示容器在应用启动时就加载并初始化这个servlet
spring.mvc.servlet.load-on-startup=100

#jwt
jwt.secret=&^*2sSEJHBQLF
jwt.expiration=1800

#OkHttp
ok.http.connect-timeout=30
ok.http.read-timeout=30
ok.http.write-timeout=30
# 连接池中整体的空闲连接的最大数量
ok.http.max-idle-connections=10
# 连接空闲时间最多为 300 秒
ok.http.keep-alive-duration=300

#小程序接口
wx.grantType=authorization_code
wx.requestUrl=https://api.weixin.qq.com/sns/jscode2session
wx.accessTokenUrl=https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=APPID&secret=APPSECRET
wx.qrCodeUnlimitedUrl=https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=ACCESS_TOKEN
#违规文字检测接口
wx.msgSecCheckUrl=https://api.weixin.qq.com/wxa/msg_sec_check?access_token=ACCESS_TOKEN
#图片检测接口
wx.imgSecCheckUrl=https://api.weixin.qq.com/wxa/img_sec_check?access_token=ACCESS_TOKEN
