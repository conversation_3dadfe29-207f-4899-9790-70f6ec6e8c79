[INFO] Scanning for projects...
[INFO] ------------------------------------------------------------------------
[INFO] Reactor Build Order:
[INFO] 
[INFO] taurus-cloud                                                       [pom]
[INFO] taurus-gateway                                                     [jar]
[INFO] exam-main-service                                                  [jar]
[INFO] exam-qkk-study-service                                             [jar]
[INFO] taurus-form-system                                                 [jar]
[INFO] taurus-oss                                                         [jar]
[INFO] taurus-payment                                                     [jar]
[INFO] taurus-comment                                                     [jar]
[INFO] taurus-llm                                                         [jar]
[INFO] taurus-sse                                                         [jar]
[INFO] taurus-party-games                                                 [jar]
[INFO] taurus-monolith                                                    [jar]
[INFO] 
[INFO] ----------------------< com.taurus:taurus-cloud >-----------------------
[INFO] Building taurus-cloud 0.0.1-SNAPSHOT                              [1/12]
[INFO]   from pom.xml
[INFO] --------------------------------[ pom ]---------------------------------
[INFO] 
[INFO] >>> spring-boot:2.7.18:run (default-cli) > test-compile @ taurus-cloud >>>
[INFO] 
[INFO] <<< spring-boot:2.7.18:run (default-cli) < test-compile @ taurus-cloud <<<
[INFO] 
[INFO] 
[INFO] --- spring-boot:2.7.18:run (default-cli) @ taurus-cloud ---
[INFO] ------------------------------------------------------------------------
[INFO] Reactor Summary:
[INFO] 
[INFO] taurus-cloud 0.0.1-SNAPSHOT ........................ FAILURE [  1.810 s]
[INFO] taurus-gateway 0.1.0 ............................... SKIPPED
[INFO] exam-main-service 1.0.0 ............................ SKIPPED
[INFO] exam-qkk-study-service 1 ........................... SKIPPED
[INFO] taurus-form-system 0.1.0 ........................... SKIPPED
[INFO] taurus-oss 0.1.0 ................................... SKIPPED
[INFO] taurus-payment 0.1.0 ............................... SKIPPED
[INFO] taurus-comment 0.1.0 ............................... SKIPPED
[INFO] taurus-llm 0.1.0 ................................... SKIPPED
[INFO] taurus-sse 0.1.0 ................................... SKIPPED
[INFO] taurus-party-games 1.0.0 ........................... SKIPPED
[INFO] taurus-monolith 1.0.0 .............................. SKIPPED
[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  3.535 s
[INFO] Finished at: 2025-06-22T19:58:32+08:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.springframework.boot:spring-boot-maven-plugin:2.7.18:run (default-cli) on project taurus-cloud: Unable to find a suitable main class, please add a 'mainClass' property -> [Help 1]
[ERROR] 
[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.
[ERROR] Re-run Maven using the -X switch to enable full debug logging.
[ERROR] 
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/MojoExecutionException
