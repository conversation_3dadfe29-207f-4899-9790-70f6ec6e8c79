name: CI Build

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  build:
    name: Build Main Branch
    runs-on: ubuntu-latest

    steps:
      - name: Checkout source code
        uses: actions/checkout@v3

      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'temurin'
          cache: maven

      - name: Build Initial with <PERSON>ven
        working-directory: ./initial
        run: ./mvnw --batch-mode clean package

      - name: Build Initial with Gradle
        working-directory: ./initial
        run: ./gradlew build

      - name: Build Complete with Maven
        working-directory: ./complete
        run: ./mvnw --batch-mode clean package

      - name: Build Complete with Gradle
        working-directory: ./complete
        run: ./gradlew build
