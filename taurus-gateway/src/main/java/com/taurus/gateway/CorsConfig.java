package com.taurus.gateway;

import java.util.Arrays;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.reactive.CorsWebFilter;
import org.springframework.web.cors.reactive.UrlBasedCorsConfigurationSource;
import org.springframework.web.util.pattern.PathPatternParser;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
public class CorsConfig {

    @Value("${spring.profiles.active}")
    private String profile;

    @Bean
    CorsWebFilter corsWebFilter() {
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowCredentials(true);
        config.setAllowedMethods(Arrays.asList("DELETE", "POST", "GET", "OPTIONS"));
        config.setAllowedHeaders(Arrays.asList("enctype", "Origin", "X-Requested-With", "Content-Type", "Accept",
                "Authorization", "Access-Control-Request-Headers", "Access-Control-Request-Method",
                "Access-Control-Allow-Origin", "Access-Control-Allow-Credentials", "requestid", "token", "userId",
                "client"));

        log.info("配置文件名称：{}", profile);

        // 设置允许的来源站点URL
        if (profile.startsWith("prod")) {
            config.setAllowedOriginPatterns(
                    Arrays.asList("https://www.qikaokao.com", "https://qikaokao.com", "https://www.51kaoshi.wang",
                            "https://51kaoshi.wang", "http://www.51kaoshi.wang", "https://qkkservice.51kaoshi.wang",
                            "https://diamond.edmarketing.cn"));
        } else {
            config.setAllowedOriginPatterns(Arrays.asList("http://localhost:8080", "http://localhost:5500",
                    "http://127.0.0.1:8080", "http://127.0.0.1:5500", "http://localhost", "null",
                    "https://qkkservice.51kaoshi.wang", "https://www.qikaokao.com", "https://qikaokao.com",
                    "https://exam.edmarketing.cn"));
        }

        config.setExposedHeaders(Arrays.asList("Content-Disposition"));
        // 设置预检请求的缓存时间（秒），在这个时间段里，对于相同的跨域请求不会再预检了
        config.setMaxAge(18000L);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource(new PathPatternParser());
        source.registerCorsConfiguration("/**", config);
        return new CorsWebFilter(source);
    }
}
