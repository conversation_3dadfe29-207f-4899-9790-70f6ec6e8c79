package com.taurus.gateway;

import static org.springframework.cloud.loadbalancer.core.CachingServiceInstanceListSupplier.SERVICE_INSTANCE_CACHE_NAME;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;

import com.alibaba.nacos.client.naming.event.InstancesChangeEvent;
import com.alibaba.nacos.common.notify.NotifyCenter;
import com.alibaba.nacos.common.notify.listener.Subscriber;
import com.alibaba.nacos.common.utils.JacksonUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 订阅nacos通知
 * 接收nacos推送的微服务上下线实例信息
 * <AUTHOR>
 */
@Component
@Slf4j
public class NacosInstancesChangeEventListener extends Subscriber<InstancesChangeEvent> {
	@Resource
	private CacheManager defaultLoadBalancerCacheManager;
    @PostConstruct
    public void registerToNotifyCenter(){
        NotifyCenter.registerSubscriber(this);
    }
	@Override
	public void onEvent(InstancesChangeEvent event) {
		log.info("SpringCloud Gateway 接收微服务实例刷新事件：{}, 开始刷新本地存储的微服务实例信息的缓存", JacksonUtils.toJson(event));
		Cache cache = defaultLoadBalancerCacheManager.getCache(SERVICE_INSTANCE_CACHE_NAME);
		if (cache != null) {
			cache.evict(event.getServiceName());
		}
		log.info("SpringCloud Gateway 实例刷新完成");

	}

	@Override
	public Class<? extends com.alibaba.nacos.common.notify.Event> subscribeType() {
		return InstancesChangeEvent.class;
	}
}
