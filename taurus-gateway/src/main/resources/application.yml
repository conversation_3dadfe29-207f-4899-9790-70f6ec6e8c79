server:
  port: 8000
spring:
  application:
    name: taurus-gateway
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        service: taurus-gateway
    gateway:
      discovery:
        locator:
          #开启从注册中心动态创建路由的功能，利用微服务名进行路由
          enabled: true
          #开启小写验证，默认feign根据服务名查找都是用的全大写
          lowerCaseServiceId: true
      routes:
        - id: exam-main-service
          uri: lb://exam-main-service
          # 断言,路径相匹配的进行路由
          predicates:
            - Path=/exam/**
          filters:
            - StripPrefix=1

        - id: qkk-study-service
          uri: lb://qkk-study-service
          # 断言,路径相匹配的进行路由
          predicates:
            - Path=/study/**
          filters:
            - StripPrefix=1

        - id: taurus-pay
          uri: lb://taurus-pay
          # 断言,路径相匹配的进行路由
          predicates:
            - Path=/pay/**
          filters:
            - StripPrefix=1

        - id: taurus-comment
          uri: lb://taurus-comment
          # 断言,路径相匹配的进行路由
          predicates:
            - Path=/comment/**
          filters:
            - StripPrefix=1

        - id: taurus-formSystem
          uri: lb://taurus-formSystem
          # 断言,路径相匹配的进行路由
          predicates:
            - Path=/form/**
          filters:
            - StripPrefix=1

        - id: taurus-oss
          uri: lb://taurus-oss
          # 断言,路径相匹配的进行路由
          predicates:
            - Path=/oss/**
          filters:
            - StripPrefix=1

        - id: taurus-punchcard
          uri: lb://taurus-punchcard
          # 断言,路径相匹配的进行路由
          predicates:
            - Path=/punchcard/**
          filters:
            - StripPrefix=1

        - id: taurus-questionnaire
          uri: lb://taurus-questionnaire
          # 断言,路径相匹配的进行路由
          predicates:
            - Path=/questionnaire/**
          filters:
            - StripPrefix=1

        - id: taurus-llm
          uri: lb://taurus-llm
          # 断言,路径相匹配的进行路由
          predicates:
            - Path=/llm/**
          filters:
            - StripPrefix=1

        - id: taurus-sse
          uri: lb://taurus-sse
          # 断言,路径相匹配的进行路由
          predicates:
            - Path=/sse/**
          filters:
            - StripPrefix=1
        - id: taurus-api
          uri: lb://taurus-api
          # 断言,路径相匹配的进行路由
          predicates:
            - Path=/api/**
          filters:
            - StripPrefix=1
management:
  endpoints:
    enabled-by-default: false
    web:
      base-path: /activeEgoolanw98sjHpK
