# 性能监控配置模板
# 适用于所有微服务

# Spring Boot Actuator配置
management:
  endpoints:
    web:
      exposure:
        # 暴露监控端点
        include: health,info,metrics,prometheus,env,beans,configprops,threaddump,heapdump
      base-path: /actuator
  endpoint:
    health:
      show-details: always
      show-components: always
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
        step: 10s
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5,0.9,0.95,0.99
    tags:
      application: ${spring.application.name}

# 服务器配置优化
server:
  # Undertow优化配置
  undertow:
    # IO线程数，通常设置为CPU核心数
    io-threads: 4
    # 工作线程数，通常设置为IO线程数的8倍
    worker-threads: 32
    # 缓冲区大小
    buffer-size: 1024
    # 是否使用直接内存
    direct-buffers: true
    # 最大HTTP POST内容大小
    max-http-post-size: 10MB
  # 连接超时配置
  connection-timeout: 30000
  # 线程池配置
  tomcat:
    threads:
      max: 200
      min-spare: 10
    connection-timeout: 30000
    keep-alive-timeout: 30000

# 数据库连接池优化配置
spring:
  datasource:
    druid:
      # 核心连接池配置
      initial-size: 5
      min-idle: 5
      max-active: 50
      max-wait: 30000
      
      # 连接检测配置
      validation-query: SELECT 1
      validation-query-timeout: 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      
      # 连接回收配置
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      
      # 预编译语句缓存
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      
      # 监控配置
      filters: stat,wall,slf4j
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: admin
        login-password: admin123

  # Redis连接池优化
  redis:
    timeout: 5000
    jedis:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 2
        max-wait: 3000

  # 缓存配置
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=1000,expireAfterWrite=30m

# 日志配置优化
logging:
  level:
    root: INFO
    # 关闭SQL日志输出以提升性能
    org.apache.ibatis.logging: WARN
    # 关闭Spring Cloud的DEBUG日志
    org.springframework.cloud: WARN
    # 关闭Nacos的DEBUG日志
    com.alibaba.nacos: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    max-size: 100MB
    max-history: 30
    total-size-cap: 1GB

# 异步配置
spring:
  task:
    execution:
      pool:
        core-size: 5
        max-size: 20
        queue-capacity: 100
        keep-alive: 60s
      thread-name-prefix: async-task-
    scheduling:
      pool:
        size: 5
      thread-name-prefix: scheduled-task-

# HTTP客户端优化
feign:
  httpclient:
    enabled: true
    max-connections: 200
    max-connections-per-route: 50
    connection-timeout: 5000
    connection-timer-repeat: 3000
  compression:
    request:
      enabled: true
    response:
      enabled: true

# Ribbon负载均衡优化
ribbon:
  # 启用饥饿加载
  eager-load:
    enabled: true
  # 连接超时时间
  ConnectTimeout: 5000
  # 读取超时时间
  ReadTimeout: 10000
  # 重试次数
  MaxAutoRetries: 1
  MaxAutoRetriesNextServer: 2 