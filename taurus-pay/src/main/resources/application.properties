spring.profiles.active=test
spring.application.name=taurus-pay
spring.cloud.nacos.discovery.service=taurus-pay


server.port=8086
server.servlet.context-path:/

spring.jmx.enabled=true
spring.jmx.default-domain=taurus-pay

spring.main.allow-circular-references=true
mybatis.type-handlers-package=com.taurus.mybatis

# actuator 监控配置
management.endpoints.enabled-by-default=false
management.endpoints.web.base-path: /activeEgoolanw98sjHpK

eureka.instance.instance-id=${spring.application.name}:${spring.cloud.client.ip-address}:${server.port}
eureka.instance.prefer-ip-address=true
#心跳间隔时间,默认是30秒
eureka.instance.lease-renewal-interval-in-seconds=30
#最后一次心跳时间后leaseExpirationDurationInSeconds秒就认为是下线了，默认是90秒
eureka.instance.leaseExpirationDurationInSeconds=90
eureka.instance.health-check-url=/actuator/health

eureka.client.serviceUrl.defaultZone=http://localhost:7070/eureka
eureka.client.fetch-registry=true
eureka.client.registerWithEureka=true
#拉取注册表间隔时间
eureka.client.registry-fetch-interval-seconds=5

#client端的url用ip不用主机名
spring.boot.admin.client.instance.service-host-type=ip
#注册到server的client地址
spring.boot.admin.client.url=http://localhost:7070/jj


spring.datasource.url=**************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=87362$#sJG@33H

#Date对象的格式化
spring.jackson.date-format=yyyy/MM/dd HH:mm:ss
spring.jackson.time-zone=GMT+8

# mybatis xml classpath
mybatis.mapper-locations=classpath:mybatis/mapper/*.xml
mybatis.type-aliases-package=com.taurus.payment.entity


#log文件配置
logging.config=classpath:log4j2-prod.xml

spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=80MB
spring.servlet.multipart.max-request-size=80MB

#支付成功后，微信回调地址
wx.notifyUrl=https://diamond.qikaokao.com/pay/weChatPay/weChatNotify
