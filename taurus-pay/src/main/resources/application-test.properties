spring.cloud.nacos.discovery.server-addr=localhost:8848
spring.cloud.nacos.discovery.enabled=true
spring.cloud.nacos.discovery.username=nacos
spring.cloud.nacos.discovery.password=9945xqyg
#log文件配置
logging.config=classpath:log4j2-test.xml

spring.datasource.url=jdbc:mysql://**************:3306/payment?useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false
spring.datasource.username=exjjssid
spring.datasource.password=873dPw(*^Kd%

#微信支付回调url
wx.notifyUrl=https://qkkservice.51kaoshi.wang/pay/weChatPay/weChatNotify