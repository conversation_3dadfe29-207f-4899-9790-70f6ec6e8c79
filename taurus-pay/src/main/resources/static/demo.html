<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.5.0/css/bootstrap.css" rel="stylesheet">
</head>

<body>
    <form>
        <div class="form-group">
            <label for="exampleInputOrder1">订单号：</label>
            <input type="text" class="form-control" id="exampleInputOrder1" placeholder="订单号">
        </div>
        <div class="form-group">
            <label for="exampleInputName1">商品名称</label>
            <input type="text" class="form-control" id="exampleInputName1" placeholder="商品名称">
        </div>
        <div class="form-group">
            <label for="exampleInputTotal1">价格</label>
            <input type="text" class="form-control" id="exampleInputTotal1" placeholder="价格">
        </div>
    </form>
    <button>提交</button>
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.4.1/jquery.js"></script>
    <script>
        $('button').on('click', function () {
            var outTradeNo = $("#exampleInputOrder1").val();
            var totalFee = $("#exampleInputTotal1").val();
            var shopName = $("#exampleInputName1").val();
            if (outTradeNo == '' || totalFee == '' || shopName == '') {
                alert('输入框不能为空')
            } else {
                $.ajax({
                    url: 'https://diamond.qikaokao.com/ExaminationPayment/weixinMobile/h5pay',
                    data: {
                        body: shopName,
                        outTradeNo: outTradeNo,
                        totalFee: totalFee,
                        originChannel: 'miniapp'
                    },
                    success: function (res) {
                        console.log(res)
                        window.location.assign(res)
                    }
                })
            }

        })
    </script>
</body>

</html>