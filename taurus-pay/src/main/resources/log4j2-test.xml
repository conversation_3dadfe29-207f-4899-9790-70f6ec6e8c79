<?xml version="1.0" encoding="UTF-8"?>
<!--设置log4j2的自身log级别为warn -->
<!--日志级别以及优先级排序: OFF > FATAL > ERROR > WARN > INFO > DEBUG > TRACE > ALL -->
<!--Configuration后面的status，这个用于设置log4j2自身内部的信息输出，可以不设置， 当设置成trace时，你会看到log4j2内部各种详细输出 -->
<!--monitorInterval：Log4j能够自动检测修改配置 文件和重新配置本身，设置间隔秒数 -->
<configuration status="warn" monitorInterval="30">
	<properties>
		<property name="LOG_HOME">${sys:user.dir}/logs</property>
		<property name="PROJECT_NAME">payment</property>
	</properties>
	<!--先定义所有的appender记录器 -->
	<appenders>
		<!--这个输出控制台的配置 -->
		<console name="Console" target="SYSTEM_OUT" >
			<!--输出日志的格式 -->
			<PatternLayout
				pattern="[%d{HH:mm:ss:SSS}] [%p] - %l - %m%n" />
		</console>
		<!--文件会打印出所有信息，这个log每次运行程序会自动清空，由append属性决定，这个也挺有用的，适合临时测试用 -->
		<File name="log" fileName="${LOG_HOME}/${PROJECT_NAME}/test.log"
			append="false">
			<PatternLayout
				pattern="%d{HH:mm:ss.SSS} %-5level %class{36} %L %M - %msg%xEx%n" />
		</File>
		<!-- 这个会打印出所有的info及以下级别的信息，每次大小超过size， 则这size大小的日志会自动存入按年份-月份建立的文件夹下面并进行压缩，作为存档 -->
		<RollingFile name="RollingFileInfo"
			fileName="${LOG_HOME}/${PROJECT_NAME}/info.log"
			filePattern="${LOG_HOME}/${PROJECT_NAME}/$${date:yyyy-MM}/info-%d{yyyy-MM-dd}-%i.log">
			<Filters>
				<!--控制台只输出level及以上级别的信息（onMatch），其他的直接拒绝（onMismatch） -->
				<ThresholdFilter level="INFO" onMatch="ACCEPT"
					onMismatch="DENY" />
				<ThresholdFilter level="WARN" onMatch="DENY"
					onMismatch="NEUTRAL" />
			</Filters>
			<PatternLayout
				pattern="[%d{HH:mm:ss:SSS}] [%p] - %l - %m%n" />
			<Policies>
				<TimeBasedTriggeringPolicy />
				<SizeBasedTriggeringPolicy size="100 MB" />
			</Policies>
		</RollingFile>
		<RollingFile name="RollingFileDebug"
			fileName="${LOG_HOME}/${PROJECT_NAME}/debug.log"
			filePattern="${LOG_HOME}/${PROJECT_NAME}/$${date:yyyy-MM}/debug-%d{yyyy-MM-dd}-%i.log">
			<Filters>
				<!--控制台只输出level及以上级别的信息（onMatch），其他的直接拒绝（onMismatch） -->
				<ThresholdFilter level="OFF" onMatch="ACCEPT"
					onMismatch="DENY" />
				<ThresholdFilter level="OFF" onMatch="DENY"
					onMismatch="NEUTRAL" />
			</Filters>
			<PatternLayout
				pattern="[%d{HH:mm:ss:SSS}] [%p] - %l - %m%n" />
			<Policies>
				<TimeBasedTriggeringPolicy />
				<SizeBasedTriggeringPolicy size="100 MB" />
			</Policies>
		</RollingFile>

		<RollingFile name="RollingFileWarn"
			fileName="${LOG_HOME}/${PROJECT_NAME}/warn.log"
			filePattern="${LOG_HOME}/${PROJECT_NAME}/$${date:yyyy-MM}/warn-%d{yyyy-MM-dd}-%i.log">
			<Filters>
				<ThresholdFilter level="OFF" onMatch="ACCEPT"
					onMismatch="DENY" />
				<ThresholdFilter level="ERROR" onMatch="DENY"
					onMismatch="NEUTRAL" />
			</Filters>
			<PatternLayout
				pattern="[%d{HH:mm:ss:SSS}] [%p] - %l - %m%n" />
			<Policies>
				<TimeBasedTriggeringPolicy />
				<SizeBasedTriggeringPolicy size="100 MB" />
			</Policies>
			<!-- DefaultRolloverStrategy属性如不设置，则默认为最多同一文件夹下7个文件，这里设置了20 -->
			<DefaultRolloverStrategy max="20" />
		</RollingFile>

		<RollingFile name="RollingFileError"
			fileName="${LOG_HOME}/${PROJECT_NAME}/error.log"
			filePattern="${LOG_HOME}/${PROJECT_NAME}/$${date:yyyy-MM}/error-%d{yyyy-MM-dd}-%i.log">
			<ThresholdFilter level="ERROR" />
			<PatternLayout
				pattern="[%d{HH:mm:ss:SSS}] [%p] - %l - %m%n" />
			<Policies>
				<TimeBasedTriggeringPolicy />
				<SizeBasedTriggeringPolicy size="100 MB" />
			</Policies>
		</RollingFile>
	</appenders>
	<!--然后定义logger，只有定义了logger并引入的appender，appender才会生效 -->
	<loggers>
		<!--过滤掉spring、hibernate、thymeleaf的一些无用的debug信息 -->
        <!-- additivity="false" 该属性表示不使用父类的appender配置信息 -->
        <logger name="com.taurus.payment" level="DEBUG" additivity="true">
        	
		</logger>
		<logger name="org.springframework" level="DEBUG" additivity="false">
			<appender-ref ref="Console" />
		</logger>
		<logger name="org.hibernate" level="INFO" additivity="false">
		</logger>
		<logger name="org.thymeleaf" level="INFO" additivity="false">
		</logger>
		<logger name="org.apache" level="INFO" additivity="false">
		</logger>
		<logger name="org.mybatis" level="INFO" additivity="false">
		</logger>
		
		<root level="INFO">
			<appender-ref ref="Console" />
			<appender-ref ref="RollingFileInfo" />
			<appender-ref ref="RollingFileDebug" />
			<appender-ref ref="RollingFileWarn" />
			<appender-ref ref="RollingFileError" />
		</root>
	</loggers>
</configuration>