package com.taurus.payment.utils;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.util.HashMap;
import java.util.Map;

import javax.imageio.ImageIO;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.common.BitMatrix;

import sun.misc.BASE64Encoder;

/**
 * 二维码生成器
 * <AUTHOR>
 *
 */
public class QrCodeGenerator {
	/**
	 * 生成指定内容的二维码图片
	 * 
	 * @param request
	 * @param response
	 * @return 
	 * @return
	 */

	public static BufferedImage toImage(String content) {
		try {
			MultiFormatWriter multiFormatWriter = new MultiFormatWriter();
			Map<EncodeHintType, String> hints = new HashMap<EncodeHintType, String>();
			hints.put(EncodeHintType.CHARACTER_SET, "UTF-8"); // 设置字符集编码类型
			BitMatrix bitMatrix = null;
			bitMatrix = multiFormatWriter.encode(content, BarcodeFormat.QR_CODE, 300, 300, hints);
			BufferedImage image = PayUtil.toBufferedImage(bitMatrix);
			return image;

		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 生成指定内容的base64格式二维码
	 * 
	 * @param request
	 * @param response
	 * @return
	 */

	public static String toBase64Format(String content) {
		ByteArrayOutputStream baos = null;
		String responseResult = null;
		try {
			MultiFormatWriter multiFormatWriter = new MultiFormatWriter();
			Map<EncodeHintType, String> hints = new HashMap<EncodeHintType, String>();
			hints.put(EncodeHintType.CHARACTER_SET, "UTF-8"); // 设置字符集编码类型
			BitMatrix bitMatrix = null;
			bitMatrix = multiFormatWriter.encode(content, BarcodeFormat.QR_CODE, 300, 300, hints);
			BufferedImage image = PayUtil.toBufferedImage(bitMatrix);
		    //将图片转换为BASE64加密字符串
			baos = new ByteArrayOutputStream();
			ImageIO.write(image, "png", baos);
			byte[] bytes = baos.toByteArray();
			BASE64Encoder encoder = new sun.misc.BASE64Encoder();
			responseResult = encoder.encodeBuffer(bytes).trim();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (baos != null) {
					baos.close();
					baos = null;
				}
			} catch (Exception e) {
				e.printStackTrace();
				System.out.println("关闭文件流发生异常: " + e);
			}

		}
		return responseResult;
	}
}
