package com.taurus.payment.utils;

import java.security.MessageDigest;

/** 
 * MD5工具类 
 * <AUTHOR> 
 */ 
public class MD5Util {

	 public final static String MD5(String s) {  
	        char hexDigits[]={'0','1','2','3','4','5','6','7','8','9','A','B','C','D','E','F'};         
	  
	        try {  
	            byte[] btInput = s.getBytes();  
	            // 鑾峰緱MD5鎽樿绠楁硶鐨�MessageDigest 瀵硅�?  
	            MessageDigest mdInst = MessageDigest.getInstance("MD5");  
	            // 浣跨敤鎸囧畾鐨勫瓧鑺傛洿鏂版憳瑕�?  
	            mdInst.update(btInput);  
	            // 鑾峰緱�?�嗘�?  
	            byte[] md = mdInst.digest();  
	            // 鎶婂瘑鏂囪浆鎹㈡垚鍗佸叚杩涘埗鐨勫瓧绗︿覆褰㈠紡  
	            int j = md.length;  
	            char str[] = new char[j * 2];  
	            int k = 0;  
	            for (int i = 0; i < j; i++) {  
	                byte byte0 = md[i];  
	                str[k++] = hexDigits[byte0 >>> 4 & 0xf];  
	                str[k++] = hexDigits[byte0 & 0xf];  
	            }  
	            String md5Str = new String(str);   
	            return md5Str;  
	        } catch (Exception e) {  
	            e.printStackTrace();  
	            return null;  
	        }  
	    }

	 
	 private static String byteArrayToHexString(byte b[]) {
			StringBuffer resultSb = new StringBuffer();
			for (int i = 0; i < b.length; i++)
				resultSb.append(byteToHexString(b[i]));

			return resultSb.toString();
		}

		private static String byteToHexString(byte b) {
			int n = b;
			if (n < 0)
				n += 256;
			int d1 = n / 16;
			int d2 = n % 16;
			return hexDigits[d1] + hexDigits[d2];
		}

		public static String MD5Encode(String origin, String charsetname) {
			String resultString = null;
			try {
				resultString = new String(origin);
				MessageDigest md = MessageDigest.getInstance("MD5");
				if (charsetname == null || "".equals(charsetname))
					resultString = byteArrayToHexString(md.digest(resultString.getBytes()));
				else
					resultString = byteArrayToHexString(md.digest(resultString.getBytes(charsetname)));
			} catch (Exception exception) {
			}
			return resultString;
		}

		private static final String hexDigits[] = { "0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f" };
	}
