package com.taurus.payment.utils;

import java.net.InetAddress;
import java.net.UnknownHostException;

import javax.servlet.http.HttpServletRequest;

import com.alibaba.druid.util.StringUtils;

public class Network {
	
	/**
	 * 获取真实ip地址 通过阿帕奇代理的也能获取到真实ip
	 * 
	 * @param request
	 * @return
	 */
	public static String getRealIp(HttpServletRequest request) {
		String ip = request.getHeader("x-forwarded-for");
		if (ip == null || ip.length() == 0 || " unknown ".equalsIgnoreCase(ip)) {
			ip = request.getHeader("Proxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 || " unknown ".equalsIgnoreCase(ip)) {
			ip = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 || " unknown ".equalsIgnoreCase(ip)) {
			ip = request.getHeader("HTTP_CLIENT_IP");
		}
		if (ip == null || ip.length() == 0 || " unknown ".equalsIgnoreCase(ip)) {
			ip = request.getHeader("HTTP_X_FORWARDED_FOR");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getRemoteAddr();
		}
		if (!StringUtils.isEmpty(ip)) {
			String[] ipSplit = ip.split(":");
			ip = ipSplit[0];
		}
		if(ip.equals("0")) {
			ip = getLocalIP();
		}
		return ip;
	}
	/**
	 * 获取本地ip
	 * @return
	 */
	public static String getLocalIP() {   
        InetAddress addr = null;   
        try {
            addr = InetAddress.getLocalHost();
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }         
        byte[] ipAddr = addr.getAddress();   
        String ipAddrStr = "";   
        for (int i = 0; i < ipAddr.length; i++) {   
            if (i > 0) {   
                ipAddrStr += ".";   
            }   
            ipAddrStr += ipAddr[i] & 0xFF;   
        }   
        return ipAddrStr;   
    }
}
