package com.taurus.payment.utils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.SortedMap;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

import org.jdom.Document;
import org.jdom.Element;
import org.jdom.JDOMException;
import org.jdom.input.SAXBuilder;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
/**
 * XML解析
 *
 */
public class XMLUtil {
	/**
	 * 解析xml,返回第一级元素键值对。如果第一级元素有子节点，则此节点的值是子节点的xml数据。
	 * 
	 * @param strxml
	 * @return
	 * @throws JDOMException
	 * @throws IOException
	 */
	public static Map<String, String> doXMLParse(String strxml) throws JDOMException, IOException {
		//过滤关键词，防止XXE漏洞攻击
	    strxml = filterXXE(strxml);
		strxml = strxml.replaceFirst("encoding=\".*\"", "encoding=\"UTF-8\"");

		if (null == strxml || "".equals(strxml)) {
			return null;
		}

		Map<String, String> m = new HashMap<>();

		InputStream in = new ByteArrayInputStream(strxml.getBytes("UTF-8"));
		SAXBuilder builder = new SAXBuilder();
		Document doc = builder.build(in);
		Element root = doc.getRootElement();
		List<?> list = root.getChildren();
		Iterator<?> it = list.iterator();
		while (it.hasNext()) {
			Element e = (Element) it.next();
			String k = e.getName();
			String v = "";
			List<?> children = e.getChildren();
			if (children.isEmpty()) {
				v = e.getTextNormalize();
			} else {
				v = XMLUtil.getChildrenText(children);
			}

			m.put(k, v);
		}

		// 关闭流
		in.close();

		return m;
	}

	/**
	 * 获取子结点的xml
	 * 
	 * @param children
	 * @return String
	 */
	@SuppressWarnings({ "rawtypes" })
	public static String getChildrenText(List children) {
		StringBuffer sb = new StringBuffer();
		if (!children.isEmpty()) {
			Iterator it = children.iterator();
			while (it.hasNext()) {
				Element e = (Element) it.next();
				String name = e.getName();
				String value = e.getTextNormalize();
				List list = e.getChildren();
				sb.append("<" + name + ">");
				if (!list.isEmpty()) {
					sb.append(XMLUtil.getChildrenText(list));
				}
				sb.append(value);
				sb.append("</" + name + ">");
			}
		}

		return sb.toString();
	}
	/**
	 * 通过DOCTYPE和ENTITY来加载本地受保护的文件、替换掉即可
	 * 漏洞原理：https://my.oschina.net/u/574353/blog/1841103
     * 防止 XXE漏洞 注入实体攻击
     * 过滤 过滤用户提交的XML数据
     * 过滤关键词：<!DOCTYPE和<!ENTITY，或者SYSTEM和PUBLIC。
    */
	public static String filterXXE(String xmlStr){
	    xmlStr = xmlStr.replace("DOCTYPE", "").replace("SYSTEM", "").replace("ENTITY", "").replace("PUBLIC", "");
	     return xmlStr;
	}
	
	/**
	 * 微信给出的 XXE漏洞方案
	 * https://pay.weixin.qq.com/wiki/doc/api/jsapi.php?chapter=23_5
	 * @param strXML
	 * @return
	 * @throws Exception
	 */
	public static Map<String, String> doXMLParse2(String strXML) throws Exception {
	   Map<String,String> m = new HashMap<String,String>();
	   DocumentBuilderFactory documentBuilderFactory = DocumentBuilderFactory.newInstance();
	   String FEATURE = null;
	   try {
	      FEATURE = "http://apache.org/xml/features/disallow-doctype-decl";
	      documentBuilderFactory.setFeature(FEATURE, true);

	      FEATURE = "http://xml.org/sax/features/external-general-entities";
	      documentBuilderFactory.setFeature(FEATURE, false);

	      FEATURE = "http://xml.org/sax/features/external-parameter-entities";
	      documentBuilderFactory.setFeature(FEATURE, false);

	      FEATURE = "http://apache.org/xml/features/nonvalidating/load-external-dtd";
	      documentBuilderFactory.setFeature(FEATURE, false);

	      documentBuilderFactory.setXIncludeAware(false);
	      documentBuilderFactory.setExpandEntityReferences(false);
	   } catch (ParserConfigurationException e) {
	      e.printStackTrace();
	   }
	   DocumentBuilder documentBuilder= documentBuilderFactory.newDocumentBuilder();
	   InputStream stream = new ByteArrayInputStream(strXML.getBytes("UTF-8"));
	   org.w3c.dom.Document doc = documentBuilder.parse(stream);
	   doc.getDocumentElement().normalize();
	   NodeList nodeList = doc.getDocumentElement().getChildNodes();
	   for (int idx=0; idx<nodeList.getLength(); ++idx) {
	      Node node = nodeList.item(idx);
	      if (node.getNodeType() == Node.ELEMENT_NODE) {
	         org.w3c.dom.Element element = (org.w3c.dom.Element) node;
	         m.put(element.getNodeName(), element.getTextContent());
	      }
	   }
	   stream.close();
	   return m;
	}
	/**
	 * XML格式字符串转换为Map
	 * 
	 * @param strXML XML字符串
	 * @return XML数据转换后的Map
	 * @throws Exception
	 */
	public static Map<String, String> xmlToMap(String strXML) throws Exception {
		try {
			Map<String, String> data = new HashMap<String, String>();
			DocumentBuilderFactory documentBuilderFactory = DocumentBuilderFactory.newInstance();
			DocumentBuilder documentBuilder = documentBuilderFactory.newDocumentBuilder();
			InputStream stream = new ByteArrayInputStream(strXML.getBytes("UTF-8"));
			org.w3c.dom.Document doc = documentBuilder.parse(stream);
			doc.getDocumentElement().normalize();
			NodeList nodeList = doc.getDocumentElement().getChildNodes();
			for (int idx = 0; idx < nodeList.getLength(); ++idx) {
				Node node = nodeList.item(idx);
				if (node.getNodeType() == Node.ELEMENT_NODE) {
					org.w3c.dom.Element element = (org.w3c.dom.Element) node;
					data.put(element.getNodeName(), element.getTextContent());
				}
			}
			try {
				stream.close();
			} catch (Exception ex) {
				// do nothing
			}
			return data;
		} catch (Exception ex) {
			throw ex;
		}

	}
	
	/**
	 * 将Map转换为XML格式的字符串
	 * 
	 * @param data Map类型数据
	 * @return XML格式的字符串
	 * @throws Exception
	 */
	public static String mapToXml(Map<String, String> data) throws Exception {
		DocumentBuilderFactory documentBuilderFactory = DocumentBuilderFactory.newInstance();
		DocumentBuilder documentBuilder = documentBuilderFactory.newDocumentBuilder();
		org.w3c.dom.Document document = documentBuilder.newDocument();
		org.w3c.dom.Element root = document.createElement("xml");
		document.appendChild(root);
		for (String key : data.keySet()) {
			String value = data.get(key);
			if (value == null) {
				value = "";
			}
			value = value.trim();
			org.w3c.dom.Element filed = document.createElement(key);
			filed.appendChild(document.createTextNode(value));
			root.appendChild(filed);
		}
		TransformerFactory tf = TransformerFactory.newInstance();
		Transformer transformer = tf.newTransformer();
		DOMSource source = new DOMSource(document);
		transformer.setOutputProperty(OutputKeys.ENCODING, "UTF-8");
		transformer.setOutputProperty(OutputKeys.INDENT, "yes");
		StringWriter writer = new StringWriter();
		StreamResult result = new StreamResult(writer);
		transformer.transform(source, result);
		String output = writer.getBuffer().toString(); // .replaceAll("\n|\r", "");
		try {
			writer.close();
		} catch (Exception ex) {
		}
		return output;
	}
	
	/**
	 * Map转xml数据
	 */
	public static String mapToXml2(Map<String, String> param) {
		StringBuffer sb = new StringBuffer();
		sb.append("<xml>");
		for (Map.Entry<String, String> entry : param.entrySet()) {
			sb.append("<" + entry.getKey() + ">");
			sb.append(entry.getValue());
			sb.append("</" + entry.getKey() + ">");
		}
		sb.append("</xml>");
		return sb.toString();
	}
	
	public static String sortedMaptoXml(SortedMap<String,String> params) {
        StringBuilder sb = new StringBuilder();
        Set<Entry<String, String>> es = params.entrySet();
        Iterator<Entry<String, String>> it = es.iterator();
        sb.append("<xml>\n");
        while(it.hasNext()) {
            Map.Entry entry = (Map.Entry)it.next();
            String k = (String)entry.getKey();
            Object v = entry.getValue();
            sb.append("<"+k+">");
            sb.append(v);
            sb.append("</"+k+">\n");
        }
        sb.append("</xml>");
        return sb.toString();
    }
}
