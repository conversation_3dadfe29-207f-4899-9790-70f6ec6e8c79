package com.taurus.payment.utils;

import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.io.Writer;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.URL;
import java.net.URLEncoder;
import java.net.UnknownHostException;
import java.security.MessageDigest;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.UUID;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import org.jdom.Document;
import org.jdom.Element;
import org.jdom.input.SAXBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.taurus.payment.entity.UnifiedOrderRequest;
import com.taurus.payment.entity.UnifiedOrderRespose;
import com.taurus.payment.entity.business.WXPayConstants;
import com.taurus.payment.entity.business.WXPayConstants.SignType;
import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.core.util.QuickWriter;
import com.thoughtworks.xstream.io.HierarchicalStreamWriter;
import com.thoughtworks.xstream.io.xml.PrettyPrintWriter;
import com.thoughtworks.xstream.io.xml.XppDriver;

/**
 * 支付工具类
 * 
 * <AUTHOR>
 */
public class WXPayUtil {

	private static final String DEF_CHATSET = "UTF-8";
	private static final int DEF_CONN_TIMEOUT = 60000;
	private static final int DEF_READ_TIMEOUT = 60000;

	private static Logger log = LoggerFactory.getLogger(WXPayUtil.class);

	/**
	 * 生成订单对象信息
	 * 
	 * @param orderId          订单号
	 * @param appId            微信appId
	 * @param mch_id           微信分配的商户ID
	 * @param body             支付介绍主体
	 * @param price            支付价格（放大100倍）
	 * @param spbill_create_ip 终端IP
	 * @param notify_url       异步直接结果通知接口地址
	 * @param noncestr
	 * @return
	 * @throws Exception
	 */
	public static Map<String, Object> createOrderInfo(Map<String, String> requestMap) throws Exception {
		// 生成订单对象
		UnifiedOrderRequest unifiedOrderRequest = new UnifiedOrderRequest();
		unifiedOrderRequest.setAppid(requestMap.get("appId"));// 公众账号ID
		unifiedOrderRequest.setBody(requestMap.get("body"));// 商品描述
		unifiedOrderRequest.setMch_id(requestMap.get("mch_id"));// 商户号
		unifiedOrderRequest.setNonce_str(requestMap.get("noncestr"));// 随机字符串
		unifiedOrderRequest.setNotify_url(requestMap.get("notify_url"));// 通知地址
		unifiedOrderRequest.setOpenid(requestMap.get("userWeixinOpenId"));
		unifiedOrderRequest.setDetail(requestMap.get("detail"));// 详情
		unifiedOrderRequest.setOut_trade_no(requestMap.get("out_trade_no"));// 商户订单号
		unifiedOrderRequest.setSpbill_create_ip(requestMap.get("spbill_create_ip"));// 终端IP
		unifiedOrderRequest.setTotal_fee(convertToFenValue(Double.parseDouble(requestMap.get("payMoney")))); // 金额需要扩大100倍:1代表支付时是0.01
		unifiedOrderRequest.setTrade_type("JSAPI");// JSAPI--公众号支付、NATIVE--原生扫码支付、APP--app支付
		SortedMap<String, String> packageParams = new TreeMap<String, String>();
		packageParams.put("appid", unifiedOrderRequest.getAppid()); // 生成prepay_id时appid是小写的i,生成paySign时，appId是大写的I
		packageParams.put("body", unifiedOrderRequest.getBody());
		packageParams.put("mch_id", unifiedOrderRequest.getMch_id());
		packageParams.put("nonce_str", unifiedOrderRequest.getNonce_str());
		packageParams.put("notify_url", unifiedOrderRequest.getNotify_url());
		packageParams.put("openid", unifiedOrderRequest.getOpenid());
		packageParams.put("detail", unifiedOrderRequest.getDetail());
		packageParams.put("out_trade_no", unifiedOrderRequest.getOut_trade_no());
		packageParams.put("spbill_create_ip", unifiedOrderRequest.getSpbill_create_ip());
		packageParams.put("total_fee", unifiedOrderRequest.getTotal_fee());
		packageParams.put("trade_type", unifiedOrderRequest.getTrade_type());
		try {
			unifiedOrderRequest.setSign(generateSignature(packageParams, "jiarenhuiDFGH71928asashashdashdj"));// 签名
		} catch (Exception e) {
			e.printStackTrace();
		}
		// 将订单对象转为xml格式
		xstream.alias("xml", UnifiedOrderRequest.class);// 根元素名需要是xml
		System.out.println("封装好的统一下单请求数据：" + xstream.toXML(unifiedOrderRequest).replace("__", "_"));
		Map<String, Object> responseMap = new HashMap<String, Object>();
		responseMap.put("orderInfo_toString", xstream.toXML(unifiedOrderRequest).replace("__", "_"));
		responseMap.put("unifiedOrderRequest", unifiedOrderRequest);
		return responseMap;
	}

	/**
	 * 生成签名
	 * 
	 * @param appid_value
	 * @param mch_id_value
	 * @param productId
	 * @param nonce_str_value
	 * @param trade_type
	 * @param notify_url
	 * @param spbill_create_ip
	 * @param total_fee
	 * @param out_trade_no
	 * @return
	 */
	private static String createSign(UnifiedOrderRequest unifiedOrderRequest) {
		// 根据规则创建可排序的map集合
		SortedMap<String, String> packageParams = new TreeMap<String, String>();
		packageParams.put("appid", unifiedOrderRequest.getAppid());
		packageParams.put("body", unifiedOrderRequest.getBody());
		packageParams.put("mch_id", unifiedOrderRequest.getMch_id());
		packageParams.put("nonce_str", unifiedOrderRequest.getNonce_str());
		packageParams.put("notify_url", unifiedOrderRequest.getNotify_url());
		packageParams.put("out_trade_no", unifiedOrderRequest.getOut_trade_no());
		packageParams.put("spbill_create_ip", unifiedOrderRequest.getSpbill_create_ip());
		packageParams.put("trade_type", unifiedOrderRequest.getTrade_type());
		packageParams.put("total_fee", unifiedOrderRequest.getTotal_fee());
		StringBuffer sb = new StringBuffer();
		Set<?> es = packageParams.entrySet();// 字典序
		Iterator<?> it = es.iterator();
		while (it.hasNext()) {
			Map.Entry entry = (Map.Entry) it.next();
			String k = (String) entry.getKey();
			String v = (String) entry.getValue();
			// 为空不参与签名、参数名区分大小写
			if (null != v && !"".equals(v) && !"sign".equals(k) && !"key".equals(k)) {
				sb.append(k + "=" + v + "&");
			}
		}
		// 第二步拼接key，key设置路径：微信商户平台(pay.weixin.qq.com)-->账户设置-->API安全-->密钥设置
		sb.append("key=" + "你的密匙");
		String sign = MD5Util.MD5(sb.toString()).toUpperCase();// MD5加密
		log.error("方式一生成的签名=" + sign);
		return sign;
	}

	private static XStream xstream = new XStream(new XppDriver() {
		public HierarchicalStreamWriter createWriter(Writer out) {
			return new PrettyPrintWriter(out) {
				// 对所有xml节点的转换都增加CDATA标记
				boolean cdata = true;
				String NodeName = "";

				@SuppressWarnings("unchecked")
				public void startNode(String name, Class clazz) {
					NodeName = name;
					super.startNode(name, clazz);
				}

				protected void writeText(QuickWriter writer, String text) {
					if (cdata) {
						if (!NodeName.equals("detail")) {
							writer.write(text);
						} else {
							writer.write("<![CDATA[");
							writer.write(text);
							writer.write("]]>");
						}
					} else {
						writer.write(text);
					}
				}
			};
		}
	});

	// xml解析
	public static SortedMap<String, String> doXMLParseWithSorted(String strxml) throws Exception {
		strxml = strxml.replaceFirst("encoding=\".*\"", "encoding=\"UTF-8\"");
		if (null == strxml || "".equals(strxml)) {
			return null;
		}
		SortedMap<String, String> m = new TreeMap<String, String>();
		InputStream in = new ByteArrayInputStream(strxml.getBytes("UTF-8"));
		SAXBuilder builder = new SAXBuilder();
		Document doc = builder.build(in);
		Element root = doc.getRootElement();
		List list = root.getChildren();
		Iterator it = list.iterator();
		while (it.hasNext()) {
			Element e = (Element) it.next();
			String k = e.getName();
			String v = "";
			List children = e.getChildren();
			if (children.isEmpty()) {
				v = e.getTextNormalize();
			} else {
				v = getChildrenText(children);
			}
			m.put(k, v);
		}
		// 关闭流
		in.close();
		return m;
	}

	public static String getChildrenText(List children) {
		StringBuffer sb = new StringBuffer();
		if (!children.isEmpty()) {
			Iterator it = children.iterator();
			while (it.hasNext()) {
				Element e = (Element) it.next();
				String name = e.getName();
				String value = e.getTextNormalize();
				List list = e.getChildren();
				sb.append("<" + name + ">");
				if (!list.isEmpty()) {
					sb.append(getChildrenText(list));
				}
				sb.append(value);
				sb.append("</" + name + ">");
			}
		}
		return sb.toString();
	}

	/**
	 * 调统一下单API
	 * 
	 * @param orderInfo
	 * @return
	 */
	public static UnifiedOrderRespose httpOrder(String orderInfo) {
		String url = "https://api.mch.weixin.qq.com/pay/unifiedorder";
		try {
			HttpURLConnection conn = (HttpURLConnection) new URL(url).openConnection();
			// 加入数据
			conn.setRequestMethod("POST");
			conn.setDoOutput(true);
			BufferedOutputStream buffOutStr = new BufferedOutputStream(conn.getOutputStream());
			buffOutStr.write(orderInfo.getBytes("UTF-8"));
			buffOutStr.flush();
			buffOutStr.close();
			// 获取输入流
			BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
			String line = null;
			StringBuffer sb = new StringBuffer();
			while ((line = reader.readLine()) != null) {
				sb.append(line);
			}
			// 将请求返回的内容通过xStream转换为UnifiedOrderRespose对象
			xstream.alias("xml", UnifiedOrderRespose.class);
			UnifiedOrderRespose unifiedOrderRespose = (UnifiedOrderRespose) xstream.fromXML(sb.toString());
			return unifiedOrderRespose;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}


	/**
	 * 生成带有 sign 的 XML 格式字符串
	 * 
	 * @param data Map类型数据
	 * @param key  API密钥
	 * @return 含有sign字段的XML
	 */
	public static String generateSignedXml(final Map<String, String> data, String key) throws Exception {
		return generateSignedXml(data, key, SignType.MD5);
	}

	/**
	 * 生成带有 sign 的 XML 格式字符串
	 * 
	 * @param data     Map类型数据
	 * @param key      API密钥
	 * @param signType 签名类型
	 * @return 含有sign字段的XML
	 */
	public static String generateSignedXml(final Map<String, String> data, String key, SignType signType)
			throws Exception {
		String sign = generateSignature(data, key, signType);
		data.put(WXPayConstants.FIELD_SIGN, sign);
		return XMLUtil.mapToXml(data);
	}

	/**
	 * 判断签名是否正确
	 * 
	 * @param xmlStr XML格式数据
	 * @param key    API密钥
	 * @return 签名是否正确
	 * @throws Exception
	 */
	public static boolean isSignatureValid(String xmlStr, String key) throws Exception {
		Map<String, String> data = XMLUtil.xmlToMap(xmlStr);
		if (!data.containsKey(WXPayConstants.FIELD_SIGN)) {
			return false;
		}
		String sign = data.get(WXPayConstants.FIELD_SIGN);
		return generateSignature(data, key).equals(sign);
	}

	/**
	 * 判断签名是否正确，必须包含sign字段，否则返回false。使用MD5签名。
	 * 
	 * @param data Map类型数据
	 * @param key  API密钥
	 * @return 签名是否正确
	 * @throws Exception
	 */
	public static boolean isSignatureValid(Map<String, String> data, String key) throws Exception {
		return isSignatureValid(data, key, SignType.MD5);
	}

	/**
	 * 判断签名是否正确，必须包含sign字段，否则返回false。
	 * 
	 * @param data     Map类型数据
	 * @param key      API密钥
	 * @param signType 签名方式
	 * @return 签名是否正确
	 * @throws Exception
	 */
	public static boolean isSignatureValid(Map<String, String> data, String key, SignType signType) throws Exception {
		if (!data.containsKey(WXPayConstants.FIELD_SIGN)) {
			return false;
		}
		String sign = data.get(WXPayConstants.FIELD_SIGN);
		return generateSignature(data, key, signType).equals(sign);
	}

	/**
	 * 生成签名
	 * 
	 * @param data 待签名数据
	 * @param key  API密钥
	 * @return 签名
	 */
	public static String generateSignature(final Map<String, String> data, String key) throws Exception {
		return generateSignature(data, key, SignType.MD5);
	}

	/**
	 * 生成签名. 注意，若含有sign_type字段，必须和signType参数保持一致。
	 * 
	 * @param data     待签名数据
	 * @param key      API密钥
	 * @param signType 签名方式
	 * @return 签名
	 */
	public static String generateSignature(final Map<String, String> data, String key, SignType signType)
			throws Exception {
		Set<String> keySet = data.keySet();
		String[] keyArray = keySet.toArray(new String[keySet.size()]);
		Arrays.sort(keyArray);
		StringBuilder sb = new StringBuilder();
		for (String k : keyArray) {
			if (k.equals(WXPayConstants.FIELD_SIGN)) {
				continue;
			}
			if (data.get(k).trim().length() > 0) // 参数值为空，则不参与签名
				sb.append(k).append("=").append(data.get(k).trim()).append("&");
		}
		sb.append("key=").append(key);
		if (SignType.MD5.equals(signType)) {
			return MD5(sb.toString()).toUpperCase();
		} else if (SignType.HMACSHA256.equals(signType)) {
			return HMACSHA256(sb.toString(), key);
		} else {
			log.error("获取签名失败，失败原因：" + String.format("Invalid sign_type: %s", signType));
			throw new Exception(String.format("Invalid sign_type: %s", signType));
		}
	}

	/**
	 * 获取随机字符串 Nonce Str
	 * 
	 * @return String 随机字符串
	 */
	public static String generateNonceStr() {
		return UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32);
	}


	/**
	 * 生成 MD5
	 * 
	 * @param data 待处理数据
	 * @return MD5结果
	 */
	public static String MD5(String data) throws Exception {
		java.security.MessageDigest md = MessageDigest.getInstance("MD5");
		byte[] array = md.digest(data.getBytes("UTF-8"));
		StringBuilder sb = new StringBuilder();
		for (byte item : array) {
			sb.append(Integer.toHexString((item & 0xFF) | 0x100).substring(1, 3));
		}
		return sb.toString().toUpperCase();
	}

	/**
	 * 生成 HMACSHA256
	 * 
	 * @param data 待处理数据
	 * @param key  密钥
	 * @return 加密结果
	 * @throws Exception
	 */
	public static String HMACSHA256(String data, String key) throws Exception {
		Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
		SecretKeySpec secret_key = new SecretKeySpec(key.getBytes("UTF-8"), "HmacSHA256");
		sha256_HMAC.init(secret_key);
		byte[] array = sha256_HMAC.doFinal(data.getBytes("UTF-8"));
		StringBuilder sb = new StringBuilder();
		for (byte item : array) {
			sb.append(Integer.toHexString((item & 0xFF) | 0x100).substring(1, 3));
		}
		return sb.toString().toUpperCase();
	}

	/**
	 * 日志
	 * 
	 * @return
	 */
	public static Logger getLogger() {
		Logger logger = LoggerFactory.getLogger("wxpay java sdk");
		return logger;
	}

	/**
	 * 获取当前时间戳，单位秒
	 * 
	 * @return
	 */
	public static long getCurrentTimestamp() {
		return System.currentTimeMillis() / 1000;
	}

	/**
	 * 获取当前时间戳，单位毫秒
	 * 
	 * @return
	 */
	public static long getCurrentTimestampMs() {
		return System.currentTimeMillis();
	}

	/**
	 * 生成 uuid， 即用来标识一笔单，也用做 nonce_str
	 * 
	 * @return
	 */
	public static String generateUUID() {
		return UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32);
	}

	/**
	 * 支付签名
	 * 
	 * @param timestamp
	 * @param noncestr
	 * @param packages
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	public static String paySign(String timestamp, String noncestr, String packages, String appId) {
		Map<String, String> paras = new HashMap<String, String>();
		paras.put("appid", appId);
		paras.put("timestamp", timestamp);
		paras.put("noncestr", noncestr);
		paras.put("package", packages);
		paras.put("signType", "MD5");
		StringBuffer sb = new StringBuffer();
		Set es = paras.entrySet();// 字典序
		Iterator it = es.iterator();
		while (it.hasNext()) {
			Map.Entry entry = (Map.Entry) it.next();
			String k = (String) entry.getKey();
			String v = (String) entry.getValue();
			// 为空不参与签名、参数名区分大小写
			if (null != v && !"".equals(v) && !"sign".equals(k) && !"key".equals(k)) {
				sb.append(k + "=" + v + "&");
			}
		}
		String sign = MD5Util.MD5(sb.toString()).toUpperCase();// MD5加密
		return sign;
	}

	/**
	 * 将人民币元换算成分
	 * 注意少一分钱的问题
	 * @param money
	 * @return
	 * <AUTHOR>
	 */
	public static String convertToFenValue(Double money) {
		Double fen = money*1000/10;
		int fenint = fen.intValue();
		return String.valueOf(fenint);
	}

	/**
	 * POST请求
	 * 
	 * @param url 请求地址
	 * @param xml xml数据
	 * @return
	 * @throws Exception
	 */
	public static String doPostXml(String url, String xml) {
		HttpURLConnection conn=null;
		BufferedReader reader = null;
		try {
			conn = (HttpURLConnection) new URL(url).openConnection();
			// 加入数据
			conn.setRequestMethod("POST");
			conn.setRequestProperty("Content-Type", "text/xml;charset=utf-8");
			conn.setDoOutput(true);
			BufferedOutputStream buffOutStr = new BufferedOutputStream(conn.getOutputStream());
			buffOutStr.write(xml.getBytes("UTF-8"));
			buffOutStr.flush();
			buffOutStr.close();
			// 获取输入流
			reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
			String line = null;
			StringBuffer sb = new StringBuffer();
			while ((line = reader.readLine()) != null) {
				sb.append(line);
			}
			return sb.toString();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (reader != null) {
				try {
					reader.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
			if (conn != null) {
				conn.disconnect();
			}
		}
		return null;
	}

	/**
	 *
	 * @param strUrl 访问地址路径url
	 * @param params 提交的参数map集合
	 * @param method 访问方式GET或者POST
	 * @param token  权限验证，如果没有选择空即可
	 * @return
	 * @throws Exception
	 */
	public static String net(String strUrl, Map<String, String> params, String method) {
		HttpURLConnection conn = null;
		BufferedReader reader = null;
		String rs = null;
		try {
			StringBuffer sb = new StringBuffer();
			if (method == null || method.equals("GET")) {
				strUrl = strUrl + "?" + urlencode(params);
			}
			URL url = new URL(strUrl);
			conn = (HttpURLConnection) url.openConnection();
			if (method == null || method.equals("GET")) {
				conn.setRequestMethod("GET");
			} else {
				conn.setDoOutput(true);
				conn.setRequestMethod("POST");
				if ("json".equals(params.get("paramType"))) {
					conn.setRequestProperty("Content-Type", "application/json");
				} else {
					conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
				}
				conn.setRequestProperty("Content-Length", "content.length");

			}
			conn.setDoOutput(true);
			conn.setDoInput(true);
			conn.setUseCaches(false);
			conn.setRequestProperty("Charset", DEF_CHATSET);
			conn.setConnectTimeout(DEF_CONN_TIMEOUT);
			conn.setReadTimeout(DEF_READ_TIMEOUT);
			conn.setInstanceFollowRedirects(false);
			conn.connect();
			if (params != null && method.equals("POST")) {
				try {
					DataOutputStream out = new DataOutputStream(conn.getOutputStream());
					if ("json".equals(params.get("paramType"))) {
						// 如果接收参数为实体类型，就进行json转码然后发送
						out.writeBytes(params.get("json").toString());
					} else {
						// 如果接收的参数为几个字段类型，就是用键值对的形式进行urlencode进行编码发送
						out.writeBytes(urlencode(params));
					}
				} catch (Exception e) {
					// TODO: handle exception
				}
			}
			InputStream is = conn.getInputStream();
			reader = new BufferedReader(new InputStreamReader(is, DEF_CHATSET));
			String strRead = null;
			while ((strRead = reader.readLine()) != null) {
				sb.append(strRead);
			}
			rs = sb.toString();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			if (reader != null) {
				try {
					reader.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
			if (conn != null) {
				conn.disconnect();
			}
		}
		return rs;
	}

	// 将map型转为请求参数型（get访问时使用）
	public static String urlencode(Map<String, String> data) {
		StringBuilder sb = new StringBuilder();
		for (Map.Entry i : data.entrySet()) {
			try {
				sb.append(i.getKey()).append("=").append(URLEncoder.encode(i.getValue() + "", "UTF-8")).append("&");
			} catch (UnsupportedEncodingException e) {
				e.printStackTrace();
			}
		}
		return sb.toString();
	}

	/**
	 * 获取本地ip
	 * @return
	 */
	public static String getLocalIP() {   
        InetAddress addr = null;   
        try {
            addr = InetAddress.getLocalHost();
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }         
        byte[] ipAddr = addr.getAddress();   
        String ipAddrStr = "";   
        for (int i = 0; i < ipAddr.length; i++) {   
            if (i > 0) {   
                ipAddrStr += ".";   
            }   
            ipAddrStr += ipAddr[i] & 0xFF;   
        }   
        return ipAddrStr;   
    }

	
	/**
     * @Title: createSign
     * @Description: 签名算法,创建md5摘要,规则是:按参数名称a-z排序,遇到空值的参数不参加签名。
     * 参照：https://pay.weixin.qq.com/wiki/doc/api/tools/mch_pay.php?chapter=4_3
     */
    public static String createSign(SortedMap<String, String> packageParams, String ApiKey) {
        StringBuffer sb = new StringBuffer();
        Set<Entry<String, String>> es = packageParams.entrySet();
        Iterator<Entry<String, String>> it = es.iterator();
        while (it.hasNext()) {
            Map.Entry entry = (Map.Entry) it.next();
            String k = (String) entry.getKey();
            String v = (String) entry.getValue();
            if (null != v && !"".equals(v) && !"sign".equals(k) && !"key".equals(k)) {
                sb.append(k + "=" + v + "&");
            }
        }
        sb.append("key=" + ApiKey);
        String sign = MD5Util.MD5Encode(sb.toString(), "UTF-8").toUpperCase();
        return sign;
    }
}
