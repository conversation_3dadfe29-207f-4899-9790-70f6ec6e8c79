package com.taurus.payment.utils;

public class WeChatPayConfig {
	//微信小程序appid
    public static final String small_appid = "";
    //微信公众号appid
    public static final String public_appid = "";
    //微信支付的商户id
    public static final String mch_id = "";
    //微信支付的商户密钥
    public static final String key = "";

    //签名方式，固定值
    public static final String SIGNTYPE = "MD5";
    //交易类型，小程序/JSAPI支付的固定值为JSAPI
    public static final String TRADETYPE = "JSAPI";
    //微信统一下单接口地址
    public static final String pay_url = "https://api.mch.weixin.qq.com/pay/unifiedorder";
    // 委托扣款-请求扣款
    public static final String pay_apply_url = "https://api.mch.weixin.qq.com/pay/pappayapply";
    // 微信订单查询
    public static final String order_query_url = "https://api.mch.weixin.qq.com/pay/orderquery";
    
}
 