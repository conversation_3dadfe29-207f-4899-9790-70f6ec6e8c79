package com.taurus.payment;

import javax.servlet.Filter;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration("paymentWebFilterConfig")
public class WebFilterConfig {

	// @Bean
	// FilterRegistrationBean<Filter> jwtFilterRegistration() {
	// FilterRegistrationBean<Filter> registration = new FilterRegistrationBean<>();
	// registration.setFilter(jwtFilter());
	// registration.addUrlPatterns("/test/*");// 先不启用
	// //registration.setName("JwtFilter");
	// registration.setOrder(2);
	// return registration;
	// }

	@Bean("paymentLocationFilterRegistration")
	FilterRegistrationBean<Filter> locationFilterRegistration() {
		FilterRegistrationBean<Filter> registration = new FilterRegistrationBean<>();
		registration.setFilter(locationFilter());
		registration.addUrlPatterns("/examination/*");
		registration.setName("locationFilter");
		registration.setOrder(3);
		return registration;
	}

	/*
	 * @Bean
	 * public Filter jwtFilter() {
	 * return new com.qkk.filter.JwtFilter();
	 * }
	 */

	@Bean("paymentLocationFilter")
	Filter locationFilter() {
		return new com.taurus.filter.LocationFilter();
	}
}
