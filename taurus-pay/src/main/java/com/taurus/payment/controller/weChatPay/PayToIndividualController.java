package com.taurus.payment.controller.weChatPay;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.taurus.entity.ResponseObject;
import com.taurus.payment.service.wechatPay.PayToIndividualService;
import com.taurus.payment.utils.WXPayUtil;

/**
 * 微信支付，付款给个人
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/payToIndividual")
public class PayToIndividualController {
	@Autowired
	private PayToIndividualService payToIndividualService;

	/**
	 * 付款给个人
	 * 
	 * @param openid
	 * @return
	 */
	@PostMapping("/pay")
	public ResponseObject payToIndividual(@RequestBody JSONObject json) {
		try {
			String openid = json.getString("openid");
			String amount = json.getString("amount");// 单位：分
			String desc = json.getString("desc");

			// openid="oGNg34_HZbvR-XgTyC1-m9ZMgPI0";
			// amount="100";
			// desc="测试付款";

			if (amount == null || openid == null || desc == null) {
				return ResponseObject.failure("PARAMETERS_ERROR");
			}

			String ip = WXPayUtil.getLocalIP();
			JSONObject res = payToIndividualService.payToIndividual(openid, ip, amount, desc);
			return ResponseObject.success(res);

		} catch (Exception ex) {
			ex.printStackTrace();
			return ResponseObject.failure("ERROR");
		}
	}

}
