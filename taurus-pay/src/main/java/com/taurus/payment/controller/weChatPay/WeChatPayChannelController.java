/**
 * 微信支付中心-渠道端支付参数配置
 */
package com.taurus.payment.controller.weChatPay;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.taurus.payment.entity.WeChatPayChannel;
import com.taurus.payment.service.wechatPay.WeChatPayChannelService;


/**
 * 支付渠道
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping(value = "/channel")
public class WeChatPayChannelController {
	
	private static Logger logger = LoggerFactory.getLogger(WeChatPayChannelController.class);

	@Autowired
	private WeChatPayChannelService weChatPayChannelService;
	
	

	
	/**
	 * 创建微信支付渠道调用端参数组
	 * 连接支付中心的每个支付渠道都定义了一组属性
	 * @param configJson
	 * @return
	 */
	@RequestMapping("/create")
	public JSONObject createChannel(@RequestParam(value = "channel") String channelStr) {
		JSONObject json = new JSONObject();
		try {
			WeChatPayChannel channel = JSON.parseObject(channelStr, WeChatPayChannel.class);
			if (channel == null || StringUtils.isEmpty(channel.getAppid()) || StringUtils.isEmpty(channel.getOriginChannel())
					|| StringUtils.isEmpty(channel.getMchId()) || StringUtils.isEmpty(channel.getKey())) {
				json.put("result_code", "ERROR");
				return json;
			}
			WeChatPayChannel oldConfig = weChatPayChannelService.getByOriginChannel(channel.getOriginChannel());
			if (oldConfig != null) {
				json.put("result_code", "ERROR");
				return json;
			}
			weChatPayChannelService.save(channel);
			json.put("result_code", "OK");
			return json;
		} catch (Exception e) {
			e.printStackTrace();
			json.put("result_code", "ERROR");
			return json;
		}
	}

	@RequestMapping("/update")
	public JSONObject updateBasicConfig(@RequestParam(value = "configJson") String channelStr) {
		JSONObject json = new JSONObject();
		try {
			WeChatPayChannel channel = JSON.parseObject(channelStr, WeChatPayChannel.class);
			if (channel == null || channel.getId()==0 || StringUtils.isEmpty(channel.getAppid()) || StringUtils.isEmpty(channel.getOriginChannel())
					|| StringUtils.isEmpty(channel.getMchId()) || StringUtils.isEmpty(channel.getKey())) {
				json.put("result_code", "ERROR");
				return json;
			}
			weChatPayChannelService.update(channel);
			json.put("result_code", "OK");
			return json;
		}catch(Exception e) {
			e.printStackTrace();
			json.put("result_code", "ERROR");
			return json;
		}
	}
	
	
	@RequestMapping("/get")
	public WeChatPayChannel getByChannelName(@RequestParam(value = "orginChannel") String originChannel,
			HttpServletRequest request, HttpServletResponse response) {
		logger.info("查询基础数据，参数originChannel: " + originChannel);
		return weChatPayChannelService.getByOriginChannel(originChannel);
	}
	

}
