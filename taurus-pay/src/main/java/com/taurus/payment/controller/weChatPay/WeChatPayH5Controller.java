package com.taurus.payment.controller.weChatPay;

import java.io.BufferedOutputStream;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.taurus.payment.entity.Payment;
import com.taurus.payment.service.wechatPay.WeChatPayH5Service;
import com.taurus.payment.utils.AddressUtils;
import com.taurus.payment.utils.ConfigUtil;
import com.taurus.payment.utils.DateUtils;
import com.taurus.payment.utils.HttpUtil;
import com.taurus.payment.utils.MobileUtil;
import com.taurus.payment.utils.PayCommonUtil;
import com.taurus.payment.utils.XMLUtil;

/**
 * H5支付
 * H5支付主要是在手机、ipad等移动设备中通过浏览器来唤起微信支付的支付产品。
 */
@Controller
@RequestMapping(value = "/weChatPayH5")
public class WeChatPayH5Controller {

	private static final Logger logger = LoggerFactory.getLogger(WeChatPayH5Controller.class);

	@Autowired
	private WeChatPayH5Service weChatPayH5Service;

	@Value("${wx.notifyUrl}")
	private String notify_url;

	@PostMapping("pay")
	public String pay(@RequestBody Payment payment, ModelMap map) {
		logger.info("H5支付(需要公众号内支付)");
		String url = weChatPayH5Service.weixinPayMobile(payment);
		return "redirect:" + url;
	}

	@PostMapping("payPage")
	public String pay(HttpServletRequest request, HttpServletResponse response) throws Exception {
		// 这里因为无法测试、模板下是个JSP页面、无法正常运行，请自行修改逻辑
		return "weixinpay/payPage";
	}

	@ResponseBody
	@GetMapping("h5pay")
	public String h5pay(HttpServletRequest request, String body, String outTradeNo, String totalFee,
			String originChannel) {

		logger.info("纯H5支付(不建议在APP端使用)");
		Payment payment = new Payment();

		payment.setOutTradeNo(outTradeNo);
		payment.setTotalFee(Double.parseDouble(totalFee));
		payment.setOriginChannel(originChannel);

		// @RequestBody Payment payment //,ModelMap map
		// mweb_url为拉起微信支付收银台的中间页面，可通过访问该url来拉起微信客户端，完成支付,mweb_url的有效期为5分钟。
		String mweb_url = weChatPayH5Service.weixinPayH5(payment);
		if (StringUtils.isNotBlank(mweb_url)) {
			// 回调设置
			// String redirect_url =
			// "******此处写自己的域名******/faint-service/static/h5/app/successh5.html";
			// String redirect_urlEncode = URLEncoder.encode(redirect_url,
			// "utf-8");//对上面地址urlencode
			// mweb_url = mweb_url + "&redirect_url=" + redirect_urlEncode;//拼接返回地址

			return mweb_url;
		} else {
			return "redirect:https://www.51kaoshi.wang";// 自定义错误页面
		}
	}

	@PostMapping("smallRoutine")
	public String smallRoutine(@RequestBody Payment payment, ModelMap map) {
		logger.info("小程序支付(需要HTTPS)、不需要支付目录和授权域名");
		String url = weChatPayH5Service.weixinPayMobile(payment);
		return "redirect:" + url;
	}

	/**
	 * 预下单(对于已经产生的订单)
	 * 
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception String
	 *
	 */

	@PostMapping("dopay")
	public String dopay(HttpServletRequest request, HttpServletResponse response) throws Exception {
		String orderNo = request.getParameter("outTradeNo");
		String totalFee = request.getParameter("totalFee");
		// 获取code 这个在微信支付调用时会自动加上这个参数 无须设置
		String code = request.getParameter("code");
		// 获取用户openID(JSAPI支付必须传openid)
		String openId = MobileUtil.getOpenId(code);
		String trade_type = "JSAPI";// 交易类型H5支付 也可以是小程序支付参数
		SortedMap<Object, Object> packageParams = new TreeMap<Object, Object>();
		ConfigUtil.commonParams(packageParams);
		packageParams.put("body", "报告");// 商品描述
		packageParams.put("out_trade_no", orderNo);// 商户订单号
		packageParams.put("total_fee", totalFee);// 总金额
		packageParams.put("spbill_create_ip", AddressUtils.getIpAddr(request));// 发起人IP地址
		packageParams.put("notify_url", notify_url);// 回调地址
		packageParams.put("trade_type", trade_type);// 交易类型
		packageParams.put("openid", openId);// 用户openID
		String sign = PayCommonUtil.createSign("UTF-8", packageParams, ConfigUtil.API_KEY);
		packageParams.put("sign", sign);// 签名
		String requestXML = PayCommonUtil.getRequestXml(packageParams);
		String resXml = HttpUtil.post(ConfigUtil.UNIFIED_ORDER_URL, requestXML);
		Map<String, String> map = XMLUtil.doXMLParse(resXml);
		String returnCode = (String) map.get("return_code");
		String returnMsg = (String) map.get("return_msg");
		StringBuffer url = new StringBuffer();
		if ("SUCCESS".equals(returnCode)) {
			String resultCode = (String) map.get("result_code");
			String errCodeDes = (String) map.get("err_code_des");
			if ("SUCCESS".equals(resultCode)) {
				// 获取预支付交易会话标识
				String prepay_id = (String) map.get("prepay_id");
				String prepay_id2 = "prepay_id=" + prepay_id;
				String packages = prepay_id2;
				SortedMap<Object, Object> finalpackage = new TreeMap<>();
				String timestamp = DateUtils.getTimestamp();
				String nonceStr = packageParams.get("nonce_str").toString();
				finalpackage.put("appId", com.taurus.payment.utils.ConfigUtil.APP_ID);
				finalpackage.put("timeStamp", timestamp);
				finalpackage.put("nonceStr", nonceStr);
				finalpackage.put("package", packages);
				finalpackage.put("signType", "MD5");
				// 这里很重要 参数一定要正确 狗日的腾讯 参数到这里就成大写了
				// 可能报错信息(支付验证签名失败 get_brand_wcpay_request:fail)
				sign = PayCommonUtil.createSign("UTF-8", finalpackage, ConfigUtil.API_KEY);
				url.append("redirect:/weixinMobile/payPage?");
				url.append("timeStamp=" + timestamp + "&nonceStr=" + nonceStr + "&package=" + packages);
				url.append("&signType=MD5" + "&paySign=" + sign + "&appid=" + ConfigUtil.APP_ID);
				url.append("&orderNo=" + orderNo + "&totalFee=" + totalFee);
			} else {
				logger.info("订单号:{}错误信息:{}", orderNo, errCodeDes);
				url.append("redirect:/weixinMobile/error?code=0&orderNo=" + orderNo);// 该订单已支付
			}
		} else {
			logger.info("订单号:{}错误信息:{}", orderNo, returnMsg);
			url.append("redirect:/weixinMobile/error?code=1&orderNo=" + orderNo);// 系统错误
		}
		return url.toString();
	}

	/**
	 * 手机支付完成回调
	 * 
	 * @param request
	 * @param response
	 *
	 */
	@PostMapping("WXPayBack")
	public void WXPayBack(HttpServletRequest request, HttpServletResponse response) {
		String resXml = "";
		try {
			// 解析XML
			Map<String, String> map = MobileUtil.parseXml(request);
			String return_code = map.get("return_code");// 状态
			String out_trade_no = map.get("out_trade_no");// 订单号
			if (return_code.equals("SUCCESS")) {
				if (out_trade_no != null) {
					// 处理订单逻辑
					logger.info("微信手机支付回调成功订单号:{}", out_trade_no);
					resXml = "<xml>" + "<return_code><![CDATA[SUCCESS]]></return_code>"
							+ "<return_msg><![CDATA[OK]]></return_msg>" + "</xml> ";
				}
			} else {
				logger.info("微信手机支付回调失败订单号:{}", out_trade_no);
				resXml = "<xml>" + "<return_code><![CDATA[FAIL]]></return_code>"
						+ "<return_msg><![CDATA[报文为空]]></return_msg>" + "</xml> ";
			}
		} catch (Exception e) {
			logger.error("手机支付回调通知失败", e);
			resXml = "<xml>" + "<return_code><![CDATA[FAIL]]></return_code>"
					+ "<return_msg><![CDATA[报文为空]]></return_msg>" + "</xml> ";
		}
		try {
			// ------------------------------
			// 处理业务完毕
			// ------------------------------
			BufferedOutputStream out = new BufferedOutputStream(response.getOutputStream());
			out.write(resXml.getBytes());
			out.flush();
			out.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}