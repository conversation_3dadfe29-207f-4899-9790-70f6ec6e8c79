package com.taurus.payment.controller.weChatPay;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.taurus.entity.ResponseObject;
import com.taurus.payment.entity.Payment;
import com.taurus.payment.entity.Result;
import com.taurus.payment.entity.WeChatPayChannel;
import com.taurus.payment.service.PaymentService;
import com.taurus.payment.service.wechatPay.WeChatPayChannelService;
import com.taurus.payment.service.wechatPay.WeChatPayNativeService;
import com.taurus.payment.utils.Network;
import com.taurus.payment.utils.PayUtil;
import com.taurus.payment.utils.QrCodeGenerator;
import com.taurus.payment.utils.WXPayUtil;
import com.taurus.payment.utils.WeChatPayConfig;
import com.taurus.payment.utils.XMLUtil;

/**
 * Native支付
 * Native支付是商户系统按微信支付协议生成支付二维码，用户再用微信“扫一扫”完成支付的模式。该模式适用于PC网站支付、实体店单品或订单支付、媒体广告支付等场景。
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping(value = "/weChatPayNative")

public class WeChatPayNativeController {
	private static final Logger logger = LoggerFactory.getLogger(WeChatPayNativeController.class);

	@Value("${wx.notifyUrl}")
	private String notifyUrl;

	@Autowired
	private WeChatPayChannelService weChatPayChannelService;

	@Autowired
	private WeChatPayNativeService weChatPayNativeService;

	@Autowired
	private PaymentService paymentService;

	/**
	 * 
	 * 二维码支付(模式二)下单并生成二维码
	 * 
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/createOrder")
	public ResponseObject createOrder(HttpServletRequest request, HttpServletResponse response) {
		try {
			// 获取参数
			ServletInputStream instream = request.getInputStream();
			StringBuffer sb = new StringBuffer();
			int len = -1;
			byte[] buffer = new byte[1024];
			while ((len = instream.read(buffer)) != -1) {
				sb.append(new String(buffer, 0, len));
			}
			instream.close();
			logger.info("微信Native支付接口接收参数：" + sb.toString());
			JSONObject json = JSONObject.parseObject(sb.toString());
			return handleOrderReq(json, request);

		} catch (Exception e) {
			e.printStackTrace();
			logger.error("微信Native支付接口异常：" + e.getMessage());
			return ResponseObject.failure("IO_EXCEPTION");
		}
	}

	/**
	 * 处理订单请求
	 * 
	 * @param json
	 * @param request
	 * @return
	 * @throws Exception
	 */
	private ResponseObject handleOrderReq(JSONObject json, HttpServletRequest request) throws Exception {
		// 商品名称
		String body = json.getString("body");
		String out_trade_no = json.getString("out_trade_no");
		Object moneyObject = json.get("money");
		Double money = null;
		if (moneyObject instanceof BigDecimal) {
			BigDecimal bigdeci = (BigDecimal) moneyObject;
			money = new Double(bigdeci.doubleValue());
		} else if (moneyObject instanceof Double) {
			money = (Double) moneyObject;
		}

		String originChannel = json.getString("originChannel");

		// 获取来源渠道端的微信支付参数
		WeChatPayChannel channel = weChatPayChannelService.getByOriginChannel(originChannel);
		if (channel == null) {
			return ResponseObject.failure("CHANNEL_NOT_EXISTED");
		}

		String nonce_str = PayUtil.generateRandomUUID();
		if (!StringUtils.isEmpty(body)) {
			// 组装参数，用户生成统一下单接口的签名
			Map<String, String> packageParams = new HashMap<>();
			packageParams.put("appid", channel.getAppid());
			packageParams.put("attach", channel.getOriginChannel());
			packageParams.put("body", body);
			packageParams.put("mch_id", channel.getMchId());
			packageParams.put("nonce_str", nonce_str);
			packageParams.put("notify_url", notifyUrl);

			packageParams.put("out_trade_no", out_trade_no);
			packageParams.put("spbill_create_ip", Network.getRealIp(request));
			packageParams.put("total_fee", WXPayUtil.convertToFenValue(money));
			packageParams.put("trade_type", "NATIVE");

			String sign = WXPayUtil.generateSignature(packageParams, channel.getKey());// 商户api密钥
			packageParams.put("sign", sign);

			// 调用统一下单接口，并接受返回的结果
			String responseXML = WXPayUtil.doPostXml(WeChatPayConfig.pay_url, XMLUtil.mapToXml2(packageParams));

			Map<String, String> map = XMLUtil.xmlToMap(responseXML);
			// 生成二维码图片 不存储 直接以流的形式输出到页面
			if ("SUCCESS".equals(map.get("return_code")) && "SUCCESS".equals(map.get("result_code"))) {
				// 往数据库中写入payment
				Payment oldPay = paymentService.getPayment(out_trade_no, channel.getOriginChannel());
				if (oldPay != null) {
					oldPay.setTotalFee(money);
					oldPay.setTradeType("NATIVE");
					oldPay.setCreateTime(new Date());
					paymentService.updatePayment(oldPay);
				} else {
					Payment pay = new Payment();
					pay.setOutTradeNo(out_trade_no);
					pay.setTotalFee(money);
					pay.setTradeType("NATIVE");
					pay.setOriginChannel(channel.getOriginChannel());
					pay.setCreateTime(new Date());
					paymentService.createPayment(pay);
				}
				// 生成二维码图片 不存储 直接以流的形式输出到页面
				String codeUrl = map.get("code_url");
				String base64Url = QrCodeGenerator.toBase64Format(codeUrl);
				return ResponseObject.success(base64Url);
			} else {
				return ResponseObject.failure("NET_CONNECTION_ERROR");
			}
		} else {
			return ResponseObject.failure("PARAMETERS_ERROR");
		}
	}

	/**
	 * 方式2
	 * 
	 * @param json
	 * @param request
	 * @return
	 * @throws Exception
	 */
	@PostMapping("/createPreOrderByFeign")
	public ResponseObject createPreOrderByFeign(@RequestBody JSONObject json, HttpServletRequest request)
			throws Exception {
		return handleOrderReq(json, request);
	}

	/**
	 * 
	 * 
	 * 
	 * /** 查询支付状态
	 * 
	 * @param out_trade_no  商户订单号
	 * @param originChannel 发起支付请求的来源渠道
	 * @return
	 */
	@RequestMapping(value = "/queryPayStatus", method = RequestMethod.GET)
	public Result queryPayStatus(String out_trade_no, String originChannel) {
		Result result = null;
		// 获取来源渠道端的微信支付参数
		WeChatPayChannel config = weChatPayChannelService.getByOriginChannel(originChannel);
		String mch_id = config.getMchId();// 商户支付账号
		String appid = config.getAppid();// 应用账号ID
		int x = 0;
		while (true) {

			Map<String, String> map = weChatPayNativeService.queryPayStatus(out_trade_no, mch_id, appid);// 调用查询
			if (map == null) {
				result = new Result(false, "支付发生错误");
				break;
			}
			if (map.get("trade_state").equals("SUCCESS")) {// 支付成功
				result = new Result(true, "支付成功");
				// weChatPayChannelService.updateOrderStatus(out_trade_no,
				// map.get("transaction_id"));//修改订单状态
				break;
			}

			try {
				Thread.sleep(3000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}

			x++;
			if (x >= 100) {
				result = new Result(false, "二维码超时");
				break;
			}

		}
		return result;
	}

}
