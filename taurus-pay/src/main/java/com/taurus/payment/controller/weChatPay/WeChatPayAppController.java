package com.taurus.payment.controller.weChatPay;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.taurus.entity.ResponseObject;
import com.taurus.payment.entity.Payment;
import com.taurus.payment.entity.WeChatPayChannel;
import com.taurus.payment.service.PaymentService;
import com.taurus.payment.service.wechatPay.WeChatPayChannelService;
import com.taurus.payment.utils.Network;
import com.taurus.payment.utils.WXPayUtil;
import com.taurus.payment.utils.WeChatPayConfig;
import com.taurus.payment.utils.XMLUtil;

/**
 * APP内支付
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping(value = "/weChatPayApp")
public class WeChatPayAppController {

	private static org.slf4j.Logger logger = LoggerFactory.getLogger(WeChatPayAppController.class);

	@Value("${wx.notifyUrl}")
	private String notifyUrl;

	@Autowired
	private PaymentService paymentService;

	@Autowired
	private WeChatPayChannelService weChatPayChannelService;

	/**
	 * APP上创建预支付
	 * 方式1
	 * 
	 * @return
	 */
	@RequestMapping("/createPreOrder")
	@ResponseBody
	public Map<String, Object> createPreOrder(HttpServletRequest request, HttpServletResponse response) {
		logger.info("调用微信统一支付接口");
		Map<String, Object> result = new HashMap<>();
		try {
			// 获取参数
			ServletInputStream instream = request.getInputStream();
			StringBuffer sb = new StringBuffer();
			int len = -1;
			byte[] buffer = new byte[1024];
			while ((len = instream.read(buffer)) != -1) {
				sb.append(new String(buffer, 0, len));
			}
			instream.close();

			JSONObject json = JSONObject.parseObject(sb.toString());
			result = handleOrderReq(json, request);

		} catch (Exception e) {
			e.printStackTrace();
			result.put("result", -1);
			result.put("msg", "解析报错：" + e.getMessage());
		}

		return result;
	}

	/**
	 * APP上创建预支付
	 * 方式2
	 * 
	 * @param json
	 * @param request
	 * @return
	 */
	@PostMapping("/createPreOrderByFeign")
	public ResponseObject createPreOrderByFeign(@RequestBody JSONObject json, HttpServletRequest request) {
		logger.info("调用微信统一支付接口");
		Map<String, Object> result = new HashMap<>();
		try {
			result = handleOrderReq(json, request);
			Integer code = (Integer) result.get("result");
			if (code == -1) {
				ResponseObject res = ResponseObject.failure("ERROR");
				res.setMessage((String) result.get("msg"));
				return res;
			} else {
				return ResponseObject.success(result);
			}
		} catch (Exception e) {
			e.printStackTrace();
			ResponseObject res = ResponseObject.failure("ERROR");
			res.setMessage(e.getMessage());
			return res;
		}

	}

	/**
	 * 处理订单
	 * 
	 * @param json
	 * @param request
	 * @return
	 * @throws Exception
	 */
	private Map<String, Object> handleOrderReq(JSONObject json, HttpServletRequest request) throws Exception {
		Map<String, Object> result = new HashMap<>();
		// 商品名称
		String body = (String) json.get("body");
		String out_trade_no = (String) json.get("out_trade_no");

		Object moneyObject = json.get("money");
		Double money = null;
		if (moneyObject instanceof BigDecimal) {
			BigDecimal bigdeci = (BigDecimal) moneyObject;
			money = new Double(bigdeci.doubleValue());
		} else if (moneyObject instanceof Double) {
			money = (Double) moneyObject;
		}

		String originChannel = (String) json.get("originChannel");// 发起支付请求的来源渠道

		// 获取来源渠道端的微信支付参数
		WeChatPayChannel channel = weChatPayChannelService.getByOriginChannel(originChannel);
		if (channel == null) {
			result.put("result", -1);
			result.put("msg", "数据库中未配置originChannel对应的参数，无法调用！");
			return result;
		}

		if (!StringUtils.isEmpty(body) && !StringUtils.isEmpty(out_trade_no) && money != null) {
			String appid = channel.getAppid();// appId
			String mch_id = channel.getMchId();// 支付商户id
			String nonce_str = WXPayUtil.generateNonceStr();// 生成随机字符串
			String spbill_create_ip = Network.getRealIp(request);
			String total_fee = WXPayUtil.convertToFenValue(money);
			String trade_type = "APP";
			// 组装参数，生成统一下单接口的签名
			Map<String, String> signMap = new HashMap<>();
			signMap.put("appid", appid);
			signMap.put("attach", channel.getOriginChannel());// 附带自定义参数，在微信回调接口会原样传回
			signMap.put("body", body);
			signMap.put("mch_id", mch_id);// 商户号
			signMap.put("nonce_str", nonce_str);
			signMap.put("notify_url", notifyUrl);
			signMap.put("out_trade_no", out_trade_no);
			signMap.put("spbill_create_ip", spbill_create_ip);
			signMap.put("total_fee", total_fee);
			signMap.put("trade_type", trade_type);
			// 第一次签名
			String sign = WXPayUtil.generateSignature(signMap, channel.getKey());

			// 拼接xml格式的请求
			StringBuilder XML = new StringBuilder();
			XML.append("<XML>");
			XML.append("<mch_id>" + mch_id + "</mch_id>");
			XML.append("<appid>" + appid + "</appid>");
			XML.append("<attach>" + channel.getOriginChannel() + "</attach>");
			XML.append("<notify_url>" + notifyUrl + "</notify_url>");
			XML.append("<trade_type>" + trade_type + "</trade_type>");
			XML.append("<nonce_str>" + nonce_str + "</nonce_str>");
			XML.append("<sign>" + sign + "</sign>");
			XML.append("<body><![CDATA[" + body + "]]></body>");
			XML.append("<detail><![CDATA[" + "" + "]]></detail>");
			XML.append("<out_trade_no>" + out_trade_no + "</out_trade_no>");
			XML.append("<total_fee>" + total_fee + "</total_fee>");
			XML.append("<spbill_create_ip>" + spbill_create_ip + "</spbill_create_ip>");
			XML.append("</XML>");
			String xml = XML.toString();
			logger.info("统一下单接口请求：");
			logger.info(xml);
			// 调用统一下单接口
			String responseXML = WXPayUtil.doPostXml(WeChatPayConfig.pay_url, xml);
			Map<String, String> responseMap = XMLUtil.xmlToMap(responseXML);
			logger.info("统一下单接口响应：");
			logger.info(JSONObject.toJSONString(responseMap));

			String return_code = responseMap.get("return_code");
			String result_code = responseMap.get("result_code");
			if ("SUCCESS".equals(return_code) && "SUCCESS".equals(result_code)) {
				String prepay_id = responseMap.get("prepay_id");
				String timeStamp = String.valueOf(WXPayUtil.getCurrentTimestamp());
				// 拼接签名需要的参数
				// ！！！切记这里的参数都是小写的！！！
				SortedMap<String, String> packageParams = new TreeMap<String, String>();
				packageParams.put("appid", appid);
				packageParams.put("partnerid", mch_id);
				packageParams.put("prepayid", prepay_id);
				packageParams.put("package", "Sign=WXPay");
				packageParams.put("noncestr", nonce_str);
				packageParams.put("timestamp", timeStamp);
				// 再次签名，这个签名用于APP端调起支付

				logger.info("需要签名的原始数据：{}", JSONObject.toJSONString(packageParams));
				logger.info("key：{}", channel.getKey());
				String paySign = WXPayUtil.generateSignature(packageParams, channel.getKey());
				logger.info("签名后的数据：{}", paySign);
				result.put("sign", paySign);
				result.put("appid", appid);
				result.put("partnerId", mch_id);
				result.put("prepayId", prepay_id);
				result.put("package", "Sign=WXPay");
				result.put("nonceStr", nonce_str);
				result.put("timeStamp", timeStamp);

				// 往数据库中写入payment
				Payment oldPay = paymentService.getPayment(out_trade_no, channel.getOriginChannel());
				if (oldPay != null) {
					oldPay.setTotalFee(money);
					oldPay.setTradeType("APP");
					oldPay.setCreateTime(new Date());
					paymentService.updatePayment(oldPay);
				} else {
					Payment pay = new Payment();
					pay.setOutTradeNo(out_trade_no);
					pay.setTotalFee(money);
					pay.setTradeType("APP");
					pay.setOriginChannel(channel.getOriginChannel());
					pay.setCreateTime(new Date());
					paymentService.createPayment(pay);
				}
				result.put("result", 1);
				result.put("msg", "统一下单成功！");
			} else {
				result.put("result", -1);
				String returnMsg = responseMap.get("return_msg");
				result.put("msg", returnMsg);
			}
		} else {
			result.put("result", -1);
			result.put("msg", "参数缺失或为空");
		}
		return result;
	}

	/**
	 * 微信支付回调接口
	 * 
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/weChatNotify")
	public String weChatNotify(HttpServletRequest request, HttpServletResponse response) {
		logger.info("微信支付回调本接口，通知支付结果：");

		try {
			BufferedReader br = new BufferedReader(new InputStreamReader(request.getInputStream()));
			String line = null;
			StringBuilder sb = new StringBuilder();
			while ((line = br.readLine()) != null) {
				sb.append(line);
			}
			br.close();

			Map<String, String> map = XMLUtil.xmlToMap(sb.toString());
			String return_code = map.get("return_code");

			if (return_code.equals("SUCCESS")) {
				// 以下字段在return_code为SUCCESS的时候有返回
				String out_trade_no = map.get("out_trade_no");
				String originChannel = map.get("attach");

				WeChatPayChannel channel = weChatPayChannelService.getByOriginChannel(originChannel);

				if (WXPayUtil.isSignatureValid(map, channel.getKey())) {

					Payment pay = paymentService.getPayment(out_trade_no, originChannel);
					if (pay != null) {
						/*
						 * 注意要判断微信支付重复回调，支付成功后微信会重复的进行回调 /**此处添加自己的业务逻辑代码
						 **/
						if ("SUCCESS".equals(pay.getResultCode()) || "FAIL".equals(pay.getResultCode())) {
							// 重复通知，已经处理过了，不做任何东西
						} else {
							// 保存微信回调内容
							pay.setReturnCode(return_code);
							pay.setReturnMsg(map.get("return_msg"));
							pay.setErrCode(map.get("err_code"));
							pay.setErrCodeDes(map.get("err_code_des"));
							pay.setTransactionId(map.get("transaction_id"));
							pay.setOpenid(map.get("openid"));
							pay.setTotalFee(Double.valueOf(map.get("total_fee")) / 100);// 分转换为元
							pay.setTimeEnd(map.get("time_end"));
							pay.setTradeType(map.get("trade_type"));// 交易类型：JSAPI、NATIVE、APP
							pay.setCreateTime(new Date());
							pay.setResultCode(map.get("result_code"));// 业务结果

							// 通知originChannel方支付结果
							try {
								Map<String, Object> parameters = new HashMap<>();
								parameters.put("out_trade_no", out_trade_no);
								parameters.put("originChannel", originChannel);
								parameters.put("result_code", map.get("result_code"));
								parameters.put("total_fee", pay.getTotalFee()); // double

								HashMap<String, String> requestMap = new HashMap<>();
								requestMap.put("paramType", "json");
								requestMap.put("json", JSON.toJSONString(parameters));

								// 回调支付渠道接口，通知支付渠道微信支付的结果
								String result = WXPayUtil.net(channel.getNotifyUrl(), requestMap, "POST");

								if (result.equals("SUCCESS")) {
									// 接口调用成功，通知成功
									pay.setIfNotifyOriginChannel(true);
								} else {
									// 接口调用失败，通知失败
									pay.setIfNotifyOriginChannel(false);
								}
								pay.setNotifyOriginChannelResult(result);// 回调来源渠道的结果
								paymentService.updatePayment(pay);

							} catch (Exception e) {
								e.printStackTrace();
								logger.info("接口：" + channel.getNotifyUrl() + "，报错：" + e.getMessage());
							}
						}
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		// 返回微信success表示正常接收到微信的通知
		Map<String, String> responseResult = new HashMap<String, String>();
		responseResult.put("return_code", "SUCCESS");
		responseResult.put("return_msg", "OK");
		return XMLUtil.mapToXml2(responseResult);
	}

	/**
	 * 重新主动回调渠道方 对于第一次回调渠道失败的情况，进行重试，以保证支付结果能通知到渠道方
	 * 
	 * @param originChannel
	 */
	@GetMapping("/notifyChannel")
	public void notifyChannel(String originChannel) {
		WeChatPayChannel channel = weChatPayChannelService.getByOriginChannel(originChannel);
		List<Payment> paymentList = paymentService.getSuccessPayment("WRONG CALL");

		for (Payment pay : paymentList) {

			if (pay != null) {

				// 通知originChannel方支付结果
				try {
					Map<String, Object> parameters = new HashMap<>();
					parameters.put("out_trade_no", pay.getOutTradeNo());
					parameters.put("originChannel", pay.getOriginChannel());
					parameters.put("result_code", pay.getResultCode());
					parameters.put("total_fee", pay.getTotalFee()); // double

					HashMap<String, String> requestMap = new HashMap<>();
					requestMap.put("paramType", "json");
					requestMap.put("json", JSON.toJSONString(parameters));
					// 回调支付渠道接口，通知支付渠道微信支付的结果
					String result = WXPayUtil.net(channel.getNotifyUrl(), requestMap, "POST");

					if (result.equals("SUCCESS")) {
						// 接口调用成功，通知成功
						pay.setIfNotifyOriginChannel(true);
					} else {
						// 接口调用失败，通知失败
						pay.setIfNotifyOriginChannel(false);
					}
					pay.setNotifyOriginChannelResult(result);// 回调来源支付渠道接口的结果

					paymentService.updatePayment(pay);

				} catch (Exception e) {
					e.printStackTrace();
					logger.info("接口：" + channel.getNotifyUrl() + "，报错：" + e.getMessage());
				}

			}

		}

	}
}
