package com.taurus.payment.controller.weChatPay;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.druid.util.StringUtils;
import com.taurus.payment.entity.Payment;
import com.taurus.payment.service.PaymentService;
import com.taurus.payment.utils.Network;
import com.taurus.payment.utils.WXPayUtil;
import com.taurus.payment.utils.WeChatPayConfig;
import com.taurus.payment.utils.XMLUtil;
/**
 * 委托代扣
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping(value = "/weChatPayEntrustedWithhold")
public class WeChatPayEntrustedWithholdController {
	private static final Logger logger = LoggerFactory.getLogger(WeChatPayEntrustedWithholdController.class);
	
	@Autowired
	private PaymentService paymentService;

	/**
	 * 委托代扣-扣款申请 注意回调通知notify_url需要是80端口
	 * 扣款接口请求成功，返回success仅代表扣款申请受理成功，不代表扣款成功。扣款是否成功以支付通知的结果为准。
	 */
	@RequestMapping("/payApply")
	@ResponseBody
	public Map<String, String> payApply(HttpServletRequest request, HttpServletResponse response) throws Exception {
		logger.info("委托代扣-扣款申请");
		Map<String, String> result = new HashMap<>();
		try {
			// 小程序appid
			String appid = WeChatPayConfig.small_appid;
			// 商户号
			String mch_id = WeChatPayConfig.mch_id;
			// 随机字符串
			String nonce_str = WXPayUtil.generateNonceStr();
			// 商品描述
			// String body = "全民考试包月费";
			String body = request.getParameter("body");
			// 商户订单号
			String out_trade_no = request.getParameter("out_trade_no");
			String money = request.getParameter("money");
			// 调用微信支付API的机器IP
			String spbill_create_ip = Network.getRealIp(request);
			// 注意回调通知notify_url需要是80端口,http是80端口、https是443端口
			String notify_url = "http://www.examinationPayment.com/examinationPayment/apply/payApplyNotice";
			// 交易类型PAP-微信委托代扣支付
			String trade_type = "PAP";
			// 委托代扣协议id
			String contract_id = request.getParameter("contract_id");
			if (StringUtils.isEmpty(appid) || StringUtils.isEmpty(mch_id) || StringUtils.isEmpty(nonce_str)
					|| StringUtils.isEmpty(body) || StringUtils.isEmpty(out_trade_no) || StringUtils.isEmpty(money)
					|| StringUtils.isEmpty(spbill_create_ip) || StringUtils.isEmpty(notify_url)
					|| StringUtils.isEmpty(trade_type) || StringUtils.isEmpty(contract_id)) {
				result.put("result", "FAIL");
				result.put("msg", "参数缺失");
//				logger.info("appid: " + appid + "mch_id: " + mch_id + "noncestr: " + noncestr + "body: " + body
//						+ "out_trade_no: " + out_trade_no + "money: " + money + "spbill_create_ip: " + spbill_create_ip
//						+ "notify_url: " + notify_url + "trade_type: " + trade_type + "contract_id: " + contract_id);
				logger.info("接口参数缺失");
				return result;
			}
			// 总金额单位是分
			String total_fee = WXPayUtil.convertToFenValue(Double.parseDouble(money));
			SortedMap<String, String> signMap = new TreeMap<String, String>();
			signMap.put("appid", appid);
			signMap.put("body", body);
			signMap.put("contract_id", contract_id);
			signMap.put("mch_id", mch_id);
			signMap.put("nonce_str", nonce_str);
			signMap.put("notify_url", notify_url);
			signMap.put("out_trade_no", out_trade_no);
			signMap.put("spbill_create_ip", spbill_create_ip);
			signMap.put("total_fee", total_fee);
			signMap.put("trade_type", trade_type);
			// 签名
			String sign = WXPayUtil.generateSignature(signMap, WeChatPayConfig.key);
			// 拼接xml
			StringBuilder XML = new StringBuilder();
			XML.append("<XML>");
			XML.append("<mch_id><![CDATA[" + mch_id + "]]></mch_id>");
			XML.append("<appid><![CDATA[" + appid + "]]></appid>");
			XML.append("<notify_url><![CDATA[" + notify_url + "]]></notify_url>");
			XML.append("<trade_type><![CDATA[" + trade_type + "]]></trade_type>");
			XML.append("<nonce_str><![CDATA[" + nonce_str + "]]></nonce_str>");
			XML.append("<sign><![CDATA[" + sign + "]]></sign>");
			XML.append("<body><![CDATA[" + body + "]]></body>");
			XML.append("<out_trade_no><![CDATA[" + out_trade_no + "]]></out_trade_no>");
			XML.append("<total_fee><![CDATA[" + total_fee + "]]></total_fee>");
			XML.append("<spbill_create_ip><![CDATA[" + spbill_create_ip + "]]></spbill_create_ip>");
			XML.append("<contract_id><![CDATA[" + contract_id + "]]></contract_id>");
			XML.append("</xml>");
			String xml = XML.toString();
			//调用扣款接口
	        String responseXML = WXPayUtil.doPostXml(WeChatPayConfig.pay_apply_url,xml);
	        Map<String, String> responseMap = XMLUtil.xmlToMap(responseXML);
	        String return_code = responseMap.get("return_code");
	        if("SUCCESS".equals(return_code) && "SUCCESS".equals(responseMap.get("result_code"))) {
	        	result.put("result", "SUCCESS");
	        } else if("FAIL".equals(return_code)) {
	        	result.put("result", "FAIL");
				result.put("msg", responseMap.get("return_msg"));
	        }
	        logger.info("委托代扣申请返回参数responseXML：" + responseXML);
	        return result;
		} catch (Exception e) {
			result.put("result", "FAIL");
			result.put("msg", e.getMessage());
			logger.info("委托代扣申请返回参数报错：" + e.getMessage());
			return result;
		}
       
	}
	
	
	/**
	 * 委托代扣-扣款结果通知
	 * 扣款接口请求成功，返回success仅代表扣款申请受理成功，不代表扣款成功。扣款是否成功以支付通知的结果为准。
	 */
	@RequestMapping("/payApplyNotice")
	@ResponseBody
	public String payApplyNotice(HttpServletRequest request, HttpServletResponse response) throws Exception {
		logger.info("委托代扣-扣款结果通知");
		Map<String, String> return_data = new HashMap<String, String>();
		try {
			ServletInputStream instream = request.getInputStream();
			StringBuffer sb = new StringBuffer();
			int len = -1;
			byte[] buffer = new byte[1024];
			while ((len = instream.read(buffer)) != -1) {
				sb.append(new String(buffer, 0, len));
			}
			instream.close();
			Map<String, String> map = XMLUtil.xmlToMap(sb.toString());// 接受微信的回调的通知参数
			logger.info("来自微信的签约成功与否通知参数：" + map);
			
			if (map.get("return_code").toString().equals("FAIL")) {
				return_data.put("return_code", "FAIL");
				return_data.put("return_msg", map.get("return_msg"));
				logger.info("返回微信数据：" + XMLUtil.mapToXml(return_data));
				return XMLUtil.mapToXml(return_data);
			} else if (map.get("return_code").toString().equals("SUCCESS")) {
				// 判断签名是否正确
				if (WXPayUtil.isSignatureValid(map, WeChatPayConfig.key)) {
					Payment payment = paymentService.getPaymentByOutTradeNo(map.get("out_trade_no"));
					if(payment == null) {
						return_data.put("return_code", "FAIL");
						return_data.put("return_msg", "该订单不存在");
						logger.info("返回微信数据：" + XMLUtil.mapToXml(return_data));
						return XMLUtil.mapToXml(return_data);
					} else {
						if("SUCCESS".equals(payment.getReturnCode()) || "FAIL".equals(payment.getReturnCode())) {
							//重复请求不做处理
						} else {
							payment.setReturnCode(map.get("return_code"));
							payment.setReturnMsg(map.get("return_msg"));
							payment.setResultCode(map.get("result_code"));
							payment.setErrCode(map.get("err_code"));
							payment.setErrCodeDes(map.get("err_code_des"));
							try {
								payment.setTransactionId(map.get("transaction_id"));
							} catch (Exception e) {
								e.printStackTrace();
							}
							payment.setOpenid(map.get("openid"));
							payment.setTradeState(map.get("trade_state"));
							payment.setTotalFee(Double.valueOf(map.get("total_fee"))/100);
							payment.setOutTradeNo(map.get("out_trade_no"));
							payment.setTimeEnd(map.get("time_end"));
							payment.setTradeType(map.get("trade_type"));
							payment.setContractId(map.get("contract_id"));
							payment.setCreateTime(new Date());
							paymentService.updatePayment(payment);
						}
					}
					return_data.put("return_code", "SUCCESS");
					return_data.put("return_msg", "OK");
					logger.info("返回微信数据：" + XMLUtil.mapToXml(return_data));
					return XMLUtil.mapToXml(return_data); 
				} else {
					return_data.put("return_code", "FAIL");
					return_data.put("return_msg", "签名错误");
					logger.info("返回微信数据：" + XMLUtil.mapToXml(return_data));
					return XMLUtil.mapToXml(return_data);   
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			return_data.put("return_code", "FAIL");
			return_data.put("return_msg", "ERROR");
		}
		logger.info("返回微信数据：" + XMLUtil.mapToXml(return_data));
		return XMLUtil.mapToXml(return_data);   
       
	}
}
