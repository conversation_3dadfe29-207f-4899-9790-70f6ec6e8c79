package com.taurus.payment.controller.weChatPay;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.taurus.entity.ResponseObject;
import com.taurus.payment.entity.Payment;
import com.taurus.payment.entity.WeChatPayChannel;
import com.taurus.payment.feign.ExamMainServiceFeign;
import com.taurus.payment.service.PaymentService;
import com.taurus.payment.service.wechatPay.WeChatPayChannelService;
import com.taurus.payment.utils.ListUtil;
import com.taurus.payment.utils.Network;
import com.taurus.payment.utils.WXPayUtil;
import com.taurus.payment.utils.WeChatPayConfig;
import com.taurus.payment.utils.XMLUtil;

/**
 * 小程序支付
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping(value = "/weChatPay")
public class WeChatPayMPController {

	private static org.slf4j.Logger logger = LoggerFactory.getLogger(WeChatPayMPController.class);

	@Value("${wx.notifyUrl}")
	private String notifyUrl;

	private static final String orderQueryUrl = "https://api.mch.weixin.qq.com/pay/orderquery";

	private static final String[] oldOrderTypes = { "miniapp", "qkk_miniapp", "qmkk_account", "qmtk_wxh5", "ksite_gzzz",
			"qkk_android", "miniapp_test", "etea_android" };
	private static final String[] universalOrderTypes = { "etea_minimp_adv", "qmkk_account_adv", "etea_android_adv",
			"etea_android_adv_test", "etea_minimp_adv_test" };

	@Autowired
	ExamMainServiceFeign examMainServiceFeignClient;

	@Autowired
	private PaymentService paymentService;

	@Autowired
	private WeChatPayChannelService weChatPayChannelService;

	/**
	 * 微信小程序、微信JSAPI 创建预支付
	 * 
	 * 
	 * @param elementJson
	 * @return
	 */
	@RequestMapping("/createWeChatPayOrder")
	@ResponseBody
	public Map<String, Object> createWeChatPayOrder(HttpServletRequest request, HttpServletResponse response) {
		logger.info("微信统一支付接口：/weChatPay/createWeChatPayOrder");
		Map<String, Object> result = new HashMap<>();
		try {
			// 获取参数
			ServletInputStream instream = request.getInputStream();
			StringBuffer sb = new StringBuffer();
			int len = -1;
			byte[] buffer = new byte[1024];
			while ((len = instream.read(buffer)) != -1) {
				sb.append(new String(buffer, 0, len));
			}
			instream.close();
			logger.info("微信统一支付接口接收参数：" + sb.toString());
			JSONObject json = JSONObject.parseObject(sb.toString());
			result = handleOrderReq(json, request);

		} catch (Exception e) {
			e.printStackTrace();
			result.put("result", -1);
			result.put("msg", "解析报错：" + e.getMessage());
		}

		return result;
	}

	/**
	 * 处理订单
	 * 
	 * @param json
	 * @param request
	 * @return
	 * @throws Exception
	 */
	private Map<String, Object> handleOrderReq(JSONObject json, HttpServletRequest request) throws Exception {
		Map<String, Object> result = new HashMap<>();

		// 商品名称
		String body = (String) json.get("body");
		String out_trade_no = (String) json.get("out_trade_no");
		Object moneyObject = json.get("money");
		Double money = null;
		if (moneyObject instanceof BigDecimal) {
			BigDecimal bigdeci = (BigDecimal) moneyObject;
			money = new Double(bigdeci.doubleValue());
		} else if (moneyObject instanceof Double) {
			money = (Double) moneyObject;
		}

		String openid = (String) json.get("openid");
		String originChannel = (String) json.get("originChannel");// 发起支付请求的来源渠道

		// 获取来源渠道端的微信支付参数
		WeChatPayChannel channel = weChatPayChannelService.getByOriginChannel(originChannel);
		if (channel == null) {
			result.put("result", -1);
			result.put("msg", "数据库中未配置originChannel对应的参数，无法调用！");
			return result;
		}

		String nonce_str = WXPayUtil.generateNonceStr();

		if (!StringUtils.isEmpty(body) && !StringUtils.isEmpty(out_trade_no) && money != null) {
			String appid = channel.getAppid();// appId
			String mch_id = channel.getMchId();// 支付商户id
			String tradeType = channel.getTradeType();
			String spbill_create_ip = Network.getRealIp(request);
			String total_fee = WXPayUtil.convertToFenValue(money);

			// 组装参数，用户生成统一下单接口的签名
			Map<String, String> signMap = new HashMap<>();
			signMap.put("appid", appid);
			signMap.put("attach", originChannel);// 附带自定义参数，在微信回调接口会原样传回
			signMap.put("body", body);
			signMap.put("mch_id", mch_id);// 商户号
			signMap.put("nonce_str", nonce_str);
			signMap.put("notify_url", notifyUrl);
			signMap.put("out_trade_no", out_trade_no);
			signMap.put("spbill_create_ip", spbill_create_ip);
			signMap.put("total_fee", total_fee);
			signMap.put("trade_type", tradeType);
			if (tradeType.equals("JSAPI"))
				signMap.put("openid", openid);

			// 第一次签名
			String sign = WXPayUtil.generateSignature(signMap, channel.getKey());

			// 拼接xml
			StringBuilder XML = new StringBuilder();
			XML.append("<XML>");
			XML.append("<mch_id>" + mch_id + "</mch_id>");
			XML.append("<appid>" + appid + "</appid>");
			XML.append("<attach>" + originChannel + "</attach>");
			XML.append("<notify_url>" + notifyUrl + "</notify_url>");
			XML.append("<trade_type>" + tradeType + "</trade_type>");
			XML.append("<nonce_str>" + nonce_str + "</nonce_str>");
			XML.append("<sign>" + sign + "</sign>");
			if (tradeType.equals("JSAPI")) {
				XML.append("<openid>" + openid + "</openid>");
			}
			XML.append("<body><![CDATA[" + body + "]]></body>");
			XML.append("<detail><![CDATA[" + "" + "]]></detail>");
			XML.append("<out_trade_no>" + out_trade_no + "</out_trade_no>");
			XML.append("<total_fee>" + total_fee + "</total_fee>");
			XML.append("<spbill_create_ip>" + spbill_create_ip + "</spbill_create_ip>");
			XML.append("</XML>");
			logger.info("统一下单接口请求：{}", XML.toString());

			// 调用统一下单接口
			String responseXML = WXPayUtil.doPostXml(WeChatPayConfig.pay_url, XML.toString());
			logger.info("统一下单接口响应：{}", responseXML);

			Map<String, String> responseMap = XMLUtil.xmlToMap(responseXML);
			if ("SUCCESS".equals(responseMap.get("return_code")) && "SUCCESS".equals(responseMap.get("result_code"))) {

				String prepay_id = responseMap.get("prepay_id");
				String timeStamp = String.valueOf(WXPayUtil.getCurrentTimestamp());

				// 拼接签名需要的参数
				SortedMap<String, String> packageParams = new TreeMap<String, String>();
				if (tradeType.equals("JSAPI")) {
					// 生成prepay_id时appid是小写的i,生成paySign时，appId是大写的I
					packageParams.put("appId", appid);
					packageParams.put("signType", "MD5");
					packageParams.put("nonceStr", nonce_str);
					packageParams.put("timeStamp", timeStamp);
					packageParams.put("package", "prepay_id=" + prepay_id);
					// 再次签名，这个签名用于小程序端调用wx.requesetPayment方法
					String paySign = WXPayUtil.generateSignature(packageParams, channel.getKey());
					result.put("appid", appid);
					result.put("paySign", paySign);
					result.put("nonceStr", nonce_str);
					result.put("package", "prepay_id=" + prepay_id);
					result.put("signType", "MD5");
					result.put("timeStamp", timeStamp);
				} else if (tradeType.equals("APP")) {
					packageParams.put("appid", appid);
					packageParams.put("partnerid", mch_id);
					packageParams.put("prepayid", prepay_id);
					packageParams.put("package", "Sign=WXPay");
					packageParams.put("noncestr", nonce_str);
					packageParams.put("timestamp", timeStamp);
					// 再次签名，这个签名用于APP端调起支付
					String paySign = WXPayUtil.generateSignature(packageParams, channel.getKey());

					result.put("appid", appid);
					result.put("sign", paySign);
					result.put("partnerId", mch_id);
					result.put("prepayId", prepay_id);
					result.put("package", "Sign=WXPay");
					result.put("nonceStr", nonce_str);
					result.put("timeStamp", timeStamp);
				}

				// 往数据库中写入payment
				Payment oldPay = paymentService.getPayment(out_trade_no, channel.getOriginChannel());
				if (oldPay != null) {
					oldPay.setOpenid(openid);
					oldPay.setTotalFee(money);
					oldPay.setTradeType(tradeType);
					oldPay.setCreateTime(new Date());
					paymentService.updatePayment(oldPay);
				} else {
					Payment pay = new Payment();
					pay.setOpenid(openid);
					pay.setOutTradeNo(out_trade_no);
					pay.setTotalFee(money);
					pay.setTradeType(tradeType);
					pay.setOriginChannel(channel.getOriginChannel());
					pay.setCreateTime(new Date());
					paymentService.createPayment(pay);
				}
				result.put("result", 1);
				result.put("msg", "统一下单成功！");
			} else {
				result.put("result", -1);
				String returnMsg = responseMap.get("return_msg");
				result.put("msg", returnMsg);
			}
		} else {
			result.put("result", -1);
			result.put("msg", "参数缺失或为空");
		}
		return result;
	}

	/**
	 * 创建预订单
	 * 通过springcloud Feign方式调用
	 * 
	 * @param body
	 * @param request
	 * @return
	 */
	@PostMapping("/createPreOrderByFeign")
	public ResponseObject createPreOrderByFeign(@RequestBody JSONObject json, HttpServletRequest request) {
		logger.info("微信统一支付接口接收参数：" + json.toString());

		Map<String, Object> result = new HashMap<>();
		try {
			result = handleOrderReq(json, request);
			Integer code = (Integer) result.get("result");
			if (code == -1) {
				ResponseObject res = ResponseObject.failure("ERROR");
				res.setMessage((String) result.get("msg"));
				return res;
			} else {
				return ResponseObject.success(result);
			}
		} catch (Exception e) {
			e.printStackTrace();

			ResponseObject res = ResponseObject.failure("ERROR");
			res.setMessage(e.getMessage());
			return res;
		}
	}

	/**
	 * APP支付 创建预支付
	 * 
	 * 
	 * @param elementJson
	 * @return
	 */
	@RequestMapping("/createPreOrder")
	@ResponseBody
	public Map<String, Object> createPreOrder(HttpServletRequest request, HttpServletResponse response) {
		logger.info("调用微信统一支付接口");
		Map<String, Object> result = new HashMap<>();
		try {
			// 获取参数
			ServletInputStream instream = request.getInputStream();
			StringBuffer sb = new StringBuffer();
			int len = -1;
			byte[] buffer = new byte[1024];
			while ((len = instream.read(buffer)) != -1) {
				sb.append(new String(buffer, 0, len));
			}
			instream.close();

			JSONObject json = JSONObject.parseObject(sb.toString());

			// 商品名称
			String body = (String) json.get("body");
			String out_trade_no = (String) json.get("out_trade_no");
			BigDecimal bigdeci = (BigDecimal) json.get("money");
			Double money = new Double(bigdeci.doubleValue());
			String originChannel = (String) json.get("originChannel");// 发起支付请求的来源渠道

			// 获取来源渠道端的微信支付参数
			WeChatPayChannel channel = weChatPayChannelService.getByOriginChannel(originChannel);
			if (channel == null) {
				result.put("result", -1);
				result.put("msg", "数据库中未配置originChannel对应的参数，无法调用！");
				return result;
			}

			if (!StringUtils.isEmpty(body) && !StringUtils.isEmpty(out_trade_no) && money != null) {
				String appid = channel.getAppid();// appId
				String mch_id = channel.getMchId();// 支付商户id
				String trade_type = channel.getTradeType();// 交易类型

				String nonce_str = WXPayUtil.generateNonceStr();// 生成随机字符串
				String total_fee = WXPayUtil.convertToFenValue(money);
				String spbill_create_ip = Network.getRealIp(request);

				// 组装参数，生成统一下单接口的签名
				Map<String, String> signMap = new HashMap<>();
				signMap.put("appid", appid);
				signMap.put("attach", originChannel);// 附带自定义参数，在微信回调接口会原样传回
				signMap.put("body", body);
				signMap.put("mch_id", mch_id);// 商户号
				signMap.put("nonce_str", nonce_str);
				signMap.put("notify_url", notifyUrl);
				signMap.put("out_trade_no", out_trade_no);
				signMap.put("spbill_create_ip", spbill_create_ip);
				signMap.put("total_fee", total_fee);
				signMap.put("trade_type", trade_type);
				// 第一次签名
				String sign = WXPayUtil.generateSignature(signMap, channel.getKey());

				// 拼接xml格式的请求
				StringBuilder XML = new StringBuilder();
				XML.append("<XML>");
				XML.append("<mch_id>" + mch_id + "</mch_id>");
				XML.append("<appid>" + appid + "</appid>");
				XML.append("<attach>" + originChannel + "</attach>");
				XML.append("<notify_url>" + notifyUrl + "</notify_url>");
				XML.append("<trade_type>" + trade_type + "</trade_type>");
				XML.append("<nonce_str>" + nonce_str + "</nonce_str>");
				XML.append("<sign>" + sign + "</sign>");
				XML.append("<body><![CDATA[" + body + "]]></body>");
				XML.append("<detail><![CDATA[" + "" + "]]></detail>");
				XML.append("<out_trade_no>" + out_trade_no + "</out_trade_no>");
				XML.append("<total_fee>" + total_fee + "</total_fee>");
				XML.append("<spbill_create_ip>" + spbill_create_ip + "</spbill_create_ip>");
				XML.append("</XML>");

				// 调用统一下单接口
				String responseXML = WXPayUtil.doPostXml(WeChatPayConfig.pay_url, XML.toString());
				Map<String, String> responseMap = XMLUtil.xmlToMap(responseXML);

				String return_code = responseMap.get("return_code");
				String result_code = responseMap.get("result_code");

				if ("SUCCESS".equals(return_code) && "SUCCESS".equals(result_code)) {
					String prepay_id = responseMap.get("prepay_id");
					String timeStamp = String.valueOf(WXPayUtil.getCurrentTimestamp());

					// ！！！切记这里的参数都是小写的！！！
					SortedMap<String, String> packageParams = new TreeMap<String, String>();
					packageParams.put("appid", appid);
					packageParams.put("partnerid", mch_id);
					packageParams.put("prepayid", prepay_id);
					packageParams.put("package", "Sign=WXPay");
					packageParams.put("noncestr", nonce_str);
					packageParams.put("timestamp", timeStamp);
					// 再次签名，这个签名用于APP端调起支付
					String paySign = WXPayUtil.generateSignature(packageParams, channel.getKey());

					result.put("appid", appid);
					result.put("sign", paySign);
					result.put("partnerId", mch_id);
					result.put("prepayId", prepay_id);
					result.put("package", "Sign=WXPay");
					result.put("nonceStr", nonce_str);
					result.put("timeStamp", timeStamp);

					// 往数据库中写入payment
					Payment oldPay = paymentService.getPayment(out_trade_no, channel.getOriginChannel());
					if (oldPay != null) {
						oldPay.setTotalFee(money);
						oldPay.setTradeType("APP");
						oldPay.setCreateTime(new Date());
						paymentService.updatePayment(oldPay);
					} else {
						Payment pay = new Payment();
						pay.setOutTradeNo(out_trade_no);
						pay.setTotalFee(money);
						pay.setTradeType("APP");
						pay.setOriginChannel(channel.getOriginChannel());
						pay.setCreateTime(new Date());
						paymentService.createPayment(pay);
					}
					result.put("result", 1);
					result.put("msg", "统一下单成功！");
				} else {
					result.put("result", -1);
					String returnMsg = responseMap.get("return_msg");
					result.put("msg", returnMsg);
				}
			} else {
				result.put("result", -1);
				result.put("msg", "参数缺失或为空");
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.put("result", -1);
			result.put("msg", "解析报错：" + e.getMessage());
		}
		logger.info(JSONObject.toJSONString(result));
		return result;
	}

	/**
	 * 微信支付回调接口
	 * 
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/weChatNotify")
	public String weChatNotify(HttpServletRequest request, HttpServletResponse response) {
		System.out.println("Received request for: " + request.getRequestURI());
		logger.info("微信支付回调本接口，通知支付结果：");
		try {
			BufferedReader br = new BufferedReader(new InputStreamReader(request.getInputStream()));
			String line = null;
			StringBuilder sb = new StringBuilder();
			while ((line = br.readLine()) != null) {
				sb.append(line);
			}
			br.close();

			logger.info("微信返回参数：{}", sb.toString());
			Map<String, String> map = XMLUtil.xmlToMap(sb.toString());
			String return_code = map.get("return_code");

			if (return_code.equals("SUCCESS")) {
				// 以下字段在return_code为SUCCESS的时候有返回
				String out_trade_no = map.get("out_trade_no");
				String originChannel = map.get("attach");

				WeChatPayChannel channel = weChatPayChannelService.getByOriginChannel(originChannel);

				if (channel != null && WXPayUtil.isSignatureValid(map, channel.getKey())) {
					Payment pay = paymentService.getPayment(out_trade_no, originChannel);
					if (pay != null) {
						/*
						 * 注意要判断微信支付重复回调，支付成功后微信会重复的进行回调 /**此处添加自己的业务逻辑代码
						 **/
						if ("SUCCESS".equals(pay.getResultCode()) || "FAIL".equals(pay.getResultCode())) {
							// 重复通知，已经处理过了，不做任何东西
						} else {
							// 保存微信回调内容
							pay.setReturnCode(return_code);
							pay.setReturnMsg(map.get("return_msg"));
							pay.setErrCode(map.get("err_code"));
							pay.setErrCodeDes(map.get("err_code_des"));
							pay.setTransactionId(map.get("transaction_id"));
							pay.setOpenid(map.get("openid"));
							pay.setTotalFee(Double.valueOf(map.get("total_fee")) / 100);// 分转换为元
							pay.setTimeEnd(map.get("time_end"));
							pay.setTradeType(map.get("trade_type"));// 交易类型：JSAPI、NATIVE、APP
							pay.setCreateTime(new Date());
							pay.setResultCode(map.get("result_code"));// 业务结果

							// 通知originChannel方支付结果
							try {
								JSONObject parameters = new JSONObject();
								parameters.put("out_trade_no", out_trade_no);
								parameters.put("originChannel", originChannel);
								parameters.put("result_code", map.get("result_code"));
								parameters.put("total_fee", pay.getTotalFee()); // double

								HashMap<String, String> requestMap = new HashMap<>();
								requestMap.put("paramType", "json");
								requestMap.put("json", JSON.toJSONString(parameters));

								// 回调支付渠道接口，通知支付渠道微信支付的结果
								String result = "";
								if (ListUtil.contain(oldOrderTypes, channel.getOriginChannel())) {
									examMainServiceFeignClient.oldOrderNotify(parameters);
								} else if (ListUtil.contain(universalOrderTypes, channel.getOriginChannel())) {
									examMainServiceFeignClient.universalOrderNotify(parameters);
								}

								if (result.equals("SUCCESS")) {
									// 接口调用成功，通知成功
									pay.setIfNotifyOriginChannel(true);
								} else {
									// 接口调用失败，通知失败
									pay.setIfNotifyOriginChannel(false);
								}
								pay.setNotifyOriginChannelResult(result);// 回调来源渠道的结果
								paymentService.updatePayment(pay);

							} catch (Exception e) {
								e.printStackTrace();
								logger.info("接口：" + channel.getNotifyUrl() + "，报错：" + e.getMessage());
							}
						}
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		// 返回微信success表示系统正常接收到微信的通知
		Map<String, String> responseResult = new HashMap<String, String>();
		responseResult.put("return_code", "SUCCESS");
		responseResult.put("return_msg", "OK");
		return XMLUtil.mapToXml2(responseResult);
	}

	/**
	 * 重新主动回调渠道方 对于第一次回调渠道失败的情况，进行重试，以保证支付结果能通知到渠道方
	 * 
	 * @param originChannel
	 */
	@GetMapping("/notifyChannel")
	public void notifyChannel(String originChannel) {

		WeChatPayChannel channel = weChatPayChannelService.getByOriginChannel(originChannel);
		List<Payment> paymentList = paymentService.getSuccessPayment("WRONG CALL");

		for (Payment pay : paymentList) {

			if (pay != null) {

				// 通知originChannel方支付结果
				try {
					Map<String, Object> parameters = new HashMap<>();
					parameters.put("out_trade_no", pay.getOutTradeNo());
					parameters.put("originChannel", pay.getOriginChannel());
					parameters.put("result_code", pay.getResultCode());
					parameters.put("total_fee", pay.getTotalFee()); // double

					HashMap<String, String> requestMap = new HashMap<>();
					requestMap.put("paramType", "json");
					requestMap.put("json", JSON.toJSONString(parameters));
					// 回调支付渠道接口，通知支付渠道微信支付的结果
					String result = WXPayUtil.net(channel.getNotifyUrl(), requestMap, "POST");

					if (result.equals("SUCCESS")) {
						// 接口调用成功，通知成功
						pay.setIfNotifyOriginChannel(true);
					} else {
						// 接口调用失败，通知失败
						pay.setIfNotifyOriginChannel(false);
					}
					pay.setNotifyOriginChannelResult(result);// 回调来源支付渠道接口的结果

					paymentService.updatePayment(pay);

				} catch (Exception e) {
					e.printStackTrace();
					logger.info("接口：" + channel.getNotifyUrl() + "，报错：" + e.getMessage());
				}

			}

		}

	}

	/**
	 * 查询微信订单 该接口提供所有微信支付订单的查询，商户可以通过查询订单接口主动查询订单状态，完成下一步的业务逻辑。 需要调用查询接口的情况： ◆
	 * 当商户后台、网络、服务器等出现异常，商户系统最终未接收到支付通知； ◆ 调用支付接口后，返回系统错误或未知交易状态情况； ◆
	 * 调用刷卡支付API，返回USERPAYING的状态； ◆ 调用关单或撤销接口API之前，需确认支付状态；
	 * 
	 * @param
	 * @return
	 */
	@RequestMapping("/queryOrder")
	public Map<String, String> queryOrder(HttpServletRequest request, HttpServletResponse response) {
		logger.info("查询微信订单：/queryOrder");
		Map<String, String> return_result = new HashMap<>();
		try {
			// 商户订单号
			String out_trade_no = request.getParameter("out_trade_no");
			if (StringUtils.isEmpty(out_trade_no)) {
				return_result.put("return_code", "FAIL");
				return_result.put("return_msg", "微信订单号及商户订单号参数都为空");
				return return_result;
			}

			Payment pay = paymentService.getPaymentByOutTradeNo(out_trade_no);
			String channelName = pay.getOriginChannel();

			WeChatPayChannel channel = weChatPayChannelService.getByOriginChannel(channelName);

			// 微信公众号或者小程序等appid，必传参数
			String appid = channel.getAppid();
			// 商户号
			String mch_id = channel.getMchId();
			// key
			String key = channel.getKey();
			// 随机字符串
			String nonce_str = WXPayUtil.generateNonceStr();
			SortedMap<String, String> signMap = new TreeMap<String, String>();
			signMap.put("appid", appid);
			signMap.put("mch_id", mch_id);
			signMap.put("nonce_str", nonce_str);
			// 商户订单号和微信订单号二选一
			signMap.put("out_trade_no", out_trade_no);
			// 签名
			String sign = WXPayUtil.generateSignature(signMap, key);
			signMap.put("sign", sign);
			// 请求参数转成XML
			String xml = XMLUtil.mapToXml(signMap);
			String responseXML = WXPayUtil.doPostXml(orderQueryUrl, xml);
			return XMLUtil.xmlToMap(responseXML);
		} catch (Exception e) {
			e.printStackTrace();
			return_result.put("return_code", "FAIL");
			return_result.put("return_msg", e.getMessage());
			return return_result;
		}
	}
}
