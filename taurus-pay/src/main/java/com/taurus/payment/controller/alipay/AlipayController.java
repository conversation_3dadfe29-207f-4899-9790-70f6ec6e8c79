package com.taurus.payment.controller.alipay;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayTradePrecreateRequest;
import com.alipay.api.response.AlipayTradePrecreateResponse;
import com.taurus.payment.entity.business.AlipayConfig;

@RestController
@RequestMapping(value = "/alipay")
public class AlipayController {

	private static Logger logger = LoggerFactory.getLogger(AlipayController.class);

	/**
	 * 支付宝扫码支付
	 * 
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/alipayPrecreate")
	@ResponseBody
	public JSONObject alipayPrecreate(HttpServletRequest request, HttpServletResponse response) {
		logger.info("发起支付宝扫码支付");
		JSONObject result = new JSONObject();
		try {
			// 获取参数
			ServletInputStream instream = request.getInputStream();
			StringBuffer sb = new StringBuffer();
			int len = -1;
			byte[] buffer = new byte[1024];
			while ((len = instream.read(buffer)) != -1) {
				sb.append(new String(buffer, 0, len));
			}
			instream.close();
			logger.info("支付宝扫码支付参数：" + sb.toString());
			JSONObject jsonConf = JSONObject.parseObject(sb.toString());
			Map<String, Object> param = new HashMap<>();
			String subject = (String) jsonConf.get("subject"); // 商品名称
			String out_trade_no = (String) jsonConf.get("out_trade_no");
			String total_amount = (String) jsonConf.get("total_amount");
			// String origin_type = (String) jsonConf.get("origin_type");
			if (StringUtils.isEmpty(subject) || StringUtils.isEmpty(out_trade_no)
					|| StringUtils.isEmpty(total_amount)) {
				result.put("code", -1);
				result.put("msg", "订单支付参数缺失");
				return result;
			}
			// 获得初始化的AlipayClient
			AlipayClient alipayClient = new DefaultAlipayClient(AlipayConfig.gate_way_url, AlipayConfig.app_id,
					AlipayConfig.private_key, "json", AlipayConfig.charset, AlipayConfig.alipay_public_key,
					AlipayConfig.sign_type);
			// 创建API对应的request类
			AlipayTradePrecreateRequest req = new AlipayTradePrecreateRequest();
			param.put("out_trade_no", out_trade_no);
			param.put("total_amount", total_amount);// 单位为：元 如：8.88
			param.put("subject", subject);
			param.put("timeout_express", "10m");// 请在10分钟之内付款
			String postdata = JSONObject.toJSONString(param).toString();
			req.setBizContent(postdata);
			AlipayTradePrecreateResponse resp = alipayClient.execute(req);
			String body = resp.getBody();
			JSONObject jsonObject = JSONObject.parseObject(body);
			String qr_code = jsonObject.getJSONObject("alipay_trade_precreate_response").getString("qr_code");
			result.put("code", 1);
			result.put("qr_code", qr_code);
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			result.put("code", -1);
			result.put("msg", e.getMessage());
			return result;
		}
	}

	/**
	 * 支付宝支付成功回调
	 * 
	 * @param request
	 * @param response
	 * @throws AlipayApiException
	 */
	@RequestMapping(value = "/alipayNotify")
	public void alipayNotify(HttpServletRequest request, HttpServletResponse response) {
		logger.info("支付宝支付成功回调>>>" + request.getQueryString());
		try {
			Map<String, String> responseMap = toAlipayMap(request);
			//校验签名
			boolean flag = AlipaySignature.rsaCheckV1(responseMap, AlipayConfig.alipay_public_key, AlipayConfig.charset,
					AlipayConfig.sign_type);
			if(flag) {
				String trade_no = responseMap.get("trade_no"); //支付宝交易号
				String out_trade_no = responseMap.get("out_trade_no");
				String total_amount = responseMap.get("total_amount");
				String subject = responseMap.get("subject");
				String buyer_id = responseMap.get("buyer_id"); // 买家支付宝用户号
				//业务逻辑校验
				response.getWriter().write("success");
				return;
			}else {
				logger.info(">>>加密解析问题");
				response.getWriter().write("failed");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static Map<String, String> toAlipayMap(HttpServletRequest request) {
		Map<String, String> params = new HashMap<String, String>();
		Map<String, String[]> requestParams = request.getParameterMap();
		for (Iterator<String> iter = requestParams.keySet().iterator(); iter.hasNext();) {
			String name = (String) iter.next();
			String[] values = (String[]) requestParams.get(name);
			String valueStr = "";
			for (int i = 0; i < values.length; i++) {
				valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
			}
			// 乱码解决，这段代码在出现乱码时使用。
			// valueStr = new String(valueStr.getBytes("ISO-8859-1"), "utf-8");
			params.put(name, valueStr);
		}
		return params;
	}

}
