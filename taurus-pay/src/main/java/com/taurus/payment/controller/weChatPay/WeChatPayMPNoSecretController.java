package com.taurus.payment.controller.weChatPay;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.taurus.payment.entity.ContractSign;
import com.taurus.payment.service.ContractSignService;
import com.taurus.payment.utils.WXPayUtil;
import com.taurus.payment.utils.WeChatPayConfig;
import com.taurus.payment.utils.XMLUtil;

/**
 * 小程序免密支付
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping(value = "/weChatPayMPNoSecret")
public class WeChatPayMPNoSecretController {

	private static Logger logger = Logger.getLogger("ContractNotifyController");
	
	@Autowired
	private ContractSignService contractService;


	/**
	 * 小程序用户签约免密支付(自动扣费)签约成功与否微信返回数据的通知接收接口
	 * 用户签约动作由小程序端发起
	 * 注意:微信支付回调接口需要是80端口,微信可能会多次重复调用该接口
	 * 签约结果通知路径为签约接口商户上传的notify_url字段，解约结果通知路径为商户配置委托扣款模版ID时填写的解约回调地址
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/contractNotify")
	@ResponseBody
	public String contractNotify(HttpServletRequest request, HttpServletResponse response) throws Exception {
		logger.info("小程序用户签约免密支付回调接口");
		Map<String, String> return_data = new HashMap<String, String>();
		try {
			ServletInputStream instream = request.getInputStream();
			StringBuffer sb = new StringBuffer();
			int len = -1;
			byte[] buffer = new byte[1024];
			while ((len = instream.read(buffer)) != -1) {
				sb.append(new String(buffer, 0, len));
			}
			instream.close();
			Map<String, String> map = XMLUtil.xmlToMap(sb.toString());// 接受微信的回调的通知参数
			logger.info("来自微信的签约成功与否通知参数：" + map);
			
			if (map.get("return_code").toString().equals("FAIL")) {
				return_data.put("return_code", "FAIL");
				return_data.put("return_msg", map.get("return_msg"));
				logger.info("返回微信数据：" + XMLUtil.mapToXml(return_data));
				return XMLUtil.mapToXml(return_data);
			} else if (map.get("return_code").toString().equals("SUCCESS")) {
				// 判断签名是否正确
				if (WXPayUtil.isSignatureValid(map, WeChatPayConfig.key)) {
					String result_code = map.get("result_code").toString();
					if(result_code.equals("FAIL")) {
						return_data.put("return_code", "FAIL");
						return_data.put("return_msg", map.get("return_msg"));
						logger.info("返回微信数据：" + XMLUtil.mapToXml(return_data));
						return XMLUtil.mapToXml(return_data);
					}else if (result_code.equals("SUCCESS")) {
						//自己的业务逻辑
						String mch_id = map.get("mch_id").toString(); //商户号
						String contract_code = map.get("contract_code").toString(); //签约协议号
						String plan_id = map.get("plan_id").toString(); //模板id
						String openid = map.get("openid").toString(); //用户标识
						String sign = map.get("sign").toString(); //签名
						String change_type = map.get("change_type").toString(); //变更类型,ADD--签约 ,DELETE--解约 ,商户可通过该字段判断是签约回调还是解约回调
						String operate_time = map.get("operate_time").toString(); //操作时间
						String contract_id = map.get("contract_id").toString(); //委托代扣协议id
						String contract_expired_time = null; //协议到期时间，当change_type为ADD时有返回
						String contract_termination_mode = null; //协议解约方式，当change_type为DELETE时有返回 
						if("ADD".equals(change_type)) {
							contract_expired_time = map.get("contract_expired_time").toString(); 
						}
						if("DELETE".equals(change_type)) {
							contract_termination_mode = map.get("contract_termination_mode").toString(); 
						}
					    String request_serial = map.get("request_serial").toString(); //请求序列号
					    ContractSign contract = contractService.getContractSign(contract_id);
					    if(contract == null) {
					    	ContractSign newContract = new ContractSign();
					    	newContract.setReturnCode(map.get("return_code"));
					    	newContract.setReturnMsg(map.get("return_msg"));
					    	newContract.setResultCode(result_code);
					    	newContract.setChangeType(change_type);
					    	newContract.setContractCode(contract_code);
					    	newContract.setContractExpiredTime(contract_expired_time);
					    	newContract.setContractId(contract_id);
					    	newContract.setContractTerminationMode(contract_termination_mode);
					    	newContract.setCreateTime(new Date());
					    	newContract.setMchId(mch_id);
					    	newContract.setOpenid(openid);
					    	newContract.setOperateTime(operate_time);
					    	newContract.setPlanId(plan_id);
					    	newContract.setRequestSerial(request_serial);
					    	newContract.setSign(sign);
					    	contractService.createContractSign(newContract);
					    } else if ( contract != null && "ADD".equals(contract.getChangeType()) && "DELETE".equals(change_type)) {
					    	contract.setChangeType(change_type);
					    	contract.setContractTerminationMode(contract_termination_mode);
					    	contract.setOperateTime(operate_time);
					    	contract.setSign(sign);
					    	contractService.updateContractSign(contract);
					    } else {
					    	//重复请求不做处理
					    }
					    return_data.put("return_code", "SUCCESS");
						return_data.put("return_msg", "OK");
						logger.info("返回微信数据：" + XMLUtil.mapToXml(return_data));
						return XMLUtil.mapToXml(return_data);    
					}
				} else {
					return_data.put("return_code", "FAIL");
					return_data.put("return_msg", "签名错误");
					logger.info("返回微信数据：" + XMLUtil.mapToXml(return_data));
					return XMLUtil.mapToXml(return_data);   
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			return_data.put("return_code", "FAIL");
			return_data.put("return_msg", "ERROR");
		}
		logger.info("返回微信数据：" + XMLUtil.mapToXml(return_data));
		return XMLUtil.mapToXml(return_data);   
	}

}
