package com.taurus.payment.entity;

import java.util.Map;

/**
 * 接收到的消息信息
 */
public class MessageFrom {
	// appid
	private String appid;

	// 接口调用凭证
	private String access_token;
	// 接收者(用户)的openid
	private String touser;
	// 模板消息的id
	private String template_id;
	// 跳转页面
	private String page;
	// 表单提交场景下，为 submit 事件带上的 formId；支付场景下，为本次支付的 prepay_id
	private String form_id;
	// 模板需要放大的关键词
	private String emphasis_keyword;
	// 模板内容
	private Map<String, TemplateData> data;

	public String getAppid() {
		return appid;
	}

	public void setAppid(String appid) {
		this.appid = appid;
	}

	public String getAccess_token() {
		return access_token;
	}

	public void setAccess_token(String access_token) {
		this.access_token = access_token;
	}

	public String getTouser() {
		return touser;
	}

	public void setTouser(String touser) {
		this.touser = touser;
	}

	public String getTemplate_id() {
		return template_id;
	}

	public void setTemplate_id(String template_id) {
		this.template_id = template_id;
	}

	public String getPage() {
		return page;
	}

	public void setPage(String page) {
		this.page = page;
	}

	public String getForm_id() {
		return form_id;
	}

	public void setForm_id(String form_id) {
		this.form_id = form_id;
	}

	public String getEmphasis_keyword() {
		return emphasis_keyword;
	}

	public void setEmphasis_keyword(String emphasis_keyword) {
		this.emphasis_keyword = emphasis_keyword;
	}

	public Map<String, TemplateData> getData() {
		return data;
	}

	public void setData(Map<String, TemplateData> data) {
		this.data = data;
	}

}
