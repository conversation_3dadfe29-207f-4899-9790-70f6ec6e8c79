package com.taurus.payment.entity.business;

/**
 * 支付宝扫码支付基础配置类
 * <AUTHOR>
 *
 */
public class AlipayConfig {
	//应用appid
	//public static String app_id = "123"; //沙箱测试
	public static String app_id = "456"; //正式
	
	//商户私钥，您的PKCS8
	public static String private_key = "axxxxxxxx";
	
	//支付宝公钥
	public static String alipay_public_key = "bxxxxxxxx";
	
	//服务器异步通知地址
	public static String notify_url = "http://examination/pay/alipayNotice";
	
	//支付宝网关
	//public static String gate_way_url = "https://openapi.alipaydev.com/gateway.do"; //测试
	public static String gate_way_url = "https://openapi.alipay.com/gateway.do"; //正式
	
	//签名方式
	public static String sign_type = "RSA2";
	
	//字符编码格式 目前支持 gbk 或 utf-8
	public static String charset = "UTF-8";
	
}
