package com.taurus.payment.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 小程序免密代扣支付签约表
 * 表名：contract_sign
 */
@Table(name = "contract_sign")
public class ContractSign {
	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	/** 签约通知序号, 数据库字段：id */
	private Integer id;
	
	/** 返回状态码, 数据库字段：return_code */
	private String returnCode;
	
	/** 返回信息, 数据库字段：return_msg */
	private String returnMsg;
	
	/** 业务结果, 数据库字段：result_code */
	private String resultCode;
	
	/** 商户号, 数据库字段：mch_id */
	private String mchId;
	
	/** 签约协议号, 数据库字段：contract_code */
	private String contractCode;
	
	/** 模板id, 数据库字段：plan_id */
	private String planId;
	
	/** 用户标识, 数据库字段：openid */
	private String openid;
	
	/** 签名, 数据库字段：sign */
	private String sign;
	
	/** 变更类型, 数据库字段：change_type */
	private String changeType;
	
	/** 操作时间, 数据库字段：operate_time */
	private String operateTime;
	
	/** 委托代扣协议id, 数据库字段：contract_id */
	private String contractId;
	
	/** 协议到期时间, 数据库字段：contract_expired_time */
	private String contractExpiredTime;
	
	/** 协议解约方式, 数据库字段：contract_termination_mode */
	private String contractTerminationMode;
	
	/** 请求序列号, 数据库字段：request_serial */
	private String requestSerial;
	
	/** 写入时间, 数据库字段：create_time */
	private Date createTime;
	
	/** 更新时间, 数据库字段：update_time */
	private Date updateTime;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getReturnCode() {
		return returnCode;
	}

	public void setReturnCode(String returnCode) {
		this.returnCode = returnCode;
	}

	public String getReturnMsg() {
		return returnMsg;
	}

	public void setReturnMsg(String returnMsg) {
		this.returnMsg = returnMsg;
	}

	public String getResultCode() {
		return resultCode;
	}

	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}

	public String getMchId() {
		return mchId;
	}

	public void setMchId(String mchId) {
		this.mchId = mchId;
	}

	public String getContractCode() {
		return contractCode;
	}

	public void setContractCode(String contractCode) {
		this.contractCode = contractCode;
	}

	public String getPlanId() {
		return planId;
	}

	public void setPlanId(String planId) {
		this.planId = planId;
	}

	public String getOpenid() {
		return openid;
	}

	public void setOpenid(String openid) {
		this.openid = openid;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	public String getChangeType() {
		return changeType;
	}

	public void setChangeType(String changeType) {
		this.changeType = changeType;
	}

	public String getOperateTime() {
		return operateTime;
	}

	public void setOperateTime(String operateTime) {
		this.operateTime = operateTime;
	}

	public String getContractId() {
		return contractId;
	}

	public void setContractId(String contractId) {
		this.contractId = contractId;
	}

	public String getContractExpiredTime() {
		return contractExpiredTime;
	}

	public void setContractExpiredTime(String contractExpiredTime) {
		this.contractExpiredTime = contractExpiredTime;
	}

	public String getContractTerminationMode() {
		return contractTerminationMode;
	}

	public void setContractTerminationMode(String contractTerminationMode) {
		this.contractTerminationMode = contractTerminationMode;
	}

	public String getRequestSerial() {
		return requestSerial;
	}

	public void setRequestSerial(String requestSerial) {
		this.requestSerial = requestSerial;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ContractSign other = (ContractSign) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ContractSign [id=" + id + ", returnCode=" + returnCode + ", returnMsg=" + returnMsg + ", resultCode="
				+ resultCode + ", mchId=" + mchId + ", contractCode=" + contractCode + ", planId=" + planId
				+ ", openid=" + openid + ", sign=" + sign + ", changeType=" + changeType + ", operateTime="
				+ operateTime + ", contractId=" + contractId + ", contractExpiredTime=" + contractExpiredTime
				+ ", contractTerminationMode=" + contractTerminationMode + ", requestSerial=" + requestSerial
				+ ", createTime=" + createTime + ", updateTime=" + updateTime + "]";
	}

	
	
}
