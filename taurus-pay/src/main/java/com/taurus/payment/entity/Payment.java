package com.taurus.payment.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 微信收款表，注意成功与否返回的字段不同  
 * 表名：payment
 */
@Table(name = "payment")
public class Payment {
	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	/** 支付序号, 数据库字段：id */
	private Integer id;
	
	/** 返回状态码, 数据库字段：return_code */
	private String returnCode;
	
	/** 返回信息, 数据库字段：return_msg */
	private String returnMsg;
	
	/** 业务结果, 数据库字段：result_code */
	private String resultCode;
	
	/** 错误代码, 数据库字段：err_code */
	private String errCode;
	
	/** 错误代码描述, 数据库字段：err_code_des */
	private String errCodeDes;
	
	/** 用户标识, 数据库字段：openid */
	private String openid;
	
	/** 交易类型, 数据库字段：trade_type */
	private String tradeType;
	
	/** 订单金额, 数据库字段：total_fee */
	private Double totalFee;
	
	/** 微信支付订单号, 数据库字段：transaction_id */
	private String transactionId;
	
	/** 商户订单号, 数据库字段：out_trade_no */
	private String outTradeNo;
	
	/** 支付完成时间, 数据库字段：time_end */
	private String timeEnd;
	
	/** 写入时间, 数据库字段：create_time */
	private Date createTime;
	
	/** 请求来源, 数据库字段：origin_channel */
	private String originChannel;
	
	
	/** 是否正确通知来源渠道**/
	private boolean ifNotifyOriginChannel;
	
	/** 通知来源渠道后的结果**/
	private String notifyOriginChannelResult;
	
	//以下是自动扣款通知接口专有字段
	/** 交易状态, 数据库字段：trade_state */
	private String tradeState;
	
	/** 委托代扣协议id, 数据库字段：contract_id */
	private String contractId;
	

	
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getReturnCode() {
		return returnCode;
	}

	public void setReturnCode(String returnCode) {
		this.returnCode = returnCode;
	}

	public String getReturnMsg() {
		return returnMsg;
	}

	public void setReturnMsg(String returnMsg) {
		this.returnMsg = returnMsg;
	}

	public String getResultCode() {
		return resultCode;
	}

	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}

	public String getErrCode() {
		return errCode;
	}

	public void setErrCode(String errCode) {
		this.errCode = errCode;
	}

	public String getErrCodeDes() {
		return errCodeDes;
	}

	public void setErrCodeDes(String errCodeDes) {
		this.errCodeDes = errCodeDes;
	}

	public String getOpenid() {
		return openid;
	}

	public void setOpenid(String openid) {
		this.openid = openid;
	}

	public String getTradeType() {
		return tradeType;
	}

	public void setTradeType(String tradeType) {
		this.tradeType = tradeType;
	}

	public Double getTotalFee() {
		return totalFee;
	}

	public void setTotalFee(Double totalFee) {
		this.totalFee = totalFee;
	}

	public String getTransactionId() {
		return transactionId;
	}

	public void setTransactionId(String transactionId) {
		this.transactionId = transactionId;
	}

	public String getOutTradeNo() {
		return outTradeNo;
	}

	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}

	public String getTimeEnd() {
		return timeEnd;
	}

	public void setTimeEnd(String timeEnd) {
		this.timeEnd = timeEnd;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getTradeState() {
		return tradeState;
	}

	public void setTradeState(String tradeState) {
		this.tradeState = tradeState;
	}

	public String getContractId() {
		return contractId;
	}

	public void setContractId(String contractId) {
		this.contractId = contractId;
	}


	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Payment other = (Payment) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "Payment [id=" + id + ", returnCode=" + returnCode + ", returnMsg=" + returnMsg + ", resultCode="
				+ resultCode + ", errCode=" + errCode + ", errCodeDes=" + errCodeDes + ", openid=" + openid
				+ ", tradeType=" + tradeType + ", totalFee=" + totalFee + ", transactionId=" + transactionId
				+ ", outTradeNo=" + outTradeNo + ", timeEnd=" + timeEnd + ", createTime=" + createTime + ", tradeState="
				+ tradeState + ", contractId=" + contractId + ", originChannel=" + originChannel+", ifNotifyOriginChannel=" + ifNotifyOriginChannel+", notifyOriginChannelResult=" + notifyOriginChannelResult + "]";
	}

	public String getOriginChannel() {
		return originChannel;
	}

	public void setOriginChannel(String originChannel) {
		this.originChannel = originChannel;
	}

	public boolean isIfNotifyOriginChannel() {
		return ifNotifyOriginChannel;
	}

	public void setIfNotifyOriginChannel(boolean ifNotifyOriginChannel) {
		this.ifNotifyOriginChannel = ifNotifyOriginChannel;
	}

	public String getNotifyOriginChannelResult() {
		return notifyOriginChannelResult;
	}

	public void setNotifyOriginChannelResult(String notifyOriginChannelResult) {
		this.notifyOriginChannelResult = notifyOriginChannelResult;
	}
	
}
