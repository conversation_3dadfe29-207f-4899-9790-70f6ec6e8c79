package com.taurus.payment.entity;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 微信参数表 表名：wechat_pay_channel
 */
@Table(name = "wechat_pay_channel")
public class WeChatPayChannel {
	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	/** 支付序号, 数据库字段：id */
	private Integer id;

	/** 微信平台分配的appid, 数据库字段：appid */
	private String appid;

	/** 商户号, 数据库字段：mch_id */
	private String mchId;

	/** 商户密钥, 数据库字段：key */
	private String key;

	/** 请求来源渠道, 数据库字段：origin_channel */
	private String originChannel;

	/** 业务回调接口, 数据库字段：notify_url */
	private String notifyUrl;
	
	/** 交易类型，数据库字段：dradeType **/
	private String tradeType;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getAppid() {
		return appid;
	}

	public void setAppid(String appid) {
		this.appid = appid;
	}

	public String getMchId() {
		return mchId;
	}

	public void setMchId(String mchId) {
		this.mchId = mchId;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public String getNotifyUrl() {
		return notifyUrl;
	}

	public void setNotifyUrl(String notifyUrl) {
		this.notifyUrl = notifyUrl;
	}
	

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		WeChatPayChannel other = (WeChatPayChannel) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "WeChatPayChannel [id=" + id + ", appid=" + appid + ", mchId=" + mchId + ", key=" + key + ", originChannel="
				+ originChannel + ", notifyUrl=" + notifyUrl + "]";
	}

	public String getOriginChannel() {
		return originChannel;
	}

	public void setOriginChannel(String originChannel) {
		this.originChannel = originChannel;
	}

	public String getTradeType() {
		return tradeType;
	}

	public void setTradeType(String tradeType) {
		this.tradeType = tradeType;
	}



}
