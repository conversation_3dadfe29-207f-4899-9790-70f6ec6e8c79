package com.taurus.payment.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.alibaba.fastjson.JSONObject;

@FeignClient(value = "exam-main-service") //这里的name对应调用服务的spring.application.name
@Component
public interface ExamMainServiceFeign {

	/**
	 * 老订单通知
	 * @param body
	 * @return
	 */
    @PostMapping("/order/orderNotify")
    public String oldOrderNotify(@RequestBody JSONObject body);
    
    /**
     * 统一订单通知
     * @param request
     * @return
     */
    @PostMapping("/univsersalOrder/orderNotify")
    public String universalOrderNotify(@RequestBody JSONObject body); 
}
