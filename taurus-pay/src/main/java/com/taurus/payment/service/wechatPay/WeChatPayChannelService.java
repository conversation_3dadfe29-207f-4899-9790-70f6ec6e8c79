package com.taurus.payment.service.wechatPay;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.taurus.payment.entity.WeChatPayChannel;
import com.taurus.payment.mapper.WeChatPayChannelMapper;

@Service
public class WeChatPayChannelService {
	private static final Logger logger = LoggerFactory.getLogger(WeChatPayChannelService.class);
	
	
	@Autowired
	private WeChatPayChannelMapper weChatPayChannelMapper;
	
	public void save(WeChatPayChannel channel) {
		weChatPayChannelMapper.saveIgnoreNull(channel);
	}
	
	public void update(WeChatPayChannel channel) {
		weChatPayChannelMapper.updateIgnoreNull(channel);
	}
	
	public WeChatPayChannel getByOriginChannel(String channelName) {
		return weChatPayChannelMapper.getByColumn("origin_channel", channelName);
	}

	
}





