package com.taurus.payment.service.wechatPay;

import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.taurus.payment.entity.Payment;
import com.taurus.payment.entity.WeChatPayChannel;
import com.taurus.payment.utils.CommonUtils;
import com.taurus.payment.utils.ConfigUtil;
import com.taurus.payment.utils.HttpUtil;
import com.taurus.payment.utils.PayCommonUtil;
import com.taurus.payment.utils.XMLUtil;

import weixin.popular.api.SnsAPI;

@Service
public class WeChatPayH5Service {
	private static final Logger logger = LoggerFactory.getLogger(WeChatPayH5Service.class);

	@Value("${wx.notifyUrl}")
	private String notifyUrl;

	@Autowired
	private WeChatPayChannelService weChatPayChannelService;

	public String weixinPayMobile(Payment payment) {
		String totalFee = payment.getTotalFee().toString();
		// redirect_uri 需要在微信支付端添加认证网址
		totalFee = CommonUtils.subZeroAndDot(totalFee);
		WeChatPayChannel channel = weChatPayChannelService.getByOriginChannel(payment.getOriginChannel());
		String redirect_uri = notifyUrl + "?outTradeNo=" + payment.getOutTradeNo() + "&totalFee=" + totalFee;
		// 也可以通过state传递参数 redirect_uri 后面加参数未经过验证
		return SnsAPI.connectOauth2Authorize(channel.getAppid(), redirect_uri, true, null);
	}

	public String weixinPayH5(Payment payment) {
		logger.info("订单号：{}发起H5支付", payment.getOutTradeNo());
		String mweb_url = "";
		try {
			// 账号信息
			WeChatPayChannel channel = weChatPayChannelService.getByOriginChannel(payment.getOriginChannel());
			String key = channel.getKey(); // key
			String trade_type = "MWEB";// 交易类型 H5 支付
			SortedMap<Object, Object> packageParams = new TreeMap<Object, Object>();
			ConfigUtil.commonParams(packageParams);

			// packageParams.put("product_id", payment.getProductId());// 商品ID
			packageParams.put("body", "");// 商品描述
			packageParams.put("out_trade_no", payment.getOutTradeNo());// 商户订单号
			String totalFee = payment.getTotalFee().toString();
			totalFee = CommonUtils.subZeroAndDot(totalFee);
			packageParams.put("total_fee", totalFee);// 总金额
			packageParams.put("spbill_create_ip", "");// 发起人IP地址
			packageParams.put("notify_url", notifyUrl);// 回调地址
			packageParams.put("trade_type", trade_type);// 交易类型
			packageParams.put("mch_id", channel.getMchId());// 商户支付账号
			packageParams.put("appid", channel.getAppid());// 应用账号ID
			// 生成随机字符串
			String currTime = PayCommonUtil.getCurrTime();
			String strTime = currTime.substring(8, currTime.length());
			String strRandom = PayCommonUtil.buildRandom(4) + "";
			String nonce_str = strTime + strRandom;
			packageParams.put("nonce_str", nonce_str);// 随机字符串
			// H5支付专用
			JSONObject value = new JSONObject();
			value.put("type", "WAP");
			value.put("wap_url", "https://diamond.qikaokao.com");//// WAP网站URL地址
			value.put("wap_name", "全民考试助手");// WAP 网站名
			JSONObject scene_info = new JSONObject();
			scene_info.put("h5_info", value);
			packageParams.put("scene_info", scene_info.toString());

			String sign = PayCommonUtil.createSign("UTF-8", packageParams, key);
			packageParams.put("sign", sign);// 签名

			String requestXML = PayCommonUtil.getRequestXml(packageParams);
			String resXml = HttpUtil.post(ConfigUtil.UNIFIED_ORDER_URL, requestXML);
			Map<?, ?> map = XMLUtil.doXMLParse(resXml);
			String returnCode = (String) map.get("return_code");
			if ("SUCCESS".equals(returnCode)) {
				String resultCode = (String) map.get("result_code");
				if ("SUCCESS".equals(resultCode)) {
					logger.info("订单号：{}发起H5支付成功", payment.getOutTradeNo());
					mweb_url = (String) map.get("mweb_url");
					System.out.println("mweb_url:" + mweb_url);
				} else {
					String errCodeDes = (String) map.get("err_code_des");
					logger.info("订单号：{}发起H5支付(系统)失败:{}", payment.getOutTradeNo(), errCodeDes);
				}
			} else {
				String returnMsg = (String) map.get("return_msg");
				logger.info("(订单号：{}发起H5支付(通信)失败:{}", payment.getOutTradeNo(), returnMsg);
			}
		} catch (Exception e) {
			logger.error("订单号：{}发起H5支付失败(系统异常))", payment.getOutTradeNo(), e);
		}
		return mweb_url;
	}
}
