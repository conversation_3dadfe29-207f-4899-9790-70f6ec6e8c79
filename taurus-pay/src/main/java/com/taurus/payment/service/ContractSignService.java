package com.taurus.payment.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.taurus.payment.entity.ContractSign;
import com.taurus.payment.mapper.ContractSignMapper;

@Service
public class ContractSignService {
	
	@Autowired
	private ContractSignMapper contractMapper;
	
	public void createContractSign(ContractSign sign) {
		contractMapper.saveIgnoreNull(sign);
	}
	
	public void updateContractSign(ContractSign sign) {
		contractMapper.updateIgnoreNull(sign);
	}
	
	/*
	 * 根据委托代扣协议id，查找代扣协议
	 */
	public ContractSign getContractSign(String contractId) {
		return contractMapper.getByColumn("contract_id", contractId);
	}
}
