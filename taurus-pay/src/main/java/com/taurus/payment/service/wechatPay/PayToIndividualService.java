package com.taurus.payment.service.wechatPay;

import java.io.File;
import java.io.FileInputStream;
import java.security.KeyStore;
import java.util.SortedMap;
import java.util.TreeMap;

import javax.net.ssl.SSLContext;

import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.DefaultHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.taurus.payment.utils.PayUtil;
import com.taurus.payment.utils.WXPayUtil;
import com.taurus.payment.utils.XMLUtil;

@Service
public class PayToIndividualService {
	
	private static Logger log = LoggerFactory.getLogger(PayToIndividualService.class);
	
	private static final String APP_ID = "wxa38e954efdceffbe";//全民考试助手appid
	private static final String MCH_ID = "1523154441";//微信商户号
	private static final String API_SECRET="23AE823JHjk8jshOP982317566J82K03";//微信支付api密钥
	private static final String TRANSFERS_PAY = "https://api.mch.weixin.qq.com/mmpaymkttransfers/promotion/transfers";
	
	/**
	 * 付款给个人
	 * @param openid
	 * @param ip
	 * @param amount
	 * @param desc
	 * @return
	 */
	public JSONObject payToIndividual(String openid,String ip,String amount,String desc) {
		JSONObject res = new JSONObject();
		
		String xml = payToIndividualXml(openid,ip,amount,desc);
		try {
			String filePath = this.getClass().getClassLoader().getResource("apiclient_cert.p12").getPath();
            //指定读取证书格式为PKCS12
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            //linux系统,读取本机存放的PKCS12证书文件
            FileInputStream instream = new FileInputStream(new File(filePath));
            try {
            	//指定PKCS12的密码(商户ID)
            	keyStore.load(instream, MCH_ID.toCharArray());
            }finally {
                instream.close();
            }
            
            SSLContext sslcontext = SSLContexts.custom().loadKeyMaterial(keyStore, MCH_ID.toCharArray()).build();
        	//指定TLS版本, Allow TLSv1 protocol only
            SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslcontext,
                    new String[] {"TLSv1"}, null,new DefaultHostnameVerifier());
            //设置httpclient的SSLSocketFactory
            CloseableHttpClient httpclient = HttpClients.custom().setSSLSocketFactory(sslsf).build();
            HttpPost httppost = new HttpPost(TRANSFERS_PAY);
        	//这里要设置编码，不然xml中有中文的话会提示签名失败或者展示乱码
            httppost.addHeader("Content-Type", "text/xml");
            StringEntity se = new StringEntity(xml,"UTF-8");
            httppost.setEntity(se);
            CloseableHttpResponse responseEntry = httpclient.execute(httppost);
            try {
                HttpEntity entity = responseEntry.getEntity();
                if (entity != null) {
                   
                    SAXReader saxReader = new SAXReader();
                    Document document = saxReader.read(entity.getContent());
                    log.info(document.toString());
                    Element rootElt = document.getRootElement();
                    String resultCode = rootElt.elementText("result_code");
                   
                    res.put("result_code", resultCode);
                    if(resultCode.equals("FAIL")){
                    	res.put("err_code", rootElt.elementText("err_code"));
                    	res.put("err_code_des", rootElt.elementText("err_code_des"));
                    }
         
                }
                EntityUtils.consume(entity);
            }catch(Exception e){
            	log.error("请求失败");
            }
            finally {
                responseEntry.close();
            }
        }catch(Exception e){
        	log.error("请求失败");
        }
		return res;
	}
	
	/**
	 * 请求参数
	 * @param openid
	 * @param ip
	 * @param amount
	 * @param desc
	 * @return
	 */
	private String payToIndividualXml(String openid,String ip,String amount,String desc) {
		
        try {
        	SortedMap<String,String> param = new TreeMap<String,String>();
            param.put("mch_appid", APP_ID); //公众账号appid
            param.put("mchid", MCH_ID); //商户号
            param.put("nonce_str", WXPayUtil.generateNonceStr()); //随机字符串
            param.put("partner_trade_no", PayUtil.getTradeNo()); //商户订单号
            param.put("openid", openid); //用户openid
            param.put("check_name", "NO_CHECK"); //校验用户姓名选项 OPTION_CHECK
            //param.put("re_user_name", "安迪"); //check_name设置为FORCE_CHECK或OPTION_CHECK，则必填
            param.put("amount", amount); //转账金额
            param.put("desc",desc); //企业付款描述信息
            param.put("spbill_create_ip", ip); //服务器Ip地址
            param.put("sign", WXPayUtil.createSign(param, API_SECRET));
            
            String paramStr = XMLUtil.sortedMaptoXml(param);
            return paramStr;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
		
	}
	

}
