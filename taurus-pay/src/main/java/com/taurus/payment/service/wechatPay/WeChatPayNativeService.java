package com.taurus.payment.service.wechatPay;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.taurus.payment.utils.ConfigUtil;
import com.taurus.payment.utils.HttpClient;
import com.taurus.payment.utils.WXPayUtil;
import com.taurus.payment.utils.XMLUtil;

@Service
public class WeChatPayNativeService {
	
	private static final Logger logger = LoggerFactory.getLogger(WeChatPayNativeService.class);
	


  //查询支付状态
	public Map<String,String> queryPayStatus(String out_trade_no,String mch_id,String appid) {
		//1.封装参数
		Map<String,String> param=new HashMap<>();
		param.put("mch_id", mch_id);// 商户支付账号
		param.put("appid", appid);// 应用账号ID		
		param.put("out_trade_no", out_trade_no);
		param.put("nonce_str", WXPayUtil.generateNonceStr());
		try {
			String xmlParam = WXPayUtil.generateSignedXml(param, ConfigUtil.API_KEY);
			//2.发送请求
			HttpClient httpClient=new HttpClient("https://api.mch.weixin.qq.com/pay/orderquery");
			httpClient.setHttps(true);
			httpClient.setXmlParam(xmlParam);
			httpClient.post();
			
			//3.获取结果
			String xmlResult = httpClient.getContent();
			Map<String, String> map = XMLUtil.xmlToMap(xmlResult);
			System.out.println("调动查询API返回结果："+xmlResult);
			
			return map;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
		
	}
}
