package com.taurus.payment.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.gitee.fastmybatis.core.query.Query;
import com.taurus.payment.entity.Payment;
import com.taurus.payment.mapper.PaymentMapper;

@Service
public class PaymentService {
	
	
	@Autowired
	private PaymentMapper paymentMapper;
	
	public void createPayment(Payment payment) {
		paymentMapper.saveIgnoreNull(payment);
	}
	
	public void updatePayment(Payment payment) {
		paymentMapper.updateIgnoreNull(payment);
	}
	
	/**
	 * 根据订单号和渠道获取支付记录
	 * @param outTradeNo
	 * @param originChannel
	 * @return
	 */
	public Payment getPayment(String outTradeNo, String originChannel) {
		Query query = new Query();
		query.eq("out_trade_no", outTradeNo);
		query.eq("origin_channel", originChannel);
		return paymentMapper.getByQuery(query);
	}
	
	/**
	 * 返回所有指定条件的payment
	 * @param notifyOriginChannelResult
	 * @return
	 */
	public List<Payment> getSuccessPayment(String notifyOriginChannelResult){
		Query query = new Query().eq("notify_origin_channel_result", notifyOriginChannelResult).eq("return_code", "SUCCESS");
		return paymentMapper.list(query);
	}
	
	public Payment getPaymentByOutTradeNo(String outTradeNo) {
		return paymentMapper.getByColumn("out_trade_no", outTradeNo);
	}
	
}
