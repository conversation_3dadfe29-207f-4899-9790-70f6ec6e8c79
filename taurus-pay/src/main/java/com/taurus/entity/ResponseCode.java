package com.taurus.entity;

public class ResponseCode {

	// 通用
	public final static String SUCESS = "SUCESS";
	public final static String IO_EXCEPTION = "IO_EXCEPTION";

	// 参数
	public final static String CONTENT_EMPTY = "CONTENT_EMPTY";
	public final static String PARAMETERS_ERROR = "PARAMETERS_ERROR";

	// 安全Token
	public final static String NO_TOKEN = "NO_TOKEN";
	public final static String INVALID_TOKEN = "INVALID_TOKEN";
	public final static String OVERDUE_TOKEN = "OVERDUE_TOKEN";

	// 文件系统
	public final static String NO_MULTIPARTFILE = "NO_MULTIPARTFILE";
	public final static String FILE_PARSE_ERROR = "FILE_PARSE_ERROR";

	// 网络情况
	public final static String NET_CONNECTION_ERROR = "NET_CONNECTION_ERROR";

	// 支付结果
	public final static String CHANNEL_NOT_EXISTED = "数据库中未配置originChannel对应的参数，无法调用！";
}
