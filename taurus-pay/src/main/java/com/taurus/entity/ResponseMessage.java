package com.taurus.entity;

import java.lang.reflect.Field;

public class ResponseMessage {

	// 转交考试
	public final static String TRANSMIT_OVER = "已经转交过了";//
	public final static String TRANSMIT_SELF = "这是您自己的考试";//

	public final static String NO_OPENID = "未获取到openid";//

	// 通用
	public final static String SUCESS = "成功";
	public final static String IO_EXCEPTION = "发生IO流错误";

	// 参数
	public final static String CONTENT_EMPTY = "内容为空";
	public final static String PARAMETERS_ERROR = "参数不合法或错误";

	// 安全Token
	public final static String NO_TOKEN = "无TOKEN";
	public final static String INVALID_TOKEN = "无效的token";
	public final static String OVERDUE_TOKEN = "过期的token";

	// 文件系统
	public final static String NO_MULTIPARTFILE = "未选择文件上传";
	public final static String FILE_PARSE_ERROR = "文件解析错误";

	// 网络情况
	public final static String NET_CONNECTION_ERROR = "网络连接错误";

	// 支付结果
	public final static String CHANNEL_NOT_EXISTED = "数据库中未配置originChannel对应的参数，无法调用！";

	// 通过反射获取属性值
	public static String getValueByFieldName(String fieldName) {
		try {
			Field fieldTag = ResponseMessage.class.getDeclaredField(fieldName);
			return (String) fieldTag.get(ResponseMessage.class);
		} catch (Exception ex) {
			return null;
		}
	}
}
