package com.taurus.filter;

import java.io.IOException;
import java.util.Map;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/*import com.auth0.jwt.interfaces.Claim;
import com.taurus.entity.business.ResponseCode;
import com.taurus.entity.business.ResponseObject;
import com.taurus.utils.IOUtil;
import com.taurus.utils.jwt.JWTUtils;*/

public class JwtFilter implements Filter {
	@Override
	public void init(FilterConfig filterConfig) throws ServletException {
	}

	@Override
	public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain)
			throws IOException, ServletException {
		final HttpServletRequest request = (HttpServletRequest) req;
		final HttpServletResponse response = (HttpServletResponse) res;

		// 获取 header里的token
		final String token = request.getHeader("authorization");

		if ("OPTIONS".equals(request.getMethod())) {
			response.setStatus(HttpServletResponse.SC_OK);
			// Except OPTIONS, other request should be checked by JWT
		} else {
			//ResponseObject responseObj = new ResponseObject();
			//if (token == null) {
				//responseObj.setStandardFailure(ResponseCode.NO_TOKEN);
				//IOUtil.responseResult(response, responseObj);
				//return;
			//}
			//Map<String, Claim> userData = JWTUtils.verifyToken(token);
			//if (userData == null) {
			//	responseObj.setStandardFailure(ResponseCode.INVALID_TOKEN);
			//	IOUtil.responseResult(response, responseObj);
			//	return;
			//}
			//Integer userId = userData.get("userId").asInt();

			// 拿到用户id，放到request中
			//request.setAttribute("userId", userId);

		}

		chain.doFilter(request, response);
	}

	@Override
	public void destroy() {
	}

}