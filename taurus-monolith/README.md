# Taurus 微服务单体化部署模块

这是一个将 Taurus Cloud 微服务进行单体化部署的聚合模块。该模块将多个微服务整合到一个 JAR 包中运行，简化部署和运维。

## 功能特点

- 将多个微服务打包到单个 JAR 文件中
- 统一配置管理
- 减少资源占用
- 简化部署和维护
- 支持选择性启用需要的微服务
- **直接使用各微服务原有配置文件**

## 微服务单体化集成要求与规则

### 1. 微服务集成的前提条件

并非所有微服务都适合集成到单体应用中。微服务必须满足以下基本条件：

#### 1.1 技术栈兼容性
- **Spring Boot版本一致性**：所有微服务必须使用相同或兼容的Spring Boot版本
- **JDK版本统一**：确保所有微服务使用相同的JDK版本
- **依赖库兼容**：各微服务的第三方依赖库版本必须兼容，避免版本冲突
- **框架统一**：使用相同的Web框架（如Spring MVC）、数据访问框架（如MyBatis）

#### 1.2 架构设计要求
- **无状态设计**：微服务必须是无状态的，不依赖本地存储
- **服务边界清晰**：微服务间有明确的业务边界，减少耦合
- **配置外部化**：配置信息通过配置文件或配置中心管理，不硬编码
- **资源隔离友好**：服务能够共享JVM资源而不相互干扰

#### 1.3 数据访问要求
- **数据源隔离**：每个微服务使用独立的数据源配置
- **事务边界明确**：避免跨服务的分布式事务依赖
- **缓存隔离**：Redis等缓存使用不同的namespace或key前缀

### 2. Bean冲突解决规则

#### 2.1 Bean命名规范
```java
// 示例：使用服务前缀避免冲突
@Component("examDataSource")
public class ExamDataSourceConfig {
    // ...
}

@Component("paymentDataSource") 
public class PaymentDataSourceConfig {
    // ...
}
```

#### 2.2 配置类冲突处理
```java
// 使用条件注解避免配置冲突
@Configuration
@ConditionalOnProperty(name = "taurus.monolith.enabled-services", havingValue = "payment")
public class PaymentServiceConfig {
    // 支付服务特定配置
}
```

#### 2.3 过滤冲突的组件
```java
@ComponentScan(basePackages = {"com.taurus"}, 
    excludeFilters = {
        @Filter(type = FilterType.REGEX, pattern = ".*WebFilter"),
        @Filter(type = FilterType.REGEX, pattern = ".*CorsConfig")
    })
```

### 3. 端口和路由管理规则

#### 3.1 端口统一原则
- **单一端口**：所有微服务通过同一个端口对外提供服务
- **路径区分**：通过不同的URL路径区分不同的微服务
- **内部转发**：使用Spring Boot的内部转发机制路由请求

#### 3.2 路由规则设计
```yaml
# 路由规则示例
taurus:
  monolith:
    routing:
      /api/exam/** -> exam-main-service
      /api/form/** -> taurus-form-system  
      /api/pay/** -> taurus-pay
      /api/oss/** -> taurus-oss
```

### 4. 配置管理规则

#### 4.1 配置优先级
1. **主配置文件** (`application-monolith.yml`) - 最高优先级
2. **微服务原配置文件** - 中等优先级  
3. **自定义配置文件** - 较低优先级
4. **默认配置** - 最低优先级

#### 4.2 配置隔离机制
```yaml
# 使用命名空间避免配置冲突
taurus-pay:
  server:
    port: 8081  # 原始端口，用于服务发现
  datasource:
    url: *********************************

taurus-exam:
  server: 
    port: 8082
  datasource:
    url: ******************************
```

### 5. 不适合集成的微服务类型

#### 5.1 技术层面限制
- **使用不同Web容器的服务**（如Jetty vs Tomcat vs Undertow）
- **依赖特定JVM参数的服务**（如大堆内存要求）
- **使用不兼容Spring Boot版本的服务**
- **有严重依赖冲突的服务**

#### 5.2 业务层面限制  
- **高并发要求的核心服务**（应独立部署以获得更好性能）
- **需要频繁发布的服务**（会影响整体应用稳定性）
- **有特殊安全要求的服务**（如需要特殊网络隔离）
- **资源消耗巨大的服务**（如大数据处理服务）

#### 5.3 运维层面限制
- **需要独立扩缩容的服务**
- **有不同监控要求的服务** 
- **需要不同部署环境的服务**

### 6. 集成最佳实践

#### 6.1 分阶段集成策略
1. **第一阶段**：集成业务关联度高、技术栈相同的服务
2. **第二阶段**：集成配置简单、依赖较少的服务
3. **第三阶段**：集成经过充分测试、稳定的服务

#### 6.2 配置管理最佳实践
```yaml
# 推荐的配置结构
taurus:
  monolith:
    # 明确声明要集成的服务
    enabled-services:
      - exam-main      # 核心业务服务
      - form-system    # 表单系统
      - oss           # 对象存储（无状态）
    
    # 排除有冲突风险的服务  
    excluded-services:
      - gateway       # 网关服务通常独立部署
      - config-server # 配置中心服务
```

#### 6.3 监控和诊断
- **Bean冲突检测**：启动时检查并报告Bean冲突
- **配置冲突检测**：检查配置项冲突并给出建议
- **资源使用监控**：监控内存、CPU使用情况
- **服务健康检查**：独立检查各微服务的健康状态

### 7. 测试验证规则

#### 7.1 集成前验证
- **依赖分析**：分析各微服务的依赖关系图
- **配置冲突检查**：检查配置文件中的冲突项
- **端口冲突检测**：确保没有端口绑定冲突

#### 7.2 集成后验证  
- **功能完整性测试**：确保各微服务功能正常
- **性能基准测试**：对比单独部署时的性能
- **稳定性测试**：长时间运行稳定性验证

### 8. 风险控制措施

#### 8.1 回滚机制
- **配置回滚**：快速回滚到上一个稳定配置
- **服务降级**：临时禁用有问题的微服务
- **监控告警**：及时发现和处理问题

#### 8.2 渐进式集成
- **灰度发布**：先在测试环境验证集成效果
- **分批集成**：避免一次性集成过多服务  
- **影响评估**：评估集成对现有服务的影响

## 实现原理

单体化部署通过以下几种技术实现微服务的整合：

1. **依赖聚合**：将微服务模块作为依赖项引入聚合模块中
2. **路由转发**：使用内部转发机制将不同 API 路径映射到对应服务的处理逻辑
3. **条件化配置**：使用 Spring 的条件化注解实现服务的按需启用
4. **多数据源管理**：配置多数据源以支持不同微服务的数据访问需求
5. **统一应用上下文**：所有微服务共享同一个 Spring 应用上下文
6. **原配置复用**：直接加载各微服务模块的原配置文件

## 如何使用

### 1. 打包单体应用

```bash
# 在项目根目录执行
mvn clean package -pl taurus-monolith -am
```

### 2. 运行单体应用

```bash
java -jar taurus-monolith/target/taurus-monolith.jar
```

### 3. 配置说明

#### 主配置文件

主配置文件 `application.yml` 中只需配置公共设置和启用的服务列表：

```yaml
taurus:
  monolith:
    enabled-services:
      - gateway # 网关服务
      - party-games # 派对游戏服务
      # - other-service # 其他需要启用的服务
    service-mappings:
      gateway: taurus-gateway
      party-games: taurus-party-games
      # 其他服务映射
```

#### 微服务原配置文件

系统会自动加载各微服务模块中的原始配置文件，加载顺序为：

1. `{module-path}/src/main/resources/application.yml`
2. `{module-path}/application.yml` 或 `{module-path}/application.properties`
3. 如未找到原配置文件，会尝试加载自定义配置文件：`config/{service-name}-service.yml`

### 4. 环境要求

- JDK 1.8+
- 4GB+ 内存 (视启用的微服务数量而定)
- MySQL 5.7+
- Redis (可选，取决于启用的服务)

### 5. 服务访问

所有微服务的 API 都通过单一入口访问，使用各微服务原有的Controller路径：

**评论服务访问路径：**
- 留言管理: `http://localhost:8100/leaveWord/**`
- 留言点赞: `http://localhost:8100/leaveWordPraised/**`  
- 留言统计: `http://localhost:8100/leaveWordSummary/**`

**支付服务访问路径：**
- 微信支付: `http://localhost:8100/weChatPay/**`
- 支付宝支付: `http://localhost:8100/alipay/**`
- 支付渠道管理: `http://localhost:8100/channel/**`
- 微信App支付: `http://localhost:8100/weChatPayApp/**`

**表单系统访问路径：**
- 表单定义: `http://localhost:8100/formDefinition/**`
- 分类管理: `http://localhost:8100/category/**`
- 流程记录: `http://localhost:8100/formFlowRecord/**`

**对象存储服务访问路径：**
- 文件管理: `http://localhost:8100/ossFile/**`
- 对象管理: `http://localhost:8100/ossObject/**`
- 仓库管理: `http://localhost:8100/ossRespository/**`

**注意：**
- 各微服务保持原有的Controller路径映射
- 无需额外的/api前缀，直接使用原始路径
- 所有服务通过端口8100统一访问

## 配置加载机制

本项目优先使用各微服务模块的原有配置文件，带来以下优势：

1. **配置一致性**：保持与独立部署时完全相同的配置
2. **无需复制**：无需单独维护配置副本
3. **配置隔离**：各微服务配置自然隔离
4. **无缝切换**：在独立部署和单体化部署间轻松切换
5. **团队协作**：各团队可继续维护自己服务的配置文件

配置文件加载流程：

1. 应用启动时首先加载主配置文件 `application.yml`
2. 根据 `taurus.monolith.enabled-services` 获取启用的服务列表
3. 对每个启用的服务，从 `taurus.monolith.service-mappings` 获取模块路径
4. 尝试加载该模块的原始配置文件
5. 若未找到原配置文件，则尝试加载自定义配置文件

配置优先级：

- 主配置文件 > 微服务原配置文件 > 自定义配置文件 > 默认配置

## 技术实现细节

### 组件扫描与加载

```java
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.taurus"})
@ComponentScan(basePackages = {"com.taurus"})
public class TaurusMonolithApplication {
    // ...
}
```

### 路由转发

```java
@Bean
public RouteLocator customRouteLocator(RouteLocatorBuilder builder) {
    return builder.routes()
        // 派对游戏服务路由
        .route("party-games-route", r -> r.path("/api/party/**", "/api/room/**")
            .uri("forward:/party-games"))
        // 可添加其他服务路由
        .build();
}
```

### 多数据源配置

```java
@Primary
@Bean(name = "partyGamesDataSource")
@ConfigurationProperties(prefix = "spring.datasource.party-games")
@ConditionalOnProperty(name = "taurus.monolith.enabled-services", havingValue = "party-games")
public DataSource partyGamesDataSource() {
    return new DruidDataSource();
}
```

### 条件化服务启用

```java
@Configuration
@ConditionalOnProperty(name = "taurus.monolith.enabled-services", havingValue = "party-games")
public static class PartyGamesConfig {
    // 服务特定配置
}
```

### 服务配置加载器

```java
@Configuration
public class ServiceConfigLoader implements ApplicationListener<ApplicationPreparedEvent> {
    @Override
    public void onApplicationEvent(ApplicationPreparedEvent event) {
        // 加载各微服务的原配置文件
        for (String serviceName : enabledServices) {
            String modulePrefix = serviceMappings.get(serviceName);
            tryLoadModuleConfig(environment, serviceName, modulePrefix);
        }
    }
}
```

## 注意事项

1. 微服务之间可能存在 Bean 冲突，需要通过 @Primary 等方式解决
2. 不同微服务的数据源配置需要相互隔离
3. 网关服务和业务服务集成时需要特别注意路由和过滤器的配置
4. 服务之间的依赖关系需要仔细处理，避免循环依赖
5. 资源消耗会随启用的服务数量增加而增加
6. 大量服务合并可能导致启动时间延长
7. 确保 service-mappings 配置正确，映射到实际的模块路径

## 性能考虑

与分布式部署相比，单体化部署有以下性能特点：

- **优势**：减少进程间通信开销，降低网络延迟，共享 JVM 资源
- **劣势**：单个 JVM 内存占用较高，垃圾回收可能影响整体性能

## 扩展与定制

如需添加更多微服务到单体部署中：

1. 在聚合模块的 pom.xml 中添加新服务的依赖
2. 在路由配置中添加新服务的路由规则
3. 在 application.yml 中的 enabled-services 中启用新服务
4. 在 service-mappings 中添加新服务的模块路径映射
