package com.taurus.monolith.config;

import java.net.InetAddress;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.SmartLifecycle;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.taurus.monolith.config.MonolithServiceManager.ServiceRegistration;

import lombok.extern.slf4j.Slf4j;

/**
 * 多服务Nacos注册器
 * 负责将各微服务注册到Nacos服务注册中心
 */
@Component
@Slf4j
@DependsOn({ "monolithServiceManager", "simpleMicroserviceConfigManager" })
public class MultiServiceNacosRegistrar implements SmartLifecycle {

    @Autowired
    private MonolithServiceManager serviceManager;

    @Autowired
    private SimpleMicroserviceConfigManager configManager;

    @Autowired
    private Environment environment;

    @Value("${server.port}")
    private int serverPort;

    @Value("${spring.cloud.nacos.discovery.server-addr:127.0.0.1:8848}")
    private String nacosServerAddr;

    private boolean running = false;
    private Map<String, NamingService> namingServices = new HashMap<>();

    @Override
    public void start() {
        log.info("开始注册多个微服务到Nacos...");

        // 检查多服务注册是否启用
        if (!serviceManager.getMultiServiceRegistration().isEnabled()) {
            log.info("多服务注册功能未启用，跳过注册");
            return;
        }

        // 获取需要注册的服务列表
        for (ServiceRegistration serviceReg : serviceManager.getMultiServiceRegistration().getServices()) {
            String serviceName = serviceReg.getName();
            registerService(serviceName);
        }

        running = true;
        log.info("所有微服务注册完成");
    }

    /**
     * 注册单个微服务到Nacos
     * 
     * @param serviceName 服务名称
     */
    private void registerService(String serviceName) {
        try {
            // 获取服务的配置
            Properties serviceProps = configManager.getServiceConfig(serviceName);
            String servicePort = "8080"; // 默认端口

            // 尝试获取服务自己的端口和Nacos配置
            if (serviceProps != null) {
                // 使用微服务自己的端口
                String port = serviceProps.getProperty("server.port");
                if (port != null && !port.isEmpty()) {
                    servicePort = port;
                    log.info("微服务[{}]使用自己的端口: {}", serviceName, port);
                }
            }

            // 获取Nacos连接配置（优先使用微服务自己的配置，如果没有则使用全局配置）
            String serverAddr = getServiceProperty(serviceName, "spring.cloud.nacos.discovery.server-addr",
                    nacosServerAddr);
            String username = getServiceProperty(serviceName, "spring.cloud.nacos.discovery.username", "nacos");
            String password = getServiceProperty(serviceName, "spring.cloud.nacos.discovery.password", "nacos");
            String namespace = getServiceProperty(serviceName, "spring.cloud.nacos.discovery.namespace", "");
            String group = getServiceProperty(serviceName, "spring.cloud.nacos.discovery.group", "DEFAULT_GROUP");
            String clusterName = getServiceProperty(serviceName, "spring.cloud.nacos.discovery.cluster-name",
                    "DEFAULT");

            log.info("微服务[{}]的Nacos配置 - 服务器: {}, 用户名: {}, 命名空间: {}, 分组: {}, 集群: {}",
                    serviceName, serverAddr, username, namespace, group, clusterName);

            // 获取或创建NamingService
            NamingService namingService = getNamingService(serverAddr, username, password, namespace);

            // 构建实例
            Instance instance = new Instance();
            instance.setIp(InetAddress.getLocalHost().getHostAddress());
            instance.setPort(Integer.parseInt(String.valueOf(serverPort))); // 使用单体应用的端口
            instance.setServiceName(serviceName);
            instance.setWeight(1.0);
            instance.setClusterName(clusterName);

            // 设置元数据
            Map<String, String> metadata = new HashMap<>();
            metadata.put("monolith", "true");
            metadata.put("registered-by", "taurus-monolith");
            metadata.put("original-port", servicePort); // 记录原始端口

            // 如果有对应的上下文路径，添加到元数据中
            for (ServiceRegistration reg : serviceManager.getMultiServiceRegistration().getServices()) {
                if (serviceName.equals(reg.getName()) && reg.getContextPath() != null) {
                    metadata.put("context-path", reg.getContextPath());
                    break;
                }
            }

            instance.setMetadata(metadata);

            // 注册实例
            namingService.registerInstance(serviceName, group, instance);
            log.info("微服务[{}]已注册到Nacos服务器[{}]，命名空间[{}]，分组[{}]",
                    serviceName, serverAddr,
                    StringUtils.hasText(namespace) ? namespace : "默认命名空间",
                    group);

        } catch (Exception e) {
            log.error("微服务[{}]注册失败: {}", serviceName, e.getMessage());
            log.error("详细错误信息: ", e);

            // 如果是认证错误，给出更具体的提示
            if (e.getMessage() != null && e.getMessage().contains("user not found")) {
                log.error("认证失败，请检查Nacos用户名和密码配置");
                log.error("当前配置 - 服务器: {}, 用户名: {}",
                        getServiceProperty(serviceName, "spring.cloud.nacos.discovery.server-addr", nacosServerAddr),
                        getServiceProperty(serviceName, "spring.cloud.nacos.discovery.username", "nacos"));
            }
        }
    }

    /**
     * 获取服务的配置属性
     */
    private String getServiceProperty(String serviceName, String key, String defaultValue) {
        return configManager.getServiceProperty(serviceName, key, defaultValue);
    }

    /**
     * 获取或创建Nacos命名服务
     * 
     * @param serverAddr 服务器地址
     * @param username   用户名
     * @param password   密码
     * @param namespace  命名空间
     * @return 命名服务
     * @throws NacosException 如果创建服务失败
     */
    private NamingService getNamingService(String serverAddr, String username, String password, String namespace)
            throws NacosException {
        String key = serverAddr + "#" + username + "#" + namespace;
        if (!namingServices.containsKey(key)) {
            Properties props = new Properties();
            props.setProperty("serverAddr", serverAddr);
            props.setProperty("username", username);
            props.setProperty("password", password);
            if (StringUtils.hasText(namespace)) {
                props.setProperty("namespace", namespace);
            }
            log.info("创建Nacos命名服务，服务器: {}, 用户名: {}, 命名空间: {}", serverAddr, username, namespace);
            NamingService namingService = NacosFactory.createNamingService(props);
            namingServices.put(key, namingService);
        }
        return namingServices.get(key);
    }

    @Override
    public void stop() {
        if (running) {
            log.info("正在注销所有注册的微服务...");

            // 从服务管理器获取注册的服务列表
            for (ServiceRegistration serviceReg : serviceManager.getMultiServiceRegistration().getServices()) {
                String serviceName = serviceReg.getName();

                try {
                    // 获取服务的Nacos配置
                    String group = getServiceProperty(serviceName, "spring.cloud.nacos.discovery.group",
                            "DEFAULT_GROUP");

                    for (Map.Entry<String, NamingService> entry : namingServices.entrySet()) {
                        // 注销所有注册的服务实例
                        entry.getValue().deregisterInstance(
                                serviceName,
                                group,
                                InetAddress.getLocalHost().getHostAddress(),
                                serverPort);
                    }
                    log.info("微服务[{}]已从Nacos注销", serviceName);
                } catch (Exception e) {
                    log.error("注销微服务[{}]失败: {}", serviceName, e.getMessage());
                }
            }

            running = false;
        }
    }

    @Override
    public boolean isRunning() {
        return running;
    }

    @Override
    public int getPhase() {
        return Integer.MAX_VALUE - 1; // 确保在大多数Bean初始化后执行
    }

    @Override
    public boolean isAutoStartup() {
        return true;
    }

    @Override
    public void stop(Runnable callback) {
        stop();
        callback.run();
    }
}