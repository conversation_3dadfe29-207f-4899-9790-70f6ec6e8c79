package com.taurus.monolith.config;

import java.util.Properties;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;

import lombok.extern.slf4j.Slf4j;

/**
 * Nacos多服务注册配置类
 * 提供基本的Nacos服务实例
 */
@Configuration
@Slf4j
@ConditionalOnProperty(name = "spring.cloud.nacos.discovery.enabled", havingValue = "true", matchIfMissing = true)
public class NacosMultiServiceConfig {

    @Value("${spring.cloud.nacos.discovery.server-addr:127.0.0.1:8848}")
    private String nacosServerAddr;

    @Value("${spring.cloud.nacos.discovery.namespace:}")
    private String nacosNamespace;

    /**
     * 创建Nacos命名服务实例
     * 用于服务注册与发现
     * 
     * @return Nacos命名服务
     */
    @Bean
    @Primary
    public NamingService namingService() throws NacosException {
        log.info("创建Nacos命名服务，服务器地址: {}", nacosServerAddr);

        Properties properties = new Properties();
        properties.setProperty("serverAddr", nacosServerAddr);

        if (nacosNamespace != null && !nacosNamespace.isEmpty()) {
            properties.setProperty("namespace", nacosNamespace);
            log.info("Nacos命名空间: {}", nacosNamespace);
        }

        return NacosFactory.createNamingService(properties);
    }
}