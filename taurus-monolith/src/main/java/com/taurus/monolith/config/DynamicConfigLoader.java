package com.taurus.monolith.config;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import org.springframework.boot.context.event.ApplicationEnvironmentPreparedEvent;
import org.springframework.boot.env.YamlPropertySourceLoader;
import org.springframework.context.ApplicationListener;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.PropertiesPropertySource;
import org.springframework.core.env.PropertySource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * 动态配置加载器
 * 严格按照Spring Boot标准配置文件加载顺序加载各微服务配置
 * 确保与微服务独立启动时的配置加载行为完全一致
 * 支持.properties和.yml/.yaml格式的配置文件
 */
@Slf4j
@Component
public class DynamicConfigLoader implements ApplicationListener<ApplicationEnvironmentPreparedEvent> {

    // 可以直接使用的配置前缀（不产生Bean冲突的配置）
    private static final String[] REUSABLE_CONFIG_PREFIXES = {
            "spring.cloud.nacos.discovery.server-addr",
            "spring.cloud.nacos.discovery.username",
            "spring.cloud.nacos.discovery.password",
            "spring.cloud.nacos.discovery.namespace",
            "spring.cloud.nacos.discovery.group",
            "spring.cloud.nacos.discovery.cluster-name",
            "logging.config",
            "logging.level",
            // 业务配置可以直接使用
            "wx.",
            "taurus.",
            // MyBatis配置可以合并使用
            "mybatis.mapper-locations",
            "mybatis.type-aliases-package",
            "mybatis.configuration",
            // 数据源配置（用于MultiDataSourceConfig创建数据源）
            "spring.datasource.url",
            "spring.datasource.username",
            "spring.datasource.password",
            "spring.datasource.driver-class-name",
            "spring.datasource.druid.initial-size",
            "spring.datasource.druid.min-idle",
            "spring.datasource.druid.max-active",
            "spring.datasource.druid.max-wait",
            "spring.datasource.druid.test-while-idle",
            "spring.datasource.druid.test-on-borrow",
            "spring.datasource.druid.test-on-return",
            "spring.datasource.druid.validation-query"
    };

    // 各微服务的配置文件加载配置（按Spring Boot标准顺序）
    private static final Map<String, MicroserviceConfigInfo> MICROSERVICE_CONFIGS = new HashMap<>();

    static {
        // 评论服务配置 - 按照Spring Boot标准加载顺序
        MICROSERVICE_CONFIGS.put("taurus-comment", new MicroserviceConfigInfo(
                "taurus-comment",
                "dev", // 激活的profile
                "taurus-comment/src/main/resources/"));

        // 支付服务配置 - 按照Spring Boot标准加载顺序
        MICROSERVICE_CONFIGS.put("taurus-pay", new MicroserviceConfigInfo(
                "taurus-pay",
                "test", // 激活的profile
                "taurus-pay/src/main/resources/"));

        // 表单系统配置 - 按照Spring Boot标准加载顺序
        MICROSERVICE_CONFIGS.put("taurus-form-system", new MicroserviceConfigInfo(
                "taurus-form-system",
                "dev", // 激活的profile
                "taurus-form-system/src/main/resources/"));

        // OSS服务配置 - 按照Spring Boot标准加载顺序
        MICROSERVICE_CONFIGS.put("taurus-oss", new MicroserviceConfigInfo(
                "taurus-oss",
                "dev", // 激活的profile
                "taurus-oss/src/main/resources/"));
    }

    // YAML加载器
    private final YamlPropertySourceLoader yamlLoader = new YamlPropertySourceLoader();

    /**
     * 微服务配置信息
     */
    private static class MicroserviceConfigInfo {
        private final String serviceName;
        private final String activeProfile;
        private final String resourcePath;

        public MicroserviceConfigInfo(String serviceName, String activeProfile, String resourcePath) {
            this.serviceName = serviceName;
            this.activeProfile = activeProfile;
            this.resourcePath = resourcePath;
        }

        /**
         * 获取按Spring Boot标准顺序的配置文件列表
         * 1. application.properties
         * 2. application.yml
         * 3. application-{profile}.properties
         * 4. application-{profile}.yml
         */
        public String[] getConfigFiles() {
            return new String[] {
                    "application.properties",
                    "application.yml",
                    "application-" + activeProfile + ".properties",
                    "application-" + activeProfile + ".yml"
            };
        }
    }

    @Override
    public void onApplicationEvent(ApplicationEnvironmentPreparedEvent event) {
        ConfigurableEnvironment environment = event.getEnvironment();

        // 检查是否启用单体化模式
        String monolithEnabled = environment.getProperty("taurus.monolith.enabled");
        if (!"true".equals(monolithEnabled)) {
            return;
        }

        log.info("开始动态加载各微服务的可复用配置...");

        // 为每个微服务按Spring Boot标准顺序加载配置
        for (Map.Entry<String, MicroserviceConfigInfo> entry : MICROSERVICE_CONFIGS.entrySet()) {
            String serviceName = entry.getKey();
            MicroserviceConfigInfo configInfo = entry.getValue();

            loadMicroserviceConfigBySpringBootOrder(environment, serviceName, configInfo);
        }

        log.info("动态配置加载完成");
    }

    /**
     * 按照Spring Boot标准顺序加载微服务配置
     * 
     * @param environment 环境对象
     * @param serviceName 服务名称
     * @param configInfo  配置信息
     */
    private void loadMicroserviceConfigBySpringBootOrder(ConfigurableEnvironment environment,
            String serviceName, MicroserviceConfigInfo configInfo) {

        log.info("正在加载微服务[{}]的可复用配置，按照Spring Boot标准顺序...", serviceName);

        String[] configFiles = configInfo.getConfigFiles();
        boolean hasLoadedAnyConfig = false;

        // 按照Spring Boot标准顺序逐个尝试加载配置文件
        for (int i = 0; i < configFiles.length; i++) {
            String configFile = configFiles[i];
            log.info("  [{}] 加载{}配置文件: {}",
                    i + 1,
                    i < 2 ? "主" : "profile特定",
                    configFile);

            boolean loaded = tryLoadConfigFile(environment, serviceName, configInfo, configFile);
            if (loaded) {
                hasLoadedAnyConfig = true;
                // 继续加载后续配置文件，因为Spring Boot会按顺序覆盖配置
            } else {
                log.warn("    配置文件[{}]未找到或加载失败", configFile);
            }
        }

        if (!hasLoadedAnyConfig) {
            log.warn("微服务[{}]未加载到任何配置", serviceName);
        }
    }

    /**
     * 尝试加载单个配置文件
     */
    private boolean tryLoadConfigFile(ConfigurableEnvironment environment, String serviceName,
            MicroserviceConfigInfo configInfo, String configFile) {

        // 构建配置文件路径 - 使用多种方式尝试定位文件
        String configPath = configInfo.resourcePath + configFile;

        try {
            Resource resource = null;
            String actualPath = null;

            // 方式1: 智能路径解析（主要方式）
            String userDir = System.getProperty("user.dir");
            String projectRoot;

            // 智能判断当前工作目录，适配Maven和IDE两种启动方式
            if (userDir.endsWith("taurus-monolith")) {
                // Maven命令行启动：工作目录在taurus-monolith下
                projectRoot = new java.io.File(userDir).getParent();
            } else if (userDir.endsWith("taurus-cloud")) {
                // IDE调试启动：工作目录在taurus-cloud根目录下
                projectRoot = userDir;
            } else {
                // 其他情况，尝试查找taurus-cloud目录
                java.io.File current = new java.io.File(userDir);
                while (current != null && !current.getName().equals("taurus-cloud")) {
                    current = current.getParentFile();
                }
                projectRoot = current != null ? current.getAbsolutePath() : new java.io.File(userDir).getParent();
            }

            if (projectRoot != null) {
                String fullPath = projectRoot + "/" + configPath;
                log.debug("      尝试路径1: {} (userDir={}, projectRoot={})", fullPath, userDir, projectRoot);
                Resource fileResource = new FileSystemResource(fullPath);
                if (fileResource.exists()) {
                    resource = fileResource;
                    actualPath = fullPath;
                    log.debug("      ✓ 路径1成功");
                } else {
                    log.debug("      ✗ 路径1失败: 文件不存在");
                }
            }

            // 方式2: 如果方式1失败，尝试从相对路径加载
            if (resource == null) {
                String fullPath = "../" + configPath;
                log.debug("      尝试路径2: {}", fullPath);
                Resource fileResource = new FileSystemResource(fullPath);
                if (fileResource.exists()) {
                    resource = fileResource;
                    actualPath = fullPath;
                    log.debug("      ✓ 路径2成功");
                } else {
                    log.debug("      ✗ 路径2失败: 文件不存在");
                }
            }

            // 方式3: 优先从内嵌classpath加载（打包后的主要方式）
            if (resource == null) {
                String embeddedPath = "microservices/" + serviceName + "/" + configFile;
                log.debug("      尝试路径3: classpath:{} (内嵌配置)", embeddedPath);
                Resource embeddedResource = new ClassPathResource(embeddedPath);
                if (embeddedResource.exists()) {
                    resource = embeddedResource;
                    actualPath = "classpath:" + embeddedPath;
                    log.debug("      ✓ 路径3成功 (内嵌配置文件)");
                } else {
                    log.debug("      ✗ 路径3失败: 内嵌配置文件不存在");
                }
            }

            // 方式4: 如果内嵌配置不存在，尝试从原始classpath加载
            if (resource == null) {
                log.debug("      尝试路径4: classpath:{} (原始路径)", configPath);
                Resource classpathResource = new ClassPathResource(configPath);
                if (classpathResource.exists()) {
                    resource = classpathResource;
                    actualPath = "classpath:" + configPath;
                    log.debug("      ✓ 路径4成功 (原始classpath)");
                } else {
                    log.debug("      ✗ 路径4失败: 原始classpath中不存在");
                }
            }

            if (resource != null && resource.exists()) {
                Properties properties = loadPropertiesFromResource(resource, configFile);
                if (properties != null && !properties.isEmpty()) {
                    addReusablePropertiesToEnvironment(environment, serviceName, properties, configFile);
                    log.info("    ✓ 成功从[{}]加载{}个配置项", actualPath, properties.size());
                    return true;
                } else {
                    log.warn("    配置文件[{}]为空", actualPath);
                    return false;
                }
            } else {
                log.debug("    配置文件[{}]不存在（已尝试多种路径）", configPath);
                log.debug("      尝试的路径: user.dir={}, parentPath={}", userDir, projectRoot);
                return false;
            }
        } catch (Exception e) {
            log.warn("    加载配置文件[{}]时出错: {}", configPath, e.getMessage());
            return false;
        }
    }

    /**
     * 从Resource加载Properties，支持.properties和.yml/.yaml文件
     */
    private Properties loadPropertiesFromResource(Resource resource, String configFile) throws IOException {
        if (configFile.endsWith(".yml") || configFile.endsWith(".yaml")) {
            // 使用Spring Boot的YamlPropertySourceLoader加载YAML文件
            return loadYamlAsProperties(resource, configFile);
        } else {
            // 加载properties文件
            Properties properties = new Properties();
            try (InputStream inputStream = resource.getInputStream()) {
                properties.load(inputStream);
                return properties;
            }
        }
    }

    /**
     * 将YAML文件加载为Properties
     */
    private Properties loadYamlAsProperties(Resource resource, String configFile) {
        try {
            List<PropertySource<?>> propertySources = yamlLoader.load(configFile, resource);
            if (propertySources != null && !propertySources.isEmpty()) {
                Properties properties = new Properties();

                // 将YAML PropertySource转换为Properties
                for (PropertySource<?> propertySource : propertySources) {
                    if (propertySource.getSource() instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> source = (Map<String, Object>) propertySource.getSource();
                        flattenMap(source, "", properties);
                    }
                }

                return properties;
            }
        } catch (Exception e) {
            log.warn("加载YAML配置文件[{}]失败: {}", configFile, e.getMessage());
        }
        return null;
    }

    /**
     * 将嵌套的Map扁平化为Properties格式
     */
    @SuppressWarnings("unchecked")
    private void flattenMap(Map<String, Object> source, String prefix, Properties properties) {
        for (Map.Entry<String, Object> entry : source.entrySet()) {
            String key = prefix.isEmpty() ? entry.getKey() : prefix + "." + entry.getKey();
            Object value = entry.getValue();

            if (value instanceof Map) {
                flattenMap((Map<String, Object>) value, key, properties);
            } else if (value instanceof List) {
                List<?> list = (List<?>) value;
                for (int i = 0; i < list.size(); i++) {
                    Object item = list.get(i);
                    if (item instanceof Map) {
                        flattenMap((Map<String, Object>) item, key + "[" + i + "]", properties);
                    } else {
                        properties.setProperty(key + "[" + i + "]", String.valueOf(item));
                    }
                }
            } else {
                properties.setProperty(key, String.valueOf(value));
            }
        }
    }

    /**
     * 将可复用的配置属性添加到环境中
     */
    private void addReusablePropertiesToEnvironment(ConfigurableEnvironment environment,
            String serviceName, Properties properties, String configFile) {

        Properties reusableProps = new Properties();
        int addedCount = 0;

        // 过滤可复用的配置
        for (String key : properties.stringPropertyNames()) {
            if (isReusableConfig(key)) {
                String value = properties.getProperty(key);

                // 特殊处理加密密码
                if (key.contains("password") && value != null && value.startsWith("ENC(")) {
                    log.warn("检测到加密密码配置[{}]，跳过加载，将使用明文密码替代", key);
                    // 对于OSS服务的密码，使用明文密码
                    if (serviceName.equals("taurus-oss")) {
                        value = "873dPw(*^Kd%";
                        log.info("为OSS服务使用明文密码替代加密密码");
                    } else {
                        // 对于其他服务，跳过加密密码的加载
                        log.debug("跳过加密密码配置: {}", key);
                        continue;
                    }
                }

                // 对于数据源配置，添加服务名前缀以避免冲突
                if (key.startsWith("spring.datasource.")) {
                    String prefixedKey = serviceName + "." + key;
                    reusableProps.setProperty(prefixedKey, value);
                    log.debug("      添加带前缀的数据源配置: {} = {}", prefixedKey, maskSensitiveValue(key, value));
                } else {
                    // 其他配置直接使用原始key
                    reusableProps.setProperty(key, value);
                    log.debug("      添加可复用配置: {} = {}", key, maskSensitiveValue(key, value));
                }
                addedCount++;
            }
        }

        // 特殊处理Nacos认证信息，存储为微服务特定的属性
        handleNacosCredentials(serviceName, properties, reusableProps);

        if (!reusableProps.isEmpty()) {
            // 创建PropertySource名称，体现加载顺序
            String propertySourceName = String.format("microservice-%s-%s", serviceName,
                    configFile.replace(".", "-"));

            PropertiesPropertySource propertySource = new PropertiesPropertySource(
                    propertySourceName, reusableProps);

            // 添加到环境中，使用适当的优先级
            // 后加载的配置文件应该有更高的优先级（Spring Boot行为）
            environment.getPropertySources().addAfter("systemEnvironment", propertySource);

            log.info("      为微服务[{}]从[{}]添加了{}个可复用配置项", serviceName, configFile, addedCount);
        }
    }

    /**
     * 处理Nacos认证信息
     */
    private void handleNacosCredentials(String serviceName, Properties properties, Properties reusableProps) {
        String nacosUsername = properties.getProperty("spring.cloud.nacos.discovery.username");
        String nacosPassword = properties.getProperty("spring.cloud.nacos.discovery.password");

        if (nacosUsername != null) {
            reusableProps.setProperty("microservice." + serviceName + ".nacos.username", nacosUsername);
            log.debug("      添加微服务[{}]特定的Nacos用户名: {}", serviceName, nacosUsername);
        }

        if (nacosPassword != null) {
            reusableProps.setProperty("microservice." + serviceName + ".nacos.password", nacosPassword);
            log.debug("      添加微服务[{}]特定的Nacos密码: ****", serviceName);
        }
    }

    /**
     * 检查配置键是否可以复用
     */
    private boolean isReusableConfig(String key) {
        for (String prefix : REUSABLE_CONFIG_PREFIXES) {
            if (key.startsWith(prefix)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 屏蔽敏感配置值
     */
    private String maskSensitiveValue(String key, String value) {
        if (key.toLowerCase().contains("password") || key.toLowerCase().contains("secret")) {
            return "****";
        }
        return value;
    }
}