package com.taurus.monolith.config;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * 配置验证器
 * 在应用启动后验证关键配置是否正确加载
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "taurus.monolith.enabled", havingValue = "true")
public class ConfigurationValidator {

    @Autowired
    private Environment environment;

    @PostConstruct
    public void validateConfigurations() {
        log.info("开始验证单体化应用的关键配置...");

        boolean allValid = true;

        // 验证微信支付配置
        allValid &= validateWeChatPayConfig();

        // 验证数据源配置
        allValid &= validateDataSourceConfig();

        // 验证Nacos配置
        allValid &= validateNacosConfig();

        if (allValid) {
            log.info("✓ 所有关键配置验证通过");
        } else {
            log.warn("⚠ 部分配置验证失败，请检查相关配置");
        }

        log.info("配置验证完成");
    }

    /**
     * 验证微信支付相关配置
     */
    private boolean validateWeChatPayConfig() {
        log.info("验证微信支付配置...");

        String wxNotifyUrl = environment.getProperty("wx.notifyUrl");
        if (wxNotifyUrl != null && !wxNotifyUrl.isEmpty()) {
            log.info("✓ wx.notifyUrl: {}", wxNotifyUrl);
            return true;
        } else {
            log.error("✗ wx.notifyUrl 配置缺失或为空");
            return false;
        }
    }

    /**
     * 验证数据源配置
     */
    private boolean validateDataSourceConfig() {
        log.info("验证数据源配置...");

        boolean valid = true;

        // 检查各微服务的数据源配置
        String[] services = { "taurus-pay", "taurus-comment", "taurus-form-system", "taurus-oss" };

        for (String service : services) {
            String urlKey = service + ".spring.datasource.url";
            String usernameKey = service + ".spring.datasource.username";

            String url = environment.getProperty(urlKey);
            String username = environment.getProperty(usernameKey);

            if (url != null && username != null) {
                log.info("✓ {}: 数据源配置存在", service);
            } else {
                log.warn("⚠ {}: 数据源配置可能缺失", service);
                // 数据源配置缺失不算严重错误，因为有备用硬编码配置
            }
        }

        return valid;
    }

    /**
     * 验证Nacos配置
     */
    private boolean validateNacosConfig() {
        log.info("验证Nacos配置...");

        String serverAddr = environment.getProperty("spring.cloud.nacos.discovery.server-addr");
        String username = environment.getProperty("spring.cloud.nacos.discovery.username");
        String password = environment.getProperty("spring.cloud.nacos.discovery.password");

        if (serverAddr != null && username != null && password != null) {
            log.info("✓ Nacos配置: 服务器={}, 用户名={}", serverAddr, username);
            return true;
        } else {
            log.error("✗ Nacos配置不完整");
            return false;
        }
    }
}