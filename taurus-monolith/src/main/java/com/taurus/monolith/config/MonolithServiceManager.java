package com.taurus.monolith.config;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 单体化服务管理器
 * 负责管理单体化部署中启用的微服务
 */
@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "taurus.monolith")
public class MonolithServiceManager {

    private boolean enabled = true;

    private List<String> enabledServices = new ArrayList<>();

    private Map<String, String> serviceMappings = new HashMap<>();

    @Data
    public static class ServiceRegistration {
        private String name;
        private String contextPath;
    }

    @Data
    public static class MultiServiceRegistrationConfig {
        private boolean enabled = true;
        private List<ServiceRegistration> services = new ArrayList<>();
    }

    private MultiServiceRegistrationConfig multiServiceRegistration = new MultiServiceRegistrationConfig();

    /**
     * 初始化服务管理器
     */
    @PostConstruct
    public void init() {
        log.info("初始化单体化部署服务管理器");
        log.info("已启用服务: {}", enabledServices);
        log.info("服务映射关系: {}", serviceMappings);
        log.info("多服务注册配置: enabled={}, serviceCount={}",
                multiServiceRegistration.isEnabled(),
                multiServiceRegistration.getServices().size());
    }

    /**
     * 检查服务是否启用
     * 
     * @param serviceName 服务名称
     * @return 是否启用
     */
    public boolean isServiceEnabled(String serviceName) {
        return enabledServices != null && enabledServices.contains(serviceName);
    }

    /**
     * 获取服务的路径映射
     * 
     * @param serviceName 服务名称
     * @return 路径映射
     */
    public String getServiceMapping(String serviceName) {
        return serviceMappings.get(serviceName);
    }

    /**
     * 获取多服务注册配置
     * 
     * @return 多服务注册配置
     */
    public MultiServiceRegistrationConfig getMultiServiceRegistration() {
        return multiServiceRegistration;
    }
}