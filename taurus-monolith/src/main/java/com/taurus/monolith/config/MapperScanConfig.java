package com.taurus.monolith.config;

import org.mybatis.spring.mapper.MapperScannerConfigurer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import lombok.extern.slf4j.Slf4j;

/**
 * 单体化部署的Mapper扫描配置
 * 为每个微服务配置独立的MapperScannerConfigurer，指定对应的SqlSessionFactory
 */
@Configuration
@Slf4j
@ConditionalOnProperty(name = "taurus.monolith.enabled", havingValue = "true")
public class MapperScanConfig {

    /**
     * 表单系统Mapper扫描配置
     */
    @Bean(name = "formSysMapperScannerConfigurer")
    public MapperScannerConfigurer formSysMapperScannerConfigurer() {
        log.info("配置表单系统Mapper扫描");

        MapperScannerConfigurer configurer = new MapperScannerConfigurer();
        configurer.setBasePackage("com.taurus.formSys.mapper");
        configurer.setSqlSessionFactoryBeanName("formSysSqlSessionFactory");

        log.info("表单系统Mapper扫描配置完成: com.taurus.formSys.mapper");
        return configurer;
    }

    /**
     * OSS服务Mapper扫描配置
     */
    @Bean(name = "ossMapperScannerConfigurer")
    public MapperScannerConfigurer ossMapperScannerConfigurer() {
        log.info("配置OSS服务Mapper扫描");

        MapperScannerConfigurer configurer = new MapperScannerConfigurer();
        configurer.setBasePackage("com.taurus.oss.mapper");
        configurer.setSqlSessionFactoryBeanName("ossSqlSessionFactory");

        log.info("OSS服务Mapper扫描配置完成: com.taurus.oss.mapper");
        return configurer;
    }

    /**
     * 评论服务Mapper扫描配置
     */
    @Bean(name = "commentMapperScannerConfigurer")
    public MapperScannerConfigurer commentMapperScannerConfigurer() {
        log.info("配置评论服务Mapper扫描");

        MapperScannerConfigurer configurer = new MapperScannerConfigurer();
        configurer.setBasePackage("com.taurus.comment.mapper");
        configurer.setSqlSessionFactoryBeanName("commentSqlSessionFactory");

        log.info("评论服务Mapper扫描配置完成: com.taurus.comment.mapper");
        return configurer;
    }

    /**
     * 支付服务Mapper扫描配置
     */
    @Bean(name = "paymentMapperScannerConfigurer")
    public MapperScannerConfigurer paymentMapperScannerConfigurer() {
        log.info("配置支付服务Mapper扫描");

        MapperScannerConfigurer configurer = new MapperScannerConfigurer();
        configurer.setBasePackage("com.taurus.payment.mapper");
        configurer.setSqlSessionFactoryBeanName("paymentSqlSessionFactory");

        log.info("支付服务Mapper扫描配置完成: com.taurus.payment.mapper");
        return configurer;
    }
}