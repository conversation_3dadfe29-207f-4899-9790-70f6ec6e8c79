package com.taurus.monolith.config;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.stereotype.Component;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 微服务兼容性检查器
 * 在应用启动时检查各微服务是否适合集成到单体应用中
 */
@Component
@Slf4j
public class ServiceCompatibilityChecker implements ApplicationListener<ApplicationReadyEvent> {

    @Autowired
    private MonolithServiceManager serviceManager;

    @Autowired
    private SimpleMicroserviceConfigManager configManager;

    @Autowired
    private ConfigurableApplicationContext applicationContext;

    // 存储检查结果
    private Map<String, CompatibilityResult> compatibilityResults = new ConcurrentHashMap<>();

    /**
     * 兼容性检查结果
     */
    @Data
    public static class CompatibilityResult {
        private String serviceName;
        private boolean compatible = true;
        private List<String> warnings = new ArrayList<>();
        private List<String> errors = new ArrayList<>();
        private Map<String, Object> metadata = new HashMap<>();

        public void addWarning(String warning) {
            warnings.add(warning);
        }

        public void addError(String error) {
            errors.add(error);
            compatible = false;
        }

        public boolean hasIssues() {
            return !warnings.isEmpty() || !errors.isEmpty();
        }
    }

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        log.info("应用启动完成，开始执行微服务兼容性检查...");
        performCompatibilityCheck();
        generateCompatibilityReport();
    }

    /**
     * 执行兼容性检查
     */
    private void performCompatibilityCheck() {
        Set<String> enabledServices = new HashSet<>(serviceManager.getEnabledServices());
        Map<String, String> serviceMappings = serviceManager.getServiceMappings();

        for (String serviceAlias : enabledServices) {
            String serviceName = serviceMappings.get(serviceAlias);
            if (serviceName != null) {
                CompatibilityResult result = checkServiceCompatibility(serviceAlias, serviceName);
                compatibilityResults.put(serviceAlias, result);
            }
        }
    }

    /**
     * 检查单个服务的兼容性
     */
    private CompatibilityResult checkServiceCompatibility(String serviceAlias, String serviceName) {
        CompatibilityResult result = new CompatibilityResult();
        result.setServiceName(serviceName);

        log.info("正在检查微服务[{}]的兼容性...", serviceName);

        // 1. 检查配置文件存在性
        checkConfigurationAvailability(result, serviceName);

        // 2. 检查端口冲突
        checkPortConflicts(result, serviceName);

        // 3. 检查Bean冲突风险
        checkBeanConflicts(result, serviceName);

        // 4. 检查数据源配置
        checkDataSourceConfiguration(result, serviceName);

        // 5. 检查Spring Boot版本兼容性
        checkSpringBootCompatibility(result, serviceName);

        // 6. 检查依赖冲突风险
        checkDependencyConflicts(result, serviceName);

        // 7. 检查资源使用情况
        checkResourceUsage(result, serviceName);

        if (result.isCompatible()) {
            log.info("微服务[{}]兼容性检查通过", serviceName);
        } else {
            log.warn("微服务[{}]兼容性检查发现问题", serviceName);
        }

        return result;
    }

    /**
     * 检查配置文件可用性
     */
    private void checkConfigurationAvailability(CompatibilityResult result, String serviceName) {
        Properties serviceConfig = configManager.getServiceConfig(serviceName);

        if (serviceConfig == null || serviceConfig.isEmpty()) {
            result.addError("未找到微服务配置文件或配置为空");
            return;
        }

        // 检查必要的配置项
        String[] requiredConfigs = {
                "spring.application.name",
                "server.port"
        };

        for (String config : requiredConfigs) {
            if (serviceConfig.getProperty(config) == null) {
                result.addWarning("缺少推荐配置项: " + config);
            }
        }

        result.getMetadata().put("configCount", serviceConfig.size());
    }

    /**
     * 检查端口冲突
     */
    private void checkPortConflicts(CompatibilityResult result, String serviceName) {
        Properties serviceConfig = configManager.getServiceConfig(serviceName);
        if (serviceConfig == null)
            return;

        String servicePort = serviceConfig.getProperty("server.port");
        if (servicePort != null) {
            // 检查是否有其他服务使用相同端口
            for (String otherService : compatibilityResults.keySet()) {
                if (!otherService.equals(serviceName)) {
                    Properties otherConfig = configManager.getServiceConfig(otherService);
                    if (otherConfig != null) {
                        String otherPort = otherConfig.getProperty("server.port");
                        if (servicePort.equals(otherPort)) {
                            result.addWarning("端口冲突：与服务[" + otherService + "]使用相同端口[" + servicePort + "]");
                        }
                    }
                }
            }

            result.getMetadata().put("originalPort", servicePort);
        }
    }

    /**
     * 检查Bean冲突风险
     */
    private void checkBeanConflicts(CompatibilityResult result, String serviceName) {
        try {
            // 获取当前应用上下文中的Bean定义
            String[] beanNames = applicationContext.getBeanDefinitionNames();

            // 检查常见的冲突Bean模式
            String[] conflictPatterns = {
                    "dataSource", "sqlSessionFactory", "transactionManager",
                    "redisTemplate", "cacheManager", "webMvcConfigurer"
            };

            Set<String> suspiciousBeans = new HashSet<>();
            for (String beanName : beanNames) {
                for (String pattern : conflictPatterns) {
                    if (beanName.toLowerCase().contains(pattern.toLowerCase())) {
                        suspiciousBeans.add(beanName);
                    }
                }
            }

            if (suspiciousBeans.size() > 10) {
                result.addWarning("检测到大量可能冲突的Bean定义，请注意Bean命名规范");
            }

            result.getMetadata().put("suspiciousBeanCount", suspiciousBeans.size());
            result.getMetadata().put("totalBeanCount", beanNames.length);

        } catch (Exception e) {
            result.addWarning("Bean冲突检查时出错: " + e.getMessage());
        }
    }

    /**
     * 检查数据源配置
     */
    private void checkDataSourceConfiguration(CompatibilityResult result, String serviceName) {
        Properties serviceConfig = configManager.getServiceConfig(serviceName);
        if (serviceConfig == null)
            return;

        // 检查数据源配置
        String datasourceUrl = serviceConfig.getProperty("spring.datasource.url");
        String datasourceDriver = serviceConfig.getProperty("spring.datasource.driver-class-name");

        if (datasourceUrl != null) {
            result.getMetadata().put("hasDatasource", true);
            result.getMetadata().put("datasourceUrl", maskSensitiveInfo(datasourceUrl));

            // 检查数据库类型兼容性
            if (datasourceUrl.contains("mysql")) {
                result.getMetadata().put("databaseType", "MySQL");
            } else if (datasourceUrl.contains("oracle")) {
                result.getMetadata().put("databaseType", "Oracle");
                result.addWarning("使用Oracle数据库，请确保驱动依赖正确配置");
            } else if (datasourceUrl.contains("postgresql")) {
                result.getMetadata().put("databaseType", "PostgreSQL");
            }

            // 检查连接池配置
            String maxActive = serviceConfig.getProperty("spring.datasource.druid.max-active");
            if (maxActive != null) {
                try {
                    int maxConn = Integer.parseInt(maxActive);
                    if (maxConn > 50) {
                        result.addWarning("数据库连接池配置较大(max-active=" + maxConn + ")，注意资源使用");
                    }
                } catch (NumberFormatException e) {
                    result.addWarning("数据库连接池配置格式错误");
                }
            }
        } else {
            result.getMetadata().put("hasDatasource", false);
        }
    }

    /**
     * 检查Spring Boot版本兼容性
     */
    private void checkSpringBootCompatibility(CompatibilityResult result, String serviceName) {
        try {
            // 检查Spring Boot版本
            String bootVersion = org.springframework.boot.SpringBootVersion.getVersion();
            result.getMetadata().put("springBootVersion", bootVersion);

            // 检查是否使用了过时的配置
            Properties serviceConfig = configManager.getServiceConfig(serviceName);
            if (serviceConfig != null) {
                // 检查过时的配置项
                String[] deprecatedConfigs = {
                        "security.basic.enabled", // Spring Boot 2.0+已移除
                        "management.security.enabled", // 已替换为management.endpoints
                        "server.context-path" // 已替换为server.servlet.context-path
                };

                for (String deprecatedConfig : deprecatedConfigs) {
                    if (serviceConfig.getProperty(deprecatedConfig) != null) {
                        result.addWarning("使用了过时的配置项: " + deprecatedConfig);
                    }
                }
            }

        } catch (Exception e) {
            result.addWarning("Spring Boot版本检查时出错: " + e.getMessage());
        }
    }

    /**
     * 检查依赖冲突风险
     */
    private void checkDependencyConflicts(CompatibilityResult result, String serviceName) {
        try {
            // 检查类路径中的关键依赖
            PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();

            // 检查是否存在多个版本的关键库
            String[] criticalLibraries = {
                    "spring-boot-*.jar",
                    "spring-core-*.jar",
                    "mysql-connector-*.jar",
                    "druid-*.jar",
                    "mybatis-*.jar"
            };

            for (String libPattern : criticalLibraries) {
                try {
                    Resource[] resources = resolver.getResources(
                            "classpath*:META-INF/maven/**/" + libPattern.replace("*.jar", "*/pom.properties"));
                    if (resources.length > 1) {
                        result.addWarning("检测到可能的依赖版本冲突: " + libPattern);
                    }
                } catch (IOException e) {
                    // 忽略查找异常
                }
            }

        } catch (Exception e) {
            result.addWarning("依赖冲突检查时出错: " + e.getMessage());
        }
    }

    /**
     * 检查资源使用情况
     */
    private void checkResourceUsage(CompatibilityResult result, String serviceName) {
        // 获取当前JVM内存使用情况
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();

        result.getMetadata().put("jvmUsedMemoryMB", usedMemory / 1024 / 1024);
        result.getMetadata().put("jvmMaxMemoryMB", maxMemory / 1024 / 1024);

        // 如果已使用内存超过最大内存的80%，给出警告
        if (usedMemory > maxMemory * 0.8) {
            result.addWarning("JVM内存使用率较高，建议调整堆内存配置");
        }

        // 检查是否配置了大量线程池
        Properties serviceConfig = configManager.getServiceConfig(serviceName);
        if (serviceConfig != null) {
            String taskExecutorConfig = serviceConfig.getProperty("spring.task.execution.pool.max-size");
            if (taskExecutorConfig != null) {
                try {
                    int maxThreads = Integer.parseInt(taskExecutorConfig);
                    if (maxThreads > 100) {
                        result.addWarning("线程池配置较大(max-size=" + maxThreads + ")，注意资源使用");
                    }
                } catch (NumberFormatException e) {
                    result.addWarning("线程池配置格式错误");
                }
            }
        }
    }

    /**
     * 生成兼容性报告
     */
    private void generateCompatibilityReport() {
        String separator = repeatChar("=", 80);
        log.info("\n" + separator);
        log.info("微服务兼容性检查报告");
        log.info(separator);

        int totalServices = compatibilityResults.size();
        int compatibleServices = 0;
        int servicesWithWarnings = 0;
        int incompatibleServices = 0;

        for (Map.Entry<String, CompatibilityResult> entry : compatibilityResults.entrySet()) {
            String serviceAlias = entry.getKey();
            CompatibilityResult result = entry.getValue();

            log.info("\n服务: {} ({})", serviceAlias, result.getServiceName());
            log.info("状态: {}", result.isCompatible() ? "✓ 兼容" : "✗ 不兼容");

            if (!result.getWarnings().isEmpty()) {
                log.info("警告:");
                for (String warning : result.getWarnings()) {
                    log.info("  - {}", warning);
                }
                servicesWithWarnings++;
            }

            if (!result.getErrors().isEmpty()) {
                log.info("错误:");
                for (String error : result.getErrors()) {
                    log.info("  - {}", error);
                }
                incompatibleServices++;
            }

            if (result.isCompatible()) {
                compatibleServices++;
            }

            // 输出元数据信息
            if (!result.getMetadata().isEmpty()) {
                log.info("详细信息:");
                for (Map.Entry<String, Object> metadata : result.getMetadata().entrySet()) {
                    log.info("  {}: {}", metadata.getKey(), metadata.getValue());
                }
            }
        }

        // 汇总报告
        log.info("\n" + separator);
        log.info("检查汇总:");
        log.info("总服务数: {}", totalServices);
        log.info("兼容服务: {}", compatibleServices);
        log.info("有警告的服务: {}", servicesWithWarnings);
        log.info("不兼容服务: {}", incompatibleServices);

        if (incompatibleServices > 0) {
            log.warn("发现{}个不兼容的服务，建议修复后再进行集成!", incompatibleServices);
        } else if (servicesWithWarnings > 0) {
            log.warn("发现{}个服务有警告，建议关注相关问题", servicesWithWarnings);
        } else {
            log.info("所有服务兼容性检查通过!");
        }

        log.info(separator);
    }

    /**
     * 屏蔽敏感信息
     */
    private String maskSensitiveInfo(String info) {
        if (info == null)
            return null;

        // 屏蔽数据库连接字符串中的密码
        return info.replaceAll("password=[^&;]*", "password=****")
                .replaceAll("pwd=[^&;]*", "pwd=****");
    }

    /**
     * 获取兼容性检查结果
     */
    public Map<String, CompatibilityResult> getCompatibilityResults() {
        return new HashMap<>(compatibilityResults);
    }

    /**
     * 获取指定服务的兼容性结果
     */
    public CompatibilityResult getServiceCompatibilityResult(String serviceAlias) {
        return compatibilityResults.get(serviceAlias);
    }

    /**
     * 重复字符串的辅助方法（兼容Java 8）
     */
    private String repeatChar(String str, int count) {
        StringBuilder sb = new StringBuilder(str.length() * count);
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
}