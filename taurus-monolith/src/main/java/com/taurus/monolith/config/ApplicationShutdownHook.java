package com.taurus.monolith.config;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * 应用关闭钩子
 * 确保应用关闭时注销所有注册的服务
 */
@Component
@Slf4j
public class ApplicationShutdownHook {

    @Autowired
    private MultiServiceNacosRegistrar registrar;

    /**
     * 注册JVM关闭钩子
     */
    @PostConstruct
    public void registerShutdownHook() {
        log.info("注册应用关闭钩子，确保应用关闭时注销所有微服务...");
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log.info("应用正在关闭，开始注销所有注册的微服务...");
            registrar.stop();
            log.info("所有微服务注销完成");
        }));
    }
}