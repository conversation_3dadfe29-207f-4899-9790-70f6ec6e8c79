package com.taurus.monolith.config;

import javax.sql.DataSource;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import com.gitee.fastmybatis.core.ext.SqlSessionFactoryBeanExt;

import lombok.extern.slf4j.Slf4j;

/**
 * FastMyBatis多数据源配置
 * 为单体化部署创建多个SqlSessionFactory，每个微服务使用独立的数据源和SqlSessionFactory
 * 
 * 解决单体化部署中FastMyBatis无法识别多数据源的问题
 */
@Configuration
@Slf4j
@ConditionalOnProperty(name = "taurus.monolith.enabled", havingValue = "true")
public class FastMyBatisMultiDataSourceConfig {

    @Autowired
    @Qualifier("formSysDataSource")
    private DataSource formSysDataSource;

    @Autowired
    @Qualifier("ossDataSource")
    private DataSource ossDataSource;

    @Autowired
    @Qualifier("commentDataSource")
    private DataSource commentDataSource;

    @Autowired
    @Qualifier("paymentDataSource")
    private DataSource paymentDataSource;

    /**
     * 表单系统SqlSessionFactory（主要的）
     */
    @Bean(name = "formSysSqlSessionFactory")
    @Primary
    public SqlSessionFactory formSysSqlSessionFactory() throws Exception {
        log.info("创建表单系统FastMyBatis SqlSessionFactory");

        SqlSessionFactoryBeanExt sqlSessionFactory = new SqlSessionFactoryBeanExt();
        sqlSessionFactory.setDataSource(formSysDataSource);

        // 设置FastMyBatis必需的basePackage属性
        sqlSessionFactory.setBasePackage("com.taurus.formSys.entity");

        // 启用Mapper XML自动生成功能（FastMyBatis会自动检测实体类并生成对应的Mapper XML）
        // 通过设置basePackage属性，FastMyBatis会自动扫描该包下的实体类并生成Mapper XML

        // 设置MyBatis配置文件 - 移除这一行，因为mybatisConfig.xml文件不存在
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();

        // 设置实体类包路径
        sqlSessionFactory.setTypeAliasesPackage("com.taurus.formSys.entity,com.taurus.entity");

        // 设置Mapper XML文件路径 - 包含动态生成的和静态的
        sqlSessionFactory.setMapperLocations(resolver.getResources("classpath*:mapper/**/*.xml"));

        log.info("表单系统FastMyBatis SqlSessionFactory配置完成");
        return sqlSessionFactory.getObject();
    }

    /**
     * 表单系统SqlSessionTemplate
     */
    @Bean(name = "formSysSqlSessionTemplate")
    @Primary
    public SqlSessionTemplate formSysSqlSessionTemplate(
            @Qualifier("formSysSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        log.info("创建表单系统SqlSessionTemplate");
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    /**
     * OSS服务SqlSessionFactory
     */
    @Bean(name = "ossSqlSessionFactory")
    public SqlSessionFactory ossSqlSessionFactory() throws Exception {
        log.info("创建OSS服务FastMyBatis SqlSessionFactory");

        SqlSessionFactoryBeanExt sqlSessionFactory = new SqlSessionFactoryBeanExt();
        sqlSessionFactory.setDataSource(ossDataSource);

        // 设置FastMyBatis必需的basePackage属性
        sqlSessionFactory.setBasePackage("com.taurus.oss.entity");

        // 启用Mapper XML自动生成功能（FastMyBatis会自动检测实体类并生成对应的Mapper XML）
        // 通过设置basePackage属性，FastMyBatis会自动扫描该包下的实体类并生成Mapper XML

        // 设置MyBatis配置文件 - 移除这一行，因为mybatisConfig.xml文件不存在
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();

        // 设置实体类包路径
        sqlSessionFactory.setTypeAliasesPackage("com.taurus.oss.entity,com.taurus.entity");

        // 设置Mapper XML文件路径
        sqlSessionFactory.setMapperLocations(resolver.getResources("classpath*:mapper/**/*.xml"));

        log.info("OSS服务FastMyBatis SqlSessionFactory配置完成");
        return sqlSessionFactory.getObject();
    }

    /**
     * OSS服务SqlSessionTemplate
     */
    @Bean(name = "ossSqlSessionTemplate")
    public SqlSessionTemplate ossSqlSessionTemplate(
            @Qualifier("ossSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        log.info("创建OSS服务SqlSessionTemplate");
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    /**
     * 评论服务SqlSessionFactory
     */
    @Bean(name = "commentSqlSessionFactory")
    public SqlSessionFactory commentSqlSessionFactory() throws Exception {
        log.info("创建评论服务FastMyBatis SqlSessionFactory");

        SqlSessionFactoryBeanExt sqlSessionFactory = new SqlSessionFactoryBeanExt();
        sqlSessionFactory.setDataSource(commentDataSource);

        // 设置FastMyBatis必需的basePackage属性
        sqlSessionFactory.setBasePackage("com.taurus.comment.entity");

        // 启用Mapper XML自动生成功能（FastMyBatis会自动检测实体类并生成对应的Mapper XML）
        // 通过设置basePackage属性，FastMyBatis会自动扫描该包下的实体类并生成Mapper XML

        // 设置MyBatis配置文件 - 移除这一行，因为mybatisConfig.xml文件不存在
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();

        // 设置实体类包路径
        sqlSessionFactory.setTypeAliasesPackage("com.taurus.comment.entity,com.taurus.entity");

        // 设置Mapper XML文件路径
        sqlSessionFactory.setMapperLocations(resolver.getResources("classpath*:mapper/**/*.xml"));

        log.info("评论服务FastMyBatis SqlSessionFactory配置完成");
        return sqlSessionFactory.getObject();
    }

    /**
     * 评论服务SqlSessionTemplate
     */
    @Bean(name = "commentSqlSessionTemplate")
    public SqlSessionTemplate commentSqlSessionTemplate(
            @Qualifier("commentSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        log.info("创建评论服务SqlSessionTemplate");
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    /**
     * 支付服务SqlSessionFactory
     */
    @Bean(name = "paymentSqlSessionFactory")
    public SqlSessionFactory paymentSqlSessionFactory() throws Exception {
        log.info("创建支付服务FastMyBatis SqlSessionFactory");

        SqlSessionFactoryBeanExt sqlSessionFactory = new SqlSessionFactoryBeanExt();
        sqlSessionFactory.setDataSource(paymentDataSource);

        // 设置FastMyBatis必需的basePackage属性
        sqlSessionFactory.setBasePackage("com.taurus.payment.entity");

        // 启用Mapper XML自动生成功能（FastMyBatis会自动检测实体类并生成对应的Mapper XML）
        // 通过设置basePackage属性，FastMyBatis会自动扫描该包下的实体类并生成Mapper XML

        // 设置MyBatis配置文件 - 移除这一行，因为mybatisConfig.xml文件不存在
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();

        // 设置实体类包路径
        sqlSessionFactory.setTypeAliasesPackage("com.taurus.payment.entity,com.taurus.entity");

        // 设置Mapper XML文件路径
        sqlSessionFactory.setMapperLocations(resolver.getResources("classpath*:mapper/**/*.xml"));

        log.info("支付服务FastMyBatis SqlSessionFactory配置完成");
        return sqlSessionFactory.getObject();
    }

    /**
     * 支付服务SqlSessionTemplate
     */
    @Bean(name = "paymentSqlSessionTemplate")
    public SqlSessionTemplate paymentSqlSessionTemplate(
            @Qualifier("paymentSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        log.info("创建支付服务SqlSessionTemplate");
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}