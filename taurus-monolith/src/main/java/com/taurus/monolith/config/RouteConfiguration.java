package com.taurus.monolith.config;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import lombok.extern.slf4j.Slf4j;

/**
 * 单体化应用路由配置
 * 负责将不同的URL路径路由到对应的微服务模块
 */
@Configuration
@Slf4j
public class RouteConfiguration implements WebMvcConfigurer {

    /**
     * 添加路由拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new ServiceRouteInterceptor())
                .addPathPatterns("/**");
    }

    /**
     * 服务路由拦截器
     * 用于记录请求路由到哪个微服务模块
     */
    @Slf4j
    public static class ServiceRouteInterceptor extends HandlerInterceptorAdapter {

        @Override
        public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
            String requestURI = request.getRequestURI();
            String targetService = determineTargetService(requestURI);

            if (targetService != null) {
                // 在请求属性中记录目标服务，便于日志和监控
                request.setAttribute("target.service", targetService);
                log.debug("请求[{}]路由到微服务[{}]", requestURI, targetService);
            }

            return true;
        }

        /**
         * 根据请求URI确定目标微服务
         * 这里展示路由规则的实现逻辑
         */
        private String determineTargetService(String requestURI) {
            // 根据URL路径前缀确定目标微服务（使用真实的Controller路径）
            if (requestURI.startsWith("/leaveWord") ||
                    requestURI.startsWith("/leaveWordPraised") ||
                    requestURI.startsWith("/leaveWordSummary")) {
                return "taurus-comment";
            } else if (requestURI.startsWith("/weChatPay") ||
                    requestURI.startsWith("/alipay") ||
                    requestURI.startsWith("/channel") ||
                    requestURI.startsWith("/weChatPayApp") ||
                    requestURI.startsWith("/payToIndividual")) {
                return "taurus-pay";
            } else if (requestURI.startsWith("/ossFile") ||
                    requestURI.startsWith("/ossObject") ||
                    requestURI.startsWith("/ossRespository")) {
                return "taurus-oss";
            } else if (requestURI.startsWith("/formDefinition") ||
                    requestURI.startsWith("/category") ||
                    requestURI.startsWith("/formFlowRecord") ||
                    requestURI.startsWith("/formFieldStructure") ||
                    requestURI.startsWith("/categoryForm")) {
                return "taurus-form-system";
            }

            return null; // 无法确定目标服务
        }
    }

    /**
     * 路由规则说明文档Bean
     * 用于在应用启动时输出路由规则
     */
    @Bean
    public RouteDocumentation routeDocumentation() {
        return new RouteDocumentation();
    }

    /**
     * 路由规则文档类
     */
    @Slf4j
    public static class RouteDocumentation {

        public RouteDocumentation() {
            printRouteRules();
        }

        private void printRouteRules() {
            String separator = repeat("=", 60);
            log.info("\n" + separator);
            log.info("单体化应用URL路由规则");
            log.info(separator);
            log.info("所有请求通过端口8100进入，根据URL路径路由到对应微服务模块：");
            log.info("");
            log.info("📄 评论服务 (taurus-comment):");
            log.info("   /leaveWord/**        -> 留言管理功能");
            log.info("   /leaveWordPraised/** -> 留言点赞功能");
            log.info("   /leaveWordSummary/** -> 留言统计功能");
            log.info("");
            log.info("💳 支付服务 (taurus-pay):");
            log.info("   /weChatPay/**        -> 微信支付功能");
            log.info("   /alipay/**           -> 支付宝支付功能");
            log.info("   /channel/**          -> 支付渠道管理");
            log.info("   /weChatPayApp/**     -> 微信App支付");
            log.info("");
            log.info("📁 对象存储服务 (taurus-oss):");
            log.info("   /ossFile/**          -> 文件管理功能");
            log.info("   /ossObject/**        -> 对象管理功能");
            log.info("   /ossRespository/**   -> 仓库管理功能");
            log.info("");
            log.info("📋 表单系统 (taurus-form-system):");
            log.info("   /formDefinition/**   -> 表单定义管理");
            log.info("   /category/**         -> 分类管理功能");
            log.info("   /formFlowRecord/**   -> 表单流程记录");
            log.info("");

            log.info("注意：");
            log.info("- 所有微服务运行在同一个JVM进程中");
            log.info("- 统一使用端口8100对外提供服务");
            log.info("- 通过URL路径区分不同的微服务功能");
            log.info("- 各微服务保持原有的Controller路径映射");
            log.info("- 无需额外的/api前缀，直接使用原始路径");
            log.info(separator);
        }

        /**
         * 重复字符串的辅助方法（兼容Java 8）
         */
        private String repeat(String str, int count) {
            StringBuilder sb = new StringBuilder(str.length() * count);
            for (int i = 0; i < count; i++) {
                sb.append(str);
            }
            return sb.toString();
        }
    }
}