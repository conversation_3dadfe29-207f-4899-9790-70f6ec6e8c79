package com.taurus.monolith.config;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * 简化的微服务配置管理器
 * 仅负责提供基本的配置查询API，避免复杂的配置文件加载逻辑
 */
@Slf4j
@Component
public class SimpleMicroserviceConfigManager {

    @Autowired
    private MonolithServiceManager serviceManager;

    @Autowired
    private Environment environment;

    // 存储各微服务的配置信息
    private Map<String, Properties> serviceConfigurations = new HashMap<>();

    /**
     * 初始化时创建基本的服务配置映射
     */
    @PostConstruct
    public void init() {
        log.info("初始化微服务配置管理器...");

        Map<String, String> serviceMappings = serviceManager.getServiceMappings();
        if (serviceMappings == null || serviceMappings.isEmpty()) {
            log.warn("未配置微服务映射");
            return;
        }

        // 为每个服务创建基本配置对象
        for (Map.Entry<String, String> entry : serviceMappings.entrySet()) {
            String serviceAlias = entry.getKey();
            String serviceName = entry.getValue();

            Properties serviceProps = createBasicServiceConfig(serviceName);
            serviceConfigurations.put(serviceName, serviceProps);
            log.info("已创建微服务[{}]的基本配置", serviceName);
        }

        log.info("微服务配置管理器初始化完成，管理{}个微服务", serviceConfigurations.size());
    }

    /**
     * 创建服务的基本配置
     * 从当前环境中提取相关配置，包括DynamicConfigLoader加载的配置
     */
    private Properties createBasicServiceConfig(String serviceName) {
        Properties properties = new Properties();

        // 设置基本的服务信息
        properties.setProperty("spring.application.name", serviceName);

        // 从环境中获取Nacos相关配置
        String nacosServerAddr = environment.getProperty("spring.cloud.nacos.discovery.server-addr", "127.0.0.1:8848");
        String nacosNamespace = environment.getProperty("spring.cloud.nacos.discovery.namespace", "");
        String nacosGroup = environment.getProperty("spring.cloud.nacos.discovery.group", "DEFAULT_GROUP");

        // 单体化服务的默认认证信息（从配置文件中获取）
        String defaultUsername = environment.getProperty("spring.cloud.nacos.discovery.username", "nacos");
        String defaultPassword = environment.getProperty("spring.cloud.nacos.discovery.password", "9945xqyg");

        // 尝试从DynamicConfigLoader加载的微服务特定配置中获取认证信息
        // 如果微服务有自己的认证配置，优先使用；否则使用单体化服务的默认配置
        String serviceSpecificUsername = environment.getProperty("microservice." + serviceName + ".nacos.username");
        String serviceSpecificPassword = environment.getProperty("microservice." + serviceName + ".nacos.password");

        String nacosUsername = serviceSpecificUsername != null ? serviceSpecificUsername : defaultUsername;
        String nacosPassword = serviceSpecificPassword != null ? serviceSpecificPassword : defaultPassword;

        properties.setProperty("spring.cloud.nacos.discovery.server-addr", nacosServerAddr);
        properties.setProperty("spring.cloud.nacos.discovery.username", nacosUsername);
        properties.setProperty("spring.cloud.nacos.discovery.password", nacosPassword);

        if (!nacosNamespace.isEmpty()) {
            properties.setProperty("spring.cloud.nacos.discovery.namespace", nacosNamespace);
        }
        properties.setProperty("spring.cloud.nacos.discovery.group", nacosGroup);

        // 设置默认端口（实际不会使用，仅用于兼容性）
        properties.setProperty("server.port", "8080");

        // 从环境中获取微信支付等业务配置（DynamicConfigLoader已经加载）
        String wxNotifyUrl = environment.getProperty("wx.notifyUrl");
        if (wxNotifyUrl != null) {
            properties.setProperty("wx.notifyUrl", wxNotifyUrl);
        }

        log.info("为微服务[{}]创建配置，Nacos服务器: {}, 用户名: {}", serviceName, nacosServerAddr, nacosUsername);

        return properties;
    }

    /**
     * 获取指定服务的配置
     */
    public Properties getServiceConfig(String serviceName) {
        return serviceConfigurations.get(serviceName);
    }

    /**
     * 获取指定服务的配置属性值
     */
    public String getServiceProperty(String serviceName, String key) {
        Properties props = getServiceConfig(serviceName);
        return props != null ? props.getProperty(key) : null;
    }

    /**
     * 获取指定服务的配置属性值，如果不存在则返回默认值
     */
    public String getServiceProperty(String serviceName, String key, String defaultValue) {
        String value = getServiceProperty(serviceName, key);
        return value != null ? value : defaultValue;
    }

    /**
     * 获取所有已加载的服务名称
     */
    public Set<String> getServiceNames() {
        return serviceConfigurations.keySet();
    }

    /**
     * 检查是否有配置
     */
    public boolean hasConfigurations() {
        return !serviceConfigurations.isEmpty();
    }
}