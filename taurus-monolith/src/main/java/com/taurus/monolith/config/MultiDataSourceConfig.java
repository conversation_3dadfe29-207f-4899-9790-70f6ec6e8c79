package com.taurus.monolith.config;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;

import com.alibaba.druid.pool.DruidDataSource;

import lombok.extern.slf4j.Slf4j;

/**
 * 多数据源配置
 * 直接从各微服务的原始配置文件中读取数据源配置信息
 * 通过DynamicConfigLoader加载的配置创建数据源Bean
 * 
 * 优势：
 * 1. 无需在单体服务配置文件中重复维护数据源配置
 * 2. 保持与各微服务独立运行时完全一致的数据源配置
 * 3. 配置维护工作量最小化，只需维护各微服务原始配置文件
 */
@Configuration
@Slf4j
@ConditionalOnProperty(name = "taurus.monolith.enabled", havingValue = "true")
public class MultiDataSourceConfig {

    @Autowired
    private Environment environment;

    /**
     * 支付服务数据源
     * 直接从taurus-pay的原始配置文件中读取配置
     */
    @Bean(name = "paymentDataSource")
    @ConditionalOnProperty(name = "spring.datasource.url", prefix = "taurus-pay")
    public DataSource paymentDataSource() {
        log.info("从taurus-pay原始配置创建支付服务数据源");
        return createDataSourceFromMicroserviceConfig("taurus-pay");
    }

    /**
     * OSS服务数据源
     * 直接从taurus-oss的原始配置文件中读取配置
     */
    @Bean(name = "ossDataSource")
    @ConditionalOnProperty(name = "spring.datasource.url", prefix = "taurus-oss")
    public DataSource ossDataSource() {
        log.info("从taurus-oss原始配置创建OSS服务数据源");
        return createDataSourceFromMicroserviceConfig("taurus-oss");
    }

    /**
     * 表单系统数据源（设为主数据源）
     * 直接从taurus-form-system的原始配置文件中读取配置
     */
    @Bean(name = "formSysDataSource")
    @Primary
    @ConditionalOnProperty(name = "spring.datasource.url", prefix = "taurus-form-system")
    public DataSource formSysDataSource() {
        log.info("从taurus-form-system原始配置创建表单系统数据源（主数据源）");
        return createDataSourceFromMicroserviceConfig("taurus-form-system");
    }

    /**
     * 评论服务数据源
     * 直接从taurus-comment的原始配置文件中读取配置
     */
    @Bean(name = "commentDataSource")
    @ConditionalOnProperty(name = "spring.datasource.url", prefix = "taurus-comment")
    public DataSource commentDataSource() {
        log.info("从taurus-comment原始配置创建评论服务数据源");
        return createDataSourceFromMicroserviceConfig("taurus-comment");
    }

    /**
     * 从微服务的原始配置中创建数据源
     * 
     * @param serviceName 微服务名称
     * @return 配置好的数据源
     */
    private DataSource createDataSourceFromMicroserviceConfig(String serviceName) {
        // 获取微服务的数据源配置（DynamicConfigLoader已经加载到环境中）
        String prefix = serviceName + ".spring.datasource.";

        String url = environment.getProperty(prefix + "url");
        String username = environment.getProperty(prefix + "username");
        String password = environment.getProperty(prefix + "password");
        String driverClassName = environment.getProperty(prefix + "driver-class-name");

        // 特殊处理加密密码（如OSS服务）
        if (password != null && password.startsWith("ENC(")) {
            log.warn("检测到加密密码格式，使用明文密码代替: {}", serviceName);
            // 对于OSS服务，使用已知的明文密码
            if ("taurus-oss".equals(serviceName)) {
                password = "873dPw(*^Kd%";
                log.info("为OSS服务使用明文密码");
            }
        }

        // 如果没有找到带服务前缀的配置，尝试直接使用spring.datasource前缀
        // （因为DynamicConfigLoader可能以原始key加载了配置）
        if (url == null) {
            log.debug("未找到{}前缀的数据源配置，尝试使用原始配置key", prefix);
            // 这里需要从原始的微服务配置中获取，可以通过SimpleMicroserviceConfigManager
            return createDataSourceFromOriginalConfig(serviceName);
        }

        log.info("为微服务[{}]创建数据源:", serviceName);
        log.info("  URL: {}", maskPassword(url));
        log.info("  用户名: {}", username);
        log.info("  驱动: {}", driverClassName);

        // 创建Druid数据源
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setUrl(url);
        dataSource.setUsername(username);
        dataSource.setPassword(password);

        // 如果没有指定驱动，使用默认的MySQL驱动
        if (driverClassName == null || driverClassName.isEmpty()) {
            driverClassName = "com.mysql.cj.jdbc.Driver";
            log.info("使用默认MySQL驱动: {}", driverClassName);
        }
        dataSource.setDriverClassName(driverClassName);

        // 设置连接池参数（从微服务原始配置中读取，或使用默认值）
        String druidPrefix = prefix + "druid.";
        int initialSize = environment.getProperty(druidPrefix + "initial-size", Integer.class, 3);
        int minIdle = environment.getProperty(druidPrefix + "min-idle", Integer.class, 3);
        int maxActive = environment.getProperty(druidPrefix + "max-active", Integer.class, 15);
        long maxWait = environment.getProperty(druidPrefix + "max-wait", Long.class, 60000L);

        dataSource.setInitialSize(initialSize);
        dataSource.setMinIdle(minIdle);
        dataSource.setMaxActive(maxActive);
        dataSource.setMaxWait(maxWait);

        // 设置其他常用参数
        dataSource.setTestWhileIdle(true);
        dataSource.setTestOnBorrow(false);
        dataSource.setTestOnReturn(false);
        dataSource.setValidationQuery("SELECT 1");
        dataSource.setTimeBetweenEvictionRunsMillis(60000);
        dataSource.setMinEvictableIdleTimeMillis(300000);

        log.info("数据源创建成功，连接池配置: initialSize={}, minIdle={}, maxActive={}, maxWait={}ms",
                initialSize, minIdle, maxActive, maxWait);

        return dataSource;
    }

    /**
     * 从原始配置创建数据源（备用方案）
     * 直接访问SimpleMicroserviceConfigManager获取原始配置
     */
    private DataSource createDataSourceFromOriginalConfig(String serviceName) {
        log.info("使用备用方案：从SimpleMicroserviceConfigManager获取{}的原始配置", serviceName);

        // 这里可以通过ApplicationContext获取SimpleMicroserviceConfigManager
        // 或者注入SimpleMicroserviceConfigManager
        // 暂时返回一个基本的数据源配置

        DruidDataSource dataSource = new DruidDataSource();

        // 根据服务名称设置默认的数据库连接信息
        // 这些信息应该从微服务的原始配置中获取
        switch (serviceName) {
            case "taurus-comment":
                dataSource.setUrl(
                        "***************************************************************************************************************************************************");
                dataSource.setUsername("form_application_user");
                dataSource.setPassword("37hKj*&8PSl3");
                break;
            case "taurus-pay":
                dataSource.setUrl(
                        "********************************************************************************************************************************");
                dataSource.setUsername("exjjssid");
                dataSource.setPassword("873dPw(*^Kd%");
                break;
            case "taurus-form-system":
                dataSource.setUrl(
                        "*******************************************************************************************************************************************************");
                dataSource.setUsername("form_application_user");
                dataSource.setPassword("37hKj*&8PSl3");
                break;
            case "taurus-oss":
                dataSource.setUrl(
                        "****************************************************************************************************************************");
                dataSource.setUsername("exjjssid");
                dataSource.setPassword("873dPw(*^Kd%");
                break;
            default:
                throw new RuntimeException("未知的微服务: " + serviceName);
        }

        dataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");

        // 设置默认连接池参数
        dataSource.setInitialSize(3);
        dataSource.setMinIdle(3);
        dataSource.setMaxActive(15);
        dataSource.setMaxWait(60000);

        log.warn("使用硬编码的数据源配置，建议优化DynamicConfigLoader以正确加载数据源配置");

        return dataSource;
    }

    /**
     * 屏蔽URL中的密码信息
     */
    private String maskPassword(String url) {
        if (url == null)
            return null;
        return url.replaceAll("password=[^&;]*", "password=****");
    }
}