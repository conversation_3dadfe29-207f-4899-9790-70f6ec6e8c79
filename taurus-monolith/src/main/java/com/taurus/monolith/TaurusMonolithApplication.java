package com.taurus.monolith;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScan.Filter;
import org.springframework.context.annotation.FilterType;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;

import lombok.extern.slf4j.Slf4j;

/**
 * 微服务聚合单体化部署应用启动类
 * 集成 taurus-comment、taurus-pay、taurus-form-system、taurus-oss 四个微服务
 */
@Slf4j
@SpringBootApplication(exclude = {
                DataSourceAutoConfiguration.class,
                DruidDataSourceAutoConfigure.class,
                com.gitee.fastmybatis.spring.boot.autoconfigure.MybatisAutoConfiguration.class
})
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {
                "com.taurus.comment",
                "com.taurus.payment",
                "com.taurus.formSys",
                "com.taurus.oss",
                "com.taurus.monolith"
})
@ComponentScan(basePackages = {
                "com.taurus.comment", // 评论服务
                "com.taurus.payment", // 支付服务
                "com.taurus.formSys", // 表单系统
                "com.taurus.oss", // 对象存储
                "com.taurus.monolith" // 单体化配置
}, excludeFilters = {
                // 排除已知冲突的配置类
                @Filter(type = FilterType.REGEX, pattern = ".*WebFilter"),
                @Filter(type = FilterType.REGEX, pattern = ".*CorsConfig"),
                // 排除Redis配置类，避免RedisTemplate冲突
                @Filter(type = FilterType.REGEX, pattern = ".*RedisConfig"),
                // 排除不需要的微服务组件（更精确的排除）
                @Filter(type = FilterType.REGEX, pattern = "com\\.taurus\\.llm\\..*"),
                @Filter(type = FilterType.REGEX, pattern = "com\\.taurus\\.sse\\..*"),
                @Filter(type = FilterType.REGEX, pattern = "com\\.taurus\\.exam\\..*"),
                @Filter(type = FilterType.REGEX, pattern = "com\\.taurus\\.qkk\\..*")
})
// Mapper扫描由专门的MapperScanConfig配置类处理
// @MapperScan 注解已移除，使用MapperScannerConfigurer替代
public class TaurusMonolithApplication extends SpringBootServletInitializer {

        /**
         * 主应用入口
         */
        public static void main(String[] args) {
                log.info("准备启动Taurus微服务单体化应用...");
                log.info("集成的微服务: taurus-comment, taurus-pay, taurus-form-system, taurus-oss");

                // 设置系统属性
                System.setProperty("taurus.monolith.enabled", "true");
                System.setProperty("spring.main.allow-bean-definition-overriding", "true");
                System.setProperty("spring.main.allow-circular-references", "true");
                System.setProperty("spring.main.lazy-initialization", "false");
                System.setProperty("spring.main.banner-mode", "off");

                // 创建应用实例
                SpringApplication application = new SpringApplication(TaurusMonolithApplication.class);

                // 设置环境配置文件，确保monolith和dev配置都生效
                application.setAdditionalProfiles("monolith", "dev");

                // 启动应用（移除复杂的配置管理器，使用简化版本）
                application.run(args);

                log.info("Taurus微服务单体化应用启动成功!");
                log.info("访问地址: http://localhost:8100");
                log.info("监控地址: http://localhost:8101/actuator");
                log.info("服务路由:");
                log.info("  评论服务:");
                log.info("    - 留言管理: http://localhost:8100/leaveWord/**");
                log.info("    - 留言点赞: http://localhost:8100/leaveWordPraised/**");
                log.info("    - 留言统计: http://localhost:8100/leaveWordSummary/**");
                log.info("  支付服务:");
                log.info("    - 微信支付: http://localhost:8100/weChatPay/**");
                log.info("    - 支付宝支付: http://localhost:8100/alipay/**");
                log.info("    - 支付渠道: http://localhost:8100/channel/**");
                log.info("  表单系统:");
                log.info("    - 表单定义: http://localhost:8100/formDefinition/**");
                log.info("    - 分类管理: http://localhost:8100/category/**");
                log.info("    - 流程记录: http://localhost:8100/formFlowRecord/**");
                log.info("  对象存储:");
                log.info("    - 文件管理: http://localhost:8100/ossFile/**");
                log.info("    - 对象管理: http://localhost:8100/ossObject/**");
                log.info("    - 仓库管理: http://localhost:8100/ossRespository/**");
        }
}