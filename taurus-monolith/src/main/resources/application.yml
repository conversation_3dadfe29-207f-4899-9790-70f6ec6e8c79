server:
  port: 8100
  undertow:
    # IO线程数，默认为CPU核心数，最小为2
    io-threads: 4
    # 工作线程数，默认为IO线程数*8
    worker-threads: 32
    # 缓冲区大小，越小内存占用越小
    buffer-size: 1024
    # 是否分配直接内存
    direct-buffers: true
  servlet:
    context-path: /

spring:
  application:
    name: taurus-monolith
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        service: taurus-monolith
        # 使用默认的public命名空间
        namespace: ""
        group: DEFAULT_GROUP
        # 单体化服务使用的Nacos认证信息
        username: nacos
        password: 9945xqyg
        # 注册为单体应用，由MultiServiceNacosRegistrar额外注册各微服务
        register-enabled: true

  profiles:
    active: dev # 默认激活dev环境
  # 设置Bean处理相关属性
  main:
    allow-bean-definition-overriding: true # 允许Bean定义覆盖
    allow-circular-references: true # 允许循环引用
    lazy-initialization: false # 禁用延迟初始化，确保所有Bean立即加载

# 应用监控配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always

# 日志配置
logging:
  level:
    root: info
    com.taurus: debug
    # Nacos日志级别
    com.alibaba.nacos.client: info

# 单体化部署配置
taurus:
  monolith:
    # 是否启用单体化部署
    enabled: true
    # 启用的服务列表（仅包含需要的4个服务）
    enabled-services:
      - comment
      - pay
      - form-system
      - oss
    # 模块路径映射
    service-mappings:
      comment: taurus-comment
      pay: taurus-pay
      form-system: taurus-form-system
      oss: taurus-oss
    # 多服务注册配置
    multi-service-registration:
      enabled: true # 启用多服务注册
      services:
        - name: taurus-comment
          context-path: /comment
        - name: taurus-pay
          context-path: /pay
        - name: taurus-form-system
          context-path: /form
        - name: taurus-oss
          context-path: /oss
