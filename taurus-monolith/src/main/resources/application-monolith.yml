# 单体化部署专用配置
# 用于覆盖默认配置，确保单体化部署正常工作

spring:
  # Bean处理配置 - 单体化部署必需
  main:
    allow-bean-definition-overriding: true # 允许Bean覆盖
    allow-circular-references: true # 允许循环引用
    lazy-initialization: false # 禁用延迟初始化

# 单体化应用特有日志配置
logging:
  level:
    # 单体化配置管理器日志
    com.taurus.monolith: debug
    # Bean冲突检测日志
    org.springframework.beans.factory.support.DefaultListableBeanFactory: warn
  pattern:
    # 添加服务标识到日志中
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level [%X{target.service:-monolith}] %logger{36} - %msg%n"

# 单体化模式标识
taurus:
  monolith:
    enabled: true
    mode: "integrated" # 集成模式
    version: "1.0.0"
