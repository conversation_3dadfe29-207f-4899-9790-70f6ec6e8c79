# 开发环境单体化应用配置
# 专门针对 taurus-comment、taurus-pay、taurus-form-system、taurus-oss 四个微服务

server:
  port: 8100 # 开发环境端口

spring:
  # Bean处理配置，单体化应用特有
  main:
    # 允许Bean覆盖，解决多个微服务中同名Bean的冲突问题
    allow-bean-definition-overriding: true
    # 允许循环引用，解决某些微服务间的依赖问题
    allow-circular-references: true
    # 禁用延迟初始化，确保所有Bean在启动时初始化完成
    lazy-initialization: false

  # Nacos注册中心配置
  cloud:
    nacos:
      discovery:
        service: taurus-monolith-dev
        # 使用默认的public命名空间（空字符串）
        namespace: ""
        # 注册为单体应用，由MultiServiceNacosRegistrar额外注册各微服务
        register-enabled: true
        # 开发环境分组
        group: DEFAULT_GROUP
        # 单体化服务使用的Nacos认证信息
        server-addr: 127.0.0.1:8848
        username: nacos
        password: 9945xqyg

  # Redis配置（从各微服务的原配置文件中复制）
  # 统一使用表单系统的Redis配置（database=0）
  redis:
    host: **************
    port: 6379
    password: jfksio&^%^2fjlsfdh217^%02
    database: 0 # 表单系统和OSS使用database=0，评论服务使用database=1
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

# ==========================================
# 各微服务业务配置（由DynamicConfigLoader自动从各微服务配置文件加载）
# ==========================================

# 注意：wx.notifyUrl等业务配置将自动从taurus-pay/application-test.properties中加载
# 数据源配置也将自动从各微服务的原始配置文件中加载，无需在此处重复配置

# 开发环境日志配置
logging:
  level:
    root: info
    com.taurus: debug
    # 单体化配置管理器日志
    com.taurus.monolith.config: debug
    # Nacos客户端日志
    com.alibaba.nacos.client: info
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level [%X{target.service:-monolith}] %logger{36} - %msg%n"

# 单体化部署特有配置
taurus:
  monolith:
    # 启用的服务列表（仅包含需要的4个服务）
    enabled-services:
      - comment
      - pay
      - form-system
      - oss

    # 模块路径映射
    service-mappings:
      comment: taurus-comment
      pay: taurus-pay
      form-system: taurus-form-system
      oss: taurus-oss

    # 多服务注册配置
    multi-service-registration:
      enabled: true # 启用多服务注册，使用各自的认证信息
      services:
        - name: taurus-comment
          context-path: /comment
        - name: taurus-pay
          context-path: /pay
        - name: taurus-formSystem
          context-path: /form
        - name: taurus-oss
          context-path: /oss

# 开发环境监控配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
  server:
    port: 8101 # 管理端口，避免与主服务端口冲突

# 开发环境特定配置
debug: false # 开发时可以设置为true以获得更详细的调试信息
