<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
	<artifactId>taurus-monolith</artifactId>
	<version>1.0.0</version>
	<name>taurus-monolith</name>
	<description>单体化部署微服务聚合模块</description>
	<packaging>jar</packaging>

	<parent>
		<groupId>com.taurus</groupId>
		<artifactId>taurus-cloud</artifactId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>

	<properties>
		<java.version>1.8</java.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
	</properties>

	<dependencies>
		
		<!-- 表单系统服务 -->
		<dependency>
			<groupId>com.taurus.form</groupId>
			<artifactId>taurus-form-system</artifactId>
			<version>0.1.0</version>
		</dependency>
		
		<!-- 对象存储服务 -->
		<dependency>
			<groupId>com.taurus.oss</groupId>
			<artifactId>taurus-oss</artifactId>
			<version>0.1.0</version>
		</dependency>
		
		<!-- 支付服务 -->
		<dependency>
			<groupId>com.taurus.pay</groupId>
			<artifactId>taurus-pay</artifactId>
			<version>0.1.0</version>
		</dependency>
		
		<!-- 评论服务 -->
		<dependency>
			<groupId>com.taurus.comment</groupId>
			<artifactId>taurus-comment</artifactId>
			<version>0.1.0</version>
		</dependency>
		
		<!-- FastMyBatis - 单体化环境必需 -->
		<dependency>
			<groupId>net.oschina.durcframework</groupId>
			<artifactId>fastmybatis-spring-boot-starter</artifactId>
			<version>1.8.4</version>
		</dependency>
		
		<!-- MyBatis Spring Boot Starter -->
		<dependency>
			<groupId>org.mybatis.spring.boot</groupId>
			<artifactId>mybatis-spring-boot-starter</artifactId>
			<version>1.3.2</version>
		</dependency>
		
		<!-- Druid数据源 -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
			<version>1.1.17</version>
		</dependency>
		
		<!-- MySQL驱动 -->
		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
			<scope>runtime</scope>
		</dependency>
		
		<!-- Web支持 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-tomcat</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		
		<!-- 使用Undertow作为Web容器 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-undertow</artifactId>
		</dependency>
		
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		
		<!-- Nacos注册中心 -->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
		</dependency>
		
		<!-- 开发工具 -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>

		<!-- log4j 1.2.17 -->
		<dependency>
			<groupId>log4j</groupId>
			<artifactId>log4j</artifactId>
			<version>1.2.17</version>
		</dependency>
		<dependency>
			<groupId>net.oschina.durcframework</groupId>
			<artifactId>fastmybatis-spring-boot-starter</artifactId>
			<version>1.8.4</version>
		</dependency>
		
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<scope>runtime</scope>
			<optional>true</optional>
		</dependency>
	</dependencies>

	<build>
		<finalName>taurus-monolith</finalName>
		<resources>
			<!-- 默认资源目录 -->
			<resource>
				<directory>src/main/resources</directory>
			</resource>
			<!-- 复制各微服务的配置文件到classpath -->
			<resource>
				<directory>../taurus-form-system/src/main/resources</directory>
				<targetPath>microservices/taurus-form-system</targetPath>
				<includes>
					<include>application*.properties</include>
					<include>application*.yml</include>
				</includes>
			</resource>
			<resource>
				<directory>../taurus-pay/src/main/resources</directory>
				<targetPath>microservices/taurus-pay</targetPath>
				<includes>
					<include>application*.properties</include>
					<include>application*.yml</include>
				</includes>
			</resource>
			<resource>
				<directory>../taurus-comment/src/main/resources</directory>
				<targetPath>microservices/taurus-comment</targetPath>
				<includes>
					<include>application*.properties</include>
					<include>application*.yml</include>
				</includes>
			</resource>
			<resource>
				<directory>../taurus-oss/src/main/resources</directory>
				<targetPath>microservices/taurus-oss</targetPath>
				<includes>
					<include>application*.properties</include>
					<include>application*.yml</include>
				</includes>
			</resource>
		</resources>
		<plugins>
			<!-- 资源复制插件 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>3.2.0</version>
				<configuration>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<mainClass>com.taurus.monolith.TaurusMonolithApplication</mainClass>
					<layout>JAR</layout>
					<excludes>
						<exclude>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</exclude>
					</excludes>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project> 