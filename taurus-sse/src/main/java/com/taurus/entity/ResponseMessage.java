package com.taurus.entity;

import java.lang.reflect.Field;

public class ResponseMessage {
	
	public final static String RpcException="远程服务调用超时，请稍后重试。";
	public final static String UnknownHostException="网络无法连接，请稍后重试。";
	public final static String IOException="远程服务IO错误，请稍后重试。";
	public final static String HttpRequestMethodNotSupported="不支持的方法调用";
	public final static String NumberFormatException="参数缺失或格式错误";
	public final static String JSONException="JSON解析错误";
	public final static String NullPointerException="空指针错误";
	public final static String ServerUnknowException="服务器未知错误";
	public final static String NO_DATA="无记录";
	public final static String NOT_EXISTED="不存在";
	
	//上传文件
	public final static String NO_FILE="上传的文件不能为空";
	public final static String FILE_NOT_PERMITTED="文件不被支持";
	public final static String NO_MULTIPARTFILE="未选择文件上传";
	public final static String FILE_PARSE_ERROR="文件解析错误";
	public final static String NOT_EXCEL_FILE="不是excel文件";
	public final static String NOT_WORD_FILE="不是word文件";
	public final static String NO_IMAGE_FILE="没有图片文件";
	public final static String NOT_EXCEL_OR_WORD_FILE="不是excel或word文件";
	public final static String EXCEED_MAX_FILE_SIZE ="您的文件大小超过限制";
	public final static String EXCEED_MAX_CONTENT_LENGTH="内容字符数超过限制";
    
	
	public final static String CONTENT_EMPTY="内容为空";
	public final static String PARAMETERS_ERROR ="参数不合法或错误";
	public final static String DUPLICATED = "已存在";
	

	public final static String NO_USER_SCAN ="用户未扫描登陆二维码";
	
	//JWT
	public final static String NO_TOKEN = "无TOKEN";
	public final static String INVALID_TOKEN ="无效的token";
	public final static String OVERDUE_TOKEN ="过期的token";
	public final static String NO_TOKEN_USER_ID="未设置token_user_id";
	
	
	//注册qkk账户
	public final static String HAVE_SUBMIT="注册请求已提交，请稍后！";
	public final static String SUCESS = "成功";
	public final static String NO_USER_ID="无用户Id";
	public final static String NO_EXAMINATION_ID="无考试Id";
	public final static String NO_COMPANY_ID="无公司Id";
	public final static String INVALID_COMPANY_ID="无效的公司id";
	public final static String REPEATED_COMPANY_NAME="公司名已存在";
	public final static String YOU_HAVE_REGISTED="您已经注册过企业账号";
	
	public final static String NO_PDDS_TIMES="未购买成绩详情报表服务，请联系客服";
	
	public final static String IO_EXCEPTION="发生IO流错误";
	
	
	
	//用户独立运营产品
	public final static String NOT_VALID_PRODUCT_CODE="不是有效的产品代码";
	public final static String SUBJECT_NAME_IS_EMPTY="空的栏目名称，请补充";
	
	
	
	//转交考试
	public final static String TRANSMIT_OVER="已经转交过了";//
	public final static String TRANSMIT_SELF="这是您自己的考试";//
	
	//短信验证码
	public final static String OVERDUE="过期";//
	public final static String DISMATCH="不匹配";//
	
	
	public final static String NO_OPENID="未获取到openid";//
	
	//支付
	public final static String WRONG_PAYMENT_WAY="无效的支付方式";
	
	//管理员权限移交
	public final static String NOT_COMPANY_USER="不是公司注册用户";
	
	
	//上传文件到aliyun
	public final static String OBJECT_NAME_IS_NULL="objectName为空";
	public final static String UPLOAD_FAIL="上传失败";
	
	//超出允许考试次数限制
	public final static String EXCEED_TIMES_LIMIT="超出允许考试次数";
	
	//K站
	//已注册过K站
	public final static String REGISTED="已经注册过K站";
	public final static String DUPICATED_NAME="K站名重复";
	
	//接口访问
	public final static String CANNOT_CONNECT_PAY_CENTER="无法连接支付中心";
	
	//K币
	public final static String NOT_ENOUGH_K_CURRENCY="K币不足";
	public final static String DUPICATED_PHONE="手机号重复";
	public final static String END_VALID_TIME_FRESHED="有效期已更新";
	
	//公司自定义注册表单
	public final static String NO_COMPANY_FORM="未创建自定义注册表单";
	
	
	//游戏
	public final static String NO_GAME_ID="游戏不存在";
	public final static String USER_FULL="游戏满员";
	public final static String GAMES_PLAYING="游戏进行中";
	
	//积分
	public final static String NOT_ENOUGH_POINT="积分不足";
	
	
	
	//用户相关
	public final static String NOT_REGISTED="未注册";
	
	
	//权限
	public final static String NO_PERMISSION="无权限";
	
	public final static String SCENE_OVER_LENGTH = "scene超长";
	
	
	//注销单位账号
	public final static String COMPANY_NOT_EXISTED = "不存在此单位账号";
	public final static String WRONG_ADMIN = "您不是账户创建者，无法销户";
	public final static String LEFT_EXAMNEE = "您未删除所有学员账号，无法销户，请先删除";
	public final static String LEFT_QUESTION = "您未删除所有考题，无法销户，请先删除";
	public final static String LEFT_OTHERS = "您未删除所有考试和练习，无法销户，请先删除";
		
	// 通过反射获取属性值
    public static String getValueByFieldName(String fieldName) {
        try {
            Field fieldTag = ResponseMessage.class.getDeclaredField(fieldName);
            return (String) fieldTag.get(ResponseMessage.class);
        } catch (Exception ex) {
            return null;
        }
    }
}
