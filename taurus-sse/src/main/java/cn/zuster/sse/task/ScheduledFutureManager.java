package cn.zuster.sse.task;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 心跳管理器
 */
public class ScheduledFutureManager {

    private static final Logger logger = LoggerFactory.getLogger(ScheduledFutureManager.class);

    private static Map<String, ScheduledFuture<?>> SESSION = new ConcurrentHashMap<>();

    /**
     * 心跳间隔时间（秒）
     * 设置较短的心跳间隔，以确保连接不会因超时而关闭
     */
    private static final int HEARTBEAT_INTERVAL = 5;

    /**
     * 首次心跳延迟（秒）
     */
    private static final int INITIAL_DELAY = 2;

    /**
     * 发送心跳线程池
     */
    private static ScheduledExecutorService heartbeatExecutors = Executors.newScheduledThreadPool(
            Runtime.getRuntime().availableProcessors(),
            new ThreadFactory() {
                @Override
                public Thread newThread(Runnable r) {
                    Thread t = Executors.defaultThreadFactory().newThread(r);
                    t.setDaemon(true);
                    t.setName("sse-heartbeat-" + t.getId());
                    return t;
                }
            });

    /**
     * 判断ScheduledFuture是否存在
     *
     * @param id 客户端ID
     * @return true 如果存在, false 如果不存在
     */
    public static boolean exist(String id) {
        return SESSION.get(id) != null;
    }

    /**
     * 增加ScheduledFuture
     *
     * @param id 客户端ID
     */
    public static void add(String id) {
        final ScheduledFuture<?> oldFuture = SESSION.get(id);

        if (oldFuture != null) {
            logger.info("检测到重复心跳任务, id:{}, 正在清理旧任务", id);
            del(id);
        }

        try {
            final ScheduledFuture<?> future = heartbeatExecutors.scheduleAtFixedRate(
                    new HeartBeatTask(id), INITIAL_DELAY, HEARTBEAT_INTERVAL, TimeUnit.SECONDS);
            SESSION.put(id, future);
            logger.info("成功为客户端 {} 创建心跳任务, 心跳间隔: {}秒", id, HEARTBEAT_INTERVAL);
        } catch (Exception e) {
            logger.error("创建心跳任务失败, 客户端ID: {}, 错误信息: {}", id, e.getMessage());
        }
    }

    /**
     * 删除ScheduledFuture
     *
     * @param id 客户端ID
     * @return
     */
    public static boolean del(String id) {
        final ScheduledFuture<?> future = SESSION.remove(id);
        if (future != null) {
            try {
                future.cancel(true);
                logger.info("成功删除客户端 {} 的心跳任务", id);
                return true;
            } catch (Exception e) {
                logger.error("删除心跳任务失败, 客户端ID: {}, 错误信息: {}", id, e.getMessage());
            }
        } else {
            logger.warn("尝试删除不存在的心跳任务, 客户端ID: {}", id);
        }
        return false;
    }

    /**
     * 关闭心跳线程池
     */
    public static void shutdown() {
        try {
            // 先尝试优雅关闭
            heartbeatExecutors.shutdown();
            if (!heartbeatExecutors.awaitTermination(5, TimeUnit.SECONDS)) {
                // 强制关闭
                heartbeatExecutors.shutdownNow();
            }
            logger.info("心跳线程池已关闭");
        } catch (InterruptedException e) {
            // 恢复中断状态
            Thread.currentThread().interrupt();
            logger.error("关闭心跳线程池时被中断", e);
        }
    }
}
