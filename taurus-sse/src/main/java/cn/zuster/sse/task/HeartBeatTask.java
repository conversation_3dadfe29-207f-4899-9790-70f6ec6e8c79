package cn.zuster.sse.task;

import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.taurus.entity.ResponseObject;

import cn.zuster.sse.session.SseSession;

/**
 * 心跳任务
 *
 * <AUTHOR>
 * @date 2021/1/5
 */
public class HeartBeatTask implements Runnable {
    private static final Logger logger = LoggerFactory.getLogger(HeartBeatTask.class);
    private final String clientId;
    private int failCount = 0;
    private static final int MAX_FAIL_COUNT = 3; // 最大失败次数

    // 上次心跳时间
    private long lastHeartbeatTime;

    public HeartBeatTask(String clientId) {
        // 这里可以按照业务传入需要的数据
        this.clientId = clientId;
        this.lastHeartbeatTime = System.currentTimeMillis();
    }

    @Override
    public void run() {
        try {
            // 检查会话是否存在
            if (!SseSession.exist(clientId)) {
                logger.warn("心跳任务检测到会话不存在，客户端ID: {}, 正在取消心跳任务", clientId);
                ScheduledFutureManager.del(clientId);
                return;
            }

            // 计算距离上次心跳的时间间隔（秒）
            long currentTime = System.currentTimeMillis();
            long timeSinceLastHeartbeat = (currentTime - lastHeartbeatTime) / 1000;

            logger.info("MSG: SseHeartbeat | ID: {} | Date: {} | 距离上次心跳: {}秒",
                    clientId, new Date(), timeSinceLastHeartbeat);

            // 发送心跳消息，包含一个时间戳防止浏览器缓存
            boolean success = SseSession.send(clientId, ResponseObject.success("ping-" + currentTime));

            if (success) {
                // 更新上次心跳时间
                this.lastHeartbeatTime = currentTime;

                // 发送成功，重置失败计数
                if (failCount > 0) {
                    failCount = 0;
                    logger.info("心跳恢复正常，客户端ID: {}", clientId);
                }
            } else {
                failCount++;
                logger.warn("心跳发送失败，客户端ID: {}, 失败次数: {}/{}", clientId, failCount, MAX_FAIL_COUNT);

                if (failCount >= MAX_FAIL_COUNT) {
                    logger.error("心跳发送连续失败达到上限，客户端ID: {}, 正在关闭连接", clientId);
                    SseSession.del(clientId);
                    ScheduledFutureManager.del(clientId);
                }
            }
        } catch (Exception e) {
            logger.error("心跳任务执行异常，客户端ID: {}, 错误信息: {}", clientId, e.getMessage(), e);
            // 出现未预期的异常，也计入失败次数
            failCount++;
            if (failCount >= MAX_FAIL_COUNT) {
                logger.error("心跳任务异常次数达到上限，客户端ID: {}, 正在关闭连接", clientId);
                SseSession.del(clientId);
                ScheduledFutureManager.del(clientId);
            }
        }
    }
}
