package cn.zuster.sse.controller;

import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.StringUtil;
import com.taurus.entity.ResponseObject;

import cn.zuster.sse.exception.SseException;
import cn.zuster.sse.service.SseService;
import cn.zuster.sse.session.SseSession;

/**
 * SSE测试控制器
 * 
 * @date 2021/1/5
 */

@RestController
@ComponentScan("cn.zuster")
@RequestMapping("/sseTest")
public class SseTestController {

    private static final Logger logger = LoggerFactory.getLogger(SseTestController.class);
    @Autowired
    private SseService sseService;

    /**
     * 建立SSE连接
     * 
     * @param clientId 客户端ID
     * @param response HTTP响应对象，用于设置响应头
     * @return SseEmitter对象
     */
    @GetMapping(value = "/start", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter start(@RequestParam String clientId, HttpServletResponse response) {
        if (StringUtil.isNullOrEmpty(clientId)) {
            logger.error("客户端ID为空，无法建立连接");
            throw new SseException("clientId不能为空");
        }

        // 设置响应头，防止代理服务器缓存
        response.setHeader("Cache-Control", "no-cache, no-store, max-age=0, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Expires", "0");
        // 保持连接打开
        response.setHeader("Connection", "keep-alive");

        try {
            logger.info("开始创建SSE连接：{}", clientId);
            SseEmitter emitter = sseService.start(clientId);

            // 立即发送一条测试消息确认连接已建立
            emitter.send(SseEmitter.event()
                    .name("connect")
                    .data(ResponseObject.success("连接已建立 - " + System.currentTimeMillis())));

            return emitter;
        } catch (Exception e) {
            logger.error("创建SSE连接失败，客户端ID: {}，错误信息: {}", clientId, e.getMessage());
            throw new SseException("创建连接失败: " + e.getMessage());
        }
    }

    /**
     * 向指定客户端发送消息
     * 
     * @param body 包含clientId和msg的请求体
     * @return 响应对象
     */
    @PostMapping("/send")
    public ResponseObject send(@RequestBody JSONObject body) {
        String clientId = body.getString("clientId");
        String msg = body.getString("msg");

        if (StringUtil.isNullOrEmpty(clientId) || StringUtil.isNullOrEmpty(msg)) {
            logger.warn("发送消息参数错误，clientId: {}, msg: {}", clientId, msg);
            return ResponseObject.failure("参数错误: clientId和msg不能为空");
        }

        try {
            logger.info("向客户端 {} 发送消息: {}", clientId, msg);
            String res = sseService.send(clientId, ResponseObject.success(msg));

            if (res.equals("success")) {
                return ResponseObject.success("发送成功");
            } else {
                logger.warn("向客户端 {} 发送消息失败", clientId);
                return ResponseObject.failure("发送失败: " + res);
            }
        } catch (Exception e) {
            logger.error("发送消息时发生异常，clientId: {}, 错误信息: {}", clientId, e.getMessage());
            return ResponseObject.failure("发送异常: " + e.getMessage());
        }
    }

    /**
     * 检查SSE连接状态
     * 
     * @param clientId 客户端ID
     * @return 连接状态信息
     */
    @GetMapping("/status")
    public ResponseObject status(@RequestParam String clientId) {
        if (StringUtil.isNullOrEmpty(clientId)) {
            return ResponseObject.failure("clientId不能为空");
        }

        boolean exists = SseSession.exist(clientId);
        if (exists) {
            return ResponseObject.success("连接正常");
        } else {
            return ResponseObject.failure("连接不存在");
        }
    }

    /**
     * 关闭SSE连接
     *
     * @param clientId 客户端ID
     * @return 结果信息
     */
    @GetMapping("/end")
    public ResponseObject close(@RequestParam String clientId) {
        if (StringUtil.isNullOrEmpty(clientId)) {
            logger.error("客户端ID为空，无法关闭连接");
            return ResponseObject.failure("clientId不能为空");
        }

        try {
            logger.info("关闭SSE连接：{}", clientId);
            String result = sseService.close(clientId);

            if ("Success".equals(result)) {
                return ResponseObject.success("连接已关闭");
            } else {
                logger.warn("关闭客户端 {} 连接失败", clientId);
                return ResponseObject.failure("关闭连接失败: " + result);
            }
        } catch (Exception e) {
            logger.error("关闭连接时发生异常，clientId: {}, 错误信息: {}", clientId, e.getMessage());
            return ResponseObject.failure("关闭连接异常: " + e.getMessage());
        }
    }
}
