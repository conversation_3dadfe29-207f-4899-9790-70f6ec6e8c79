package cn.zuster.sse;

import javax.annotation.PreDestroy;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;

import cn.zuster.sse.task.ScheduledFutureManager;

@EnableDiscoveryClient
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class })
@ComponentScan("cn.zuster")
public class SseApplication {
    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(SseApplication.class, args);

        // 添加关闭钩子，确保应用关闭时清理资源
        context.registerShutdownHook();
    }

    @PreDestroy
    public void destroy() {
        // 应用关闭时关闭线程池
        ScheduledFutureManager.shutdown();
    }
}
