package cn.zuster.sse;

import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration;

@Component("sseNacosConfig")
public class NacosConfig implements ApplicationRunner {

    private static final org.slf4j.Logger log = LoggerFactory.getLogger(NacosConfig.class);

    @Autowired(required = false)
    private NacosAutoServiceRegistration registration;

    @Value("${server.port}")
    Integer port;

    @Override
    public void run(ApplicationArguments args) {
        if (registration != null && port != null) {
            // 如果getTomcatPort()端口获取异常,就采用配置文件中配置的端口
            Integer tomcatPort = port;

            Integer externalPort = getTomcatPort();
            if (externalPort != null) {
                log.info("web容器的端口号为：{}", externalPort);
                tomcatPort = externalPort;
            }

            registration.setPort(tomcatPort);
            registration.start();
        }
    }

    /**
     * 获取外置tomcat端口
     */
    public Integer getTomcatPort() {
        return port;
        // MBeanServer beanServer = ManagementFactory.getPlatformMBeanServer();
        // try{
        // Set<ObjectName> objectNames = beanServer.queryNames(new
        // ObjectName("*:type=Connector,*"), Query.match(Query.attr("protocol"),
        // Query.value("HTTP/1.1")));
        // for (ObjectName objectName : objectNames) {
        // String catalina = objectName.getDomain();
        // if ("Catalina".equals(catalina)) {
        // return objectName.getKeyProperty("port");
        // }
        // }
        // return null;
        // }catch(Exception ex) {
        // log.info("应用未在web容器内启动（如：tomcat），未获取到端口号");
        // return null;
        // }
    }
}
