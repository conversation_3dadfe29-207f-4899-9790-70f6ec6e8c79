package cn.zuster.sse.service.impl;

import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import cn.zuster.sse.exception.SseException;
import cn.zuster.sse.service.SseService;
import cn.zuster.sse.session.SseSession;
import cn.zuster.sse.task.ScheduledFutureManager;

/**
 * SSE 相关业务实现
 *
 * <AUTHOR>
 * @date 2021/1/5
 */
@Service
public class SseServiceImpl implements SseService {
	private static final Logger logger = LoggerFactory.getLogger(SseServiceImpl.class);

	/**
	 * 新建连接
	 *
	 * @param clientId 客户端ID
	 * @return
	 */
	@Override
	synchronized public SseEmitter start(String clientId) {
		ScheduledFutureManager.add(clientId);
		return SseSession.add(clientId);
	}

	/**
	 * 发送数据
	 *
	 * @param clientId 客户端ID
	 * @return
	 */
	@Override
	public String send(String clientId, Object msg) {
		if (SseSession.send(clientId, msg)) {
			return "success";
		} else {
			return "error";
		}
	}

	/**
	 * 关闭连接
	 *
	 * @param clientId 客户端ID
	 * @return
	 */
	@Override
	public String close(String clientId) {
		logger.info("MSG: SseConnectClose | ID: {} | Date: {}", clientId, new Date());
		if (SseSession.del(clientId)&&ScheduledFutureManager.del(clientId))
			return "Success";
		return "Error";
	}
}
