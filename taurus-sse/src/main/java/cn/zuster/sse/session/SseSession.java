package cn.zuster.sse.session;

import java.io.IOException;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import cn.zuster.sse.exception.SseException;
import cn.zuster.sse.task.ScheduledFutureManager;

/**
 * SSE Session
 *
 * <AUTHOR>
 * @date 2021/1/5
 */
public class SseSession {
	private static final Logger logger = LoggerFactory.getLogger(SseSession.class);

	/**
	 * Session维护Map
	 */
	private static Map<String, SseEmitter> SESSION = new ConcurrentHashMap<>();

	/**
	 * 默认的SSE超时时间（毫秒），设置为30分钟
	 * 根据应用场景调整，对于需要长时间连接的场景，可以设置更长的时间
	 */
	private static final long DEFAULT_TIMEOUT = 30 * 60 * 1000L;

	/**
	 * 判断Session是否存在
	 *
	 * @param id 客户端ID
	 * @return true 如果session存在, false 如果不存在
	 */
	public static boolean exist(String id) {
		return SESSION.get(id) != null;
	}

	/**
	 * 增加Session
	 *
	 * @param id 客户端ID
	 * @return SseEmitter 对象
	 */
	public static SseEmitter add(String id) {
		final SseEmitter oldEmitter = SESSION.get(id);

		if (oldEmitter != null) {
			logger.info("检测到重复连接, emitter id:{}, 正在清理旧连接", id);
			del(id);
		}

		// 设置较长的超时时间，避免正常场景下出现超时
		SseEmitter emitter = new SseEmitter(DEFAULT_TIMEOUT);
		logger.info("MSG: SseConnect | EmitterHash: {} | ID: {} | Date: {} | Timeout: {}ms",
				emitter.hashCode(), id, new Date(), DEFAULT_TIMEOUT);

		emitter.onCompletion(() -> {
			logger.info("MSG: SseConnectCompletion | EmitterHash: {} |ID: {} | Date: {}", emitter.hashCode(), id,
					new Date());
			SseSession.onCompletion(id);
			ScheduledFutureManager.del(id);
		});
		emitter.onTimeout(() -> {
			logger.error("MSG: SseConnectTimeout | EmitterHash: {} |ID: {} | Date: {} | 连接超时({}ms)，可能是网络问题或客户端未正确保持连接",
					emitter.hashCode(), id, new Date(), DEFAULT_TIMEOUT);
			SseSession.onError(id,
					new SseException("TimeOut(clientId: " + id + ", 连接超过" + (DEFAULT_TIMEOUT / 1000) + "秒未活动)"));
			ScheduledFutureManager.del(id);
		});
		emitter.onError(t -> {
			logger.error("MSG: SseConnectError | EmitterHash: {} |ID: {} | Date: {} | ErrorType: {} | ErrorMessage: {}",
					emitter.hashCode(), id, new Date(), t.getClass().getName(), t.getMessage());
			SseSession.onError(id, new SseException("Error(clientId: " + id + "): " + t.getMessage()));
			ScheduledFutureManager.del(id);
		});

		SESSION.put(id, emitter);

		return emitter;
	}

	/**
	 * 删除Session
	 *
	 * @param id 客户端ID
	 * @return
	 */
	public static boolean del(String id) {
		final SseEmitter emitter = SESSION.remove(id);
		if (emitter != null) {
			try {
				emitter.complete();
				logger.info("成功删除客户端 {} 的连接", id);
				return true;
			} catch (Exception e) {
				logger.error("删除连接时发生错误, 客户端ID: {}, 错误信息: {}", id, e.getMessage());
			}
		} else {
			logger.warn("尝试删除不存在的连接, 客户端ID: {}", id);
		}
		return false;
	}

	/**
	 * 发送消息
	 *
	 * @param id  客户端ID
	 * @param msg 发送的消息
	 * @return
	 */
	public static boolean send(String id, Object msg) {
		final SseEmitter emitter = SESSION.get(id);
		if (emitter != null) {
			try {
				emitter.send(msg);
				return true;
			} catch (IOException e) {
				logger.error("MSG: SendMessageError-IOException | ID: {} | Date: {} | ErrorMessage: {}",
						id, new Date(), e.getMessage());
				SseSession.del(id);
				return false;
			} catch (Exception e) {
				logger.error(
						"MSG: SendMessageError-OtherException | ID: {} | Date: {} | ErrorType: {} | ErrorMessage: {}",
						id, new Date(), e.getClass().getName(), e.getMessage());
				SseSession.del(id);
				return false;
			}
		} else {
			logger.warn("尝试向不存在的连接发送消息, 客户端ID: {}", id);
		}
		return false;
	}

	/**
	 * SseEmitter onCompletion 后执行的逻辑
	 *
	 * @param id 客户端ID
	 */
	public static void onCompletion(String id) {
		SseSession.del(id);
	}

	/**
	 * SseEmitter onTimeout 或 onError 后执行的逻辑
	 *
	 * @param id
	 * @param e
	 */
	public static void onError(String id, SseException e) {
		final SseEmitter emitter = SESSION.get(id);
		if (emitter != null) {
			try {
				emitter.completeWithError(e);
				logger.info("已完成错误处理, 客户端ID: {}", id);
			} catch (Exception ex) {
				logger.error("处理错误时发生异常, 客户端ID: {}, 错误信息: {}", id, ex.getMessage());
				SseSession.del(id);
			}
		} else {
			logger.warn("尝试处理不存在连接的错误, 客户端ID: {}", id);
		}
	}
}
