package com.taurus.oss.aliyun;

/***
 * oss仓库
 * 
 * <AUTHOR>
 *
 */
public class OssRespository {
	
	private String endpoint;
	private String accessKeyId;
	private String accessKeySecret;
	private String bucketName;

	public String getEndpoint() {
		return endpoint;
	}

	public void setEndpoint(String endpoint) {
		this.endpoint = endpoint;
	}

	public String getAccessKeyId() {
		return accessKeyId;
	}

	public void setAccessKeyId(String accessKeyId) {
		this.accessKeyId = accessKeyId;
	}

	public String getAccessKeySecret() {
		return accessKeySecret;
	}




	public void setAccessKeySecret(String accessKeySecret) {
		this.accessKeySecret = accessKeySecret;
	}




	public String getBucketName() {
		return bucketName;
	}




	public void setBucketName(String bucketName) {
		this.bucketName = bucketName;
	}
	
	
	/**
	 * 根据产品名称找到对应的aliyun oss仓库对象
	 * @param productName
	 * @return
	 */
	public static OssRespository getRespositoryByProductName(String productName) {
		OssRespository respository = new OssRespository();
		if(productName.equals("etea")||productName.equals("punchcard")) {
			String endpoint = "https://oss-cn-hangzhou.aliyuncs.com";
			// RAM账号。
			String accessKeyId = "LTAI4G6geztw5fN5jH8pnW4j";
			String accessKeySecret = "******************************";

			String bucketName = "examination-etea";
			respository.setEndpoint(endpoint);
			respository.setAccessKeyId(accessKeyId);
			respository.setAccessKeySecret(accessKeySecret);
			respository.setBucketName(bucketName);
		}else if(productName.equals("qkk")) {
			String endpoint = "https://oss-cn-hangzhou.aliyuncs.com";
			// RAM账号。
			String accessKeyId = "LTAI4G6geztw5fN5jH8pnW4j";
			String accessKeySecret = "******************************";

			String bucketName = "exam-qkk";
			respository.setEndpoint(endpoint);
			respository.setAccessKeyId(accessKeyId);
			respository.setAccessKeySecret(accessKeySecret);
			respository.setBucketName(bucketName);
		}
		return respository;
		
	}

}
