package com.taurus.oss.aliyun;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.aliyun.oss.ClientException;
import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.DeleteObjectsRequest;
import com.aliyun.oss.model.DeleteObjectsResult;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.ListObjectsRequest;
import com.aliyun.oss.model.OSSObjectSummary;
import com.aliyun.oss.model.ObjectListing;

/**
 * 阿里云oss基本操作
 * 
 * <AUTHOR>
 *
 */
public class OssOperation {
	/**
	 * 判断文件是否存在
	 * 
	 * @param respository
	 * @param objectName
	 * @return
	 */
	public static boolean doesObjectExist(OssRespository respository, String objectName) {
		// 创建OSSClient实例。
		OSS ossClient = new OSSClientBuilder().build(respository.getEndpoint(), respository.getAccessKeyId(),
				respository.getAccessKeySecret());

		boolean found = ossClient.doesObjectExist(respository.getBucketName(), objectName);
		System.out.println(found);
		// 关闭OSSClient。
		ossClient.shutdown();
		return found;
	}

	/**
	 * 创建一个指定名称的空文件夹
	 * 
	 * @param repository
	 * @param keySuffixWithSlash 注意结尾一定是/
	 * @throws IOException
	 */
	public static void createEmptyFolder(OssRespository respository, String keySuffixWithSlash) {
		/*
		 * Constructs a client instance with your account for accessing OSS
		 */
		OSS client = new OSSClientBuilder().build(respository.getEndpoint(), respository.getAccessKeyId(),
				respository.getAccessKeySecret());

		try {
			/*
			 * Create an empty folder without request body, note that the key must be
			 * suffixed with a slash
			 */

			client.putObject(respository.getBucketName(), keySuffixWithSlash, new ByteArrayInputStream(new byte[0]));
			System.out.println("Creating an empty folder " + keySuffixWithSlash + "\n");

			/*
			 * Verify whether the size of the empty folder is zero
			 */
//            OSSObject object = client.getObject(respository.getBucketName(), keySuffixWithSlash);
//            System.out.println("Size of the empty folder '" + object.getKey() + "' is " + 
//                    object.getObjectMetadata().getContentLength());
//            object.getObjectContent().close();

		} catch (OSSException oe) {
			System.out.println("Caught an OSSException, which means your request made it to OSS, "
					+ "but was rejected with an error response for some reason.");
			System.out.println("Error Message: " + oe.getErrorMessage());
			System.out.println("Error Code:       " + oe.getErrorCode());
			System.out.println("Request ID:      " + oe.getRequestId());
			System.out.println("Host ID:           " + oe.getHostId());
		} catch (ClientException ce) {
			System.out.println("Caught an ClientException, which means the client encountered "
					+ "a serious internal problem while trying to communicate with OSS, "
					+ "such as not being able to access the network.");
			System.out.println("Error Message: " + ce.getMessage());
		} finally {
			/*
			 * Do not forget to shut down the client finally to release all allocated
			 * resources.
			 */
			client.shutdown();
		}
	}

	/**
	 * 根据阿里云oss对象名获取该对象的防盗链形式的url
	 * 
	 * @param repository
	 * @param objectName
	 * @Param type 类型 P图片，A音频，V视频
	 * @return
	 */

	public static String generateAccessUrl(OssRespository respository, String objectName, String type) {

		Boolean enabledCDN = true;//是否开启CDN加速
		String bucketName = respository.getBucketName();
		if (bucketName.equals("examination-etea") && enabledCDN) {
			StringBuffer cdnUrl = new StringBuffer("https://cdn-etea.51kaoshi.wang/");
			cdnUrl.append(objectName);
			if(type.equals("P")) {
				cdnUrl.append("?x-oss-process=").append("image%2Fresize%2Cm_fixed%2Cw_750");
			}
			return cdnUrl.toString();
		} else {
			// 创建OSSClient实例。
			OSS ossClient = new OSSClientBuilder().build(respository.getEndpoint(), respository.getAccessKeyId(),
					respository.getAccessKeySecret());

			// 设置URL过期时间为20天。
			Date expiration = new Date(new Date().getTime() + 20 * 24 * 60 * 60 * 1000);
			GeneratePresignedUrlRequest req = new GeneratePresignedUrlRequest(respository.getBucketName(), objectName,
					HttpMethod.GET);
			req.setExpiration(expiration);
			if (type.equals("P")) {
				String style = "image/resize,m_fixed,w_750";
				req.setProcess(style);
			}

			// 生成以GET方法访问的签名URL，访客可以直接通过浏览器访问相关内容。
			URL signedUrl = ossClient.generatePresignedUrl(req);
			// URL signedUrl = ossClient.generatePresignedUrl(respository.getBucketName(),
			// objectName, expiration);
			ossClient.shutdown();
			return signedUrl.toString();
		}
	}

	/**
	 * 删除oss指定key的文件
	 * 
	 * @param respository
	 * @param objectName
	 */
	public static void deleteObject(OssRespository respository, String objectName) {
		// 创建OSSClient实例。
		OSS ossClient = new OSSClientBuilder().build(respository.getEndpoint(), respository.getAccessKeyId(),
				respository.getAccessKeySecret());
		try {
			if (ossClient.doesBucketExist(respository.getBucketName())) {
				ossClient.deleteObject(respository.getBucketName(), objectName);
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (ossClient != null) {
				ossClient.shutdown();
			}
		}
	}

	/**
	 * 批量删除 少于1000个
	 * 
	 * @param respository
	 * @param objectNameList
	 */
	public static void deleteMultipleObjects(OssRespository respository, List<String> objectNameList) {
		// 创建OSSClient实例。
		OSS ossClient = new OSSClientBuilder().build(respository.getEndpoint(), respository.getAccessKeyId(),
				respository.getAccessKeySecret());
		DeleteObjectsResult deleteObjectsResult = ossClient
				.deleteObjects(new DeleteObjectsRequest(respository.getBucketName()).withKeys(objectNameList));
		List<String> deletedObjects = deleteObjectsResult.getDeletedObjects();
		try {
			for (String obj : deletedObjects) {
				String deleteObj = URLDecoder.decode(obj, "UTF-8");
				System.out.println(deleteObj);
			}
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		// 关闭OSSClient。
		ossClient.shutdown();
	}

	/**
	 * 
	 * @param args
	 */
	public static void main(String[] args) {
		String endpoint = "http://oss-cn-hangzhou.aliyuncs.com";
		// RAM账号。
		String accessKeyId = "LTAI4G6geztw5fN5jH8pnW4j";
		String accessKeySecret = "******************************";
		String bucketName = "examination-etea";

		OssRespository respository = new OssRespository();
		respository.setEndpoint(endpoint);
		respository.setAccessKeyId(accessKeyId);
		respository.setAccessKeySecret(accessKeySecret);
		respository.setBucketName(bucketName);

		doesObjectExist(respository, "question/");

		createEmptyFolder(respository, "examSnap/20210209/");
	}

	/**
	 * 列举所有包含指定前缀的文件并删除
	 * 
	 * @param respository
	 * @param prefix
	 */
	public static void deleteObjectsByPrefix(OssRespository respository, String prefix) {
		// 创建OSSClient实例。
		OSS ossClient = new OSSClientBuilder().build(respository.getEndpoint(), respository.getAccessKeyId(),
				respository.getAccessKeySecret());
		String nextMarker = null;
		ObjectListing objectListing = null;
		do {
			ListObjectsRequest listObjectsRequest = new ListObjectsRequest(respository.getBucketName())
					.withPrefix(prefix).withMarker(nextMarker);

			objectListing = ossClient.listObjects(listObjectsRequest);
			if (objectListing.getObjectSummaries().size() > 0) {
				List<String> keys = new ArrayList<String>();
				for (OSSObjectSummary s : objectListing.getObjectSummaries()) {
					System.out.println("key name: " + s.getKey());
					keys.add(s.getKey());
				}
				DeleteObjectsRequest deleteObjectsRequest = new DeleteObjectsRequest(respository.getBucketName())
						.withKeys(keys);
				DeleteObjectsResult deleteObjectsResult = ossClient.deleteObjects(deleteObjectsRequest);
				List<String> deletedObjects = deleteObjectsResult.getDeletedObjects();
				try {
					for (String obj : deletedObjects) {
						String deleteObj = URLDecoder.decode(obj, "UTF-8");
						System.out.println(deleteObj);
					}
				} catch (UnsupportedEncodingException e) {
					e.printStackTrace();
				}
			}

			nextMarker = objectListing.getNextMarker();
		} while (objectListing.isTruncated());

		// 关闭OSSClient。
		ossClient.shutdown();

	}
}
