package com.taurus.utils;

import java.util.List;
import java.util.Map;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.util.StringUtils;

public class POIUtil {
	
	public static HSSFWorkbook generateWorkbook(List<Map<String, Object>> mapList, String sheetName, Integer rowHeight,
			List<Map<String, Object>> sheetHead) {
		HSSFWorkbook workbook = new HSSFWorkbook();
		HSSFSheet Sheet = workbook.createSheet(sheetName);
		HSSFRow headRow = Sheet.createRow(0);
		headRow.setHeightInPoints(30);// 设置表头行高

		/******* 设置表头样式 ********/
		HSSFCellStyle headerCellStyle = workbook.createCellStyle();
		// 居中显示
		headerCellStyle.setAlignment(HorizontalAlignment.CENTER);
		headerCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		// 标题字体
		HSSFFont titleFont = workbook.createFont();
		// 字体大小
		titleFont.setBold(true);
		titleFont.setFontHeightInPoints((short) 14);
		headerCellStyle.setFont(titleFont);

		for (int i = 0; i < sheetHead.size(); i++) {
			Sheet.setColumnWidth(i, 16 * 256);
			HSSFCell cell = headRow.createCell(i);
			cell.setCellValue((String) sheetHead.get(i).get("fieldName"));//表头字段名
			cell.setCellStyle(headerCellStyle);
		}
		
		

		/******* 设置记录条样式 ********/
		HSSFCellStyle recordCellStyle = workbook.createCellStyle();
		// 居中显示
		headerCellStyle.setAlignment(HorizontalAlignment.CENTER);
		headerCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		// 标题字体
		HSSFFont recordFont = workbook.createFont();
		// 字体大小
		recordFont.setFontHeightInPoints((short) 12);
		recordFont.setFontName("Arial Unicode MS");
		recordCellStyle.setFont(recordFont);

		int i = 1;// 从第2行开始为数据行
		for (Map<String, Object> map : mapList) {
			HSSFRow row = Sheet.createRow(i);
			Integer currentRowHeight = 20;// 默认行高20
			if (rowHeight != null) {
				currentRowHeight = rowHeight;
			}
			row.setHeightInPoints(currentRowHeight);// 设置行高

			for (int j = 0; j < sheetHead.size(); j++) {
				String key = String.valueOf(sheetHead.get(j).get("fieldKey"));
				HSSFCell cell = row.createCell(j);
				cell.setCellValue(StringUtils.isEmpty(map.get(key)) ? "" : String.valueOf(map.get(key)));
				cell.setCellStyle(recordCellStyle);
			}
			i++;
		}

		return workbook;
	}
}
