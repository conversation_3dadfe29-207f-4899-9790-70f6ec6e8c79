package com.taurus.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.alibaba.fastjson.JSONArray;

public class MyStringUtil {

	/**
	 * 把字符串转换为byte[]
	 * 
	 * @param str
	 * @return
	 */
	public static Byte[] string2bytes(String str) {
		if (str == null) {
			return new Byte[] {};
		}
		String upStr = str.toUpperCase().replace(" ", ""); // 转为全大写和去空格
		char[] chars = upStr.toCharArray();
		List<Byte> list = new ArrayList<>();
		for (int i = 0; i < chars.length; i++) {
			list.add((byte) chars[i]);
		}
		return list.toArray(new Byte[list.size()]);

	}

	/**
	 * 计算汉字个数
	 * 
	 * @param text
	 * @return
	 */
	public static int count(String text) {
		String Reg = "^[\u4e00-\u9fa5]{1}$";// 正则
		int result = 0;
		for (int i = 0; i < text.length(); i++) {
			String b = Character.toString(text.charAt(i));
			if (b.matches(Reg))
				result++;
		}
		return result;
	}

	/**
	 * 去除全角空格
	 * 
	 * @param str
	 * @return
	 */
	public static String replaceBlankSpace(String str) {
		// 去除全角空格
		str = str.replaceAll((char) 12288 + "", "");
		// 去除半角空格
		Pattern pattern = Pattern.compile("\\s*"); // 包含这个模式
		Matcher match = pattern.matcher(str);
		// 去掉所有的空格,然后返回
		return match.replaceAll("");
	}
	
	
	/**
	 * 去除首位空格
	 * @param str
	 * @return
	 */
	public static String replaceHeadAndTailBlank(String str){
		  Pattern pt=Pattern.compile("^\\s*|\\s*$");
		  Matcher mt=pt.matcher(str);
		  str=mt.replaceAll("");
		  return str;
	}

	/**
	 * 汉字 转换为对应的 UTF-8编码
	 * 
	 * @param s 木
	 * @return E69CA8
	 */
	public static String convertStringToUTF8(String s) {
		if (s == null || s.equals("")) {
			return null;
		}
		StringBuffer sb = new StringBuffer();
		try {
			char c;
			for (int i = 0; i < s.length(); i++) {
				c = s.charAt(i);
				if (c >= 0 && c <= 255) {
					sb.append(c);
				} else {
					byte[] b;
					b = Character.toString(c).getBytes("utf-8");
					for (int j = 0; j < b.length; j++) {
						int k = b[j];
						// 转换为unsigned integer 无符号integer
						/*
						 * if (k < 0) k += 256;
						 */
						k = k < 0 ? k + 256 : k;
						// 返回整数参数的字符串表示形式 作为十六进制（base16）中的无符号整数
						// 该值以十六进制（base16）转换为ASCII数字的字符串
						sb.append(Integer.toHexString(k).toUpperCase());

						// url转置形式
						// sb.append("%" +Integer.toHexString(k).toUpperCase());
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return sb.toString();
	}

	/**
	 * 判断keys中是否包含str
	 * @param str
	 * @param keys
	 * @return
	 */
	public static boolean isContainedInArray(String str,JSONArray keys) {
		
		if(keys==null) {
			return false;
		}
		for(int i=0;i<keys.size();i++) {
			String key = keys.getString(i);
			if(str.equals(key)) {
				return true;
			}
		}
		return false;
	}
	
	/**
	 * 获取随机位数字符串
	 * @param length
	 * @return
	 */
	public static String getRandomString(int length) {
		Random random = new Random();
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < length; i++) {
			int number = random.nextInt(3);
			long result = 0;
			switch (number) {
			case 0:
				result = Math.round(Math.random() * 25 + 65);
				sb.append(String.valueOf((char) result));
				break;
			case 1:
				result = Math.round(Math.random() * 25 + 97);
				sb.append(String.valueOf((char) result));
				break;
			case 2:
				sb.append(String.valueOf(new Random().nextInt(10)));
				break;
			}
		}
		return sb.toString();
	}

}
