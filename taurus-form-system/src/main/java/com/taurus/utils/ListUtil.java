package com.taurus.utils;

import java.util.Collections;
import java.util.List;

public class ListUtil<T> {
	
	/**
	 * 随机取出列表中的N个元素
	 * @param list
	 * @param takeNum
	 * @return
	 */
	public  List<T> randomTakeout(List<T> list,Integer takeNum){
	    if(takeNum<list.size()) {
	    	Collections.shuffle(list); 
	    	List<T> randomList = list.subList(0, takeNum);
		    return randomList;
	    }else {
		    return list;
	    }  
	}
	
	/**
	 * 通过分页方式取list元素
	 * @param list
	 * @param pageSize
	 * @param pageIndex
	 * @return
	 */
	public List<T> getItemsByPage(List<T> list,Integer pageSize,Integer pageIndex){
		int from= (pageIndex-1)*pageSize;
		int to = pageIndex*pageSize;
		int total= list.size();
		if(to<=total) {
			return list.subList(from, to);
		}else if(from<=total&&to>total) {
			return list.subList(from, total);
		}else if(from>total) {
			return null;
		}
		return null;
	}
}
