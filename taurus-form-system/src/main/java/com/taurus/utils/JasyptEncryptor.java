package com.taurus.utils;

import org.jasypt.util.text.BasicTextEncryptor;

public class JasyptEncryptor {
	public static void main(String[] args) {
        BasicTextEncryptor textEncryptor = new BasicTextEncryptor();
        //加密所需的salt(盐)
        textEncryptor.setPassword("EDsPO|sdfa2812&823&&^dlfFdKdajdingss");
        //要加密的数据（数据库的用户名或密码）
        String username = textEncryptor.encrypt("873dPw(*^Kd%");
        String password = textEncryptor.encrypt("873dPw(*^Kd%");
        System.out.println("username:"+username);
        System.out.println("password:"+password);
    }
}
