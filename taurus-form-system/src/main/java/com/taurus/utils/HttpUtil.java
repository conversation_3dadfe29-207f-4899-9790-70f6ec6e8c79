package com.taurus.utils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URI;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicHeader;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

public class HttpUtil {

	private static final String Charset = "utf-8";

	/**
	 * 发送请求，如果失败，会返回null
	 * 
	 * @param url
	 * @param map
	 * @return
	 */
	public static String post(String url, Map<String, String> map) {
		// 处理请求地址
		try {
			HttpClient client = HttpClientBuilder.create().build();
			URI uri = new URI(url);
			HttpPost post = new HttpPost(uri);

			// 添加参数
			List<NameValuePair> params = new ArrayList<NameValuePair>();
			for (String str : map.keySet()) {
				params.add(new BasicNameValuePair(str, map.get(str)));
			}
			post.setEntity(new UrlEncodedFormEntity(params, Charset));
			// 执行请求
			HttpResponse response = client.execute(post);

			if (response.getStatusLine().getStatusCode() == 200) {
				// 处理请求结果
				StringBuffer buffer = new StringBuffer();
				InputStream in = null;
				try {
					in = response.getEntity().getContent();
					BufferedReader reader = new BufferedReader(new InputStreamReader(in, Charset));
					String line = null;
					while ((line = reader.readLine()) != null) {
						buffer.append(line);
					}

				} catch (Exception e) {
					e.printStackTrace();
				} finally {
					// 关闭流
					if (in != null)
						try {
							in.close();
						} catch (Exception e) {
							e.printStackTrace();
						}
				}

				return buffer.toString();
			} else {
				return null;
			}
		} catch (Exception e1) {
			e1.printStackTrace();
		}
		return null;

	}

	/**
	 * 发送请求，如果失败会返回null
	 * 
	 * @param url
	 * @param str
	 * @return
	 */
	public static String post(String url, String str) {
		// 处理请求地址
		try {
			HttpClient client = HttpClientBuilder.create().build();
			URI uri = new URI(url);
			HttpPost post = new HttpPost(uri);
			post.setEntity(new StringEntity(str, Charset));
			// 执行请求
			HttpResponse response = client.execute(post);

			if (response.getStatusLine().getStatusCode() == 200) {
				// 处理请求结果
				StringBuffer buffer = new StringBuffer();
				InputStream in = null;
				try {
					in = response.getEntity().getContent();
					BufferedReader reader = new BufferedReader(new InputStreamReader(in, "utf-8"));
					String line = null;
					while ((line = reader.readLine()) != null) {
						buffer.append(line);
					}

				} finally {
					// 关闭流
					if (in != null)
						in.close();
				}

				return buffer.toString();
			} else {
				return null;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;

	}

	/**
	 * 发送GET方式的请求，并返回结果字符串。 <br>
	 * 
	 * @param url
	 * @return 如果失败，返回为null
	 */
	public static String get(String url) {
		try {
			HttpClient client = HttpClientBuilder.create().build();
			URI uri = new URI(url);
			HttpGet get = new HttpGet(uri);
			HttpResponse response = client.execute(get);
			if (response.getStatusLine().getStatusCode() == 200) {
				StringBuffer buffer = new StringBuffer();
				InputStream in = null;
				try {
					in = response.getEntity().getContent();
					BufferedReader reader = new BufferedReader(new InputStreamReader(in, Charset));
					String line = null;
					while ((line = reader.readLine()) != null) {
						buffer.append(line);
					}

				} finally {
					if (in != null)
						in.close();
				}

				return buffer.toString();
			} else {
				return null;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 编写Get请求的方法。但没有参数传递的时候，可以使用Get请求
	 * 
	 */
	public static JSONObject doGetStr(String url) throws ClientProtocolException, IOException {
		CloseableHttpClient httpClient = HttpClientBuilder.create().build();
		HttpGet httpGet = new HttpGet(url);
		JSONObject jsonObject = null;
		HttpResponse response = httpClient.execute(httpGet);
		HttpEntity entity = response.getEntity();
		if (entity != null) {
			String result = EntityUtils.toString(entity, "UTF-8");
			jsonObject = JSONObject.parseObject(result);
		}
		return jsonObject;
	}

	/**
	 * 编写Post请求的方法。当我们需要参数传递的时候，可以使用Post请求
	 */
	public static JSONObject doPostStr(String url, String json) throws ClientProtocolException, IOException {
		CloseableHttpClient httpClient = HttpClientBuilder.create().build();
		HttpPost httPost = new HttpPost(url);
		JSONObject jsonObject = null;
		StringEntity se = new StringEntity(json);
		se.setContentType("text/json");
		se.setContentEncoding(new BasicHeader(HTTP.CONTENT_TYPE, "application/json"));
		httPost.setEntity(se);
		HttpResponse response = httpClient.execute(httPost);
		String result = EntityUtils.toString(response.getEntity(), "UTF-8");
		jsonObject = JSONObject.parseObject(result);
		return jsonObject;
	}

	/**
	 * 打印request头信息
	 * 
	 * @param request
	 * @param logger
	 */
	public static void printRequestHeaders(HttpServletRequest request, Logger logger) {
		Enumeration<String> enumString = request.getHeaderNames();
		while (enumString.hasMoreElements()) {
			String name = enumString.nextElement();
			String value = request.getHeader(name);
			logger.info("headName:" + name);
			logger.info("headerValue:" + value);
		}
	}

	/**
	 * 打印请求参数
	 * 
	 * @param request
	 * @param logger
	 */
	public static void printRequestParameters(HttpServletRequest request, Logger logger) {
		// Header部分
		Enumeration<?> enum1 = request.getHeaderNames();
		while (enum1.hasMoreElements()) {
			String key = (String) enum1.nextElement();
			String value = request.getHeader(key);
			System.out.println(key + "\t" + value);
		}

		String method = request.getMethod();

		if (method.equals("POST")) {
			// body部分
			String inputLine;
			String str = "";
			try {
				BufferedReader reader = new BufferedReader(new InputStreamReader(request.getInputStream()));
				while ((inputLine = reader.readLine()) != null) {
					str += inputLine;
				}
				reader.close();
			} catch (IOException e) {
				System.out.println("IOException: " + e);
			}
			logger.info("请求参数: {}", str);
		} else if (method.equals("GET")) {
			Map<String, String> params = new HashMap<>();
			Enumeration<?> temp = request.getParameterNames();
			if (null != temp) {
				while (temp.hasMoreElements()) {
					String en = (String) temp.nextElement();
					String value = request.getParameter(en);
					params.put(en, value);
				}
			}
			logger.info("请求参数: {}", JSON.toJSONString(params));
		}
	}
}
