package com.taurus.formSys.entity;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;


/**
 * 表名：category_form
 * 备注：每个分类下包含哪些表单
 *
 * <AUTHOR>
 */
@Table(name = "category_form")
public class CategoryForm {
    /**  数据库字段：id */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**产品名称*/
    private String productName;
    
    /** 类别id, 数据库字段：category_id */
    private Integer categoryId;

    /** 表单id, 数据库字段：form_id */
    private Integer formId;
    
    /** 数据状态,	数据库字段：state*/
    private String state;
    
    /** 表单在类别中的排序号, 数据库字段：sequence_no */
    private Integer sequenceNo;
    
    public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

    /**  数据库字段：category_form.id */
    public void setId(Integer id) {
        this.id = id;
    }

    /**  数据库字段：category_form.id */
    public Integer getId() {
        return this.id;
    }

    /** 设置类别id, 数据库字段：category_form.category_id */
    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    /** 获取类别id, 数据库字段：category_form.category_id */
    public Integer getCategoryId() {
        return this.categoryId;
    }

    /** 设置表单id, 数据库字段：category_form.form_id */
    public void setFormId(Integer formId) {
        this.formId = formId;
    }

    /** 获取表单id, 数据库字段：category_form.form_id */
    public Integer getFormId() {
        return this.formId;
    }
    
    public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}
	
	public Integer getSequenceNo() {
		return sequenceNo;
	}

	public void setSequenceNo(Integer sequenceNo) {
		this.sequenceNo = sequenceNo;
	}


    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = (prime * result) + ((id == null) ? 0 : id.hashCode());

        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        if (obj == null) {
            return false;
        }

        if (getClass() != obj.getClass()) {
            return false;
        }

        CategoryForm other = (CategoryForm) obj;

        if (id == null) {
            if (other.id != null) {
                return false;
            }
        } else if (!id.equals(other.id)) {
            return false;
        }

        return true;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("CategoryForm [");
        sb.append("id=").append(id);
        sb.append(", ");
        sb.append("productName=").append(productName);
        sb.append(", ");
        sb.append("categoryId=").append(categoryId);
        sb.append(", ");
        sb.append("formId=").append(formId);
        sb.append(", ");
        sb.append("state=").append(state);
        sb.append(", ");
        sb.append("sequenceNo=").append(sequenceNo);
        sb.append("]");

        return sb.toString();
    }
	
}
