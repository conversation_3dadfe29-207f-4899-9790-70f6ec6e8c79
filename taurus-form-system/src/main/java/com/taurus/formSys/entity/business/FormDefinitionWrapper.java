package com.taurus.formSys.entity.business;

import java.util.List;

import com.taurus.formSys.entity.FormDefinition;
import com.taurus.formSys.entity.FormFieldStructure;

/**
 * 表单wrapper
 * <AUTHOR>
 *
 */
public class FormDefinitionWrapper extends FormDefinition {
	//表单背景图片可访问的url
	private String bgImageUrl;
	//表单背景音乐可访问的url
	private String bgAudioUrl;
	//表单微信小程序卡片主图url
	private String wxMpCardImageUrl;
	//表单字段列表
	private List<FormFieldStructure> fieldList;
	
	public String getBgImageUrl() {
		return bgImageUrl;
	}
	public String getBgAudioUrl() {
		return bgAudioUrl;
	}
	public void setBgAudioUrl(String bgAudioUrl) {
		this.bgAudioUrl = bgAudioUrl;
	}
	public void setBgImageUrl(String bgImageUrl) {
		this.bgImageUrl = bgImageUrl;
	}
	public List<FormFieldStructure> getFieldList() {
		return fieldList;
	}
	public void setFieldList(List<FormFieldStructure> fieldList) {
		this.fieldList = fieldList;
	}
	public String getWxMpCardImageUrl() {
		return wxMpCardImageUrl;
	}
	public void setWxMpCardImageUrl(String wxMpCardImageUrl) {
		this.wxMpCardImageUrl = wxMpCardImageUrl;
	}
	
}
