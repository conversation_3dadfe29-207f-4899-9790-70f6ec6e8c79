package com.taurus.formSys.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;


/**
 * 表名：form_definition
 * 备注：表单定义表
说明了表单的基本属性
 *
 * <AUTHOR>
 */
@Table(name = "form_definition")
public class FormDefinition {
    /**  数据库字段：id */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /** 引用码-使用表单系统的产品编号，用来标识来源, 数据库字段：product_name */
    private String productName;

    /** 表单创建者id, 数据库字段：user_id */
    private Integer userId;

    /** 表单标题, 数据库字段：title */
    private String title;

    /** 表单填写说明, 数据库字段：explanation */
    private String explanation;

    /** 跳转类型：S代表系统内置模板页，C代表用户自定义页, 数据库字段：redirect_type */
    private String redirectType;

    /** 提交表单后跳转页的URL（默认跳转系统模板页）, 数据库字段：redirect_url_after_submit */
    private String redirectUrlAfterSubmit;

    /** 表单有效起始时间, 数据库字段：valid_start_time */
    private Date validStartTime;

    /** 表单有效结束时间, 数据库字段：valid_end_time */
    private Date validEndTime;

    /** 每用户最多可以提交表单几次, 数据库字段：per_user_submit_max_times */
    private Integer perUserSubmitMaxTimes;

    /** 所有用户最多可以提交表单次数, 数据库字段：submit_max_times */
    private Integer submitMaxTimes;

    /** 创建时间, 数据库字段：create_time */
    private Date createTime;

    /** 表单组件布局类型：1表示字段标签和输入框分开；2只有输入框, 数据库字段：widget_theme */
    private Integer widgetTheme;

    /** 微信小程序卡片主题图, 数据库字段：wx_mp_card_image_object_name */
    private String wxMpCardImageObjectName;

    /** 是否启用密码, 数据库字段：password_enabled */
    private Boolean passwordEnabled;

    /** 访问密码, 数据库字段：password */
    private String password;

    /** 背景图片oss对象名称, 数据库字段：bg_image_object_name */
    private String bgImageObjectName;

    /** 背景音乐oss对象名称, 数据库字段：bg_audio_object_name */
    private String bgAudioObjectName;
    
    /** 表单背景颜色 **/
    private String bgcolor;
    
    /** 表单高度 **/
    private Integer formHeight;
    
    /** 是否已删除, 数据库字段：enabled */
    private Boolean enabled;
    
    /** 表单创建单位名称, 数据库字段：sponsor */
    private String sponsor;

    /**  数据库字段：form_definition.id */
    public void setId(Integer id) {
        this.id = id;
    }

    /**  数据库字段：form_definition.id */
    public Integer getId() {
        return this.id;
    }

    /** 设置引用码-使用表单系统的产品名称，用来标识来源, 数据库字段：form_definition.product_name */
    public void setProductName(String productName) {
        this.productName = productName;
    }

    /** 获取引用码-使用表单系统的产品名称，用来标识来源, 数据库字段：form_definition.product_name */
    public String getProductName() {
        return this.productName;
    }

    /** 设置表单创建者id, 数据库字段：form_definition.user_id */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /** 获取表单创建者id, 数据库字段：form_definition.user_id */
    public Integer getUserId() {
        return this.userId;
    }

    /** 设置表单标题, 数据库字段：form_definition.title */
    public void setTitle(String title) {
        this.title = title;
    }

    /** 获取表单标题, 数据库字段：form_definition.title */
    public String getTitle() {
        return this.title;
    }

    /** 设置表单填写说明, 数据库字段：form_definition.explanation */
    public void setExplanation(String explanation) {
        this.explanation = explanation;
    }

    /** 获取表单填写说明, 数据库字段：form_definition.explanation */
    public String getExplanation() {
        return this.explanation;
    }

    /** 设置跳转类型：S代表系统内置模板页，C代表用户自定义页, 数据库字段：form_definition.redirect_type */
    public void setRedirectType(String redirectType) {
        this.redirectType = redirectType;
    }

    /** 获取跳转类型：S代表系统内置模板页，C代表用户自定义页, 数据库字段：form_definition.redirect_type */
    public String getRedirectType() {
        return this.redirectType;
    }

    /** 设置提交表单后跳转页的URL（默认跳转系统模板页）, 数据库字段：form_definition.redirect_url_after_submit */
    public void setRedirectUrlAfterSubmit(String redirectUrlAfterSubmit) {
        this.redirectUrlAfterSubmit = redirectUrlAfterSubmit;
    }

    /** 获取提交表单后跳转页的URL（默认跳转系统模板页）, 数据库字段：form_definition.redirect_url_after_submit */
    public String getRedirectUrlAfterSubmit() {
        return this.redirectUrlAfterSubmit;
    }

    /** 设置表单有效起始时间, 数据库字段：form_definition.valid_start_time */
    public void setValidStartTime(Date validStartTime) {
        this.validStartTime = validStartTime;
    }

    /** 获取表单有效起始时间, 数据库字段：form_definition.valid_start_time */
    public Date getValidStartTime() {
        return this.validStartTime;
    }

    /** 设置表单有效结束时间, 数据库字段：form_definition.valid_end_time */
    public void setValidEndTime(Date validEndTime) {
        this.validEndTime = validEndTime;
    }

    /** 获取表单有效结束时间, 数据库字段：form_definition.valid_end_time */
    public Date getValidEndTime() {
        return this.validEndTime;
    }

    /** 设置每用户最多可以提交表单几次, 数据库字段：form_definition.per_user_submit_max_times */
    public void setPerUserSubmitMaxTimes(Integer perUserSubmitMaxTimes) {
        this.perUserSubmitMaxTimes = perUserSubmitMaxTimes;
    }

    /** 获取每用户最多可以提交表单几次, 数据库字段：form_definition.per_user_submit_max_times */
    public Integer getPerUserSubmitMaxTimes() {
        return this.perUserSubmitMaxTimes;
    }

    /** 设置所有用户最多可以提交表单次数, 数据库字段：form_definition.submit_max_times */
    public void setSubmitMaxTimes(Integer submitMaxTimes) {
        this.submitMaxTimes = submitMaxTimes;
    }

    /** 获取所有用户最多可以提交表单次数, 数据库字段：form_definition.submit_max_times */
    public Integer getSubmitMaxTimes() {
        return this.submitMaxTimes;
    }

    /** 设置创建时间, 数据库字段：form_definition.create_time */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /** 获取创建时间, 数据库字段：form_definition.create_time */
    public Date getCreateTime() {
        return this.createTime;
    }

    /** 设置表单组件布局类型：1表示字段标签和输入框分开；2只有输入框, 数据库字段：form_definition.widget_theme */
    public void setWidgetTheme(Integer widgetTheme) {
        this.widgetTheme = widgetTheme;
    }

    /** 获取表单组件布局类型：1表示字段标签和输入框分开；2只有输入框, 数据库字段：form_definition.widget_theme */
    public Integer getWidgetTheme() {
        return this.widgetTheme;
    }

    /** 设置微信小程序卡片主题图, 数据库字段：form_definition.wx_mp_card_image_object_name */
    public void setWxMpCardImageObjectName(String wxMpCardImageObjectName) {
        this.wxMpCardImageObjectName = wxMpCardImageObjectName;
    }

    /** 获取微信小程序卡片主题图, 数据库字段：form_definition.wx_mp_card_image_object_name */
    public String getWxMpCardImageObjectName() {
        return this.wxMpCardImageObjectName;
    }

    /** 设置是否启用密码, 数据库字段：form_definition.password_enabled */
    public void setPasswordEnabled(Boolean passwordEnabled) {
        this.passwordEnabled = passwordEnabled;
    }

    /** 获取是否启用密码, 数据库字段：form_definition.password_enabled */
    public Boolean getPasswordEnabled() {
        return this.passwordEnabled;
    }

    /** 设置访问密码, 数据库字段：form_definition.password */
    public void setPassword(String password) {
        this.password = password;
    }

    /** 获取访问密码, 数据库字段：form_definition.password */
    public String getPassword() {
        return this.password;
    }

    /** 设置是否已删除, 数据库字段：form_definition.enabled */
    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    /** 获取是否已删除, 数据库字段：form_definition.enabled */
    public Boolean getEnabled() {
        return this.enabled;
    }

    /** 设置背景图片oss对象名称, 数据库字段：form_definition.bg_image_object_name */
    public void setBgImageObjectName(String bgImageObjectName) {
        this.bgImageObjectName = bgImageObjectName;
    }

    /** 获取背景图片oss对象名称, 数据库字段：form_definition.bg_image_object_name */
    public String getBgImageObjectName() {
        return this.bgImageObjectName;
    }

    /** 设置背景音乐oss对象名称, 数据库字段：form_definition.bg_audio_object_name */
    public void setBgAudioObjectName(String bgAudioObjectName) {
        this.bgAudioObjectName = bgAudioObjectName;
    }

    /** 获取背景音乐oss对象名称, 数据库字段：form_definition.bg_audio_object_name */
    public String getBgAudioObjectName() {
        return this.bgAudioObjectName;
    }

    public String getBgcolor() {
		return bgcolor;
	}

	public void setBgcolor(String bgcolor) {
		this.bgcolor = bgcolor;
	}

	@Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = (prime * result) + ((id == null) ? 0 : id.hashCode());

        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        if (obj == null) {
            return false;
        }

        if (getClass() != obj.getClass()) {
            return false;
        }

        FormDefinition other = (FormDefinition) obj;

        if (id == null) {
            if (other.id != null) {
                return false;
            }
        } else if (!id.equals(other.id)) {
            return false;
        }

        return true;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("FormDefinition [");
        sb.append("id=").append(id);
        sb.append(", ");
        sb.append("productName=").append(productName);
        sb.append(", ");
        sb.append("userId=").append(userId);
        sb.append(", ");
        sb.append("title=").append(title);
        sb.append(", ");
        sb.append("explanation=").append(explanation);
        sb.append(", ");
        sb.append("redirectType=").append(redirectType);
        sb.append(", ");
        sb.append("redirectUrlAfterSubmit=").append(redirectUrlAfterSubmit);
        sb.append(", ");
        sb.append("validStartTime=").append(validStartTime);
        sb.append(", ");
        sb.append("validEndTime=").append(validEndTime);
        sb.append(", ");
        sb.append("perUserSubmitMaxTimes=").append(perUserSubmitMaxTimes);
        sb.append(", ");
        sb.append("submitMaxTimes=").append(submitMaxTimes);
        sb.append(", ");
        sb.append("createTime=").append(createTime);
        sb.append(", ");
        sb.append("widgetTheme=").append(widgetTheme);
        sb.append(", ");
        sb.append("wxMpCardImageObjectName=").append(wxMpCardImageObjectName);
        sb.append(", ");
        sb.append("passwordEnabled=").append(passwordEnabled);
        sb.append(", ");
        sb.append("password=").append(password);
        sb.append(", ");
        sb.append("enabled=").append(enabled);
        sb.append(", ");
        sb.append("bgImageObjectName=").append(bgImageObjectName);
        sb.append(", ");
        sb.append("bgAudioObjectName=").append(bgAudioObjectName);
        sb.append(", ");
        sb.append("sponsor=").append(sponsor);
        sb.append("]");

        return sb.toString();
    }

	public Integer getFormHeight() {
		return formHeight;
	}

	public void setFormHeight(Integer formHeight) {
		this.formHeight = formHeight;
	}

	public String getSponsor() {
		return sponsor;
	}

	public void setSponsor(String sponsor) {
		this.sponsor = sponsor;
	}
}
