package com.taurus.formSys.entity;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;


/**
 * 表名：category
 *
 * <AUTHOR>
 */
@Table(name = "category")
public class Category {
    /**  数据库字段：id */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /** 类别级别, 数据库字段：level */
    private Integer level;

    /** 产品名称, 数据库字段：product_name */
    private String productName;

    /** 产品模块名称, 数据库字段：module_name */
    private String moduleName;

    /** 类别名称, 数据库字段：name */
    private String name;

    /** 显示顺序, 数据库字段：sequence_num */
    private Integer sequenceNum;

    /** 父类别id, 数据库字段：parent_id */
    private Integer parentId;

    /** 有效位, 数据库字段：enabled */
    private Boolean enabled;

    /**  数据库字段：category.id */
    public void setId(Integer id) {
        this.id = id;
    }

    /**  数据库字段：category.id */
    public Integer getId() {
        return this.id;
    }

    /** 设置类别级别, 数据库字段：category.level */
    public void setLevel(Integer level) {
        this.level = level;
    }

    /** 获取类别级别, 数据库字段：category.level */
    public Integer getLevel() {
        return this.level;
    }

    /** 设置产品名称, 数据库字段：category.product_name */
    public void setProductName(String productName) {
        this.productName = productName;
    }

    /** 获取产品名称, 数据库字段：category.product_name */
    public String getProductName() {
        return this.productName;
    }

    /** 设置产品模块名称, 数据库字段：category.module_name */
    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    /** 获取产品模块名称, 数据库字段：category.module_name */
    public String getModuleName() {
        return this.moduleName;
    }

    /** 设置类别名称, 数据库字段：category.name */
    public void setName(String name) {
        this.name = name;
    }

    /** 获取类别名称, 数据库字段：category.name */
    public String getName() {
        return this.name;
    }

    /** 设置显示顺序, 数据库字段：category.sequence_num */
    public void setSequenceNum(Integer sequenceNum) {
        this.sequenceNum = sequenceNum;
    }

    /** 获取显示顺序, 数据库字段：category.sequence_num */
    public Integer getSequenceNum() {
        return this.sequenceNum;
    }

    /** 设置父类别id, 数据库字段：category.parent_id */
    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    /** 获取父类别id, 数据库字段：category.parent_id */
    public Integer getParentId() {
        return this.parentId;
    }

    /** 设置有效位, 数据库字段：category.enabled */
    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    /** 获取有效位, 数据库字段：category.enabled */
    public Boolean getEnabled() {
        return this.enabled;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = (prime * result) + ((id == null) ? 0 : id.hashCode());

        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        if (obj == null) {
            return false;
        }

        if (getClass() != obj.getClass()) {
            return false;
        }

        Category other = (Category) obj;

        if (id == null) {
            if (other.id != null) {
                return false;
            }
        } else if (!id.equals(other.id)) {
            return false;
        }

        return true;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("Category [");
        sb.append("id=").append(id);
        sb.append(", ");
        sb.append("level=").append(level);
        sb.append(", ");
        sb.append("productName=").append(productName);
        sb.append(", ");
        sb.append("moduleName=").append(moduleName);
        sb.append(", ");
        sb.append("name=").append(name);
        sb.append(", ");
        sb.append("sequenceNum=").append(sequenceNum);
        sb.append(", ");
        sb.append("parentId=").append(parentId);
        sb.append(", ");
        sb.append("enabled=").append(enabled);
        sb.append("]");

        return sb.toString();
    }
}
