package com.taurus.formSys.entity;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;


/**
 * 表名：form_flow_record_data
 * 备注：表单数据记录详情
 *
 * <AUTHOR>
 */
@Table(name = "form_flow_record_data")
public class FormFlowRecordData {
    /** 表单id, 数据库字段：form_id */
    private Integer formId;

    /** 表单流水记录id, 数据库字段：form_flow_record_id */
    @Id
    @Column(name = "form_flow_record_id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Integer formFlowRecordId;

    /** 字段id, 数据库字段：field_id */
    @Id
    @Column(name = "field_id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Integer fieldId;

    /** 字段值
    1、单行文本框的值为用户填写的内容
    2、多行文本框的值为用户填写的内容
    3、下拉框的值为用户选择的内容
    4、组合单选框的值为用户选择的内容
    5、组合多选框的值为用户选择的内容，多个选项逗号分隔
    , 数据库字段：field_value */
    private String fieldValue;

    /** 设置表单id, 数据库字段：form_flow_record_data.form_id */
    public void setFormId(Integer formId) {
        this.formId = formId;
    }

    /** 获取表单id, 数据库字段：form_flow_record_data.form_id */
    public Integer getFormId() {
        return this.formId;
    }

    /** 设置表单流水记录id, 数据库字段：form_flow_record_data.form_flow_record_id */
    public void setFormFlowRecordId(Integer formFlowRecordId) {
        this.formFlowRecordId = formFlowRecordId;
    }

    /** 获取表单流水记录id, 数据库字段：form_flow_record_data.form_flow_record_id */
    public Integer getFormFlowRecordId() {
        return this.formFlowRecordId;
    }

    /** 设置字段id, 数据库字段：form_flow_record_data.field_id */
    public void setFieldId(Integer fieldId) {
        this.fieldId = fieldId;
    }

    /** 获取字段id, 数据库字段：form_flow_record_data.field_id */
    public Integer getFieldId() {
        return this.fieldId;
    }

    /** 设置字段值
    1、单行文本框的值为用户填写的内容
    2、多行文本框的值为用户填写的内容
    3、下拉框的值为用户选择的内容
    4、组合单选框的值为用户选择的内容
    5、组合多选框的值为用户选择的内容，多个选项逗号分隔
    , 数据库字段：form_flow_record_data.field_value */
    public void setFieldValue(String fieldValue) {
        this.fieldValue = fieldValue;
    }

    /** 获取字段值
    1、单行文本框的值为用户填写的内容
    2、多行文本框的值为用户填写的内容
    3、下拉框的值为用户选择的内容
    4、组合单选框的值为用户选择的内容
    5、组合多选框的值为用户选择的内容，多个选项逗号分隔
    , 数据库字段：form_flow_record_data.field_value */
    public String getFieldValue() {
        return this.fieldValue;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = (prime * result) +
            ((formFlowRecordId == null) ? 0 : formFlowRecordId.hashCode());

        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        if (obj == null) {
            return false;
        }

        if (getClass() != obj.getClass()) {
            return false;
        }

        FormFlowRecordData other = (FormFlowRecordData) obj;

        if (formFlowRecordId == null) {
            if (other.formFlowRecordId != null) {
                return false;
            }
        } else if (!formFlowRecordId.equals(other.formFlowRecordId)) {
            return false;
        }

        return true;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("FormFlowRecordData [");
        sb.append("formId=").append(formId);
        sb.append(", ");
        sb.append("formFlowRecordId=").append(formFlowRecordId);
        sb.append(", ");
        sb.append("fieldId=").append(fieldId);
        sb.append(", ");
        sb.append("fieldValue=").append(fieldValue);
        sb.append("]");

        return sb.toString();
    }
}
