package com.taurus.formSys.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;


/**
 * 表名：form_flow_record
 * 备注：表单数据记录
 *
 * <AUTHOR>
 */
@Table(name = "form_flow_record")
public class FormFlowRecord {
    /** 流水记录id, 数据库字段：id */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /** 表单id, 数据库字段：form_id */
    private Integer formId;

    /** 填报人id, 数据库字段：user_id */
    private Integer userId;

    /** 创建时间, 数据库字段：create_time */
    private Date createTime;
    
    /** 是否已删除, 数据库字段：enabled */
    private Boolean enabled;
    
    /** 设置流水记录id, 数据库字段：form_flow_record.id */
    public void setId(Integer id) {
        this.id = id;
    }

    /** 获取流水记录id, 数据库字段：form_flow_record.id */
    public Integer getId() {
        return this.id;
    }

    /** 设置表单id, 数据库字段：form_flow_record.form_id */
    public void setFormId(Integer formId) {
        this.formId = formId;
    }

    /** 获取表单id, 数据库字段：form_flow_record.form_id */
    public Integer getFormId() {
        return this.formId;
    }

    /** 设置填报人id, 数据库字段：form_flow_record.user_id */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /** 获取填报人id, 数据库字段：form_flow_record.user_id */
    public Integer getUserId() {
        return this.userId;
    }

    /** 设置创建时间, 数据库字段：form_flow_record.create_time */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /** 获取创建时间, 数据库字段：form_flow_record.create_time */
    public Date getCreateTime() {
        return this.createTime;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = (prime * result) + ((id == null) ? 0 : id.hashCode());

        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        if (obj == null) {
            return false;
        }

        if (getClass() != obj.getClass()) {
            return false;
        }

        FormFlowRecord other = (FormFlowRecord) obj;

        if (id == null) {
            if (other.id != null) {
                return false;
            }
        } else if (!id.equals(other.id)) {
            return false;
        }

        return true;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("FormFlowRecord [");
        sb.append("id=").append(id);
        sb.append(", ");
        sb.append("formId=").append(formId);
        sb.append(", ");
        sb.append("userId=").append(userId);
        sb.append(", ");
        sb.append("createTime=").append(createTime);
        sb.append("]");

        return sb.toString();
    }

	public Boolean getEnabled() {
		return enabled;
	}

	public void setEnabled(Boolean enabled) {
		this.enabled = enabled;
	}
}
