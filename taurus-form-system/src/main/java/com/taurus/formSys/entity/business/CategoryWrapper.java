/**
 * 用户前端展示类别的树状结构补充的BO
 */
package com.taurus.formSys.entity.business;

import com.taurus.formSys.entity.Category;

public class CategoryWrapper extends Category {
	/**折叠状态**/
	private Boolean collapsed;
	
	/**是否选中状态**/
	private Boolean selected;
	
	/**是否处于编辑状态**/
	private Boolean edited;
	
	/**包含内容的数量**/
	private Integer contentNum;
	
	
	public Boolean getCollapsed() {
		return collapsed;
	}
	public void setCollapsed(Boolean collapsed) {
		this.collapsed = collapsed;
	}
	public Boolean getSelected() {
		return selected;
	}
	public void setSelected(<PERSON><PERSON><PERSON> selected) {
		this.selected = selected;
	}
	public Boolean getEdited() {
		return edited;
	}
	public void setEdited(<PERSON><PERSON><PERSON> edited) {
		this.edited = edited;
	}
	public Integer getContentNum() {
		return contentNum;
	}
	public void setContentNum(Integer contentNum) {
		this.contentNum = contentNum;
	}
}
