package com.taurus.formSys.entity;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;


/**
 * 表名：form_field_structure
 *
 * <AUTHOR>
 */
@Table(name = "form_field_structure")
public class FormFieldStructure {
    /** 表单字段id, 数据库字段：id */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /** 表单定义表的id, 数据库字段：form_id */
    private Integer formId;

    /** 表单字段名称, 数据库字段：field_name */
    private String fieldName;

    /** 表单字段类型：1表示单行input；2表示多行textarea；3表示下拉框；4表示组合单选框；5表示组合多选框，其他详见字段类型表, 数据库字段：field_type */
    private Integer fieldType;

    /** 字段初始值列表
    1、单行文本框：{defaultValue:'中国上海'}
    2、多行文本框：{defaultValue:'上海位于中国东方。'}
    3、下拉框：[{text:"行政部",isSelected:"false"},{text:"财务部",isSelected:"false"},{text:"技术部",isSelected:"false"}]
    4、单选框：[{text:"行政部",isSelected:"false"},{text:"财务部",isSelected:"false"},{text:"技术部",isSelected:"false"}]
    5、组合多选框：[{text:"行政部",isSelected:"false"},{text:"财务部",isSelected:"false"},{text:"技术部",isSelected:"false"}]

    isSelected标明默认值, 数据库字段：field_init_value */
    private String fieldInitValue;

    /** 字段显示顺序（自上而下）, 数据库字段：field_seq */
    private Integer fieldSeq;

    /** 字段属性定义描述
    1、单行文本框：{notEmpty:true}
    2、多行文本框：{notEmpty:true}
    3、下拉框：{notEmpty:true}
    4、组合单选框：{notEmpty:true,arrange:"vertical"}
    5、组合多选框：{notEmpty:true,arrange:"vertical"}, 数据库字段：format */
    private String format;

    /** 字段显示出来的前置条件（默认为null，代表直接显示，其他内容为前置条件表达式）前置条件表达式, 数据库字段：display_preconditions */
    private String displayPreconditions;

    /** 表单字段验证要求
    提交表单时会对表单进行验证，验证以下方面：
    验证包含格式验证和内容验证两部分；
    验证分为本地验证和联网型验证；
    为null表示不需要验证；

    A表示本地内容列表验证；B表示不能为空；C表示远程内容列表验证, 数据库字段：validation_type */
    private String validationType;

    /** 验证内容列表，多个用,间隔, 数据库字段：validation_content */
    private String validationContent;

    /** 是否已删除, 数据库字段：enabled */
    private Boolean enabled;

    /** 设置表单字段id, 数据库字段：form_field_structure.id */
    public void setId(Integer id) {
        this.id = id;
    }

    /** 获取表单字段id, 数据库字段：form_field_structure.id */
    public Integer getId() {
        return this.id;
    }

    /** 设置表单定义表的id, 数据库字段：form_field_structure.form_id */
    public void setFormId(Integer formId) {
        this.formId = formId;
    }

    /** 获取表单定义表的id, 数据库字段：form_field_structure.form_id */
    public Integer getFormId() {
        return this.formId;
    }

    /** 设置表单字段名称, 数据库字段：form_field_structure.field_name */
    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    /** 获取表单字段名称, 数据库字段：form_field_structure.field_name */
    public String getFieldName() {
        return this.fieldName;
    }

    /** 设置表单字段类型：1表示单行input；2表示多行textarea；3表示下拉框；4表示组合单选框；5表示组合多选框，其他详见字段类型表, 数据库字段：form_field_structure.field_type */
    public void setFieldType(Integer fieldType) {
        this.fieldType = fieldType;
    }

    /** 获取表单字段类型：1表示单行input；2表示多行textarea；3表示下拉框；4表示组合单选框；5表示组合多选框，其他详见字段类型表, 数据库字段：form_field_structure.field_type */
    public Integer getFieldType() {
        return this.fieldType;
    }

    /** 设置字段初始值列表
    1、单行文本框：{defaultValue:'中国上海'}
    2、多行文本框：{defaultValue:'上海位于中国东方。'}
    3、下拉框：[{text:"行政部",isSelected:"false"},{text:"财务部",isSelected:"false"},{text:"技术部",isSelected:"false"}]
    4、单选框：[{text:"行政部",isSelected:"false"},{text:"财务部",isSelected:"false"},{text:"技术部",isSelected:"false"}]
    5、组合多选框：[{text:"行政部",isSelected:"false"},{text:"财务部",isSelected:"false"},{text:"技术部",isSelected:"false"}]

    isSelected标明默认值, 数据库字段：form_field_structure.field_init_value */
    public void setFieldInitValue(String fieldInitValue) {
        this.fieldInitValue = fieldInitValue;
    }

    /** 获取字段初始值列表
    1、单行文本框：{defaultValue:'中国上海'}
    2、多行文本框：{defaultValue:'上海位于中国东方。'}
    3、下拉框：[{text:"行政部",isSelected:"false"},{text:"财务部",isSelected:"false"},{text:"技术部",isSelected:"false"}]
    4、单选框：[{text:"行政部",isSelected:"false"},{text:"财务部",isSelected:"false"},{text:"技术部",isSelected:"false"}]
    5、组合多选框：[{text:"行政部",isSelected:"false"},{text:"财务部",isSelected:"false"},{text:"技术部",isSelected:"false"}]

    isSelected标明默认值, 数据库字段：form_field_structure.field_init_value */
    public String getFieldInitValue() {
        return this.fieldInitValue;
    }

    /** 设置字段显示顺序（自上而下）, 数据库字段：form_field_structure.field_seq */
    public void setFieldSeq(Integer fieldSeq) {
        this.fieldSeq = fieldSeq;
    }

    /** 获取字段显示顺序（自上而下）, 数据库字段：form_field_structure.field_seq */
    public Integer getFieldSeq() {
        return this.fieldSeq;
    }

    /** 设置字段属性定义描述
    1、单行文本框：{notEmpty:true}
    2、多行文本框：{notEmpty:true}
    3、下拉框：{notEmpty:true}
    4、组合单选框：{notEmpty:true,arrange:"vertical"}
    5、组合多选框：{notEmpty:true,arrange:"vertical"}, 数据库字段：form_field_structure.format */
    public void setFormat(String format) {
        this.format = format;
    }

    /** 获取字段属性定义描述
    1、单行文本框：{notEmpty:true}
    2、多行文本框：{notEmpty:true}
    3、下拉框：{notEmpty:true}
    4、组合单选框：{notEmpty:true,arrange:"vertical"}
    5、组合多选框：{notEmpty:true,arrange:"vertical"}, 数据库字段：form_field_structure.format */
    public String getFormat() {
        return this.format;
    }

    /** 设置字段显示出来的前置条件（默认为null，代表直接显示，其他内容为前置条件表达式）前置条件表达式, 数据库字段：form_field_structure.display_preconditions */
    public void setDisplayPreconditions(String displayPreconditions) {
        this.displayPreconditions = displayPreconditions;
    }

    /** 获取字段显示出来的前置条件（默认为null，代表直接显示，其他内容为前置条件表达式）前置条件表达式, 数据库字段：form_field_structure.display_preconditions */
    public String getDisplayPreconditions() {
        return this.displayPreconditions;
    }

    /** 设置表单字段验证要求
    提交表单时会对表单进行验证，验证以下方面：
    验证包含格式验证和内容验证两部分；
    验证分为本地验证和联网型验证；
    为null表示不需要验证；

    A表示本地内容列表验证；B表示不能为空；C表示远程内容列表验证, 数据库字段：form_field_structure.validation_type */
    public void setValidationType(String validationType) {
        this.validationType = validationType;
    }

    /** 获取表单字段验证要求
    提交表单时会对表单进行验证，验证以下方面：
    验证包含格式验证和内容验证两部分；
    验证分为本地验证和联网型验证；
    为null表示不需要验证；

    A表示本地内容列表验证；B表示不能为空；C表示远程内容列表验证, 数据库字段：form_field_structure.validation_type */
    public String getValidationType() {
        return this.validationType;
    }

    /** 设置验证内容列表，多个用,间隔, 数据库字段：form_field_structure.validation_content */
    public void setValidationContent(String validationContent) {
        this.validationContent = validationContent;
    }

    /** 获取验证内容列表，多个用,间隔, 数据库字段：form_field_structure.validation_content */
    public String getValidationContent() {
        return this.validationContent;
    }

    /** 设置是否已删除, 数据库字段：form_field_structure.enabled */
    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    /** 获取是否已删除, 数据库字段：form_field_structure.enabled */
    public Boolean getEnabled() {
        return this.enabled;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = (prime * result) + ((id == null) ? 0 : id.hashCode());

        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        if (obj == null) {
            return false;
        }

        if (getClass() != obj.getClass()) {
            return false;
        }

        FormFieldStructure other = (FormFieldStructure) obj;

        if (id == null) {
            if (other.id != null) {
                return false;
            }
        } else if (!id.equals(other.id)) {
            return false;
        }

        return true;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("FormFieldStructure [");
        sb.append("id=").append(id);
        sb.append(", ");
        sb.append("formId=").append(formId);
        sb.append(", ");
        sb.append("fieldName=").append(fieldName);
        sb.append(", ");
        sb.append("fieldType=").append(fieldType);
        sb.append(", ");
        sb.append("fieldInitValue=").append(fieldInitValue);
        sb.append(", ");
        sb.append("fieldSeq=").append(fieldSeq);
        sb.append(", ");
        sb.append("format=").append(format);
        sb.append(", ");
        sb.append("displayPreconditions=").append(displayPreconditions);
        sb.append(", ");
        sb.append("validationType=").append(validationType);
        sb.append(", ");
        sb.append("validationContent=").append(validationContent);
        sb.append(", ");
        sb.append("enabled=").append(enabled);
        sb.append("]");

        return sb.toString();
    }
    
}
