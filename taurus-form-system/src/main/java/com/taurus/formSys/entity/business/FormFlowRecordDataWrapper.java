package com.taurus.formSys.entity.business;

import java.util.Date;

import com.taurus.formSys.entity.FormFlowRecordData;

public class Form<PERSON>lowRecordDataWrapper extends FormFlowRecordData {
	
	/**填表用户id**/
	private Integer userId;
	
	/**字段名**/
	private String fieldName;
	
	/**字段类型**/
	private Integer fieldType;
	
	/**表单记录时间**/
	private Date createTime;
	
	public String getFieldName() {
		return fieldName;
	}
	public void setFieldName(String fieldName) {
		this.fieldName = fieldName;
	}
	public Integer getFieldType() {
		return fieldType;
	}
	public void setFieldType(Integer fieldType) {
		this.fieldType = fieldType;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public Integer getUserId() {
		return userId;
	}
	public void setUserId(Integer userId) {
		this.userId = userId;
	}
	
	
	

}
