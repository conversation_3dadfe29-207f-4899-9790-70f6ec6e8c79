package com.taurus.formSys;

import javax.servlet.Filter;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.taurus.formSys.filter.LocationFilter;

@Configuration("formSysWebFilterConfig")
public class WebFilterConfig {

    // @Bean
    // public FilterRegistrationBean<Filter> corsFilterRegistration() {
    // FilterRegistrationBean<Filter> registration = new FilterRegistrationBean<>();
    // registration.setFilter(corsFilter());
    // registration.addUrlPatterns("/*");
    // registration.setName("CorsFilter");
    // registration.setOrder(1);
    // return registration;
    // }

    @Bean("formSysLocationFilterRegistration")
    FilterRegistrationBean<Filter> locationFilterRegistration() {
        FilterRegistrationBean<Filter> registration = new FilterRegistrationBean<>();
        registration.setFilter(locationFilter());
        registration.addUrlPatterns("/examination/*");
        registration.setName("locationFilter");
        registration.setOrder(3);
        return registration;
    }

    @Bean("formSysLocationFilter")
    Filter locationFilter() {
        return new LocationFilter();
    }

}
