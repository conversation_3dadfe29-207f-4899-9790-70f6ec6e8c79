package com.taurus.formSys.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.gitee.fastmybatis.core.mapper.CrudMapper;
import com.taurus.formSys.entity.FormDefinition;

public interface FormDefinitionMapper extends CrudMapper<FormDefinition, Integer> {

	@Select("<script>"
			+ "select t.id as formId,t.product_name as productName,t.title,t.explanation,t.redirect_type as redirectType,t.redirect_url_after_submit as redirectUrlAfterSubmit,t.valid_start_time as validStartTime,t.valid_end_time as validEndTime,"
			+ "t.per_user_submit_max_times as perUserSubmitMaxTimes,t.submit_max_times as submitMaxTimes,t.create_time as createTime,t.enabled,t.form_height as formHeight,t.bgcolor,t.sponsor,"
			+ "t1.id,t1.field_name as fieldName,t1.field_type as fieldType,t1.field_init_value as fieldInitValue,t1.field_seq as fieldSeq,t1.format,t1.display_preconditions as displayPreconditions,t1.validation_type as validationType,"
			+ "t1.validation_content as validationContent,t.bg_image_object_name as bgImageObjectName,t.bg_audio_object_name as bgAudioObjectName from form_definition t left join form_field_structure t1 ON t.id=t1.form_id  where t.id=#{formId} order by t1.field_seq asc "
			+ "</script>")
	List<Map<String, Object>> getFormFieldWrapperById(Integer formId);

	@Select("<script>"
			+ "select t.id as formId,t.product_name as productName,t.title,t.explanation,t.redirect_type as redirectType,t.redirect_url_after_submit as redirectUrlAfterSubmit,t.valid_start_time as validStartTime,t.valid_end_time as validEndTime,"
			+ "t.per_user_submit_max_times as perUserSubmitMaxTimes,t.submit_max_times as submitMaxTimes,t.create_time as createTime,t.enabled,t.form_height as formHeight,t.bgcolor,"
			+ "t1.id,t1.field_name as fieldName,t1.field_type as fieldType,t1.field_init_value as fieldInitValue,t1.field_seq as fieldSeq,t1.format,t1.display_preconditions as displayPreconditions,t1.validation_type as validationType,"
			+ "t1.validation_content as validationContent,t.bg_image_object_name as bgImageObjectName,t.bg_audio_object_name as bgAudioObjectName from form_definition t left join form_field_structure t1 ON t.id=t1.form_id "
			+ " where 1=1 "
			+ "<foreach item='item' index='index' collection='ids' open=' and t.id in (' separator=',' close=')'>"
			+ "#{item}"
			+ "</foreach>"
			+ " order by t1.field_seq asc "
			+ "</script>")
	List<Map<String, Object>> getFormDefinitionAndFieldListByIds(@Param(value="ids") List<Integer> ids);
}
