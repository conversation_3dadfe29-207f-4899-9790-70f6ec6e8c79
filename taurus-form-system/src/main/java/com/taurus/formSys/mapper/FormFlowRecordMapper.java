package com.taurus.formSys.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.gitee.fastmybatis.core.mapper.CrudMapper;
import com.taurus.formSys.entity.FormFlowRecord;


public interface FormFlowRecordMapper extends CrudMapper<FormFlowRecord, Integer> {

	/**
	 * 获取多个form的已填报的表单总数
	 * 
	 * @param ids
	 * @return
	 */
	@Select("<script>" 
			+ "select count(*) as totalNum,form_id as id from form_flow_record where enabled=true and form_id in "
			+ "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'>" 
				+ "#{item}"
			+ "</foreach>"
			+ " group by form_id" 
			+ "</script>")

	List<Map<String, Object>> getFormFlowRecordSummaryPerForm(@Param("ids") List<Integer> ids);

	@Update("update form_flow_record set enabled=false where form_id=#{formId}")
	void clear(@Param("formId") Integer formId);
}
