package com.taurus.formSys.mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.gitee.fastmybatis.core.mapper.CrudMapper;
import com.taurus.formSys.entity.FormFlowRecordData;


public interface FormFlowRecordDataMapper extends CrudMapper<FormFlowRecordData, String> {
	/**
	 * 获取多条表单填写记录详情
	 * @param idsList
	 * @return
	 */
	@Select("<script>"
			+ "<foreach item='item' index='index' collection='idsList' open='' separator='union all' close=''>"
			+ " (select form_flow_record_id as formFlowRecordId,form_id as formId,field_id as fieldId,field_value as fieldValue from form_flow_record_data where form_flow_record_id=#{item}) "
			+ "</foreach>"
			+ "</script>")
	List<Map<String, Object>> getFormFlowRecordWrapperByIds(@Param("idsList") List<Integer> idsList);

	/**
	 * 获取多条表单填写记录
	 * @param idsList
	 * @return
	 */

	@Select("<script>"
			+ "select t.form_flow_record_id as formFlowRecordId,t.form_id as formId,t1.id as fieldId,ifnull(t.field_value,'') as fieldValue,t1.field_type as fieldType,t1.field_name as fieldName from form_flow_record_data t "
			+ "LEFT JOIN form_field_structure t1 ON t.field_id =t1.id and t1.enabled=true where 1=1 "
			+ "<foreach item='item' index='index' collection='idsList' open=' and t.form_flow_record_id in (' separator=',' close=')'>"
			+ "#{item}"
			+ "</foreach>"
			+ " order by t1.field_seq asc "
			+ "</script>")
	List<Map<String, Object>> getFormFlowRecordDataByIds(@Param("idsList") List<Integer> idsList);

	/**
	 * 获取某个formId的记录
	 * @param formId
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	@Select("<script>"
			+ "select t.form_flow_record_id as formFlowRecordId,t.form_id as formId,t1.id as fieldId,ifnull(t.field_value,'') as fieldValue,t1.field_type as fieldType,t1.field_name as fieldName,t2.user_id as userId,t2.create_time as createTime from form_flow_record_data t "
			+ "RIGHT JOIN form_field_structure t1 ON t.field_id =t1.id and t1.enabled=true "
			+ "LEFT JOIN form_flow_record t2 ON t2.id = t.form_flow_record_id "
			+ "where t.form_id= #{formId} "
			+ "<if test=\"beginTime!=null and endTime!=null\">"
			+ "and t2.create_time between #{beginTime} and #{endTime} "
			+ "</if>"
			+ " order by t1.field_seq asc "
			+ "</script>")
	List<Map<String, Object>> getFormFlowRecordDataByFormId(Integer formId, Date beginTime, Date endTime);

	
	/**
	 * 获取某个表单各字段填写内容的分布式情况
	 * @param formId
	 * @return
	 */
	@Select("select t.field_id as fieldId, t.field_value as fieldValue,count(t.field_value) as num from form_flow_record_data t left join form_flow_record t1 ON t.form_flow_record_id=t1.id where t.form_id=#{formId} and t1.enabled=true group by t.field_id,t.field_value order by t.field_id asc")
	List<Map<String, Object>> getDistrbutionData(Integer formId);
}
