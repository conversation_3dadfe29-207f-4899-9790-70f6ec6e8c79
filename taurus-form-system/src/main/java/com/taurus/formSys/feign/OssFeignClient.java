package com.taurus.formSys.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.taurus.entity.ResponseObject;

@FeignClient(value = "taurus-oss") //这里的name对应调用服务的spring.application.name
@Component
public interface OssFeignClient {

	/**
	 * 获取objectName对应的url
	 * @param formId
	 * @return
	 */
    @GetMapping("/ossObject/getAccessUrl")
    public ResponseObject getAccessUrl(@RequestParam("respositoryName") String respositoryName,@RequestParam("objectName") String objectName);
    
}
