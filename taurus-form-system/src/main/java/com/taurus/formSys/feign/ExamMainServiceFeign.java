package com.taurus.formSys.feign;

import javax.servlet.http.HttpServletRequest;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.alibaba.fastjson.JSONObject;
import com.taurus.entity.ResponseObject;

@FeignClient(value = "exam-main-service") //这里的name对应调用服务的spring.application.name
@Component
public interface ExamMainServiceFeign {

	/**
	 * 获取objectName对应的url
	 * @param formId
	 * @return
	 */
    @PostMapping("/weixinAccount/msgSecCheck")
    public ResponseObject msgSecCheck(@RequestBody JSONObject body);
    
    @PostMapping("/weixinAccount/imgSecCheck")
    public ResponseObject imgSecCheck(HttpServletRequest request); 
}
