package com.taurus.formSys.filter;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletResponse;


public class Cors<PERSON>ilter implements Filter {

	@Override
	public void init(FilterConfig filterConfig) throws ServletException {
		

	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
			throws IOException, ServletException {
		request.setCharacterEncoding("utf-8");
		response.setCharacterEncoding("utf-8");
		HttpServletResponse res = (HttpServletResponse) response;
		res.setHeader("Access-Control-Allow-Origin", "*");
		res.setHeader("Access-Control-Allow-Methods", "POST, GET, OPTIONS, DELETE");
		res.setHeader("Access-Control-Max-Age", "3600");
		res.addHeader("Access-Control-Allow-Headers",
				"enctype,Origin, X-Requested-With, Content-Type, Accept, Authorization, Access-Control-Allow-Credentials");
		res.addHeader("Access-Control-Allow-Credentials", "true");
		//某些客户端需要服务器指定可以显示的头列表，如axios
		res.addHeader("Access-Control-Expose-Headers","Content-Disposition");
		
		/**
		 * 调用下一个Filter或者在没有下一个Filter的情况下,调用请求终点站——Servlet
		 */
		chain.doFilter(request, response);
		
		
		
	}

	@Override
	public void destroy() {

	}

}
