package com.taurus.formSys.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.gitee.fastmybatis.core.query.Query;
import com.gitee.fastmybatis.core.query.Sort;
import com.gitee.fastmybatis.core.util.MyBeanUtil;
import com.taurus.formSys.entity.FormFieldStructure;
import com.taurus.formSys.entity.FormFlowRecordData;
import com.taurus.formSys.entity.business.FormDefinitionWrapper;
import com.taurus.formSys.mapper.FormFlowRecordDataMapper;
import com.taurus.oss.aliyun.OssOperation;
import com.taurus.oss.aliyun.OssRespository;

@Service
public class FormFlowRecordDataService {

	@Autowired
	private FormFlowRecordDataMapper formFlowRecordDataMapper;

	@Autowired
	private FormFieldStructureService formFieldStructureService;

	@Autowired
	private RedisService redisService;

	public void save(FormFlowRecordData record) {
		formFlowRecordDataMapper.saveIgnoreNull(record);
	}

	public void batchSave(List<FormFlowRecordData> entityList) {
		formFlowRecordDataMapper.saveBatch(entityList);
	}

	/**
	 * 根据form_flow_record_id和field_id更新
	 * 
	 * @param record
	 * @param map
	 */
	public void upInsert(FormFlowRecordData record, HashMap<String, Object> map) {
		Query query = new Query().eq("form_flow_record_id", record.getFormFlowRecordId()).eq("field_id",
				record.getFieldId());
		FormFlowRecordData entity = formFlowRecordDataMapper.getByQuery(query);
		if (entity != null) {
			formFlowRecordDataMapper.updateByMap(map, query);
		} else {
			formFlowRecordDataMapper.saveIgnoreNull(record);
		}
	}

	/**
	 * 获取某个form的用户填写记录
	 * 
	 * @param formId
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	public Map<String, List<Map<String, Object>>> getFormFlowRecordDataGroupByFormFlowRecordId(Integer formId,
			Date beginTime, Date endTime) {
		List<Map<String, Object>> mapList = formFlowRecordDataMapper.getFormFlowRecordDataByFormId(formId, beginTime,
				endTime);
		mapList = mapList.stream().filter(n -> isWritableField(n)).collect(Collectors.toList());// 过滤出可填写字段
		
		List<Map<String,Object>> filterList =  mapList.stream().filter(n->{String userId = String.valueOf(n.get("userId"));
		
				if(userId!=null&&userId.equals("1500869")) {
					return true;
				}else {
					return false;
				}
		}).collect(Collectors.toList());
		
		Map<String, List<Map<String, Object>>> map = mapList.stream()
				.collect(Collectors.groupingBy(n -> String.valueOf((Integer) n.get("formFlowRecordId"))));
		return map;
	}

	/**
	 * 获取多个表单的填写记录
	 * 
	 * @param idsList
	 * @return
	 */
	public Map<String, List<Map<String, Object>>> getFormFlowRecordDataByIdsGroupByFormFlowRecordId(
			List<Integer> idsList) {
		List<Map<String, Object>> mapList = formFlowRecordDataMapper.getFormFlowRecordDataByIds(idsList);
		mapList = mapList.stream().filter(n -> isWritableField(n)).collect(Collectors.toList());
		mapList = mapList.stream().map(n->{
			Object fieldType = n.get("fieldType");
			if(fieldType.toString().equals("7")||fieldType.toString().equals("8")||fieldType.toString().equals("10")||fieldType.toString().equals("11")) {
				String fieldValue = (String) n.get("fieldValue");
				JSONArray fieldValueArray = JSON.parseArray(fieldValue);
				for(int i=0;i<fieldValueArray.size();i++) {
					JSONObject field =  fieldValueArray.getJSONObject(i);
					String ossBucketName = field.getString("ossBucketName");
					String objectName = field.getString("objectName");
					OssRespository respository = OssRespository.getRespositoryByProductName(ossBucketName);
					String type="F";
					if(fieldType.toString().equals("8")) {
						type="A";
					}else if(fieldType.toString().equals("10")) {
						type="P";
					}else if(fieldType.toString().equals("11")) {
						type="V";
					}
					String url = OssOperation.generateAccessUrl(respository , objectName, type);
					field.put("src", url);
				}
				n.put("fieldValue", fieldValueArray);
			}
			return n;
		}).collect(Collectors.toList());
		
		Map<String, List<Map<String, Object>>> map = mapList.stream()
				.collect(Collectors.groupingBy(n -> String.valueOf((Integer) n.get("formFlowRecordId"))));
		return map;
	}


	/**
	 * 判断是否是可写的组件
	 * 1表示单行input；2表示多行textarea；3表示下拉框；4表示组合单选框；5表示组合多选框；6表示文本段落；7表示上传文件，8表示上传录音；9表示上传定位；10表示上传图片；11表示上传视频；12表示省市区；13表示日期；14表示时间；15表示穷举；2x为企考考内置组件：20姓名；21手机号；22工号；23身份证号；24邮箱；25性别；26备注；27部门；28职位；29角色
	 * @param n
	 * @return
	 */
	private Boolean isWritableField(Map<String, Object> n) {
		Object type = n.get("fieldType");
		if (type instanceof String) {
			String fieldType = (String) type;
		
			if (fieldType.equals("1") || fieldType.equals("2") || fieldType.equals("3") || fieldType.equals("4")
					|| fieldType.equals("5") || fieldType.equals("6")|| fieldType.equals("7") || fieldType.equals("8")
					|| fieldType.equals("9") || fieldType.equals("10")|| fieldType.equals("11")|| fieldType.equals("12") || fieldType.equals("13")|| fieldType.equals("14")|| fieldType.equals("15")) {
				return true;
			}
			//企考考中的字段定义：
			if (fieldType.equals("90") || fieldType.equals("20") || fieldType.equals("21") || fieldType.equals("22")
					|| fieldType.equals("23") || fieldType.equals("24") || fieldType.equals("25")
					|| fieldType.equals("26") || fieldType.equals("27") || fieldType.equals("28")
					|| fieldType.equals("29")) {
				return true;
			}
			//天天问卷中的字段定义：
			// 31表示单行填空，32多行填空 33区间值选择 34文本型单选 35文本型多选 36图片型单选 37图片型多选
			if (fieldType.equals("31") || fieldType.equals("32") || fieldType.equals("33") || fieldType.equals("34")
					|| fieldType.equals("35") || fieldType.equals("36") || fieldType.equals("37")) {
				return true;
			}
		} else if (type instanceof Integer) {
			Integer fieldType = (Integer) type;
			// 1表示单行文本框，2多行文本框 3下拉框 4单选框 5多选框 6文本段落
			if (fieldType >= 1 &&fieldType<=15) {
				return true;
			}
			
			if (fieldType >= 20 && fieldType <= 29) {
				return true;
			}

			// 31表示单行填空，32多行填空 33区间值选择 34文本型单选 35文本型多选 36图片型单选 37图片型多选
			if (fieldType == 31 || fieldType == 32 || fieldType == 33 || fieldType == 34 || fieldType == 35
					|| fieldType == 36 || fieldType == 37) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 获取表单每个字段填写内容的分布式情况
	 * 
	 * @param formId
	 * @return
	 */
	public List<Map<String, Object>> getDistrbutionAnalysisData(Integer formId) {

		List<Map<String, Object>> distrbutionList = formFlowRecordDataMapper.getDistrbutionData(formId);

		Map<String, List<Map<String, Object>>> listMap = distrbutionList.stream()
				.collect(Collectors.groupingBy(n -> String.valueOf(n.get("fieldId"))));

		StringBuffer keyBuffer = new StringBuffer().append("newFormWrapper").append(formId);
		String value = redisService.get(keyBuffer.toString());

		List<FormFieldStructure> fieldList = null;
		if (StringUtils.isEmpty(value)) {
			fieldList = formFieldStructureService.getFormFieldStructureListByFormId(formId);
		} else {
			FormDefinitionWrapper wrapper = JSON.parseObject(value, FormDefinitionWrapper.class);
			fieldList = wrapper.getFieldList();
		}

		List<Map<String, Object>> list = new ArrayList<>();
		fieldList.forEach(item -> {
			Map<String, Object> fieldMap = new HashMap<>();
			fieldMap.put("fieldType", item.getFieldType());
			fieldMap.put("fieldName", item.getFieldName());
			fieldMap.put("fieldInitValue", item.getFieldInitValue());

			Integer id = item.getId();
			List<Map<String, Object>> distrbutionListOfField = listMap.get(String.valueOf(id));
			if (distrbutionListOfField == null) {
				distrbutionListOfField = new ArrayList<>();
				Map<String, Object> defaultMap = new HashMap<>();
				defaultMap.put("fieldValue", "");
				defaultMap.put("num", 0);
				distrbutionListOfField.add(defaultMap);
			}
			fieldMap.put("distrbutionData", distrbutionListOfField);
			list.add(fieldMap);
		});
		return list;
	}

	/**
	 * 删除某次表单填写的记录
	 * 
	 * @param id
	 */
	public void batchDelete(Integer id) {
		Query query = new Query().eq("form_flow_record_id", id);
		formFlowRecordDataMapper.deleteByQuery(query);
	}

	/**
	 * 获取某次表单填写的详情数据（包括表单结构和填写的内容）
	 * 
	 * @param id
	 * @return
	 */
	public List<Map<String, Object>> getListByFormFlowRecordId(Integer id) {
		Query query = new Query();
		query.join("LEFT JOIN form_field_structure t1 ON t.field_id = t1.id").eq("t.form_flow_record_id", id)
				.orderby("t1.field_seq", Sort.ASC);
		List<String> colums = Arrays.asList("t.form_id as formId", "t.form_flow_record_id as formFlowRecordId",
				"t.field_id as fieldId", "t.field_value as fieldValue", "t1.field_name as fieldName",
				"t1.field_type as fieldType", "t1.field_init_value as fieldInitValue", "t1.format");
		return formFlowRecordDataMapper.listMap(colums, query);
	}

	public Boolean isWritableField(FormFieldStructure n) {
		return isWritableField(MyBeanUtil.pojoToMap(n));
	}

}
