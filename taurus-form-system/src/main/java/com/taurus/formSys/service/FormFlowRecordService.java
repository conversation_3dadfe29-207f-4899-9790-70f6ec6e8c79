package com.taurus.formSys.service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.gitee.fastmybatis.core.query.Query;
import com.gitee.fastmybatis.core.util.MyBeanUtil;
import com.taurus.formSys.entity.FormFlowRecord;
import com.taurus.formSys.entity.FormFlowRecordData;
import com.taurus.formSys.entity.business.FormFlowRecordWrapper;
import com.taurus.formSys.mapper.FormFlowRecordMapper;

@Service
public class FormFlowRecordService {

	@Autowired
	private FormFlowRecordMapper formFlowRecordMapper;
	
	public FormFlowRecord save(FormFlowRecord entity) {
		formFlowRecordMapper.saveIgnoreNull(entity);	
		return entity;
	}
	
	public FormFlowRecord getById(Integer id) {
		return formFlowRecordMapper.getById(id);
	}
	
	public void  update(FormFlowRecord record) {
		formFlowRecordMapper.updateIgnoreNull(record);
	}

	/**
	 * 获取某次表单填写的详细内容
	 * @param formFlowRecordId
	 * @return
	 */
	public FormFlowRecordWrapper getFormFlowRecordWrapperById(Integer formFlowRecordId) {
		List<String> columns= Arrays.asList("t.user_id as userId","t.create_time as createTime","t.id as formFlowRecordId","t.form_id as formId","t1.field_id as fieldId","t1.field_value as fieldValue");
		Query query=new Query().join("Left join form_flow_record_data t1 ON t.id=t1.form_flow_record_id").eq("t.id", formFlowRecordId);
		List<Map<String,Object>> mapList = formFlowRecordMapper.listMap(columns, query);
		if(mapList.size()>0) {
			Map<String,Object> map = mapList.get(0);
			FormFlowRecordWrapper wrapper = new FormFlowRecordWrapper();
			wrapper.setId(formFlowRecordId);
			wrapper.setCreateTime((Date)map.get("createTime"));
			wrapper.setUserId((Integer)map.get("userId"));
			wrapper.setFormId((Integer)map.get("formId"));
			List<FormFlowRecordData> dataList = mapList.stream().map(n->{return extractAsFormFlowRecordData(n);}).collect(Collectors.toList());
			wrapper.setFormFlowRecordDataList(dataList);
			return wrapper;
		}
		return null;
	}
	/**
	 * 获取填报记录的数量
	 * @param formId
	 * @param userId
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	public Long getNumber(Integer formId,Integer userId,Date beginTime,Date endTime) {
		Query query = new Query();
		if(formId!=null)
			query.eq("form_id", formId);
		if(userId!=null)
			query.eq("user_id", userId);
		if(beginTime!=null)
			query.ge("create_time", beginTime);
		if(endTime!=null)
			query.le("create_time", endTime);
		return formFlowRecordMapper.getCount(query);
	}

	private FormFlowRecordData extractAsFormFlowRecordData(Map<String, Object> n) {
		FormFlowRecordData entity = new FormFlowRecordData();
		MyBeanUtil.copyPropertiesForMap(n, entity);
		return entity;
	}

	/**
	 * 清除所有已填写的表单记录
	 * @param formId
	 */
	public void clear(Integer formId) {
		formFlowRecordMapper.clear(formId);
	}
	
	/**
	 * 获取指定表单已填报记录的汇总数据
	 * @param ids
	 * @return
	 */
	public List<Map<String,Object>> getFormFlowRecordSummaryPerForm(List<Integer> ids){
	List<Map<String,Object>> summaryPerFormList = formFlowRecordMapper.getFormFlowRecordSummaryPerForm(ids);
	return summaryPerFormList;
	}

	public List<FormFlowRecord> getUserFormFlowRecordList(Integer formId, List<Integer> idsList) {
		Query query = new Query().eq("form_id", formId).in("user_id", idsList);
		return formFlowRecordMapper.list(query );
	}
}
