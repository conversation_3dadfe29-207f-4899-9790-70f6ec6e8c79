package com.taurus.formSys.service;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

@Service("formSysRedisService")
public class RedisService {
	@Autowired
	@Qualifier("formSysRedisTemplate")
	private RedisTemplate<String, Object> redisTemplate;

	/**
	 * 获取微信基础accessToken
	 * 
	 * @param account
	 * @return
	 */
	public String getBaseAccessToken(String accountName) {
		StringBuffer buffer = new StringBuffer("baseAccessToken_");
		buffer.append(accountName);
		String value = (String) redisTemplate.opsForValue().get(buffer.toString());
		return value;
	}

	/**
	 * 保存微信accessToken
	 * 
	 * @param token
	 * @param account
	 */
	public void saveBaseAccessToken(String tokenStr, String accountName) {
		StringBuffer buffer = new StringBuffer("baseAccessToken_");
		buffer.append(accountName);
		redisTemplate.opsForValue().set(buffer.toString(), tokenStr, 1L, TimeUnit.HOURS);// 保存1小时
	}

	/**
	 * 删除
	 * 
	 * @param accountName
	 */
	public void deleteBaseAccessToken(String accountName) {
		StringBuffer buffer = new StringBuffer("baseAccessToken_");
		buffer.append(accountName);
		redisTemplate.delete(buffer.toString());
	}

	/**
	 * 取出所有键前缀为prefix的值列表
	 * 
	 * @param key
	 * @return
	 */
	public List<Object> getMultipleValueByKeyPrefix(String prefix) {
		Set<String> keys = redisTemplate.keys(prefix + "*");
		List<Object> list = redisTemplate.opsForValue().multiGet(keys);
		return list;
	}

	/**
	 * 取出一个redis缓存对象
	 * 
	 * @param key
	 * @return
	 */
	public String get(String key) {
		return (String) redisTemplate.opsForValue().get(key);
	}

	/**
	 * 放入一个redis对象
	 * 
	 * @param key
	 * @param value
	 */
	public void put(String key, Object value) {
		redisTemplate.opsForValue().set(key, JSONObject.toJSONStringWithDateFormat(value, "yyyy-MM-dd HH:mm:ss",
				SerializerFeature.WriteDateUseDateFormat), 1, TimeUnit.DAYS);// 保存1天
	}

	/**
	 * 删除一个键值
	 * 
	 * @param key
	 * @return
	 */
	public boolean delete(StringBuffer key) {
		String value = (String) redisTemplate.opsForValue().get(key.toString());
		if (value != null) {
			return redisTemplate.delete(key.toString());
		} else {
			return true;
		}

	}

}
