package com.taurus.formSys.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.gitee.fastmybatis.core.query.Query;
import com.gitee.fastmybatis.core.query.Sort;
import com.taurus.formSys.entity.Category;
import com.taurus.formSys.entity.business.CategoryWrapper;
import com.taurus.formSys.mapper.CategoryMapper;

@Service("formSysCategoryService")
public class CategoryService {
	@Autowired
	@Qualifier("formSysCategoryMapper")
	private CategoryMapper categoryMapper;

	/**
	 * 增加新分类
	 * 
	 * @param category
	 * @return
	 */
	public Integer addCategory(Category category) {
		categoryMapper.saveIgnoreNull(category);
		return category.getId();
	}

	/**
	 * 逻辑删除某个类别
	 * 
	 * @param id
	 */
	public void deleteCategoryById(Integer id) {
		Map<String, Object> map = new HashMap<>();
		map.put("enabled", false);
		Query query = new Query().eq("id", id);
		categoryMapper.updateByMap(map, query);
	}

	/**
	 * 更新类别信息
	 * 
	 * @param category
	 */
	public Boolean updateCategory(Category category) {
		try {
			categoryMapper.updateIgnoreNull(category);
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
			return false;
		}

	}

	/**
	 * 获取某用户指定类型的类别列表
	 * 
	 * @param moduleName
	 * @param productName
	 * @return
	 */
	public List<Category> getCategoriesByUserIdAndSpaceName(String moduleName, String productName) {
		Query query = new Query().eq("module_name", moduleName).eq("product_name", productName).eq("enabled", true)
				.orderby("sequence_num", Sort.ASC);
		return categoryMapper.list(query);
	}

	/**
	 * 获取小于等于
	 * 
	 * @param productName
	 * @param moduleName
	 * @param level
	 * @return
	 */
	public List<Category> getLevelCategoriesOfLessThanOrEqualDesignativeLevel(String productName, String moduleName,
			Integer level) {
		Query query = new Query().eq("product_name", productName).eq("module_name", moduleName).le("level", level)
				.eq("enabled", true).orderby("sequence_num", Sort.ASC);
		List<Category> list = categoryMapper.list(query);
		return list;
	}

	/**
	 * 获取小于等于某层级的递归层级对象
	 * 算法简述：从第N层开始逐层包装在Map对象中
	 * 
	 * @param productName
	 * @param moduleName
	 * @param level
	 * @return
	 */
	public List<Map<String, Object>> getRecursionLevelCategoriesOfLessThanOrEqualDesignativeLevel(String productName,
			String moduleName, Integer level) {
		List<Map<String, Object>> comparelevelList = new ArrayList<>();

		List<Category> list = getLevelCategoriesOfLessThanOrEqualDesignativeLevel(productName, moduleName, level);
		// level次循环，分别找出
		for (int i = level; i >= 1; i--) {

			List<Map<String, Object>> currentlevelList = new ArrayList<>();
			for (Category item : list) {
				CategoryWrapper wrapper = new CategoryWrapper();
				BeanUtils.copyProperties(item, wrapper);
				wrapper.setCollapsed(true);// 默认折叠状态
				wrapper.setSelected(false);// 默认未选中状态
				wrapper.setEdited(false);// 默认未编辑状态

				if (i == item.getLevel()) {
					Map<String, Object> currentMap = new HashMap<>();
					List<Map<String, Object>> containCategoryList = new ArrayList<>();

					for (Map<String, Object> compareMap : comparelevelList) {
						Category downCategory = (Category) compareMap.get("category");
						if (downCategory.getParentId().intValue() == item.getId().intValue()) {
							containCategoryList.add(compareMap);

						}
					}

					currentMap.put("category", wrapper);
					currentMap.put("displayDownLevel", false);// 是否显示下层级别的类别
					currentMap.put("shiftSelected", false);// 前端选中的可以上下移动的类别
					currentMap.put("containCategoryList", containCategoryList);
					currentlevelList.add(currentMap);// 放入当前级别的

				}

			}
			comparelevelList = currentlevelList;
		}
		return comparelevelList;
	}

	/**
	 * 获取某个类别的直接子类别列表
	 * 
	 * @param parentId
	 * @return
	 */
	public List<Category> getChildCategoryListByParentId(Integer parentId) {
		Query query = new Query().eq("parent_id", parentId).eq("enabled", true);
		return categoryMapper.list(query);
	}

	/**
	 * 获取category对象
	 * 
	 * @param categoryId
	 * @return
	 */
	public Category getCategoryById(Integer categoryId) {
		return categoryMapper.getById(categoryId);
	}

	/**
	 * 获取单个类别的路径
	 * 
	 * @param categoryId
	 * @return
	 */
	public List<Map<String, Object>> getPathOfCategory(Integer categoryId) {
		List<Map<String, Object>> pathList = new ArrayList<>();
		Category category = getCategoryById(categoryId);
		if (category != null) {
			Integer parentId = category.getParentId();

			Map<String, Object> map = new HashMap<>();
			map.put("id", categoryId);
			map.put("name", category.getName());

			pathList.add(map);

			while (true) {
				Category parentCategory = getCategoryById(parentId);
				if (parentCategory != null) {
					parentId = parentCategory.getParentId();
					Map<String, Object> parentMap = new HashMap<>();
					parentMap.put("id", parentCategory.getId());
					parentMap.put("name", parentCategory.getName());
					pathList.add(parentMap);
				} else {
					break;
				}
			}
			return pathList;
		} else {
			return null;
		}
	}

	/**
	 * 获取多个类别的path
	 * 
	 * @param ids
	 * @return
	 */
	public List<List<Map<String, Object>>> getBatchPathOfCategoryList(List<Integer> ids) {
		List<List<Map<String, Object>>> pathList = new ArrayList<>();
		for (int i = 0; i < ids.size(); i++) {
			List<Map<String, Object>> path = getPathOfCategory(ids.get(i));
			if (path != null) {
				pathList.add(path);
			}

		}
		return pathList;
	}

	/**
	 * 批量更新类别
	 * 
	 * @param categoryList
	 * @return
	 */
	public Boolean updateCategoryList(List<Category> categoryList) {
		try {
			for (Category entity : categoryList) {
				categoryMapper.updateIgnoreNull(entity);
			}

			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
			return false;
		}

	}

}
