package com.taurus.formSys.service;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.gitee.fastmybatis.core.query.Query;
import com.gitee.fastmybatis.core.query.Sort;
import com.taurus.formSys.entity.FormFieldStructure;
import com.taurus.formSys.mapper.FormFieldStructureMapper;

@Service
public class FormFieldStructureService {
	private static final Logger logger = LoggerFactory.getLogger(FormFieldStructureService.class);


	@Autowired
	private FormFieldStructureMapper formFieldStructureMapper;

	public void create(FormFieldStructure structure) {
		formFieldStructureMapper.saveIgnoreNull(structure);
	}

	public void createBatch(List<FormFieldStructure> list) {
		formFieldStructureMapper.saveBatch(list);
	}

	public FormFieldStructure getById(Integer id) {
		return formFieldStructureMapper.getById(id);
	}

	public void update(FormFieldStructure record) {
		formFieldStructureMapper.updateIgnoreNull(record);
	}

	public List<FormFieldStructure> getFormFieldStructureListByFormId(Integer formId) {
		Query query = new Query().eq("form_id", formId).eq("enabled", true).orderby("field_seq", Sort.ASC);
		List<FormFieldStructure> list = formFieldStructureMapper.list(query);
		return list;
	}

	public void deleteById(Integer id) {
		formFieldStructureMapper.deleteById(id);
	}

	/**
	 * 删除某表单包含的所有字段
	 * 
	 * @param id
	 */
	public void deleteByFormId(Integer formId) {
		Query query = new Query().eq("form_id", formId);
		formFieldStructureMapper.deleteByQuery(query);
	}
}
