package com.taurus.formSys.service;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.gitee.fastmybatis.core.query.Query;
import com.gitee.fastmybatis.core.query.Sort;
import com.taurus.formSys.entity.CategoryForm;
import com.taurus.formSys.entity.business.FormDefinitionWrapper;
import com.taurus.formSys.mapper.CategoryFormMapper;

@Service
public class CategoryFormService {

	@Autowired
	private CategoryFormMapper categoryFormMapper;
	
	@Autowired
	private FormDefinitionService formDefinitionService;

	/**
	 * 获取某个类别的表单wrapper列表
	 * @param categoryId
	 * @return
	 */
	public List<FormDefinitionWrapper> getFormWrapperListByColumns(String productName,Integer categoryId,String state,Integer pageSize,Integer pageIndex) {
		Query query = new Query().eq("product_name", productName);
		if(categoryId!=null) {
			query.eq("t.category_id", categoryId);
		}
		if(state!=null) {
			query.eq("t.state", state);
		}
		query.page(pageIndex,pageSize).orderby("sequence_no", Sort.DESC);
		List<CategoryForm> mapList = categoryFormMapper.list(query);
		List<Integer> ids = mapList.stream().map(n->n.getFormId()).collect(Collectors.toList());
		return formDefinitionService.getFormWrapperListByIds(ids);
	}
}
