package com.taurus.formSys.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.gitee.fastmybatis.core.query.Query;
import com.gitee.fastmybatis.core.query.Sort;
import com.gitee.fastmybatis.core.util.MyBeanUtil;
import com.taurus.entity.ResponseObject;
import com.taurus.formSys.entity.FormDefinition;
import com.taurus.formSys.entity.FormFieldStructure;
import com.taurus.formSys.entity.business.FormDefinitionWrapper;
import com.taurus.formSys.feign.OssFeignClient;
import com.taurus.formSys.mapper.FormDefinitionMapper;

@Service
public class FormDefinitionService {

	private static final Logger logger = LoggerFactory.getLogger(FormDefinitionService.class);

	@Autowired
	private RedisService redisService;
	@Autowired
	private FormFlowRecordService formFlowRecordService;

	@Autowired
	private FormDefinitionMapper formDefinitionMapper;

	@Autowired
	private OssFeignClient ossFeignClient;

	public void create(FormDefinition form) {
		formDefinitionMapper.saveIgnoreNull(form);
	}

	/**
	 * 获取formDefinition对象
	 * 
	 * @param id
	 * @return
	 */
	public Map<String, Object> getFormDefinitionById(Integer id) {
		FormDefinition formDefinition = formDefinitionMapper.getById(id);
		Map<String, Object> map = MyBeanUtil.pojoToMap(formDefinition);
		String productName = (String) map.get("productName");
		String bgImageObjectName = (String) map.get("bgImageObjectName");
		String bgAudioObjectName = (String) map.get("bgAudioObjectName");
		String wxMpCardImageObjectName = (String) map.get("wxMpCardImageObjectName");
		
		String respositoryName = getRepositoryNameByProductName(productName);
		if (!StringUtils.isEmpty(bgImageObjectName)) {
			ResponseObject res = ossFeignClient.getAccessUrl(respositoryName, bgImageObjectName);
			if (res.getCode().equals("1")) {
				String bgImageUrl = (String) res.getData();
				map.put("bgImageUrl", bgImageUrl.toString());
			} else {
				logger.info("bgImageUrl获取失败");
			}
		}
		if (!StringUtils.isEmpty(bgAudioObjectName)) {
			ResponseObject res = ossFeignClient.getAccessUrl(respositoryName, bgAudioObjectName);
			if (res.getCode().equals("1")) {
				String bgImageUrl = (String) res.getData();
				map.put("bgAudioUrl", bgImageUrl.toString());
			} else {
				logger.info("bgAudioUrl获取失败");
			}
		}
		if (!StringUtils.isEmpty(wxMpCardImageObjectName)) {
			ResponseObject res = ossFeignClient.getAccessUrl(respositoryName, wxMpCardImageObjectName);
			if (res.getCode().equals("1")) {
				String bgImageUrl = (String) res.getData();
				map.put("wxMpCardImageUrl", bgImageUrl.toString());
			} else {
				logger.info("wxMpCardImageUrl获取失败");
			}
		}
		map = generateOssAccessUrl(map);
		return map;
	}

	/**
	 * 生成objectName对应的url
	 * 
	 * @param map
	 * @return
	 */
	private Map<String, Object> generateOssAccessUrl(Map<String, Object> map) {

		return map;
	}

	public void update(FormDefinition formDefinition) {
		formDefinitionMapper.updateIgnoreNull(formDefinition);
	}

	/**
	 * 根据id获取表单信息
	 * 
	 * @param id
	 * @return
	 */
	public FormDefinitionWrapper getFormWrapperById(Integer id) {
		StringBuffer keyBuffer = new StringBuffer().append("newFormWrapper").append(id);
		String value = redisService.get(keyBuffer.toString());
		if (value != null) {
			FormDefinitionWrapper wrapper = JSON.parseObject(value, FormDefinitionWrapper.class);
			return wrapper;
		} else {
			List<Map<String, Object>> fieldWrapperList = formDefinitionMapper.getFormFieldWrapperById(id);
			if (fieldWrapperList.size() > 0) {
				FormDefinitionWrapper wrapper = new FormDefinitionWrapper();
				Map<String, Object> map = fieldWrapperList.get(0);
				MyBeanUtil.copyPropertiesForMap(map, wrapper);
				List<FormFieldStructure> fieldList = fieldWrapperList.stream().map(n -> {
					return extractAsFormField(n);
				}).collect(Collectors.toList());
				wrapper.setFieldList(fieldList);
				wrapper = generateOssAccessUrl(wrapper);
				redisService.put(keyBuffer.toString(), wrapper);
				return wrapper;
			} else {
				return null;
			}
		}
	}

	private FormFieldStructure extractAsFormField(Map<String, Object> n) {
		FormFieldStructure entity = new FormFieldStructure();
		MyBeanUtil.copyPropertiesForMap(n, entity);
		return entity;
	}

	/**
	 * 通过ids获取表单列表
	 * 
	 * @param pageIndex
	 * @param pageSize
	 * @return
	 */
	public List<FormDefinition> getFormListByIds(List<Integer> ids) {
		Query query = new Query().in("id", ids);
		List<FormDefinition> fieldList = formDefinitionMapper.list(query);
		return fieldList;
	}

	/**
	 * 获取多个表单的formWrapperList
	 * 
	 * @param ids
	 * @return
	 */
	public List<FormDefinitionWrapper> getFormWrapperListByIds(List<Integer> ids) {
		List<Map<String, Object>> formAndFieldList = formDefinitionMapper.getFormDefinitionAndFieldListByIds(ids);
		return transform2FormDefinitionWrapperList(formAndFieldList, ids);
	}

	/**
	 * FormDefinitionAndFormFieldList 转化为 FormDefinitionWrapperList
	 * 
	 * @param formAndFieldList
	 * @return
	 */
	public List<FormDefinitionWrapper> transform2FormDefinitionWrapperList(List<Map<String, Object>> formAndFieldList,
			List<Integer> ids) {
		// 按formId进行分组
		Map<String, List<Map<String, Object>>> groupByFormId = formAndFieldList.stream()
				.collect(Collectors.groupingBy(n -> String.valueOf((Integer) n.get("formId"))));
		List<FormDefinitionWrapper> wrapperList = new ArrayList<>();

		for (Integer key : ids) {
			List<Map<String, Object>> value = groupByFormId.get(String.valueOf(key));
			FormDefinitionWrapper wrapper = new FormDefinitionWrapper();
			if (value != null && value.size() > 0) {
				Map<String, Object> formDefinitionMap = value.get(0);
				FormDefinitionWrapper existedWrapper = MyBeanUtil.mapToPojo(formDefinitionMap,
						FormDefinitionWrapper.class);
				BeanUtils.copyProperties(existedWrapper, wrapper);
				wrapper = generateOssAccessUrl(wrapper);
				wrapper.setId(key);
				List<FormFieldStructure> formFieldList = value.stream().map(n -> {
					return extractAsFormField(n);
				}).collect(Collectors.toList());
				wrapper.setFieldList(formFieldList);
				wrapperList.add(wrapper);
			}
		}
		return wrapperList;
	}

	/**
	 * wrapper对象中所有objectName生成对应的url
	 * 
	 * @param wrapper
	 * @return
	 */
	private FormDefinitionWrapper generateOssAccessUrl(FormDefinitionWrapper wrapper) {
		String productName = wrapper.getProductName();
		String bgImageObjectName = wrapper.getBgImageObjectName();
		String bgAudioObjectName = wrapper.getBgAudioObjectName();
		String wxMpCardImageObjectName = wrapper.getWxMpCardImageObjectName();

		String respositoryName = getRepositoryNameByProductName(productName);
		if (!StringUtils.isEmpty(bgImageObjectName)) {
			ResponseObject res = ossFeignClient.getAccessUrl(respositoryName, bgImageObjectName);
			if (res.getCode().equals("1")) {
				String bgImageUrl = (String) res.getData();
				wrapper.setBgImageUrl(bgImageUrl.toString());
			} else {
				logger.info("bgImageUrl获取失败");
			}
		}
		if (!StringUtils.isEmpty(bgAudioObjectName)) {
			ResponseObject res = ossFeignClient.getAccessUrl(respositoryName, bgAudioObjectName);
			if (res.getCode().equals("1")) {
				String bgAudioUrl = (String) res.getData();
				wrapper.setBgAudioUrl(bgAudioUrl.toString());
			} else {
				logger.info("bgImageUrl获取失败");
			}
		}
		if (!StringUtils.isEmpty(wxMpCardImageObjectName)) {
			ResponseObject res = ossFeignClient.getAccessUrl(respositoryName, wxMpCardImageObjectName);
			if (res.getCode().equals("1")) {
				String url = (String) res.getData();
				wrapper.setWxMpCardImageUrl(url.toString());
			} else {
				logger.info("bgImageUrl获取失败");
			}
		}

		List<FormFieldStructure> fieldList = wrapper.getFieldList();
		if (fieldList != null && fieldList.size() > 0) {
			fieldList.forEach(field -> {
				String fieldInitValue = field.getFieldInitValue();
				// 字符串以[开头
				if (!StringUtils.isEmpty(fieldInitValue) && fieldInitValue.startsWith("[")) {
					JSONArray fieldJsonArray = JSONObject.parseArray(fieldInitValue);
					if (fieldJsonArray != null && fieldJsonArray.size() > 0) {
						for (int i = 0; i < fieldJsonArray.size(); i++) {
							JSONObject json = fieldJsonArray.getJSONObject(i);
							String objectName = json.getString("objectName");
							if (!StringUtils.isEmpty(objectName)) {
								ResponseObject res = ossFeignClient.getAccessUrl(productName, objectName);
								if (res.getCode().equals("1")) {
									String url = (String) res.getData();
									json.put("url", url);
								} else {
									logger.info("fieldInitValue获取失败");
								}
							}
						}
						field.setFieldInitValue(fieldJsonArray.toJSONString());
					}
				}
			});
		}
		return wrapper;
	}

	/**
	 * 获取产品对应的oss仓库名称
	 * @param productName
	 * @return
	 */
	private String getRepositoryNameByProductName(String productName) {
		if(productName.equals("etea")) {
			return "examination-etea";
		}else if(productName.equals("universalForm")) {
			return "universalform";
		}else if(productName.equals("punchcard")) {
			return "examination-etea";
		}else if(productName.equals("questionnaire")) {
			return "questionnaire88";
		}else if(productName.equals("qkk")) {//自定义注册表单
			return "exam-qkk";
		}
		return null;
	}

	/**
	 * 获取某产品的表单列表
	 * 
	 * @param productName 引用的产品代码
	 * @param userId
	 * @return
	 */
	public List<FormDefinition> getFormListByProduct(String productName, Integer userId, Integer pageIndex,
			Integer pageSize) {
		Query query = new Query().eq("product_name", productName).eq("enabled", true);
		if (userId != null && userId > 0) {
			query.eq("user_id", userId);
		}
		query.orderby("create_time", Sort.DESC);
		query.page(pageIndex, pageSize);
		return formDefinitionMapper.list(query);
	}

	/**
	 * 获取简明的表单列表信息 包含表单标题，id，已填表统计量
	 * 
	 * @param productName
	 * @param userId
	 * @param pageIndex
	 * @param pageSize
	 * @return
	 */
	public List<Map<String, Object>> getConciseFormListByReference(String productName, Integer userId, Integer formId,
			Integer pageIndex, Integer pageSize) {
		Query query = new Query().eq("product_name", productName).eq("enabled", true);
		if (userId != null && userId > 0) {
			query.eq("user_id", userId);
		}
		if (formId != null) {
			query.eq("id", formId);
		}
		query.orderby("create_time", Sort.DESC);
		query.page(pageIndex, pageSize);
		List<String> columns = Arrays.asList("id", "title", "wx_mp_card_image_object_name as wxMpCardImageObjectName");
		List<Map<String, Object>> mapList = formDefinitionMapper.listMap(columns, query);
		if (mapList != null && mapList.size() > 0) {
			List<Integer> ids = mapList.stream().map(n -> (Integer) n.get("id")).collect(Collectors.toList());
			List<Map<String, Object>> summaryPerFormList = formFlowRecordService.getFormFlowRecordSummaryPerForm(ids);
			Map<Integer, Object> idTotalNum = summaryPerFormList.stream()
					.collect(Collectors.toMap(n -> (Integer) n.get("id"), n -> (Long) n.get("totalNum"),(v1,v2)->v2));
			// 加入已填写表单统计量
			mapList.forEach(item -> {
				String wxMpCardImageObjectName = (String) item.get("wxMpCardImageObjectName");
				if (!StringUtils.isEmpty(wxMpCardImageObjectName)) {
					String respositoryName = getRepositoryNameByProductName(productName);
					ResponseObject res = ossFeignClient.getAccessUrl(respositoryName, wxMpCardImageObjectName);
					if (res.getCode().equals("1")) {
						String url = (String) res.getData();
						item.put("wxMpCardImageUrl", url.toString());
					} else {
						logger.info("bgImageUrl获取失败");
					}
				}
				Integer id = (Integer) item.get("id");
				item.put("totalNum", idTotalNum.get(id) == null ? 0 : idTotalNum.get(id));
			});
		}

		return mapList;
	}

}
