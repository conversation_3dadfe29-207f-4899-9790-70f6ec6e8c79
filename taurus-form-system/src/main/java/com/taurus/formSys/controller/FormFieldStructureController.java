package com.taurus.formSys.controller;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.taurus.entity.ResponseObject;
import com.taurus.formSys.entity.FormFieldStructure;
import com.taurus.formSys.service.FormFieldStructureService;
import com.taurus.formSys.service.FormFlowRecordDataService;

@RestController
@RequestMapping("/formFieldStructure")
public class FormFieldStructureController {

	@Autowired
	private FormFieldStructureService formFieldStructureService;
	
	@Autowired
	private FormFlowRecordDataService formFlowRecordDataService;
	
	/**
	 *获取表单所有字段信息
	 */
	@GetMapping("/getFormFieldStructureListByFormId")
	ResponseObject getFormFieldStructureListByFormId(Integer formId) {
		List<FormFieldStructure> list = formFieldStructureService.getFormFieldStructureListByFormId(formId);
		return ResponseObject.success(list);
	}
	
	/**
	 * 获取表单可编辑字段的结构信息
	 * @param formId
	 * @return
	 */
	@GetMapping("/getFormEditableFiledStructureListByFormId")
	ResponseObject getFormEditableFiledStructureListByFormId(Integer formId) {
		List<FormFieldStructure> fieldList = formFieldStructureService.getFormFieldStructureListByFormId(formId);
		fieldList = fieldList.stream().filter(n -> formFlowRecordDataService.isWritableField(n)).collect(Collectors.toList());
		return ResponseObject.success(fieldList);
	}
}
