package com.taurus.formSys.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.taurus.entity.ResponseObject;
import com.taurus.formSys.entity.business.FormDefinitionWrapper;
import com.taurus.formSys.service.CategoryFormService;

@RestController
@RequestMapping("/categoryForm")
public class CategoryFormController {
	
	@Autowired
	private CategoryFormService categoryFormService;
	
	/**
	 * 获取某类别下的表单
	 * @param categoryId
	 * @param state
	 * @return
	 */
	@PostMapping("/getFormWrapperListByColumns")
	public ResponseObject getFormListByColumns(@RequestBody JSONObject body) {
		String productName = body.getString("productName");
		Integer categoryId= body.getInteger("categoryId");
		String state = body.getString("state");
		Integer pageSize=body.getInteger("pageSize");
		Integer pageIndex= body.getInteger("pageIndex");
		if(productName==null||pageSize==null||pageIndex==null) {
			return ResponseObject.failure("PARAMETERS_ERROR");
		}
		List<FormDefinitionWrapper> wrapperList = categoryFormService.getFormWrapperListByColumns(productName,categoryId,state,pageSize,pageIndex);
		return ResponseObject.success(wrapperList);
	}

}
