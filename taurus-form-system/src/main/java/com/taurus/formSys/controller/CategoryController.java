package com.taurus.formSys.controller;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.taurus.formSys.entity.Category;
import com.taurus.formSys.service.CategoryService;

@RestController("formSysCategoryController")
@RequestMapping(value = "/category")
public class CategoryController {

	@Autowired
	@Qualifier("formSysCategoryService")
	private CategoryService categoryService;

	/**
	 * 添加新类别
	 * 
	 * @param body
	 * @return 新类别的id
	 */
	@PostMapping("/addCategory")
	public Integer addCategory(@RequestBody JSONObject body) {
		Category category = JSONObject.parseObject(body.toJSONString(), Category.class);
		return categoryService.addCategory(category);
	}

	/**
	 * 获取某个对象的N级类别关系结构数据
	 * 
	 * @param productName
	 * @param userId
	 * @param level
	 * @return
	 */
	@GetMapping("/getNLevelCategory")
	public List<Map<String, Object>> getLevelCategoriesOfLessThanOrEqualDesignativeLevel(String productName,
			String moduleName, Integer level) {
		return categoryService.getRecursionLevelCategoriesOfLessThanOrEqualDesignativeLevel(productName, moduleName,
				level);
	}

	/**
	 * 获取某个类别的自上而下的路径 list从0开始从子类到父类依次排列
	 * 
	 * @param categoryId
	 * @return
	 */
	@GetMapping("/getPathOfCategory")
	public List<Map<String, Object>> getPathOfCategory(Integer categoryId) {
		return categoryService.getPathOfCategory(categoryId);
	}

	/**
	 * 获取多个类别的层级访问path
	 * 
	 * @param ids
	 * @return
	 */
	@PostMapping("/getBatchPathOfCategoryList")
	public List<List<Map<String, Object>>> getBatchPathOfCategoryList(@RequestBody JSONArray body) {
		List<Integer> ids = JSONArray.parseArray(body.toJSONString(), Integer.class);
		return categoryService.getBatchPathOfCategoryList(ids);
	}

	@PostMapping("/updateCategory")
	public Boolean updateCategory(@RequestBody JSONObject body) {
		Category category = JSONObject.parseObject(body.toJSONString(), Category.class);
		return categoryService.updateCategory(category);
	}

	/**
	 * 批量更新类别
	 * 
	 * @param body
	 * @return
	 */
	@PostMapping("/updateCategoryList")
	public Boolean updateCategoryList(@RequestBody JSONArray body) {
		List<Category> categoryList = JSONArray.parseArray(body.toJSONString(), Category.class);
		return categoryService.updateCategoryList(categoryList);
	}

}
