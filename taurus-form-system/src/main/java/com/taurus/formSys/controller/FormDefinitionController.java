package com.taurus.formSys.controller;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.taurus.entity.ResponseObject;
import com.taurus.formSys.entity.FormDefinition;
import com.taurus.formSys.entity.FormFieldStructure;
import com.taurus.formSys.entity.business.FormDefinitionWrapper;
import com.taurus.formSys.feign.ExamMainServiceFeign;
import com.taurus.formSys.service.FormDefinitionService;
import com.taurus.formSys.service.FormFieldStructureService;
import com.taurus.formSys.service.RedisService;

@RestController
@RequestMapping("/formDefinition")
public class FormDefinitionController {

	@Autowired
	private FormDefinitionService formDefinitionService;

	@Autowired
	private FormFieldStructureService formFieldStructureService;

	@Autowired
	private RedisService redisService;
	
	@Autowired
	private ExamMainServiceFeign examMainServiceFeign;

	/**
	 * 创建表单
	 * 
	 * @param body
	 */
	@PostMapping("/createForm")
	@Transactional
	public ResponseObject createForm(@RequestBody JSONObject body) {
		JSONObject form = body.getJSONObject("form");
		JSONArray fields = body.getJSONArray("fields");
		String account = body.getString("account");
		Boolean ifCheck = body.getBoolean("ifCheck");

		if (form == null || fields == null || (fields != null && fields.size() == 0)) {
			return ResponseObject.failure("ERROR");
		}

		FormDefinition formObj = JSON.toJavaObject(form, FormDefinition.class);
		String referenceCode = form.getString("referenceCode");
		if (!StringUtils.isEmpty(referenceCode)) {
			formObj.setProductName(referenceCode);
		}

		if (formObj.getProductName() == null || formObj.getTitle() == null) {
			return ResponseObject.failure("PARAMETERS_ERROR");
		}
		
		if(StringUtils.isEmpty(account)) {
			account="etea";
		}

		if (ifCheck == null) {
			ifCheck = false;
		}
		
		List<FormFieldStructure> list = JSONArray.parseArray(fields.toJSONString(), FormFieldStructure.class);
		if (ifCheck) {
			StringBuffer msgBuffer = new StringBuffer().append(formObj.getTitle()).append(formObj.getExplanation());
			if (list != null && list.size() > 0) {
				for (int i = 0; i < list.size(); i++) {
					FormFieldStructure field = list.get(i);
					msgBuffer.append(field.getFieldName());
					msgBuffer.append(field.getFieldInitValue());
					msgBuffer.append(field.getValidationContent());
				}
			}
			
			JSONObject req = new JSONObject();
			req.put("account", account);
			req.put("msg",  msgBuffer.toString());
			ResponseObject result = examMainServiceFeign.msgSecCheck(req);

			if (result.getCode().equals("reject") || result.getCode().equals("IO_EXCEPTION")) {
				return result;
			}
		}

		formObj.setCreateTime(new Date());
		formObj.setPerUserSubmitMaxTimes(0);// 0表示无限制
		formObj.setSubmitMaxTimes(0);// 0表示无限制
		formObj.setEnabled(true);
		formDefinitionService.create(formObj);

		List<FormFieldStructure> handledList = list.stream().map((n) -> {
			return setFormIdIntoStructure(formObj.getId(), n);
		}).collect(Collectors.toList());
		formFieldStructureService.createBatch(handledList);
		return ResponseObject.success(formObj);
	}

	// 设置formId
	private FormFieldStructure setFormIdIntoStructure(Integer id, FormFieldStructure structure) {
		structure.setFormId(id);
		structure.setEnabled(true);
		return structure;
	}

	/**
	 * 更新表单结构
	 * 
	 * @param body
	 * @return
	 */
	@PostMapping("/update")
	@Transactional
	public ResponseObject update(@RequestBody JSONObject body) {
		JSONObject form = body.getJSONObject("form");
		JSONArray fields = body.getJSONArray("fields");
		String account = body.getString("account");
		Boolean ifCheck = body.getBoolean("ifCheck");

		FormDefinition formObj = JSON.toJavaObject(form, FormDefinition.class);
		Integer id = formObj.getId();
		if (id == null) {
			return ResponseObject.failure("PARAMETERS_ERROR");
		}
		if(StringUtils.isEmpty(account)) {
			account="etea";
		}
		if (ifCheck == null) {
			ifCheck = false;
		}
		
		List<FormFieldStructure> list =null;
		if (fields != null && fields.size() > 0) {
			list = JSONArray.parseArray(fields.toJSONString(), FormFieldStructure.class);
		}
		if (ifCheck) {
			StringBuffer msgBuffer = new StringBuffer().append(formObj.getTitle()).append(formObj.getExplanation());

			if (list != null && list.size() > 0) {
				for (int i = 0; i < list.size(); i++) {
					FormFieldStructure field = list.get(i);
					msgBuffer.append(field.getFieldName());
					msgBuffer.append(field.getFieldInitValue());
					msgBuffer.append(field.getValidationContent());
				}
			}
			JSONObject req = new JSONObject();
			req.put("account", account);
			req.put("msg",  msgBuffer.toString());
			ResponseObject result = examMainServiceFeign.msgSecCheck(req);
			if (result.getCode().equals("reject") || result.getCode().equals("IO_EXCEPTION")) {
				return result;
			}
		}

		formDefinitionService.update(formObj);

		/** 删除redis缓存 **/
		StringBuffer keyBuffer = new StringBuffer().append("newFormWrapper").append(formObj.getId());
		redisService.delete(keyBuffer);
		if (fields != null && fields.size() > 0) {
			List<FormFieldStructure> currentList = formFieldStructureService
					.getFormFieldStructureListByFormId(formObj.getId());

			List<FormFieldStructure> needUpdateList = list.stream().filter(n -> n.getId() != null)
					.collect(Collectors.toList());
			List<FormFieldStructure> newList = list.stream().filter(n -> n.getId() == null)
					.collect(Collectors.toList());

			// 找到需要删除的字段列表
			for (FormFieldStructure field : needUpdateList) {
				// 从现有的中除掉需要更新的，剩下的都是需要删除的
				currentList.removeIf(x -> (field.getId().intValue() == x.getId().intValue()));
			}
			// 先删除不要的字段列表
			for (FormFieldStructure field : currentList) {
				formFieldStructureService.deleteById(field.getId());
			}
			// 更新字段列表
			needUpdateList.forEach((item) -> {
				item.setEnabled(true);
				formFieldStructureService.update(item);
			});
			// 新增
			if (newList != null && newList.size() > 0) {
				newList = newList.stream().map(n -> {
					n.setEnabled(true);
					return n;
				}).collect(Collectors.toList());
				formFieldStructureService.createBatch(newList);
			}
		}
		return ResponseObject.success(true);
	}

	/**
	 * 获取form对象
	 * 
	 * @param id
	 * @return
	 */
	@GetMapping("/getFormById")
	public ResponseObject getFormById(Integer id) {
		Map<String, Object> formDefinition = formDefinitionService.getFormDefinitionById(id);

		return ResponseObject.success(formDefinition);
	}

	/**
	 * 根据id获取表单信息
	 * 
	 * @param id
	 * @return
	 */
	@GetMapping("/getFormWrapperById")
	public ResponseObject getFormWrapperById(Integer id) {
		FormDefinitionWrapper wrapper = formDefinitionService.getFormWrapperById(id);
		return ResponseObject.success(wrapper);
	}

	/**
	 * 获取指定id集合的表单列表
	 * 
	 */
	@PostMapping("/getFormListByIds")
	public ResponseObject getFormListByIds(@RequestBody JSONArray body) {
		List<Integer> ids = JSONArray.parseArray(body.toJSONString(), Integer.class);
		if (ids != null && ids.size() > 0) {
			List<FormDefinition> formList = formDefinitionService.getFormListByIds(ids);
			return ResponseObject.success(formList);
		} else {
			return ResponseObject.success(null);
		}
	}

	@PostMapping("/getFormWrapperListByIds")
	public ResponseObject getFormWrapperListByIds(@RequestBody JSONArray body) {
		List<Integer> ids = JSONArray.parseArray(body.toJSONString(), Integer.class);
		if (ids != null && ids.size() > 0) {
			List<FormDefinitionWrapper> formList = formDefinitionService.getFormWrapperListByIds(ids);
			return ResponseObject.success(formList);
		} else {
			return ResponseObject.success(null);
		}
	}

	/**
	 * 获取某产品的表单列表
	 * 
	 * @param referenceProductCode
	 * @param userId
	 * @return
	 */
	@GetMapping("/getFormListOfProduct")
	public ResponseObject getFormListOfProduct(@RequestParam(required = true) String referenceProductCode,
			@RequestParam(required = false) Integer userId, @RequestParam(required = true) Integer pageIndex,
			@RequestParam(required = true) Integer pageSize) {
		List<FormDefinition> formList = formDefinitionService.getFormListByProduct(referenceProductCode, userId,
				pageIndex, pageSize);
		return ResponseObject.success(formList);
	}

	/**
	 * 获取简明的表单列表信息 包含表单标题，id，已填表统计量
	 * 
	 * @param referenceProductCode
	 * @param userId
	 * @param pageIndex
	 * @param pageSize
	 * @return
	 */
	@GetMapping("/getConciseFormListOfProduct")
	public ResponseObject getConciseFormListOfProduct(@RequestParam(required = true) String referenceProductCode,
			@RequestParam(required = false) Integer userId, @RequestParam(required = false) Integer formId,
			@RequestParam(required = true) Integer pageIndex, @RequestParam(required = true) Integer pageSize) {
		if (StringUtils.isEmpty(referenceProductCode) || pageIndex == null || pageIndex == 0 || pageSize == null
				|| pageSize == 0) {
			return ResponseObject.failure("PARAMETERS_ERROR");
		}
		List<Map<String, Object>> formList = formDefinitionService.getConciseFormListByReference(referenceProductCode,
				userId, formId, pageIndex, pageSize);
		return ResponseObject.success(formList);
	}

	/**
	 * 删除某个表单，非物理删除
	 * 
	 * @param formId
	 * @return
	 */
	@GetMapping("/disableForm")
	public ResponseObject disableForm(Integer formId) {
		FormDefinition form = new FormDefinition();
		form.setId(formId);
		form.setEnabled(false);
		formDefinitionService.update(form);
		return ResponseObject.success(true);
	}
}
