package com.taurus.formSys.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.gitee.fastmybatis.core.util.MyBeanUtil;
import com.taurus.entity.ResponseObject;
import com.taurus.formSys.entity.FormFieldStructure;
import com.taurus.formSys.entity.FormFlowRecord;
import com.taurus.formSys.entity.FormFlowRecordData;
import com.taurus.formSys.entity.business.FormDefinitionWrapper;
import com.taurus.formSys.entity.business.FormFlowRecordDataWrapper;
import com.taurus.formSys.entity.business.FormFlowRecordWrapper;
import com.taurus.formSys.feign.ExamMainServiceFeign;
import com.taurus.formSys.service.FormDefinitionService;
import com.taurus.formSys.service.FormFieldStructureService;
import com.taurus.formSys.service.FormFlowRecordDataService;
import com.taurus.formSys.service.FormFlowRecordService;

@RestController
@RequestMapping("/formFlowRecord")
public class FormFlowRecordController {

	private static Logger logger = LoggerFactory.getLogger(FormFlowRecordController.class);

	@Autowired
	private FormFlowRecordService formFlowRecordService;

	@Autowired
	private FormFlowRecordDataService formFlowRecordDataService;

	@Autowired
	private FormFieldStructureService formFieldStructureService;

	@Autowired
	private FormDefinitionService formDefinitionService;

	@Autowired
	private ExamMainServiceFeign examMainServiceFeign;

	@PostMapping("/save")
	@Transactional
	public ResponseObject save(@RequestBody JSONObject body) {

		JSONObject formFlowRecordJson = body.getJSONObject("formFlowRecord");
		JSONArray formFlowRecordDataJson = body.getJSONArray("formFlowRecordData");
		String account = body.getString("account");
		Boolean ifCheck = body.getBoolean("ifCheck");

		FormFlowRecord formFlowRecord = JSONObject.parseObject(formFlowRecordJson.toJSONString(), FormFlowRecord.class);
		List<FormFlowRecordData> formFlowRecordDataList = JSONArray.parseArray(formFlowRecordDataJson.toJSONString(),
				FormFlowRecordData.class);
		Integer userId = formFlowRecord.getUserId();
		Integer formId = formFlowRecord.getFormId();
		// 验证userId和formId
		if (formId == null || formId == 0 || userId == null || userId == 0 || formFlowRecordDataList == null
				|| formFlowRecordDataList.size() == 0) {
			return ResponseObject.failure("PARAMETERS_ERROR");
		}

		if (StringUtils.isEmpty(account)) {
			account = "etea";
		}

		if (ifCheck != null && ifCheck) {
			try {
				StringBuffer msgBuffer = new StringBuffer();
				if (formFlowRecordDataList != null && formFlowRecordDataList.size() > 0) {
					for (int i = 0; i < formFlowRecordDataList.size(); i++) {
						FormFlowRecordData record = formFlowRecordDataList.get(i);
						msgBuffer.append(record.getFieldValue());
					}
				}
				JSONObject req = new JSONObject();
				req.put("account", account);
				req.put("msg", msgBuffer.toString());
				ResponseObject result = examMainServiceFeign.msgSecCheck(req);

				if (result.getCode().equals("reject") || result.getCode().equals("IO_EXCEPTION")) {
					return result;
				}
			} catch (Exception ex) {
				logger.error("eteaFeignClient.msgSecCheck接口不可用");
				ex.printStackTrace();
			}
		}

		logger.info("formRecordflowRecord:{},formFlowRecordDataJson:{}", formFlowRecordJson, formFlowRecordDataJson);

		FormDefinitionWrapper wrapper = formDefinitionService.getFormWrapperById(formId);
		Integer perUserMaxTimes = wrapper.getPerUserSubmitMaxTimes();

		if (perUserMaxTimes != null && perUserMaxTimes != 0) {
			Long totalTimes = formFlowRecordService.getNumber(formId, userId, null, null);
			if (totalTimes != null && totalTimes >= perUserMaxTimes) {
				return ResponseObject.failure("REACHED_PER_USER_SUBMIT_MAX_TIMES");
			}
		}

		formFlowRecord.setEnabled(true);
		formFlowRecord.setCreateTime(new Date());
		formFlowRecordService.save(formFlowRecord);

		List<FormFlowRecordData> handledList = formFlowRecordDataList.stream().map(n -> {
			return setFormFlowRecordId(formFlowRecord.getId(), n);
		}).collect(Collectors.toList());

		formFlowRecordDataService.batchSave(handledList);
		return ResponseObject.success(formFlowRecord.getId());
	}

	private FormFlowRecordData setFormFlowRecordId(Integer id, FormFlowRecordData n) {
		n.setFormFlowRecordId(id);
		return n;
	}

	/**
	 * 更新某次表单记录
	 * 
	 * @param body
	 * @return
	 */
	@PostMapping("/update")
	@Transactional
	public ResponseObject update(@RequestBody JSONObject body) {

		JSONObject formFlowRecordJson = body.getJSONObject("formFlowRecord");
		JSONArray formFlowRecordDataJson = body.getJSONArray("formFlowRecordData");

		FormFlowRecord formFlowRecord = JSONObject.parseObject(formFlowRecordJson.toJSONString(), FormFlowRecord.class);
		List<FormFlowRecordData> formFlowRecordDataList = JSONArray.parseArray(formFlowRecordDataJson.toJSONString(),
				FormFlowRecordData.class);
		Integer id = formFlowRecord.getId();
		Integer userId = formFlowRecord.getUserId();
		Integer formId = formFlowRecord.getFormId();
		// 验证userId和formId
		if (id == null || id == 0 || formId == null || formId == 0 || userId == null || userId == 0) {
			return ResponseObject.failure("PARAMETERS_ERROR");
		}

		// 校验数据的准确性
		for (int i = 0; i < formFlowRecordDataList.size(); i++) {
			FormFlowRecordData data = formFlowRecordDataList.get(i);
			Integer fieldId = data.getFieldId();
			data.setFormFlowRecordId(id);
			data.setFormId(formId);
			if (fieldId == null || fieldId == 0) {
				return ResponseObject.failure("PARAMETERS_ERROR");
			}
		}

		formFlowRecordService.update(formFlowRecord);

		formFlowRecordDataService.batchDelete(id);
		formFlowRecordDataService.batchSave(formFlowRecordDataList);

		return ResponseObject.success(true);
	}

	/**
	 * 清空已填写的所有表单数据
	 * 
	 * @param formId
	 * @return
	 */
	@GetMapping("/clear")
	public ResponseObject clear(Integer formId) {
		formFlowRecordService.clear(formId);
		return ResponseObject.success(true);
	}

	@PostMapping("/getUserFormFlowRecordList")
	public ResponseObject getUserFormFlowRecordList(@RequestBody JSONObject body) {
		JSONArray idsArray = body.getJSONArray("userIdList");
		Integer formId = body.getInteger("formId");
		if (idsArray == null || formId == null || formId == 0) {
			return ResponseObject.failure("PARAMETERS_ERROR");
		}
		List<Integer> idsList = JSONArray.parseArray(idsArray.toJSONString(), Integer.class);
		List<FormFlowRecord> recordList = formFlowRecordService.getUserFormFlowRecordList(formId, idsList);
		return ResponseObject.success(recordList);
	}

	/**
	 * 获取某次表单填写的详情内容
	 * 
	 * @param formFlowRecordId
	 * @return
	 */
	@GetMapping("/getFormFlowRecordWrapperById")
	public ResponseObject getFormFlowRecordWrapperById(Integer formFlowRecordId) {
		if (formFlowRecordId == null || formFlowRecordId == 0) {
			return ResponseObject.failure("PARAMETERS_ERROR");
		}
		FormFlowRecordWrapper wrapper = formFlowRecordService.getFormFlowRecordWrapperById(formFlowRecordId);
		return ResponseObject.success(wrapper);
	}

	/**
	 * 获取多个表单的填写记录
	 * 
	 * @param body（包含了多个表单填写记录的id）
	 * @return 每个记录号对应一个表单记录详情；记录号为key，记录详情为value
	 */
	@PostMapping("/getFormFlowRecordWrapperByIds")
	public ResponseObject getFormFlowRecordDataByIds(@RequestBody JSONObject body) {
		JSONArray idsArray = body.getJSONArray("ids");
		Integer formId = body.getInteger("formId");
		List<Integer> idsList = JSONArray.parseArray(idsArray.toJSONString(), Integer.class);

		if (idsList != null && idsList.size() > 0) {
			Map<String, List<Map<String, Object>>> map = formFlowRecordDataService
					.getFormFlowRecordDataByIdsGroupByFormFlowRecordId(idsList);
			List<FormFieldStructure> fieldList = formFieldStructureService.getFormFieldStructureListByFormId(formId);
			// fieldList = fieldList.stream().filter(n ->
			// isWritableField(n)).collect(Collectors.toList());// 过滤出用户填写的内容字段

			List<FormFlowRecordDataWrapper> templateWrapperList = fieldList.stream().map(n -> {
				return transform2FormFlowRecordDataWrapper(n);
			}).collect(Collectors.toList());

			Map<String, List<FormFlowRecordDataWrapper>> targetMap = new HashMap<>();
			map.forEach((key, value) -> {
				List<FormFlowRecordDataWrapper> dataWrapperList = MyBeanUtil.mapListToObjList(value,
						FormFlowRecordDataWrapper.class);
				Map<Integer, FormFlowRecordDataWrapper> wrapperMap = dataWrapperList.stream().collect(Collectors
						.toMap(FormFlowRecordDataWrapper::getFieldId, Function.identity(), (key1, key2) -> key2));

				List<FormFlowRecordDataWrapper> targetWrapperList = new ArrayList<FormFlowRecordDataWrapper>();
				templateWrapperList.forEach(item -> {
					Integer fieldId = item.getFieldId();
					FormFlowRecordDataWrapper formFlowRecordDataWrapper = wrapperMap.get(fieldId);
					if (formFlowRecordDataWrapper != null) {
						targetWrapperList.add(formFlowRecordDataWrapper);
					} else {
						targetWrapperList.add(item);
					}
				});
				targetMap.put(key, targetWrapperList);
			});
			return ResponseObject.success(targetMap);
		} else {
			return ResponseObject.success(null);
		}
	}

	/**
	 * 表单数据模板
	 * 
	 * @param n
	 * @return
	 */
	private FormFlowRecordDataWrapper transform2FormFlowRecordDataWrapper(FormFieldStructure n) {
		FormFlowRecordDataWrapper wrapper = new FormFlowRecordDataWrapper();
		MyBeanUtil.copyProperties(n, wrapper);
		wrapper.setFieldId(n.getId());
		return wrapper;
	}

	/**
	 * 判断是否是可写的组件
	 * 
	 * @param n
	 * @return
	 */
	private Boolean isWritableField(FormFieldStructure n) {
		Integer fieldType = n.getFieldType();
		// 1表示单行文本框，2多行文本框 3下拉框 4单选框 5多选框 6文本段落
		if (fieldType == 1 || fieldType == 2 || fieldType == 3 || fieldType == 4 || fieldType == 5 || fieldType == 6) {
			return true;
		}
		return false;
	}

}
