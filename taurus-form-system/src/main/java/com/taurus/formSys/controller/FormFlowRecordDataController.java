package com.taurus.formSys.controller;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.gitee.fastmybatis.core.util.MyBeanUtil;
import com.taurus.entity.ResponseObject;
import com.taurus.formSys.entity.FormFieldStructure;
import com.taurus.formSys.entity.FormFlowRecordData;
import com.taurus.formSys.entity.business.FormFlowRecordDataWrapper;
import com.taurus.formSys.service.FormFieldStructureService;
import com.taurus.formSys.service.FormFlowRecordDataService;
import com.taurus.oss.aliyun.OssOperation;
import com.taurus.oss.aliyun.OssRespository;
import com.taurus.utils.POIUtil;
import com.taurus.utils.Time;

@RestController
@RequestMapping("/formFlowRecordData")
public class FormFlowRecordDataController {

	@Autowired
	private FormFlowRecordDataService formFlowRecordDataService;

	@Autowired
	private FormFieldStructureService formFieldStructureService;

	/**
	 * 更新批量记录
	 * 
	 * @param body
	 * @return
	 */
	@PostMapping("/updateFormFlowRecordData")
	public ResponseObject updateFormFlowRecordData(@RequestBody JSONArray widgetList) {
		for (int i = 0; i < widgetList.size(); i++) {
			JSONObject obj = widgetList.getJSONObject(i);
			Integer formId = obj.getInteger("formId");
			Integer fieldId = obj.getInteger("fieldId");
			Integer formFlowRecordId = obj.getInteger("formFlowRecordId");
			if (formId == null || formId == 0 || fieldId == null || fieldId == 0 || formFlowRecordId == null
					|| formFlowRecordId == 0) {
				return ResponseObject.failure("PARAMETERS_ERROR");
			}
		}

		for (int i = 0; i < widgetList.size(); i++) {
			JSONObject obj = widgetList.getJSONObject(i);
			Integer formId = obj.getInteger("formId");
			Integer fieldId = obj.getInteger("fieldId");
			Integer formFlowRecordId = obj.getInteger("formFlowRecordId");
			String fieldValue = obj.getString("fieldValue");
			FormFlowRecordData entity = new FormFlowRecordData();
			entity.setFieldId(fieldId);
			entity.setFieldValue(fieldValue);
			entity.setFormFlowRecordId(formFlowRecordId);
			entity.setFormId(formId);

			HashMap<String, Object> map = new HashMap<>();
			map.put("field_value", entity.getFieldValue());
			formFlowRecordDataService.upInsert(entity, map);
		}

		return ResponseObject.success(true);
	}

	/**
	 * 获取某次表单填写的详情数据（包括表单结构和填写的内容）
	 * 
	 * @param id
	 * @return
	 */
	@GetMapping("/getListByFormFlowRecordId")
	public ResponseObject getListByFormFlowRecordId(Integer id) {
		if (id == null || id == 0) {
			return ResponseObject.failure("PARAMETERS_ERROR");
		}
		List<Map<String, Object>> list = formFlowRecordDataService.getListByFormFlowRecordId(id);
		return ResponseObject.success(list);
	}

	/**
	 * 
	 * @param formId
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	@GetMapping("/getFormFlowRecordDataByFormId")
	public ResponseObject getFormFlowRecordDataByFormId(Integer formId, @RequestParam(required = false) Date beginTime,
			@RequestParam(required = false) Date endTime) {
		if (formId == null || formId == 0) {
			return ResponseObject.failure("PARAMETERS_ERROR");
		}

		Map<String, List<Map<String, Object>>> map = formFlowRecordDataService
				.getFormFlowRecordDataGroupByFormFlowRecordId(formId, beginTime, endTime);

		// 过滤出用户填写的内容字段
		List<FormFieldStructure> fieldList = formFieldStructureService.getFormFieldStructureListByFormId(formId);
		fieldList = fieldList.stream().filter(n -> formFlowRecordDataService.isWritableField(n))
				.collect(Collectors.toList());

		List<FormFlowRecordDataWrapper> templateWrapperList = fieldList.stream().map(n -> {
			return transform2FormFlowRecordDataWrapper(n);
		}).collect(Collectors.toList());

		List<Map<Integer, Object>> targetList = new ArrayList<>();
		map.forEach((key, value) -> {
			List<FormFlowRecordDataWrapper> dataWrapperList = MyBeanUtil.mapListToObjList(value,
					FormFlowRecordDataWrapper.class);
			Map<Integer, FormFlowRecordDataWrapper> wrapperMap = dataWrapperList.stream().collect(
					Collectors.toMap(FormFlowRecordDataWrapper::getFieldId, Function.identity(), (key1, key2) -> key2));

			List<FormFlowRecordDataWrapper> targetWrapperList = new ArrayList<FormFlowRecordDataWrapper>();
			// value中包含非可填写字段的内容，需要剔除；所以从所有用户可填写的内容字段里遍历，提取出
			templateWrapperList.forEach(item -> {
				Integer fieldId = item.getFieldId();
				FormFlowRecordDataWrapper formFlowRecordDataWrapper = wrapperMap.get(fieldId);
				if (formFlowRecordDataWrapper != null) {
					targetWrapperList.add(formFlowRecordDataWrapper);
				} else {
					targetWrapperList.add(item);
				}
			});
			// 将list结构转为map结构后，
			Map<Integer, Object> fieldIdMap = targetWrapperList.stream().collect(Collectors
					.toMap(FormFlowRecordDataWrapper::getFieldId, n -> getFieldInfo(n), (key1, key2) -> key2));

			targetList.add(fieldIdMap);
		});
		return ResponseObject.success(targetList);
	}

	private Map<String, Object> getFieldInfo(FormFlowRecordDataWrapper n) {
		Map<String, Object> fieldInfo = new HashMap<>();
		fieldInfo.put("fieldValue", n.getFieldValue());
		fieldInfo.put("createTime",Time.dateToStrLong(n.getCreateTime()));
		return fieldInfo;
	}

	/**
	 * 
	 * @param formId
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	@GetMapping("/getFormFlowRecordMapDataByFormId")
	public ResponseObject getFormFlowRecordMapDataByFormId(Integer formId,
			@RequestParam(required = false) Date beginTime, @RequestParam(required = false) Date endTime) {
		if (formId == null) {
			return ResponseObject.failure("PARAMETERS_ERROR");
		}

		

		// 过滤出用户可填写的内容字段列表
		List<FormFieldStructure> fieldList = formFieldStructureService.getFormFieldStructureListByFormId(formId);
		List<FormFieldStructure> filtedFieldList = fieldList.stream()
				.filter(n -> formFlowRecordDataService.isWritableField(n)).collect(Collectors.toList());

		// 所有表单填写记录列表（单条填写记录用map记录）
		List<Map<String, Object>> targetList = new ArrayList<>();
		
		Map<String, List<Map<String, Object>>> map = formFlowRecordDataService
				.getFormFlowRecordDataGroupByFormFlowRecordId(formId, beginTime, endTime);
		map.forEach((key, value) -> {
			List<FormFlowRecordDataWrapper> dataWrapperList = MyBeanUtil.mapListToObjList(value,
					FormFlowRecordDataWrapper.class);
			// list转map；字段id为key，字段填写记录为value的map；原则上一个表单内不会存在相同fieldId的字段，不过这里仍然做了去重操作
			Map<Integer, FormFlowRecordDataWrapper> wrapperMap = dataWrapperList.stream().collect(
					Collectors.toMap(FormFlowRecordDataWrapper::getFieldId, Function.identity(), (key1, key2) -> key2));
			// 定义一个接收过滤后数据的list对象
			List<FormFlowRecordDataWrapper> filtedTargetWrapperList = new ArrayList<FormFlowRecordDataWrapper>();

			// value中包含非可填写字段的内容，需要剔除；所以从所有用户可填写的内容字段里遍历，提取出
			filtedFieldList.forEach(item -> {
				Integer fieldId = item.getId();
				FormFlowRecordDataWrapper formFlowRecordDataWrapper = wrapperMap.get(fieldId);
				if (formFlowRecordDataWrapper != null) {
					Integer fieldType = formFlowRecordDataWrapper.getFieldType();
					String fieldValue = formFlowRecordDataWrapper.getFieldValue();
					if(fieldType==7||fieldType==8||fieldType==10||fieldType==11) {
						List<String> urlList  = new ArrayList<>();
						JSONArray fieldValueArray = JSONArray.parseArray(fieldValue);
						for(int i=0;i<fieldValueArray.size();i++) {
							JSONObject fieldValueJson = fieldValueArray.getJSONObject(i);
							String ossBucketName = fieldValueJson.getString("ossBucketName");
							String objectName = fieldValueJson.getString("objectName");
							OssRespository respository = OssRespository.getRespositoryByProductName(ossBucketName);
							String type="F";
							if(fieldType.toString().equals("8")) {
								type="A";
							}else if(fieldType.toString().equals("10")) {
								type="P";
							}else if(fieldType.toString().equals("11")) {
								type="V";
							}
							String url = OssOperation.generateAccessUrl(respository , objectName, type);
							urlList.add(url);
						}
						formFlowRecordDataWrapper.setFieldValue(urlList.toString());
					}
					filtedTargetWrapperList.add(formFlowRecordDataWrapper);
				}
			});

			// 将list结构转为map结构后，Map内包含了每条记录的组成结构（对应的表单字段id与字段值的映射）
			Map<String, Object> fieldIdMap = filtedTargetWrapperList.stream().collect(Collectors
					.toMap(n -> String.valueOf(n.getFieldId()), n -> n.getFieldValue(), (key1, key2) -> key2));

			// 补充一个表单填写时的时间
			Integer dIdx=dataWrapperList.size()-1;
			Date createTime = dataWrapperList.get(dIdx).getCreateTime();
			Integer userId = dataWrapperList.get(dIdx).getUserId();
			fieldIdMap.put("createTime", Time.dateToStrLong(createTime));
			fieldIdMap.put("userId", userId);

			targetList.add(fieldIdMap);
		});
		return ResponseObject.success(targetList);
	}

	/**
	 * 获取多个表单的填写记录
	 * 
	 * @param body（包含了多个表单填写记录的id）
	 * @return 每个记录号对应一个表单记录详情；记录号为key，记录详情为value
	 */
	@PostMapping("/getFormFlowRecordDataByIds")
	public ResponseObject getFormFlowRecordDataByIds(@RequestBody JSONObject body) {
		JSONArray idsArray = body.getJSONArray("ids");
		Integer formId = body.getInteger("formId");
		List<Integer> idsList = JSONArray.parseArray(idsArray.toJSONString(), Integer.class);

		if (idsList != null && idsList.size() > 0) {
			Map<String, List<Map<String, Object>>> map = formFlowRecordDataService
					.getFormFlowRecordDataByIdsGroupByFormFlowRecordId(idsList);
			Map<String, List<FormFlowRecordDataWrapper>> targetMap = new HashMap<>();

			// 当表单id存在时
			if (formId != null && formId != 0) {
				List<FormFieldStructure> fieldList = formFieldStructureService
						.getFormFieldStructureListByFormId(formId);
				fieldList = fieldList.stream().filter(n -> formFlowRecordDataService.isWritableField(n))
						.collect(Collectors.toList());// 过滤出用户填写的内容字段

				List<FormFlowRecordDataWrapper> templateWrapperList = fieldList.stream().map(n -> {
					return transform2FormFlowRecordDataWrapper(n);
				}).collect(Collectors.toList());

				map.forEach((key, value) -> {
					List<FormFlowRecordDataWrapper> dataWrapperList = MyBeanUtil.mapListToObjList(value,
							FormFlowRecordDataWrapper.class);
					// list转map；各字段填写值的列表转化为：各字段key和字段数据对象value的键值对
					Map<Integer, FormFlowRecordDataWrapper> wrapperMap = dataWrapperList.stream().collect(Collectors
							.toMap(FormFlowRecordDataWrapper::getFieldId, Function.identity(), (key1, key2) -> key2));

					List<FormFlowRecordDataWrapper> targetWrapperList = new ArrayList<FormFlowRecordDataWrapper>();
					templateWrapperList.forEach(item -> {
						Integer fieldId = item.getFieldId();
						FormFlowRecordDataWrapper formFlowRecordDataWrapper = wrapperMap.get(fieldId);
						if (formFlowRecordDataWrapper != null) {
							targetWrapperList.add(formFlowRecordDataWrapper);
						} else {
							targetWrapperList.add(item);
						}
					});
					targetMap.put(key, targetWrapperList);
				});

			} else {
				map.forEach((key, value) -> {
					List<FormFlowRecordDataWrapper> dataWrapperList = MyBeanUtil.mapListToObjList(value,
							FormFlowRecordDataWrapper.class);
					targetMap.put(key, dataWrapperList);
				});
			}
			return ResponseObject.success(targetMap);
		} else {
			return ResponseObject.success(null);
		}
	}

	/**
	 * 表单数据模板
	 * 
	 * @param n
	 * @return
	 */
	private FormFlowRecordDataWrapper transform2FormFlowRecordDataWrapper(FormFieldStructure n) {
		FormFlowRecordDataWrapper wrapper = new FormFlowRecordDataWrapper();
		MyBeanUtil.copyProperties(n, wrapper);
		wrapper.setFieldId(n.getId());
		return wrapper;
	}

	/**
	 * 导出某个表单的所有数据到excel文件
	 * 
	 * @param formId
	 * @param beginTimeStr
	 * @param endTimeStr
	 * @param response
	 * @return
	 */
	@GetMapping("/exportFormFlowRecordData")
	public ResponseObject exportFormFlowRecordData(Integer formId, @RequestParam(required = false) String beginTimeStr,
			@RequestParam(required = false) String endTimeStr, HttpServletResponse response) {
		if (formId == null) {
			return ResponseObject.failure("PARAMETERS_ERROR");
		}
		Date beginTime = null;
		Date endTime = null;
		if (beginTimeStr != null)
			beginTime = Time.strToDateByFormat(beginTimeStr, "yyyy-MM-dd");
		if (endTimeStr != null)
			endTime = Time.strToDateByFormat(endTimeStr, "yyyy-MM-dd");

		String fileName = "数据" + formId;
		String sheetName = "记录";
		OutputStream out = null;
		HSSFWorkbook workbook = null;

		// 获取表头
		List<FormFieldStructure> fieldList = formFieldStructureService.getFormFieldStructureListByFormId(formId);
		fieldList = fieldList.stream().filter(n -> formFlowRecordDataService.isWritableField(n)).collect(Collectors.toList());// 过滤出用户填写的内容字段
		
		List<Map<String, Object>> sheetHead = new ArrayList<>();
		for (FormFieldStructure st : fieldList) {
			Map<String, Object> map = new HashMap<>();
			map.put("fieldName", st.getFieldName());
			map.put("fieldKey", st.getId());
			sheetHead.add(map);
		}
		Map<String, Object> timeMap = new HashMap<>();
		timeMap.put("fieldName", "创建时间");
		timeMap.put("fieldKey", "createTime");
		sheetHead.add(timeMap);

		// 获取数据
		ResponseObject res = this.getFormFlowRecordMapDataByFormId(formId, beginTime, endTime);
		List<Map<String, Object>> dataList = (List<Map<String, Object>>) res.getData();
		try {
			out = response.getOutputStream();
			response.setHeader("Content-disposition", "attachment; filename = " + URLEncoder.encode(fileName, "UTF-8"));
			response.setContentType("application/vnd.ms-excel");
			workbook = POIUtil.generateWorkbook(dataList, sheetName, null, sheetHead);
			workbook.write(out);

		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				try {
					out.close();
				} catch (Exception e) {
					e.printStackTrace();
				}
			}

			if (null != workbook) {
				try {
					workbook.close();
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
		return null;
	}

	/**
	 * 获取每道问题的答案的分布统计情况
	 * 
	 * @param formId
	 * @return
	 */
	@GetMapping("/getDistrbutionAnalysisData")
	public ResponseObject getDistrbutionAnalysisData(Integer formId) {
		if (formId == null || formId == 0) {
			return ResponseObject.failure("PARAMETERS_ERROR");
		}
		List<Map<String, Object>> list = formFlowRecordDataService.getDistrbutionAnalysisData(formId);
		return ResponseObject.success(list);
	}
}
