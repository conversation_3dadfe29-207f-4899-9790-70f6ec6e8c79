package com.taurus.mybatis;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.Date;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import com.taurus.utils.Time;

/**
 * 自定义类型转换
 * 
 * typeHandler的实现使用需要实现4个方法，这四个方法中有一个方式设set，三个方法是get。
 * 如此我们便可以在set方法中通过对传递过来的数据进行加密再保存到数据库。在get方法中，
 * 我们可以使用解密方法解密之后再返回到对象的属性中。
 *
 * <AUTHOR>
 * @date 2021/7/7
 */
@MappedTypes(value = Object.class)
@MappedJdbcTypes(value = { JdbcType.TIMESTAMP})
public class DateTimeHandler extends BaseTypeHandler<Date> {

	@Override
	public Date getNullableResult(ResultSet resultSet, String s) throws SQLException {
		return parse2time(resultSet.getObject(s));
	}

	@Override
	public Date getNullableResult(ResultSet resultSet, int i) throws SQLException {
		return parse2time(resultSet.getObject(i));
	}

	@Override
	public Date getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
		return parse2time(callableStatement.getObject(i));
	}

	private Date parse2time(Object value) {
		if (value instanceof Timestamp) {
			Long time = ((Timestamp)value).getTime();
			return new Date(time);
		}else if(value instanceof LocalDateTime) {
			try {
				Date date = Time.localDateTime2Date((LocalDateTime)value);
				return date;
			} catch (ParseException e) {
				return null;
			}
		}
		return null;
	}

	@Override
	public void setNonNullParameter(PreparedStatement ps, int i, Date parameter, JdbcType jdbcType)
			throws SQLException {
		if (jdbcType == JdbcType.TIMESTAMP) {
			Timestamp timestamp = new Timestamp(parameter.getTime());
			ps.setTimestamp(i, timestamp);
		} 
		
	}
}
