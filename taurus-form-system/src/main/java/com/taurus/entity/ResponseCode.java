package com.taurus.entity;

public class ResponseCode {
	

	public final static String PARAMETERS_ERROR="PARAMETERS_ERROR"; 
	
	public final static String NO_USER_SCAN="NO_USER_SCAN"; 
	public final static String NO_TOKEN = "NO_TOKEN";
	public final static String INVALID_TOKEN ="INVALID_TOKEN";
	public final static String OVERDUE_TOKEN ="OVERDUE_TOKEN";
	
	public final static String SUCESS = "SUCESS";
	public final static String NO_USER_ID="NO_USER_ID";
	public final static String NO_EXAMINATION_ID="NO_EXAMINATION_ID";
	public final static String NO_COMPANY_ID="NO_COMPANY_ID";
	public final static String INVALID_COMPANY_ID="INVALID_COMPANY_ID";
	
	public final static String NO_PDDS_TIMES="NO_PDDS_TIMES";//成绩详情下载服务次数不足
	
	public final static String IO_EXCEPTION="IO_EXCEPTION";
	
	public final static String NO_MULTIPARTFILE="NO_MULTIPARTFILE";
	public final static String FILE_PARSE_ERROR="FILE_PARSE_ERROR";
	public final static String NOT_EXCEL_FILE="NOT_EXCEL_FILE";
	public final static String NOT_WORD_FILE="NOT_WORD_FILE";
	public final static String NO_IMAGE_FILE="NO_IMAGE_FILE";
	public final static String NOT_EXCEL_OR_WORD_FILE="NOT_EXCEL_OR_WORD_FILE";
	
	public final static String NET_CONNECTION_ERROR="NET_CONNECTION_ERROR";
	
	//用户独立运营产品
	public final static String NOT_VALID_PRODUCT_CODE="NOT_VALID_PRODUCT_CODE";
	public final static String SUBJECT_NAME_IS_EMPTY="SUBJECT_NAME_IS_EMPTY";
	
	//转交考试
	public final static String TRANSMIT_OVER="TRANSMIT_OVER";//已经转交过了
	public final static String TRANSMIT_SELF="TRANSMIT_SELF";//自己接收
	
	//用户填报表单次数达到最大限制
	public final static String REACHED_PER_USER_SUBMIT_MAX_TIMES="REACHED_PER_USER_SUBMIT_MAX_TIMES";
}
