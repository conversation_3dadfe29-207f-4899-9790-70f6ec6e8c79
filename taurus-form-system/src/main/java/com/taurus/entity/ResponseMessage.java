package com.taurus.entity;

import java.lang.reflect.Field;

public class ResponseMessage {
	
	public static final String ERROR = "发生错误";
	public static final String PARAMETERS_ERROR ="参数不合法或错误";
	public static final String INVALID_VALUE ="无效值";
	
	public static final String NO_USER_SCAN ="用户未扫描登陆二维码";
	public static final String NO_TOKEN = "无TOKEN";
	public static final String INVALID_TOKEN ="无效的token";
	public static final String OVERDUE_TOKEN ="过期的token";
	
	public static final String SUCESS = "成功";
	public static final String NO_USER_ID="无用户Id";
	public static final String NO_EXAMINATION_ID="无考试Id";
	public static final String NO_COMPANY_ID="无公司Id";
	public static final String INVALID_COMPANY_ID="无效的公司id";
	
	public static final String NO_PDDS_TIMES="未购买成绩详情报表服务，请联系客服";
	
	public static final String IO_EXCEPTION="发生IO流错误";
	
	public static final String NO_MULTIPARTFILE="未选择文件上传";
	public static final String EXCEL_FIELD_EMPTY="字段值为空";
	public static final String UNKNOWN_FILE="未知文件";
	public static final String FILE_PARSE_ERROR="文件解析错误";
	public static final String EXCEL_DATA_ERROR="excel数据格式有误";
	public static final String NOT_EXCEL_FILE="不是excel文件";
	public static final String NOT_WORD_FILE="不是word文件";
	public static final String NO_IMAGE_FILE="没有图片文件";
	public static final String NOT_EXCEL_OR_WORD_FILE="不是excel或word文件";
	
	
	public static final String REACHED_PER_USER_SUBMIT_MAX_TIMES="达到最大用户填写次数限制";
	
	
	
	//用户
	public static final String EXISTED_USER="已存在的用户";
	

	
	
	//用户独立运营产品
	public static final String NOT_VALID_PRODUCT_CODE="不是有效的产品代码";
	public static final String SUBJECT_NAME_IS_EMPTY="空的栏目名称，请补充";
	
	//转交考试
	public static final String TRANSMIT_OVER="已经转交过了";//
	public static final String TRANSMIT_SELF="这是您自己的考试";//
	
	
	//短信
	public static final String SMS_ERROR="短信验证码不正确或已过期，请重新获取";
	
	//短信发送接口
	
	public static final String SMS_ERROR_00="手机号码格式不正确";
	public static final String SMS_ERROR_01="短信服务网络连接失败";
	public static final String SMS_ERROR_02="服务器内部错误";//
	
	//语音识别接口返回码
	public static final String SR_ERROR_00="语音识别初始化失败";
	
	
	//语音识别结果返回码
	public static final String SR_RESULT_00="停顿时间超过限定时常";
	
	
	//通过反射获取变量名
	public static String getFieldName(String fieldName) {
        try {
            Field fieldTag = ResponseMessage.class.getDeclaredField(fieldName);
            return (String) fieldTag.getName();
        } catch (Exception ex) {
            return null;
        }
    }
	
	// 通过反射获取属性值
    public static String getValueByFieldName(String fieldName) {
        try {
            Field fieldTag = ResponseMessage.class.getDeclaredField(fieldName);
            return (String) fieldTag.get(ResponseMessage.class);
        } catch (Exception ex) {
            return null;
        }
    }
}
