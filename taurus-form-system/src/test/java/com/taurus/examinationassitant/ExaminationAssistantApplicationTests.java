package com.taurus.examinationassitant;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.taurus.formSys.FormSystemApplication;
import com.taurus.utils.MyStringUtil;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = FormSystemApplication.class)
public class ExaminationAssistantApplicationTests {

	private static final org.slf4j.Logger logger = LoggerFactory.getLogger(ExaminationAssistantApplicationTests.class);

	@Test
	public void countWordNum() {

		int num = MyStringUtil.count("adbc");
		System.out.println("汉字个数：" + num);
	}
	
	@Test
	public void total(){
		int totalScore=98;
		int mark=100;
		int percentTotalScore = Math.round(totalScore*100 / mark );
		System.out.println(percentTotalScore);
	}
	
	@Test
	public void test() {
		int a=-2;
		int b= a>>>3;
		System.out.print("testValue:"+b);
	}

}
